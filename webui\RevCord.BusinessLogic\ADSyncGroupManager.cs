﻿using RevCord.DataAccess;
using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.MessageBase;
using RevCord.DataContracts.Messages;
using RevCord.DataContracts.UserManagement;
using RevCord.Util;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Script.Serialization;

namespace RevCord.BusinessLogic
{
    public class ADSyncGroupManager
    {
        private readonly static string _serverIP = SiteConfig.HelperServiceAddress;

        public ADSyncGroupResponse GetAllADGroups(ADSyncGroupRequest request)
        {
            try
            {
                List<ADSyncGroup> objADSyncGroupList = null;

                objADSyncGroupList = new ADSyncGroupDAL(request.tenantId).GetAllADGroups();
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.ADSyncGroups, "GetAllADGroups", request.tenantId, new JavaScriptSerializer().Serialize(objADSyncGroupList)));
                
                return new ADSyncGroupResponse
                {
                    Acknowledge = objADSyncGroupList == null ? AcknowledgeType.Failure : AcknowledgeType.Success,
                    ADSyncGroups = objADSyncGroupList
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.ADSyncGroups, "GetAllADGroups", request.tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.ADSyncGroups, "GetAllADGroups", request.tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public ADSyncGroupResponse SaveADSyncGroup(ADSyncGroupRequest request)
        {
            try
            {
                ADSyncGroup objGroup = new ADSyncGroupDAL(request.tenantId).SaveADSyncGroup(request.ADSyncGroup);
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.ADSyncGroups, "SaveADSyncGroup", request.tenantId, " ADSyncGroupId = " + objGroup.Id));
                return new ADSyncGroupResponse
                {
                    Acknowledge = objGroup.Id > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    ADSyncGroup = objGroup
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.ADSyncGroups, "SaveADSyncGroup", request.tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.ADSyncGroups, "SaveADSyncGroup", request.tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public ADSyncGroupResponse SyncADSyncGroup(ADSyncGroupRequest request)
        {
            try
            {
                bool isSynced = new ADSyncGroupDAL(request.tenantId).SyncADSyncGroup(request.Id);
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.ADSyncGroups, "SyncADSyncGroup", request.tenantId, " ADSyncGroupId = " + request.Id));
                return new ADSyncGroupResponse
                {
                    Acknowledge = isSynced ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    Id = request.Id
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.ADSyncGroups, "SyncADSyncGroup", request.tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.ADSyncGroups, "SyncADSyncGroup", request.tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public ADSyncGroupResponse UnSyncADSyncGroup(ADSyncGroupRequest request)
        {
            try
            {
                bool isSynced = new ADSyncGroupDAL(request.tenantId).UnSyncADSyncGroup(request.Id);
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.ADSyncGroups, "UnSyncADSyncGroup", request.tenantId, " ADSyncGroupId = " + request.Id));
                return new ADSyncGroupResponse
                {
                    Acknowledge = isSynced ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    Id = request.Id
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.ADSyncGroups, "UnSyncADSyncGroup", request.tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.ADSyncGroups, "UnSyncADSyncGroup", request.tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public ADSyncGroupResponse RemoveADSyncGroup(ADSyncGroupRequest request)
        {
            try
            {
                bool isRemoved = new ADSyncGroupDAL(request.tenantId).RemoveADSyncGroup(request.Id);
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.ADSyncGroups, "RemoveADSyncGroup", request.tenantId, " ADSyncGroupId = " + request.Id));

                return new ADSyncGroupResponse
                {
                    Acknowledge = isRemoved ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    Id = request.Id
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.ADSyncGroups, "RemoveADSyncGroup", request.tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.ADSyncGroups, "RemoveADSyncGroup", request.tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

    }
}
