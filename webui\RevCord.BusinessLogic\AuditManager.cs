﻿using RevCord.DataAccess;
using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.Messages;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.BusinessLogic
{
    public class AuditManager
    {
        public AuditResponse GetAllAudits(AuditRequest auditRequest)
        {
            try
            {
                int totalPages = 0;
                int totalRecords = 0;
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Audit, "GetAllAudits", auditRequest.TenantId, "GetAllAudits function has been called successfully."));
                return new AuditResponse { Audits = new AuditDAL(auditRequest.TenantId).GetAllAudits(auditRequest.AuditCriteria.StartDate, auditRequest.AuditCriteria.EndDate, auditRequest.AuditCriteria.PageNumber, auditRequest.AuditCriteria.PageSize, out totalPages, out totalRecords),
                    TotalPages = totalPages,
                    TotalRecords = totalRecords,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "GetAllAudits", auditRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "GetAllAudits", auditRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public AuditResponse GetAuditsByCategory(AuditRequest auditRequest)
        {
            try
            {
                int totalPages = 0;
                int totalRecords = 0;
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Audit, "GetAuditsByCategory", auditRequest.TenantId, "GetAuditsByCategory function has been called successfully."));
                return new AuditResponse { Audits = new AuditDAL(auditRequest.TenantId).GetAuditsByCategory(Convert.ToInt32(auditRequest.AuditCriteria.AuditLogCategory), auditRequest.AuditCriteria.StartDate, auditRequest.AuditCriteria.EndDate, auditRequest.AuditCriteria.PageNumber, auditRequest.AuditCriteria.PageSize, out totalPages, out totalRecords),
                    TotalPages = totalPages,
                    TotalRecords = totalRecords,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Audit, "GetAuditsByCategory", auditRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Audit, "GetAuditsByCategory", auditRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public AuditResponse GetAuditLogsCount(AuditRequest auditRequest)
        {
            try
            {
                int totalPages = 0;
                int totalRecords = 0;
                var auditLogCount = new AuditDAL(auditRequest.TenantId).GetAuditLogsCount(Convert.ToInt32(auditRequest.AuditCriteria.AuditLogCategory), auditRequest.AuditCriteria.StartDate, auditRequest.AuditCriteria.EndDate, auditRequest.AuditCriteria.PageNumber, auditRequest.AuditCriteria.PageSize, out totalPages, out totalRecords);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Audit, "GetAuditLogsCount", auditRequest.TenantId, "GetAuditLogsCount function has been called successfully. auditLogCount = " + auditLogCount));
                return new AuditResponse
                {
                    TotalPages = totalPages,
                    TotalRecords = totalRecords,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Audit, "GetAuditLogsCount", auditRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Audit, "GetAuditLogsCount", auditRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public AuditResponse GetAuditDetails(AuditRequest auditRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Audit, "GetAuditDetails", auditRequest.TenantId, "GetAuditDetails function has been called successfully."));
                return new AuditResponse
                {
                    Audit = new AuditDAL(auditRequest.TenantId).GetAuditDetails(auditRequest.AuditCriteria.Id)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Audit, "GetAuditDetails", auditRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Audit, "GetAuditDetails", auditRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
    }
}