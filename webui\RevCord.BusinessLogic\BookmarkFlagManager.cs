﻿using System;
using System.Collections.Generic;
using RevCord.DataAccess;
using System.Data.SqlClient;
using System.Threading.Tasks;
using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.VoiceRecEntities;

namespace RevCord.BusinessLogic
{
    public class BookmarkFlagManager
    {
        public List<BmFlag> GetFlagList(int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Playlist, "GetFlagList", tenantId, "GetFlagList function has been called successfully. "));
                return new BookmarkFlagDAL(tenantId).GetBookmarkFlag();
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Common, "GetFlagList", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Common, "GetFlagList", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public List<BmFlag> SaveBookmarkFlags(List<BmFlag> lBmFlag, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Playlist, "SaveBookmarkFlags", tenantId, "SaveBookmarkFlags function has been called successfully. "));
                return new BookmarkFlagDAL(tenantId).SaveBookmarkFlag(lBmFlag);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Common, "SaveBookmarkFlags", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Common, "SaveBookmarkFlags", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public int DeleteBookmarkFlag(int tagid, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Playlist, "DeleteBookmarkFlag", tenantId, "DeleteBookmarkFlag function has been called successfully. "));
                return new BookmarkFlagDAL(tenantId).DeleteBookmarkFlag(tagid);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Common, "DeleteBookmarkFlag", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Common, "DeleteBookmarkFlag", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

    }
}
