﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.ViewModelEntities;

namespace RevCord.BusinessLogic
{
    public static partial class StringExtension
    {
        public static string GenerateStencilNumber(DateTime? dOB, string sSN)
        {
            var dob = dOB.HasValue ? dOB.GetValueOrDefault().ToString("yyMMdd") : "010101";
            var ssn = !string.IsNullOrEmpty(sSN) ? sSN.Substring(sSN.Length - 2) : "00";

            return $"WBS{dob}{ssn}";
        }
    }
    public static partial class EnumerableExtension
    {
        public static List<TreeviewData> BuildGroupChannelsTree(this List<TreeviewData> items)
        {
            items.ForEach(i => i.Childrens = items.Where(ch => i.NodeId.Equals(ch.ParentNodeId.ToString()) && !ch.NodeId.Equals(ch.ParentNodeId.ToString())).ToList());
            return items.Where(i => i.ParentNodeId.ToString().Equals(i.NodeId)).ToList();
        }

        public static TreeviewData BuildGroupsTree(this List<TreeviewData> items)
        {
            if (items == null || items.Count == 0)
                return new TreeviewData { Childrens = new List<TreeviewData>() };
            //for groups order as per RevConfig display
            //items.ForEach(i => i.Childrens = items.Where(ch => i.NodeId.Equals(ch.ParentNodeId.ToString()) && !ch.NodeId.Equals(ch.ParentNodeId.ToString())).OrderByDescending(tv => tv.IsGroup).ThenBy(tv => Convert.ToInt32(tv.Param3)).ToList());
            items.ForEach(i => i.Childrens = items.Where(ch => i.NodeId.Equals(ch.ParentNodeId.ToString()) && !ch.NodeId.Equals(ch.ParentNodeId.ToString())).ToList());
            return items.Where(i => i.ParentNodeId.ToString().Equals(i.NodeId)).First();
            //return items.OrderBy(tv => tv.IsGroup).Where(i => i.ParentNodeId.ToString().Equals(i.NodeId)).First();
        }

        public static TreeviewData BuildRVIGroupChannelsTree(this List<TreeviewData> items)
        {
            items.ForEach(i => i.Childrens = items.Where(ch => i.NodeId.Equals(ch.ParentNodeId.ToString()) && !ch.NodeId.Equals(ch.ParentNodeId.ToString())).ToList());
            return items.Where(i => i.ParentNodeId.ToString().Equals(i.NodeId)).First();
        }
    }
}