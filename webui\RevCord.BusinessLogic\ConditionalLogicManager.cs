﻿using RevCord.DataAccess;
using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.IQ3ConditionalLogic;
using RevCord.DataContracts.Messages;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.BusinessLogic
{
    public class ConditionalLogicManager
    {
        public ConditionalLogicResponse SaveMarkerLogic(ConditionalLogicRequest conditionalLogicRequest)
        {
            try
            {
                MarkerLogic markerLogic = new ConditionalLogicDAL(conditionalLogicRequest.TenantId).SaveMarkerLogic(conditionalLogicRequest.MarkerLogic);
                return new ConditionalLogicResponse
                {
                    MarkerLogic = markerLogic,
                    Message = "Marker logic object has been saved successfully.",
                    Status = markerLogic.Id > 0 ? true : false
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.ConditionalLogics, "SaveMarkerLogic", conditionalLogicRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.ConditionalLogics, "SaveMarkerLogic", conditionalLogicRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public ConditionalLogicResponse SaveLogicTrigger(ConditionalLogicRequest conditionalLogicRequest)
        {
            try
            {
                LogicTrigger logicTrigger = new ConditionalLogicDAL(conditionalLogicRequest.TenantId).SaveLogicTrigger(conditionalLogicRequest.LogicTrigger);
                return new ConditionalLogicResponse
                {
                    LogicTrigger = logicTrigger,
                    Message = "Logic trigger object has been saved successfully.",
                    Status = logicTrigger.Id > 0 ? true : false
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.ConditionalLogics, "SaveLogicTrigger", conditionalLogicRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.ConditionalLogics, "SaveLogicTrigger", conditionalLogicRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public ConditionalLogicResponse GetMarkerLogics(ConditionalLogicRequest conditionalLogicRequest)
        {
            try
            {
                List<MarkerLogic> markerLogics = new ConditionalLogicDAL(conditionalLogicRequest.TenantId).GetMarkerLogics(conditionalLogicRequest.MarkerId);
                return new ConditionalLogicResponse
                {
                    MarkerLogics = markerLogics,
                    Message = "Marker logics have been fetched successfully.",
                    Status = markerLogics.Count > 0 ? true : false
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.ConditionalLogics, "GetMarkerLogics", conditionalLogicRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.ConditionalLogics, "GetMarkerLogics", conditionalLogicRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public ConditionalLogicResponse DeleteMarkerLogic(ConditionalLogicRequest conditionalLogicRequest)
        {
            try
            {
                var response = new ConditionalLogicDAL(conditionalLogicRequest.TenantId).DeleteMarkerLogic(conditionalLogicRequest.MarkerLogicId);
                return new ConditionalLogicResponse
                {
                    Message = response ? "Marker logic has beend deleted successfully." : "Something went wrong while deleting the marker logic.",
                    Status = response
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.ConditionalLogics, "DeleteMarkerLogic", conditionalLogicRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.ConditionalLogics, "DeleteMarkerLogic", conditionalLogicRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public ConditionalLogicResponse DeleteLogicTrigger(ConditionalLogicRequest conditionalLogicRequest)
        {
            try
            {
                var response = new ConditionalLogicDAL(conditionalLogicRequest.TenantId).DeleteLogicTrigger(conditionalLogicRequest.LogicTriggerId);
                return new ConditionalLogicResponse
                {
                    Message = response ? "Logic trigger has beend deleted successfully." : "Something went wrong while deleting the logic trigger.",
                    Status = response
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.ConditionalLogics, "DeleteLogicTrigger", conditionalLogicRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.ConditionalLogics, "DeleteLogicTrigger", conditionalLogicRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        //public ConditionalLogicResponse UpdateMarkerLogic(ConditionalLogicRequest conditionalLogicRequest)
        //{
        //    try
        //    {
        //        MarkerLogic markerLogic = new ConditionalLogicDAL(conditionalLogicRequest.TenantId).UpdateMarkerLogic(conditionalLogicRequest.MarkerLogic);
        //        return new ConditionalLogicResponse
        //        {
        //            MarkerLogic = markerLogic,
        //            Message = "Marker logic object has been updated successfully.",
        //            Status = true
        //        };
        //    }
        //    catch (SqlException sqle)
        //    {
        //        Task.Run(() => RevAuditLogger.WriteSQLException(Originator.ConditionalLogics, "UpdateMarkerLogic", conditionalLogicRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
        //        throw sqle;
        //    }
        //    catch (Exception ex)
        //    {
        //        Task.Run(() => RevAuditLogger.WriteException(Originator.ConditionalLogics, "UpdateMarkerLogic", conditionalLogicRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
        //        throw ex;
        //    }
        //}

        public ConditionalLogicResponse UpdateMarkerLogicType(ConditionalLogicRequest conditionalLogicRequest)
        {
            try
            {
                MarkerLogic markerLogic = new ConditionalLogicDAL(conditionalLogicRequest.TenantId).UpdateMarkerLogicType(conditionalLogicRequest.MarkerLogic);
                return new ConditionalLogicResponse
                {
                    MarkerLogic = markerLogic,
                    Message = "Marker logic type has been updated successfully.",
                    Status = true
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.ConditionalLogics, "UpdateMarkerLogicType", conditionalLogicRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.ConditionalLogics, "UpdateMarkerLogicType", conditionalLogicRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public ConditionalLogicResponse UpdateMarkerLogicMatchText(ConditionalLogicRequest conditionalLogicRequest)
        {
            try
            {
                MarkerLogic markerLogic = new ConditionalLogicDAL(conditionalLogicRequest.TenantId).UpdateMarkerLogicMatchText(conditionalLogicRequest.MarkerLogic);
                return new ConditionalLogicResponse
                {
                    MarkerLogic = markerLogic,
                    Message = "Marker logic type has been updated successfully.",
                    Status = true
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.ConditionalLogics, "UpdateMarkerLogicMatchText", conditionalLogicRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.ConditionalLogics, "UpdateMarkerLogicMatchText", conditionalLogicRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public ConditionalLogicResponse FetchTriggerAction(ConditionalLogicRequest conditionalLogicRequest)
        {
            try
            {
                TriggerAction triggerAction = new ConditionalLogicDAL(conditionalLogicRequest.TenantId).FetchTriggerAction(conditionalLogicRequest.LogicTriggerId, conditionalLogicRequest.TriggerTypeId);
                return new ConditionalLogicResponse
                {
                    TriggerAction = triggerAction,
                    Message = "Trigger action object has been fetched successfully.",
                    Status = true
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.ConditionalLogics, "FetchTriggerAction", conditionalLogicRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.ConditionalLogics, "FetchTriggerAction", conditionalLogicRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public ConditionalLogicResponse UpdateTriggerAction(ConditionalLogicRequest conditionalLogicRequest)
        {
            try
            {
                TriggerAction triggerAction = new ConditionalLogicDAL(conditionalLogicRequest.TenantId).UpdateTriggerAction(conditionalLogicRequest.TriggerAction);
                return new ConditionalLogicResponse
                {
                    TriggerAction = triggerAction,
                    Message = "Trigger action has been fetched successfully. Id = " + conditionalLogicRequest.TriggerAction.Id,
                    Status = true
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.ConditionalLogics, "UpdateTriggerAction", conditionalLogicRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.ConditionalLogics, "UpdateTriggerAction", conditionalLogicRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
    }
}
