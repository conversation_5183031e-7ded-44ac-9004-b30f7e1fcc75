﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataAccess;
using RevCord.DataContracts.Messages;
using System.Data.SqlClient;
using System.Threading.Tasks;
using RevCord.DataAccess.Util;
using RevCord.DataContracts;

namespace RevCord.BusinessLogic
{
    public class DashboardManager
    {
        public DashboardResponse GetDashboardStatistics(DashboardRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Dashboard, "GetDashboardStatistics", request.TenantId, "GetDashboardStatistics function has been called successfully."));
                return new DashboardResponse { DashboardOverallStats = new DashboardDAL(request.TenantId).GetDashboardStatistics(request.UserId, request.DashboardCriteria.StartDate, request.DashboardCriteria.EndDate, request.CSVExtensions) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Dashboard, "GetDashboardStatistics", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Dashboard, "GetDashboardStatistics", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public DashboardResponse GetDashboardStatisticsLite(DashboardRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Dashboard, "GetDashboardStatisticsLite", request.TenantId, "GetDashboardStatistics function has been called successfully."));
                return new DashboardResponse { DashboardOverallStats = new DashboardDAL(request.TenantId).GetDashboardStatisticsLite(request.UserId, request.DashboardCriteria.StartDate, request.DashboardCriteria.EndDate, request.CSVExtensions) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Dashboard, "GetDashboardStatisticsLite", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Dashboard, "GetDashboardStatisticsLite", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public DashboardResponse ReloadDashboardStatistics(DashboardRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Dashboard, "ReloadDashboardStatistics", request.TenantId, "ReloadDashboardStatistics function has been called successfully."));
                return new DashboardResponse { DashboardOverallStats = new DashboardDAL(request.TenantId).ReloadDashboardStatistics(request.UserId, request.DashboardCriteria.StartDate, request.DashboardCriteria.EndDate, request.CSVExtensions) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Dashboard, "ReloadDashboardStatistics", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Dashboard, "ReloadDashboardStatistics", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public DashboardResponse ReloadDashboardStatisticsLite(DashboardRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Dashboard, "ReloadDashboardStatisticsLite", request.TenantId, "ReloadDashboardStatisticsLite function has been called successfully."));
                return new DashboardResponse { DashboardOverallStats = new DashboardDAL(request.TenantId).ReloadDashboardStatisticsLite(request.UserId, request.DashboardCriteria.StartDate, request.DashboardCriteria.EndDate, request.CSVExtensions) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Dashboard, "ReloadDashboardStatisticsLite", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Dashboard, "ReloadDashboardStatisticsLite", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public DashboardResponse GetIRData(DashboardRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Dashboard, "GetIRData", request.TenantId, "GetIRData function has been called successfully."));

                return new DashboardResponse { DashboardOverallStats = new DashboardDAL(request.TenantId).GetIRData(request.UserId, request.CSVExtensions, request.LastStartTime) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Dashboard, "GetIRData", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Dashboard, "GetIRData", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public DashboardResponse GetRecentRecords(DashboardRequest request)
	{
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Dashboard, "GetRecentRecords", request.TenantId, "GetRecentRecords function has been called successfully."));
                return new DashboardResponse { DashboardOverallStats = new DashboardDAL(request.TenantId).GetRecentRecords(request.UserId, request.CSVExtensions, request.NoOfRecords, request.IsAvrisView) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Dashboard, "GetRecentRecords", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Dashboard, "GetRecentRecords", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public DashboardResponse GetCallCountData(DashboardRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Dashboard, "GetCallCountData", request.TenantId, "GetCallCountData function has been called successfully."));

                return new DashboardResponse { DashboardOverallStats = new DashboardDAL(request.TenantId).GetCallCountData(request.UserId, request.CSVExtensions) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Dashboard, "GetCallCountData", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Dashboard, "GetCallCountData", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public DashboardResponse LoadDashboardDataLite(DashboardRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Dashboard, "LoadDashboardDataLite", request.TenantId, "LoadDashboardDataLite function has been called successfully."));

                return new DashboardResponse { DashboardOverallStats = new DashboardDAL(request.TenantId).LoadDashboardDataLite(request.UserId, request.CSVExtensions) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Dashboard, "LoadDashboardDataLite", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Dashboard, "LoadDashboardDataLite", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public DashboardResponse LoadSevenDayCallData(DashboardRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Dashboard, "LoadSevenDayCallData", request.TenantId, "LoadSevenDayCallData function has been called successfully."));
                return new DashboardResponse { DashboardOverallStats = new DashboardDAL(request.TenantId).LoadSevenDayCallData(request.UserId, request.CSVExtensions) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Dashboard, "LoadSevenDayCallData", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Dashboard, "LoadSevenDayCallData", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public DashboardResponse GetLiveEvents(DashboardRequest request)
        {
            return new DashboardResponse { LiveEvents = new DashboardDAL(request.TenantId).GetLiveEvents(request.UserId, request.LastStartTime) };
        }
    }
}
