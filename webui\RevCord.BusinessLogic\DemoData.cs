﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.ViewModelEntities;
using RevCord.DataContracts;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.DataContracts.Criteria;
using System.Diagnostics;
using RevCord.Util;

namespace RevCord.BusinessLogic
{

    public static class DemoData
    {
        private static string demoRecorderName = AppSettingsUtil.GetString("demoRecName", "");
        public static List<GroupTree> GetDemoGroups()
        {
            List<GroupTree> demoGroups = new List<GroupTree>()
            {
                new GroupTree { Id = 2, GroupType = GroupType.Video, CssName = "video"/*"video_list.png"*/, TreeviewData = _videos  },
                //new GroupTree { Id = 3, GroupType = GroupType.Text, CssName = "text"/*"text_list.png"*/, TreeviewData = _texts },
                new GroupTree { Id = 4, GroupType = GroupType.Social, CssName = "social"/*"social_list.png"*/, TreeviewData = _socials },
                new GroupTree { Id = 5, GroupType = GroupType.Email, CssName = "email"/*"email_list.png"*/, TreeviewData = _emails },
            };
            return demoGroups;
        }
        //public static IEnumerable<GroupTree> GroupTree { get { return _demoGroups; } }
        //static List<GroupTree> _demoGroups = new List<GroupTree>()
        //{
        //    new GroupTree { Id = 2, GroupType = GroupType.Video, ImageName = "video_list.png", TreeviewData= _videos  },
        //    new GroupTree { Id = 3, GroupType = GroupType.Text, ImageName = "text_list.png", TreeviewData= _texts },
        //    new GroupTree { Id = 4, GroupType = GroupType.Social, ImageName = "social_list.png", TreeviewData= _socials },
        //    new GroupTree { Id = 5, GroupType = GroupType.Email, ImageName = "email_list.png", TreeviewData= _emails },
        //};


        #region ------- Private Member's -------

        #region ------- Demo Extensions -------

        static TreeviewData _videos = new TreeviewData
                {
                    NodeId="400", NodeCaption="Video Truck", ParentNodeId=400, Depth=0, MenuType=1, ViewType=0, IsGroup=true, Param1="400", Param2="", Param3="1",
                    Childrens= new List<TreeviewData>
                    {
                        new TreeviewData
                        { 
                            NodeId="401", NodeCaption="Video Truck Fire", ParentNodeId=400, Depth=2, MenuType=1, ViewType=0, IsGroup=true, Param1="401", Param2="", Param3="2", 
                            Childrens=new List<TreeviewData>
                            {
                                new TreeviewData{ NodeId="401E403", NodeCaption="Video Truck 1 Fire", ParentNodeId=401, Depth=3, MenuType=1, ViewType=1, IsGroup=false, Param1="403", Param2="401", Param3="2", Childrens=new List<TreeviewData>(), },
                                new TreeviewData{ NodeId="401E404", NodeCaption="Video Truck 2 Fire", ParentNodeId=401, Depth=3, MenuType=1, ViewType=1, IsGroup=false, Param1="404", Param2="401", Param3="3", Childrens=new List<TreeviewData>(), },
                            }
                        },
                        new TreeviewData
                        { 
                            NodeId="402", NodeCaption="Video Police", ParentNodeId=400, Depth=2, MenuType=1, ViewType=0, IsGroup=true, Param1="402", Param2="", Param3="3", 
                            Childrens=new List<TreeviewData>
                            {
                                new TreeviewData{ NodeId="402E405", NodeCaption="Video Truck Vice", ParentNodeId=402, Depth=3, MenuType=1, ViewType=1, IsGroup=false, Param1="405", Param2="402", Param3="2", Childrens=new List<TreeviewData>(), },
                                new TreeviewData{ NodeId="402E406", NodeCaption="Video 911 Truck", ParentNodeId=402, Depth=3, MenuType=1, ViewType=1, IsGroup=false, Param1="406", Param2="402", Param3="3", Childrens=new List<TreeviewData>(), },
                            } 
                        },
                    }
            };

        //static TreeviewData _texts = new TreeviewData
        //    {
        //        NodeId="100", NodeCaption="SMS Fire", ParentNodeId=100, Depth=0, MenuType=1, ViewType=0, IsGroup=true, Param1="100", Param2="", Param3="1",
        //        Childrens= new List<TreeviewData>
        //        {
        //            new TreeviewData
        //            { 
        //                NodeId="101", NodeCaption="SMS Admin Fire", ParentNodeId=100, Depth=2, MenuType=1, ViewType=0, IsGroup=true, Param1="101", Param2="", Param3="2",
        //                Childrens=new List<TreeviewData>
        //                {
        //                    new TreeviewData{ NodeId="101E103", NodeCaption="SMS Admin 1 Fire", ParentNodeId=101, Depth=3, MenuType=1, ViewType=1, IsGroup=false, Param1="103", Param2="101", Param3="2", Childrens=new List<TreeviewData>(), },
        //                    new TreeviewData{ NodeId="101E104", NodeCaption="SMS Admin 2 Fire", ParentNodeId=101, Depth=3, MenuType=1, ViewType=1, IsGroup=false, Param1="104", Param2="101", Param3="3", Childrens=new List<TreeviewData>(), },
        //                }
        //            },
        //            new TreeviewData
        //            { 
        //                NodeId="102", NodeCaption="SMS Admin Police", ParentNodeId=100, Depth=2, MenuType=1, ViewType=0, IsGroup=true, Param1="102", Param2="", Param3="3", 
        //                Childrens=new List<TreeviewData>
        //                {
        //                    new TreeviewData{ NodeId="102E105", NodeCaption="SMS Admin 1 Police", ParentNodeId=102, Depth=3, MenuType=1, ViewType=1, IsGroup=false, Param1="105", Param2="102", Param3="2", Childrens=new List<TreeviewData>(), },
        //                    new TreeviewData{ NodeId="102E106", NodeCaption="SMS Admin 2 Police", ParentNodeId=102, Depth=3, MenuType=1, ViewType=1, IsGroup=false, Param1="106", Param2="102", Param3="3", Childrens=new List<TreeviewData>(), },
        //                }
        //            },
        //        }
        //    };

        static TreeviewData _socials = new TreeviewData
            {
                NodeId="200", NodeCaption="Social Fire", ParentNodeId=200, Depth=0, MenuType=1, ViewType=0, IsGroup=true, Param1="200", Param2="", Param3="1",
                Childrens= new List<TreeviewData>
                {
                    new TreeviewData
                    { 
                        NodeId="201", NodeCaption="Social Admin Fire", ParentNodeId=200, Depth=2, MenuType=1, ViewType=0, IsGroup=true, Param1="201", Param2="", Param3="2", 
                        Childrens=new List<TreeviewData>
                        {
                            new TreeviewData{ NodeId="201E203", NodeCaption="Social Admin 1 Fire", ParentNodeId=401, Depth=3, MenuType=1, ViewType=1, IsGroup=false, Param1="203", Param2="201", Param3="2", Childrens=new List<TreeviewData>(), },
                            new TreeviewData{ NodeId="201E204", NodeCaption="Social Admin 2 Fire", ParentNodeId=401, Depth=3, MenuType=1, ViewType=1, IsGroup=false, Param1="204", Param2="201", Param3="3", Childrens=new List<TreeviewData>(), },
                        }
                    },
                    new TreeviewData
                    { 
                        NodeId="202", NodeCaption="Social Admin Police", ParentNodeId=200, Depth=2, MenuType=1, ViewType=0, IsGroup=true, Param1="202", Param2="", Param3="3", 
                        Childrens=new List<TreeviewData>
                        {
                            new TreeviewData{ NodeId="202E205", NodeCaption="Social Admin 1 Police", ParentNodeId=102, Depth=3, MenuType=1, ViewType=1, IsGroup=false, Param1="205", Param2="202", Param3="2", Childrens=new List<TreeviewData>(), },
                            new TreeviewData{ NodeId="202E206", NodeCaption="Social Admin 2 Police", ParentNodeId=102, Depth=3, MenuType=1, ViewType=1, IsGroup=false, Param1="206", Param2="202", Param3="3", Childrens=new List<TreeviewData>(), },
                        }
                    },
                }
            };

        static TreeviewData _emails = new TreeviewData
            {
                NodeId="300", NodeCaption="Email Admin", ParentNodeId=300, Depth=0, MenuType=1, ViewType=0, IsGroup=true, Param1="300", Param2="", Param3="1",
                Childrens= new List<TreeviewData>
                {
                    new TreeviewData
                    { 
                        NodeId="301", NodeCaption="Email Admin Police", ParentNodeId=300, Depth=2, MenuType=1, ViewType=0, IsGroup=true, Param1="301", Param2="", Param3="2", 
                        Childrens=new List<TreeviewData>
                        {
                            new TreeviewData{ NodeId="301E303", NodeCaption="Email Admin 1 Fire", ParentNodeId=301, Depth=3, MenuType=1, ViewType=1, IsGroup=false, Param1="303", Param2="301", Param3="2", Childrens=new List<TreeviewData>(), },
                            new TreeviewData{ NodeId="301E304", NodeCaption="Email Admin 1 Police", ParentNodeId=301, Depth=3, MenuType=1, ViewType=1, IsGroup=false, Param1="304", Param2="301", Param3="3", Childrens=new List<TreeviewData>(), },
                        }
                    },
                    new TreeviewData
                    { 
                        NodeId="302", NodeCaption="Email Call Center", ParentNodeId=300, Depth=2, MenuType=1, ViewType=0, IsGroup=true, Param1="302", Param2="", Param3="3", 
                        Childrens=new List<TreeviewData>
                        {
                            new TreeviewData{ NodeId="302E305", NodeCaption="Email CC 1", ParentNodeId=302, Depth=3, MenuType=1, ViewType=1, IsGroup=false, Param1="305", Param2="302", Param3="2", Childrens=new List<TreeviewData>(), },
                            new TreeviewData{ NodeId="302E306", NodeCaption="Email CC 2", ParentNodeId=302, Depth=3, MenuType=1, ViewType=1, IsGroup=false, Param1="306", Param2="302", Param3="3", Childrens=new List<TreeviewData>(), },
                        }
                    },
                }
            };

        #endregion


        #region ------- Demo Calls -------

        public static List<CallInfo> GetVideos()
        {
            //int startRowCounter = 1;startRowCounter++,
            return new List<CallInfo>()
            {
                #region Group 401
		 
                new CallInfo
                {
                    //CallId = "01829f2eb1d3417bbe4cdda866c9b7dc", 
                    CallId = Guid.NewGuid().ToString("N"), CallType = 2, CallType_inq = 2, DurationInMilliSeconds = 72000, FileName = "policeFire.mp4",
                    ChannelId = 403, ChannelName = "Video Truck 1 Fire",
                    GroupId = 401, GroupName = "Video Truck Fire",
                    AgentId = 1013,
                    RecorderId = 1,
                    RowNo = 85,
                    StartTime = DateTime.Now,
                    StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"),
                    BookmarkCSV = string.Empty, CallComments = string.Empty, CalledID = string.Empty, Tag1 = string.Empty, Tag2 = string.Empty, 
                    Tag3 = string.Empty, Tag4 = string.Empty, CallerID = string.Empty, CustName = string.Empty, ANI = string.Empty, 
                    ANIName = string.Empty, ANIDetails = string.Empty, ANIPhone = string.Empty, RecorderName = demoRecorderName,
                    UniqueId = 40196,
                },
                new CallInfo
                {
                    //CallId = "70644e9d3e234ae49227a01f99cd0e1c", 
                    CallId = Guid.NewGuid().ToString("N"), CallType = 2, CallType_inq = 2, DurationInMilliSeconds = 31000, FileName = "policeInstructions.mp4",
                    ChannelId = 404, ChannelName = "Video Truck 2 Fire",
                    GroupId = 401, GroupName = "Video Truck Fire",
                    AgentId = 1014,
                    RecorderId = 1,
                    RowNo = 99,
                    StartTime = DateTime.Now,
                    StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"),
                    BookmarkCSV = string.Empty, CallComments = string.Empty, CalledID = string.Empty, Tag1 = string.Empty, Tag2 = string.Empty, 
                    Tag3 = string.Empty, Tag4 = string.Empty, CallerID = string.Empty, CustName = string.Empty, ANI = string.Empty, 
                    ANIName = string.Empty, ANIDetails = string.Empty, ANIPhone = string.Empty, RecorderName = demoRecorderName,
                    UniqueId = 40197,
                },
	            #endregion

                #region Group 402

                new CallInfo
                {
                    //CallId = "159b4484ed14434eb1402780821c70e2", 
                    CallId = Guid.NewGuid().ToString("N"), CallType = 2, CallType_inq = 2, DurationInMilliSeconds = 31000, FileName = "tireVideo.mp4",
                    ChannelId = 405, ChannelName = "Video Truck Vice",
                    AgentId = 1015,
                    GroupId = 402, GroupName = "Video Police",
                    RecorderId = 1,
                    RowNo = 98,
                    StartTime = DateTime.Now,
                    StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"),
                    BookmarkCSV = string.Empty, CallComments = string.Empty, CalledID = string.Empty, Tag1 = string.Empty, Tag2 = string.Empty, 
                    Tag3 = string.Empty, Tag4 = string.Empty, CallerID = string.Empty, CustName = string.Empty, ANI = string.Empty, 
                    ANIName = string.Empty, ANIDetails = string.Empty, ANIPhone = string.Empty, RecorderName = demoRecorderName,
                    UniqueId = 40298,
                },
                new CallInfo
                {
                    //CallId = "2c5ef3193cb7430da1d0c1355be5c4a9", 
                    CallId = Guid.NewGuid().ToString("N"), CallType = 2, CallType_inq = 2, DurationInMilliSeconds = 43000, FileName = "policeChase.mp4",
                    ChannelId = 406, ChannelName = "Video 911 Truck",
                    AgentId = 1016,
                    GroupId = 402, GroupName = "Video Police",
                    RecorderId = 1,
                    RowNo = 97,
                    StartTime = DateTime.Now,
                    StartTimeString= DateTime.Now.ToString("yyyyMMddHHmmss"),
                    BookmarkCSV = string.Empty, CallComments = string.Empty, CalledID = string.Empty, Tag1 = string.Empty, Tag2 = string.Empty, 
                    Tag3 = string.Empty, Tag4 = string.Empty, CallerID = string.Empty, CustName = string.Empty, ANI = string.Empty, 
                    ANIName = string.Empty, ANIDetails = string.Empty, ANIPhone = string.Empty, RecorderName = demoRecorderName,
                    UniqueId = 40299,
                },
	            #endregion
            };
        }

        //public static List<CallInfo> GetTexts()
        //{
        //    return new List<CallInfo>()
        //    {
        //        #region Group 101
		 
        //        new CallInfo
        //        {
        //            //CallId = "807be55349f843b0b4315553fbcc078b", 
        //            CallId = Guid.NewGuid().ToString("N"), CallType = 3, CallType_inq = 3, DurationInMilliSeconds = 1000, FileName = "SMS1.sms",
        //            ChannelId = 103, ChannelName = "SMS Admin 1 Fire",
        //            GroupId = 101, GroupName = "SMS Admin Fire",
        //            AgentId = 1001,
        //            RecorderId = 1,
        //            RowNo = 11,
        //            StartTime = DateTime.Now, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"),
        //            MessageBody = DateTime.Now.ToString("HH:mm:ss") + " IN: I need a police office immediately",
        //            //MessageBody = "SMS has no priority mechanisms today.",
        //            BookmarkCSV = string.Empty, CallComments = string.Empty, CalledID = string.Empty, Tag1 = string.Empty, Tag2 = string.Empty, 
        //            Tag3 = string.Empty, Tag4 = string.Empty, CallerID = string.Empty, CustName = string.Empty, ANI = string.Empty, 
        //            ANIName = string.Empty, ANIDetails = string.Empty, ANIPhone = string.Empty, RecorderName = demoRecorderName,
        //            UniqueId = 10198,
        //        },
        //        new CallInfo
        //        {
        //            //CallId = "059a08aa73504287be8b92f19b74b104", 
        //            CallId = Guid.NewGuid().ToString("N"), CallType = 3, CallType_inq = 3, DurationInMilliSeconds = 1000, FileName = "SMS2.sms",
        //            ChannelId = 104, ChannelName = "SMS Admin 2 Fire",
        //            GroupId = 101, GroupName = "SMS Admin Fire",
        //            AgentId = 1002,
        //            RecorderId = 1,
        //            RowNo = 87,
        //            StartTime = DateTime.Now, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"),
        //            MessageBody = DateTime.Now.ToString("HH:mm:ss") + " IN: I need a police office immediately ",
        //            //MessageBody = "Long story short:-  Disability community does ",
        //            BookmarkCSV = string.Empty, CallComments = string.Empty, CalledID = string.Empty, Tag1 = string.Empty, Tag2 = string.Empty, 
        //            Tag3 = string.Empty, Tag4 = string.Empty, CallerID = string.Empty, CustName = string.Empty, ANI = string.Empty, 
        //            ANIName = string.Empty, ANIDetails = string.Empty, ANIPhone = string.Empty, RecorderName = demoRecorderName,
        //            UniqueId = 10199,
        //        },
	       //     #endregion

        //        #region Group 102

        //        new CallInfo
        //        {
        //            //CallId = "23f5391838564fc4bb70a41d5fcb6fd2", 
        //            CallId = Guid.NewGuid().ToString("N"), CallType = 3, CallType_inq = 3, DurationInMilliSeconds = 1000, FileName = "SMS3.sms",
        //            ChannelId = 105, ChannelName = "SMS Admin 1 Police",
        //            AgentId = 1003,
        //            GroupId = 102, GroupName = "SMS Admin Police",
        //            RecorderId = 1,
        //            RowNo = 5,
        //            StartTime = DateTime.Now, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"),
        //            MessageBody = DateTime.Now.ToString("HH:mm:ss") + " IN: I need a police office immediately",
        //            //MessageBody = "SMS originally designed in Europe in early 1990s.",
        //            BookmarkCSV = string.Empty, CallComments = string.Empty, CalledID = string.Empty, Tag1 = string.Empty, Tag2 = string.Empty, 
        //            Tag3 = string.Empty, Tag4 = string.Empty, CallerID = string.Empty, CustName = string.Empty, ANI = string.Empty, 
        //            ANIName = string.Empty, ANIDetails = string.Empty, ANIPhone = string.Empty, RecorderName = demoRecorderName,
        //            UniqueId = 10298,
        //        },
        //        new CallInfo
        //        {
        //            //CallId = "09311cc559464292ab12ceac045da0aa", 
        //            CallId = Guid.NewGuid().ToString("N"), CallType = 3, CallType_inq = 3, DurationInMilliSeconds = 1000, FileName = "SMS4.sms",
        //            ChannelId = 106, ChannelName = "SMS Admin 2 Police",
        //            AgentId = 1004,
        //            GroupId = 102, GroupName = "SMS Admin Police",
        //            RecorderId = 1,
        //            RowNo = 7,
        //            StartTime = DateTime.Now, StartTimeString= DateTime.Now.ToString("yyyyMMddHHmmss"),
        //            MessageBody = DateTime.Now.ToString("HH:mm:ss") + " IN: I need a police office immediately",
        //            //MessageBody = "Technical Challenges of SMS to 911.",
        //            BookmarkCSV = string.Empty, CallComments = string.Empty, CalledID = string.Empty, Tag1 = string.Empty, Tag2 = string.Empty, 
        //            Tag3 = string.Empty, Tag4 = string.Empty, CallerID = string.Empty, CustName = string.Empty, ANI = string.Empty, 
        //            ANIName = string.Empty, ANIDetails = string.Empty, ANIPhone = string.Empty, RecorderName = demoRecorderName,
        //            UniqueId = 10299,
        //        },
	       //     #endregion
        //    };
        //}

        public static List<CallInfo> GetSocials()
        {
            return new List<CallInfo>()
            {
                #region Group 201
		 
                new CallInfo
                {
                    //CallId = "05e7c0cdfc024b7f891abfdebb7cf577", 
                    CallId = Guid.NewGuid().ToString("N"), CallType = 4, CallType_inq = 4, DurationInMilliSeconds = 1000, FileName = "Tweet1.tsv",
                    ChannelId = 203, ChannelName = "Social Admin 1 Fire",
                    AgentId = 1005,
                    GroupId = 201, GroupName = "Social Admin Fire",
                    RecorderId = 1,
                    RowNo = 11,
                    StartTime = DateTime.Now, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"),
                    MessageBody = "Free Full Day of Online ASP.NET Training on February 22!",
                    BookmarkCSV = string.Empty, CallComments = string.Empty, CalledID = string.Empty, Tag1 = string.Empty, Tag2 = string.Empty, 
                    Tag3 = string.Empty, Tag4 = string.Empty, CallerID = string.Empty, CustName = string.Empty, ANI = string.Empty, 
                    ANIName = string.Empty, ANIDetails = string.Empty, ANIPhone = string.Empty, RecorderName = demoRecorderName,
                    UniqueId = 20198,
                },
                new CallInfo
                {
                    //CallId = "29e47d6eefb149beba21554cbc7138ec", 
                    CallId = Guid.NewGuid().ToString("N"), CallType = 4, CallType_inq = 4, DurationInMilliSeconds = 1000, FileName = "Tweet2.tsv",
                    ChannelId = 204, ChannelName = "SMS Admin 2 Fire",
                    AgentId = 1006,
                    GroupId = 201, GroupName = "Social Admin Fire",
                    RecorderId = 1,
                    RowNo = 1,
                    StartTime = DateTime.Now, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"),
                    MessageBody = "Visual Studio Virtual Launch on September 12.",
                    BookmarkCSV = string.Empty, CallComments = string.Empty, CalledID = string.Empty, Tag1 = string.Empty, Tag2 = string.Empty, 
                    Tag3 = string.Empty, Tag4 = string.Empty, CallerID = string.Empty, CustName = string.Empty, ANI = string.Empty, 
                    ANIName = string.Empty, ANIDetails = string.Empty, ANIPhone = string.Empty, RecorderName = demoRecorderName,
                    UniqueId = 20199,
                },
	            #endregion

                #region Group 202

                new CallInfo
                {
                    //CallId = "7e1ef8606d0949c68237d3a0ac0b7be2", 
                    CallId = Guid.NewGuid().ToString("N"), CallType = 4, CallType_inq = 4, DurationInMilliSeconds = 1000, FileName = "Tweet3.tsv",
                    ChannelId = 205, ChannelName = "Social Admin 1 Police",
                    AgentId = 1007,
                    GroupId = 202, GroupName = "Social Admin Police",
                    RecorderId = 1,
                    RowNo = 5,
                    StartTime = DateTime.Now, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"),
                    MessageBody = "Dig this new USB 3.0 hub. Ability to turn off ports is great for audio comp switching. Also charge port is nice.",
                    BookmarkCSV = string.Empty, CallComments = string.Empty, CalledID = string.Empty, Tag1 = string.Empty, Tag2 = string.Empty, 
                    Tag3 = string.Empty, Tag4 = string.Empty, CallerID = string.Empty, CustName = string.Empty, ANI = string.Empty, 
                    ANIName = string.Empty, ANIDetails = string.Empty, ANIPhone = string.Empty, RecorderName = demoRecorderName,
                    UniqueId = 20298,
                },
                new CallInfo
                {
                    //CallId = "2fa1d805038d4c659abfae1bcc5cabcd", 
                    CallId = Guid.NewGuid().ToString("N"), CallType = 4, CallType_inq = 4, DurationInMilliSeconds = 1000, FileName = "Tweet4.tsv",
                    ChannelId = 206, ChannelName = "Social Admin 2 Police",
                    AgentId = 1008,
                    GroupId = 202, GroupName = "Social Admin Police",
                    RecorderId = 1,
                    RowNo = 7,
                    StartTime = DateTime.Now, StartTimeString= DateTime.Now.ToString("yyyyMMddHHmmss"),
                    MessageBody = "WebMatrix 2: New Templates, Improved IntelliSense, Windows Azure Integration.",
                    BookmarkCSV = string.Empty, CallComments = string.Empty, CalledID = string.Empty, Tag1 = string.Empty, Tag2 = string.Empty, 
                    Tag3 = string.Empty, Tag4 = string.Empty, CallerID = string.Empty, CustName = string.Empty, ANI = string.Empty, 
                    ANIName = string.Empty, ANIDetails = string.Empty, ANIPhone = string.Empty, RecorderName = demoRecorderName,
                    UniqueId = 20299,
                },
	            #endregion
            };
        }

        public static List<CallInfo> GetEmails()
        {
            return new List<CallInfo>()
            {
                #region Group 301
		 
                new CallInfo
                {
                    //CallId = "02dc3d3f46944f668b0fbf733f03c1d5", 
                    CallId = Guid.NewGuid().ToString("N"), CallType = 5, CallType_inq = 5, DurationInMilliSeconds = 1000, FileName = "Email1.eml",
                    ChannelId = 303, ChannelName = "Email Admin 1 Fire",
                    AgentId = 1009,
                    GroupId = 301, GroupName = "Email Admin Police",
                    RecorderId = 1,
                    RowNo = 86,
                    StartTime = DateTime.Now, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"),
                    MessageBody = "Dear UVW, I almost have done my tasks and email you regarding this.",
                    BookmarkCSV = string.Empty, CallComments = string.Empty, CalledID = string.Empty, Tag1 = string.Empty, Tag2 = string.Empty, 
                    Tag3 = string.Empty, Tag4 = string.Empty, CallerID = string.Empty, CustName = string.Empty, ANI = string.Empty, 
                    ANIName = string.Empty, ANIDetails = string.Empty, ANIPhone = string.Empty, RecorderName = demoRecorderName,
                    UniqueId = 30198,
                },
                new CallInfo
                {
                    //CallId = "120d894858154130bccd75dbacc1b1d4", 
                    CallId = Guid.NewGuid().ToString("N"), CallType = 5, CallType_inq = 5, DurationInMilliSeconds = 1000, FileName = "Email2.eml",
                    ChannelId = 304, ChannelName = "Email Admin 1 Police",
                    AgentId = 1010,
                    GroupId = 301, GroupName = "Email Admin Police",
                    RecorderId = 1,
                    RowNo = 1,
                    StartTime = DateTime.Now, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"),
                    MessageBody = "Hello XYZ, Please find the attachment. I have added my notes against all issues.",
                    BookmarkCSV = string.Empty, CallComments = string.Empty, CalledID = string.Empty, Tag1 = string.Empty, Tag2 = string.Empty, 
                    Tag3 = string.Empty, Tag4 = string.Empty, CallerID = string.Empty, CustName = string.Empty, ANI = string.Empty, 
                    ANIName = string.Empty, ANIDetails = string.Empty, ANIPhone = string.Empty, RecorderName = demoRecorderName,
                    UniqueId = 30199,
                },
	            #endregion

                #region Group 302

                new CallInfo
                {
                    //CallId = "1b8ff99f7e6349e1a2fe79e2cb8ea52d", 
                    CallId = Guid.NewGuid().ToString("N"), CallType = 5, CallType_inq = 5, DurationInMilliSeconds = 1000, FileName = "Email3.eml",
                    ChannelId = 305, ChannelName = "Email CC 1",
                    AgentId = 1011,
                    GroupId = 302, GroupName = "Email Call Center",
                    RecorderId = 1,
                    RowNo = 5,
                    StartTime = DateTime.Now, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"),
                    MessageBody = "Hello DEF, I agree with you but Silverlight has very limited number of codecs supported.",
                    BookmarkCSV = string.Empty, CallComments = string.Empty, CalledID = string.Empty, Tag1 = string.Empty, Tag2 = string.Empty, 
                    Tag3 = string.Empty, Tag4 = string.Empty, CallerID = string.Empty, CustName = string.Empty, ANI = string.Empty, 
                    ANIName = string.Empty, ANIDetails = string.Empty, ANIPhone = string.Empty, RecorderName = demoRecorderName,
                    UniqueId = 30298,
                },
                new CallInfo
                {
                    //CallId = "322aaa24e26d412196decbbb9754142d", 
                    CallId = Guid.NewGuid().ToString("N"), CallType = 5, CallType_inq = 5, DurationInMilliSeconds = 1000, FileName = "Email4.eml",
                    ChannelId = 306, ChannelName = "Email CC 2",
                    AgentId = 1012,
                    GroupId = 302, GroupName = "Email Call Center",
                    RecorderId = 1,
                    RowNo = 7,
                    StartTime = DateTime.Now, StartTimeString= DateTime.Now.ToString("yyyyMMddHHmmss"),
                    MessageBody = "Dear ABC, I have some confusions about yor suggestions and will have a chat with you regarding this issue.",
                    BookmarkCSV = string.Empty, CallComments = string.Empty, CalledID = string.Empty, Tag1 = string.Empty, Tag2 = string.Empty, 
                    Tag3 = string.Empty, Tag4 = string.Empty, CallerID = string.Empty, CustName = string.Empty, ANI = string.Empty, 
                    ANIName = string.Empty, ANIDetails = string.Empty, ANIPhone = string.Empty, RecorderName = demoRecorderName,
                    UniqueId = 30299,
                },
	            #endregion
            };
        }

        #endregion

        #endregion

        #region ------- Demo Page Data -------
        public static List<CallInfo> GetDemoDataForPage()
        {
            List<CallInfo> democalls = new List<CallInfo>();
            //democalls.Add(GetVideos()[0]);
            democalls.AddRange(GetVideos());
            //democalls.AddRange(GetTexts());
            democalls.AddRange(GetSocials());
            democalls.AddRange(GetEmails());

            return democalls.OrderBy(ci => ci.CallId).ToList();
            //return democalls.OrderBy(ci => Guid.NewGuid()).ToList();
        }

        #endregion

        #region ------- Demo Data Search -------

        public static List<CallInfo> SearchInDemoData(CategoryGroupExtension catGrpExt)
        {
            if (catGrpExt.GroupExtensions.Count > 0)
            {
                IEnumerable<int> extIds = catGrpExt.GroupExtensions.SelectMany(ge => ge.ExtensionIds).Select(s => int.Parse(s)).ToList();
                switch (catGrpExt.GroupType)
                {
                    case GroupType.Video:
                        //var csvExts = catGrpExt.GroupExtensions.SelectMany(ge => ge.ExtensionIdsCSV);
                        //var rVideoResults = from v in GetVideos()
                        //                    where extIds.Contains(v.ChannelId)
                        //                    select v;
                        var demoVideos = GetVideos().Where(v => extIds.Contains(v.ChannelId));
                        return demoVideos.ToList();
                    //case GroupType.Text:
                    //    var demoSMS = GetTexts().Where(v => extIds.Contains(v.ChannelId));
                    //    return demoSMS.ToList();
                    case GroupType.Social:
                        var demoSocial = GetSocials().Where(v => extIds.Contains(v.ChannelId));
                        return demoSocial.ToList();
                    case GroupType.Email:
                        var demoEmail = GetEmails().Where(v => extIds.Contains(v.ChannelId));
                        return demoEmail.ToList();
                    default:
                        return null;
                }
            }
            return null;
        }

        public static CallInfo SearchInDemoDataBasedOnFileName(string fileName, FileType fileType)
        {
            switch (fileType)
            {
                case FileType.Video:
                    return GetVideos().FirstOrDefault(v => v.FileName== fileName);
                //case FileType.Text:
                //    return GetTexts().FirstOrDefault(t => t.FileName == fileName);
                case FileType.Social:
                    return GetSocials().FirstOrDefault(s => s.FileName == fileName);
                case FileType.Email:
                    return GetEmails().FirstOrDefault(e => e.FileName == fileName);
                default:
                    return null;
            }
        }

        #endregion

    }
}

//public static List<CallInfo> getDummyListSMS()
//{
//    List<CallInfo> SmsEmailSocial = new List<CallInfo>();//20130207015719
//    //SmsEmailSocial.Add(new CallInfo { CallId = "ba38446d851d43f7b0b8051a87a2f9eb", CallType = 5, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "testing.DSF", ChannelId = 1013, ChannelName = "", GroupName = "", MessageBody = "" });

//    //************************* Video *************************//
//    //DateTime.Now.Subtract(new TimeSpan(5,0,0)).ToString("yyyyMMddHHmmss")
//    SmsEmailSocial.Add(new CallInfo { CallId = "b74e4f6247144a19a2707c40860e825d", CallType = 2, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "test.wmv", ChannelId = 1004, ChannelName = "", GroupName = "", MessageBody = "" });
//    SmsEmailSocial.Add(new CallInfo { CallId = "b15bad0fde0b4c7fb0429a77600d9611", CallType = 2, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "test.wmv", ChannelId = 1005, ChannelName = "", GroupName = "", MessageBody = "" });
//    SmsEmailSocial.Add(new CallInfo { CallId = "95ff00c941c34494b9a98b46063e7fb6", CallType = 2, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "test.wmv", ChannelId = 1006, ChannelName = "", GroupName = "", MessageBody = "" });
//    SmsEmailSocial.Add(new CallInfo { CallId = "21f50968fc94436f98c51f052ed58e05", CallType = 2, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "test.wmv", ChannelId = 1007, ChannelName = "", GroupName = "", MessageBody = "" });
//    SmsEmailSocial.Add(new CallInfo { CallId = "47dc1d37482a49189d9c78265f499a00", CallType = 2, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "test.wmv", ChannelId = 1008, ChannelName = "", GroupName = "", MessageBody = "" });
//    SmsEmailSocial.Add(new CallInfo { CallId = "e21edffd387e4bd68ffa2c72475c790d", CallType = 2, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "test.wmv", ChannelId = 1009, ChannelName = "", GroupName = "", MessageBody = "" });
//    SmsEmailSocial.Add(new CallInfo { CallId = "247c293cbc0e4b39878349d71dfbfcff", CallType = 2, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "test.wmv", ChannelId = 1010, ChannelName = "", GroupName = "", MessageBody = "" });
//    SmsEmailSocial.Add(new CallInfo { CallId = "2320d2cd93ae4ded9c295be2c59961c2", CallType = 2, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "test.wmv", ChannelId = 1011, ChannelName = "", GroupName = "", MessageBody = "" });
//    SmsEmailSocial.Add(new CallInfo { CallId = "c0855a8c161b480e9cd37d470540b27b", CallType = 2, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "test.wmv", ChannelId = 1012, ChannelName = "", GroupName = "", MessageBody = "" });
//    SmsEmailSocial.Add(new CallInfo { CallId = "56271dbe9c234ee2a20dd2258d633074", CallType = 2, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "test.wmv", ChannelId = 1013, ChannelName = "", GroupName = "", MessageBody = "" });

//    //************************* SMS *************************//

//    SmsEmailSocial.Add(new CallInfo { CallId = "03b4df0dc3214d8e964e2a88e61390a7", MessageBody = "<strong>Long story short:</strong> </br> <p>Disability community does not favor IP relay and does not favor relay solutions for SMS.</p>", CallType = 3, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "testing.DSF", ChannelId = 1007, ChannelName = "CS 4 CC", GroupName = "Customer Service" });
//    SmsEmailSocial.Add(new CallInfo { CallId = "3988911c0721431aa9ec20fdb258b721", MessageBody = "SMS originally designed in Europe in early 1990s", CallType = 3, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "testing.DSF", ChannelId = 1000, ChannelName = "", GroupName = "" });
//    SmsEmailSocial.Add(new CallInfo { CallId = "89a491d69bd946a9aa06b0e24ee5f847", MessageBody = "SMS has no priority mechanisms today.", CallType = 3, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "testing.DSF", ChannelId = 1000, ChannelName = "", GroupName = "" });
//    SmsEmailSocial.Add(new CallInfo { CallId = "e83781d1051349679e9843e0b8716ab6", MessageBody = "Technical Challenges of SMS to 911", CallType = 3, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "testing.DSF", ChannelId = 1000, ChannelName = "", GroupName = "" });
//    SmsEmailSocial.Add(new CallInfo { CallId = "6a7916d10433498fbdecc4cb31e26be5", MessageBody = "SMS to 911 has to work end to end – from the mobile device to the PSAP. <ul><li>This is mostly an issue on the PSAP side</li></ul>", CallType = 3, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "testing.DSF", ChannelId = 1000, ChannelName = "", GroupName = "" });
//    SmsEmailSocial.Add(new CallInfo { CallId = "7bf40b5827984b99912ecc2793928885", MessageBody = "Additional Sample Requirements for SMS to 911", CallType = 3, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "testing.DSF", ChannelId = 1000, ChannelName = "", GroupName = "" });
//    SmsEmailSocial.Add(new CallInfo { CallId = "6f43e239491c41589e9063becec29d10", MessageBody = "Non-CMRS-originated SMS, such as portal-based SMS messaging, is not supported.", CallType = 3, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "testing.DSF", ChannelId = 1000, ChannelName = "", GroupName = "" });
//    SmsEmailSocial.Add(new CallInfo { CallId = "8cda1b16dace4d4782203c5bb080b732", MessageBody = "MMS to 911 is for further study, as sometimes including multiple recipients in an SMS message results in an MMS message.", CallType = 3, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "testing.DSF", ChannelId = 1000, ChannelName = "", GroupName = "" });
//    SmsEmailSocial.Add(new CallInfo { CallId = "aa0d8a8787a246b39cb69c400a4b0a7a", MessageBody = "Non-service initialized mobile devices are not supported.", CallType = 3, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "testing.DSF", ChannelId = 1000, ChannelName = "", GroupName = "" });
//    SmsEmailSocial.Add(new CallInfo { CallId = "c22e7afbf905463fab78aa39a30ea1cb", MessageBody = "The wireless operator is not responsible for the translation of the SMS message to the format specified by the receiving PSAP (since there are over 6,000 PSAPs).", CallType = 3, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "testing.DSF", ChannelId = 1000, ChannelName = "", GroupName = "" });

//    //************************* Social *************************//

//    SmsEmailSocial.Add(new CallInfo { CallId = "97dd3396bdd24f09b92e0ed7fdb11ced", CallType = 4, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "testing.DSF", ChannelId = 1004, ChannelName = "CS 4 CC", GroupName = "Customer Service", MessageBody = "aspConf - The Free, Online ASP.NET Conference - July 17-18" });
//    SmsEmailSocial.Add(new CallInfo { CallId = "f05b82af137b4218b85c79fe48483757", CallType = 4, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "testing.DSF", ChannelId = 1005, ChannelName = "", GroupName = "", MessageBody = "Free Full Day of Online ASP.NET Training on February 22!" });
//    SmsEmailSocial.Add(new CallInfo { CallId = "b74e7e3edb4b44ff8560e35d8b47f7d0", CallType = 4, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "testing.DSF", ChannelId = 1006, ChannelName = "", GroupName = "", MessageBody = "Roadmap for ASP.NET Fall 2012 Update." });
//    SmsEmailSocial.Add(new CallInfo { CallId = "03817e08c65e440781b3eecf4e5ae6e9", CallType = 4, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "testing.DSF", ChannelId = 1007, ChannelName = "", GroupName = "", MessageBody = "New Ajax Control Toolkit Release" });
//    SmsEmailSocial.Add(new CallInfo { CallId = "a3ac1257a4a740589de6e0c8c15fdb49", CallType = 4, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "testing.DSF", ChannelId = 1008, ChannelName = "", GroupName = "", MessageBody = "Visual Studio Virtual Launch on September 12" });
//    SmsEmailSocial.Add(new CallInfo { CallId = "02cf408070064a6f977ce89e51703ea5", CallType = 4, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "testing.DSF", ChannelId = 1009, ChannelName = "", GroupName = "", MessageBody = "WebMatrix 2: New Templates, Improved IntelliSense, Windows Azure Integration" });
//    SmsEmailSocial.Add(new CallInfo { CallId = "7c330386b1a64f798934b02a2da2ad82", CallType = 4, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "testing.DSF", ChannelId = 1010, ChannelName = "", GroupName = "", MessageBody = "Productivity Power Tools Has Arrived for Visual Studio 2012!" });
//    SmsEmailSocial.Add(new CallInfo { CallId = "cf9d4c50ff524d5d9cc169f2ebadcccb", CallType = 4, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "testing.DSF", ChannelId = 1011, ChannelName = "", GroupName = "", MessageBody = "<strong>Sign On Testers’ Doors</strong></br></br> Do not disturb. Already disturbed!" });
//    SmsEmailSocial.Add(new CallInfo { CallId = "13b427ef00af435dada8c07140110ccb", CallType = 4, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "testing.DSF", ChannelId = 1012, ChannelName = "", GroupName = "", MessageBody = "<strong>Programmer Responses</strong></br><ul><li>“It works fine on MY computer”</li><li>“It worked yesterday.”</li><li>“It must be a hardware problem.”</li><li>“What did you type in wrong to get it to crash?”</li><li>“You must have the wrong version.”</li><li>“Somebody must have changed my code.”</li><li>“Why do you want to do it that way?”</li><li>“I thought I fixed that.”</li></ul>" });
//    //SmsEmailSocial.Add(new CallInfo { CallId = "ba38446d851d43f7b0b8051a87a2f9eb", CallType = 4, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "testing.DSF", ChannelId = 1013, ChannelName = "", GroupName = "", MessageBody = "<strong>The Search</strong><br /><br />Under a streetlight, on a very dark night, a software tester was looking for a set of lost keys.<br />A policeman came by, asked him about the object of his search, and joined him to help. After the two had searched for some time, the policeman asked, \"Are you sure you lost them here?\"<br />\"Oh, no,\" said the software tester. \"I lost the keys somewhere else.\"<br />\"Then why are you looking for them over here?\" the policeman asked.<br />\"Because this is where the light is!\" the software tester replied.<br /><br /><strong>Moral: Do not be so stupid that you search for bugs only at the obvious places.</strong>" });
//    SmsEmailSocial.Add(new CallInfo { CallId = "ba38446d851d43f7b0b8051a87a2f9eb", CallType = 4, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "testing.DSF", ChannelId = 1013, ChannelName = "", GroupName = "", MessageBody = "A guy is standing on the corner of the street smoking one cigarette after another. <br/>A lady walking by notices him and says<br/>\"Hey, don't you know that those things can kill you? I mean, didn't you see the giant warning on the box?!\"<br/>\"That's OK\" says the guy, \"I'm a computer programmer\"<br/>\"So? What's that got to do with anything?\"<br/><br/><strong>\"We don't care about warnings. We only care about errors.\"<strong>" });

//    //************************* E-mail *************************//
//    SmsEmailSocial.Add(new CallInfo { CallId = "84fd4464d1d44c38b43e1f0f2db89e75", CallType = 5, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "testing.DSF", ChannelId = 1004, ChannelName = "", GroupName = "", MessageBody = "Hello Sher, </br>I almost complete the User Login and authentication functionality. I have some confusions about User Authentication and will have a chat with Vasanth regarding this issue" });
//    SmsEmailSocial.Add(new CallInfo { CallId = "d94992d44c994723ab3b3f1ca405526e", CallType = 5, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "testing.DSF", ChannelId = 1005, ChannelName = "", GroupName = "", MessageBody = "Hello Sher, </br>Today I have detected some layout changes in Evaluation module. Updated Bug List. {PFA “DefectSheet.xls”} Please fix these bugs as soon as possible." });
//    SmsEmailSocial.Add(new CallInfo { CallId = "71b9fbe6cb8144b3b48ca62f9ed69511", CallType = 5, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "testing.DSF", ChannelId = 1006, ChannelName = "", GroupName = "", MessageBody = "Tested Live Player and writing bug list.{“Player Bugs Version 4” } “Player Bug List Version 4” is not complete at the moment. I will send you updated copy tomorrow." });
//    SmsEmailSocial.Add(new CallInfo { CallId = "09c302662d3a401993a894459c569d8d", CallType = 5, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "testing.DSF", ChannelId = 1007, ChannelName = "", GroupName = "", MessageBody = "Hello Vasanth,</br>I agree that there are advantages of using WMV9 Screen codec but Silverlight has very limited number of codecs supported. So I'm afraid there is only one way to make it work - use standard WMV9 codec." });
//    SmsEmailSocial.Add(new CallInfo { CallId = "d0c87ceabc0341a9ade5fbfecd39a33f", CallType = 5, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "testing.DSF", ChannelId = 1008, ChannelName = "", GroupName = "", MessageBody = "Hello Sher and Vasanth,</br>We are facing a very acute issue regarding playback. File Player is not functioning properly. For details please see the attached file." });
//    SmsEmailSocial.Add(new CallInfo { CallId = "dbfada8c3af54e2186582c2f22a5238d", CallType = 5, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "testing.DSF", ChannelId = 1009, ChannelName = "", GroupName = "", MessageBody = "Below are comments from a trusted source. Subject to Trey’s input I recommend the highlighted responses</br>1.)	I like <span style='background-color: yellow;'>QA Evaluation</span>, but I think Evaluation will work as well." });
//    SmsEmailSocial.Add(new CallInfo { CallId = "fdfdaf02ebcb4646a33cfbd04452490e", CallType = 5, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "testing.DSF", ChannelId = 1010, ChannelName = "", GroupName = "", MessageBody = "Attached is an overview of NG9-1-1 as it applies to logging. A Logging Service is a core element of the ESInet (Emergency Services IP Network) and has many requirements new to logging recorders." });
//    SmsEmailSocial.Add(new CallInfo { CallId = "921ccb1a144645bf8ac2509c570b735b", CallType = 5, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "testing.DSF", ChannelId = 1011, ChannelName = "", GroupName = "", MessageBody = "I have discussed release 9.0 bugs/issues with Guy and German respectively. All the points have been discussed and there are no more confusions." });
//    SmsEmailSocial.Add(new CallInfo { CallId = "7691f4d3d3094c839a49088a2293d98b", CallType = 5, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "testing.DSF", ChannelId = 1012, ChannelName = "", GroupName = "", MessageBody = "Hey Sarfraz, </br>Today when I am checking the rewind option on the live player I came to know that the Channel state wasn’t properly handled.. after the call ends I mean no voice the channel still remains in active state and so the player for about 30 minutes or so " });
//    SmsEmailSocial.Add(new CallInfo { CallId = "8af86f02be4b401eb51b372501373399", CallType = 5, StartTimeString = DateTime.Now.ToString("yyyyMMddHHmmss"), StartTime = DateTime.Now, FileName = "testing.DSF", ChannelId = 1013, ChannelName = "", GroupName = "", MessageBody = "Hello Sher, </br>Please find the attachment. I have added my notes against all issues." });





//    //return sms.OrderBy(emp => Guid.NewGuid()).ToList();//random sort

//    return SmsEmailSocial;

//    //for (int i = 0; i < 7; i++)
//    //{
//    //    Guid g = Guid.NewGuid();
//    //}
//}
