﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RevCord.DataContracts.Response;
using RevCord.DataContracts.Request;
using RevCord.DataContracts.SurveyEntities;
using RevCord.DataAccess;
using RevCord.DataContracts.Messages;
using RevCord.DataContracts.InquireEntities;

using RevCord.DataContracts.MessageBase;
using RevCord.DataContracts;
using RevCord.DataContracts.Criteria;
using RevCord.Util;
using System.Data.SqlClient;
using RevCord.DataAccess.Util;

namespace RevCord.BusinessLogic
{
    public class InquireManager
    {
        public bool CheckAlreadyInvited(string email, InvitationStatus status, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Inquire, "CheckAlreadyInvited", tenantId, "CheckAlreadyInvited function has been called successfully."));
                return new InquireDAL(tenantId).CheckAlreadyInvited(email, status);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Inquire, "CheckAlreadyInvited", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Inquire, "CheckAlreadyInvited", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InquireResponse CreateInvitation(InquireRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Inquire, "CreateInvitation", request.TenantId, "CreateInvitation function has been called successfully."));
                switch (request.PersistType)
                {
                    case PersistType.Insert:
                        int lastId = new InquireDAL(request.TenantId).InsertInvitation(request.Invitation);
                        //request.Invitation.Id = lastId;
                        return new InquireResponse
                        {
                            Acknowledge = lastId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                            InvitationId = lastId,
                            Invitation = request.Invitation,
                            Message = string.Format("Invitation has been saved successfully against invitee email:{0}.", request.Invitation.SentToEmail)
                        };
                    //case PersistType.Update:
                    //    int updatedId = InquireDAL.UpdateInvitation(request.Invitation, DateTime.Now, request.UserId);
                    //    return new InquireResponse
                    //    {
                    //        Acknowledge = updatedId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    //        InvitationId = updatedId,
                    //        Message = string.Format("Invitation has been updated successfully against invitationId:{0}.", request.Invitation.Id)
                    //    };
                    default:
                        return null;
                }
                return null;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Inquire, "CreateInvitation", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Inquire, "CreateInvitation", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool ChangeInvitationStatus(int invitationId, InvitationStatus status, int? userId, DateTime? modifiedDate, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Inquire, "ChangeInvitationStatus", tenantId, "ChangeInvitationStatus function has been called successfully. invitationId = " + invitationId + " status = " + status));
                return new InquireDAL(tenantId).ChangeInvitationStatus(invitationId, status, userId, modifiedDate) > 0;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Inquire, "ChangeInvitationStatus", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Inquire, "ChangeInvitationStatus", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool CancelInvitation(int invitationId, int userId, DateTime modifiedDate, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Inquire, "CancelInvitation", tenantId, "CancelInvitation function has been called successfully. invitationId = " + invitationId));
                return new InquireDAL(tenantId).CancelInvitation(invitationId, InvitationStatus.Revoked, userId, modifiedDate) > 0;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Inquire, "CancelInvitation", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Inquire, "CancelInvitation", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        public Invitation GetInvitationById(int invitationId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Inquire, "GetInvitationById", tenantId, "GetInvitationById function has been called successfully. invitationId = " + invitationId));
                return new InquireDAL(tenantId).GetInvitationById(invitationId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Inquire, "GetInvitationById", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Inquire, "GetInvitationById", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InquireResponse GetInvitationsByWhereClause(InquireRequest request)//InquireCriteria criteria
        {
            try
            {
                StringBuilder sbWhereClause = new StringBuilder();
                //sbWhereClause.Append(" 1 = 1 ");
                InquireCriteria criteria = request.Criteria;
                #region ------- Search Criteria -------

                if (criteria != null)
                {
                    if (criteria.SentByUserId > 0)
                    {
                        sbWhereClause.Append(" AND ");
                        sbWhereClause.Append(" (SentBy = " + criteria.SentByUserId + ")");
                        sbWhereClause.AppendLine();
                    }
                    if (!string.IsNullOrEmpty(criteria.InvitationCode))
                    {
                        sbWhereClause.Append(" AND (InviteCode = " + criteria.InvitationCode + ")");
                        sbWhereClause.AppendLine();
                    }
                    if (criteria.InvitationStatuses != null)
                    {
                        sbWhereClause.Append(" AND (StatusId IN ( ");
                        foreach (var status in criteria.InvitationStatuses)
                        {
                            sbWhereClause.AppendFormat("{0},", (int)status);
                        }
                        sbWhereClause.RemoveLast(",");
                        sbWhereClause.Append(" ) ");
                        sbWhereClause.Append(" ) ");
                        sbWhereClause.AppendLine();
                    }

                    sbWhereClause.AppendLine();
                }
                #endregion

                System.Diagnostics.Debug.WriteLine(sbWhereClause.ToString());
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Inquire, "GetInvitationsByWhereClause", request.TenantId, "GetInvitationsByWhereClause function has been called successfully. sbWhereClause = " + sbWhereClause.ToString()));
                return new InquireResponse
                {
                    Invitations = new InquireDAL(request.TenantId).GetInvitationsByWhereClause(sbWhereClause.ToString()),
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Inquire, "GetInvitationsByWhereClause", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Inquire, "GetInvitationsByWhereClause", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InquireResponse AcceptInvitation(InquireRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Inquire, "AcceptInvitation", request.TenantId, "AcceptInvitation function has been called successfully. request.RegisterUser.InvitationId = " + request.RegisterUser.InvitationId));

                int lastUserId = new InquireDAL(request.TenantId).AcceptInvitation(request.RegisterUser, request.RegisterUser.InvitationId, InvitationStatus.Accepted, request.ModifiedDate);
                //request.Invitation.Id = lastId;
                return new InquireResponse
                {
                    Acknowledge = lastUserId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    InvitationId = request.RegisterUser.InvitationId,
                    UserId = lastUserId,
                    Message = string.Format("User has been saved successfully against invitation Id:{0} and Invitee Email:{1}.", request.RegisterUser.InvitationId, request.RegisterUser.Email)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Inquire, "AcceptInvitation", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Inquire, "AcceptInvitation", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool RejectInvitation(int invitationId, DateTime modifiedDate, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Inquire, "RejectInvitation", tenantId, "RejectInvitation function has been called successfully. invitationId = " + invitationId));
                return new InquireDAL(tenantId).RejectInvitation(invitationId, InvitationStatus.Rejected, modifiedDate) > 0;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Inquire, "RejectInvitation", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Inquire, "RejectInvitation", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InquireGMResponse AddUserToGroup(int groupNum, int Ext, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Inquire, "AddUserToGroup", tenantId, "AddUserToGroup function has been called successfully. groupNum = " + groupNum + " Ext = " + Ext));
                InquireGMResponse inqGMResponse = new InquireGMResponse();
                var dal = new InquireDAL(tenantId);
                dal.AddUserToGroup(groupNum, Ext);
                return inqGMResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Inquire, "AddUserToGroup", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Inquire, "AddUserToGroup", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool licensecreate(License_Information Obj_license_information, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Inquire, "licensecreate", tenantId, "licensecreate function has been called successfully. "));
                return new InquireDAL(tenantId).licansevalueinsert(Obj_license_information); ;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Inquire, "licensecreate", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Inquire, "licensecreate", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public string getcustomerid(int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Inquire, "getcustomerid", tenantId, "getcustomerid function has been called successfully. "));
                return new InquireDAL(tenantId).getcustomerid();
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Inquire, "getcustomerid", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Inquire, "getcustomerid", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public string getexpirydate(int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Inquire, "getexpirydate", tenantId, "getexpirydate function has been called successfully. "));
                return new InquireDAL(tenantId).getexpirydate();
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Inquire, "getexpirydate", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Inquire, "getexpirydate", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public string getextensionusingusernum(string s_usernum, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Inquire, "getextensionusingusernum", tenantId, "getextensionusingusernum function has been called successfully. s_usernum =  " + s_usernum));
                return new InquireDAL(tenantId).getextensionusingusernum(s_usernum);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Inquire, "getextensionusingusernum", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Inquire, "getextensionusingusernum", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public int isalreadyexistscheck(string s_mailid, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Inquire, "isalreadyexistscheck", tenantId, "isalreadyexistscheck function has been called successfully. s_mailid = " + s_mailid));
                return new InquireDAL(tenantId).isalreadyexistscheck(s_mailid);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Inquire, "isalreadyexistscheck", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Inquire, "isalreadyexistscheck", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InquireResponse SaveEventInvitation(InquireRequest request)
        {
            try
            {
                switch (request.PersistType)
                {
                    case PersistType.Insert:
                        int lastEventId = InquireDAL.SaveEventInvitation(request.EventInvitationGroup, request.TenantId);
                        Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Inquire, "SaveEventInvitation", request.TenantId, "SaveEventInvitation function has been called successfully. lastEventId = " + lastEventId));
                        //request.Invitation.Id = lastId;
                        return new InquireResponse
                        {
                            Acknowledge = lastEventId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                            InvitationId = lastEventId,
                            EventInvitationGroup = request.EventInvitationGroup,
                            Message = string.Format("Invitation has been saved successfully against invitee email:{0}.", request.EventInvitationGroup.SentToEmail)
                        };
                    case PersistType.Update:
                        if (request.Action == "UpdateEventInvitationStatusOnly")
                        {
                            int updatedId = InquireDAL.ChangeEventInvitationStatus(request.InvitationId, request.InvitationStatus, request.TenantId, request.AcceptedDate);
                            Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Inquire, "SaveEventInvitation", request.TenantId, "SaveEventInvitation function has been called successfully. updatedId = " + updatedId));
                            return new InquireResponse
                            {
                                Acknowledge = updatedId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                                InvitationId = updatedId,
                                Message = string.Format("Invitation has been updated successfully against invitationGroupId:{0}.", request.InvitationId)
                            };
                        }
                        else
                        {
                            int updatedId = InquireDAL.UpdateEventInvitation(request.EventInvitationGroup, DateTime.Now, request.TenantId);
                            return new InquireResponse
                            {
                                Acknowledge = updatedId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                                InvitationId = updatedId,
                                Message = string.Format("Invitation has been updated successfully against invitationGroupId:{0}.", request.EventInvitationGroup.Id)
                            };
                        }
                    default:
                        return null;
                }
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Inquire, "SaveEventInvitation", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Inquire, "SaveEventInvitation", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public EventInvitationGroup GetEventInvitation(int invitationGroupId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Inquire, "GetEventInvitation", tenantId, "GetEventInvitation function has been called successfully. invitationGroupId = " + invitationGroupId));
                return InquireDAL.GetEventInvitationById(invitationGroupId, tenantId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Inquire, "GetEventInvitation", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Inquire, "GetEventInvitation", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InquireResponse GetPictureEventsByCallId(int tenantId, string callId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Inquire, "GetPictureEventsByCallId", tenantId, "GetPictureEventsByCallId function has been called successfully. callId = " + callId));
                var pictureEvent = new InquireDAL(tenantId).GetPictureEventsByCallId(callId);
                return new InquireResponse
                {
                    Acknowledge = pictureEvent != null ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    PictureEvent = pictureEvent,
                    Message = string.Format("Event has been fetched against CallId:{0}.", callId)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Inquire, "GetPictureEventsByCallId", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Inquire, "GetPictureEventsByCallId", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

    }
}