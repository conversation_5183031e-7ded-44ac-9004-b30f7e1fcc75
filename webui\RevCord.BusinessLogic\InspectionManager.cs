﻿using RevCord.DataAccess;
using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.IQ3InspectionEntities;
using RevCord.DataContracts.MessageBase;
using RevCord.DataContracts.Messages;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Script.Serialization;

namespace RevCord.BusinessLogic
{
    public class InspectionManager
    {
        public InspectionResponse FetchInspectionTemplates(InspectionRequest inspectionRequest)
        {
            try
            {
                InspectionResponse inspectionResponse = new InspectionResponse { InspectionTemplates = new InspectionDAL(inspectionRequest.TenantId).FetchInspectionTemplates(inspectionRequest.UserId, inspectionRequest.IsCommon, inspectionRequest.OnlyDefaultTemplates) };
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Inspection, "FetchInspectionTemplates", inspectionRequest.TenantId, "FetchInspectionTemplates function has been called successfully. inspectionTemplates = " + new JavaScriptSerializer().Serialize(inspectionResponse.InspectionTemplates)));
                return inspectionResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Inspection, "FetchInspectionTemplates", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Inspection, "FetchInspectionTemplates", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse GetInspectionTemplateById(InspectionRequest inspectionRequest) {
            try
            {
                var inspectionTemplate = new InspectionDAL(inspectionRequest.TenantId).GetInspectionTemplateById(inspectionRequest.InspectionTemplateId, inspectionRequest.ViewOnly);
                InspectionResponse inspectionResponse = new InspectionResponse { InspectionTemplate = inspectionTemplate , FlagStatus = (inspectionTemplate == null) ? false : true };
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Inspection, "GetInspectionTemplateById", inspectionRequest.TenantId, "GetAllInspectionTemplates function has been called successfully. inspectionTemplates = " + new JavaScriptSerializer().Serialize(inspectionResponse.InspectionTemplates)));
                return inspectionResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Inspection, "GetInspectionTemplateById", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Inspection, "GetInspectionTemplateById", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse AddTemplate(InspectionRequest inspectionRequest)
        {
            try
            {
                InspectionResponse inspectionResponse = new InspectionResponse { InspectionTemplate = new InspectionDAL(inspectionRequest.TenantId).addTemplate(inspectionRequest.InspectionTemplate) };
                inspectionResponse.FlagStatus = (inspectionResponse.InspectionTemplate.Id != -1);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InspectionTemplate, "AddTemplate", inspectionRequest.TenantId, " Inspection template has been created successfully.  " + new JavaScriptSerializer().Serialize(inspectionResponse.InspectionTemplate)));
                return inspectionResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InspectionTemplate, "AddTemplate", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InspectionTemplate, "AddTemplate", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse AddPreInspectionData(InspectionRequest inspectionRequest)
        {
            try
            {
                InspectionResponse inspectionResponse = new InspectionResponse { PreInspections = new InspectionDAL(inspectionRequest.TenantId).addPreInspectionData(inspectionRequest.InspectionTemplateId, inspectionRequest.PreInspections, inspectionRequest.CustomFieldIds) };
                if (inspectionRequest.PreInspections.Count == 0)
                {
                    // Updating User Custom Fields only
                    inspectionResponse.FlagStatus = true;
                }
                else
                {
                    inspectionResponse.FlagStatus = inspectionResponse.PreInspections != null && inspectionResponse.PreInspections.Count > 0 ? (inspectionResponse.PreInspections.FirstOrDefault().Id > 0 ? true : false) : false;
                }
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InspectionTemplate, "AddPreInspectionData", inspectionRequest.TenantId, "Pre-Inspection Data has been created successfully.  " + new JavaScriptSerializer().Serialize(inspectionResponse.InspectionTemplate)));
                return inspectionResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InspectionTemplate, "AddPreInspectionData", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InspectionTemplate, "AddPreInspectionData", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse SaveMarker(InspectionRequest inspectionRequest)
        {
            try
            {
                InspectionResponse inspectionResponse = new InspectionResponse { MarkerId = new InspectionDAL(inspectionRequest.TenantId).SaveMarker(inspectionRequest.Marker) };
                inspectionResponse.FlagStatus = inspectionResponse.MarkerId > 0 ? true : false;
                inspectionResponse.Marker = inspectionRequest.Marker;
                inspectionResponse.Marker.Id = inspectionResponse.MarkerId;
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InspectionTemplate, "SaveMarker", inspectionRequest.TenantId, "Marker has been saved successfully.  " + new JavaScriptSerializer().Serialize(inspectionResponse.InspectionTemplate)));
                return inspectionResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InspectionTemplate, "SaveMarker", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InspectionTemplate, "SaveMarker", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #region Section

        public InspectionResponse GetSectionsByTemplateId(InspectionRequest inspectionRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InspectionTemplate, "GetSectionsByTemplateId", inspectionRequest.TenantId, " GetSectionsBySurveyId function has been called successfully.  inspectionTemplateId = " + inspectionRequest.InspectionTemplateId));
                return new InspectionResponse { Sections = new InspectionDAL(inspectionRequest.TenantId).GetSectionsByTemplateId(inspectionRequest.InspectionTemplateId) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InspectionTemplate, "GetSectionsByTemplateId", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InspectionTemplate, "GetSectionsByTemplateId", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse CreateAndGetSections(InspectionRequest inspectionRequest)//Section section, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InspectionTemplate, "CreateAndGetSections", inspectionRequest.TenantId, " CreateAndGetSections function has been called successfully.  section.Title = " + inspectionRequest.Section.Title));
                return new InspectionResponse { Sections = new InspectionDAL(inspectionRequest.TenantId).CreateAndGetSections(inspectionRequest.Section) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InspectionTemplate, "CreateAndGetSections", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InspectionTemplate, "CreateAndGetSections", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public InspectionResponse UpdateAndGetSections(InspectionRequest inspectionRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InspectionTemplate, "UpdateAndGetSections", inspectionRequest.TenantId, " UpdateAndGetSections function has been called successfully.  section.Id = " + inspectionRequest.Section.Id + " - section.Title = " + inspectionRequest.Section.Title));
                return new InspectionResponse { Sections = new InspectionDAL(inspectionRequest.TenantId).UpateAndGetSections(inspectionRequest.Section), FlagStatus = true };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InspectionTemplate, "UpdateAndGetSections", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InspectionTemplate, "UpdateAndGetSections", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse DeleteAndGetSections(InspectionRequest inspectionRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InspectionTemplate, "DeleteAndGetSections", inspectionRequest.TenantId, " DeleteAndGetSections function has been called successfully.  inspectionTemplateId = " + inspectionRequest.InspectionTemplateId));
                return new InspectionResponse { Sections = new InspectionDAL(inspectionRequest.TenantId).DeleteAndGetSections(inspectionRequest.Section.Id, inspectionRequest.Section.InspectionTemplateId) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InspectionTemplate, "DeleteAndGetSections", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InspectionTemplate, "DeleteAndGetSections", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        //List<Marker> GetMarkersByInspectionTemplateId(int inspectionTemplateId)
        public InspectionResponse GetMarkersByInspectionTemplateId(InspectionRequest inspectionRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InspectionTemplate, "GetMarkersByInspectionTemplateId", inspectionRequest.TenantId, " GetMarkersByInspectionTemplateId function has been called successfully.  inspectionTemplateId = " + inspectionRequest.InspectionTemplateId));
                return new InspectionResponse { Markers = new InspectionDAL(inspectionRequest.TenantId).GetMarkersByInspectionTemplateId(inspectionRequest.InspectionTemplateId) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InspectionTemplate, "GetMarkersByInspectionTemplateId", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InspectionTemplate, "GetMarkersByInspectionTemplateId", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        
        public InspectionResponse UpdateMarkerSectionAndGetAllSections(InspectionRequest inspectionRequest)
        {
            try
            {
                var dal = new InspectionDAL(inspectionRequest.TenantId);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InspectionTemplate, "UpdateMarkerSectionAndGetAllSections", inspectionRequest.TenantId, " UpdateMarkerSectionAndGetAllSections function has been called successfully."));
                return new InspectionResponse { Sections = dal.UpdateMarkerSectionAndGetAllSections(inspectionRequest.InspectionTemplateId, inspectionRequest.SectionId, inspectionRequest.AssignedMarkers, inspectionRequest.UnassignedMarkers) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InspectionTemplate, "UpdateMarkerSectionAndGetAllSections", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InspectionTemplate, "UpdateMarkerSectionAndGetAllSections", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        #endregion

        public InspectionResponse GetInspectionByEventId(InspectionRequest inspectionRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Inspection, "GetInspectionByEventId", inspectionRequest.TenantId, " GetInspectionByEventId function has been called successfully."));
                var inspection = new InspectionDAL(inspectionRequest.TenantId).GetInspectionByEventId(inspectionRequest.EventId);
                return new InspectionResponse { Inspection = inspection, FlagStatus = (inspection == null ? false : true) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Inspection, "GetInspectionByEventId", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Inspection, "GetInspectionByEventId", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse DeleteTemplate(InspectionRequest inspectionRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Inspection, "DeleteTemplate", inspectionRequest.TenantId, " DeleteTemplate function has been called successfully."));
                int RowsAffected = new InspectionDAL(inspectionRequest.TenantId).DeleteTemplate(inspectionRequest.InspectionTemplateId, inspectionRequest.TemplateRevSyncServerID);
                return new InspectionResponse { Inspection = null, RowsAffected = RowsAffected, Acknowledge = (RowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Inspection, "DeleteTemplate", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Inspection, "DeleteTemplate", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse ShareTemplate(InspectionRequest inspectionRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Inspection, "ShareTemplate", inspectionRequest.TenantId, " ShareTemplate function has been called successfully."));
                int RowsAffected = new InspectionDAL(inspectionRequest.TenantId).ShareTemplate(inspectionRequest.InspectionTemplateId, inspectionRequest.SelectedUsers, inspectionRequest.UserId);
                return new InspectionResponse { Inspection = null, RowsAffected = RowsAffected, Acknowledge = (RowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Inspection, "ShareTemplate", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Inspection, "ShareTemplate", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse UnshareTemplate(InspectionRequest inspectionRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Inspection, "UnshareTemplate", inspectionRequest.TenantId, " UnshareTemplate function has been called successfully."));
                var RowsAffected = new InspectionDAL(inspectionRequest.TenantId).UnshareTemplate(inspectionRequest.InspectionTemplateId, inspectionRequest.UserId);
                return new InspectionResponse { Inspection = null, RowsAffected = RowsAffected, Acknowledge = (RowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Inspection, "UnshareTemplate", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Inspection, "UnshareTemplate", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse CopyTemplate(InspectionRequest inspectionRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Inspection, "CopyTemplate", inspectionRequest.TenantId, " CopyTemplate function has been called successfully."));
                var inspection = new InspectionDAL(inspectionRequest.TargetTenantId).CopyTemplate(inspectionRequest.UserId, inspectionRequest.InspectionTemplate);
                return new InspectionResponse { InspectionTemplate = inspection, Acknowledge = (inspection == null ? AcknowledgeType.Failure : AcknowledgeType.Success) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Inspection, "CopyTemplate", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Inspection, "CopyTemplate", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse UpdateTemplate(InspectionRequest inspectionRequest)
        {
            try
            {
                InspectionResponse inspectionResponse = new InspectionResponse();
                inspectionResponse.FlagStatus = new InspectionDAL(inspectionRequest.TenantId).updateTemplate(inspectionRequest.InspectionTemplate);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InspectionTemplate, "UpdateTemplate", inspectionRequest.TenantId, " Inspection template has been updated successfully.  " + inspectionResponse.FlagStatus));
                return inspectionResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InspectionTemplate, "UpdateTemplate", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InspectionTemplate, "UpdateTemplate", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse DeletePreInspection(InspectionRequest inspectionRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Inspection, "DeletePreInspection", inspectionRequest.TenantId, " DeletePreInspection function has been called successfully."));
                int RowsAffected = new InspectionDAL(inspectionRequest.TenantId).DeletePreInspection(inspectionRequest.PreInspectionId);
                return new InspectionResponse { Inspection = null, RowsAffected = RowsAffected, Acknowledge = (RowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Inspection, "DeletePreInspection", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Inspection, "DeletePreInspection", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse UpdatePreInspectionData(InspectionRequest inspectionRequest)
        {
            try
            {
                InspectionResponse inspectionResponse = new InspectionResponse { FlagStatus = new InspectionDAL(inspectionRequest.TenantId).updatePreInspectionData(inspectionRequest.InspectionTemplateId, inspectionRequest.PreInspections, inspectionRequest.CustomFieldIds) };
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InspectionTemplate, "AddPreInspectionData", inspectionRequest.TenantId, "Pre-Inspection Data has been updated successfully.  "));
                return inspectionResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InspectionTemplate, "UpdatePreInspectionData", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InspectionTemplate, "UpdatePreInspectionData", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse UpdateMarker(InspectionRequest inspectionRequest)
        {
            try
            {
                InspectionResponse inspectionResponse = new InspectionResponse { Marker = new InspectionDAL(inspectionRequest.TenantId).UpdateMarker(inspectionRequest.Marker) };
                inspectionResponse.FlagStatus = inspectionResponse.Marker.Id > 0 ? true : false;
                //inspectionResponse.Marker = inspectionRequest.Marker;
                //inspectionResponse.Marker.Id = inspectionResponse.MarkerId;
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InspectionTemplate, "UpdateMarker", inspectionRequest.TenantId, "Marker data has been updated successfully.  "));
                return inspectionResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InspectionTemplate, "UpdateMarker", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InspectionTemplate, "UpdateMarker", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse DeleteMarker(InspectionRequest inspectionRequest)
        {
            try
            {
                int RowsAffected = new InspectionDAL(inspectionRequest.TenantId).DeleteMarker(inspectionRequest.MarkerId);
                InspectionResponse inspectionResponse = new InspectionResponse { Inspection = null, RowsAffected = RowsAffected, FlagStatus = (RowsAffected > 0 ? true : false) };
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InspectionTemplate, "DeleteMarker", inspectionRequest.TenantId, "Marker has been deleted successfully.  "));
                return inspectionResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InspectionTemplate, "DeleteMarker", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InspectionTemplate, "DeleteMarker", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public InspectionResponse LoadTitleDetails(InspectionRequest inspectionRequest)
        {
            try
            {
                InspectionTitle _InspectionTitle = new InspectionDAL(inspectionRequest.TenantId).LoadTitleDetails();
                InspectionResponse inspectionResponse = new InspectionResponse { InspectionTitle = _InspectionTitle, FlagStatus = true };
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InspectionTemplate, "LoadTitleDetails", inspectionRequest.TenantId, "LoadTitleDetails has been fetched successfully.  "));
                return inspectionResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InspectionTemplate, "LoadTitleDetails", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InspectionTemplate, "LoadTitleDetails", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse SaveTitleDetails(InspectionRequest inspectionRequest)
        {
            try
            {
                bool FlagStatus = new InspectionDAL(inspectionRequest.TenantId).SaveTitleDetails(inspectionRequest._InspectionTitle);
                InspectionResponse inspectionResponse = new InspectionResponse { Inspection = null, FlagStatus = FlagStatus };
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InspectionTemplate, "LoadTitleDetails", inspectionRequest.TenantId, "LoadTitleDetails has been fetched successfully.  "));
                return inspectionResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InspectionTemplate, "LoadTitleDetails", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InspectionTemplate, "LoadTitleDetails", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse GetMultiSectionMarkers(InspectionRequest inspectionRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InspectionTemplate, "GetMultiSectionMarkers", inspectionRequest.TenantId, " GetMultiSectionMarkers function has been called successfully.  inspectionTemplateId = " + inspectionRequest.InspectionTemplateId));
                return new InspectionResponse { Markers = new InspectionDAL(inspectionRequest.TenantId).GetMultiSectionMarkers(inspectionRequest.InspectionTemplateId) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InspectionTemplate, "GetMultiSectionMarkers", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InspectionTemplate, "GetMultiSectionMarkers", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse SaveMarkerSections(InspectionRequest inspectionRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InspectionTemplate, "SaveMarkerSections", inspectionRequest.TenantId, " SaveMarkerSections function has been called successfully.  inspectionTemplateId = " + inspectionRequest.InspectionTemplateId));
                return new InspectionResponse { MarkerSections = new InspectionDAL(inspectionRequest.TenantId).SaveMarkerSections(inspectionRequest.InspectionTemplateId, inspectionRequest.MarkerSections) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InspectionTemplate, "SaveMarkerSections", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InspectionTemplate, "SaveMarkerSections", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse CreateSectionAndMoveMarkers(int defaultSectionId, string sectionTitle, int inspectionTemplateId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InspectionTemplate, "CreateSectionAndMoveMarkers", tenantId, " CreateSectionAndMoveMarkers function has been called successfully.  inspectionTemplateId = " + inspectionTemplateId));
                return new InspectionResponse { FlagStatus = new InspectionDAL(tenantId).CreateSectionAndMoveMarkers(defaultSectionId, sectionTitle, inspectionTemplateId) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InspectionTemplate, "CreateSectionAndMoveMarkers", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InspectionTemplate, "CreateSectionAndMoveMarkers", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse GetDefaultSectionMarkers(InspectionRequest inspectionRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InspectionTemplate, "GetDefaultSectionMarkers", inspectionRequest.TenantId, " GetDefaultSectionMarkers function has been called successfully.  inspectionTemplateId = " + inspectionRequest.InspectionTemplateId));
                return new InspectionResponse { Markers = new InspectionDAL(inspectionRequest.TenantId).GetDefaultSectionMarkers(inspectionRequest.SectionId) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InspectionTemplate, "GetDefaultSectionMarkers", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InspectionTemplate, "GetDefaultSectionMarkers", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse SaveGraphicMarkerSection(InspectionRequest inspectionRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InspectionTemplate, "SaveGraphicMarkerSection", inspectionRequest.TenantId, " SaveGraphicMarkerSection function has been called successfully.  inspectionTemplateId = " + inspectionRequest.InspectionTemplateId  + " - SectionTitle = " + inspectionRequest.Section.Title + " - GraphicMarkerId = " + inspectionRequest.MarkerId ));
                return new InspectionResponse { SectionId = new InspectionDAL(inspectionRequest.TenantId).SaveGraphicMarkerSection(inspectionRequest.Section, inspectionRequest.MarkerId) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InspectionTemplate, "SaveGraphicMarkerSection", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InspectionTemplate, "SaveGraphicMarkerSection", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse GetGraphicMarkerDetailsById(int tenantId, int graphicMarkerId) {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InspectionTemplate, "GetGraphicMarkerDetailsById", tenantId, " GetGraphicMarkerDetailsById function has been called successfully. tenantId = " + tenantId +" - graphicMarkerId = " + graphicMarkerId));
                return new InspectionResponse { GraphicMarker = new InspectionDAL(tenantId).GetGraphicMarkerDetailsById(graphicMarkerId) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InspectionTemplate, "GetGraphicMarkerDetailsById", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InspectionTemplate, "GetGraphicMarkerDetailsById", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse DeleteGraphicMarkerDetailsSection(InspectionRequest inspectionRequest)
        {
            try
            {
                int RowsAffected = new InspectionDAL(inspectionRequest.TenantId).DeleteGraphicMarkerDetailsSection(inspectionRequest.SectionId);
                InspectionResponse inspectionResponse = new InspectionResponse { Inspection = null, RowsAffected = RowsAffected, FlagStatus = (RowsAffected > 0 ? true : false) };
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InspectionTemplate, "DeleteGraphicMarkerDetailsSection", inspectionRequest.TenantId, "Graphic marker details section has been deleted successfully.  "));
                return inspectionResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InspectionTemplate, "DeleteGraphicMarkerDetailsSection", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InspectionTemplate, "DeleteGraphicMarkerDetailsSection", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool UpdateSectionName(InspectionRequest inspectionRequest)
        {
            try
            {
                bool rowsAffected = new InspectionDAL(inspectionRequest.TenantId).UpdateSectionName(inspectionRequest.Section.Id, inspectionRequest.Section.Title);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InspectionTemplate, "UpdateSectionName", inspectionRequest.TenantId, "Section title has been updated successfully.  "));
                return rowsAffected;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InspectionTemplate, "UpdateSectionName", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InspectionTemplate, "UpdateSectionName", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        //
        public bool UpdatePdfFileNameForGraphicMarker(int tenantId, string fileName, int graphicMarkerId)
        {
            try
            {
                bool rowsAffected = new InspectionDAL(tenantId).UpdatePdfFileNameForGraphicMarker(fileName, graphicMarkerId);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InspectionTemplate, "UpdatePdfFileNameForGraphicMarker", tenantId, "Marker Description / FilaName has been updated successfully.  Marker Id = " + graphicMarkerId.ToString()));
                return rowsAffected;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InspectionTemplate, "UpdatePdfFileNameForGraphicMarker", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InspectionTemplate, "UpdatePdfFileNameForGraphicMarker", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse GetIQ3InspectionParameters(InspectionRequest inspectionRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InspectionTemplate, "GetIQ3InspectionParameters", inspectionRequest.TenantId, " GetIQ3InspectionParameters function has been called successfully."));
                return new InspectionResponse { IQ3InspectionParameters = new InspectionDAL(inspectionRequest.TenantId).GetIQ3InspectionParameters(inspectionRequest.UserId, inspectionRequest.InspectionParameterType, inspectionRequest.StartDateTime, inspectionRequest.EndDateTime) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InspectionTemplate, "GetIQ3InspectionParameters", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InspectionTemplate, "GetIQ3InspectionParameters", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        public InspectionResponse LoadRVIMessage(InspectionRequest inspectionRequest)
        {
            try
            {
                RVIMessage rviMessage = new InspectionDAL(inspectionRequest.TenantId).LoadRVIMessage();
                InspectionResponse inspectionResponse = new InspectionResponse { RVIMessage = rviMessage, FlagStatus = true };
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InspectionTemplate, "LoadRVIMessage", inspectionRequest.TenantId, "LoadRVIMessage has been fetched successfully.  "));
                return inspectionResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InspectionTemplate, "LoadRVIMessage", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InspectionTemplate, "LoadRVIMessage", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse SaveRVIMessage(InspectionRequest inspectionRequest)
        {
            try
            {
                bool FlagStatus = new InspectionDAL(inspectionRequest.TenantId).SaveRVIMessage(inspectionRequest.RVIMessage);
                InspectionResponse inspectionResponse = new InspectionResponse { Inspection = null, FlagStatus = FlagStatus };
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InspectionTemplate, "SaveRVIMessage", inspectionRequest.TenantId, "RVI Message has been updated successfully.  "));
                return inspectionResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InspectionTemplate, "SaveRVIMessage", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InspectionTemplate, "SaveRVIMessage", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #region - User Custom Fields

        public InspectionResponse SearchAllPreInspectionGroup(InspectionRequest inspectionRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.UserCustomField, "SearchAllPreInspectionGroup", inspectionRequest.TenantId, "SearchAllPreInspectionGroup function has been called. inspectionRequest.TenantId = " + inspectionRequest.TenantId));
                return new InspectionResponse
                {
                    PreInspectionGroups = new InspectionDAL(inspectionRequest.TenantId).SearchAllPreInspectionGroup(inspectionRequest.SearchText, inspectionRequest.UserId, inspectionRequest.IncludePreInspections)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserCustomField, "SearchAllPreInspectionGroup", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserCustomField, "SearchAllPreInspectionGroup", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse GetAllPreInspectionGroup(InspectionRequest inspectionRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.UserCustomField, "GetAllPreInspectionGroup", inspectionRequest.TenantId, "GetAllPreInspectionGroup function has been called. inspectionRequest.TenantId = " + inspectionRequest.TenantId));
                return new InspectionResponse
                {
                    PreInspectionGroups = new InspectionDAL(inspectionRequest.TenantId).GetAllPreInspectionGroup(inspectionRequest.UserId, inspectionRequest.IncludePreInspections)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserCustomField, "GetAllPreInspectionGroup", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserCustomField, "GetAllPreInspectionGroup", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse GetAllPreInspectionGroupFields(InspectionRequest inspectionRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.UserCustomField, "GetAllPreInspectionGroupFields", inspectionRequest.TenantId, "GetAllPreInspectionGroupFields function has been called. inspectionRequest.TenantId = " + inspectionRequest.TenantId));
                return new InspectionResponse
                {
                    CustomFields = new InspectionDAL(inspectionRequest.TenantId).GetAllUserPreInspectionsByGroupId(inspectionRequest.GroupId)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserCustomField, "GetAllPreInspectionGroupFields", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserCustomField, "GetAllPreInspectionGroupFields", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse AddPreInspectionGroup(InspectionRequest inspectionRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.UserCustomField, "AddPreInspectionGroup", inspectionRequest.TenantId, "AddPreInspectionGroup function has been called. inspectionRequest.TenantId = " + inspectionRequest.TenantId));
                return new InspectionResponse
                {
                    FlagStatus = new InspectionDAL(inspectionRequest.TenantId).AddPreInspectionGroup(inspectionRequest.PreInspectionGroup)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserCustomField, "AddPreInspectionGroup", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserCustomField, "AddPreInspectionGroup", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse UpdatePreInspectionGroup(InspectionRequest inspectionRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.UserCustomField, "UpdatePreInspectionGroup", inspectionRequest.TenantId, "UpdatePreInspectionGroup function has been called. inspectionRequest.TenantId = " + inspectionRequest.TenantId));
                return new InspectionResponse
                {
                    FlagStatus = new InspectionDAL(inspectionRequest.TenantId).UpdatePreInspectionGroup(inspectionRequest.PreInspectionGroup)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserCustomField, "UpdatePreInspectionGroup", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserCustomField, "UpdatePreInspectionGroup", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse DeletePreInspectionGroup(InspectionRequest inspectionRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.UserCustomField, "DeletePreInspectionGroup", inspectionRequest.TenantId, "DeletePreInspectionGroup function has been called. inspectionRequest.TenantId = " + inspectionRequest.TenantId));
                return new InspectionResponse
                {
                    FlagStatus = new InspectionDAL(inspectionRequest.TenantId).DeletePreInspectionGroup(inspectionRequest.GroupId)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserCustomField, "DeletePreInspectionGroup", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserCustomField, "DeletePreInspectionGroup", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse SearchAllCustomFields(InspectionRequest inspectionRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.UserCustomField, "SearchAllCustomFields", inspectionRequest.TenantId, "SearchAllCustomFields function has been called. inspectionRequest.TenantId = " + inspectionRequest.TenantId));
                return new InspectionResponse
                {
                    CustomFields = new InspectionDAL(inspectionRequest.TenantId).SearchAllCustomFields(inspectionRequest.SearchText, inspectionRequest.UserId)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserCustomField, "SearchAllCustomFields", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserCustomField, "SearchAllCustomFields", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse GetAllCustomFields(InspectionRequest inspectionRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.UserCustomField, "GetAllCustomFields", inspectionRequest.TenantId, "GetAllCustomFields function has been called. inspectionRequest.TenantId = " + inspectionRequest.TenantId));
                return new InspectionResponse
                {
                    CustomFields = new InspectionDAL(inspectionRequest.TenantId).GetAllCustomFields(inspectionRequest.UserId)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserCustomField, "GetAllCustomFields", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserCustomField, "GetAllCustomFields", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse AddCustomField(InspectionRequest inspectionRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.UserCustomField, "AddCustomField", inspectionRequest.TenantId, "AddCustomField function has been called. inspectionRequest.TenantId = " + inspectionRequest.TenantId));
                return new InspectionResponse
                {
                    FlagStatus = new InspectionDAL(inspectionRequest.TenantId).AddCustomField(inspectionRequest.CustomField, inspectionRequest.UserId)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserCustomField, "AddCustomField", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserCustomField, "AddCustomField", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse UpdateCustomField(InspectionRequest inspectionRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.UserCustomField, "UpdateCustomField", inspectionRequest.TenantId, "UpdateCustomField function has been called. inspectionRequest.TenantId = " + inspectionRequest.TenantId));
                return new InspectionResponse
                {
                    FlagStatus = new InspectionDAL(inspectionRequest.TenantId).UpdateCustomField(inspectionRequest.CustomField)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserCustomField, "UpdateCustomField", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserCustomField, "UpdateCustomField", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse DeleteCustomField(InspectionRequest inspectionRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.UserCustomField, "DeleteCustomField", inspectionRequest.TenantId, "DeleteCustomField function has been called. inspectionRequest.TenantId = " + inspectionRequest.TenantId));
                return new InspectionResponse
                {
                    FlagStatus = new InspectionDAL(inspectionRequest.TenantId).DeleteCustomField(inspectionRequest.CustomFieldId)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserCustomField, "DeleteCustomField", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserCustomField, "DeleteCustomField", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse UnDeleteCustomField(InspectionRequest inspectionRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.UserCustomField, "UnDeleteCustomField", inspectionRequest.TenantId, "UnDeleteCustomField function has been called. inspectionRequest.TenantId = " + inspectionRequest.TenantId));
                return new InspectionResponse
                {
                    FlagStatus = new InspectionDAL(inspectionRequest.TenantId).UnDeleteCustomField(inspectionRequest.CustomFieldIds)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserCustomField, "UnDeleteCustomField", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserCustomField, "UnDeleteCustomField", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #endregion - User Custom Fields


        public InspectionResponse SaveAutoReportRecipients(InspectionRequest inspectionRequest)
        {
            try
            {
                List<AutoReportRecipient> autoReportRecipients = inspectionRequest.AutoReportRecipients;

                foreach (var autoReportRecipient in autoReportRecipients)
                {
                    var rowsAffected = new InspectionDAL(inspectionRequest.TenantId).SaveAutoReportRecipient(autoReportRecipient);
                    if (rowsAffected > 0)
                        Task.Run(() => RevAuditLogger.WriteSuccess(Originator.AutoReportRecipient, "SaveAutoReportRecipients", inspectionRequest.TenantId, "Auto report recipient has been saved successfully. Id = " + autoReportRecipient.Id + " Name = " + autoReportRecipient.Name));
                }
                return new InspectionResponse { AutoReportRecipients = autoReportRecipients };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.AutoReportRecipient, "SaveAutoReportRecipients", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.AutoReportRecipient, "SaveAutoReportRecipients", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));

                throw ex;
            }
        }

        public InspectionResponse FetchAutoReportRecipients(InspectionRequest inspectionRequest)
        {
            try
            {
                var autoReportRecipients = new InspectionDAL(inspectionRequest.TenantId).FetchAutoReportRecipients(inspectionRequest.UserId);
                InspectionResponse inspectionResponse = new InspectionResponse { AutoReportRecipients = autoReportRecipients };
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InspectionTemplate, "FetchAutoReportRecipients", inspectionRequest.TenantId, "Auto report recipients list has been fetched successfully.  "));
                return inspectionResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InspectionTemplate, "FetchAutoReportRecipients", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));

                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InspectionTemplate, "FetchAutoReportRecipients", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse UpdateAutoReportRecipientActiveStatus(InspectionRequest inspectionRequest)
        {
            try
            {
                AutoReportRecipient autoReportRecipient = inspectionRequest.AutoReportRecipient;
                var rowsAffected = new InspectionDAL(inspectionRequest.TenantId).UpdateAutoReportRecipientActiveStatus(autoReportRecipient.Id, autoReportRecipient.IsActive);
                if (rowsAffected > 0)
                    Task.Run(() => RevAuditLogger.WriteSuccess(Originator.AutoReportRecipient, "UpdateAutoReportRecipientActiveStatus", inspectionRequest.TenantId, "Auto report recipient active status has been updated successfully. Id = " + autoReportRecipient.Id + " status = " + autoReportRecipient.IsActive));
                return new InspectionResponse { FlagStatus = rowsAffected  > 0};
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.AutoReportRecipient, "UpdateAutoReportRecipientActiveStatus", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));

                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.AutoReportRecipient, "UpdateAutoReportRecipientActiveStatus", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));

                throw ex;
            }
        }

        public InspectionResponse DeleteAutoReportRecipient(InspectionRequest inspectionRequest)
        {
            try
            {
                AutoReportRecipient autoReportRecipient = inspectionRequest.AutoReportRecipient;
                var result = new InspectionDAL(inspectionRequest.TenantId).DeleteAutoReportRecipient(autoReportRecipient.Id);
                if (result)
                    Task.Run(() => RevAuditLogger.WriteSuccess(Originator.AutoReportRecipient, "DeleteAutoReportRecipient", inspectionRequest.TenantId, "Auto report recipient active status has been deleted successfully. Id = " + autoReportRecipient.Id));
                return new InspectionResponse { FlagStatus = result };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.AutoReportRecipient, "DeleteAutoReportRecipient", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.AutoReportRecipient, "DeleteAutoReportRecipient", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InspectionResponse GetMarkersByInspectionTemplate(InspectionRequest inspectionRequest)
        {
            try
            {
                var markers = new InspectionDAL(inspectionRequest.TenantId).GetMarkersByInspectionTemplate(inspectionRequest.InspectionTemplateId);
                InspectionResponse inspectionResponse = new InspectionResponse { Markers = markers , FlagStatus = (markers == null) ? false : true };
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Inspection, "GetMarkersByInspectionTemplate", inspectionRequest.TenantId, "GetMarkersByInspectionTemplate function has been called successfully. inspectionTemplates Id = " + inspectionRequest.InspectionTemplateId));
                return inspectionResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Inspection, "GetMarkersByInspectionTemplate", inspectionRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Inspection, "GetMarkersByInspectionTemplate", inspectionRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
    }
}
