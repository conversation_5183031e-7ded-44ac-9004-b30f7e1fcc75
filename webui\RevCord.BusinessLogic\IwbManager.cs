﻿using RevCord.DataAccess;
using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.IWBEntities;
using RevCord.DataContracts.MessageBase;
using RevCord.DataContracts.Messages;
using RevCord.Util;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Text;
using System.Threading.Tasks;
using System.Linq;
using System.Configuration;
using Newtonsoft.Json;

namespace RevCord.BusinessLogic
{
    public class IwbManager
    {

        #region ------- WPS -------


        public IwbResponse SaveWps(IwbRequest request)
        {
            try
            {
                int lastId = new IwbDAL(request.TenantId).InsertWPS(request.Wps);
                return new IwbResponse
                {
                    Acknowledge = lastId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    IwbId = lastId,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "SaveWps", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "SaveWps", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public IwbResponse GetWpsList(IwbRequest request)
        {
            var response = new IwbResponse();
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.IWB, "GetWpsList", request.TenantId, "GetWpsList function has been called successfully."));
                response.WPSs = new IwbDAL(request.TenantId).GetWpsList();
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "GetWpsList", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "GetWpsList", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return response;
        }


        public Wps GetWpsById(int wpsId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.IWB, "GetWpsById", tenantId, "GetWpsById has been exeuted successfully. "));
                return new IwbDAL(tenantId).GetWpsById(wpsId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "GetWpsById", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "GetWpsById", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }





        #endregion


        #region ------- Job -------

        public IwbResponse SaveJob(IwbRequest request)
        {
            try
            {
                int lastId = new IwbDAL(request.TenantId).InsertJob(request.Job);
                return new IwbResponse
                {
                    Acknowledge = lastId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    LastSavedId = lastId,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "SaveJob", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "SaveJob", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public IwbResponse AssignJob(IwbRequest request)
        {
            try
            {
                int lastId = new IwbDAL(request.TenantId).AssignJob(request.JobId, request.WelderId, DateTime.Now, request.UserId, request.JobApplicationStatusId);
                return new IwbResponse
                {
                    Acknowledge = lastId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    LastSavedId = lastId,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "AssignJob", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "AssignJob", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public IwbResponse GetJobsByWhereClause(IwbRequest request)
        {
            try
            {
                StringBuilder sbWhereClause = new StringBuilder();
                sbWhereClause.Append(" 1 = 1 "); 

                var criteria = request.Criteria;

                #region ------- Search Criteria -------
                if (criteria != null)
                {
                    if (criteria.OrganizationId > 0)
                    {
                        sbWhereClause.Append(" AND ");
                        sbWhereClause.Append(" (OrganizationId = " + criteria.OrganizationId + ")");
                        sbWhereClause.AppendLine();
                    }
                    if (criteria.CreatedBy > 0)
                    {
                        sbWhereClause.Append(" AND ");
                        sbWhereClause.Append(" (CreatedBy = " + criteria.CreatedBy + ")");
                        sbWhereClause.AppendLine();
                    }

                    if (criteria.WelderId > 0)
                    {
                        sbWhereClause.Append(" AND ");
                        sbWhereClause.Append(" (PerformerId = " + criteria.WelderId + ")");
                        sbWhereClause.AppendLine();
                    }

                    if (!string.IsNullOrEmpty(criteria.JobName))
                    {
                        var jobName = criteria.JobName.Trim();
                        if (!string.IsNullOrEmpty(jobName))
                        {
                            sbWhereClause.Append(" AND (Title LIKE N'%" + jobName + "%')");
                        }
                    }

                    if (criteria.JobStatuses != null && criteria.JobStatuses.Count > 0)
                    {
                        sbWhereClause.Append(" AND (CurrentStatus IN (");
                        foreach (var status in criteria.JobStatuses)
                        {
                            sbWhereClause.AppendFormat("{0},", (int)status);
                        }
                        sbWhereClause.Remove(sbWhereClause.Length - 1, 1); 
                        sbWhereClause.Append("))");
                        sbWhereClause.AppendLine();
                    }

                    if (!string.IsNullOrEmpty(criteria.JobWps))
                    {
                        var jobWps = criteria.JobWps.Trim();
                        if (!string.IsNullOrEmpty(jobWps))
                        {
                            // We intentionally do not include a WpsData condition in the WHERE clause
                            // because jobWps is passed as a separate SQL parameter to the stored procedure.
                        }
                    }

                    sbWhereClause.AppendLine();
                }
                #endregion

                System.Diagnostics.Debug.WriteLine(sbWhereClause.ToString());
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.IWB, "GetJobsByWhereClause", request.TenantId, "GetJobsByWhereClause function has been called successfully. sbWhereClause = " + sbWhereClause.ToString()));

                int totalPages = 0;
                long totalRecords = 0;

                var jobs = new IwbDAL(request.TenantId).GetJobsByWhereClause(sbWhereClause.ToString(), out totalPages, out totalRecords, criteria.PageIndex, criteria.PageSize, criteria.JobWps);

                return new IwbResponse
                {
                    Jobs = jobs,
                    TotalPages = totalPages,
                    TotalRecords = Convert.ToInt32(totalRecords)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "GetJobsByWhereClause", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "GetJobsByWhereClause", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public IwbResponse UpdateJobStatus(IwbRequest request)
        {
            try
            {
                int noOfRows = new IwbDAL(request.TenantId).UpdateJobStatus(request.JobId, request.JobStatusId);
                return new IwbResponse
                {
                    Acknowledge = noOfRows > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    LastSavedId = noOfRows,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "UpdateJobStatus", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "UpdateJobStatus", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public IwbResponse UpdateJobApplicantStatus(IwbRequest request)
        {
            try
            {
                int noOfRows = new IwbDAL(request.TenantId).UpdateJobApplicantStatus(request.JobApplicant);
                return new IwbResponse
                {
                    Acknowledge = noOfRows > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    LastSavedId = noOfRows,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "UpdateJobApplicantStatus", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "UpdateJobApplicantStatus", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public IwbResponse ApplyForJob(IwbRequest request)
        {
            try
            {
                int lastId = new IwbDAL(request.TenantId).ApplyForJob(request.JobApplicant);
                return new IwbResponse
                {
                    Acknowledge = lastId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    LastSavedId = lastId,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "ApplyForJob", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "ApplyForJob", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public IwbResponse GetJobApplicants(IwbRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.IWB, "GetJobApplicants", request.TenantId, "GetJobApplicants has been exeuted successfully. "));
                return new IwbResponse
                {
                    JobApplicants = new IwbDAL(request.TenantId).GetJobApplicants(request.JobId),
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "GetJobApplicants", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "GetJobApplicants", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #endregion

        #region Welders

        public IwbResponse GetWelders(IwbRequest request)
        {
            try
            {
                StringBuilder sbWhereClause = new StringBuilder();
                sbWhereClause.Append(" 1 = 1 ");
                var criteria = request.Criteria;
                #region ------- Search Criteria -------

                if (criteria != null)
                {
                    if (criteria.OrganizationId > 0)
                    {
                        sbWhereClause.Append(" AND ");
                        sbWhereClause.Append(" (OrganizationId = " + criteria.OrganizationId + ")");
                        sbWhereClause.AppendLine();
                    }
                    if (criteria.WelderId > 0)
                    {
                        sbWhereClause.Append(" AND ");
                        sbWhereClause.Append(" (UserNum = " + criteria.WelderId + ")");
                        sbWhereClause.AppendLine();
                    }
                    if (!string.IsNullOrWhiteSpace(criteria.Name))
                    {
                        string trimmedName = criteria.Name.Trim();
                        sbWhereClause.Append(" AND (UserName LIKE N'%" + trimmedName + "%')");
                    }
                    if (!String.IsNullOrEmpty(criteria.StencilNumber))
                    {
                        var oper = String.IsNullOrEmpty(criteria.Name) ? " AND " : " OR ";
                        sbWhereClause.Append($" {oper} (StencilNumber LIKE N'%" + criteria.StencilNumber + "%')");
                    }

                    sbWhereClause.AppendLine();
                }
                #endregion

                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.IWB, "GetWelders", request.TenantId, "GetWelders function has been called successfully."));
                return new IwbResponse
                {
                    Users = new IwbDAL(request.TenantId).GetWelders(sbWhereClause.ToString()),
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "GetWelders", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "GetWelders", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        #endregion

        #region ------- Work History -------


        public IwbResponse SaveUserWorkHistory(IwbRequest request)
        {
            try
            {
                int lastId = new IwbDAL(request.TenantId).InsertWorkHistory(request.WorkHistory);
                return new IwbResponse
                {
                    Acknowledge = lastId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    LastSavedId = lastId,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "SaveUserWorkHistory", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "SaveUserWorkHistory", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public IwbResponse GetWorkHistory(IwbRequest request)
        {
            //var response = new IwbResponse();
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.IWB, "GetWorkHistories", request.TenantId, "GetWorkHistories function has been called successfully."));
                //response.UserWorkHistories = new IwbDAL(request.TenantId).GetWorkHistories(request.UserId);
                return new IwbDAL(request.TenantId).GetWorkHistory(request.UserId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "GetWorkHistories", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "GetWorkHistories", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            //return response;
        }

        #endregion


        #region ------- Organization -------

        public IwbResponse SaveOrganization(IwbRequest request)
        {
            try
            {
                int lastId = new IwbDAL(request.TenantId).InsertOrganization(request.Organization);
                return new IwbResponse
                {
                    Acknowledge = lastId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    LastSavedId = lastId,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "SaveOrganization", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "SaveOrganization", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public IwbResponse GetOrganizations(IwbRequest request)
        {
            try
            {
                StringBuilder sbWhereClause = new StringBuilder();
                sbWhereClause.Append(" 1 = 1 ");
                var criteria = request.Criteria;
                #region ------- Search Criteria -------

                if (criteria != null)
                {
                    if (criteria.OrganizationId > 0)
                    {
                        sbWhereClause.Append(" AND ");
                        sbWhereClause.Append(" (Id = " + criteria.OrganizationId + ")");
                        sbWhereClause.AppendLine();
                    }
                    if (criteria.OrganizationTypeId > 0)
                    {
                        sbWhereClause.Append(" AND ");
                        sbWhereClause.Append(" (Type = " + criteria.OrganizationTypeId + ")");
                        sbWhereClause.AppendLine();
                    }
                    if (!string.IsNullOrWhiteSpace(criteria.Name))
                    {
                        string trimmedName = criteria.Name.Trim();
                        sbWhereClause.Append(" AND (Name LIKE N'%" + trimmedName + "%' OR Name IS NULL)");
                    }

                    sbWhereClause.AppendLine();
                }
                #endregion

                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.IWB, "GetOrganizations", request.TenantId, "GetOrganizations function has been called successfully."));
                return new IwbResponse
                {
                    Organizations = new IwbDAL(request.TenantId).GetOrganizations(sbWhereClause.ToString()),
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "GetOrganizations", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "GetOrganizations", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        public IwbResponse SaveOrganizationLocation(IwbRequest request)
        {
            try
            {
                int lastId = new IwbDAL(request.TenantId).InsertOrganizationLocation(request.Location);
                return new IwbResponse
                {
                    Acknowledge = lastId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    LastSavedId = lastId,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "SaveOrganizationLocation", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "SaveOrganizationLocation", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public IwbResponse GetLocationsByOrganizationId(int organizationId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.IWB, "GetLocationsByOrganizationId", tenantId, "GetLocationsByOrganizationId has been executed successfully. "));

                return new IwbResponse
                {
                    Locations = new IwbDAL(tenantId).GetLocations(organizationId),
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "GetLocationsByOrganizationId", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "GetLocationsByOrganizationId", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public IwbResponse DeleteLocation(IwbRequest request)
        {
            return new IwbResponse { RowsAffected = new IwbDAL(request.TenantId).DeleteLocation(request.LocationId, request.ModifiedDate, request.UserId) };
        }


        #endregion
        public IwbResponse GetJobsByWelder(IwbRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.IWB,"GetJobsByWelder", request.TenantId,"GetJobsByWelder has been executed successfully."));

                return new IwbResponse
                {
                    JobWelders = new IwbDAL(request.TenantId).GetJobsByWelder(request.UserId),
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(
                    Originator.IWB,"GetJobsByWelder", request.TenantId, sqle.Message,sqle.StackTrace,sqle.ErrorCode,string.Empty,0
                ));
                throw;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "GetLocationsByOrganizationId", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public IwbResponse GetUserDataByUser(IwbRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(
                    Originator.IWB, "GetUserDataByUser", request.TenantId, "GetUserDataByUser has been executed successfully."
                ));

                return new IwbResponse
                {
                    UserInfoData = new IwbDAL(request.TenantId).GetUserDataByUser(request.UserId),
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(
                    Originator.IWB, "GetUserDataByUser", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0
                ));
                throw;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(
                    Originator.IWB, "GetUserDataByUser", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0
                ));
                throw;
            }
        }


        public IwbResponse UploadWPQ(IwbRequest request)
        {
            try
            {
                int lastId = new IwbDAL(request.TenantId).InsertWPQ(request.Wpq);
                return new IwbResponse
                {
                    Acknowledge = lastId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    IwbId = lastId,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "UploadWPQ", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "UploadWPQ", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        #region ------- WPS -------


        public IwbResponse SaveWPQ(IwbRequest request)
        {
            try
            {
                int lastId = new IwbDAL(request.TenantId).InsertWPQ(request.Wpq);
                return new IwbResponse
                {
                    Acknowledge = lastId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    IwbId = lastId,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "SaveWPQ", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "SaveWPQ", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        public IwbResponse GetWPQsByUserId(IwbRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.IWB, "GetWPQsByUserId", request.TenantId, "GetWPQsByUserId has been exeuted successfully. "));
                return new IwbResponse
                {
                    WPQs = new IwbDAL(request.TenantId).GetWPQsByUserId(request.UserId),
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "GetWPQsByUserId", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "GetWPQsByUserId", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }




        #endregion

        #region ------- Document -------

        public IwbResponse SaveScannedDocument(IwbRequest request)
        {
            try
            {
                int lastId = 0;
                switch (request.Document.Type)
                {
                    case IwbDocumentType.MTR:
                        lastId = new IwbDAL(request.TenantId).InsertScannedDocumentMTR(request.Document);
                        break;
                    case IwbDocumentType.WPQ:
                        lastId = new IwbDAL(request.TenantId).InsertScannedDocumentWPQ(request.Document);
                        break;
                    case IwbDocumentType.WPS:
                        lastId = new IwbDAL(request.TenantId).InsertScannedDocumentWPS(request.Document);
                        break;
                    case IwbDocumentType.PQR:
                    default:
                        lastId = new IwbDAL(request.TenantId).InsertScannedDocument(request.Document);
                        break;
                }

                return new IwbResponse
                {
                    Acknowledge = lastId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    IwbId = lastId,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "SaveScannedDocument", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "SaveScannedDocument", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public IwbResponse SaveDocument(IwbRequest request)
        {
            try
            {
                int lastId = new IwbDAL(request.TenantId).InsertDocument(request.Document);
                return new IwbResponse
                {
                    Acknowledge = lastId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    IwbId = lastId,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "SaveDocument", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "SaveDocument", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public IwbResponse GetDocumentById(IwbRequest request)
        {
            try
            {
                IwbDocument document = new IwbDAL(request.TenantId).GetDocumentById(request.DocumentId);
                return new IwbResponse
                {
                    Acknowledge = document != null ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    Document = document
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "GetDocumentById", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "GetDocumentById", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw;
            }
        }



        public IwbResponse VerifyDocument(IwbRequest request)
        {
            try
            {
                new IwbDAL(request.TenantId).VerifyDocument(request.Document);
                return new IwbResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    IwbId = request.Document.Id,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "VerifyDocument", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "VerifyDocument", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public IwbResponse GetDocumentsByWhereClause(IwbRequest request)
        {
            try
            {
                StringBuilder sbWhereClause = new StringBuilder();
                sbWhereClause.Append(" 1 = 1 ");
                var criteria = request.Criteria;
                #region ------- Search Criteria -------

                if (criteria != null)
                {
                    if (criteria.OrganizationId > 0)
                    {
                        sbWhereClause.Append(" AND ");
                        sbWhereClause.Append(" (iwb.OrganizationId = " + criteria.OrganizationId + ")");
                        sbWhereClause.AppendLine();
                    }
                    if (criteria.CreatedBy > 0)
                    {
                        sbWhereClause.Append(" AND ");
                        sbWhereClause.Append(" (iwb.CreatedBy = " + criteria.CreatedBy + ")");
                        sbWhereClause.AppendLine();
                    }
                    if (criteria.DocumentTypeId > 0)
                    {
                        sbWhereClause.Append(" AND ");
                        sbWhereClause.Append(" (DocumentTypeId = " + criteria.DocumentTypeId + ")");
                        sbWhereClause.AppendLine();
                    }

                    if (criteria.WelderId > 0)
                    {
                        sbWhereClause.Append(" AND ");
                        sbWhereClause.Append(" (iwb.UserId = " + criteria.WelderId + ")");
                        sbWhereClause.AppendLine();
                    }
                    if (!string.IsNullOrWhiteSpace(criteria.Name))
                    {
                        string trimmedName = criteria.Name.Trim();
                        sbWhereClause.Append(" AND (iwb.Name LIKE N'%" + trimmedName + "%')");
                    }

                    if (!string.IsNullOrWhiteSpace(criteria.Alias))
                    {
                        string trimmedAlias = criteria.Alias.Trim();
                        sbWhereClause.Append(" AND (Alias LIKE N'%" + trimmedAlias + "%')");
                    }

                    if (criteria.DocumentTypes != null)
                    {
                        sbWhereClause.Append(" AND (DocumentTypeId IN ( ");
                        foreach (var type in criteria.DocumentTypes)
                        {
                            sbWhereClause.AppendFormat("{0},", (int)type);
                        }
                        sbWhereClause.RemoveLast(",");
                        sbWhereClause.Append(" ) ");
                        sbWhereClause.Append(" ) ");
                        sbWhereClause.AppendLine();
                    }

                    sbWhereClause.AppendLine();
                }
                #endregion

                //System.Diagnostics.Debug.WriteLine(sbWhereClause.ToString());
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.IWB, "GetDocumentsByWhereClause", request.TenantId, "GetDocumentsByWhereClause function has been called successfully. sbWhereClause = " + sbWhereClause.ToString()));

                int totalPages = 0;
                long totalRecords = 0;
                return new IwbResponse
                {
                    Documents = new IwbDAL(request.TenantId).GetDocumentsByWhereClause(sbWhereClause.ToString(), out totalPages, out totalRecords, criteria.PageIndex, criteria.PageSize),
                    TotalPages = totalPages,
                    TotalRecords = Convert.ToInt32(totalRecords),
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "GetDocumentsByWhereClause", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "GetDocumentsByWhereClause", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }

        }


        #endregion

        #region Testing

        public IwbResponse GetTestTypes(IwbRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.IWB, "GetTestTypes", request.TenantId, "GetTestTypes has been exeuted successfully. "));
                return new IwbResponse
                {
                    TestTypes = new IwbDAL(request.TenantId).GetTestTypeList(),
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "GetTestTypes", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "GetTestTypes", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public IwbResponse SaveTest(IwbRequest request)
        {
            try
            {
                int lastId = new IwbDAL(request.TenantId).InsertTest(request.Test);
                return new IwbResponse
                {
                    Acknowledge = lastId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    IwbId = lastId,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "SaveTest", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "SaveTest", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        public IwbResponse GetTestsByWhereClause(IwbRequest request)
        {
            try
            {
                StringBuilder sbWhereClause = new StringBuilder();
                sbWhereClause.Append(" 1 = 1 ");
                var criteria = request.Criteria;
                #region ------- Search Criteria -------

                if (criteria != null)
                {
                    if (criteria.OrganizationId > 0)
                    {
                        sbWhereClause.Append(" AND ");
                        sbWhereClause.Append(" (OrganizationId = " + criteria.OrganizationId + ")");
                        sbWhereClause.AppendLine();
                    }
                    if (criteria.CreatedBy > 0)
                    {
                        sbWhereClause.Append(" AND ");
                        sbWhereClause.Append(" (CreatedBy = " + criteria.CreatedBy + ")");
                        sbWhereClause.AppendLine();
                    }
                    if (!string.IsNullOrWhiteSpace(criteria.Name))
                    {
                        string trimmedName = criteria.Name.Trim();
                        sbWhereClause.Append(" AND (Name LIKE N'%" + trimmedName + "%')");
                    }

                    if (!string.IsNullOrWhiteSpace(criteria.TypeCode))
                    {
                        string trimmedTypeCode = criteria.TypeCode.Trim();
                        sbWhereClause.Append(" AND (tt.CostCode LIKE N'%" + trimmedTypeCode + "%')");
                        sbWhereClause.AppendLine();
                    }

                    if (criteria.TestStatuses != null && criteria.TestStatuses.Any())
                    {
                        var statusIds = criteria.TestStatuses.Select(status => ((int)status).ToString()).ToList();
                        string inClause = string.Join(",", statusIds);
                        sbWhereClause.Append($" AND (CurrentStatusId IN ({inClause}))");
                    }
                }
                #endregion

                //System.Diagnostics.Debug.WriteLine(sbWhereClause.ToString());
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.IWB, "GetTestsByWhereClause", request.TenantId, "GetTestsByWhereClause function has been called successfully. sbWhereClause = " + sbWhereClause.ToString()));

                int totalPages = 0;
                long totalRecords = 0;
                return new IwbResponse
                {
                    Tests = new IwbDAL(request.TenantId).GetTestsByWhereClause(sbWhereClause.ToString(), out totalPages, out totalRecords, criteria.PageIndex, criteria.PageSize),
                    TotalPages = totalPages,
                    TotalRecords = Convert.ToInt32(totalRecords),
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "GetTestsByWhereClause", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "GetTestsByWhereClause", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }

        }

        public IwbResponse RegisterWelderForTest(IwbRequest request)
        {
            try
            {
                int lastId = new IwbDAL(request.TenantId).RegisterWelderForTest(request.UserId, request.TestId, request.UserId, DateTime.Now);
                return new IwbResponse
                {
                    Acknowledge = lastId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    IwbId = lastId,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "RegisterWelderForTest", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "RegisterWelderForTest", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public IwbResponse GetTestAttendees(IwbRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.IWB, "GetTestAttendees", request.TenantId, "GetTestAttendees has been exeuted successfully. "));
                return new IwbResponse
                {
                    TestAttendees = new IwbDAL(request.TenantId).GetTestAttendees(request.TestId),
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "GetTestAttendees", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "GetTestAttendees", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public IwbResponse GetSignOffRequestUsers(IwbRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.IWB, "GetSignOffRequestUsers", request.TenantId, "GetSignOffRequestUsers has been exeuted successfully. "));

                Dictionary<string, object> resultSet = new IwbDAL(request.TenantId).GetSignOffRequestUsers(request.TestId);

                IwbResponse result = new IwbResponse();
                result.ResultData = resultSet;

                return result;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "GetSignOffRequestUsers", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "GetSignOffRequestUsers", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public IwbResponse UpdateTestStatus(IwbRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.IWB, "UpdateTestStatus", request.TenantId, "UpdateTestStatus has been exeuted successfully. "));
                return new IwbResponse
                {
                    ResultData = new IwbDAL(request.TenantId).UpdateTestStatus(request.Test),
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "UpdateTestStatus", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "UpdateTestStatus", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public IwbResponse UpdateWelderTestStatus(IwbRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.IWB, "UpdateWelderTestStatus", request.TenantId, "UpdateWelderTestStatus has been exeuted successfully. "));
                return new IwbResponse
                {
                    ResultData = new IwbDAL(request.TenantId).UpdateWelderTestStatus((IwbTestAttendee)request.Data, request.UserId),
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "UpdateWelderTestStatus", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "UpdateWelderTestStatus", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public IwbResponse SaveTestInvitation(IwbRequest request)
        {
            try
            {
                //return new IwbResponse { ResultData = new IwbDAL(request.TenantId).InsertTestInvitation(request.TestInvitation) };
                int rowsAffected = 0;
                foreach (var item in request.Ids)
                {
                    Task.Run(() => RevAuditLogger.WriteSuccess(Originator.IWB, "SaveTestInvitation", request.TenantId, "SaveTestInvitation has been executed successfully. "));
                    var invitation = new IwbTestInvitation
                    {
                        OrganizationId = request.OrganizationId,
                        TestId = request.TestId,
                        WelderId = item,
                        CreatedDate = request.ModifiedDate,
                        CreatedBy = request.UserId,
                        Description = request.Description
                    };
                    rowsAffected += new IwbDAL(request.TenantId).InsertTestInvitation(invitation);
                }
                return new IwbResponse
                {
                    Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    RowsAffected = rowsAffected
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "SaveTestInvitation", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "SaveTestInvitation", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #endregion

        #region Dashboard

        public IwbResponse GetWelderDashboardData(IwbRequest request)
        {
            try
            {
                return new IwbDAL(request.TenantId).GetWelderDashboardData(request.UserId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "GetWelderDashboardData", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "GetWelderDashboardData", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        #endregion

        #region Users, Roles & Permissions

        public IwbResponse RegisterUser(IwbRequest request)
        {
            try
            {
                if (request.User.Type == IwbUserType.Welder)
                {
                    request.User.WelderStencilNumber = StringExtension.GenerateStencilNumber(request.User.DOB.GetValueOrDefault(), request.User.SocialSecurityNumber);
                }

                int lastId = new IwbDAL(request.TenantId).RegisterUser(request.User, request.OrganizationId, request.OrganizationAccountPassword);
                return new IwbResponse
                {
                    Acknowledge = lastId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    LastSavedId = lastId,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "RegisterUser", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "RegisterUser", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        public IwbResponse SetCustomerIdForUser(IwbRequest request)
        {
            try
            {
                int noOfRows = new IwbDAL(request.TenantId).SetCustomerIdForUser(request.UserId, request.CustomerIdZoho);
                return new IwbResponse
                {
                    Acknowledge = noOfRows > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    LastSavedId = request.UserId,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "UpdateJobStatus", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "UpdateJobStatus", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }

        }

        #endregion

        #region MYWPQ methods
        public IwbResponse GetMyWPQList(IwbRequest request)
        {
            try
            {
                StringBuilder sbWhereClause = new StringBuilder();
                sbWhereClause.Append(" 1 = 1 ");
                var criteria = request.Criteria;

                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.IWB, "GetMyWPQList", request.TenantId, "GetMyWPQList function has been called successfully. sbWhereClause = " + sbWhereClause.ToString()));

                int totalPages = 0;
                long totalRecords = 0;
                return new IwbResponse
                {
                    WPQs = new IwbDAL(request.TenantId).GetWPQList(request.UserId),
                    TotalPages = totalPages,
                    TotalRecords = Convert.ToInt32(totalRecords),
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "GetMyWPQList", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "GetMyWPQList", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }

        }

        public IwbResponse GetWPQDetail(IwbRequest request)
        {
            try
            {
                StringBuilder sbWhereClause = new StringBuilder();

                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.IWB, "GetWPQDetail", request.TenantId, "GetWPQDetail function has been called successfully. sbWhereClause = " + sbWhereClause.ToString()));

                return new IwbResponse
                {
                    ResultData = new IwbDAL(request.TenantId).GetWPQDetail(request.Wpq)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "GetWPQDetail", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "GetWPQDetail", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }

        }
        #endregion

        #region MYWPQ methods
        public IwbResponse GetReportData(IwbRequest request)
        {
            try
            {
                StringBuilder sbWhereClause = new StringBuilder();
                sbWhereClause.Append(" 1 = 1 ");
                var criteria = request.Criteria;

                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.IWB, "GetReportData", request.TenantId, "GetcontractorReport function has been called successfully. sbWhereClause = " + sbWhereClause.ToString()));

                int totalPages = 0;
                long totalRecords = 0;

                IwbResponse resultResponse = null;
                switch (request.Criteria.Alias)
                {
                    case "contractor":
                    case "welder":
                        resultResponse = new IwbResponse
                        {
                            Jobs = new IwbDAL(request.TenantId).GetcontractorReport(request.UserId, request.Criteria, Convert.ToInt32(request.Data)),
                            TotalPages = totalPages,
                            TotalRecords = Convert.ToInt32(totalRecords),
                        };
                        break;
                    case "owner":
                        resultResponse = new IwbResponse
                        {
                            Documents = new IwbDAL(request.TenantId).GetOwnerReportdata(request.UserId, request.Criteria, Convert.ToInt32(request.Data)),
                            TotalPages = totalPages,
                            TotalRecords = Convert.ToInt32(totalRecords),
                        };
                        break;
                    case "insurance":
                        resultResponse = new IwbResponse
                        {
                            Documents = new IwbDAL(request.TenantId).GetInsuranceReportdata(request.UserId, request.Criteria, Convert.ToInt32(request.Data)),
                            TotalPages = totalPages,
                            TotalRecords = Convert.ToInt32(totalRecords),
                        };
                        break;
                    case "testing":
                        resultResponse = new IwbResponse
                        {
                            Tests = new IwbDAL(request.TenantId).GetTestingReportdata(request.UserId, request.Criteria, Convert.ToInt32(request.Data)),
                            TotalPages = totalPages,
                            TotalRecords = Convert.ToInt32(totalRecords),
                        };
                        break;

                    default:
                        resultResponse = new IwbResponse
                        {
                            TotalPages = totalPages,
                            TotalRecords = Convert.ToInt32(totalRecords),
                        };
                        break;
                }

                return resultResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "GetReportData", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "GetReportData", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }

        }
        #endregion

        #region Workder history related methods
        public IwbResponse GetContractorWorkHistory(IwbRequest request)
        {
            try
            {
                StringBuilder sbWhereClause = new StringBuilder();
                sbWhereClause.Append(" 1 = 1 ");
                var criteria = request.Criteria;

                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.IWB, "GetContractorWorkHistory", request.TenantId, "GetContractorWorkHistory function has been called successfully. sbWhereClause = " + sbWhereClause.ToString()));

                int totalPages = 0;
                long totalRecords = 0;
                return new IwbResponse
                {
                    UserWorkHistories = new IwbDAL(request.TenantId).GetContractorWorkHistory(request.UserId),
                    TotalPages = totalPages,
                    TotalRecords = Convert.ToInt32(totalRecords),
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "GetContractorWorkHistory", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "GetContractorWorkHistory", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }

        }

        public IwbResponse GetWelderWorkHistory(IwbRequest request)
        {
            try
            {
                StringBuilder sbWhereClause = new StringBuilder();
                sbWhereClause.Append(" 1 = 1 ");
                var criteria = request.Criteria;

                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.IWB, "GetWelderWorkHistory", request.TenantId, "GetWelderWorkHistory function has been called successfully. sbWhereClause = " + sbWhereClause.ToString()));

                int totalPages = 0;
                long totalRecords = 0;
                return new IwbResponse
                {
                    ResultData = new IwbDAL(request.TenantId).GetWelderWorkHistory(request.UserId, request.JobStatusId == 1),
                    TotalPages = totalPages,
                    TotalRecords = Convert.ToInt32(totalRecords),
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "GetWelderWorkHistory", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "GetWelderWorkHistory", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }

        }

        public IwbResponse GetWelderInfo(IwbRequest request)
        {
            try
            {
                StringBuilder sbWhereClause = new StringBuilder();
                sbWhereClause.Append(" 1 = 1 ");
                var criteria = request.Criteria;

                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.IWB, "GetWelderInfo", request.TenantId, "GetWelderInfo function has been called successfully. sbWhereClause = " + sbWhereClause.ToString()));

                int totalPages = 0;
                long totalRecords = 0;
                return new IwbResponse
                {
                    ResultData = new IwbDAL(request.TenantId).GetWelderInfo(Convert.ToInt32(request.Data)),
                    TotalPages = totalPages,
                    TotalRecords = Convert.ToInt32(totalRecords),
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "GetWelderInfo", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "GetWelderInfo", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }

        }
        #endregion

        public IwbResponse SaveAIResponse(IwbRequest request)
        {
            try
            {
                new IwbDAL(request.TenantId).InsertAIResponse((AIResponse)request.Data);
                return new IwbResponse
                {
                    Acknowledge = AcknowledgeType.Success
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "SaveAIResponse", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "SaveAIResponse", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public IwbResponse SaveWelderRatings(IwbRequest request)
        {
            try
            {
                //new IwbDAL(request.TenantId).InsertWelderRatings((WelderRatingModel)request.Data);
                new IwbDAL(request.TenantId).InsertWelderRatings(request.WelderRating);
                return new IwbResponse
                {
                    Acknowledge = AcknowledgeType.Success
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "SaveWelderRatings", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "SaveWelderRatings", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public IwbResponse GetWelderRatingsById(IwbRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.IWB, "GetWelderRatingsById", request.TenantId, "GetWelderRatingsById executed successfully."));

                return new IwbResponse
                {
                    WelderRatings = new IwbDAL(request.TenantId).GetWelderRatingsById(request.WelderRating.WelderId)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "GetTestAttendees", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "GetTestAttendees", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public IwbResponse GetJobTitles(IwbRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.IWB, "GetJobTitles", request.TenantId, "GetJobTitles executed successfully."));

                return new IwbResponse
                {
                    JobTitles = new IwbDAL(request.TenantId).GetJobTitles()
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "GetJobTitles", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "GetJobTitles", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public IwbResponse GetJobDetailsById(IwbRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.IWB, "GetJobDetailsById", request.TenantId, "GetJobDetailsById executed successfully."));

                return new IwbResponse
                {
                    JobDetails = new IwbDAL(request.TenantId).GetJobDetailsById(request.JobId)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.IWB, "GetJobDetailsById", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "GetJobDetailsById", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        

        public IwbResponse CreateZohoInvoice(IwbRequest request)
        {
            var response = new IwbResponse();
            ZohoInvoicePayload payload = null;

            // Prefer ObjectData if already deserialized
            if (request.ObjectData is ZohoInvoicePayload directPayload)
            {
                payload = directPayload;
            }
            // Fallback to JsonData
            else if (!string.IsNullOrEmpty(request.JsonData))
            {
                try
                {
                    payload = JsonConvert.DeserializeObject<ZohoInvoicePayload>(request.JsonData);
                }
                catch (Exception ex)
                {
                    response.Result = $"Failed to deserialize JsonData: {ex.Message}";
                    return response;
                }
            }

            if (payload != null)
            {
                // ✅ Extract from ContextData
                var accessToken = request.ContextData["AccessToken"]?.ToString();
                var orgId = request.ContextData["OrgId"]?.ToString();
                var endpoint = request.ContextData["Endpoint"]?.ToString();

                // ✅ Pass to DAL
                var result = new IwbDAL(request.TenantId).PostToZohoInvoiceAPI(payload, accessToken, orgId, endpoint);

                response.IsSuccess = result.Item1;
                response.Result = result.Item2;
            }
            else
            {
                response.Result = "Invalid or missing payload.";
            }

            return response;
        }



        public IwbResponse GetZohoTransactionHistory(IwbRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.IWB, "GetZohoTransactionHistory", request.TenantId, "GetZohoTransactionHistory function called successfully."));

                return new IwbResponse
                {
                    TransactionHistory = new IwbDAL(request.TenantId).GetZohoTransactionHistory(request.CustomerId),
                    Acknowledge = AcknowledgeType.Success
                };
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "GetZohoTransactionHistory", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

    }
}