﻿using RevCord.DataAccess;
using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.Messages;
using RevCord.DataContracts.MGODataEntities;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.BusinessLogic
{
    public class MGODataManager
    {
        public MGODataResponse GetMGOTempDataById(int tenantId, int Id)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.MGOData, "GetMGOTempDataById", tenantId, "GetMGOTempDataById function has been called. tenantId = " + tenantId));
                return new MGODataResponse
                {
                    TempData = new MGODataDAL(tenantId).GetMGOTempDataById(Id)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.MGOData, "GetMGOTempDataById", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.MGOData, "GetMGOTempDataById", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public MGODataResponse SaveMGOTempData(int tenantId, MGOTempData data)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.MGOData, "SaveMGOTempData", tenantId, "SaveMGOTempData function has been called. tenantId = " + tenantId));
                int Id = new MGODataDAL(tenantId).SaveMGOTempData(data);
                return new MGODataResponse
                {
                    FlagStatus = (Id > 0) ? true : false,
                    Id = Id
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.MGOData, "SaveMGOTempData", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.MGOData, "SaveMGOTempData", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public MGODataResponse GetMGOReportDataByEventId(int tenantId, string EventId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.MGOData, "GetMGOTempDataById", tenantId, "GetMGOReportDataByEventId function has been called. tenantId = " + tenantId));
                return new MGODataResponse
                {
                    ReportData = new MGODataDAL(tenantId).GetMGOReportDataByEventId(EventId)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.MGOData, "GetMGOReportDataByEventId", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.MGOData, "GetMGOReportDataByEventId", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public MGODataResponse SaveMGOReportData(int tenantId, MGOReportData data)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.MGOData, "SaveMGOReportData", tenantId, "SaveMGOReportData function has been called. tenantId = " + tenantId));
                int Id = new MGODataDAL(tenantId).SaveMGOReportData(data);
                return new MGODataResponse
                {
                    FlagStatus = (Id > 0) ? true : false,
                    Id = Id
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.MGOData, "SaveMGOReportData", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.MGOData, "SaveMGOReportData", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public MGODataResponse UpdateMGOEventStatus(int tenantId, MGOReportData data)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.MGOData, "UpdateMGOEventStatus", tenantId, "UpdateMGOEventStatus function has been called. tenantId = " + tenantId));
                int Id = new MGODataDAL(tenantId).UpdateMGOEventStatus(data);
                return new MGODataResponse
                {
                    FlagStatus = (Id > 0) ? true : false,
                    Id = Id
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.MGOData, "UpdateMGOEventStatus", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.MGOData, "UpdateMGOEventStatus", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        #region MGO - Inspection Type

        public MGODataResponse GetMGOInspectionTypes(int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.MGOData, "GetMGOInspectionTypes", tenantId, "GetMGOInspectionTypes function has been called. tenantId = " + tenantId));
                
                return new MGODataResponse
                {
                    InspectionTypes = new MGODataDAL(tenantId).GetMGOInspectionTypes()
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.MGOData, "GetMGOInspectionTypes", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.MGOData, "GetMGOInspectionTypes", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public MGODataResponse GetMGOInspectionTypesByProjectTypeID(int tenantId, int ProjectTypeID)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.MGOData, "GetMGOInspectionTypesByProjectTypeID", tenantId, "GetMGOInspectionTypesByProjectTypeID function has been called. tenantId = " + tenantId));
                
                return new MGODataResponse
                {
                    InspectionTypes = new MGODataDAL(tenantId).GetMGOInspectionTypesByProjectTypeID(ProjectTypeID)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.MGOData, "GetMGOInspectionTypesByProjectTypeID", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.MGOData, "GetMGOInspectionTypesByProjectTypeID", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public MGODataResponse GetMGOInspectionOptions(int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.MGOData, "GetMGOInspectionOptions", tenantId, "GetMGOInspectionOptions function has been called. tenantId = " + tenantId));
                
                return new MGODataResponse
                {
                    InspectionOptions = new MGODataDAL(tenantId).GetMGOInspectionOptions()
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.MGOData, "GetMGOInspectionOptions", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.MGOData, "GetMGOInspectionOptions", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public MGODataResponse GetMGOInspectionOptionsByProjectTypeID(int tenantId, int ProjectTypeID)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.MGOData, "GetMGOInspectionOptionsByProjectTypeID", tenantId, "GetMGOInspectionOptionsByProjectTypeID function has been called. tenantId = " + tenantId));
                
                return new MGODataResponse
                {
                    InspectionOptions = new MGODataDAL(tenantId).GetMGOInspectionOptionsByProjectTypeID(ProjectTypeID)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.MGOData, "GetMGOInspectionOptionsByProjectTypeID", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.MGOData, "GetMGOInspectionOptionsByProjectTypeID", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #endregion MGO - Inspection Type
    }
}
