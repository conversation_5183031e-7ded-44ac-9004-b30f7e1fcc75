﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RevCord.DataContracts.Messages;
using RevCord.DataContracts.MessageBase;
using RevCord.DataAccess;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.Util;
using RevCord.DataContracts;
using System.Data.SqlClient;
using RevCord.DataAccess.Util;

namespace RevCord.BusinessLogic
{
    public class MTRManager
    {
        private readonly static string _serverIP = SiteConfig.HelperServiceAddress; //"127.0.0.1";

        //contentNode.CustomProperties.AddRange(
        #region MTR

        public void SaveMTRReport(MTRCertificate vrReq, int tenantId, int playlistId)
        {
            try
            {
                new MTRDAL(tenantId).SaveMTRReport(vrReq, playlistId);
            }
            catch (Exception)
            {
                throw;
            }
        }

        public List<Signature> GetSignOffHistory(int signDocumentId, int tenantId)
        {
            try
            {
                var mtrSignOff = new MTRDAL(tenantId).GetSignOffHistory(signDocumentId, tenantId);
                return mtrSignOff;
            }
            catch (SqlException sqle)
            {
                throw sqle;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public void SaveMTRChecklist(Checklist vrReq, int tenantId, int playlistId)
        {
            try
            {
                new MTRDAL(tenantId).SaveMTRChecklist(vrReq, playlistId);
            }
            catch (Exception)
            {
                throw;
            }
        }
        #endregion
    }
}
