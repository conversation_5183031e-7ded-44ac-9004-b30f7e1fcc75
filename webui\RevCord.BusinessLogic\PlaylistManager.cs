﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.Messages;
using RevCord.DataContracts.MessageBase;
using RevCord.DataAccess;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.Util;
using RevCord.DataContracts;
using System.Data.SqlClient;
using System.Threading.Tasks;
using RevCord.DataAccess.Util;

namespace RevCord.BusinessLogic
{
    public class PlaylistManager
    {
        private readonly static string _serverIP = SiteConfig.HelperServiceAddress; //"127.0.0.1";

        //contentNode.CustomProperties.AddRange(
        #region Playlist

        public VRResponse SavePlaylist(VRRequest vrReq)
        {
            try
            {
                //Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Playlist, "SavePlaylist", vrReq.TenantId, "SavePlaylist function has been called successfully. vrReq.PersistType = " + vrReq.PersistType.ToString() + " vrReq.Playlist.Id = " + vrReq.Playlist.Id + " vrReq.Playlist.Name = " + vrReq.Playlist.Name));
                switch (vrReq.PersistType)
                {
                    case PersistType.Insert:
                        string Ids = new PlaylistDAL(vrReq.TenantId).InsertPlaylist(vrReq.Playlist.Name, vrReq.Playlist.Comments, vrReq.Playlist.CreatedDate, vrReq.UserId, vrReq.Playlist.IsMTRplaylist);
                        int lastId = Convert.ToInt32(Ids.Split('#')[0]);
                        int RevSyncPlaylistID = Convert.ToInt32(Ids.Split('#')[1]);
                        return new VRResponse
                        {
                            //Acknowledge = PlaylistDAL.InsertPlaylist(vrReq.Playlist.Name, vrReq.Playlist.Comments, vrReq.Playlist.CreatedDate, vrReq.UserId) ? AcknowledgeType.Success : AcknowledgeType.Failure,
                            Acknowledge = lastId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                            PlaylistId = lastId,
                            RevSyncPlaylistID = RevSyncPlaylistID,
                        };
                    case PersistType.Update:
                        int rowsAffected = new PlaylistDAL(vrReq.TenantId).UpdatePlaylist(vrReq.Playlist.Id, vrReq.Playlist.Name, vrReq.Playlist.Comments, vrReq.ModifiedDate, vrReq.UserId, vrReq.Playlist.RevSyncServerID);
                        return new VRResponse
                        {
                            Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                            RowsAffected = rowsAffected,
                            PlaylistId = vrReq.Playlist.Id,
                            RevSyncPlaylistID = vrReq.Playlist.RevSyncServerID,
                        };
                    case PersistType.Delete:
                        int rowsDeleted = new PlaylistDAL(vrReq.TenantId).DeletePlaylist(vrReq.PlaylistId, vrReq.ModifiedDate, vrReq.PlaylistRevSyncServerID);
                        return new VRResponse
                        {
                            Acknowledge = rowsDeleted > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                            RowsAffected = rowsDeleted,
                            RevSyncPlaylistID = vrReq.PlaylistRevSyncServerID,
                        };
                    default:
                        break;
                }
                return null;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Playlist, "SavePlaylist", vrReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Playlist, "SavePlaylist", vrReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public List<Playlist> GetPlaylists(int userId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Playlist, "GetPlaylists", tenantId, "GetPlaylists function has been called successfully. userId = " + userId));
                return new PlaylistDAL(tenantId).GetPlaylists(userId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Playlist, "GetPlaylists", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Playlist, "GetPlaylists", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public List<Playlist> GetRepoList(int userId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Playlist, "GetRepoList", tenantId, "GetRepoList function has been called successfully. userId = " + userId));
                return new PlaylistDAL(tenantId).GetRepoList(userId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Playlist, "GetRepoList", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Playlist, "GetRepoList", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        //public Playlist GetPlaylist(int playlistId)
        //{
        //    //return PlaylistDAL.GetPlaylist(playlistId);
        //    var pl = PlaylistDAL.GetPlaylist(playlistId);
        //    foreach (var item in pl.PlaylistDetails.Where(c => string.IsNullOrEmpty(c.CallInfo.CallId) && string.IsNullOrEmpty(c.CallInfo.StartTimeString)))
        //    {
        //        item.CallInfo = DemoData.GetDemoDataForPage().FirstOrDefault(ci => ci.CallId == item.CallId);
        //    }
        //    return pl;
        //    //pl.PlaylistDetails.RemoveAll((x) => x.CallInfo == null);
        //    //return pl;
        //}

        //

        //private static List<Recorder> recorders = new List<Recorder> 
        //{ 
        //    new Recorder { Id = 2, Name = "SHER-REC 1", IP = "***********",IsPrimary=true, ConnectionString = @"Data Source=.;Initial Catalog=VoiceRec_R9.3;Integrated Security=SSPI" },
        //    new Recorder { Id = 2, Name = "SHER-REC 2", IP = "***********", ConnectionString = @"Data Source=.;Initial Catalog=VoiceRec_R9.3_ECTest;Integrated Security=SSPI" },
        //};


        //public Playlist GetPlaylistByRecorders(List<Recorder> recorders, int playlistId)
        //{
        //    Playlist playlist = null;// new Playlist { Id = playlistId };

        //    string dbCallIds = "";
        //    List<string> callIds = null;
        //    List<string> secondaryRecCallIds = null;
        //    foreach (var rec in recorders)
        //    {
        //        if (rec.IsPrimary)
        //        {
        //            playlist = PlaylistDAL.GetPlaylistWithCallIds(playlistId, out dbCallIds);
        //            callIds = dbCallIds.Split(',').ToList();
        //            foreach (var item in playlist.PlaylistDetails.Where(c => string.IsNullOrEmpty(c.CallInfo.CallId) && string.IsNullOrEmpty(c.CallInfo.StartTimeString)))
        //            {
        //                item.CallInfo = DemoData.GetDemoDataForPage().FirstOrDefault(ci => ci.CallId == item.CallId);
        //                //callIds.Remove(item.CallInfo.CallId);
        //            }

        //            var pldCallId = playlist.PlaylistDetails.Where(r => !string.IsNullOrWhiteSpace(r.CallId))
        //                                                    .Select(pld => pld.CallId).ToList();

        //            secondaryRecCallIds = callIds.Except(pldCallId, StringComparer.OrdinalIgnoreCase).ToList();
        //        }
        //        else
        //        {
        //            if (!string.IsNullOrEmpty(dbCallIds) && secondaryRecCallIds.Count != 0)
        //            {
        //                //GetCallByIds
        //                //var csv = string.Join("','", secondaryRecCallIds);
        //                //string csv = string.Join("','", secondaryRecCallIds.Select(i => i.Replace("'", "''")));
        //                string csv = string.Format("'{0}'", string.Join("','", secondaryRecCallIds.Select(i => i.Replace("'", "''"))));
        //                var secondaryRecCalls = VoiceLoggingDALEC.GetCallsByIds(rec, csv);
        //                foreach (var call in secondaryRecCalls)
        //                {
        //                    playlist.PlaylistDetails.Add(new PlaylistDetail { CallInfo = call });
        //                }
        //            }
        //        }
        //    }

        //    return playlist;
        //}

        public Playlist GetPlaylist(int playlistId, int tenantId)
        {
            try
            {
                //return PlaylistDAL.GetPlaylistById(playlistId);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Playlist, "GetPlaylist", tenantId, "GetPlaylist function has been called successfully. playlistId = " + playlistId));

                var playlist = new PlaylistDAL(tenantId).GetPlaylistById(playlistId);
                foreach (var pld in playlist.PlaylistDetails.Where(pli => pli.IsDemoItem))
                {
                    var demoRecord = DemoData.SearchInDemoDataBasedOnFileName(pld.FileName, pld.FileType);
                    if (demoRecord != null)
                    {
                        pld.ChannelId = demoRecord.ChannelId;
                        pld.ChannelName = demoRecord.ChannelName;
                    }
                }
                return playlist;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Playlist, "GetPlaylist", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Playlist, "GetPlaylist", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public Playlist GetPlaylistByRecorders(List<Recorder> recorders, int playlistId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Playlist, "GetPlaylistByRecorders", tenantId, "GetPlaylistByRecorders function has been called successfully. playlistId = " + playlistId + " recorder Ids = " + string.Join(",", recorders.Select(c=>c.Id))));
                Playlist playlist = null;// new Playlist { Id = playlistId };
                List<string> secondaryRecCallIds = null;
                var bExceptionThrown = false;
                foreach (var rec in recorders)
                {
                    try
                    {
                        bExceptionThrown = false;
                        if (rec.IsPrimary)
                        {
                            playlist = new PlaylistDAL(tenantId).GetPlaylistById(playlistId);
                            foreach (var pld in playlist.PlaylistDetails.Where(pli => pli.IsDemoItem))
                            {
                                var demoRecord = DemoData.SearchInDemoDataBasedOnFileName(pld.FileName, pld.FileType);

                                if (demoRecord != null)
                                {
                                    pld.ChannelId = demoRecord.ChannelId;
                                    pld.ChannelName = demoRecord.ChannelName;
                                }
                            }
                            secondaryRecCallIds = playlist.PlaylistDetails.Where(r => !string.IsNullOrWhiteSpace(r.CallId) && r.RecorderId != rec.Id)
                                .Select(pld => pld.CallId).ToList();
                        }
                        else
                        {
                            if (secondaryRecCallIds.Count != 0)
                            {
                                // Alternative approach
                                RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                                entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });

                                string csv = string.Format("'{0}'", string.Join("','", secondaryRecCallIds.Select(i => i.Replace("'", "''"))));
                                //ApplicationLog.PrintLog(LogCategory.Information, " secondaryRecCallIds.Count = " + secondaryRecCallIds.Count + " csv : secondaryRecCallIds " + csv);

                                //var secondaryRecCalls = VoiceLoggingDALEC.GetCallsByIds(rec, csv);
                                var secondaryRecCalls = entClient.GetCallsByIds(rec, csv).ToList();

                                #region DSF Files Mapping
                                foreach (var dsfcall in secondaryRecCalls)
                                {
                                    if (playlist.PlaylistDetails != null && dsfcall != null)
                                    {
                                        var dsfPld = playlist.PlaylistDetails.FirstOrDefault(pd => pd.RecorderId == rec.Id && pd.CallId == dsfcall.CallId);
                                        if (dsfPld != null)
                                        {
                                            //dsfPld.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);
                                            dsfPld.ChannelId = dsfcall.ChannelId;
                                            dsfPld.ChannelName = dsfcall.ChannelName;
                                            dsfPld.GroupId = dsfcall.GroupId;
                                            dsfPld.GroupName = dsfcall.GroupName;
                                            dsfPld.Comments = dsfcall.CallComments;
                                            dsfPld.Tag4 = dsfcall.Tag4;
                                            dsfPld.Tag3 = dsfcall.Tag3;//Address
                                            dsfPld.ANI_PH = dsfcall.ANIPhone; //Ani_PH
                                            dsfPld.RetainValue = Convert.ToBoolean(dsfcall.RetainValue);
                                            dsfPld.BookmarkXML = dsfcall.BookmarkXML;
                                            dsfPld.BookmarkCSV = dsfcall.BookmarkCSV;
                                            dsfPld.interview_DateTime = dsfcall.interview_DateTime;
                                            dsfPld.interview_Interviewer = dsfcall.interview_Interviewer;
                                            dsfPld.interview_Interviewee = dsfcall.interview_Interviewee;
                                            dsfPld.interview_InterviewId = dsfcall.interview_InterviewId;
                                            dsfPld.interview_GPS = dsfcall.interview_GPS;
                                            dsfPld.IsRevcell = dsfcall.IsRevCell;
                                            dsfPld.RecorderId = rec.Id;

                                            dsfPld.ScreenRecFile = dsfcall.ScreenFileNames;
                                            if (!string.IsNullOrEmpty(dsfcall.ScreenFileNames) && dsfcall.CallType == 1)
                                                dsfPld.FileType = FileType.Screens;
                                        }
                                    }
                                }
                                #endregion
                            }
                        }
                    }
                    catch (Exception e)
                    {
                        bExceptionThrown = true;
                        throw;
                    }
                    if (bExceptionThrown)
                        continue;
                }
                return playlist;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Playlist, "GetPlaylistByRecorders", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Playlist, "GetPlaylistByRecorders", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        #endregion


        #region Playlist Details

        //public bool AddFile(int playlistId, string callId)
        //{
        //    return PlaylistDAL.InsertPlaylistItemAudio(playlistId, callId, AppSettingsHelper.GetValueAsShort("maxPlaylistItem"));
        //}

        //public VRResponse AddFiles(int playlistId, IEnumerable<string> callIds)
        //{
        //    string ids = string.Join<string>(",", callIds);
        //    int rowsAffected = PlaylistDAL.InsertPlaylistItemsAudio(playlistId, ids, AppSettingsHelper.GetValueAsShort("maxPlaylistItem"));
        //    return new VRResponse
        //    {
        //        Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
        //        RowsAffected = rowsAffected,
        //        PlaylistId = playlistId,
        //    };
        //}

        public bool AddFile(int playlistId, PlaylistDetail playlistDetail, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Playlist, "AddFile", tenantId, "AddFile function has been called successfully. playlistId = " + playlistId));
                return new PlaylistDAL(tenantId).InsertPlaylistItemAudio(playlistId, playlistDetail.CallId, AppSettingsHelper.GetValueAsShort("maxPlaylistItem"));
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Playlist, "AddFile", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Playlist, "AddFile", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public VRResponse AddItems(int playlistId, int RevSyncServerPlaylistID, List<PlaylistDetail> playlistDetails, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Playlist, "AddItems", tenantId, "AddItems function has been called successfully. playlistId = " + playlistId + " RevSyncServerPlaylistID = " + RevSyncServerPlaylistID));
                int rowsAffected = new PlaylistDAL(tenantId).InsertPlaylistItem(playlistDetails, playlistId, RevSyncServerPlaylistID, 500);
                return new VRResponse
                {
                    Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    RowsAffected = rowsAffected,
                    PlaylistId = playlistId,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Playlist, "AddItems", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Playlist, "AddItems", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public VRResponse AddFiles(int playlistId, IEnumerable<PlaylistDetail> playlistDetails, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Playlist, "AddFiles", tenantId, "AddFiles function has been called successfully. playlistId = " + playlistId));
                var audioCalls = playlistDetails.Where(pld => pld.FileType == FileType.Audio);
                int rowsAffected = 0;
                #region --- Audio ---

                if (audioCalls != null)
                {
                    string audioCallIds = string.Join(", ", audioCalls.Select(c => "" + c.CallId + ""));
                    System.Diagnostics.Debug.WriteLine(audioCallIds);
                    rowsAffected = new PlaylistDAL(tenantId).InsertPlaylistItemsAudio(playlistId, audioCallIds, AppSettingsHelper.GetValueAsShort("maxPlaylistItem"));
                }
                #endregion

                #region --- Non-Audio ---

                var nonAudioCalls = playlistDetails.Where(pld => pld.FileType != FileType.Audio).ToList();
                if (nonAudioCalls != null)
                {
                    var savePld = new PlaylistDAL(tenantId).InsertPlaylistItemsNonAudio(playlistId, nonAudioCalls);
                    rowsAffected = rowsAffected + nonAudioCalls.Count;
                }

                #endregion

                return new VRResponse
                {
                    Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    RowsAffected = rowsAffected,
                    PlaylistId = playlistId,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Playlist, "AddFiles", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Playlist, "AddFiles", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool AddCustomItem(UserItemInfo userItem, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Playlist, "AddCustomItem", tenantId, "AddFiles function has been called successfully."));
                userItem.sCallId = Guid.NewGuid().ToString("N");
                //return true;
                return new PlaylistDAL(tenantId).InsertPlaylistCustomItem(userItem);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Playlist, "AddCustomItem", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Playlist, "AddCustomItem", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        private FileType getFileTypeFromPlayerFileType(PlayerFileType playerFormat)
        {
            try
            {
                switch (playerFormat)
                {
                    case PlayerFileType.NotSupported:
                        return FileType.None;
                    case PlayerFileType.DSF:
                        break;
                    case PlayerFileType.WAV:
                        break;
                    case PlayerFileType.WMA:
                        break;
                    case PlayerFileType.WMV:
                        break;
                    case PlayerFileType.ASF:
                        break;
                    case PlayerFileType.MP3:
                        break;
                    case PlayerFileType.SMS:
                        return FileType.Text;
                    case PlayerFileType.Email:
                        return FileType.Email;
                    case PlayerFileType.Social:
                        return FileType.Social;
                    case PlayerFileType.Image:
                        break;
                    case PlayerFileType.Document:
                        break;
                    default:
                        break;
                }
                return FileType.None;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public bool RemoveFile(int playlistItemId, int tenantId, int RevSyncServerPlaylistID)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Playlist, "RemoveFile", tenantId, "RemoveFile function has been called successfully. playlistItemId = " + playlistItemId));
                return new PlaylistDAL(tenantId).DeletePlaylistItem(playlistItemId, RevSyncServerPlaylistID);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Playlist, "RemoveFile", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Playlist, "RemoveFile", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public int RemoveFile(int playlistId, string callId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Playlist, "RemoveFile", tenantId, "RemoveFile function has been called successfully. playlistId = " + playlistId));
                return new PlaylistDAL(tenantId).DeletePlaylistItem(playlistId, callId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Playlist, "RemoveFile", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Playlist, "RemoveFile", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public bool RemoveFileByCallId(string callId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Playlist, "RemoveFileByCallId", tenantId, "RemoveFileByCallId function has been called successfully. callId = " + callId));
                return new PlaylistDAL(tenantId).DeletePlaylistItemByCallId(callId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Playlist, "RemoveFileByCallId", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Playlist, "RemoveFileByCallId", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public VRResponse RemoveFiles(int playlistId, int RevSyncServerPlaylistID, IEnumerable<string> playlistItemIds, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Playlist, "RemoveFiles", tenantId, "RemoveFiles function has been called successfully. playlistId = " + playlistId + " RevSyncServerPlaylistID = " + RevSyncServerPlaylistID));
                int rowsDeleted = new PlaylistDAL(tenantId).DeletePlaylistItems(playlistId, RevSyncServerPlaylistID);
                return new VRResponse
                {
                    Acknowledge = rowsDeleted > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    RowsAffected = rowsDeleted,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Playlist, "DeletePlaylistItems", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Playlist, "DeletePlaylistItems", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #endregion


        #region Playlist Note
        public VRResponse GetPlaylistNotes(int playlistId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Playlist, "GetPlaylistNotes", tenantId, "GetPlaylistNotes function has been called successfully. playlistId = " + playlistId));
                var playlistNotes = new PlaylistDAL(tenantId).GetPlaylistNotes(playlistId);
                return new VRResponse { PlaylistNotes = playlistNotes, Acknowledge = playlistNotes != null ? AcknowledgeType.Success : AcknowledgeType.Failure };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Playlist, "GetPlaylistNotes", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Playlist, "GetPlaylistNotes", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public VRResponse InsertPlaylistNote(PlaylistNote playlistNote, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Playlist, "InsertPlaylistNote", tenantId, "InsertPlaylistNote function has been called successfully. playlistNote.PlaylistId = " + playlistNote.PlaylistId + " playlistNote.Note = " + playlistNote.Note));
                playlistNote.Id = new PlaylistDAL(tenantId).InsertPlaylistNote(playlistNote.PlaylistId, playlistNote.NoteType, playlistNote.Note);
                return new VRResponse { PlaylistNote = playlistNote, Acknowledge = playlistNote.Id > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Playlist, "InsertPlaylistNote", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Playlist, "InsertPlaylistNote", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public bool UpdatePlaylistNote(PlaylistNote playlistNote, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Playlist, "UpdatePlaylistNote", tenantId, "UpdatePlaylistNote function has been called successfully. playlistId = " + playlistNote.PlaylistId));
                return new PlaylistDAL(tenantId).UpdatePlaylistNote(playlistNote.Id, playlistNote.PlaylistId, playlistNote.Note);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Playlist, "UpdatePlaylistNote", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Playlist, "UpdatePlaylistNote", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public bool DeletePlaylistNote(int noteId, int playlistId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Playlist, "DeletePlaylistNote", tenantId, "DeletePlaylistNote function has been called successfully. playlistId = " + playlistId));
                return new PlaylistDAL(tenantId).DeletePlaylistNote(noteId, playlistId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Playlist, "DeletePlaylistNote", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Playlist, "DeletePlaylistNote", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        #endregion

        public bool UpdatePlaylistAttributes(int playlistId, string playlistName, string visibilityType, string playlistComments, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Playlist, "UpdatePlaylistAttributes", tenantId, "UpdatePlaylistAttributes function has been called successfully. playlistId = " + playlistId + " playlistName = " + playlistName));
                return new PlaylistDAL(tenantId).UpdatePlaylistAttributes(playlistId, playlistName, visibilityType, playlistComments);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Playlist, "UpdatePlaylistAttributes", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Playlist, "UpdatePlaylistAttributes", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool AddAlternateFileName(int playlistId, string callId, string alternateFileName, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Playlist, "AddAlternateFileName", tenantId, "AddAlternateFileName function has been called successfully. playlistId = " + playlistId + " callId = " + callId + " alternateFileName = " + alternateFileName));
                return new PlaylistDAL(tenantId).AddAlternateFileName(playlistId, callId, alternateFileName);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Playlist, "AddAlternateFileName", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Playlist, "AddAlternateFileName", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public int CountPlaylistItems(int playlistId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Playlist, "CountPlaylistItems", tenantId, "CountPlaylistItems function has been called successfully. playlistId = " + playlistId));
                return new PlaylistDAL(tenantId).CountPlaylistItems(playlistId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Playlist, "CountPlaylistItems", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Playlist, "CountPlaylistItems", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        //int PlaylistShareInsert(int playlistId, int sharedBy, int sharedWith, string oneTimeLink)
        public VRResponse PlaylistShareInsert(PlaylistShare playlistShare, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Playlist, "PlaylistShareInsert", tenantId, "PlaylistShareInsert function has been called successfully."));
                playlistShare.Id = new PlaylistDAL(tenantId).PlaylistShareInsert(playlistShare.PlaylistId, playlistShare.SharedBy, playlistShare.SharedWith, playlistShare.SharedWithEmail, playlistShare.OneTimeLink);
                return new VRResponse { PlaylistShare = playlistShare, Acknowledge = AcknowledgeType.Success };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Playlist, "PlaylistShareInsert", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Playlist, "PlaylistShareInsert", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public VRResponse PlaylistShareGet(int plShareId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Playlist, "PlaylistShareGet", tenantId, "PlaylistShareGet function has been called successfully."));
                
                PlaylistShare objPlShare = new PlaylistDAL(tenantId).PlaylistShareGet(plShareId);
                return new VRResponse { PlaylistShare = objPlShare, Acknowledge = AcknowledgeType.Success };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Playlist, "PlaylistShareGet", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Playlist, "PlaylistShareGet", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool PlaylistShareUpdate(int plShareId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Playlist, "PlaylistShareUpdate", tenantId, "PlaylistShareUpdate function has been called successfully."));

                return new PlaylistDAL(tenantId).PlaylistShareUpdate(plShareId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Playlist, "PlaylistShareUpdate", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Playlist, "PlaylistShareUpdate", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool AddDocumentToPlayList(int documentId, int playlistId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Playlist, "AddDocumentToPlayList", tenantId, "AddDocumentToPlayList function has been called successfully."));

                return new PlaylistDAL(tenantId).AddDocumentToPlayList(documentId, playlistId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Playlist, "AddDocumentToPlayList", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Playlist, "AddDocumentToPlayList", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
    }
}