﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace RevCord.BusinessLogic
{
    public static class QueryConstants
    {
        /// <summary>
        ///  AND CAST(CreatedDate AS date) BETWEEN 
        /// </summary>
        //public const string CREATED_DATE_BETWEEN = " AND CAST(CreatedDate AS date) BETWEEN ";

        public const string DURATION_DEFAULT = " AND CI.Duration > 0";
        public const string DURATION_BETWEEN = " AND CI.Duration > 0 AND CI.Duration BETWEEN '";

        public const string GROUPEXT_OR_CALUSE = @"({0}.GroupNum = {1} AND {0}.Ext IN ({2}))";
        public const string GROUPEXT_CALLTYPE_OR_CALUSE = @"({0}.GroupNum = {1} AND {0}.Ext IN ({2}) AND {0}.CallType = {3})";


        #region Report's

        public const string SELECT_YEAR = "SUBSTRING(CI.StartTime, 1, 4)";
        public const string SELECT_MONTH = "SUBSTRING(CI.StartTime, 5, 2)";
        public const string SELECT_DAY = "SUBSTRING(CI.StartTime, 7, 2)";

        public const string GROUP_YEAR = "SUBSTRING(CI.StartTime, 1, 4),";
        public const string GROUP_MONTH = "SUBSTRING(CI.StartTime, 5, 2),";
        public const string GROUP_DAY = "SUBSTRING(CI.StartTime, 7, 2)";

        public const string TIME_START = "AND SUBSTRING(CI.StartTime, 9, 14) BETWEEN '";
        public const string DURATION_LESS_4_SEC = "AND CI.Duration BETWEEN 0 AND 3000";
        public const string DURATION_BETWEEN_4_AND_14_SEC = "AND CI.Duration BETWEEN 4000 AND 14000";
        public const string DURATION_BETWEEN_15_AND_59_SEC = "AND CI.Duration BETWEEN 15000 AND 59000";
        public const string DURATION_BETWEEN_1_AND_2_MIN = "AND CI.Duration BETWEEN 60000 AND 119000";
        public const string DURATION_BETWEEN_2_AND_5_MIN = "AND CI.Duration BETWEEN 120000 AND 299000";
        public const string DURATION_BETWEEN_5_AND_10_MIN = "AND CI.Duration BETWEEN 300000 AND 599000";
        public const string DURATION_BETWEEN_10_AND_30_MIN = "AND CI.Duration BETWEEN 600000 AND 1799000";
        public const string DURATION_LONGER_THEN_30_MIN = "AND CI.Duration BETWEEN 1800000 AND 86399000";

        //public const string WHERE_CLAUSE_ANIALI = " AND (CI.ANI_PH IS NOT NULL OR ANI_NAME IS NOT NULL OR ANI_DETAILS IS NOT NULL  ) ";
        // KM changed on 20160814 For IQ3
        public const string WHERE_CLAUSE_ANIALI = " AND (CI.ANI_PH IS NOT NULL OR ANI_NAME IS NOT NULL OR ANI_DETAILS IS NOT NULL or (Tag4 is not null and (len(Tag4) > 1))) ";
        


        public const string INCOMING_CALLS = " AND ((CI.CalledID LIKE 'C %' OR ( ISNULL(NULLIF([CalledID], ''),'') = ''))) "; // CI.CalledID
        public const string OUTGOING_CALLS = " AND ((CI.CalledID NOT LIKE 'C %' AND ( ISNULL(NULLIF([CalledID], ''),'') <> ''))) "; // EI.Triger

        #endregion

    }
}
