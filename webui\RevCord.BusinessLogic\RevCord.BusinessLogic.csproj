﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{DF816774-FB1D-428D-96DE-8F6B704BEDEF}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RevCord.BusinessLogic</RootNamespace>
    <AssemblyName>RevCord.BusinessLogic</AssemblyName>
    <TargetFrameworkVersion>v4.6</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="JWT, Version=6.0.0.0, Culture=neutral, PublicKeyToken=6f98bca0f40f2ecf, processorArchitecture=MSIL">
      <HintPath>..\packages\JWT.6.1.4\lib\net46\JWT.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AspNet.SignalR.Client, Version=2.2.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.SignalR.Client.2.2.0\lib\net45\Microsoft.AspNet.SignalR.Client.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AspNet.SignalR.Core, Version=2.2.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.SignalR.Core.2.2.0\lib\net45\Microsoft.AspNet.SignalR.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.3.0.1\lib\net45\Microsoft.Owin.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin.Cors, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Cors.3.0.1\lib\net45\Microsoft.Owin.Cors.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin.Host.SystemWeb, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Host.SystemWeb.3.0.1\lib\net45\Microsoft.Owin.Host.SystemWeb.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin.Security, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.3.0.1\lib\net45\Microsoft.Owin.Security.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=10.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.10.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Owin, Version=*******, Culture=neutral, PublicKeyToken=f0ebd12fd5e55cc5, processorArchitecture=MSIL">
      <HintPath>..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Plivo, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Plivo.4.15.1\lib\netstandard1.3\Plivo.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.IO.FileSystem, Version=4.0.2.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.FileSystem.4.3.0\lib\net46\System.IO.FileSystem.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives, Version=4.0.2.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.FileSystem.Primitives.4.3.0\lib\net46\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net.Http, Version=4.1.1.3, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.4.3.4\lib\net46\System.Net.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security.Cryptography.Algorithms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.0\lib\net46\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.3.0\lib\net46\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Web.Cors, Version=5.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Cors.5.0.0\lib\net45\System.Web.Cors.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ADSyncGroupManager.cs" />
    <Compile Include="AuditManager.cs" />
    <Compile Include="BookmarkFlagManager.cs" />
    <Compile Include="BusinessLogicUtil.cs" />
    <Compile Include="CallTaggingManager.cs" />
    <Compile Include="CommonManager.cs" />
    <Compile Include="ConditionalLogicManager.cs" />
    <Compile Include="CustomerDBManager.cs" />
    <Compile Include="DashboardManager.cs" />
    <Compile Include="DemoData.cs" />
    <Compile Include="EvaluationManager.cs" />
    <Compile Include="InquireGroupManager.cs" />
    <Compile Include="InquireManager.cs" />
    <Compile Include="InquireRxManager.cs" />
    <Compile Include="InspectionManager.cs" />
    <Compile Include="IntermediateQueryHelper.cs" />
    <Compile Include="IwbManager.cs" />
    <Compile Include="MGODataManager.cs" />
    <Compile Include="MTRManager.cs" />
    <Compile Include="PlaylistManager.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="QueryConstants.cs" />
    <Compile Include="ReportManager.cs" />
    <Compile Include="RevcellManager.cs" />
    <Compile Include="RevSignManager.cs" />
    <Compile Include="RoleManagementManager.cs" />
    <Compile Include="ScheduleReportManager.cs" />
    <Compile Include="SearchMediaManager.cs" />
    <Compile Include="Service References\RevcordEnterpriseSvc\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="SurveyManager.cs" />
    <Compile Include="CustomTemplateBuilder.cs" />
    <Compile Include="TenantManager.cs" />
    <Compile Include="UserManagementManager.cs" />
    <Compile Include="UserManager.cs" />
    <Compile Include="VoiceLoggingManager.cs" />
    <Compile Include="VoiceRecManager.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
    <None Include="Properties\DataSources\RevCord.DataContracts.DTO.CallEvaluationDTO.datasource" />
    <None Include="Properties\DataSources\RevCord.DataContracts.DTO.DALMediaResponse.datasource" />
    <None Include="Properties\DataSources\RevCord.DataContracts.DTO.TreeViewDataDTO.datasource" />
    <None Include="Properties\DataSources\RevCord.DataContracts.EvaluationEntities.CallEvaluation.datasource" />
    <None Include="Properties\DataSources\RevCord.DataContracts.EvaluationEntities.UserEvaluation.datasource" />
    <None Include="Properties\DataSources\RevCord.DataContracts.Messages.UMResponse.datasource" />
    <None Include="Properties\DataSources\RevCord.DataContracts.ReportEntities.CallAuditReport.datasource" />
    <None Include="Properties\DataSources\RevCord.DataContracts.ReportEntities.RPTCallInfo.datasource" />
    <None Include="Properties\DataSources\RevCord.DataContracts.ReportEntities.RPTCallInfoDetail.datasource" />
    <None Include="Properties\DataSources\RevCord.DataContracts.ReportEntities.RPTEvaluation.datasource" />
    <None Include="Properties\DataSources\RevCord.DataContracts.Response.ReportResponse.datasource" />
    <None Include="Properties\DataSources\RevCord.DataContracts.Response.SurveyResponse.datasource" />
    <None Include="Properties\DataSources\RevCord.DataContracts.Response.UserManagementResponse.datasource" />
    <None Include="Properties\DataSources\RevCord.DataContracts.SurveyEntities.Option.datasource" />
    <None Include="Properties\DataSources\RevCord.DataContracts.SurveyEntities.Question.datasource" />
    <None Include="Properties\DataSources\RevCord.DataContracts.SurveyEntities.Survey.datasource" />
    <None Include="Properties\DataSources\RevCord.DataContracts.SurveyEntities.SurveySection.datasource" />
    <None Include="Properties\DataSources\RevCord.DataContracts.UserManagement.Agent.datasource" />
    <None Include="Properties\DataSources\RevCord.DataContracts.UserManagement.User.datasource" />
    <None Include="Properties\DataSources\RevCord.DataContracts.UserManagement.UserActivity.datasource" />
    <None Include="Properties\DataSources\RevCord.DataContracts.UserManagement.UserInfoLite.datasource" />
    <None Include="Properties\DataSources\RevCord.DataContracts.ViewModelEntities.RecorderEvaluation.datasource" />
    <None Include="Properties\DataSources\RevCord.DataContracts.ViewModelEntities.TreeviewData.datasource" />
    <None Include="Properties\DataSources\RevCord.DataContracts.VoiceRecEntities.CallInfo.datasource" />
    <None Include="Properties\DataSources\RevCord.DataContracts.VoiceRecEntities.CallInfoExportResult.datasource" />
    <None Include="Properties\DataSources\RevCord.DataContracts.VoiceRecEntities.CallInfoLite.datasource" />
    <None Include="Properties\DataSources\RevCord.DataContracts.VoiceRecEntities.Channel.datasource" />
    <None Include="Properties\DataSources\RevCord.DataContracts.VoiceRecEntities.MonitorChannel.datasource" />
    <None Include="Properties\DataSources\RevCord.DataContracts.VoiceRecEntities.UserExtensionInfo.datasource" />
    <None Include="Service References\RevcordEnterpriseSvc\RevCord.BusinessLogic.RevcordEnterpriseSvc.GetEnterpriseAssociatedEvaluationsResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\RevcordEnterpriseSvc\RevCord.BusinessLogic.RevcordEnterpriseSvc.GetEvaluationsResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\RevcordEnterpriseSvc\RevCord.BusinessLogic.RevcordEnterpriseSvc.GetUserActivitiesResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\RevcordEnterpriseSvc\RevCord.BusinessLogic.RevcordEnterpriseSvc.InsertBookmarkAndGetByCallIdResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\RevcordEnterpriseSvc\RevCord.BusinessLogic.RevcordEnterpriseSvc.PerformActionAndGetEnterpriseEvaluationDTOResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\RevcordEnterpriseSvc\RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchCallsChainedDBsResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\RevcordEnterpriseSvc\RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchCallsPrimaryDBResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\RevcordEnterpriseSvc\RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchRandomCallsPrimaryDBResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\RevcordEnterpriseSvc\RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchRecordedCallsResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\RevcordEnterpriseSvc\RevCord.BusinessLogic.RevcordEnterpriseSvc.ShareUnshareAndGetEvaluationDTOResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\RevcordEnterpriseSvc\RevCord.BusinessLogic.RevcordEnterpriseSvc.UpdateBookmarkAndGetByCallIdResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\RevcordEnterpriseSvc\RevEnterpriseSvc.wsdl" />
    <None Include="Service References\RevcordEnterpriseSvc\RevEnterpriseSvc.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\RevcordEnterpriseSvc\RevEnterpriseSvc1.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\RevcordEnterpriseSvc\RevEnterpriseSvc10.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\RevcordEnterpriseSvc\RevEnterpriseSvc11.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\RevcordEnterpriseSvc\RevEnterpriseSvc12.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\RevcordEnterpriseSvc\RevEnterpriseSvc13.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\RevcordEnterpriseSvc\RevEnterpriseSvc14.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\RevcordEnterpriseSvc\RevEnterpriseSvc15.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\RevcordEnterpriseSvc\RevEnterpriseSvc16.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\RevcordEnterpriseSvc\RevEnterpriseSvc161.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\RevcordEnterpriseSvc\RevEnterpriseSvc17.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\RevcordEnterpriseSvc\RevEnterpriseSvc2.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\RevcordEnterpriseSvc\RevEnterpriseSvc3.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\RevcordEnterpriseSvc\RevEnterpriseSvc4.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\RevcordEnterpriseSvc\RevEnterpriseSvc5.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\RevcordEnterpriseSvc\RevEnterpriseSvc6.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\RevcordEnterpriseSvc\RevEnterpriseSvc7.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\RevcordEnterpriseSvc\RevEnterpriseSvc8.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\RevcordEnterpriseSvc\RevEnterpriseSvc9.xsd">
      <SubType>Designer</SubType>
    </None>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadataStorage Include="Service References\RevcordEnterpriseSvc\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\RevcordEnterpriseSvc\RevEnterpriseSvc.disco" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\RevcordEnterpriseSvc\configuration91.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\RevcordEnterpriseSvc\configuration.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\RevcordEnterpriseSvc\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\RevCord.DataAccess\RevCord.DataAccess.csproj">
      <Project>{1aabeaa7-6e73-417e-b181-1825d833b2d9}</Project>
      <Name>RevCord.DataAccess</Name>
    </ProjectReference>
    <ProjectReference Include="..\RevCord.DataContracts\RevCord.DataContracts.csproj">
      <Project>{84c8cf04-1c65-4c93-8391-df8d74bb0b96}</Project>
      <Name>RevCord.DataContracts</Name>
    </ProjectReference>
    <ProjectReference Include="..\RevCord.Util\RevCord.Util.csproj">
      <Project>{465eca0c-68e4-413d-9462-9377ad7c4d53}</Project>
      <Name>RevCord.Util</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>