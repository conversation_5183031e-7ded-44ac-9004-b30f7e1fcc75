﻿using RevCord.DataAccess;
using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.Messages;
using RevCord.DataContracts.RevSignEntities;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.BusinessLogic
{
    public class RevSignManager
    {
        public RevSignResponse GetDocuments(RevSignRequest revSignRequest)
        {
            try
            {
                List<Document> documents = new RevSignDAL(revSignRequest.TenantId).GetDocuments(revSignRequest.OwnerId);
                return new RevSignResponse
                {
                    Documents = documents,
                    Message = "",
                    Status = documents != null ? true : false
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.RevSign, "GetDocuments", revSignRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.RevSign, "GetDocuments", revSignRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public RevSignResponse GetDocumentAndSignerById(RevSignRequest revSignRequest)
        {
            try
            {
                Document document = new RevSignDAL(revSignRequest.TenantId).GetDocumentAndSignerById(revSignRequest.DocumentId, revSignRequest.DocumentSignerId);
                return new RevSignResponse
                {
                    Document = document,
                    Message = "Document object has been fetched successfully.",
                    Status = document != null ? true : false
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.RevSign, "GetDocumentAndSignerById", revSignRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.RevSign, "GetDocumentAndSignerById", revSignRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public RevSignResponse DeleteDocumentSigningRequest(RevSignRequest revSignRequest)
        {
            try
            {
                var response = new RevSignDAL(revSignRequest.TenantId).DeleteDocumentSigningRequest(revSignRequest.DocumentId);
                return new RevSignResponse
                {
                    Message = response == true ? "Document signing request has been deleted successfully" : "Unable to delete the document signing request.",
                    Status = response
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.RevSign, "DeleteDocumentSigningRequest", revSignRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.RevSign, "DeleteDocumentSigningRequest", revSignRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public RevSignResponse GetDocumentById(RevSignRequest revSignRequest)
        {
            try
            {
                Document document = new RevSignDAL(revSignRequest.TenantId).GetDocumentById(revSignRequest.DocumentId);
                return new RevSignResponse
                {
                    Document = document,
                    Message = "Document object has been fetched successfully.",
                    Status = document != null ? true : false
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.RevSign, "GetDocumentById", revSignRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.RevSign, "GetDocumentById", revSignRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public RevSignResponse SaveRevSignoffRequest(RevSignRequest revSignRequest)
        {
            try
            {
                Document document = new RevSignDAL(revSignRequest.TenantId).SaveRevSignoffRequest(revSignRequest.Document, revSignRequest.EventId);
                return new RevSignResponse
                {
                    Document = document,
                    Message = "Document object has been saved successfully.",
                    Status = document.Id > 0 ? true : false
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.RevSign, "SaveRevSignoffRequest", revSignRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.RevSign, "SaveRevSignoffRequest", revSignRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        //SaveRevSignoffRequest

        #region E-sign methods
        public DocumentResult GetESignedDocuments(RevSignRequest revSignRequest, DocumentInfo inputData)
        {
            try
            {
                DocumentResult result = new RevSignDAL(revSignRequest.TenantId).GetESignedDocuments(inputData);

                return result;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.RevSign, "SaveRevSignoffRequest", revSignRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.RevSign, "SaveRevSignoffRequest", revSignRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public DocumentResult SaveESignDocumentAgreedAsync(RevSignRequest revSignRequest, DocumentInfo inputData)
        {
            try
            {
                DocumentResult result = new RevSignDAL(revSignRequest.TenantId).SaveESignDocumentAgreedAsync(inputData);

                return result;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.RevSign, "SaveRevSignoffRequest", revSignRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.RevSign, "SaveRevSignoffRequest", revSignRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public DocumentResult SaveESignedDocumentAsync(RevSignRequest revSignRequest, DocumentInfo inputData)
        {
            try
            {
                DocumentResult result = new RevSignDAL(revSignRequest.TenantId).SaveESignedDocumentAsync(inputData);

                return result;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.RevSign, "SaveRevSignoffRequest", revSignRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.RevSign, "SaveRevSignoffRequest", revSignRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public DocumentResult GetSignedDocumentInfoAsync(RevSignRequest revSignRequest, DocumentInfo inputData)
        {
            try
            {
                DocumentResult result = new RevSignDAL(revSignRequest.TenantId).GetSignedDocumentInfoAsync(inputData);

                return result;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.RevSign, "SaveRevSignoffRequest", revSignRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.RevSign, "SaveRevSignoffRequest", revSignRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool AssociateIwbAndRevSignDocument(int tenantId, int iwbDocumentId, int revSignDocumentId)
        {
            try
            {
                return new RevSignDAL(tenantId).AssociateIwbAndRevSignDocument(iwbDocumentId, revSignDocumentId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.RevSign, "AssociateIwbAndRevSignDocument", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.RevSign, "AssociateIwbAndRevSignDocument", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        #endregion
    }
}
