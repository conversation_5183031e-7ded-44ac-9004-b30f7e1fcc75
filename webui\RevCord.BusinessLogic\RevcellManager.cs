﻿using Newtonsoft.Json;
using Plivo;
using Plivo.Exception;
using Plivo.Resource;
using Plivo.Resource.Endpoint;
using Plivo.Resource.RentedNumber;
using RevCord.DataAccess;
using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.Messages;
using RevCord.DataContracts.RevcellEntities;
using RevCord.Util;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using System.Web.Script.Serialization;

namespace RevCord.BusinessLogic
{
    public class RevcellManager
    {
        //private readonly static string _revcellSpace = AppSettingsUtil.GetString("RevcellSpace", "https://revcell.signalwire.com/");
        //private readonly static string _revcellProjectId = SiteConfig.RevcellProjectId;
        //private readonly static string _revcellAuthToken = SiteConfig.RevcellAuthToken;
        //private const string m_SipEndPointsURL = "api/relay/rest/endpoints/sip";
        //private const string m_PhoneNumbersURL = "api/relay/rest/phone_numbers";
        //private const string m_NumberGroup = "api/relay/rest/number_groups";
        //private const string m_NumberGroupMembership = "api/relay/rest/number_groups/6c9175cb-68eb-4dcc-81ea-13067100ab2d/number_group_memberships";    //TODO
        //private static readonly HttpClient client = new HttpClient();

        private readonly static string _plivoAuthId = SiteConfig.PlivoAuthID;
        private readonly static string _plivoAuthToken = SiteConfig.PlivoAuthToken;
        private readonly static string _plivoEndpointAppId = SiteConfig.PlivoEndpointAppId;
        private readonly static string _plivoPhoneNumberAppId = SiteConfig.PlivoPhoneNumberAppId;

        //#region Authorization
        //private string GetAuthorization()
        //{
        //    try
        //    {
        //        string authorization = _revcellProjectId + ":" + _revcellAuthToken;
        //        return authorization;
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //}
        //#endregion

        //#region SIP EndPoint
        //public RevcellResponse GetAllSIPEndPoints()
        //{
        //    try
        //    {
        //        string url = _revcellSpace + m_SipEndPointsURL;
        //        ServicePointManager.Expect100Continue = true;
        //        ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Ssl3;
        //        WebRequest myReq = WebRequest.Create(url);
        //        myReq.Headers.Add("Authorization", "Basic " + Convert.ToBase64String(new ASCIIEncoding().GetBytes(GetAuthorization())));
        //        WebResponse wr = myReq.GetResponse();
        //        Stream receiveStream = wr.GetResponseStream();
        //        StreamReader reader = new StreamReader(receiveStream, Encoding.UTF8);
        //        string content = reader.ReadToEnd();
        //        return new RevcellResponse
        //        {
        //            SIPEndPointRootObject = JsonConvert.DeserializeObject<SIPEndPointRootObject>(content)
        //        };
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //}
        //public RevcellResponse CreateSIPEndPoint(RevcellRequest revcellRequest)
        //{
        //    try
        //    {
        //        ServicePointManager.Expect100Continue = true;
        //        ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Ssl3;

        //        var json = @"{""username"" : """ + revcellRequest.UserName + @""", ""password"" : """ + revcellRequest.Password + @""", ""send_as"" : """ + revcellRequest.SendAs + @"""}";
        //        var url = _revcellSpace + m_SipEndPointsURL;
        //        byte[] byteArray = Encoding.UTF8.GetBytes(json);
        //        WebRequest myReq = WebRequest.Create(url);
        //        myReq.Headers.Add("Authorization", "Basic " + Convert.ToBase64String(new ASCIIEncoding().GetBytes(GetAuthorization())));
        //        myReq.Method = "POST";

        //        myReq.ContentType = "application/json";
        //        myReq.ContentLength = byteArray.Length;
        //        Stream dataStream = myReq.GetRequestStream();
        //        dataStream.Write(byteArray, 0, byteArray.Length);
        //        dataStream.Close();

        //        WebResponse wr = myReq.GetResponse();
        //        Stream receiveStream = wr.GetResponseStream();
        //        StreamReader reader = new StreamReader(receiveStream, Encoding.UTF8);
        //        string content = reader.ReadToEnd();
        //        return new RevcellResponse { SIPEndPoint = JsonConvert.DeserializeObject<SipEndpoint>(content) };
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //}
        //public RevcellResponse UpdatePasswordSIPEndPoint(RevcellRequest revcellRequest)
        //{
        //    try
        //    {
        //        ServicePointManager.Expect100Continue = true;
        //        ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Ssl3;

        //        var json = @"{""password"" : """ + revcellRequest.Password + @"""}";
        //        var url = _revcellSpace + m_SipEndPointsURL + "/" + revcellRequest.SIPId;
        //        byte[] byteArray = Encoding.UTF8.GetBytes(json);
        //        WebRequest myReq = WebRequest.Create(url);
        //        myReq.Headers.Add("Authorization", "Basic " + Convert.ToBase64String(new ASCIIEncoding().GetBytes(GetAuthorization())));
        //        myReq.Method = "PUT";

        //        myReq.ContentType = "application/json";
        //        myReq.ContentLength = byteArray.Length;
        //        Stream dataStream = myReq.GetRequestStream();
        //        dataStream.Write(byteArray, 0, byteArray.Length);
        //        dataStream.Close();

        //        WebResponse wr = myReq.GetResponse();
        //        Stream receiveStream = wr.GetResponseStream();
        //        StreamReader reader = new StreamReader(receiveStream, Encoding.UTF8);
        //        string content = reader.ReadToEnd();
        //        return new RevcellResponse { SIPEndPoint = JsonConvert.DeserializeObject<SipEndpoint>(content) };

        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //}
        //public async Task<RevcellResponse> UpdateSendAsSIPEndPointAsync(RevcellRequest revcellRequest)
        //{
        //    try
        //    {
        //        ServicePointManager.Expect100Continue = true;
        //        ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Ssl3;
        //        var json = @"{""send_as"" : """ + revcellRequest.SendAs + @"""}";
        //        var data = new StringContent(json, Encoding.UTF8, "application/json");
        //        var base64String = Convert.ToBase64String(new ASCIIEncoding().GetBytes(GetAuthorization()));
        //        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", base64String);
        //        var url = _revcellSpace + m_SipEndPointsURL + "/" + revcellRequest.SIPId;
        //        var req = new HttpRequestMessage(HttpMethod.Put, url);
        //        req.Content = data;
        //        var response = await client.SendAsync(req);
        //        string result = response.Content.ReadAsStringAsync().Result;
        //        return new RevcellResponse { SIPEndPoint = JsonConvert.DeserializeObject<SipEndpoint>(result) };
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //}
        //public RevcellResponse DeleteSIPEndPoint(RevcellRequest revcellRequest)
        //{
        //    try
        //    {
        //        ServicePointManager.Expect100Continue = true;
        //        ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Ssl3;
        //        var url = _revcellSpace + m_SipEndPointsURL + "/" + revcellRequest.SIPId;
        //        WebRequest myReq = WebRequest.Create(url);
        //        myReq.Headers.Add("Authorization", "Basic " + Convert.ToBase64String(new ASCIIEncoding().GetBytes(GetAuthorization())));
        //        myReq.Method = "DELETE";
        //        WebResponse wr = myReq.GetResponse();
        //        Stream receiveStream = wr.GetResponseStream();
        //        StreamReader reader = new StreamReader(receiveStream, Encoding.UTF8);
        //        string content = reader.ReadToEnd();
        //        RevcellResponse revcellResponse = new RevcellResponse { SIPEndPointRootObject = JsonConvert.DeserializeObject<SIPEndPointRootObject>(content) };
        //        RevcellResponse dbResponse = new RevcellResponse { IsOperationSucceeded = new RevcellDAL(revcellRequest.TenantId).DeleteRevcellInfo(revcellRequest.SIPId) };
        //        if (dbResponse.IsOperationSucceeded)
        //        {
        //        }
        //        return revcellResponse;
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //}
        //public RevcellResponse GetSIPEndpointByEmail(RevcellRequest revcellRequest)
        //{
        //    try
        //    {
        //        string parameter = "filter_username=" + revcellRequest.UserName;
        //        string url = _revcellSpace + m_SipEndPointsURL + "?" + parameter;
        //        ServicePointManager.Expect100Continue = true;
        //        ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Ssl3;
        //        WebRequest myReq = WebRequest.Create(url);
        //        myReq.Headers.Add("Authorization", "Basic " + Convert.ToBase64String(new ASCIIEncoding().GetBytes(GetAuthorization())));
        //        myReq.Method = "GET";

        //        WebResponse wr = myReq.GetResponse();
        //        Stream receiveStream = wr.GetResponseStream();
        //        StreamReader reader = new StreamReader(receiveStream, Encoding.UTF8);
        //        string content = reader.ReadToEnd();
        //        return new RevcellResponse
        //        {
        //            SIPEndPointRootObject = JsonConvert.DeserializeObject<SIPEndPointRootObject>(content)
        //        };
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //}
        //#endregion

        //#region Group Number
        //public RevcellResponse GetAllNumberGroups()
        //{
        //    try
        //    {
        //        string url = _revcellSpace + m_NumberGroup;
        //        ServicePointManager.Expect100Continue = true;
        //        ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Ssl3;
        //        WebRequest myReq = WebRequest.Create(url);
        //        myReq.Headers.Add("Authorization", "Basic " + Convert.ToBase64String(new ASCIIEncoding().GetBytes(GetAuthorization())));
        //        WebResponse wr = myReq.GetResponse();
        //        Stream receiveStream = wr.GetResponseStream();
        //        StreamReader reader = new StreamReader(receiveStream, Encoding.UTF8);
        //        string content = reader.ReadToEnd();
        //        return new RevcellResponse
        //        {
        //            NumberGroupRootObject = JsonConvert.DeserializeObject<NumberGroupRootObject>(content)
        //        };
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //}

        //private string GetNumberGroupIdBySystemSerial(string systemSerialNumber)
        //{
        //    try
        //    {
        //        string url = _revcellSpace + m_NumberGroup;
        //        ServicePointManager.Expect100Continue = true;
        //        ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Ssl3;
        //        WebRequest myReq = WebRequest.Create(url);
        //        myReq.Headers.Add("Authorization", "Basic " + Convert.ToBase64String(new ASCIIEncoding().GetBytes(GetAuthorization())));
        //        WebResponse wr = myReq.GetResponse();
        //        Stream receiveStream = wr.GetResponseStream();
        //        StreamReader reader = new StreamReader(receiveStream, Encoding.UTF8);
        //        string content = reader.ReadToEnd();
        //        NumberGroupRootObject numberGroupRootObject = JsonConvert.DeserializeObject<NumberGroupRootObject>(content);
        //        NumberGroup numberGroup = numberGroupRootObject.data.Where(g => g.Name.Contains(systemSerialNumber + "_")).FirstOrDefault();
        //        if (numberGroup == null)
        //        {
        //            return "";
        //        }
        //        return numberGroup.Id;

        //        //return numberGroupRootObject.data.FirstOrDefault().Id ?? string.Empty;
        //    }
        //    catch (Exception ex)
        //    {
        //        return string.Empty;
        //    }
        //}
        //#endregion

        //#region Number Group Memberships
        //public RevcellResponse GetNumberGroupMemberships(RevcellRequest revcellRequest)
        //{
        //    try
        //    {
        //        string numberGroupId = GetNumberGroupIdBySystemSerial(revcellRequest.SystemSerialNumber);
        //        if (!string.IsNullOrEmpty(numberGroupId))
        //        {
        //            string numberGroupURL = String.Format("api/relay/rest/number_groups/{0}/number_group_memberships", numberGroupId);
        //            string url = _revcellSpace + numberGroupURL;
        //            ServicePointManager.Expect100Continue = true;
        //            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Ssl3;
        //            WebRequest myReq = WebRequest.Create(url);
        //            myReq.Headers.Add("Authorization", "Basic " + Convert.ToBase64String(new ASCIIEncoding().GetBytes(GetAuthorization())));
        //            WebResponse wr = myReq.GetResponse();
        //            Stream receiveStream = wr.GetResponseStream();
        //            StreamReader reader = new StreamReader(receiveStream, Encoding.UTF8);
        //            string content = reader.ReadToEnd();
        //            return new RevcellResponse
        //            {
        //                NumberGroupMembershipRootObject = JsonConvert.DeserializeObject<NumberGroupMembershipRootObject>(content)
        //            };
        //        }
        //        else
        //        {
        //            return new RevcellResponse
        //            {
        //                NumberGroupMembershipRootObject = new NumberGroupMembershipRootObject(),
        //            };
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //}
        //#endregion

        //#region PhoneNumber
        //public RevcellResponse GetAllPhoneNumbers()
        //{
        //    try
        //    {
        //        string url = _revcellSpace + m_PhoneNumbersURL;
        //        ServicePointManager.Expect100Continue = true;
        //        ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Ssl3;
        //        WebRequest myReq = WebRequest.Create(url);
        //        myReq.Headers.Add("Authorization", "Basic " + Convert.ToBase64String(new ASCIIEncoding().GetBytes(GetAuthorization())));
        //        WebResponse wr = myReq.GetResponse();
        //        Stream receiveStream = wr.GetResponseStream();
        //        StreamReader reader = new StreamReader(receiveStream, Encoding.UTF8);
        //        string content = reader.ReadToEnd();
        //        return new RevcellResponse
        //        {
        //            PhoneNumberRootObject = JsonConvert.DeserializeObject<PhoneNumberRootObject>(content)
        //        };
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //}
        ////public async Task<RevcellResponse> UpdatePhoneNameAsync(RevcellRequest revcellRequest)
        //public RevcellResponse UpdatePhoneName(RevcellRequest revcellRequest)
        //{
        //    try
        //    {
        //        ServicePointManager.Expect100Continue = true;
        //        ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Ssl3;

        //        var json = @"{""name"" : """ + revcellRequest.PhoneName + @""", ""call_handler"" : ""relay_sip_endpoint"", ""call_sip_endpoint_id"" : """ + revcellRequest.SIPId + @"""}";
        //        var url = _revcellSpace + m_PhoneNumbersURL + "/" + revcellRequest.PhoneNumberId;
        //        byte[] byteArray = Encoding.UTF8.GetBytes(json);
        //        WebRequest myReq = WebRequest.Create(url);
        //        myReq.Headers.Add("Authorization", "Basic " + Convert.ToBase64String(new ASCIIEncoding().GetBytes(GetAuthorization())));
        //        myReq.Method = "PUT";

        //        myReq.ContentType = "application/json";
        //        myReq.ContentLength = byteArray.Length;
        //        Stream dataStream = myReq.GetRequestStream();
        //        dataStream.Write(byteArray, 0, byteArray.Length);
        //        dataStream.Close();

        //        WebResponse wr = myReq.GetResponse();
        //        Stream receiveStream = wr.GetResponseStream();
        //        StreamReader reader = new StreamReader(receiveStream, Encoding.UTF8);
        //        string content = reader.ReadToEnd();
        //        RevcellResponse revcellResponse = new RevcellResponse { PhoneNumber = JsonConvert.DeserializeObject<PhoneNumber>(content) };
        //        revcellRequest.PhoneNumberObj = revcellResponse.PhoneNumber;

        //        RevcellResponse dbResponse = new RevcellResponse { IsOperationSucceeded = new RevcellDAL(revcellRequest.TenantId).AddRevcellInfo(revcellRequest.UserNum, revcellResponse.PhoneNumber.Id, revcellResponse.PhoneNumber.Number, revcellResponse.PhoneNumber.Name, revcellRequest.SIPId, revcellResponse.PhoneNumber.Name) };
        //        if (dbResponse.IsOperationSucceeded) {
        //        }
        //        return revcellResponse;
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //}
        //public RevcellResponse ReleasePhoneNumber(RevcellRequest revcellRequest)
        //{
        //    try
        //    {
        //        ServicePointManager.Expect100Continue = true;
        //        ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Ssl3;

        //        var json = @"{""name"" : """ + revcellRequest.PhoneName + @""", ""call_handler"" : ""laml_webhooks""}";
        //        var url = _revcellSpace + m_PhoneNumbersURL + "/" + revcellRequest.PhoneNumberId;
        //        byte[] byteArray = Encoding.UTF8.GetBytes(json);
        //        WebRequest myReq = WebRequest.Create(url);
        //        myReq.Headers.Add("Authorization", "Basic " + Convert.ToBase64String(new ASCIIEncoding().GetBytes(GetAuthorization())));
        //        myReq.Method = "PUT";

        //        myReq.ContentType = "application/json";
        //        myReq.ContentLength = byteArray.Length;
        //        Stream dataStream = myReq.GetRequestStream();
        //        dataStream.Write(byteArray, 0, byteArray.Length);
        //        dataStream.Close();

        //        WebResponse wr = myReq.GetResponse();
        //        Stream receiveStream = wr.GetResponseStream();
        //        StreamReader reader = new StreamReader(receiveStream, Encoding.UTF8);
        //        string content = reader.ReadToEnd();
        //        return new RevcellResponse { PhoneNumber = JsonConvert.DeserializeObject<PhoneNumber>(content) };
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //}
        //#endregion

        //public RevcellResponse AddRevcellInfo(RevcellRequest revcellRequest)
        //{
        //    try
        //    {
        //        return new RevcellResponse { IsOperationSucceeded = new RevcellDAL(revcellRequest.TenantId).AddRevcellInfo(revcellRequest.UserNum, revcellRequest.PhoneNumberId, revcellRequest.PhoneNumber, revcellRequest.PhoneName, revcellRequest.SIPId, revcellRequest.SIPName) };
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //}

        //public RevcellResponse DeleteRevcellInfo(RevcellRequest revcellRequest)
        //{
        //    try
        //    {
        //        return new RevcellResponse { IsOperationSucceeded = new RevcellDAL(revcellRequest.TenantId).DeleteRevcellInfo(revcellRequest.SIPId) };
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //}

        //public RevcellResponse GetRevcellInfo(RevcellRequest revcellRequest)
        //{
        //    try
        //    {
        //        return new RevcellResponse { RevcellInfo = new RevcellDAL(revcellRequest.TenantId).GetRevcellInfoByUserNum(revcellRequest.UserNum) };
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //}

        #region Plivo
        public void PlivoGetAccount(int tenantId) {
            var plivoAPI = new PlivoApi(_plivoAuthId, _plivoAuthToken);
            try
            {
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3 | SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;
                var account = plivoAPI.Account.Get();
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Revcell, "PlivoGetAccount", tenantId, new JavaScriptSerializer().Serialize(account)));
            }
            catch (PlivoRestException pex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Revcell, "PlivoGetAccount", tenantId, " PlivoRestException : " + pex.Message, pex.StackTrace, Convert.ToInt32(pex.StatusCode), string.Empty, 0));
                throw pex;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Revcell, "PlivoGetAccount", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Revcell, "PlivoGetAccount", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public RevcellResponse CreatePlivoSIPEndPoint(RevcellRequest revcellRequest) {
            try
            {
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3 | SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;
                var plivoAPI = new PlivoApi(_plivoAuthId, _plivoAuthToken);
                EndpointCreateResponse epCreateResponse = plivoAPI.Endpoint.Create(
                    username: revcellRequest.UserName,
                    alias: revcellRequest.Alias,
                    password: revcellRequest.Password,
                    appId: revcellRequest.PlivoConfig.PlivoEndpointAppId
                );
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Revcell, "CreatePlivoSIPEndPoint", revcellRequest.TenantId, "SIP Endpoint created successfully." + new JavaScriptSerializer().Serialize(epCreateResponse)));
                return new RevcellResponse { EndpointCreateResponse = epCreateResponse };
            }
            catch (PlivoRestException pex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Revcell, "CreatePlivoSIPEndPoint", revcellRequest.TenantId, " PlivoRestException : " + pex.Message, pex.StackTrace, Convert.ToInt32(pex.StatusCode), string.Empty, 0));
                throw pex;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Revcell, "CreatePlivoSIPEndPoint", revcellRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Revcell, "CreatePlivoSIPEndPoint", revcellRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        //public RevcellResponse UpdatePlivoSIPEndPointPassword(RevcellRequest revcellRequest)
        //{
        //    try
        //    {
        //        var plivoAPI = new PlivoApi(_plivoAuthId, _plivoAuthToken);
        //        Plivo.Resource.UpdateResponse<Plivo.Resource.Endpoint.Endpoint> epUpdateResponse = plivoAPI.Endpoint.Update(
        //            revcellRequest.EndpointId,
        //            revcellRequest.Password
        //        );
        //        return new RevcellResponse { EndpointUpdateResponse = epUpdateResponse };
        //    }
        //    catch (PlivoRestException ex)
        //    {
        //        throw ex;
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //}
        public RevcellResponse DeletePlivoSIPEndPoint(RevcellRequest revcellRequest) {
            try
            {
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3 | SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;
                var plivoAPI = new PlivoApi(_plivoAuthId, _plivoAuthToken);
                DeleteResponse<Endpoint> epDeleteResponse = plivoAPI.Endpoint.Delete(revcellRequest.EndpointId);
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Revcell, "DeletePlivoSIPEndPoint", revcellRequest.TenantId, "DeletePlivoSIPEndPoint executed successfully.epUpdateResponse = " + new JavaScriptSerializer().Serialize(epDeleteResponse)));
                return new RevcellResponse { EndpointDeleteResponse = epDeleteResponse };
            }
            catch (PlivoRestException pex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Revcell, "DeletePlivoSIPEndPoint", revcellRequest.TenantId, " PlivoRestException : " + pex.Message, pex.StackTrace, Convert.ToInt32(pex.StatusCode), string.Empty, 0));
                throw pex;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Revcell, "DeletePlivoSIPEndPoint", revcellRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Revcell, "DeletePlivoSIPEndPoint", revcellRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        //public RevcellResponse GetPlivoSIPEndpoints(RevcellRequest revcellRequest)
        //{
        //    try
        //    {
        //        var plivoAPI = new PlivoApi(_plivoAuthId, _plivoAuthToken);
        //        ListResponse<Endpoint> response = new ListResponse<Endpoint>();
        //        ListResponse<Endpoint> epListResponse = plivoAPI.Endpoint.List(limit: 20, offset: 0);
        //        return new RevcellResponse { EndpointListResponse = epListResponse };
        //    }
        //    catch (PlivoRestException ex)
        //    {
        //        throw ex;
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //}

        public RevcellResponse IsSIPEndpointByEmailExists(RevcellRequest revcellRequest)
        {
            try
            {
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3 | SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;
                var plivoAPI = new PlivoApi(_plivoAuthId, _plivoAuthToken);
                ListResponse<Endpoint> epListResponse = plivoAPI.Endpoint.List(limit: 20, offset: 0);
                var bSIPByEmailExists = epListResponse.Where(c => c.Alias == revcellRequest.UserEmail).Count()>0;
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Revcell, "IsSIPEndpointByEmailExists", revcellRequest.TenantId, "IsSIPEndpointByEmailExists executed successfully. bSIPByEmailExists = " + bSIPByEmailExists));
                return new RevcellResponse { IsSIPEndpointByEmailExists = bSIPByEmailExists };
            }
            catch (PlivoRestException pex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Revcell, "IsSIPEndpointByEmailExists", revcellRequest.TenantId, " PlivoRestException : " + pex.Message, pex.StackTrace, Convert.ToInt32(pex.StatusCode), string.Empty, 0));
                throw pex;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Revcell, "IsSIPEndpointByEmailExists", revcellRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Revcell, "IsSIPEndpointByEmailExists", revcellRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public RevcellResponse CheckAndDeleteEndpoint(RevcellRequest revcellRequest)
        {
            try
            {
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3 | SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;
                var plivoAPI = new PlivoApi(_plivoAuthId, _plivoAuthToken);
                //ListResponse<Endpoint> epListResponse = plivoAPI.Endpoint.List(limit: 20, offset: 0);
                //var bSIPByEmailExists = epListResponse.Where(c => c.Alias == revcellRequest.UserEmail).Count() > 0;
                //if (bSIPByEmailExists) {
                    PlivoInfo plivoInfo = new RevcellDAL(revcellRequest.TenantId).GetPlivoInfoByUserNum(revcellRequest.UserNum);
                    
                    revcellRequest.EndpointId = plivoInfo.EndpointId;
                    revcellRequest.PhoneNumberId = plivoInfo.PhoneNumberId;
                    // Delete the SIP Endpoint
                    DeletePlivoSIPEndPoint(revcellRequest);
                    // Remove respective alias from PhoneNumber
                    UpdateResponse<RentedNumber> rnUpdateResponse = plivoAPI.Number.Update(plivoInfo.PhoneNumber, appId: _plivoPhoneNumberAppId, alias: revcellRequest.SystemSerialNumber.Trim());
                    // Set IsDelete = 1
                    ;
                //}
                return new RevcellResponse { IsOperationSucceeded = new RevcellDAL(revcellRequest.TenantId).DeletePlivoInfo(revcellRequest.EndpointId) };
            }
            catch (PlivoRestException pex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Revcell, "CheckAndDeleteEndpoint", revcellRequest.TenantId, " PlivoRestException : " + pex.Message, pex.StackTrace, Convert.ToInt32(pex.StatusCode), string.Empty, 0));
                throw pex;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Revcell, "CheckAndDeleteEndpoint", revcellRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Revcell, "CheckAndDeleteEndpoint", revcellRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public RevcellResponse CheckAndUpdateEndpointPassword(RevcellRequest revcellRequest)
        {
            try
            {
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3 | SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;
                var plivoAPI = new PlivoApi(_plivoAuthId, _plivoAuthToken);
                PlivoInfo plivoInfo = null;
                if (revcellRequest.UserNum > 0)
                {
                    plivoInfo = new RevcellDAL(revcellRequest.TenantId).GetPlivoInfoByUserNum(revcellRequest.UserNum);
                    Task.Run(() => RevAuditLogger.WriteInformation(Originator.Revcell, "CheckAndUpdateEndpointPassword", revcellRequest.TenantId, "CheckAndUpdateEndpointPassword : Password Change with UserNum = " + revcellRequest.UserNum + " scenarion, PlivoInfo = " + new JavaScriptSerializer().Serialize(plivoInfo)));
                }
                else if (!string.IsNullOrEmpty(revcellRequest.UserEmail))
                {
                    plivoInfo = new RevcellDAL(revcellRequest.TenantId).GetPlivoInfoByUserEmail(revcellRequest.UserEmail);
                    Task.Run(() => RevAuditLogger.WriteInformation(Originator.Revcell, "CheckAndUpdateEndpointPassword", revcellRequest.TenantId, "CheckAndUpdateEndpointPassword : Force Password Change, PlivoInfo = " + new JavaScriptSerializer().Serialize(plivoInfo)));
                }

                UpdateResponse<Endpoint> epUpdateResponse = plivoAPI.Endpoint.Update(plivoInfo.EndpointId, password: revcellRequest.Password);
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Revcell, "CheckAndUpdateEndpointPassword", revcellRequest.TenantId, "CheckAndUpdateEndpointPassword executed successfully. epUpdateResponse = " + new JavaScriptSerializer().Serialize(epUpdateResponse)));
                return new RevcellResponse { EndpointUpdateResponse = epUpdateResponse, IsOperationSucceeded = true };
            }
            catch (PlivoRestException pex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Revcell, "CheckAndUpdateEndpointPassword", revcellRequest.TenantId, " PlivoRestException : " + pex.Message, pex.StackTrace, Convert.ToInt32(pex.StatusCode), string.Empty, 0));
                throw pex;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Revcell, "CheckAndUpdateEndpointPassword", revcellRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Revcell, "CheckAndUpdateEndpointPassword", revcellRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #region Phone Number
        public RevcellResponse UnrentPhoneNumber(RevcellRequest revcellRequest)
        {
            var plivoAPI = new PlivoApi(_plivoAuthId, _plivoAuthToken);
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3 | SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;

            try
            {
                DeleteResponse<RentedNumber> rnDeleteResponse = plivoAPI.Number.Delete(
                    number: revcellRequest.PhoneNumber
                );
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Revcell, "UnrentPhoneNumber", revcellRequest.TenantId, "UnrentPhoneNumber executed successfully. epUpdateResponse = " + new JavaScriptSerializer().Serialize(rnDeleteResponse)));
                return new RevcellResponse { RentedNumberDeleteResponse = rnDeleteResponse };
            }
            catch (PlivoRestException pex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Revcell, "UnrentPhoneNumber", revcellRequest.TenantId, " PlivoRestException : " + pex.Message, pex.StackTrace, Convert.ToInt32(pex.StatusCode), string.Empty, 0));
                throw pex;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Revcell, "UnrentPhoneNumber", revcellRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Revcell, "UnrentPhoneNumber", revcellRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public RevcellResponse GetAllPhoneNumbers(RevcellRequest revcellRequest) {
            try
            {
                var plivoAPI = new PlivoApi(_plivoAuthId, _plivoAuthToken);
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3 | SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;
                ListResponse<RentedNumber> rnListResponse = plivoAPI.Number.List(alias: revcellRequest.SystemSerialNumber.Trim());
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Revcell, "GetAllPhoneNumbers", revcellRequest.TenantId, "GetAllPhoneNumbers executed successfully. rnListResponse = " + new JavaScriptSerializer().Serialize(rnListResponse)));
                return new RevcellResponse { RentedNumberListResponse = rnListResponse };
            }
            catch (PlivoRestException pex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Revcell, "GetAllPhoneNumbers", revcellRequest.TenantId, " PlivoRestException : " + pex.Message, pex.StackTrace, Convert.ToInt32(pex.StatusCode), string.Empty, 0));
                throw pex;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Revcell, "GetAllPhoneNumbers", revcellRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Revcell, "GetAllPhoneNumbers", revcellRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public string GetAssignedPhoneNumberByAlias(string alias, int tenantId)
        {
            try
            {
                var plivoAPI = new PlivoApi(_plivoAuthId, _plivoAuthToken);
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3 | SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;

                ListResponse<RentedNumber> rnListResponse = plivoAPI.Number.List(alias: alias.Trim());
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Revcell, "GetAllPhoneNumbers", tenantId, "GetAssignedPhoneNumberByAlias executed successfully. rnListResponse = " + new JavaScriptSerializer().Serialize(rnListResponse)));
                return rnListResponse.FirstOrDefault().Number;
            }
            catch (PlivoRestException pex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Revcell, "GetAssignedPhoneNumberByAlias", tenantId, " PlivoRestException : " + pex.Message, pex.StackTrace, Convert.ToInt32(pex.StatusCode), string.Empty, 0));
                throw pex;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Revcell, "GetAssignedPhoneNumberByAlias", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Revcell, "GetAssignedPhoneNumberByAlias", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public RevcellResponse UpdatePlivoPhoneAndAddPlivoInfo(RevcellRequest revcellRequest) {
            try
            {
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3 | SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;
                var plivoAPI = new PlivoApi(_plivoAuthId, _plivoAuthToken);
                UpdateResponse<RentedNumber> rnUpdateResponse = plivoAPI.Number.Update(revcellRequest.PhoneNumber, appId: _plivoPhoneNumberAppId, alias: revcellRequest.Alias);
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Revcell, "UpdatePlivoPhoneAndAddPlivoInfo", revcellRequest.TenantId, "UpdatePlivoPhoneAndAddPlivoInfo executed successfully. rnUpdateResponse = " + new JavaScriptSerializer().Serialize(rnUpdateResponse)));
                return new RevcellResponse { RentedNumberUpdateResponse = rnUpdateResponse, IsOperationSucceeded = new RevcellDAL(revcellRequest.TenantId).AddPlivoInfo(revcellRequest.UserNum, revcellRequest.PhoneNumberId, revcellRequest.PhoneNumber, revcellRequest.EndpointId, revcellRequest.EndpointUserName, revcellRequest.Alias) };
            }
            catch (PlivoRestException pex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Revcell, "UpdatePlivoPhoneAndAddPlivoInfo", revcellRequest.TenantId, " PlivoRestException : " + pex.Message, pex.StackTrace, Convert.ToInt32(pex.StatusCode), string.Empty, 0));
                throw pex;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Revcell, "UpdatePlivoPhoneAndAddPlivoInfo", revcellRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Revcell, "UpdatePlivoPhoneAndAddPlivoInfo", revcellRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public RevcellResponse SearchAndBuyPhoneNumber(RevcellRequest revcellRequest) {
            try
            {
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3 | SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;
                var plivoAPI = new PlivoApi(_plivoAuthId, _plivoAuthToken);
                //ListResponse<Plivo.Resource.PhoneNumber.PhoneNumber> phoneNumbers = plivoAPI.PhoneNumber.List(countryIso: "US", type: "local", pattern: "713");
                ListResponse<Plivo.Resource.PhoneNumber.PhoneNumber> phoneNumbers = plivoAPI.PhoneNumber.List(countryIso: "US");
                //if (phoneNumbers == null)
                //    phoneNumbers = plivoAPI.PhoneNumber.List(countryIso: "US", type: "local");
                //if (phoneNumbers == null)
                //    phoneNumbers = plivoAPI.PhoneNumber.List(countryIso: "US");
                var searchedPhoneNumber = phoneNumbers.FirstOrDefault();
                var phoneNumberBuyResponse = searchedPhoneNumber.Buy(appId: _plivoPhoneNumberAppId);
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Revcell, "SearchAndBuyPhoneNumber", revcellRequest.TenantId, "SearchAndBuyPhoneNumber executed successfully. phoneNumberBuyResponse = " + new JavaScriptSerializer().Serialize(phoneNumberBuyResponse)));
                return new RevcellResponse { PhoneNumberBuyResponse = phoneNumberBuyResponse };
            }
            catch (PlivoRestException pex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Revcell, "SearchAndBuyPhoneNumber", revcellRequest.TenantId, " PlivoRestException : " + pex.Message, pex.StackTrace, Convert.ToInt32(pex.StatusCode), string.Empty, 0));
                throw pex;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Revcell, "SearchAndBuyPhoneNumber", revcellRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Revcell, "SearchAndBuyPhoneNumber", revcellRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        #endregion

        #endregion
    }
}