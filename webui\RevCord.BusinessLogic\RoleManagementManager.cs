﻿using RevCord.DataAccess;
using RevCord.DataContracts.Messages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.BusinessLogic
{
    public class RoleManagementManager
    {
        public RoleManagementResponse AddRole(RoleManagementRequest rmRequest)
        {
            int roleId = new RoleManagementDAL(rmRequest.TenantId).AddRole(rmRequest.Role.RoleType, rmRequest.Role.Name, rmRequest.Role.Description, rmRequest.Role.IsSystemRole);
            rmRequest.Role.Id = roleId;
            return new RoleManagementResponse {Role = rmRequest.Role };
        }


        public RoleManagementResponse GetAllRoles(RoleManagementRequest rmRequest)
        {
            return new RoleManagementResponse { Roles = new RoleManagementDAL(rmRequest.TenantId).GetAllRoles() };
        }

        public RoleManagementResponse GetRoleById(RoleManagementRequest rmRequest)
        {
            return new RoleManagementResponse { Role = new RoleManagementDAL(rmRequest.TenantId).GetRoleById(rmRequest.Role.Id) };
        }

        public RoleManagementResponse UpdateRole(RoleManagementRequest rmRequest)
        {
            return new RoleManagementResponse { DBResponse = new RoleManagementDAL(rmRequest.TenantId).UpdateRole(rmRequest.Role ) };
        }

        public RoleManagementResponse DeleteRole(RoleManagementRequest rmRequest)
        {
            return new RoleManagementResponse { DBResponse = new RoleManagementDAL(rmRequest.TenantId).DeleteRole(rmRequest.Role.Id) };
        }

        public RoleManagementResponse GetRolePermissions(RoleManagementRequest rmRequest)
        {
            return new RoleManagementResponse { Permissions = new RoleManagementDAL(rmRequest.TenantId).GetRolePermissions(rmRequest.Role.Id) };
        }

        public RoleManagementResponse UpdateRolePermissions(RoleManagementRequest rmRequest)
        {
            var response = false;
            foreach (var permission in rmRequest.RolePermissions)
            {
                response = new RoleManagementDAL(rmRequest.TenantId).UpdateRolePermissions(permission.RoleId, permission.PermissionId, permission.IsDeleted);
            }
            return new RoleManagementResponse { DBResponse = response, RolePermissions = rmRequest.RolePermissions };
        }

        public RoleManagementResponse GetChannelBasedRolePermissions(RoleManagementRequest rmRequest)
        {
            return new RoleManagementResponse {
                Permissions = new RoleManagementDAL(rmRequest.TenantId).GetRolePermissions(rmRequest.Role.Id),
                RoleChannelPermissions = new RoleManagementDAL(rmRequest.TenantId).GetChannelBasedRolePermissions(rmRequest.Role.Id)
            };
        }

        public RoleManagementResponse UpdateChannelBasedRolePermissions(RoleManagementRequest rmRequest)
        {
            return new RoleManagementResponse
            {
                DBResponse = new RoleManagementDAL(rmRequest.TenantId).UpdateChannelBasedRolePermissions(rmRequest.RoleChannelPermission.RoleId, rmRequest.RoleChannelPermission.PermissionId, rmRequest.RoleChannelPermission.ChannelesToAllocate, rmRequest.RoleChannelPermission.ChannelesToDeAllocate)
            };
        }

        public RoleManagementResponse GetUsersByRoleId(int roleId, int tenantId)
        {
            return new RoleManagementResponse { Users = new RoleManagementDAL(tenantId).GetUsersByRoleId(roleId) };
        }

        //public RoleManagementResponse GetRoleChannelPermissions(RoleManagementRequest rmRequest)
        //{
        //    return new RoleManagementResponse { Permissions = new RoleManagementDAL(rmRequest.TenantId).GetRoleChannelPermissions(rmRequest.Role.Id) };
        //}

        //public RoleManagementResponse UpdateRoleChennelPermissions(RoleManagementRequest rmRequest)
        //{
        //    var response = false;
        //    foreach (var permission in rmRequest.RoleChannelPermissions)
        //    {
        //        response = new RoleManagementDAL(rmRequest.TenantId).UpdateRoleChennelPermissions(permission.RoleId, permission.PermissionId,permission.ChannelNum, permission.IsDeleted);
        //    }
        //    return new RoleManagementResponse { DBResponse = response, RoleChannelPermissions = rmRequest.RoleChannelPermissions };
        //}
    }
}
