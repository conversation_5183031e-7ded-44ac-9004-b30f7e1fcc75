﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RevCord.DataAccess;
using RevCord.DataContracts.Criteria;
using RevCord.DataContracts.MessageBase;
using RevCord.DataContracts.Messages;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.DataContracts.DTO;
using RevCord.DataContracts;
using RevCord.Util;
using System.Data.SqlClient;
using RevCord.DataAccess.Util;
using RevCord.DataContracts.ViewModelEntities;

namespace RevCord.BusinessLogic
{
    public class SearchMediaManager
    {
        private readonly static string _serverIP = SiteConfig.HelperServiceAddress;
        public async Task<DALMediaResponse> GetDefaultSearchResults(VRRequest request)
        {
            string durationStr = QueryConstants.DURATION_DEFAULT;
            string queryOptionSTR = string.Empty;
            string searchOperatorSpParam = string.Empty;
            try
            {
                var criteria = request.Criteria as CallCriteria;
                int i = request.UserId;
                StringBuilder sbWhereClause = new StringBuilder();//TODO:Remove this later
                if (criteria != null)
                {
                    #region ------- Duration String -------
                    if (criteria.StartDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null) || criteria.EndDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null))
                    {
                        durationStr = QueryConstants.DURATION_BETWEEN + criteria.StartDuration.TotalMilliseconds.ToString() + "' AND '" + criteria.EndDuration.TotalMilliseconds.ToString() + "'";
                    }

                    #endregion

                    #region ------- Search Restriction -------

                    if (criteria.RestrictionInHours > 0)
                    {
                        var dt = DateTime.Now.AddHours(-criteria.RestrictionInHours);

                        criteria.StartDate = dt;
                        criteria.EndDate = DateTime.Now;
                        criteria.StartTime = dt.TimeOfDay;
                        criteria.EndTime = TimeSpan.Parse(DateTime.Now.ToString("HH:mm:ss"));// TimeSpan.Parse(DateTime.Now.TimeOfDay.ToString("HH:mm:ss"));
                    }

                    #endregion

                    bool secCat = false;

                    if (criteria.CategoryGroupExtensions != null && criteria.CategoryGroupExtensions.Count() > 0)
                    {

                        var audioNscreenCategoryGroupExtensions = criteria.CategoryGroupExtensions.Where(cge => cge.GroupType == GroupType.Audio || cge.GroupType == GroupType.Screens || cge.GroupType == GroupType.Text || cge.GroupType == GroupType.IQ3 || cge.GroupType == GroupType.Teams || cge.GroupType == GroupType.Revcell );
                        if (audioNscreenCategoryGroupExtensions.Count() != 0)
                        {
                            sbWhereClause.Append(" AND ( "); //1
                            sbWhereClause.AppendLine();

                            //foreach (var catGrpExt in criteria.CategoryGroupExtensions)
                            foreach (var catGrpExt in audioNscreenCategoryGroupExtensions)
                            {

                                if (secCat && catGrpExt.GroupExtensions.Count > 0)
                                    sbWhereClause.Append(" OR ( "); //3
                                if (catGrpExt.GroupExtensions.Count > 0)
                                {
                                    sbWhereClause.Append(" ( ");//2

                                    foreach (var grpExt in catGrpExt.GroupExtensions)
                                    {
                                        //sbWhereClause.AppendFormat(@"({0}.GroupNum = {1} or {0}.Ext IN ({2}))", "CI", grpExt.GroupId, grpExt.ExtensionIdsCSV);

                                        sbWhereClause.AppendFormat(@"({0}.Ext IN ({2}))", "CI", grpExt.GroupId, grpExt.ExtensionIdsCSV);
                                        sbWhereClause.Append(" OR ");
                                        sbWhereClause.AppendLine();
                                    }
                                    sbWhereClause.RemoveLast(" OR ");
                                    sbWhereClause.Append(" ) ");//2
                                    sbWhereClause.AppendLine();

                                    sbWhereClause.AppendFormat(" AND CI.CallType = {0} ", (int)catGrpExt.GroupType);//Call Type
                                    sbWhereClause.AppendLine();
                                    //sbWhereClause.Append(" ) ");//2
                                }
                                if (secCat && catGrpExt.GroupExtensions.Count > 0)
                                    sbWhereClause.Append(" ) "); //3
                                if (catGrpExt.GroupExtensions.Count > 0)
                                    secCat = true;
                            }
                            // Added for IQ3

                            if (sbWhereClause.Equals("{ AND (  OR (  )  OR (  ) }"))
                            {
                                sbWhereClause.Replace("{ AND (  OR (  )  OR (  ) }", "");
                                sbWhereClause.Append("AND CI.CallType = 7 ");
                            }
                            else
                            {
                                //  sbWhereClause.Append("or CI.CallType = 7 ");
                            }

                            sbWhereClause.Append(" ) "); //1
                        }
                    }
                    sbWhereClause.Append(" AND CI.[IsShow] = '1' ");
                }

                //queryOptionSTR = " AND CI.[IsShow] = '1' ";
                queryOptionSTR = sbWhereClause.ToString();

                await Task.Run(() => RevAuditLogger.WriteInformation(Originator.SearchMedia, "GetDefaultSearchResults", request.TenantId, " request.PageSize = " + request.PageSize + " request.PageNumber = " + request.PageNumber + " durationStr = " + durationStr + " queryOptionSTR = " + queryOptionSTR + " criteria = " + criteria + " searchOperatorSpParam = " + searchOperatorSpParam));


                return await new SearchMediaDAL(request.TenantId).GetDefaultSearchResultsDTO(request.PageSize, request.PageNumber, durationStr, queryOptionSTR, criteria, searchOperatorSpParam);
            }
            catch (SqlException sqle)
            {
                await Task.Run(() => RevAuditLogger.WriteSQLException(Originator.SearchMedia, "GetDefaultSearchResults", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                await Task.Run(() => RevAuditLogger.WriteException(Originator.SearchMedia, "GetDefaultSearchResults", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public async Task<DALMediaResponse> GetDefaultSearchResultsByEventId(VRRequest request)
        {
            try
            {
                await Task.Run(() => RevAuditLogger.WriteInformation(Originator.SearchMedia, "GetDefaultSearchResultsByEventId", request.TenantId, " request.EventId = " + request.EventId));

                return await new SearchMediaDAL(request.TenantId).GetDefaultSearchResultsByEventIdDTO(request.EventId);
            }
            catch (SqlException sqle)
            {
                await Task.Run(() => RevAuditLogger.WriteSQLException(Originator.SearchMedia, "GetDefaultSearchResultsByEventId", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                await Task.Run(() => RevAuditLogger.WriteException(Originator.SearchMedia, "GetDefaultSearchResultsByEventId", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        public async Task<DALMediaResponse> PerformAdvanceSearch(VRRequest request)
        {
            StringBuilder sbWhereClause = new StringBuilder();//TODO:Remove this later

            string durationStr = QueryConstants.DURATION_DEFAULT;
            string queryOptionSTR = string.Empty;
            string searchOperatorSpParam = string.Empty;

            try
            {
                var criteria = request.Criteria as CallCriteria;
                int i = request.UserId;

                if (criteria != null)
                {
                    #region ------- Duration String -------
                    if (criteria.StartDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null) || criteria.EndDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null))
                    {
                        durationStr = QueryConstants.DURATION_BETWEEN + criteria.StartDuration.TotalMilliseconds.ToString() + "' AND '" + criteria.EndDuration.TotalMilliseconds.ToString() + "'";
                    }

                    #endregion

                    #region ------- Search Restriction -------

                    if (criteria.RestrictionInHours > 0)
                    {
                        var dt = DateTime.Now.AddHours(-criteria.RestrictionInHours);

                        criteria.StartDate = dt;
                        criteria.EndDate = DateTime.Now;
                        criteria.StartTime = dt.TimeOfDay;
                        criteria.EndTime = TimeSpan.Parse(DateTime.Now.ToString("HH:mm:ss"));// TimeSpan.Parse(DateTime.Now.TimeOfDay.ToString("HH:mm:ss"));
                    }

                    #endregion
                }


                if (request.Criteria.IsCustomSearch)
                {
                    queryOptionSTR = CustomSearchQueryBuilder.BuildQuery(request.CustomSearchCriteria.Conditions, request.CustomSearchCriteria.Operator);
                    sbWhereClause.Append(queryOptionSTR);
                    searchOperatorSpParam = request.CustomSearchCriteria.Operator == OperatorExpression.Like ? string.Format("Like '%{0}%'", request.Criteria.CustomText) : string.Format("= '{0}'", request.Criteria.CustomText);
                }

                #region ------- Group Extensions -------

                bool secCat = false;
                if (criteria.CategoryGroupExtensions != null && criteria.CategoryGroupExtensions.Count() > 0)
                {

                    var audioNscreenCategoryGroupExtensions = criteria.CategoryGroupExtensions.Where(cge => cge.GroupType == GroupType.Audio || cge.GroupType == GroupType.Screens || cge.GroupType == GroupType.Text || cge.GroupType == GroupType.IQ3 || cge.GroupType == GroupType.Teams || cge.GroupType == GroupType.Revcell);
                    if (audioNscreenCategoryGroupExtensions.Count() != 0)
                    {
                        sbWhereClause.Append(" AND (  ( "); //4 //1
                        sbWhereClause.AppendLine();

                        //foreach (var catGrpExt in criteria.CategoryGroupExtensions)
                        foreach (var catGrpExt in audioNscreenCategoryGroupExtensions)
                        {

                            if (secCat && catGrpExt.GroupExtensions.Count > 0)
                                sbWhereClause.Append(" OR ( "); //3
                            if (catGrpExt.GroupExtensions.Count > 0)
                            {
                                sbWhereClause.Append(" ( ");//2

                                foreach (var grpExt in catGrpExt.GroupExtensions)
                                {
                                    //sbWhereClause.AppendFormat(@"({0}.GroupNum = {1} or {0}.Ext IN ({2}))", "CI", grpExt.GroupId, grpExt.ExtensionIdsCSV);

                                    sbWhereClause.AppendFormat(@"({0}.Ext IN ({2}))", "CI", grpExt.GroupId, grpExt.ExtensionIdsCSV);
                                    sbWhereClause.Append(" OR ");
                                    sbWhereClause.AppendLine();
                                }
                                sbWhereClause.RemoveLast(" OR ");
                                sbWhereClause.Append(" ) ");//2
                                sbWhereClause.AppendLine();

                                if (catGrpExt.GroupType == GroupType.Revcell)
                                {
                                    //sbWhereClause.AppendFormat(" AND CI.CallType = {0} AND CI.IsRevcell = 1 ", (int)GroupType.Audio); //Revcell Call Type
                                    sbWhereClause.AppendFormat(" AND CI.CallType = {0} ", (int)GroupType.Audio); //Revcell Call Type
                                }
                                else
                                {
                                    sbWhereClause.AppendFormat(" AND CI.CallType = {0} ", (int)catGrpExt.GroupType);//Call Type
                                }
                                sbWhereClause.AppendLine();
                                //sbWhereClause.Append(" ) ");//2
                            }
                            if (secCat && catGrpExt.GroupExtensions.Count > 0)
                                sbWhereClause.Append(" ) "); //3
                            if (catGrpExt.GroupExtensions.Count > 0)
                                secCat = true;
                        }
                        // Added for IQ3

                        if (sbWhereClause.Equals("{ AND (  OR (  )  OR (  ) }"))
                        {
                            sbWhereClause.Replace("{ AND (  OR (  )  OR (  ) }", "");
                            sbWhereClause.Append("AND CI.CallType = 7 ");
                        }
                        else
                        {
                            //  sbWhereClause.Append("or CI.CallType = 7 ");
                        }

                        sbWhereClause.Append(" ) "); //1
                    }
                }

                if (criteria.ArchiveGroup != null && criteria.ArchiveGroup.Length > 0)
                {
                    if (!criteria.ArchiveGroup.Contains(';'))
                    {
                        if(secCat)
                            sbWhereClause.Append(" OR ");
                        else
                            sbWhereClause.Append(" AND ");
                        sbWhereClause.Append(" ( ");
                        //sbWhereClause.Append(" (CI.ExtName LIKE N'%" + criteria.ArchiveGroup + "%')");
                        sbWhereClause.Append(" (CI.ExtName = '" + criteria.ArchiveGroup + "')");
                        sbWhereClause.Append(" ) ");

                    }
                    else if (criteria.ArchiveGroup.Contains(';'))
                    {
                        string[] ArchiveGroupList = criteria.ArchiveGroup.Split(';');
                        for (int j = 0; j < ArchiveGroupList.Length; j++)
                        {
                            if (j == 0)
                            {
                                if (secCat)
                                    sbWhereClause.Append(" OR ");
                                else
                                    sbWhereClause.Append(" AND ");
                                sbWhereClause.Append(" ( ");
                                sbWhereClause.Append(" (CI.ExtName = '" + ArchiveGroupList[j] + "')");
                            }
                            else
                            {
                                sbWhereClause.Append(" OR ");
                                sbWhereClause.Append(" (CI.ExtName = '" + ArchiveGroupList[j] + "')");
                            }
                        }
                        sbWhereClause.Append(" ) ");
                    }
                }

                if(secCat)
                    sbWhereClause.Append(" ) "); //4

                sbWhereClause.Append(" AND CI.[IsShow] = '1' ");

                #endregion


                //System.Diagnostics.Debug.WriteLine(string.Format("Final Where Clause Param = {0}", sbWhereClause.ToString()));

                queryOptionSTR = sbWhereClause.ToString();

                await Task.Run(() => RevAuditLogger.WriteInformation(Originator.SearchMedia, "PerformAdvanceSearch", request.TenantId, " request.PageSize = " + request.PageSize + " request.PageNumber = " + request.PageNumber + " durationStr = " + durationStr + " queryOptionSTR = " + queryOptionSTR + " criteria = " + criteria + " searchOperatorSpParam = " + searchOperatorSpParam));

                if (request.LoadOptions.Contains("SearchCallsInPrimaryDB"))
                    return await new SearchMediaDAL(request.TenantId).GetAdvanceSearchResultsDTO(request.PageSize, request.PageNumber, durationStr, queryOptionSTR, criteria, searchOperatorSpParam);
                else
                    return await new SearchMediaDAL(request.TenantId).PerformSearchChainedDBs(request.PageSize, request.PageNumber, durationStr, queryOptionSTR, criteria, searchOperatorSpParam);

            }
            catch (SqlException sqle)
            {
                await Task.Run(() => RevAuditLogger.WriteSQLException(Originator.SearchMedia, "PerformAdvanceSearch", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                await Task.Run(() => RevAuditLogger.WriteException(Originator.SearchMedia, "PerformAdvanceSearch", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        #region QA Evaluation Search

        public async Task<DALMediaResponse> PerformDefaultQASearch(VRRequest request)
        {
            string durationStr = QueryConstants.DURATION_DEFAULT;
            string queryOptionSTR = string.Empty;
            string searchOperatorSpParam = string.Empty;
            try
            {
                var criteria = request.Criteria as CallCriteria;
                int i = request.UserId;
                StringBuilder sbWhereClause = new StringBuilder();//TODO:Remove this later
                if (criteria != null)
                {
                    #region ------- Duration String -------
                    if (criteria.StartDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null) || criteria.EndDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null))
                    {
                        durationStr = QueryConstants.DURATION_BETWEEN + criteria.StartDuration.TotalMilliseconds.ToString() + "' AND '" + criteria.EndDuration.TotalMilliseconds.ToString() + "'";
                    }

                    #endregion

                    #region ------- Search Restriction -------

                    if (criteria.RestrictionInHours > 0)
                    {
                        var dt = DateTime.Now.AddHours(-criteria.RestrictionInHours);

                        criteria.StartDate = dt;
                        criteria.EndDate = DateTime.Now;
                        criteria.StartTime = dt.TimeOfDay;
                        criteria.EndTime = TimeSpan.Parse(DateTime.Now.ToString("HH:mm:ss"));// TimeSpan.Parse(DateTime.Now.TimeOfDay.ToString("HH:mm:ss"));
                    }

                    #endregion

                    bool secCat = false;

                    if (criteria.CategoryGroupExtensions != null && criteria.CategoryGroupExtensions.Count() > 0)
                    {

                        var audioNscreenCategoryGroupExtensions = criteria.CategoryGroupExtensions.Where(cge => cge.GroupType == GroupType.Audio || cge.GroupType == GroupType.Screens || cge.GroupType == GroupType.Text || cge.GroupType == GroupType.IQ3 || cge.GroupType == GroupType.Teams || cge.GroupType == GroupType.Revcell);
                        if (audioNscreenCategoryGroupExtensions.Count() != 0)
                        {
                            sbWhereClause.Append(" AND ( "); //1
                            sbWhereClause.AppendLine();

                            //foreach (var catGrpExt in criteria.CategoryGroupExtensions)
                            foreach (var catGrpExt in audioNscreenCategoryGroupExtensions)
                            {

                                if (secCat && catGrpExt.GroupExtensions.Count > 0)
                                    sbWhereClause.Append(" OR ( "); //3
                                if (catGrpExt.GroupExtensions.Count > 0)
                                {
                                    sbWhereClause.Append(" ( ");//2

                                    foreach (var grpExt in catGrpExt.GroupExtensions)
                                    {
                                        sbWhereClause.AppendFormat(@"({0}.Ext IN ({2}))", "CI", grpExt.GroupId, grpExt.ExtensionIdsCSV);
                                        sbWhereClause.Append(" OR ");
                                        sbWhereClause.AppendLine();
                                    }
                                    sbWhereClause.RemoveLast(" OR ");
                                    sbWhereClause.Append(" ) ");//2
                                    sbWhereClause.AppendLine();

                                    sbWhereClause.AppendFormat(" AND CI.CallType = {0} ", (int)catGrpExt.GroupType);//Call Type
                                    sbWhereClause.AppendLine();
                                }
                                if (secCat && catGrpExt.GroupExtensions.Count > 0)
                                    sbWhereClause.Append(" ) "); //3
                                if (catGrpExt.GroupExtensions.Count > 0)
                                    secCat = true;
                            }
                            // Added for IQ3

                            if (sbWhereClause.Equals("{ AND (  OR (  )  OR (  ) }"))
                            {
                                sbWhereClause.Replace("{ AND (  OR (  )  OR (  ) }", "");
                                sbWhereClause.Append("AND CI.CallType = 7 ");
                            }
                            else
                            {
                                //  sbWhereClause.Append("or CI.CallType = 7 ");
                            }

                            sbWhereClause.Append(" ) "); //1
                        }
                    }
                    sbWhereClause.Append(" AND CI.[IsShow] = '1' ");
                }

                //queryOptionSTR = " AND CI.[IsShow] = '1' ";
                queryOptionSTR = sbWhereClause.ToString();

                await Task.Run(() => RevAuditLogger.WriteInformation(Originator.SearchMedia, "PerformDefaultQASearch", request.TenantId, " request.PageSize = " + request.PageSize + " request.PageNumber = " + request.PageNumber + " durationStr = " + durationStr + " queryOptionSTR = " + queryOptionSTR + " criteria = " + criteria + " searchOperatorSpParam = " + searchOperatorSpParam));

                return await new SearchMediaDAL(request.TenantId).GetDefaultQASearchResultsDTO(request.PageSize, request.PageNumber, durationStr, queryOptionSTR, criteria, searchOperatorSpParam);
            }
            catch (SqlException sqle)
            {
                await Task.Run(() => RevAuditLogger.WriteSQLException(Originator.SearchMedia, "PerformDefaultQASearch", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                await Task.Run(() => RevAuditLogger.WriteException(Originator.SearchMedia, "PerformDefaultQASearch", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public async Task<DALMediaResponse> PerformAdvanceQASearch(VRRequest request)
        {
            StringBuilder sbWhereClause = new StringBuilder();//TODO:Remove this later

            string durationStr = QueryConstants.DURATION_DEFAULT;
            string queryOptionSTR = string.Empty;
            string searchOperatorSpParam = string.Empty;

            try
            {
                var criteria = request.Criteria as CallCriteria;
                int i = request.UserId;

                if (criteria != null)
                {
                    #region ------- Duration String -------
                    if (criteria.StartDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null) || criteria.EndDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null))
                    {
                        durationStr = QueryConstants.DURATION_BETWEEN + criteria.StartDuration.TotalMilliseconds.ToString() + "' AND '" + criteria.EndDuration.TotalMilliseconds.ToString() + "'";
                    }

                    #endregion

                    #region ------- Search Restriction -------

                    if (criteria.RestrictionInHours > 0)
                    {
                        var dt = DateTime.Now.AddHours(-criteria.RestrictionInHours);

                        criteria.StartDate = dt;
                        criteria.EndDate = DateTime.Now;
                        criteria.StartTime = dt.TimeOfDay;
                        criteria.EndTime = TimeSpan.Parse(DateTime.Now.ToString("HH:mm:ss"));// TimeSpan.Parse(DateTime.Now.TimeOfDay.ToString("HH:mm:ss"));
                    }

                    #endregion
                }


                if (request.Criteria.IsCustomSearch)
                {
                    queryOptionSTR = CustomSearchQueryBuilder.BuildQuery(request.CustomSearchCriteria.Conditions, request.CustomSearchCriteria.Operator);
                    sbWhereClause.Append(queryOptionSTR);
                    searchOperatorSpParam = request.CustomSearchCriteria.Operator == OperatorExpression.Like ? string.Format("Like '%{0}%'", request.Criteria.CustomText) : string.Format("= '{0}'", request.Criteria.CustomText);
                }

                #region ------- Group Extensions -------

                bool secCat = false;
                if (criteria.CategoryGroupExtensions != null && criteria.CategoryGroupExtensions.Count() > 0)
                {

                    var audioNscreenCategoryGroupExtensions = criteria.CategoryGroupExtensions.Where(cge => cge.GroupType == GroupType.Audio || cge.GroupType == GroupType.Screens || cge.GroupType == GroupType.Text || cge.GroupType == GroupType.IQ3 || cge.GroupType == GroupType.Teams || cge.GroupType == GroupType.Revcell);
                    if (audioNscreenCategoryGroupExtensions.Count() != 0)
                    {
                        sbWhereClause.Append(" AND ( "); //1
                        sbWhereClause.AppendLine();

                        //foreach (var catGrpExt in criteria.CategoryGroupExtensions)
                        foreach (var catGrpExt in audioNscreenCategoryGroupExtensions)
                        {

                            if (secCat && catGrpExt.GroupExtensions.Count > 0)
                                sbWhereClause.Append(" OR ( "); //3
                            if (catGrpExt.GroupExtensions.Count > 0)
                            {
                                sbWhereClause.Append(" ( ");//2

                                foreach (var grpExt in catGrpExt.GroupExtensions)
                                {
                                    //sbWhereClause.AppendFormat(@"({0}.GroupNum = {1} or {0}.Ext IN ({2}))", "CI", grpExt.GroupId, grpExt.ExtensionIdsCSV);

                                    sbWhereClause.AppendFormat(@"({0}.Ext IN ({2}))", "CI", grpExt.GroupId, grpExt.ExtensionIdsCSV);
                                    sbWhereClause.Append(" OR ");
                                    sbWhereClause.AppendLine();
                                }
                                sbWhereClause.RemoveLast(" OR ");
                                sbWhereClause.Append(" ) ");//2
                                sbWhereClause.AppendLine();

                                sbWhereClause.AppendFormat(" AND CI.CallType = {0} ", (int)catGrpExt.GroupType);//Call Type
                                sbWhereClause.AppendLine();
                                //sbWhereClause.Append(" ) ");//2
                            }
                            if (secCat && catGrpExt.GroupExtensions.Count > 0)
                                sbWhereClause.Append(" ) "); //3
                            if (catGrpExt.GroupExtensions.Count > 0)
                                secCat = true;
                        }
                        // Added for IQ3

                        if (sbWhereClause.Equals("{ AND (  OR (  )  OR (  ) }"))
                        {
                            sbWhereClause.Replace("{ AND (  OR (  )  OR (  ) }", "");
                            sbWhereClause.Append("AND CI.CallType = 7 ");
                        }
                        else
                        {
                            //  sbWhereClause.Append("or CI.CallType = 7 ");
                        }

                        sbWhereClause.Append(" ) "); //1
                    }
                }
                sbWhereClause.Append(" AND CI.[IsShow] = '1' ");

                #endregion


                //System.Diagnostics.Debug.WriteLine(string.Format("Final Where Clause Param = {0}", sbWhereClause.ToString()));

                queryOptionSTR = sbWhereClause.ToString();

                await Task.Run(() => RevAuditLogger.WriteInformation(Originator.SearchMedia, "PerformAdvanceQASearch", request.TenantId, " request.PageSize = " + request.PageSize + " request.PageNumber = " + request.PageNumber + " durationStr = " + durationStr + " queryOptionSTR = " + queryOptionSTR + " criteria = " + criteria + " searchOperatorSpParam = " + searchOperatorSpParam));


                if (request.LoadOptions.Contains("SearchCallsInPrimaryDB"))
                    return await new SearchMediaDAL(request.TenantId).GetAdvanceQASearchResultsDTO(request.PageSize, request.PageNumber, durationStr, queryOptionSTR, criteria, searchOperatorSpParam);
                else
                    return await new SearchMediaDAL(request.TenantId).PerformQASearchChainedDBs(request.PageSize, request.PageNumber, durationStr, queryOptionSTR, criteria, searchOperatorSpParam);

            }
            catch (SqlException sqle)
            {
                await Task.Run(() => RevAuditLogger.WriteSQLException(Originator.SearchMedia, "PerformAdvanceQASearch", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                await Task.Run(() => RevAuditLogger.WriteException(Originator.SearchMedia, "PerformAdvanceQASearch", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #endregion

        #region Search Calls EC
        public async Task<DALMediaResponse> GetDefaultSearchResultsEC(VRRequest request)
        {
            string durationStr = QueryConstants.DURATION_DEFAULT;
            string queryOptionSTR = string.Empty;
            string searchOperatorSpParam = string.Empty;
            var recorderDALMediaResponse = new List<RecorderDALMediaResponse>();
            try
            {
                var criteria = request.Criteria as CallCriteria;
                int i = request.UserId;
                StringBuilder sbWhereClause = new StringBuilder();
                List<Recorder> recorders = request.Recorders;

                if (criteria != null)
                {
                    #region ------- Duration String -------
                    if (criteria.StartDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null) || criteria.EndDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null))
                    {
                        durationStr = QueryConstants.DURATION_BETWEEN + criteria.StartDuration.TotalMilliseconds.ToString() + "' AND '" + criteria.EndDuration.TotalMilliseconds.ToString() + "'";
                    }

                    #endregion

                    #region ------- Search Restriction -------

                    if (criteria.RestrictionInHours > 0)
                    {
                        var dt = DateTime.Now.AddHours(-criteria.RestrictionInHours);

                        criteria.StartDate = dt;
                        criteria.EndDate = DateTime.Now;
                        criteria.StartTime = dt.TimeOfDay;
                        criteria.EndTime = TimeSpan.Parse(DateTime.Now.ToString("HH:mm:ss"));// TimeSpan.Parse(DateTime.Now.TimeOfDay.ToString("HH:mm:ss"));
                    }

                    #endregion

                    bool secCat = false;

                    foreach (var rec in recorders)
                    {
                        bool bExceptionThrown = false;
                        try
                        {
                            sbWhereClause = new StringBuilder();
                            secCat = false;

                            if (criteria.RecorderCategoryGroupExtensions != null && criteria.RecorderCategoryGroupExtensions.Count() > 0)
                            {
                                var recResult = criteria.RecorderCategoryGroupExtensions.FirstOrDefault(r => r.RecorderId == rec.Id);

                                if (recResult != null && recResult.CategoryGroupExtensions.Count() != 0)
                                {
                                    sbWhereClause.Append(" AND ( ");
                                    sbWhereClause.AppendLine();

                                    foreach (var catGrpExt in recResult.CategoryGroupExtensions)
                                    {

                                        if (secCat && catGrpExt.GroupExtensions.Count > 0)
                                            sbWhereClause.Append(" OR ( ");
                                        if (catGrpExt.GroupExtensions.Count > 0)
                                        {
                                            sbWhereClause.Append(" ( ");

                                            foreach (var grpExt in catGrpExt.GroupExtensions)
                                            {
                                                sbWhereClause.AppendFormat(@"({0}.Ext IN ({2}))", "CI", grpExt.GroupId, grpExt.ExtensionIdsCSV);
                                                sbWhereClause.Append(" OR ");
                                                sbWhereClause.AppendLine();
                                            }
                                            sbWhereClause.RemoveLast(" OR ");
                                            sbWhereClause.Append(" ) ");
                                            sbWhereClause.AppendLine();

                                            sbWhereClause.AppendFormat(" AND CI.CallType = {0} ", (int)catGrpExt.GroupType);//Call Type
                                            sbWhereClause.AppendLine();
                                        }
                                        if (secCat && catGrpExt.GroupExtensions.Count > 0)
                                            sbWhereClause.Append(" ) ");
                                        if (catGrpExt.GroupExtensions.Count > 0)
                                            secCat = true;
                                    }

                                    sbWhereClause.Append(" ) ");
                                }
                            }
                            sbWhereClause.Append(" AND CI.[IsShow] = '1' ");
                            queryOptionSTR = sbWhereClause.ToString();

                            DALMediaResponse dalMediaResponse = null;
                            if (rec.IsPrimary)
                                dalMediaResponse = await new SearchMediaDALEC(request.TenantId).GetDefaultSearchResultsDTOEC(request.PageSize, request.PageNumber, durationStr, queryOptionSTR, criteria, searchOperatorSpParam, rec);
                            else
                            {
                                // Alternative approach
                                RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                                entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                                string startDate = criteria.StartDate.DateAsStringWithoutSlash();
                                string endDate = criteria.EndDate.DateAsStringWithoutSlash();
                                string startTime = criteria.StartTime.ConvertTimeSpanToString();
                                string endTime = criteria.EndTime.ConvertTimeSpanToString();

                                bool isGlobal = criteria.SearchType == SearchType.Global ? true : false;
                                dalMediaResponse = entClient.GetDefaultSearchResultsDTO(request.PageSize, request.PageNumber, durationStr, queryOptionSTR, startDate, endDate, startTime, endTime, isGlobal, searchOperatorSpParam, rec.Id, rec.Name);
                            }
                            recorderDALMediaResponse.Add(new RecorderDALMediaResponse { RecorderId = rec.Id, RecorderName = rec.Name, ListOfMedias = dalMediaResponse.ListOfMedias, PageSize = request.PageSize, TotalRecords = dalMediaResponse.TotalRecords, TotalPages = dalMediaResponse.TotalPages });
                        }
                        catch (Exception ex)
                        {
                            bExceptionThrown = true;
                            await Task.Run(() => RevAuditLogger.WriteException(Originator.SearchMedia, "GetDefaultSearchResultsEC", request.TenantId, ex.Message + " - An error has occurred (GetDefaultSearchResultsEC) while searching calls from Recorder Id = " + rec.Id + ", Recorder Name = " + rec.Name + " exception = " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                        }
                        if (bExceptionThrown)
                            continue;
                    }
                }

                long allRecTotalRecords = recorderDALMediaResponse.Sum(c => c.TotalRecords);
                int allRecTotalPages = recorderDALMediaResponse.Sum(c => c.TotalPages);

                DALMediaResponse dalMediaRes = new DALMediaResponse();
                dalMediaRes.ListOfMedias = new List<MediaInfo>();

                foreach (var rc in recorderDALMediaResponse.Where(w => w.ListOfMedias != null))
                {
                    double rCallCount = (double)(rc.TotalRecords * request.PageSize) / allRecTotalRecords; //rc.TotalRecords / hcf;
                                                                                                           //double rCallCount1 = Math.Round(Convert.ToDouble((double)rc.TotalRecords * 100 / allRecTotalRecords), 2);
                    int count = (int)Math.Round(rCallCount);

                    //====== Next Page Size ======//
                    rc.PageSize = count;
                    //====== Next Page Size ======//

                    if (count == request.PageSize)
                        count = (int)allRecTotalRecords;//or rc.TotalRecords;
                    dalMediaRes.ListOfMedias.AddRange(rc.ListOfMedias.Take(count));
                }
                dalMediaRes.ListOfMedias = dalMediaRes.ListOfMedias.OrderByDescending(o => o.StartTime).ToList();
                dalMediaRes.TotalPages = allRecTotalPages;
                dalMediaRes.TotalRecords = allRecTotalRecords;
                return dalMediaRes;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public async Task<DALMediaResponse> GetAdvanceSearchResultsEC(VRRequest request)
        {
            StringBuilder sbWhereClause = new StringBuilder();
            List<Recorder> recorders = request.Recorders;

            string durationStr = QueryConstants.DURATION_DEFAULT;
            string queryOptionSTR = string.Empty;
            string searchOperatorSpParam = string.Empty;
            var recorderDALMediaResponse = new List<RecorderDALMediaResponse>();
            try
            {
                var criteria = request.Criteria as CallCriteria;
                int i = request.UserId;

                if (criteria != null)
                {
                    #region ------- Duration String -------
                    if (criteria.StartDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null) || criteria.EndDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null))
                    {
                        durationStr = QueryConstants.DURATION_BETWEEN + criteria.StartDuration.TotalMilliseconds.ToString() + "' AND '" + criteria.EndDuration.TotalMilliseconds.ToString() + "'";
                    }

                    #endregion

                    #region ------- Search Restriction -------

                    if (criteria.RestrictionInHours > 0)
                    {
                        var dt = DateTime.Now.AddHours(-criteria.RestrictionInHours);

                        criteria.StartDate = dt;
                        criteria.EndDate = DateTime.Now;
                        criteria.StartTime = dt.TimeOfDay;
                        criteria.EndTime = TimeSpan.Parse(DateTime.Now.ToString("HH:mm:ss"));// TimeSpan.Parse(DateTime.Now.TimeOfDay.ToString("HH:mm:ss"));
                    }

                    #endregion
                }


                if (request.Criteria.IsCustomSearch)
                {
                    queryOptionSTR = CustomSearchQueryBuilder.BuildQuery(request.CustomSearchCriteria.Conditions, request.CustomSearchCriteria.Operator);
                    sbWhereClause.Append(queryOptionSTR);
                    searchOperatorSpParam = request.CustomSearchCriteria.Operator == OperatorExpression.Like ? string.Format("Like '%{0}%'", request.Criteria.CustomText) : string.Format("= '{0}'", request.Criteria.CustomText);
                }
                bool secCat = false;

                foreach (var rec in recorders)
                {
                    bool bExceptionThrown = false;
                    secCat = false;
                    //queryOptionSTR = string.Empty;
                    //sbWhereClause = new StringBuilder();
                    try
                    {
                        #region ------- Group Extensions -------

                        if (criteria.RecorderCategoryGroupExtensions != null && criteria.RecorderCategoryGroupExtensions.Count() > 0)
                        {
                            var recResult = criteria.RecorderCategoryGroupExtensions.FirstOrDefault(r => r.RecorderId == rec.Id);

                            if (recResult != null && recResult.CategoryGroupExtensions.Count() != 0)
                            {
                                var audioNscreenCategoryGroupExtensions = recResult.CategoryGroupExtensions.Where(cge => cge.GroupType == GroupType.Audio || cge.GroupType == GroupType.Screens || cge.GroupType == GroupType.Text || cge.GroupType == GroupType.IQ3 || cge.GroupType == GroupType.Teams);
                                if (audioNscreenCategoryGroupExtensions.Count() != 0)
                                {
                                    sbWhereClause.Append(" AND ( ");
                                    sbWhereClause.AppendLine();

                                    foreach (var catGrpExt in audioNscreenCategoryGroupExtensions)
                                    {

                                        if (secCat && catGrpExt.GroupExtensions.Count > 0)
                                            sbWhereClause.Append(" OR ( ");
                                        if (catGrpExt.GroupExtensions.Count > 0)
                                        {
                                            sbWhereClause.Append(" ( ");

                                            foreach (var grpExt in catGrpExt.GroupExtensions)
                                            {
                                                sbWhereClause.AppendFormat(@"({0}.Ext IN ({2}))", "CI", grpExt.GroupId, grpExt.ExtensionIdsCSV);
                                                sbWhereClause.Append(" OR ");
                                                sbWhereClause.AppendLine();
                                            }
                                            sbWhereClause.RemoveLast(" OR ");
                                            sbWhereClause.Append(" ) ");
                                            sbWhereClause.AppendLine();

                                            sbWhereClause.AppendFormat(" AND CI.CallType = {0} ", (int)catGrpExt.GroupType);//Call Type
                                            sbWhereClause.AppendLine();
                                        }
                                        if (secCat && catGrpExt.GroupExtensions.Count > 0)
                                            sbWhereClause.Append(" ) ");
                                        if (catGrpExt.GroupExtensions.Count > 0)
                                            secCat = true;
                                    }
                                    // Added for IQ3

                                    if (sbWhereClause.Equals("{ AND (  OR (  )  OR (  ) }"))
                                    {
                                        sbWhereClause.Replace("{ AND (  OR (  )  OR (  ) }", "");
                                        sbWhereClause.Append("AND CI.CallType = 7 ");
                                    }
                                    else
                                    {
                                        //  sbWhereClause.Append("or CI.CallType = 7 ");
                                    }

                                    sbWhereClause.Append(" ) "); //1
                                }
                            }
                        }

                        #endregion
                        sbWhereClause.Append(" AND CI.[IsShow] = '1' ");
                        queryOptionSTR = sbWhereClause.ToString();
                        DALMediaResponse dalMediaResponse = null;

                        if (rec.IsPrimary)
                        {
                            if (request.LoadOptions.Contains("SearchCallsInPrimaryDB"))
                                dalMediaResponse = await new SearchMediaDALEC(request.TenantId).GetAdvanceSearchResultsDTO(request.PageSize, request.PageNumber, durationStr, queryOptionSTR, criteria, searchOperatorSpParam, rec);
                            else
                                dalMediaResponse = await new SearchMediaDALEC(request.TenantId).PerformSearchChainedDBs(request.PageSize, request.PageNumber, durationStr, queryOptionSTR, criteria, searchOperatorSpParam, rec);
                        }
                        else {
                            // Alternative approach
                            RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                            entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                            string startDate = criteria.StartDate.DateAsStringWithoutSlash();
                            string endDate = criteria.EndDate.DateAsStringWithoutSlash();
                            string startTime = criteria.StartTime.ConvertTimeSpanToString();
                            string endTime = criteria.EndTime.ConvertTimeSpanToString();

                            bool isGlobal = criteria.SearchType == SearchType.Global ? true : false;
                            if (request.LoadOptions.Contains("SearchCallsInPrimaryDB"))
                                dalMediaResponse = entClient.GetAdvanceSearchResultsDTO(request.PageSize, request.PageNumber, durationStr, queryOptionSTR, startDate, endDate, startTime, endTime, isGlobal, searchOperatorSpParam, rec.Id, rec.Name);
                            else
                                dalMediaResponse = entClient.GetAdvanceSearchResultsDTO(request.PageSize, request.PageNumber, durationStr, queryOptionSTR, startDate, endDate, startTime, endTime, isGlobal, searchOperatorSpParam, rec.Id, rec.Name);
                        }
                        recorderDALMediaResponse.Add(new RecorderDALMediaResponse { RecorderId = rec.Id, RecorderName = rec.Name, ListOfMedias = dalMediaResponse.ListOfMedias, PageSize = request.PageSize, TotalRecords = dalMediaResponse.TotalRecords, TotalPages = dalMediaResponse.TotalPages });
                    }
                    catch (Exception ex)
                    {
                        bExceptionThrown = true;
                        await Task.Run(() => RevAuditLogger.WriteException(Originator.SearchMedia, "GetAdvanceSearchResultsEC", request.TenantId, "An error has occurred (GetAdvanceSearchResultsEC) while searching calls from Recorder Id = " + rec.Id + ", Recorder Name = " + rec.Name + " exception = " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                    }
                    if (bExceptionThrown)
                        continue;
                }
                long allRecTotalRecords = recorderDALMediaResponse.Sum(c => c.TotalRecords);
                int allRecTotalPages = recorderDALMediaResponse.Sum(c => c.TotalPages);

                DALMediaResponse dalMediaRes = new DALMediaResponse();
                dalMediaRes.ListOfMedias = new List<MediaInfo>();

                foreach (var rc in recorderDALMediaResponse.Where(w => w.ListOfMedias != null))
                {
                    double rCallCount = (double)(rc.TotalRecords * request.PageSize) / allRecTotalRecords; //rc.TotalRecords / hcf;
                                                                                                           //double rCallCount1 = Math.Round(Convert.ToDouble((double)rc.TotalRecords * 100 / allRecTotalRecords), 2);
                    int count = (int)Math.Round(rCallCount);

                    //====== Next Page Size ======//
                    rc.PageSize = count;
                    //====== Next Page Size ======//

                    if (count == request.PageSize)
                        count = (int)allRecTotalRecords;//or rc.TotalRecords;
                    dalMediaRes.ListOfMedias.AddRange(rc.ListOfMedias.Take(count));
                }
                dalMediaRes.ListOfMedias = dalMediaRes.ListOfMedias.OrderByDescending(o => o.StartTime).ToList();
                dalMediaRes.TotalPages = allRecTotalPages;
                dalMediaRes.TotalRecords = allRecTotalRecords;
                return dalMediaRes;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        #endregion
    }
}
