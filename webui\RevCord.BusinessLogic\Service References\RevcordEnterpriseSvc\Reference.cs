﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace RevCord.BusinessLogic.RevcordEnterpriseSvc {
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.ServiceContractAttribute(ConfigurationName="RevcordEnterpriseSvc.IRevEnterpriseSvc")]
    public interface IRevEnterpriseSvc {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/CallAuditSave", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/CallAuditSaveResponse")]
        int CallAuditSave(string callId, int userNum, string ipAddress, bool isSaved);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/CallAuditSave", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/CallAuditSaveResponse")]
        System.Threading.Tasks.Task<int> CallAuditSaveAsync(string callId, int userNum, string ipAddress, bool isSaved);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetCallAuditTrail", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetCallAuditTrailResponse")]
        RevCord.DataContracts.ReportEntities.CallAuditReport[] GetCallAuditTrail(System.DateTime startDate, System.DateTime endDate, string callId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetCallAuditTrail", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetCallAuditTrailResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.ReportEntities.CallAuditReport[]> GetCallAuditTrailAsync(System.DateTime startDate, System.DateTime endDate, string callId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetAuditedCallsByExtension", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetAuditedCallsByExtensionResponse")]
        RevCord.DataContracts.ReportEntities.CallAuditReport[] GetAuditedCallsByExtension(System.DateTime startDate, System.DateTime endDate, int ext);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetAuditedCallsByExtension", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetAuditedCallsByExtensionResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.ReportEntities.CallAuditReport[]> GetAuditedCallsByExtensionAsync(System.DateTime startDate, System.DateTime endDate, int ext);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetUserActivities", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetUserActivitiesResponse")]
        RevCord.BusinessLogic.RevcordEnterpriseSvc.GetUserActivitiesResponse GetUserActivities(RevCord.BusinessLogic.RevcordEnterpriseSvc.GetUserActivitiesRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetUserActivities", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetUserActivitiesResponse")]
        System.Threading.Tasks.Task<RevCord.BusinessLogic.RevcordEnterpriseSvc.GetUserActivitiesResponse> GetUserActivitiesAsync(RevCord.BusinessLogic.RevcordEnterpriseSvc.GetUserActivitiesRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetCallsByLocation", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetCallsByLocationResponse")]
        RevCord.DataContracts.VoiceRecEntities.CallInfo[] GetCallsByLocation(RevCord.DataContracts.DTO.CallInfoSearchCriteriaDTO callInfoSearchCriteriaDTO);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetCallsByLocation", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetCallsByLocationResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.VoiceRecEntities.CallInfo[]> GetCallsByLocationAsync(RevCord.DataContracts.DTO.CallInfoSearchCriteriaDTO callInfoSearchCriteriaDTO);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetDefaultSearchResultsDTO", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetDefaultSearchResultsDTOResponse")]
        RevCord.DataContracts.DTO.DALMediaResponse GetDefaultSearchResultsDTO(int pageSize, int pageIndex, string duration, string criteria, string startDate, string endDate, string startTime, string endTime, bool isGlobal, string customSpParam, int recId, string recName);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetDefaultSearchResultsDTO", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetDefaultSearchResultsDTOResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.DTO.DALMediaResponse> GetDefaultSearchResultsDTOAsync(int pageSize, int pageIndex, string duration, string criteria, string startDate, string endDate, string startTime, string endTime, bool isGlobal, string customSpParam, int recId, string recName);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetAdvanceSearchResultsDTO", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetAdvanceSearchResultsDTOResponse")]
        RevCord.DataContracts.DTO.DALMediaResponse GetAdvanceSearchResultsDTO(int pageSize, int pageIndex, string duration, string criteria, string startDate, string endDate, string startTime, string endTime, bool isGlobal, string customSpParam, int recId, string recName);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetAdvanceSearchResultsDTO", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetAdvanceSearchResultsDTOResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.DTO.DALMediaResponse> GetAdvanceSearchResultsDTOAsync(int pageSize, int pageIndex, string duration, string criteria, string startDate, string endDate, string startTime, string endTime, bool isGlobal, string customSpParam, int recId, string recName);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/PerformSearchChainedDBs", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/PerformSearchChainedDBsResponse")]
        RevCord.DataContracts.DTO.DALMediaResponse PerformSearchChainedDBs(int pageSize, int pageIndex, string duration, string criteria, RevCord.DataContracts.Criteria.CallCriteria callCriteria, string customSpParam, int recId, string recName);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/PerformSearchChainedDBs", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/PerformSearchChainedDBsResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.DTO.DALMediaResponse> PerformSearchChainedDBsAsync(int pageSize, int pageIndex, string duration, string criteria, RevCord.DataContracts.Criteria.CallCriteria callCriteria, string customSpParam, int recId, string recName);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetDefaultQASearchResultsDTO", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetDefaultQASearchResultsDTOResponse")]
        RevCord.DataContracts.DTO.DALMediaResponse GetDefaultQASearchResultsDTO(int tenantId, int pageSize, int pageIndex, string duration, string criteria, RevCord.DataContracts.Criteria.CallCriteria callCriteria, string customSpParam);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetDefaultQASearchResultsDTO", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetDefaultQASearchResultsDTOResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.DTO.DALMediaResponse> GetDefaultQASearchResultsDTOAsync(int tenantId, int pageSize, int pageIndex, string duration, string criteria, RevCord.DataContracts.Criteria.CallCriteria callCriteria, string customSpParam);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetAdvanceQASearchResultsDTO", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetAdvanceQASearchResultsDTOResponse")]
        RevCord.DataContracts.DTO.DALMediaResponse GetAdvanceQASearchResultsDTO(int tenantId, int pageSize, int pageIndex, string duration, string criteria, RevCord.DataContracts.Criteria.CallCriteria callCriteria, string customSpParam);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetAdvanceQASearchResultsDTO", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetAdvanceQASearchResultsDTOResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.DTO.DALMediaResponse> GetAdvanceQASearchResultsDTOAsync(int tenantId, int pageSize, int pageIndex, string duration, string criteria, RevCord.DataContracts.Criteria.CallCriteria callCriteria, string customSpParam);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/PerformQASearchChainedDBs", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/PerformQASearchChainedDBsResponse")]
        RevCord.DataContracts.DTO.DALMediaResponse PerformQASearchChainedDBs(int tenantId, int pageSize, int pageIndex, string duration, string criteria, RevCord.DataContracts.Criteria.CallCriteria callCriteria, string customSpParam);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/PerformQASearchChainedDBs", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/PerformQASearchChainedDBsResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.DTO.DALMediaResponse> PerformQASearchChainedDBsAsync(int tenantId, int pageSize, int pageIndex, string duration, string criteria, RevCord.DataContracts.Criteria.CallCriteria callCriteria, string customSpParam);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetUserExtensionInfos", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetUserExtensionInfosResponse")]
        RevCord.DataContracts.VoiceRecEntities.UserExtensionInfo[] GetUserExtensionInfos(int recId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetUserExtensionInfos", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetUserExtensionInfosResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.VoiceRecEntities.UserExtensionInfo[]> GetUserExtensionInfosAsync(int recId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetAppUsers", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetAppUsersResponse")]
        RevCord.DataContracts.Response.UserManagementResponse GetAppUsers(RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetAppUsers", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetAppUsersResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.Response.UserManagementResponse> GetAppUsersAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetAppUserAccount", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetAppUserAccountResponse")]
        RevCord.DataContracts.Response.UserManagementResponse GetAppUserAccount(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, long uId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetAppUserAccount", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetAppUserAccountResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.Response.UserManagementResponse> GetAppUserAccountAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, long uId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/RecoverAppUserAccount", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/RecoverAppUserAccountResponse")]
        int RecoverAppUserAccount(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string userIds);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/RecoverAppUserAccount", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/RecoverAppUserAccountResponse")]
        System.Threading.Tasks.Task<int> RecoverAppUserAccountAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string userIds);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/UpdateAppUserAccount", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/UpdateAppUserAccountResponse")]
        RevCord.DataContracts.UserManagement.User[] UpdateAppUserAccount(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, RevCord.DataContracts.UserManagement.User appUser);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/UpdateAppUserAccount", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/UpdateAppUserAccountResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.UserManagement.User[]> UpdateAppUserAccountAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, RevCord.DataContracts.UserManagement.User appUser);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/DeleteAppUserAccount", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/DeleteAppUserAccountResponse")]
        RevCord.DataContracts.Response.UserManagementResponse DeleteAppUserAccount(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int id);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/DeleteAppUserAccount", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/DeleteAppUserAccountResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.Response.UserManagementResponse> DeleteAppUserAccountAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int id);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/SaveAssignedGroup", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/SaveAssignedGroupResponse")]
        bool SaveAssignedGroup(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, long uId, int gId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/SaveAssignedGroup", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/SaveAssignedGroupResponse")]
        System.Threading.Tasks.Task<bool> SaveAssignedGroupAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, long uId, int gId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/DeleteUserGroup", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/DeleteUserGroupResponse")]
        bool DeleteUserGroup(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, RevCord.DataContracts.UserManagement.UserGroup uGroup);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/DeleteUserGroup", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/DeleteUserGroupResponse")]
        System.Threading.Tasks.Task<bool> DeleteUserGroupAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, RevCord.DataContracts.UserManagement.UserGroup uGroup);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetUserData", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetUserDataResponse")]
        RevCord.DataContracts.UserManagement.UserInfoLite[] GetUserData(RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetUserData", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetUserDataResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.UserManagement.UserInfoLite[]> GetUserDataAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/FetchAllActiveUsers", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/FetchAllActiveUsersResponse")]
        RevCord.DataContracts.UserManagement.User[] FetchAllActiveUsers();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/FetchAllActiveUsers", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/FetchAllActiveUsersResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.UserManagement.User[]> FetchAllActiveUsersAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetTreeViewFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetTreeViewFromRecorderResponse")]
        RevCord.DataContracts.DTO.TreeViewDataDTO[] GetTreeViewFromRecorder(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int userNum, string userId, int authNum, string authType, int type, bool getOnlyAudioChannels, int selectType);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetTreeViewFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetTreeViewFromRecorderResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.DTO.TreeViewDataDTO[]> GetTreeViewFromRecorderAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int userNum, string userId, int authNum, string authType, int type, bool getOnlyAudioChannels, int selectType);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetGroupsTree", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetGroupsTreeResponse")]
        RevCord.DataContracts.ViewModelEntities.TreeviewData[] GetGroupsTree(int userNum, string userId, int authNum, string authType, int type, int userType, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetGroupsTree", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetGroupsTreeResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.ViewModelEntities.TreeviewData[]> GetGroupsTreeAsync(int userNum, string userId, int authNum, string authType, int type, int userType, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetGroupsTreeFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetGroupsTreeFromRecorderResponse")]
        RevCord.DataContracts.ViewModelEntities.TreeviewData[] GetGroupsTreeFromRecorder(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int userNum, string userId, int authNum, string authType, int type, int userType);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetGroupsTreeFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetGroupsTreeFromRecorderResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.ViewModelEntities.TreeviewData[]> GetGroupsTreeFromRecorderAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int userNum, string userId, int authNum, string authType, int type, int userType);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetGroupsTreeNonAdminFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetGroupsTreeNonAdminFromRecorderResponse")]
        RevCord.DataContracts.ViewModelEntities.TreeviewData[] GetGroupsTreeNonAdminFromRecorder(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int userNum, string userId, int authNum, string authType, int type, int userType);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetGroupsTreeNonAdminFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetGroupsTreeNonAdminFromRecorderResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.ViewModelEntities.TreeviewData[]> GetGroupsTreeNonAdminFromRecorderAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int userNum, string userId, int authNum, string authType, int type, int userType);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetUsersTreeFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetUsersTreeFromRecorderResponse")]
        RevCord.DataContracts.ViewModelEntities.TreeviewData[] GetUsersTreeFromRecorder(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int userNum);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetUsersTreeFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetUsersTreeFromRecorderResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.ViewModelEntities.TreeviewData[]> GetUsersTreeFromRecorderAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int userNum);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetInquireGroupsTreeforEvaluationReportFromR" +
            "ecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetInquireGroupsTreeforEvaluationReportFromR" +
            "ecorderResponse")]
        RevCord.DataContracts.ViewModelEntities.TreeviewData[] GetInquireGroupsTreeforEvaluationReportFromRecorder(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int userNum, int selectType);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetInquireGroupsTreeforEvaluationReportFromR" +
            "ecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetInquireGroupsTreeforEvaluationReportFromR" +
            "ecorderResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.ViewModelEntities.TreeviewData[]> GetInquireGroupsTreeforEvaluationReportFromRecorderAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int userNum, int selectType);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetMDGroupsTreeForEvaluationReportFromRecord" +
            "er", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetMDGroupsTreeForEvaluationReportFromRecord" +
            "erResponse")]
        RevCord.DataContracts.ViewModelEntities.TreeviewData[] GetMDGroupsTreeForEvaluationReportFromRecorder(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int userNum, int selectType);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetMDGroupsTreeForEvaluationReportFromRecord" +
            "er", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetMDGroupsTreeForEvaluationReportFromRecord" +
            "erResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.ViewModelEntities.TreeviewData[]> GetMDGroupsTreeForEvaluationReportFromRecorderAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int userNum, int selectType);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetEnterpriseUserRightsData", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetEnterpriseUserRightsDataResponse")]
        RevCord.DataContracts.Messages.UMResponse GetEnterpriseUserRightsData(RevCord.DataContracts.Messages.UMRequest umRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetEnterpriseUserRightsData", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetEnterpriseUserRightsDataResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.Messages.UMResponse> GetEnterpriseUserRightsDataAsync(RevCord.DataContracts.Messages.UMRequest umRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetEnterpriseUserTree", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetEnterpriseUserTreeResponse")]
        RevCord.DataContracts.ViewModelEntities.TreeviewData[] GetEnterpriseUserTree(int userNum, string userId, int authNum, string authType, int type, int userType, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetEnterpriseUserTree", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetEnterpriseUserTreeResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.ViewModelEntities.TreeviewData[]> GetEnterpriseUserTreeAsync(int userNum, string userId, int authNum, string authType, int type, int userType, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetChannelsToMonitorFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetChannelsToMonitorFromRecorderResponse")]
        RevCord.DataContracts.VoiceRecEntities.MonitorChannel[] GetChannelsToMonitorFromRecorder(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string whereClause);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetChannelsToMonitorFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetChannelsToMonitorFromRecorderResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.VoiceRecEntities.MonitorChannel[]> GetChannelsToMonitorFromRecorderAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string whereClause);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetSurveysFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetSurveysFromRecorderResponse")]
        RevCord.DataContracts.SurveyEntities.Survey[] GetSurveysFromRecorder(RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetSurveysFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetSurveysFromRecorderResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.SurveyEntities.Survey[]> GetSurveysFromRecorderAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetPublishedSurveysFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetPublishedSurveysFromRecorderResponse")]
        RevCord.DataContracts.SurveyEntities.Survey[] GetPublishedSurveysFromRecorder(RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetPublishedSurveysFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetPublishedSurveysFromRecorderResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.SurveyEntities.Survey[]> GetPublishedSurveysFromRecorderAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/DeleteAndGetSurveysFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/DeleteAndGetSurveysFromRecorderResponse")]
        RevCord.DataContracts.SurveyEntities.Survey[] DeleteAndGetSurveysFromRecorder(int surveyId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/DeleteAndGetSurveysFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/DeleteAndGetSurveysFromRecorderResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.SurveyEntities.Survey[]> DeleteAndGetSurveysFromRecorderAsync(int surveyId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/PublishAndGetSurveysFromRecorders", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/PublishAndGetSurveysFromRecordersResponse")]
        RevCord.DataContracts.SurveyEntities.Survey[] PublishAndGetSurveysFromRecorders(int surveyId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/PublishAndGetSurveysFromRecorders", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/PublishAndGetSurveysFromRecordersResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.SurveyEntities.Survey[]> PublishAndGetSurveysFromRecordersAsync(int surveyId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetSurveyDetailsFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetSurveyDetailsFromRecorderResponse")]
        RevCord.DataContracts.SurveyEntities.Survey GetSurveyDetailsFromRecorder(int surveyId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetSurveyDetailsFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetSurveyDetailsFromRecorderResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.SurveyEntities.Survey> GetSurveyDetailsFromRecorderAsync(int surveyId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/IsSurveyExistOnRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/IsSurveyExistOnRecorderResponse")]
        bool IsSurveyExistOnRecorder(string surveyTitle, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/IsSurveyExistOnRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/IsSurveyExistOnRecorderResponse")]
        System.Threading.Tasks.Task<bool> IsSurveyExistOnRecorderAsync(string surveyTitle, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/CreateSurveyOnRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/CreateSurveyOnRecorderResponse")]
        RevCord.DataContracts.SurveyEntities.Survey CreateSurveyOnRecorder(RevCord.DataContracts.SurveyEntities.Survey survey, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/CreateSurveyOnRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/CreateSurveyOnRecorderResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.SurveyEntities.Survey> CreateSurveyOnRecorderAsync(RevCord.DataContracts.SurveyEntities.Survey survey, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/CreateQuestionOnRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/CreateQuestionOnRecorderResponse")]
        long CreateQuestionOnRecorder(RevCord.DataContracts.SurveyEntities.Question sQuestion, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/CreateQuestionOnRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/CreateQuestionOnRecorderResponse")]
        System.Threading.Tasks.Task<long> CreateQuestionOnRecorderAsync(RevCord.DataContracts.SurveyEntities.Question sQuestion, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/CreateAndGetSectionsOnRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/CreateAndGetSectionsOnRecorderResponse")]
        RevCord.DataContracts.SurveyEntities.SurveySection[] CreateAndGetSectionsOnRecorder(RevCord.DataContracts.SurveyEntities.SurveySection surveyGroup, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/CreateAndGetSectionsOnRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/CreateAndGetSectionsOnRecorderResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.SurveyEntities.SurveySection[]> CreateAndGetSectionsOnRecorderAsync(RevCord.DataContracts.SurveyEntities.SurveySection surveyGroup, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetQuestionsBySurveyIdFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetQuestionsBySurveyIdFromRecorderResponse")]
        RevCord.DataContracts.SurveyEntities.Question[] GetQuestionsBySurveyIdFromRecorder(int surveyId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetQuestionsBySurveyIdFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetQuestionsBySurveyIdFromRecorderResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.SurveyEntities.Question[]> GetQuestionsBySurveyIdFromRecorderAsync(int surveyId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetSectionsBySurveyIdFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetSectionsBySurveyIdFromRecorderResponse")]
        RevCord.DataContracts.SurveyEntities.SurveySection[] GetSectionsBySurveyIdFromRecorder(int surveyId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetSectionsBySurveyIdFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetSectionsBySurveyIdFromRecorderResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.SurveyEntities.SurveySection[]> GetSectionsBySurveyIdFromRecorderAsync(int surveyId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/UpdateQuestionSectionOnRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/UpdateQuestionSectionOnRecorderResponse")]
        bool UpdateQuestionSectionOnRecorder(int questionId, int sectionId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/UpdateQuestionSectionOnRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/UpdateQuestionSectionOnRecorderResponse")]
        System.Threading.Tasks.Task<bool> UpdateQuestionSectionOnRecorderAsync(int questionId, int sectionId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/UpdateAndGetSectionsFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/UpdateAndGetSectionsFromRecorderResponse")]
        RevCord.DataContracts.SurveyEntities.SurveySection[] UpdateAndGetSectionsFromRecorder(RevCord.DataContracts.SurveyEntities.SurveySection surveyGroup, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/UpdateAndGetSectionsFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/UpdateAndGetSectionsFromRecorderResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.SurveyEntities.SurveySection[]> UpdateAndGetSectionsFromRecorderAsync(RevCord.DataContracts.SurveyEntities.SurveySection surveyGroup, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/DeleteAndGetSectionsFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/DeleteAndGetSectionsFromRecorderResponse")]
        RevCord.DataContracts.SurveyEntities.SurveySection[] DeleteAndGetSectionsFromRecorder(int sectionId, int surveyId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/DeleteAndGetSectionsFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/DeleteAndGetSectionsFromRecorderResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.SurveyEntities.SurveySection[]> DeleteAndGetSectionsFromRecorderAsync(int sectionId, int surveyId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/AssignUnAssignQuestionsAndGetSectionsFromRec" +
            "order", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/AssignUnAssignQuestionsAndGetSectionsFromRec" +
            "orderResponse")]
        RevCord.DataContracts.SurveyEntities.SurveySection[] AssignUnAssignQuestionsAndGetSectionsFromRecorder(int surveyId, int sectionId, string assignedQuestions, string unassignedQuestions, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/AssignUnAssignQuestionsAndGetSectionsFromRec" +
            "order", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/AssignUnAssignQuestionsAndGetSectionsFromRec" +
            "orderResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.SurveyEntities.SurveySection[]> AssignUnAssignQuestionsAndGetSectionsFromRecorderAsync(int surveyId, int sectionId, string assignedQuestions, string unassignedQuestions, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetSurveyFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetSurveyFromRecorderResponse")]
        RevCord.DataContracts.SurveyEntities.Survey GetSurveyFromRecorder(int surveyId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetSurveyFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetSurveyFromRecorderResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.SurveyEntities.Survey> GetSurveyFromRecorderAsync(int surveyId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/UpdateSurveyOnRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/UpdateSurveyOnRecorderResponse")]
        void UpdateSurveyOnRecorder(RevCord.DataContracts.SurveyEntities.Survey survey, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/UpdateSurveyOnRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/UpdateSurveyOnRecorderResponse")]
        System.Threading.Tasks.Task UpdateSurveyOnRecorderAsync(RevCord.DataContracts.SurveyEntities.Survey survey, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/UpdateQuestionOnRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/UpdateQuestionOnRecorderResponse")]
        long UpdateQuestionOnRecorder(RevCord.DataContracts.SurveyEntities.Question eQuestion, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/UpdateQuestionOnRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/UpdateQuestionOnRecorderResponse")]
        System.Threading.Tasks.Task<long> UpdateQuestionOnRecorderAsync(RevCord.DataContracts.SurveyEntities.Question eQuestion, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/DeleteQuestionFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/DeleteQuestionFromRecorderResponse")]
        void DeleteQuestionFromRecorder(long id, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/DeleteQuestionFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/DeleteQuestionFromRecorderResponse")]
        System.Threading.Tasks.Task DeleteQuestionFromRecorderAsync(long id, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/UpdateIsPublishedOnRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/UpdateIsPublishedOnRecorderResponse")]
        void UpdateIsPublishedOnRecorder(int surveyId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/UpdateIsPublishedOnRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/UpdateIsPublishedOnRecorderResponse")]
        System.Threading.Tasks.Task UpdateIsPublishedOnRecorderAsync(int surveyId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/UpdateQuestionsOrderOnRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/UpdateQuestionsOrderOnRecorderResponse")]
        long UpdateQuestionsOrderOnRecorder(RevCord.DataContracts.SurveyEntities.Question[] eRequest, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/UpdateQuestionsOrderOnRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/UpdateQuestionsOrderOnRecorderResponse")]
        System.Threading.Tasks.Task<long> UpdateQuestionsOrderOnRecorderAsync(RevCord.DataContracts.SurveyEntities.Question[] eRequest, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/SearchCallsPrimaryDB", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/SearchCallsPrimaryDBResponse")]
        RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchCallsPrimaryDBResponse SearchCallsPrimaryDB(RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchCallsPrimaryDBRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/SearchCallsPrimaryDB", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/SearchCallsPrimaryDBResponse")]
        System.Threading.Tasks.Task<RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchCallsPrimaryDBResponse> SearchCallsPrimaryDBAsync(RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchCallsPrimaryDBRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/SearchRandomCallsPrimaryDB", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/SearchRandomCallsPrimaryDBResponse")]
        RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchRandomCallsPrimaryDBResponse SearchRandomCallsPrimaryDB(RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchRandomCallsPrimaryDBRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/SearchRandomCallsPrimaryDB", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/SearchRandomCallsPrimaryDBResponse")]
        System.Threading.Tasks.Task<RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchRandomCallsPrimaryDBResponse> SearchRandomCallsPrimaryDBAsync(RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchRandomCallsPrimaryDBRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/SearchCallsChainedDBs", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/SearchCallsChainedDBsResponse")]
        RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchCallsChainedDBsResponse SearchCallsChainedDBs(RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchCallsChainedDBsRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/SearchCallsChainedDBs", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/SearchCallsChainedDBsResponse")]
        System.Threading.Tasks.Task<RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchCallsChainedDBsResponse> SearchCallsChainedDBsAsync(RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchCallsChainedDBsRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/UpdateCallsCustomFields", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/UpdateCallsCustomFieldsResponse")]
        int UpdateCallsCustomFields(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callIds, int fieldType, string fieldText, int userId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/UpdateCallsCustomFields", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/UpdateCallsCustomFieldsResponse")]
        System.Threading.Tasks.Task<int> UpdateCallsCustomFieldsAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callIds, int fieldType, string fieldText, int userId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/UpdateCallCustomFields", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/UpdateCallCustomFieldsResponse")]
        int UpdateCallCustomFields(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callId, string tag1, string tag2, string tag3, string tag4, string custName, string callComments);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/UpdateCallCustomFields", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/UpdateCallCustomFieldsResponse")]
        System.Threading.Tasks.Task<int> UpdateCallCustomFieldsAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callId, string tag1, string tag2, string tag3, string tag4, string custName, string callComments);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/UpdateCallRetention", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/UpdateCallRetentionResponse")]
        int UpdateCallRetention(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callId, bool retainValue);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/UpdateCallRetention", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/UpdateCallRetentionResponse")]
        System.Threading.Tasks.Task<int> UpdateCallRetentionAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callId, bool retainValue);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/Inquiremarkerupdate", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/InquiremarkerupdateResponse")]
        int Inquiremarkerupdate(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callId, int markerid, string markertext, string markernotes);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/Inquiremarkerupdate", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/InquiremarkerupdateResponse")]
        System.Threading.Tasks.Task<int> InquiremarkerupdateAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callId, int markerid, string markertext, string markernotes);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetCallsByIds", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetCallsByIdsResponse")]
        RevCord.DataContracts.VoiceRecEntities.CallInfo[] GetCallsByIds(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callIds);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetCallsByIds", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetCallsByIdsResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.VoiceRecEntities.CallInfo[]> GetCallsByIdsAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callIds);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/InsertBookmarkAndGetByCallId", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/InsertBookmarkAndGetByCallIdResponse")]
        RevCord.BusinessLogic.RevcordEnterpriseSvc.InsertBookmarkAndGetByCallIdResponse InsertBookmarkAndGetByCallId(RevCord.BusinessLogic.RevcordEnterpriseSvc.InsertBookmarkAndGetByCallIdRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/InsertBookmarkAndGetByCallId", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/InsertBookmarkAndGetByCallIdResponse")]
        System.Threading.Tasks.Task<RevCord.BusinessLogic.RevcordEnterpriseSvc.InsertBookmarkAndGetByCallIdResponse> InsertBookmarkAndGetByCallIdAsync(RevCord.BusinessLogic.RevcordEnterpriseSvc.InsertBookmarkAndGetByCallIdRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/UpdateBookmarkAndGetByCallId", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/UpdateBookmarkAndGetByCallIdResponse")]
        RevCord.BusinessLogic.RevcordEnterpriseSvc.UpdateBookmarkAndGetByCallIdResponse UpdateBookmarkAndGetByCallId(RevCord.BusinessLogic.RevcordEnterpriseSvc.UpdateBookmarkAndGetByCallIdRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/UpdateBookmarkAndGetByCallId", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/UpdateBookmarkAndGetByCallIdResponse")]
        System.Threading.Tasks.Task<RevCord.BusinessLogic.RevcordEnterpriseSvc.UpdateBookmarkAndGetByCallIdResponse> UpdateBookmarkAndGetByCallIdAsync(RevCord.BusinessLogic.RevcordEnterpriseSvc.UpdateBookmarkAndGetByCallIdRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetCallInfoExportResults", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetCallInfoExportResultsResponse")]
        RevCord.DataContracts.VoiceRecEntities.CallInfoExportResult[] GetCallInfoExportResults(int pageSize, int pageIndex, string startDate, string endDate, string startTime, string endTime, bool isGlobalSearch, string duration, string criteria, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetCallInfoExportResults", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetCallInfoExportResultsResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.VoiceRecEntities.CallInfoExportResult[]> GetCallInfoExportResultsAsync(int pageSize, int pageIndex, string startDate, string endDate, string startTime, string endTime, bool isGlobalSearch, string duration, string criteria, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetCallInfoExportResultsByIds", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetCallInfoExportResultsByIdsResponse")]
        RevCord.DataContracts.VoiceRecEntities.CallInfoExportResult[] GetCallInfoExportResultsByIds(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callIds, System.DateTime startDate, System.DateTime endDate);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetCallInfoExportResultsByIds", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetCallInfoExportResultsByIdsResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.VoiceRecEntities.CallInfoExportResult[]> GetCallInfoExportResultsByIdsAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callIds, System.DateTime startDate, System.DateTime endDate);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetAllDrilldownChartsFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetAllDrilldownChartsFromRecorderResponse")]
        RevCord.DataContracts.ViewModelEntities.RecorderEvaluation GetAllDrilldownChartsFromRecorder(RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetAllDrilldownChartsFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetAllDrilldownChartsFromRecorderResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.ViewModelEntities.RecorderEvaluation> GetAllDrilldownChartsFromRecorderAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/InsertCallsForEvaluation", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/InsertCallsForEvaluationResponse")]
        bool InsertCallsForEvaluation(string calls, int userId, int surveyId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/InsertCallsForEvaluation", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/InsertCallsForEvaluationResponse")]
        System.Threading.Tasks.Task<bool> InsertCallsForEvaluationAsync(string calls, int userId, int surveyId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/InsertEnterpriseEvaluations", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/InsertEnterpriseEvaluationsResponse")]
        bool InsertEnterpriseEvaluations(string callIds, int userId, int surveyId, int evaluatorId, string evaluatorName, string evaluatorEmail);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/InsertEnterpriseEvaluations", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/InsertEnterpriseEvaluationsResponse")]
        System.Threading.Tasks.Task<bool> InsertEnterpriseEvaluationsAsync(string callIds, int userId, int surveyId, int evaluatorId, string evaluatorName, string evaluatorEmail);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/AddEnterpriseEvaluations", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/AddEnterpriseEvaluationsResponse")]
        short AddEnterpriseEvaluations(string callIds, int userId, int surveyId, int evaluatorId, string evaluatorName, string evaluatorEmail);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/AddEnterpriseEvaluations", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/AddEnterpriseEvaluationsResponse")]
        System.Threading.Tasks.Task<short> AddEnterpriseEvaluationsAsync(string callIds, int userId, int surveyId, int evaluatorId, string evaluatorName, string evaluatorEmail);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetEvaluations", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetEvaluationsResponse")]
        RevCord.BusinessLogic.RevcordEnterpriseSvc.GetEvaluationsResponse GetEvaluations(RevCord.BusinessLogic.RevcordEnterpriseSvc.GetEvaluationsRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetEvaluations", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetEvaluationsResponse")]
        System.Threading.Tasks.Task<RevCord.BusinessLogic.RevcordEnterpriseSvc.GetEvaluationsResponse> GetEvaluationsAsync(RevCord.BusinessLogic.RevcordEnterpriseSvc.GetEvaluationsRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetEnterpriseAssociatedEvaluations", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetEnterpriseAssociatedEvaluationsResponse")]
        RevCord.BusinessLogic.RevcordEnterpriseSvc.GetEnterpriseAssociatedEvaluationsResponse GetEnterpriseAssociatedEvaluations(RevCord.BusinessLogic.RevcordEnterpriseSvc.GetEnterpriseAssociatedEvaluationsRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetEnterpriseAssociatedEvaluations", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetEnterpriseAssociatedEvaluationsResponse")]
        System.Threading.Tasks.Task<RevCord.BusinessLogic.RevcordEnterpriseSvc.GetEnterpriseAssociatedEvaluationsResponse> GetEnterpriseAssociatedEvaluationsAsync(RevCord.BusinessLogic.RevcordEnterpriseSvc.GetEnterpriseAssociatedEvaluationsRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetUsers", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetUsersResponse")]
        RevCord.DataContracts.UserManagement.User[] GetUsers(RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetUsers", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetUsersResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.UserManagement.User[]> GetUsersAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/PerformActionAndGetEnterpriseEvaluationDTO", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/PerformActionAndGetEnterpriseEvaluationDTORe" +
            "sponse")]
        RevCord.BusinessLogic.RevcordEnterpriseSvc.PerformActionAndGetEnterpriseEvaluationDTOResponse PerformActionAndGetEnterpriseEvaluationDTO(RevCord.BusinessLogic.RevcordEnterpriseSvc.PerformActionAndGetEnterpriseEvaluationDTORequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/PerformActionAndGetEnterpriseEvaluationDTO", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/PerformActionAndGetEnterpriseEvaluationDTORe" +
            "sponse")]
        System.Threading.Tasks.Task<RevCord.BusinessLogic.RevcordEnterpriseSvc.PerformActionAndGetEnterpriseEvaluationDTOResponse> PerformActionAndGetEnterpriseEvaluationDTOAsync(RevCord.BusinessLogic.RevcordEnterpriseSvc.PerformActionAndGetEnterpriseEvaluationDTORequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/ShareUnshareAndGetEvaluationDTO", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/ShareUnshareAndGetEvaluationDTOResponse")]
        RevCord.BusinessLogic.RevcordEnterpriseSvc.ShareUnshareAndGetEvaluationDTOResponse ShareUnshareAndGetEvaluationDTO(RevCord.BusinessLogic.RevcordEnterpriseSvc.ShareUnshareAndGetEvaluationDTORequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/ShareUnshareAndGetEvaluationDTO", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/ShareUnshareAndGetEvaluationDTOResponse")]
        System.Threading.Tasks.Task<RevCord.BusinessLogic.RevcordEnterpriseSvc.ShareUnshareAndGetEvaluationDTOResponse> ShareUnshareAndGetEvaluationDTOAsync(RevCord.BusinessLogic.RevcordEnterpriseSvc.ShareUnshareAndGetEvaluationDTORequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetCallEvaluationDetailsById", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetCallEvaluationDetailsByIdResponse")]
        RevCord.DataContracts.EvaluationEntities.CallEvaluation GetCallEvaluationDetailsById(int callEvaluationId, int userId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetCallEvaluationDetailsById", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetCallEvaluationDetailsByIdResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.EvaluationEntities.CallEvaluation> GetCallEvaluationDetailsByIdAsync(int callEvaluationId, int userId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/UpdateCallEvaluation", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/UpdateCallEvaluationResponse")]
        RevCord.DataContracts.EvaluationEntities.CallEvaluation UpdateCallEvaluation(RevCord.DataContracts.EvaluationEntities.CallEvaluation callEvaluation);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/UpdateCallEvaluation", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/UpdateCallEvaluationResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.EvaluationEntities.CallEvaluation> UpdateCallEvaluationAsync(RevCord.DataContracts.EvaluationEntities.CallEvaluation callEvaluation);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/UpdateAndGetAssociatedUser", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/UpdateAndGetAssociatedUserResponse")]
        RevCord.DataContracts.UserManagement.User UpdateAndGetAssociatedUser(int evalId, int agentId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/UpdateAndGetAssociatedUser", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/UpdateAndGetAssociatedUserResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.UserManagement.User> UpdateAndGetAssociatedUserAsync(int evalId, int agentId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetEvaluationId", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetEvaluationIdResponse")]
        RevCord.DataContracts.EvaluationEntities.UserEvaluation[] GetEvaluationId(int surveyId, string callId, int userId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetEvaluationId", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetEvaluationIdResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.EvaluationEntities.UserEvaluation[]> GetEvaluationIdAsync(int surveyId, string callId, int userId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/PerformActionOnRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/PerformActionOnRecorderResponse")]
        bool PerformActionOnRecorder(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callSurveyIds, int action, int actionValue, int userId, System.Nullable<System.DateTime> ActionDate);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/PerformActionOnRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/PerformActionOnRecorderResponse")]
        System.Threading.Tasks.Task<bool> PerformActionOnRecorderAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callSurveyIds, int action, int actionValue, int userId, System.Nullable<System.DateTime> ActionDate);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/SearchRecordedCalls", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/SearchRecordedCallsResponse")]
        RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchRecordedCallsResponse SearchRecordedCalls(RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchRecordedCallsRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/SearchRecordedCalls", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/SearchRecordedCallsResponse")]
        System.Threading.Tasks.Task<RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchRecordedCallsResponse> SearchRecordedCallsAsync(RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchRecordedCallsRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetCallDetailsFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetCallDetailsFromRecorderResponse")]
        RevCord.DataContracts.VoiceRecEntities.CallInfoLite GetCallDetailsFromRecorder(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetCallDetailsFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetCallDetailsFromRecorderResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.VoiceRecEntities.CallInfoLite> GetCallDetailsFromRecorderAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetSearchResults", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetSearchResultsResponse")]
        RevCord.DataContracts.ReportEntities.RPTCallInfo[] GetSearchResults(string selectKey, string selectYear, string selectMonth, string selectDay, string fromDate, string toDate, string timeRange, string duration, string groupKey, string groupYear, string groupMonth, string groupDay, string optionString, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetSearchResults", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetSearchResultsResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.ReportEntities.RPTCallInfo[]> GetSearchResultsAsync(string selectKey, string selectYear, string selectMonth, string selectDay, string fromDate, string toDate, string timeRange, string duration, string groupKey, string groupYear, string groupMonth, string groupDay, string optionString, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetDetailSearchResults", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetDetailSearchResultsResponse")]
        RevCord.DataContracts.ReportEntities.RPTCallInfoDetail[] GetDetailSearchResults(string selectKey, string selectYear, string selectMonth, string selectDay, string fromDate, string toDate, string timeRange, string duration, string groupKey, string groupYear, string groupMonth, string groupDay, string optionString, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetDetailSearchResults", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetDetailSearchResultsResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.ReportEntities.RPTCallInfoDetail[]> GetDetailSearchResultsAsync(string selectKey, string selectYear, string selectMonth, string selectDay, string fromDate, string toDate, string timeRange, string duration, string groupKey, string groupYear, string groupMonth, string groupDay, string optionString, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetSearchResultsMonthDayOfWeek", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetSearchResultsMonthDayOfWeekResponse")]
        RevCord.DataContracts.ReportEntities.RPTCallInfo[] GetSearchResultsMonthDayOfWeek(string selectKey, string selectYear, string selectMonth, string selectDay, string fromDate, string toDate, string timeRange, string duration, string groupKey, string groupYear, string groupMonth, string groupDay, string optionString, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetSearchResultsMonthDayOfWeek", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetSearchResultsMonthDayOfWeekResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.ReportEntities.RPTCallInfo[]> GetSearchResultsMonthDayOfWeekAsync(string selectKey, string selectYear, string selectMonth, string selectDay, string fromDate, string toDate, string timeRange, string duration, string groupKey, string groupYear, string groupMonth, string groupDay, string optionString, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetSearchResultsDayOfWeek", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetSearchResultsDayOfWeekResponse")]
        RevCord.DataContracts.ReportEntities.RPTCallInfo[] GetSearchResultsDayOfWeek(string selectKey, string selectYear, string selectMonth, string selectDay, string fromDate, string toDate, string timeRange, string duration, string groupKey, string groupYear, string groupMonth, string groupDay, string optionString, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetSearchResultsDayOfWeek", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetSearchResultsDayOfWeekResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.ReportEntities.RPTCallInfo[]> GetSearchResultsDayOfWeekAsync(string selectKey, string selectYear, string selectMonth, string selectDay, string fromDate, string toDate, string timeRange, string duration, string groupKey, string groupYear, string groupMonth, string groupDay, string optionString, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetSearchResultsHour", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetSearchResultsHourResponse")]
        RevCord.DataContracts.ReportEntities.RPTCallInfo[] GetSearchResultsHour(string selectKey, string selectYear, string selectMonth, string selectDay, string fromDate, string toDate, string timeRange, string duration, string groupKey, string groupYear, string groupMonth, string groupDay, string optionString, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetSearchResultsHour", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetSearchResultsHourResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.ReportEntities.RPTCallInfo[]> GetSearchResultsHourAsync(string selectKey, string selectYear, string selectMonth, string selectDay, string fromDate, string toDate, string timeRange, string duration, string groupKey, string groupYear, string groupMonth, string groupDay, string optionString, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetCallByIdFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetCallByIdFromRecorderResponse")]
        RevCord.DataContracts.VoiceRecEntities.CallInfo GetCallByIdFromRecorder(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetCallByIdFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetCallByIdFromRecorderResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.VoiceRecEntities.CallInfo> GetCallByIdFromRecorderAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetSearchResults911", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetSearchResults911Response")]
        RevCord.DataContracts.ReportEntities.RPTCallInfo[] GetSearchResults911(string selectKey, string selectYear, string selectMonth, string selectDay, string fromDate, string toDate, string timeRange, string duration, string groupKey, string groupYear, string groupMonth, string groupDay, string optionString, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetSearchResults911", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetSearchResults911Response")]
        System.Threading.Tasks.Task<RevCord.DataContracts.ReportEntities.RPTCallInfo[]> GetSearchResults911Async(string selectKey, string selectYear, string selectMonth, string selectDay, string fromDate, string toDate, string timeRange, string duration, string groupKey, string groupYear, string groupMonth, string groupDay, string optionString, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetCallAuditSearchResults", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetCallAuditSearchResultsResponse")]
        RevCord.DataContracts.ReportEntities.RPTCallInfo[] GetCallAuditSearchResults(string fromDate, string toDate, string optionStr, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetCallAuditSearchResults", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetCallAuditSearchResultsResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.ReportEntities.RPTCallInfo[]> GetCallAuditSearchResultsAsync(string fromDate, string toDate, string optionStr, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetCallsNotAuditedSearchResults", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetCallsNotAuditedSearchResultsResponse")]
        RevCord.DataContracts.ReportEntities.RPTCallInfo[] GetCallsNotAuditedSearchResults(string fromDate, string toDate, string optionStr, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetCallsNotAuditedSearchResults", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetCallsNotAuditedSearchResultsResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.ReportEntities.RPTCallInfo[]> GetCallsNotAuditedSearchResultsAsync(string fromDate, string toDate, string optionStr, RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetRPTEvaluationReportFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetRPTEvaluationReportFromRecorderResponse")]
        RevCord.DataContracts.ReportEntities.RPTEvaluation[] GetRPTEvaluationReportFromRecorder(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string criteria, int userId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetRPTEvaluationReportFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetRPTEvaluationReportFromRecorderResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.ReportEntities.RPTEvaluation[]> GetRPTEvaluationReportFromRecorderAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string criteria, int userId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/UpdateConfiguration", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/UpdateConfigurationResponse")]
        bool UpdateConfiguration(string ipAddress);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/UpdateConfiguration", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/UpdateConfigurationResponse")]
        System.Threading.Tasks.Task<bool> UpdateConfigurationAsync(string ipAddress);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/CheckDBConnection", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/CheckDBConnectionResponse")]
        bool CheckDBConnection(string ipAddress);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/CheckDBConnection", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/CheckDBConnectionResponse")]
        System.Threading.Tasks.Task<bool> CheckDBConnectionAsync(string ipAddress);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetAudioChannelsFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetAudioChannelsFromRecorderResponse")]
        RevCord.DataContracts.VoiceRecEntities.Channel[] GetAudioChannelsFromRecorder(RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetAudioChannelsFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetAudioChannelsFromRecorderResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.VoiceRecEntities.Channel[]> GetAudioChannelsFromRecorderAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetAudioChannelFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetAudioChannelFromRecorderResponse")]
        RevCord.DataContracts.VoiceRecEntities.Channel GetAudioChannelFromRecorder(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int channelId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/GetAudioChannelFromRecorder", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/GetAudioChannelFromRecorderResponse")]
        System.Threading.Tasks.Task<RevCord.DataContracts.VoiceRecEntities.Channel> GetAudioChannelFromRecorderAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int channelId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/DeleteAudioChannels", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/DeleteAudioChannelsResponse")]
        int DeleteAudioChannels(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int[] channelIds);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/DeleteAudioChannels", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/DeleteAudioChannelsResponse")]
        System.Threading.Tasks.Task<int> DeleteAudioChannelsAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int[] channelIds);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/CreateAudioChannels", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/CreateAudioChannelsResponse")]
        int CreateAudioChannels(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int localRecorderId, int NoOfAnalogChannels, int NoOfVoIPChannels, string gatewayId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/CreateAudioChannels", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/CreateAudioChannelsResponse")]
        System.Threading.Tasks.Task<int> CreateAudioChannelsAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int localRecorderId, int NoOfAnalogChannels, int NoOfVoIPChannels, string gatewayId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/UpdateAudioChannel", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/UpdateAudioChannelResponse")]
        int UpdateAudioChannel(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, RevCord.DataContracts.VoiceRecEntities.Channel channel);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevEnterpriseSvc/UpdateAudioChannel", ReplyAction="http://tempuri.org/IRevEnterpriseSvc/UpdateAudioChannelResponse")]
        System.Threading.Tasks.Task<int> UpdateAudioChannelAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, RevCord.DataContracts.VoiceRecEntities.Channel channel);
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetUserActivities", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class GetUserActivitiesRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public int userId;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public System.DateTime startDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public System.DateTime endDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public int pageSize;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public int pageIndex;
        
        public GetUserActivitiesRequest() {
        }
        
        public GetUserActivitiesRequest(int userId, System.DateTime startDate, System.DateTime endDate, int pageSize, int pageIndex) {
            this.userId = userId;
            this.startDate = startDate;
            this.endDate = endDate;
            this.pageSize = pageSize;
            this.pageIndex = pageIndex;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetUserActivitiesResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class GetUserActivitiesResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public RevCord.DataContracts.UserManagement.UserActivity[] GetUserActivitiesResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public int totalPages;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long totalRecords;
        
        public GetUserActivitiesResponse() {
        }
        
        public GetUserActivitiesResponse(RevCord.DataContracts.UserManagement.UserActivity[] GetUserActivitiesResult, int totalPages, long totalRecords) {
            this.GetUserActivitiesResult = GetUserActivitiesResult;
            this.totalPages = totalPages;
            this.totalRecords = totalRecords;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SearchCallsPrimaryDB", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class SearchCallsPrimaryDBRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public int pageSize;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public int pageIndex;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public string startDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public string endDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public string startTime;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=5)]
        public string endTime;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=6)]
        public bool isGlobalSearch;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=7)]
        public string duration;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=8)]
        public string criteria;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=9)]
        public RevCord.DataContracts.VoiceRecEntities.Recorder recorder;
        
        public SearchCallsPrimaryDBRequest() {
        }
        
        public SearchCallsPrimaryDBRequest(int pageSize, int pageIndex, string startDate, string endDate, string startTime, string endTime, bool isGlobalSearch, string duration, string criteria, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            this.pageSize = pageSize;
            this.pageIndex = pageIndex;
            this.startDate = startDate;
            this.endDate = endDate;
            this.startTime = startTime;
            this.endTime = endTime;
            this.isGlobalSearch = isGlobalSearch;
            this.duration = duration;
            this.criteria = criteria;
            this.recorder = recorder;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SearchCallsPrimaryDBResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class SearchCallsPrimaryDBResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public RevCord.DataContracts.VoiceRecEntities.CallInfo[] SearchCallsPrimaryDBResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public int totalPages;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long totalRecords;
        
        public SearchCallsPrimaryDBResponse() {
        }
        
        public SearchCallsPrimaryDBResponse(RevCord.DataContracts.VoiceRecEntities.CallInfo[] SearchCallsPrimaryDBResult, int totalPages, long totalRecords) {
            this.SearchCallsPrimaryDBResult = SearchCallsPrimaryDBResult;
            this.totalPages = totalPages;
            this.totalRecords = totalRecords;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SearchRandomCallsPrimaryDB", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class SearchRandomCallsPrimaryDBRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public int pageSize;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public int pageIndex;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public string startDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public string endDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public string startTime;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=5)]
        public string endTime;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=6)]
        public bool isGlobalSearch;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=7)]
        public bool isRandom;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=8)]
        public bool isPercentage;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=9)]
        public string duration;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=10)]
        public string criteria;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=11)]
        public int noOfCalls;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=12)]
        public RevCord.DataContracts.VoiceRecEntities.Recorder recorder;
        
        public SearchRandomCallsPrimaryDBRequest() {
        }
        
        public SearchRandomCallsPrimaryDBRequest(int pageSize, int pageIndex, string startDate, string endDate, string startTime, string endTime, bool isGlobalSearch, bool isRandom, bool isPercentage, string duration, string criteria, int noOfCalls, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            this.pageSize = pageSize;
            this.pageIndex = pageIndex;
            this.startDate = startDate;
            this.endDate = endDate;
            this.startTime = startTime;
            this.endTime = endTime;
            this.isGlobalSearch = isGlobalSearch;
            this.isRandom = isRandom;
            this.isPercentage = isPercentage;
            this.duration = duration;
            this.criteria = criteria;
            this.noOfCalls = noOfCalls;
            this.recorder = recorder;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SearchRandomCallsPrimaryDBResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class SearchRandomCallsPrimaryDBResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public RevCord.DataContracts.VoiceRecEntities.CallInfo[] SearchRandomCallsPrimaryDBResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public int totalPages;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long totalRecords;
        
        public SearchRandomCallsPrimaryDBResponse() {
        }
        
        public SearchRandomCallsPrimaryDBResponse(RevCord.DataContracts.VoiceRecEntities.CallInfo[] SearchRandomCallsPrimaryDBResult, int totalPages, long totalRecords) {
            this.SearchRandomCallsPrimaryDBResult = SearchRandomCallsPrimaryDBResult;
            this.totalPages = totalPages;
            this.totalRecords = totalRecords;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SearchCallsChainedDBs", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class SearchCallsChainedDBsRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public int pageSize;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public int pageIndex;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public string startDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public string endDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public string startTime;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=5)]
        public string endTime;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=6)]
        public bool isGlobalSearch;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=7)]
        public string duration;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=8)]
        public string criteria;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=9)]
        public RevCord.DataContracts.VoiceRecEntities.Recorder recorder;
        
        public SearchCallsChainedDBsRequest() {
        }
        
        public SearchCallsChainedDBsRequest(int pageSize, int pageIndex, string startDate, string endDate, string startTime, string endTime, bool isGlobalSearch, string duration, string criteria, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            this.pageSize = pageSize;
            this.pageIndex = pageIndex;
            this.startDate = startDate;
            this.endDate = endDate;
            this.startTime = startTime;
            this.endTime = endTime;
            this.isGlobalSearch = isGlobalSearch;
            this.duration = duration;
            this.criteria = criteria;
            this.recorder = recorder;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SearchCallsChainedDBsResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class SearchCallsChainedDBsResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public RevCord.DataContracts.VoiceRecEntities.CallInfo[] SearchCallsChainedDBsResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public int totalPages;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long totalRecords;
        
        public SearchCallsChainedDBsResponse() {
        }
        
        public SearchCallsChainedDBsResponse(RevCord.DataContracts.VoiceRecEntities.CallInfo[] SearchCallsChainedDBsResult, int totalPages, long totalRecords) {
            this.SearchCallsChainedDBsResult = SearchCallsChainedDBsResult;
            this.totalPages = totalPages;
            this.totalRecords = totalRecords;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="InsertBookmarkAndGetByCallId", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class InsertBookmarkAndGetByCallIdRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public RevCord.DataContracts.VoiceRecEntities.Recorder recorder;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public RevCord.DataContracts.VoiceRecEntities.Bookmark bookmark;
        
        public InsertBookmarkAndGetByCallIdRequest() {
        }
        
        public InsertBookmarkAndGetByCallIdRequest(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, RevCord.DataContracts.VoiceRecEntities.Bookmark bookmark) {
            this.recorder = recorder;
            this.bookmark = bookmark;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="InsertBookmarkAndGetByCallIdResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class InsertBookmarkAndGetByCallIdResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public string[] InsertBookmarkAndGetByCallIdResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public int rowsAffected;
        
        public InsertBookmarkAndGetByCallIdResponse() {
        }
        
        public InsertBookmarkAndGetByCallIdResponse(string[] InsertBookmarkAndGetByCallIdResult, int rowsAffected) {
            this.InsertBookmarkAndGetByCallIdResult = InsertBookmarkAndGetByCallIdResult;
            this.rowsAffected = rowsAffected;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="UpdateBookmarkAndGetByCallId", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class UpdateBookmarkAndGetByCallIdRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public RevCord.DataContracts.VoiceRecEntities.Recorder recorder;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public RevCord.DataContracts.VoiceRecEntities.Bookmark bookmark;
        
        public UpdateBookmarkAndGetByCallIdRequest() {
        }
        
        public UpdateBookmarkAndGetByCallIdRequest(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, RevCord.DataContracts.VoiceRecEntities.Bookmark bookmark) {
            this.recorder = recorder;
            this.bookmark = bookmark;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="UpdateBookmarkAndGetByCallIdResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class UpdateBookmarkAndGetByCallIdResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public string[] UpdateBookmarkAndGetByCallIdResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public int rowsAffected;
        
        public UpdateBookmarkAndGetByCallIdResponse() {
        }
        
        public UpdateBookmarkAndGetByCallIdResponse(string[] UpdateBookmarkAndGetByCallIdResult, int rowsAffected) {
            this.UpdateBookmarkAndGetByCallIdResult = UpdateBookmarkAndGetByCallIdResult;
            this.rowsAffected = rowsAffected;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetEvaluations", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class GetEvaluationsRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public RevCord.DataContracts.VoiceRecEntities.Recorder recorder;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public string whereClause;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public int userId;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public int pageIndex;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public int pageSize;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=5)]
        public bool SharedRequired;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=6)]
        public bool IsEvaluatorSearch;
        
        public GetEvaluationsRequest() {
        }
        
        public GetEvaluationsRequest(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string whereClause, int userId, int pageIndex, int pageSize, bool SharedRequired, bool IsEvaluatorSearch) {
            this.recorder = recorder;
            this.whereClause = whereClause;
            this.userId = userId;
            this.pageIndex = pageIndex;
            this.pageSize = pageSize;
            this.SharedRequired = SharedRequired;
            this.IsEvaluatorSearch = IsEvaluatorSearch;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetEvaluationsResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class GetEvaluationsResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public RevCord.DataContracts.DTO.CallEvaluationDTO[] GetEvaluationsResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public int totalPages;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public int totalRecords;
        
        public GetEvaluationsResponse() {
        }
        
        public GetEvaluationsResponse(RevCord.DataContracts.DTO.CallEvaluationDTO[] GetEvaluationsResult, int totalPages, int totalRecords) {
            this.GetEvaluationsResult = GetEvaluationsResult;
            this.totalPages = totalPages;
            this.totalRecords = totalRecords;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetEnterpriseAssociatedEvaluations", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class GetEnterpriseAssociatedEvaluationsRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public RevCord.DataContracts.VoiceRecEntities.Recorder recorder;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public string whereClause;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public int userId;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public int pageIndex;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public int pageSize;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=5)]
        public bool SharedRequired;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=6)]
        public bool IsEvaluatorSearch;
        
        public GetEnterpriseAssociatedEvaluationsRequest() {
        }
        
        public GetEnterpriseAssociatedEvaluationsRequest(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string whereClause, int userId, int pageIndex, int pageSize, bool SharedRequired, bool IsEvaluatorSearch) {
            this.recorder = recorder;
            this.whereClause = whereClause;
            this.userId = userId;
            this.pageIndex = pageIndex;
            this.pageSize = pageSize;
            this.SharedRequired = SharedRequired;
            this.IsEvaluatorSearch = IsEvaluatorSearch;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetEnterpriseAssociatedEvaluationsResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class GetEnterpriseAssociatedEvaluationsResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public RevCord.DataContracts.DTO.CallEvaluationDTO[] GetEnterpriseAssociatedEvaluationsResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public int totalPages;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public int totalRecords;
        
        public GetEnterpriseAssociatedEvaluationsResponse() {
        }
        
        public GetEnterpriseAssociatedEvaluationsResponse(RevCord.DataContracts.DTO.CallEvaluationDTO[] GetEnterpriseAssociatedEvaluationsResult, int totalPages, int totalRecords) {
            this.GetEnterpriseAssociatedEvaluationsResult = GetEnterpriseAssociatedEvaluationsResult;
            this.totalPages = totalPages;
            this.totalRecords = totalRecords;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="PerformActionAndGetEnterpriseEvaluationDTO", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class PerformActionAndGetEnterpriseEvaluationDTORequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public RevCord.DataContracts.VoiceRecEntities.Recorder recorder;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public int actionToPerformOnRecorder;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public string callSurveyIds;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public string whereClause;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public bool SharedRequired;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=5)]
        public bool IsEvaluatorSearch;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=6)]
        public int action;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=7)]
        public int actionValue;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=8)]
        public int userId;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=9)]
        public System.Nullable<System.DateTime> ActionDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=10)]
        public int pageIndex;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=11)]
        public int pageSize;
        
        public PerformActionAndGetEnterpriseEvaluationDTORequest() {
        }
        
        public PerformActionAndGetEnterpriseEvaluationDTORequest(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int actionToPerformOnRecorder, string callSurveyIds, string whereClause, bool SharedRequired, bool IsEvaluatorSearch, int action, int actionValue, int userId, System.Nullable<System.DateTime> ActionDate, int pageIndex, int pageSize) {
            this.recorder = recorder;
            this.actionToPerformOnRecorder = actionToPerformOnRecorder;
            this.callSurveyIds = callSurveyIds;
            this.whereClause = whereClause;
            this.SharedRequired = SharedRequired;
            this.IsEvaluatorSearch = IsEvaluatorSearch;
            this.action = action;
            this.actionValue = actionValue;
            this.userId = userId;
            this.ActionDate = ActionDate;
            this.pageIndex = pageIndex;
            this.pageSize = pageSize;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="PerformActionAndGetEnterpriseEvaluationDTOResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class PerformActionAndGetEnterpriseEvaluationDTOResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public RevCord.DataContracts.DTO.CallEvaluationDTO[] PerformActionAndGetEnterpriseEvaluationDTOResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public int totalPages;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public int totalRecords;
        
        public PerformActionAndGetEnterpriseEvaluationDTOResponse() {
        }
        
        public PerformActionAndGetEnterpriseEvaluationDTOResponse(RevCord.DataContracts.DTO.CallEvaluationDTO[] PerformActionAndGetEnterpriseEvaluationDTOResult, int totalPages, int totalRecords) {
            this.PerformActionAndGetEnterpriseEvaluationDTOResult = PerformActionAndGetEnterpriseEvaluationDTOResult;
            this.totalPages = totalPages;
            this.totalRecords = totalRecords;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="ShareUnshareAndGetEvaluationDTO", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class ShareUnshareAndGetEvaluationDTORequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public RevCord.DataContracts.VoiceRecEntities.Recorder recorder;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public int actionToPerformOnRecorder;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public string callSurveyIds;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public string whereClause;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public int action;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=5)]
        public int actionValue;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=6)]
        public int userId;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=7)]
        public string shareWith;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=8)]
        public bool isSharedEvaluatorRetains;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=9)]
        public System.Nullable<System.DateTime> ActionDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=10)]
        public int pageIndex;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=11)]
        public int pageSize;
        
        public ShareUnshareAndGetEvaluationDTORequest() {
        }
        
        public ShareUnshareAndGetEvaluationDTORequest(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int actionToPerformOnRecorder, string callSurveyIds, string whereClause, int action, int actionValue, int userId, string shareWith, bool isSharedEvaluatorRetains, System.Nullable<System.DateTime> ActionDate, int pageIndex, int pageSize) {
            this.recorder = recorder;
            this.actionToPerformOnRecorder = actionToPerformOnRecorder;
            this.callSurveyIds = callSurveyIds;
            this.whereClause = whereClause;
            this.action = action;
            this.actionValue = actionValue;
            this.userId = userId;
            this.shareWith = shareWith;
            this.isSharedEvaluatorRetains = isSharedEvaluatorRetains;
            this.ActionDate = ActionDate;
            this.pageIndex = pageIndex;
            this.pageSize = pageSize;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="ShareUnshareAndGetEvaluationDTOResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class ShareUnshareAndGetEvaluationDTOResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public RevCord.DataContracts.DTO.CallEvaluationDTO[] ShareUnshareAndGetEvaluationDTOResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public int totalPages;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public int totalRecords;
        
        public ShareUnshareAndGetEvaluationDTOResponse() {
        }
        
        public ShareUnshareAndGetEvaluationDTOResponse(RevCord.DataContracts.DTO.CallEvaluationDTO[] ShareUnshareAndGetEvaluationDTOResult, int totalPages, int totalRecords) {
            this.ShareUnshareAndGetEvaluationDTOResult = ShareUnshareAndGetEvaluationDTOResult;
            this.totalPages = totalPages;
            this.totalRecords = totalRecords;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SearchRecordedCalls", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class SearchRecordedCallsRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public int pageSize;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public int pageIndex;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public string startDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public string endDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public string startTime;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=5)]
        public string endTime;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=6)]
        public bool isGlobalSearch;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=7)]
        public string duration;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=8)]
        public string criteria;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=9)]
        public RevCord.DataContracts.VoiceRecEntities.Recorder recorder;
        
        public SearchRecordedCallsRequest() {
        }
        
        public SearchRecordedCallsRequest(int pageSize, int pageIndex, string startDate, string endDate, string startTime, string endTime, bool isGlobalSearch, string duration, string criteria, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            this.pageSize = pageSize;
            this.pageIndex = pageIndex;
            this.startDate = startDate;
            this.endDate = endDate;
            this.startTime = startTime;
            this.endTime = endTime;
            this.isGlobalSearch = isGlobalSearch;
            this.duration = duration;
            this.criteria = criteria;
            this.recorder = recorder;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SearchRecordedCallsResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class SearchRecordedCallsResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public RevCord.DataContracts.VoiceRecEntities.CallInfo[] SearchRecordedCallsResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public int totalPages;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long totalRecords;
        
        public SearchRecordedCallsResponse() {
        }
        
        public SearchRecordedCallsResponse(RevCord.DataContracts.VoiceRecEntities.CallInfo[] SearchRecordedCallsResult, int totalPages, long totalRecords) {
            this.SearchRecordedCallsResult = SearchRecordedCallsResult;
            this.totalPages = totalPages;
            this.totalRecords = totalRecords;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public interface IRevEnterpriseSvcChannel : RevCord.BusinessLogic.RevcordEnterpriseSvc.IRevEnterpriseSvc, System.ServiceModel.IClientChannel {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public partial class RevEnterpriseSvcClient : System.ServiceModel.ClientBase<RevCord.BusinessLogic.RevcordEnterpriseSvc.IRevEnterpriseSvc>, RevCord.BusinessLogic.RevcordEnterpriseSvc.IRevEnterpriseSvc {
        
        public RevEnterpriseSvcClient() {
        }
        
        public RevEnterpriseSvcClient(string endpointConfigurationName) : 
                base(endpointConfigurationName) {
        }
        
        public RevEnterpriseSvcClient(string endpointConfigurationName, string remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public RevEnterpriseSvcClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public RevEnterpriseSvcClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress) {
        }
        
        public int CallAuditSave(string callId, int userNum, string ipAddress, bool isSaved) {
            return base.Channel.CallAuditSave(callId, userNum, ipAddress, isSaved);
        }
        
        public System.Threading.Tasks.Task<int> CallAuditSaveAsync(string callId, int userNum, string ipAddress, bool isSaved) {
            return base.Channel.CallAuditSaveAsync(callId, userNum, ipAddress, isSaved);
        }
        
        public RevCord.DataContracts.ReportEntities.CallAuditReport[] GetCallAuditTrail(System.DateTime startDate, System.DateTime endDate, string callId) {
            return base.Channel.GetCallAuditTrail(startDate, endDate, callId);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.ReportEntities.CallAuditReport[]> GetCallAuditTrailAsync(System.DateTime startDate, System.DateTime endDate, string callId) {
            return base.Channel.GetCallAuditTrailAsync(startDate, endDate, callId);
        }
        
        public RevCord.DataContracts.ReportEntities.CallAuditReport[] GetAuditedCallsByExtension(System.DateTime startDate, System.DateTime endDate, int ext) {
            return base.Channel.GetAuditedCallsByExtension(startDate, endDate, ext);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.ReportEntities.CallAuditReport[]> GetAuditedCallsByExtensionAsync(System.DateTime startDate, System.DateTime endDate, int ext) {
            return base.Channel.GetAuditedCallsByExtensionAsync(startDate, endDate, ext);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        RevCord.BusinessLogic.RevcordEnterpriseSvc.GetUserActivitiesResponse RevCord.BusinessLogic.RevcordEnterpriseSvc.IRevEnterpriseSvc.GetUserActivities(RevCord.BusinessLogic.RevcordEnterpriseSvc.GetUserActivitiesRequest request) {
            return base.Channel.GetUserActivities(request);
        }
        
        public RevCord.DataContracts.UserManagement.UserActivity[] GetUserActivities(int userId, System.DateTime startDate, System.DateTime endDate, int pageSize, int pageIndex, out int totalPages, out long totalRecords) {
            RevCord.BusinessLogic.RevcordEnterpriseSvc.GetUserActivitiesRequest inValue = new RevCord.BusinessLogic.RevcordEnterpriseSvc.GetUserActivitiesRequest();
            inValue.userId = userId;
            inValue.startDate = startDate;
            inValue.endDate = endDate;
            inValue.pageSize = pageSize;
            inValue.pageIndex = pageIndex;
            RevCord.BusinessLogic.RevcordEnterpriseSvc.GetUserActivitiesResponse retVal = ((RevCord.BusinessLogic.RevcordEnterpriseSvc.IRevEnterpriseSvc)(this)).GetUserActivities(inValue);
            totalPages = retVal.totalPages;
            totalRecords = retVal.totalRecords;
            return retVal.GetUserActivitiesResult;
        }
        
        public System.Threading.Tasks.Task<RevCord.BusinessLogic.RevcordEnterpriseSvc.GetUserActivitiesResponse> GetUserActivitiesAsync(RevCord.BusinessLogic.RevcordEnterpriseSvc.GetUserActivitiesRequest request) {
            return base.Channel.GetUserActivitiesAsync(request);
        }
        
        public RevCord.DataContracts.VoiceRecEntities.CallInfo[] GetCallsByLocation(RevCord.DataContracts.DTO.CallInfoSearchCriteriaDTO callInfoSearchCriteriaDTO) {
            return base.Channel.GetCallsByLocation(callInfoSearchCriteriaDTO);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.VoiceRecEntities.CallInfo[]> GetCallsByLocationAsync(RevCord.DataContracts.DTO.CallInfoSearchCriteriaDTO callInfoSearchCriteriaDTO) {
            return base.Channel.GetCallsByLocationAsync(callInfoSearchCriteriaDTO);
        }
        
        public RevCord.DataContracts.DTO.DALMediaResponse GetDefaultSearchResultsDTO(int pageSize, int pageIndex, string duration, string criteria, string startDate, string endDate, string startTime, string endTime, bool isGlobal, string customSpParam, int recId, string recName) {
            return base.Channel.GetDefaultSearchResultsDTO(pageSize, pageIndex, duration, criteria, startDate, endDate, startTime, endTime, isGlobal, customSpParam, recId, recName);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.DTO.DALMediaResponse> GetDefaultSearchResultsDTOAsync(int pageSize, int pageIndex, string duration, string criteria, string startDate, string endDate, string startTime, string endTime, bool isGlobal, string customSpParam, int recId, string recName) {
            return base.Channel.GetDefaultSearchResultsDTOAsync(pageSize, pageIndex, duration, criteria, startDate, endDate, startTime, endTime, isGlobal, customSpParam, recId, recName);
        }
        
        public RevCord.DataContracts.DTO.DALMediaResponse GetAdvanceSearchResultsDTO(int pageSize, int pageIndex, string duration, string criteria, string startDate, string endDate, string startTime, string endTime, bool isGlobal, string customSpParam, int recId, string recName) {
            return base.Channel.GetAdvanceSearchResultsDTO(pageSize, pageIndex, duration, criteria, startDate, endDate, startTime, endTime, isGlobal, customSpParam, recId, recName);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.DTO.DALMediaResponse> GetAdvanceSearchResultsDTOAsync(int pageSize, int pageIndex, string duration, string criteria, string startDate, string endDate, string startTime, string endTime, bool isGlobal, string customSpParam, int recId, string recName) {
            return base.Channel.GetAdvanceSearchResultsDTOAsync(pageSize, pageIndex, duration, criteria, startDate, endDate, startTime, endTime, isGlobal, customSpParam, recId, recName);
        }
        
        public RevCord.DataContracts.DTO.DALMediaResponse PerformSearchChainedDBs(int pageSize, int pageIndex, string duration, string criteria, RevCord.DataContracts.Criteria.CallCriteria callCriteria, string customSpParam, int recId, string recName) {
            return base.Channel.PerformSearchChainedDBs(pageSize, pageIndex, duration, criteria, callCriteria, customSpParam, recId, recName);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.DTO.DALMediaResponse> PerformSearchChainedDBsAsync(int pageSize, int pageIndex, string duration, string criteria, RevCord.DataContracts.Criteria.CallCriteria callCriteria, string customSpParam, int recId, string recName) {
            return base.Channel.PerformSearchChainedDBsAsync(pageSize, pageIndex, duration, criteria, callCriteria, customSpParam, recId, recName);
        }
        
        public RevCord.DataContracts.DTO.DALMediaResponse GetDefaultQASearchResultsDTO(int tenantId, int pageSize, int pageIndex, string duration, string criteria, RevCord.DataContracts.Criteria.CallCriteria callCriteria, string customSpParam) {
            return base.Channel.GetDefaultQASearchResultsDTO(tenantId, pageSize, pageIndex, duration, criteria, callCriteria, customSpParam);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.DTO.DALMediaResponse> GetDefaultQASearchResultsDTOAsync(int tenantId, int pageSize, int pageIndex, string duration, string criteria, RevCord.DataContracts.Criteria.CallCriteria callCriteria, string customSpParam) {
            return base.Channel.GetDefaultQASearchResultsDTOAsync(tenantId, pageSize, pageIndex, duration, criteria, callCriteria, customSpParam);
        }
        
        public RevCord.DataContracts.DTO.DALMediaResponse GetAdvanceQASearchResultsDTO(int tenantId, int pageSize, int pageIndex, string duration, string criteria, RevCord.DataContracts.Criteria.CallCriteria callCriteria, string customSpParam) {
            return base.Channel.GetAdvanceQASearchResultsDTO(tenantId, pageSize, pageIndex, duration, criteria, callCriteria, customSpParam);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.DTO.DALMediaResponse> GetAdvanceQASearchResultsDTOAsync(int tenantId, int pageSize, int pageIndex, string duration, string criteria, RevCord.DataContracts.Criteria.CallCriteria callCriteria, string customSpParam) {
            return base.Channel.GetAdvanceQASearchResultsDTOAsync(tenantId, pageSize, pageIndex, duration, criteria, callCriteria, customSpParam);
        }
        
        public RevCord.DataContracts.DTO.DALMediaResponse PerformQASearchChainedDBs(int tenantId, int pageSize, int pageIndex, string duration, string criteria, RevCord.DataContracts.Criteria.CallCriteria callCriteria, string customSpParam) {
            return base.Channel.PerformQASearchChainedDBs(tenantId, pageSize, pageIndex, duration, criteria, callCriteria, customSpParam);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.DTO.DALMediaResponse> PerformQASearchChainedDBsAsync(int tenantId, int pageSize, int pageIndex, string duration, string criteria, RevCord.DataContracts.Criteria.CallCriteria callCriteria, string customSpParam) {
            return base.Channel.PerformQASearchChainedDBsAsync(tenantId, pageSize, pageIndex, duration, criteria, callCriteria, customSpParam);
        }
        
        public RevCord.DataContracts.VoiceRecEntities.UserExtensionInfo[] GetUserExtensionInfos(int recId) {
            return base.Channel.GetUserExtensionInfos(recId);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.VoiceRecEntities.UserExtensionInfo[]> GetUserExtensionInfosAsync(int recId) {
            return base.Channel.GetUserExtensionInfosAsync(recId);
        }
        
        public RevCord.DataContracts.Response.UserManagementResponse GetAppUsers(RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetAppUsers(recorder);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.Response.UserManagementResponse> GetAppUsersAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetAppUsersAsync(recorder);
        }
        
        public RevCord.DataContracts.Response.UserManagementResponse GetAppUserAccount(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, long uId) {
            return base.Channel.GetAppUserAccount(recorder, uId);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.Response.UserManagementResponse> GetAppUserAccountAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, long uId) {
            return base.Channel.GetAppUserAccountAsync(recorder, uId);
        }
        
        public int RecoverAppUserAccount(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string userIds) {
            return base.Channel.RecoverAppUserAccount(recorder, userIds);
        }
        
        public System.Threading.Tasks.Task<int> RecoverAppUserAccountAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string userIds) {
            return base.Channel.RecoverAppUserAccountAsync(recorder, userIds);
        }
        
        public RevCord.DataContracts.UserManagement.User[] UpdateAppUserAccount(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, RevCord.DataContracts.UserManagement.User appUser) {
            return base.Channel.UpdateAppUserAccount(recorder, appUser);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.UserManagement.User[]> UpdateAppUserAccountAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, RevCord.DataContracts.UserManagement.User appUser) {
            return base.Channel.UpdateAppUserAccountAsync(recorder, appUser);
        }
        
        public RevCord.DataContracts.Response.UserManagementResponse DeleteAppUserAccount(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int id) {
            return base.Channel.DeleteAppUserAccount(recorder, id);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.Response.UserManagementResponse> DeleteAppUserAccountAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int id) {
            return base.Channel.DeleteAppUserAccountAsync(recorder, id);
        }
        
        public bool SaveAssignedGroup(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, long uId, int gId) {
            return base.Channel.SaveAssignedGroup(recorder, uId, gId);
        }
        
        public System.Threading.Tasks.Task<bool> SaveAssignedGroupAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, long uId, int gId) {
            return base.Channel.SaveAssignedGroupAsync(recorder, uId, gId);
        }
        
        public bool DeleteUserGroup(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, RevCord.DataContracts.UserManagement.UserGroup uGroup) {
            return base.Channel.DeleteUserGroup(recorder, uGroup);
        }
        
        public System.Threading.Tasks.Task<bool> DeleteUserGroupAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, RevCord.DataContracts.UserManagement.UserGroup uGroup) {
            return base.Channel.DeleteUserGroupAsync(recorder, uGroup);
        }
        
        public RevCord.DataContracts.UserManagement.UserInfoLite[] GetUserData(RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetUserData(recorder);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.UserManagement.UserInfoLite[]> GetUserDataAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetUserDataAsync(recorder);
        }
        
        public RevCord.DataContracts.UserManagement.User[] FetchAllActiveUsers() {
            return base.Channel.FetchAllActiveUsers();
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.UserManagement.User[]> FetchAllActiveUsersAsync() {
            return base.Channel.FetchAllActiveUsersAsync();
        }
        
        public RevCord.DataContracts.DTO.TreeViewDataDTO[] GetTreeViewFromRecorder(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int userNum, string userId, int authNum, string authType, int type, bool getOnlyAudioChannels, int selectType) {
            return base.Channel.GetTreeViewFromRecorder(recorder, userNum, userId, authNum, authType, type, getOnlyAudioChannels, selectType);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.DTO.TreeViewDataDTO[]> GetTreeViewFromRecorderAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int userNum, string userId, int authNum, string authType, int type, bool getOnlyAudioChannels, int selectType) {
            return base.Channel.GetTreeViewFromRecorderAsync(recorder, userNum, userId, authNum, authType, type, getOnlyAudioChannels, selectType);
        }
        
        public RevCord.DataContracts.ViewModelEntities.TreeviewData[] GetGroupsTree(int userNum, string userId, int authNum, string authType, int type, int userType, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetGroupsTree(userNum, userId, authNum, authType, type, userType, recorder);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.ViewModelEntities.TreeviewData[]> GetGroupsTreeAsync(int userNum, string userId, int authNum, string authType, int type, int userType, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetGroupsTreeAsync(userNum, userId, authNum, authType, type, userType, recorder);
        }
        
        public RevCord.DataContracts.ViewModelEntities.TreeviewData[] GetGroupsTreeFromRecorder(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int userNum, string userId, int authNum, string authType, int type, int userType) {
            return base.Channel.GetGroupsTreeFromRecorder(recorder, userNum, userId, authNum, authType, type, userType);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.ViewModelEntities.TreeviewData[]> GetGroupsTreeFromRecorderAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int userNum, string userId, int authNum, string authType, int type, int userType) {
            return base.Channel.GetGroupsTreeFromRecorderAsync(recorder, userNum, userId, authNum, authType, type, userType);
        }
        
        public RevCord.DataContracts.ViewModelEntities.TreeviewData[] GetGroupsTreeNonAdminFromRecorder(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int userNum, string userId, int authNum, string authType, int type, int userType) {
            return base.Channel.GetGroupsTreeNonAdminFromRecorder(recorder, userNum, userId, authNum, authType, type, userType);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.ViewModelEntities.TreeviewData[]> GetGroupsTreeNonAdminFromRecorderAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int userNum, string userId, int authNum, string authType, int type, int userType) {
            return base.Channel.GetGroupsTreeNonAdminFromRecorderAsync(recorder, userNum, userId, authNum, authType, type, userType);
        }
        
        public RevCord.DataContracts.ViewModelEntities.TreeviewData[] GetUsersTreeFromRecorder(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int userNum) {
            return base.Channel.GetUsersTreeFromRecorder(recorder, userNum);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.ViewModelEntities.TreeviewData[]> GetUsersTreeFromRecorderAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int userNum) {
            return base.Channel.GetUsersTreeFromRecorderAsync(recorder, userNum);
        }
        
        public RevCord.DataContracts.ViewModelEntities.TreeviewData[] GetInquireGroupsTreeforEvaluationReportFromRecorder(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int userNum, int selectType) {
            return base.Channel.GetInquireGroupsTreeforEvaluationReportFromRecorder(recorder, userNum, selectType);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.ViewModelEntities.TreeviewData[]> GetInquireGroupsTreeforEvaluationReportFromRecorderAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int userNum, int selectType) {
            return base.Channel.GetInquireGroupsTreeforEvaluationReportFromRecorderAsync(recorder, userNum, selectType);
        }
        
        public RevCord.DataContracts.ViewModelEntities.TreeviewData[] GetMDGroupsTreeForEvaluationReportFromRecorder(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int userNum, int selectType) {
            return base.Channel.GetMDGroupsTreeForEvaluationReportFromRecorder(recorder, userNum, selectType);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.ViewModelEntities.TreeviewData[]> GetMDGroupsTreeForEvaluationReportFromRecorderAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int userNum, int selectType) {
            return base.Channel.GetMDGroupsTreeForEvaluationReportFromRecorderAsync(recorder, userNum, selectType);
        }
        
        public RevCord.DataContracts.Messages.UMResponse GetEnterpriseUserRightsData(RevCord.DataContracts.Messages.UMRequest umRequest) {
            return base.Channel.GetEnterpriseUserRightsData(umRequest);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.Messages.UMResponse> GetEnterpriseUserRightsDataAsync(RevCord.DataContracts.Messages.UMRequest umRequest) {
            return base.Channel.GetEnterpriseUserRightsDataAsync(umRequest);
        }
        
        public RevCord.DataContracts.ViewModelEntities.TreeviewData[] GetEnterpriseUserTree(int userNum, string userId, int authNum, string authType, int type, int userType, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetEnterpriseUserTree(userNum, userId, authNum, authType, type, userType, recorder);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.ViewModelEntities.TreeviewData[]> GetEnterpriseUserTreeAsync(int userNum, string userId, int authNum, string authType, int type, int userType, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetEnterpriseUserTreeAsync(userNum, userId, authNum, authType, type, userType, recorder);
        }
        
        public RevCord.DataContracts.VoiceRecEntities.MonitorChannel[] GetChannelsToMonitorFromRecorder(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string whereClause) {
            return base.Channel.GetChannelsToMonitorFromRecorder(recorder, whereClause);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.VoiceRecEntities.MonitorChannel[]> GetChannelsToMonitorFromRecorderAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string whereClause) {
            return base.Channel.GetChannelsToMonitorFromRecorderAsync(recorder, whereClause);
        }
        
        public RevCord.DataContracts.SurveyEntities.Survey[] GetSurveysFromRecorder(RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetSurveysFromRecorder(recorder);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.SurveyEntities.Survey[]> GetSurveysFromRecorderAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetSurveysFromRecorderAsync(recorder);
        }
        
        public RevCord.DataContracts.SurveyEntities.Survey[] GetPublishedSurveysFromRecorder(RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetPublishedSurveysFromRecorder(recorder);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.SurveyEntities.Survey[]> GetPublishedSurveysFromRecorderAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetPublishedSurveysFromRecorderAsync(recorder);
        }
        
        public RevCord.DataContracts.SurveyEntities.Survey[] DeleteAndGetSurveysFromRecorder(int surveyId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.DeleteAndGetSurveysFromRecorder(surveyId, recorder);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.SurveyEntities.Survey[]> DeleteAndGetSurveysFromRecorderAsync(int surveyId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.DeleteAndGetSurveysFromRecorderAsync(surveyId, recorder);
        }
        
        public RevCord.DataContracts.SurveyEntities.Survey[] PublishAndGetSurveysFromRecorders(int surveyId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.PublishAndGetSurveysFromRecorders(surveyId, recorder);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.SurveyEntities.Survey[]> PublishAndGetSurveysFromRecordersAsync(int surveyId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.PublishAndGetSurveysFromRecordersAsync(surveyId, recorder);
        }
        
        public RevCord.DataContracts.SurveyEntities.Survey GetSurveyDetailsFromRecorder(int surveyId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetSurveyDetailsFromRecorder(surveyId, recorder);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.SurveyEntities.Survey> GetSurveyDetailsFromRecorderAsync(int surveyId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetSurveyDetailsFromRecorderAsync(surveyId, recorder);
        }
        
        public bool IsSurveyExistOnRecorder(string surveyTitle, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.IsSurveyExistOnRecorder(surveyTitle, recorder);
        }
        
        public System.Threading.Tasks.Task<bool> IsSurveyExistOnRecorderAsync(string surveyTitle, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.IsSurveyExistOnRecorderAsync(surveyTitle, recorder);
        }
        
        public RevCord.DataContracts.SurveyEntities.Survey CreateSurveyOnRecorder(RevCord.DataContracts.SurveyEntities.Survey survey, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.CreateSurveyOnRecorder(survey, recorder);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.SurveyEntities.Survey> CreateSurveyOnRecorderAsync(RevCord.DataContracts.SurveyEntities.Survey survey, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.CreateSurveyOnRecorderAsync(survey, recorder);
        }
        
        public long CreateQuestionOnRecorder(RevCord.DataContracts.SurveyEntities.Question sQuestion, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.CreateQuestionOnRecorder(sQuestion, recorder);
        }
        
        public System.Threading.Tasks.Task<long> CreateQuestionOnRecorderAsync(RevCord.DataContracts.SurveyEntities.Question sQuestion, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.CreateQuestionOnRecorderAsync(sQuestion, recorder);
        }
        
        public RevCord.DataContracts.SurveyEntities.SurveySection[] CreateAndGetSectionsOnRecorder(RevCord.DataContracts.SurveyEntities.SurveySection surveyGroup, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.CreateAndGetSectionsOnRecorder(surveyGroup, recorder);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.SurveyEntities.SurveySection[]> CreateAndGetSectionsOnRecorderAsync(RevCord.DataContracts.SurveyEntities.SurveySection surveyGroup, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.CreateAndGetSectionsOnRecorderAsync(surveyGroup, recorder);
        }
        
        public RevCord.DataContracts.SurveyEntities.Question[] GetQuestionsBySurveyIdFromRecorder(int surveyId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetQuestionsBySurveyIdFromRecorder(surveyId, recorder);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.SurveyEntities.Question[]> GetQuestionsBySurveyIdFromRecorderAsync(int surveyId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetQuestionsBySurveyIdFromRecorderAsync(surveyId, recorder);
        }
        
        public RevCord.DataContracts.SurveyEntities.SurveySection[] GetSectionsBySurveyIdFromRecorder(int surveyId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetSectionsBySurveyIdFromRecorder(surveyId, recorder);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.SurveyEntities.SurveySection[]> GetSectionsBySurveyIdFromRecorderAsync(int surveyId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetSectionsBySurveyIdFromRecorderAsync(surveyId, recorder);
        }
        
        public bool UpdateQuestionSectionOnRecorder(int questionId, int sectionId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.UpdateQuestionSectionOnRecorder(questionId, sectionId, recorder);
        }
        
        public System.Threading.Tasks.Task<bool> UpdateQuestionSectionOnRecorderAsync(int questionId, int sectionId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.UpdateQuestionSectionOnRecorderAsync(questionId, sectionId, recorder);
        }
        
        public RevCord.DataContracts.SurveyEntities.SurveySection[] UpdateAndGetSectionsFromRecorder(RevCord.DataContracts.SurveyEntities.SurveySection surveyGroup, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.UpdateAndGetSectionsFromRecorder(surveyGroup, recorder);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.SurveyEntities.SurveySection[]> UpdateAndGetSectionsFromRecorderAsync(RevCord.DataContracts.SurveyEntities.SurveySection surveyGroup, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.UpdateAndGetSectionsFromRecorderAsync(surveyGroup, recorder);
        }
        
        public RevCord.DataContracts.SurveyEntities.SurveySection[] DeleteAndGetSectionsFromRecorder(int sectionId, int surveyId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.DeleteAndGetSectionsFromRecorder(sectionId, surveyId, recorder);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.SurveyEntities.SurveySection[]> DeleteAndGetSectionsFromRecorderAsync(int sectionId, int surveyId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.DeleteAndGetSectionsFromRecorderAsync(sectionId, surveyId, recorder);
        }
        
        public RevCord.DataContracts.SurveyEntities.SurveySection[] AssignUnAssignQuestionsAndGetSectionsFromRecorder(int surveyId, int sectionId, string assignedQuestions, string unassignedQuestions, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.AssignUnAssignQuestionsAndGetSectionsFromRecorder(surveyId, sectionId, assignedQuestions, unassignedQuestions, recorder);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.SurveyEntities.SurveySection[]> AssignUnAssignQuestionsAndGetSectionsFromRecorderAsync(int surveyId, int sectionId, string assignedQuestions, string unassignedQuestions, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.AssignUnAssignQuestionsAndGetSectionsFromRecorderAsync(surveyId, sectionId, assignedQuestions, unassignedQuestions, recorder);
        }
        
        public RevCord.DataContracts.SurveyEntities.Survey GetSurveyFromRecorder(int surveyId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetSurveyFromRecorder(surveyId, recorder);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.SurveyEntities.Survey> GetSurveyFromRecorderAsync(int surveyId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetSurveyFromRecorderAsync(surveyId, recorder);
        }
        
        public void UpdateSurveyOnRecorder(RevCord.DataContracts.SurveyEntities.Survey survey, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            base.Channel.UpdateSurveyOnRecorder(survey, recorder);
        }
        
        public System.Threading.Tasks.Task UpdateSurveyOnRecorderAsync(RevCord.DataContracts.SurveyEntities.Survey survey, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.UpdateSurveyOnRecorderAsync(survey, recorder);
        }
        
        public long UpdateQuestionOnRecorder(RevCord.DataContracts.SurveyEntities.Question eQuestion, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.UpdateQuestionOnRecorder(eQuestion, recorder);
        }
        
        public System.Threading.Tasks.Task<long> UpdateQuestionOnRecorderAsync(RevCord.DataContracts.SurveyEntities.Question eQuestion, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.UpdateQuestionOnRecorderAsync(eQuestion, recorder);
        }
        
        public void DeleteQuestionFromRecorder(long id, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            base.Channel.DeleteQuestionFromRecorder(id, recorder);
        }
        
        public System.Threading.Tasks.Task DeleteQuestionFromRecorderAsync(long id, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.DeleteQuestionFromRecorderAsync(id, recorder);
        }
        
        public void UpdateIsPublishedOnRecorder(int surveyId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            base.Channel.UpdateIsPublishedOnRecorder(surveyId, recorder);
        }
        
        public System.Threading.Tasks.Task UpdateIsPublishedOnRecorderAsync(int surveyId, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.UpdateIsPublishedOnRecorderAsync(surveyId, recorder);
        }
        
        public long UpdateQuestionsOrderOnRecorder(RevCord.DataContracts.SurveyEntities.Question[] eRequest, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.UpdateQuestionsOrderOnRecorder(eRequest, recorder);
        }
        
        public System.Threading.Tasks.Task<long> UpdateQuestionsOrderOnRecorderAsync(RevCord.DataContracts.SurveyEntities.Question[] eRequest, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.UpdateQuestionsOrderOnRecorderAsync(eRequest, recorder);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchCallsPrimaryDBResponse RevCord.BusinessLogic.RevcordEnterpriseSvc.IRevEnterpriseSvc.SearchCallsPrimaryDB(RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchCallsPrimaryDBRequest request) {
            return base.Channel.SearchCallsPrimaryDB(request);
        }
        
        public RevCord.DataContracts.VoiceRecEntities.CallInfo[] SearchCallsPrimaryDB(int pageSize, int pageIndex, string startDate, string endDate, string startTime, string endTime, bool isGlobalSearch, string duration, string criteria, RevCord.DataContracts.VoiceRecEntities.Recorder recorder, out int totalPages, out long totalRecords) {
            RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchCallsPrimaryDBRequest inValue = new RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchCallsPrimaryDBRequest();
            inValue.pageSize = pageSize;
            inValue.pageIndex = pageIndex;
            inValue.startDate = startDate;
            inValue.endDate = endDate;
            inValue.startTime = startTime;
            inValue.endTime = endTime;
            inValue.isGlobalSearch = isGlobalSearch;
            inValue.duration = duration;
            inValue.criteria = criteria;
            inValue.recorder = recorder;
            RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchCallsPrimaryDBResponse retVal = ((RevCord.BusinessLogic.RevcordEnterpriseSvc.IRevEnterpriseSvc)(this)).SearchCallsPrimaryDB(inValue);
            totalPages = retVal.totalPages;
            totalRecords = retVal.totalRecords;
            return retVal.SearchCallsPrimaryDBResult;
        }
        
        public System.Threading.Tasks.Task<RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchCallsPrimaryDBResponse> SearchCallsPrimaryDBAsync(RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchCallsPrimaryDBRequest request) {
            return base.Channel.SearchCallsPrimaryDBAsync(request);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchRandomCallsPrimaryDBResponse RevCord.BusinessLogic.RevcordEnterpriseSvc.IRevEnterpriseSvc.SearchRandomCallsPrimaryDB(RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchRandomCallsPrimaryDBRequest request) {
            return base.Channel.SearchRandomCallsPrimaryDB(request);
        }
        
        public RevCord.DataContracts.VoiceRecEntities.CallInfo[] SearchRandomCallsPrimaryDB(int pageSize, int pageIndex, string startDate, string endDate, string startTime, string endTime, bool isGlobalSearch, bool isRandom, bool isPercentage, string duration, string criteria, int noOfCalls, RevCord.DataContracts.VoiceRecEntities.Recorder recorder, out int totalPages, out long totalRecords) {
            RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchRandomCallsPrimaryDBRequest inValue = new RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchRandomCallsPrimaryDBRequest();
            inValue.pageSize = pageSize;
            inValue.pageIndex = pageIndex;
            inValue.startDate = startDate;
            inValue.endDate = endDate;
            inValue.startTime = startTime;
            inValue.endTime = endTime;
            inValue.isGlobalSearch = isGlobalSearch;
            inValue.isRandom = isRandom;
            inValue.isPercentage = isPercentage;
            inValue.duration = duration;
            inValue.criteria = criteria;
            inValue.noOfCalls = noOfCalls;
            inValue.recorder = recorder;
            RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchRandomCallsPrimaryDBResponse retVal = ((RevCord.BusinessLogic.RevcordEnterpriseSvc.IRevEnterpriseSvc)(this)).SearchRandomCallsPrimaryDB(inValue);
            totalPages = retVal.totalPages;
            totalRecords = retVal.totalRecords;
            return retVal.SearchRandomCallsPrimaryDBResult;
        }
        
        public System.Threading.Tasks.Task<RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchRandomCallsPrimaryDBResponse> SearchRandomCallsPrimaryDBAsync(RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchRandomCallsPrimaryDBRequest request) {
            return base.Channel.SearchRandomCallsPrimaryDBAsync(request);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchCallsChainedDBsResponse RevCord.BusinessLogic.RevcordEnterpriseSvc.IRevEnterpriseSvc.SearchCallsChainedDBs(RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchCallsChainedDBsRequest request) {
            return base.Channel.SearchCallsChainedDBs(request);
        }
        
        public RevCord.DataContracts.VoiceRecEntities.CallInfo[] SearchCallsChainedDBs(int pageSize, int pageIndex, string startDate, string endDate, string startTime, string endTime, bool isGlobalSearch, string duration, string criteria, RevCord.DataContracts.VoiceRecEntities.Recorder recorder, out int totalPages, out long totalRecords) {
            RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchCallsChainedDBsRequest inValue = new RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchCallsChainedDBsRequest();
            inValue.pageSize = pageSize;
            inValue.pageIndex = pageIndex;
            inValue.startDate = startDate;
            inValue.endDate = endDate;
            inValue.startTime = startTime;
            inValue.endTime = endTime;
            inValue.isGlobalSearch = isGlobalSearch;
            inValue.duration = duration;
            inValue.criteria = criteria;
            inValue.recorder = recorder;
            RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchCallsChainedDBsResponse retVal = ((RevCord.BusinessLogic.RevcordEnterpriseSvc.IRevEnterpriseSvc)(this)).SearchCallsChainedDBs(inValue);
            totalPages = retVal.totalPages;
            totalRecords = retVal.totalRecords;
            return retVal.SearchCallsChainedDBsResult;
        }
        
        public System.Threading.Tasks.Task<RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchCallsChainedDBsResponse> SearchCallsChainedDBsAsync(RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchCallsChainedDBsRequest request) {
            return base.Channel.SearchCallsChainedDBsAsync(request);
        }
        
        public int UpdateCallsCustomFields(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callIds, int fieldType, string fieldText, int userId) {
            return base.Channel.UpdateCallsCustomFields(recorder, callIds, fieldType, fieldText, userId);
        }
        
        public System.Threading.Tasks.Task<int> UpdateCallsCustomFieldsAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callIds, int fieldType, string fieldText, int userId) {
            return base.Channel.UpdateCallsCustomFieldsAsync(recorder, callIds, fieldType, fieldText, userId);
        }
        
        public int UpdateCallCustomFields(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callId, string tag1, string tag2, string tag3, string tag4, string custName, string callComments) {
            return base.Channel.UpdateCallCustomFields(recorder, callId, tag1, tag2, tag3, tag4, custName, callComments);
        }
        
        public System.Threading.Tasks.Task<int> UpdateCallCustomFieldsAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callId, string tag1, string tag2, string tag3, string tag4, string custName, string callComments) {
            return base.Channel.UpdateCallCustomFieldsAsync(recorder, callId, tag1, tag2, tag3, tag4, custName, callComments);
        }
        
        public int UpdateCallRetention(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callId, bool retainValue) {
            return base.Channel.UpdateCallRetention(recorder, callId, retainValue);
        }
        
        public System.Threading.Tasks.Task<int> UpdateCallRetentionAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callId, bool retainValue) {
            return base.Channel.UpdateCallRetentionAsync(recorder, callId, retainValue);
        }
        
        public int Inquiremarkerupdate(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callId, int markerid, string markertext, string markernotes) {
            return base.Channel.Inquiremarkerupdate(recorder, callId, markerid, markertext, markernotes);
        }
        
        public System.Threading.Tasks.Task<int> InquiremarkerupdateAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callId, int markerid, string markertext, string markernotes) {
            return base.Channel.InquiremarkerupdateAsync(recorder, callId, markerid, markertext, markernotes);
        }
        
        public RevCord.DataContracts.VoiceRecEntities.CallInfo[] GetCallsByIds(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callIds) {
            return base.Channel.GetCallsByIds(recorder, callIds);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.VoiceRecEntities.CallInfo[]> GetCallsByIdsAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callIds) {
            return base.Channel.GetCallsByIdsAsync(recorder, callIds);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        RevCord.BusinessLogic.RevcordEnterpriseSvc.InsertBookmarkAndGetByCallIdResponse RevCord.BusinessLogic.RevcordEnterpriseSvc.IRevEnterpriseSvc.InsertBookmarkAndGetByCallId(RevCord.BusinessLogic.RevcordEnterpriseSvc.InsertBookmarkAndGetByCallIdRequest request) {
            return base.Channel.InsertBookmarkAndGetByCallId(request);
        }
        
        public string[] InsertBookmarkAndGetByCallId(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, RevCord.DataContracts.VoiceRecEntities.Bookmark bookmark, out int rowsAffected) {
            RevCord.BusinessLogic.RevcordEnterpriseSvc.InsertBookmarkAndGetByCallIdRequest inValue = new RevCord.BusinessLogic.RevcordEnterpriseSvc.InsertBookmarkAndGetByCallIdRequest();
            inValue.recorder = recorder;
            inValue.bookmark = bookmark;
            RevCord.BusinessLogic.RevcordEnterpriseSvc.InsertBookmarkAndGetByCallIdResponse retVal = ((RevCord.BusinessLogic.RevcordEnterpriseSvc.IRevEnterpriseSvc)(this)).InsertBookmarkAndGetByCallId(inValue);
            rowsAffected = retVal.rowsAffected;
            return retVal.InsertBookmarkAndGetByCallIdResult;
        }
        
        public System.Threading.Tasks.Task<RevCord.BusinessLogic.RevcordEnterpriseSvc.InsertBookmarkAndGetByCallIdResponse> InsertBookmarkAndGetByCallIdAsync(RevCord.BusinessLogic.RevcordEnterpriseSvc.InsertBookmarkAndGetByCallIdRequest request) {
            return base.Channel.InsertBookmarkAndGetByCallIdAsync(request);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        RevCord.BusinessLogic.RevcordEnterpriseSvc.UpdateBookmarkAndGetByCallIdResponse RevCord.BusinessLogic.RevcordEnterpriseSvc.IRevEnterpriseSvc.UpdateBookmarkAndGetByCallId(RevCord.BusinessLogic.RevcordEnterpriseSvc.UpdateBookmarkAndGetByCallIdRequest request) {
            return base.Channel.UpdateBookmarkAndGetByCallId(request);
        }
        
        public string[] UpdateBookmarkAndGetByCallId(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, RevCord.DataContracts.VoiceRecEntities.Bookmark bookmark, out int rowsAffected) {
            RevCord.BusinessLogic.RevcordEnterpriseSvc.UpdateBookmarkAndGetByCallIdRequest inValue = new RevCord.BusinessLogic.RevcordEnterpriseSvc.UpdateBookmarkAndGetByCallIdRequest();
            inValue.recorder = recorder;
            inValue.bookmark = bookmark;
            RevCord.BusinessLogic.RevcordEnterpriseSvc.UpdateBookmarkAndGetByCallIdResponse retVal = ((RevCord.BusinessLogic.RevcordEnterpriseSvc.IRevEnterpriseSvc)(this)).UpdateBookmarkAndGetByCallId(inValue);
            rowsAffected = retVal.rowsAffected;
            return retVal.UpdateBookmarkAndGetByCallIdResult;
        }
        
        public System.Threading.Tasks.Task<RevCord.BusinessLogic.RevcordEnterpriseSvc.UpdateBookmarkAndGetByCallIdResponse> UpdateBookmarkAndGetByCallIdAsync(RevCord.BusinessLogic.RevcordEnterpriseSvc.UpdateBookmarkAndGetByCallIdRequest request) {
            return base.Channel.UpdateBookmarkAndGetByCallIdAsync(request);
        }
        
        public RevCord.DataContracts.VoiceRecEntities.CallInfoExportResult[] GetCallInfoExportResults(int pageSize, int pageIndex, string startDate, string endDate, string startTime, string endTime, bool isGlobalSearch, string duration, string criteria, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetCallInfoExportResults(pageSize, pageIndex, startDate, endDate, startTime, endTime, isGlobalSearch, duration, criteria, recorder);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.VoiceRecEntities.CallInfoExportResult[]> GetCallInfoExportResultsAsync(int pageSize, int pageIndex, string startDate, string endDate, string startTime, string endTime, bool isGlobalSearch, string duration, string criteria, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetCallInfoExportResultsAsync(pageSize, pageIndex, startDate, endDate, startTime, endTime, isGlobalSearch, duration, criteria, recorder);
        }
        
        public RevCord.DataContracts.VoiceRecEntities.CallInfoExportResult[] GetCallInfoExportResultsByIds(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callIds, System.DateTime startDate, System.DateTime endDate) {
            return base.Channel.GetCallInfoExportResultsByIds(recorder, callIds, startDate, endDate);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.VoiceRecEntities.CallInfoExportResult[]> GetCallInfoExportResultsByIdsAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callIds, System.DateTime startDate, System.DateTime endDate) {
            return base.Channel.GetCallInfoExportResultsByIdsAsync(recorder, callIds, startDate, endDate);
        }
        
        public RevCord.DataContracts.ViewModelEntities.RecorderEvaluation GetAllDrilldownChartsFromRecorder(RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetAllDrilldownChartsFromRecorder(recorder);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.ViewModelEntities.RecorderEvaluation> GetAllDrilldownChartsFromRecorderAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetAllDrilldownChartsFromRecorderAsync(recorder);
        }
        
        public bool InsertCallsForEvaluation(string calls, int userId, int surveyId) {
            return base.Channel.InsertCallsForEvaluation(calls, userId, surveyId);
        }
        
        public System.Threading.Tasks.Task<bool> InsertCallsForEvaluationAsync(string calls, int userId, int surveyId) {
            return base.Channel.InsertCallsForEvaluationAsync(calls, userId, surveyId);
        }
        
        public bool InsertEnterpriseEvaluations(string callIds, int userId, int surveyId, int evaluatorId, string evaluatorName, string evaluatorEmail) {
            return base.Channel.InsertEnterpriseEvaluations(callIds, userId, surveyId, evaluatorId, evaluatorName, evaluatorEmail);
        }
        
        public System.Threading.Tasks.Task<bool> InsertEnterpriseEvaluationsAsync(string callIds, int userId, int surveyId, int evaluatorId, string evaluatorName, string evaluatorEmail) {
            return base.Channel.InsertEnterpriseEvaluationsAsync(callIds, userId, surveyId, evaluatorId, evaluatorName, evaluatorEmail);
        }
        
        public short AddEnterpriseEvaluations(string callIds, int userId, int surveyId, int evaluatorId, string evaluatorName, string evaluatorEmail) {
            return base.Channel.AddEnterpriseEvaluations(callIds, userId, surveyId, evaluatorId, evaluatorName, evaluatorEmail);
        }
        
        public System.Threading.Tasks.Task<short> AddEnterpriseEvaluationsAsync(string callIds, int userId, int surveyId, int evaluatorId, string evaluatorName, string evaluatorEmail) {
            return base.Channel.AddEnterpriseEvaluationsAsync(callIds, userId, surveyId, evaluatorId, evaluatorName, evaluatorEmail);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        RevCord.BusinessLogic.RevcordEnterpriseSvc.GetEvaluationsResponse RevCord.BusinessLogic.RevcordEnterpriseSvc.IRevEnterpriseSvc.GetEvaluations(RevCord.BusinessLogic.RevcordEnterpriseSvc.GetEvaluationsRequest request) {
            return base.Channel.GetEvaluations(request);
        }
        
        public RevCord.DataContracts.DTO.CallEvaluationDTO[] GetEvaluations(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string whereClause, int userId, int pageIndex, int pageSize, bool SharedRequired, bool IsEvaluatorSearch, out int totalPages, out int totalRecords) {
            RevCord.BusinessLogic.RevcordEnterpriseSvc.GetEvaluationsRequest inValue = new RevCord.BusinessLogic.RevcordEnterpriseSvc.GetEvaluationsRequest();
            inValue.recorder = recorder;
            inValue.whereClause = whereClause;
            inValue.userId = userId;
            inValue.pageIndex = pageIndex;
            inValue.pageSize = pageSize;
            inValue.SharedRequired = SharedRequired;
            inValue.IsEvaluatorSearch = IsEvaluatorSearch;
            RevCord.BusinessLogic.RevcordEnterpriseSvc.GetEvaluationsResponse retVal = ((RevCord.BusinessLogic.RevcordEnterpriseSvc.IRevEnterpriseSvc)(this)).GetEvaluations(inValue);
            totalPages = retVal.totalPages;
            totalRecords = retVal.totalRecords;
            return retVal.GetEvaluationsResult;
        }
        
        public System.Threading.Tasks.Task<RevCord.BusinessLogic.RevcordEnterpriseSvc.GetEvaluationsResponse> GetEvaluationsAsync(RevCord.BusinessLogic.RevcordEnterpriseSvc.GetEvaluationsRequest request) {
            return base.Channel.GetEvaluationsAsync(request);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        RevCord.BusinessLogic.RevcordEnterpriseSvc.GetEnterpriseAssociatedEvaluationsResponse RevCord.BusinessLogic.RevcordEnterpriseSvc.IRevEnterpriseSvc.GetEnterpriseAssociatedEvaluations(RevCord.BusinessLogic.RevcordEnterpriseSvc.GetEnterpriseAssociatedEvaluationsRequest request) {
            return base.Channel.GetEnterpriseAssociatedEvaluations(request);
        }
        
        public RevCord.DataContracts.DTO.CallEvaluationDTO[] GetEnterpriseAssociatedEvaluations(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string whereClause, int userId, int pageIndex, int pageSize, bool SharedRequired, bool IsEvaluatorSearch, out int totalPages, out int totalRecords) {
            RevCord.BusinessLogic.RevcordEnterpriseSvc.GetEnterpriseAssociatedEvaluationsRequest inValue = new RevCord.BusinessLogic.RevcordEnterpriseSvc.GetEnterpriseAssociatedEvaluationsRequest();
            inValue.recorder = recorder;
            inValue.whereClause = whereClause;
            inValue.userId = userId;
            inValue.pageIndex = pageIndex;
            inValue.pageSize = pageSize;
            inValue.SharedRequired = SharedRequired;
            inValue.IsEvaluatorSearch = IsEvaluatorSearch;
            RevCord.BusinessLogic.RevcordEnterpriseSvc.GetEnterpriseAssociatedEvaluationsResponse retVal = ((RevCord.BusinessLogic.RevcordEnterpriseSvc.IRevEnterpriseSvc)(this)).GetEnterpriseAssociatedEvaluations(inValue);
            totalPages = retVal.totalPages;
            totalRecords = retVal.totalRecords;
            return retVal.GetEnterpriseAssociatedEvaluationsResult;
        }
        
        public System.Threading.Tasks.Task<RevCord.BusinessLogic.RevcordEnterpriseSvc.GetEnterpriseAssociatedEvaluationsResponse> GetEnterpriseAssociatedEvaluationsAsync(RevCord.BusinessLogic.RevcordEnterpriseSvc.GetEnterpriseAssociatedEvaluationsRequest request) {
            return base.Channel.GetEnterpriseAssociatedEvaluationsAsync(request);
        }
        
        public RevCord.DataContracts.UserManagement.User[] GetUsers(RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetUsers(recorder);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.UserManagement.User[]> GetUsersAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetUsersAsync(recorder);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        RevCord.BusinessLogic.RevcordEnterpriseSvc.PerformActionAndGetEnterpriseEvaluationDTOResponse RevCord.BusinessLogic.RevcordEnterpriseSvc.IRevEnterpriseSvc.PerformActionAndGetEnterpriseEvaluationDTO(RevCord.BusinessLogic.RevcordEnterpriseSvc.PerformActionAndGetEnterpriseEvaluationDTORequest request) {
            return base.Channel.PerformActionAndGetEnterpriseEvaluationDTO(request);
        }
        
        public RevCord.DataContracts.DTO.CallEvaluationDTO[] PerformActionAndGetEnterpriseEvaluationDTO(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int actionToPerformOnRecorder, string callSurveyIds, string whereClause, bool SharedRequired, bool IsEvaluatorSearch, int action, int actionValue, int userId, System.Nullable<System.DateTime> ActionDate, int pageIndex, int pageSize, out int totalPages, out int totalRecords) {
            RevCord.BusinessLogic.RevcordEnterpriseSvc.PerformActionAndGetEnterpriseEvaluationDTORequest inValue = new RevCord.BusinessLogic.RevcordEnterpriseSvc.PerformActionAndGetEnterpriseEvaluationDTORequest();
            inValue.recorder = recorder;
            inValue.actionToPerformOnRecorder = actionToPerformOnRecorder;
            inValue.callSurveyIds = callSurveyIds;
            inValue.whereClause = whereClause;
            inValue.SharedRequired = SharedRequired;
            inValue.IsEvaluatorSearch = IsEvaluatorSearch;
            inValue.action = action;
            inValue.actionValue = actionValue;
            inValue.userId = userId;
            inValue.ActionDate = ActionDate;
            inValue.pageIndex = pageIndex;
            inValue.pageSize = pageSize;
            RevCord.BusinessLogic.RevcordEnterpriseSvc.PerformActionAndGetEnterpriseEvaluationDTOResponse retVal = ((RevCord.BusinessLogic.RevcordEnterpriseSvc.IRevEnterpriseSvc)(this)).PerformActionAndGetEnterpriseEvaluationDTO(inValue);
            totalPages = retVal.totalPages;
            totalRecords = retVal.totalRecords;
            return retVal.PerformActionAndGetEnterpriseEvaluationDTOResult;
        }
        
        public System.Threading.Tasks.Task<RevCord.BusinessLogic.RevcordEnterpriseSvc.PerformActionAndGetEnterpriseEvaluationDTOResponse> PerformActionAndGetEnterpriseEvaluationDTOAsync(RevCord.BusinessLogic.RevcordEnterpriseSvc.PerformActionAndGetEnterpriseEvaluationDTORequest request) {
            return base.Channel.PerformActionAndGetEnterpriseEvaluationDTOAsync(request);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        RevCord.BusinessLogic.RevcordEnterpriseSvc.ShareUnshareAndGetEvaluationDTOResponse RevCord.BusinessLogic.RevcordEnterpriseSvc.IRevEnterpriseSvc.ShareUnshareAndGetEvaluationDTO(RevCord.BusinessLogic.RevcordEnterpriseSvc.ShareUnshareAndGetEvaluationDTORequest request) {
            return base.Channel.ShareUnshareAndGetEvaluationDTO(request);
        }
        
        public RevCord.DataContracts.DTO.CallEvaluationDTO[] ShareUnshareAndGetEvaluationDTO(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int actionToPerformOnRecorder, string callSurveyIds, string whereClause, int action, int actionValue, int userId, string shareWith, bool isSharedEvaluatorRetains, System.Nullable<System.DateTime> ActionDate, int pageIndex, int pageSize, out int totalPages, out int totalRecords) {
            RevCord.BusinessLogic.RevcordEnterpriseSvc.ShareUnshareAndGetEvaluationDTORequest inValue = new RevCord.BusinessLogic.RevcordEnterpriseSvc.ShareUnshareAndGetEvaluationDTORequest();
            inValue.recorder = recorder;
            inValue.actionToPerformOnRecorder = actionToPerformOnRecorder;
            inValue.callSurveyIds = callSurveyIds;
            inValue.whereClause = whereClause;
            inValue.action = action;
            inValue.actionValue = actionValue;
            inValue.userId = userId;
            inValue.shareWith = shareWith;
            inValue.isSharedEvaluatorRetains = isSharedEvaluatorRetains;
            inValue.ActionDate = ActionDate;
            inValue.pageIndex = pageIndex;
            inValue.pageSize = pageSize;
            RevCord.BusinessLogic.RevcordEnterpriseSvc.ShareUnshareAndGetEvaluationDTOResponse retVal = ((RevCord.BusinessLogic.RevcordEnterpriseSvc.IRevEnterpriseSvc)(this)).ShareUnshareAndGetEvaluationDTO(inValue);
            totalPages = retVal.totalPages;
            totalRecords = retVal.totalRecords;
            return retVal.ShareUnshareAndGetEvaluationDTOResult;
        }
        
        public System.Threading.Tasks.Task<RevCord.BusinessLogic.RevcordEnterpriseSvc.ShareUnshareAndGetEvaluationDTOResponse> ShareUnshareAndGetEvaluationDTOAsync(RevCord.BusinessLogic.RevcordEnterpriseSvc.ShareUnshareAndGetEvaluationDTORequest request) {
            return base.Channel.ShareUnshareAndGetEvaluationDTOAsync(request);
        }
        
        public RevCord.DataContracts.EvaluationEntities.CallEvaluation GetCallEvaluationDetailsById(int callEvaluationId, int userId) {
            return base.Channel.GetCallEvaluationDetailsById(callEvaluationId, userId);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.EvaluationEntities.CallEvaluation> GetCallEvaluationDetailsByIdAsync(int callEvaluationId, int userId) {
            return base.Channel.GetCallEvaluationDetailsByIdAsync(callEvaluationId, userId);
        }
        
        public RevCord.DataContracts.EvaluationEntities.CallEvaluation UpdateCallEvaluation(RevCord.DataContracts.EvaluationEntities.CallEvaluation callEvaluation) {
            return base.Channel.UpdateCallEvaluation(callEvaluation);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.EvaluationEntities.CallEvaluation> UpdateCallEvaluationAsync(RevCord.DataContracts.EvaluationEntities.CallEvaluation callEvaluation) {
            return base.Channel.UpdateCallEvaluationAsync(callEvaluation);
        }
        
        public RevCord.DataContracts.UserManagement.User UpdateAndGetAssociatedUser(int evalId, int agentId) {
            return base.Channel.UpdateAndGetAssociatedUser(evalId, agentId);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.UserManagement.User> UpdateAndGetAssociatedUserAsync(int evalId, int agentId) {
            return base.Channel.UpdateAndGetAssociatedUserAsync(evalId, agentId);
        }
        
        public RevCord.DataContracts.EvaluationEntities.UserEvaluation[] GetEvaluationId(int surveyId, string callId, int userId) {
            return base.Channel.GetEvaluationId(surveyId, callId, userId);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.EvaluationEntities.UserEvaluation[]> GetEvaluationIdAsync(int surveyId, string callId, int userId) {
            return base.Channel.GetEvaluationIdAsync(surveyId, callId, userId);
        }
        
        public bool PerformActionOnRecorder(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callSurveyIds, int action, int actionValue, int userId, System.Nullable<System.DateTime> ActionDate) {
            return base.Channel.PerformActionOnRecorder(recorder, callSurveyIds, action, actionValue, userId, ActionDate);
        }
        
        public System.Threading.Tasks.Task<bool> PerformActionOnRecorderAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callSurveyIds, int action, int actionValue, int userId, System.Nullable<System.DateTime> ActionDate) {
            return base.Channel.PerformActionOnRecorderAsync(recorder, callSurveyIds, action, actionValue, userId, ActionDate);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchRecordedCallsResponse RevCord.BusinessLogic.RevcordEnterpriseSvc.IRevEnterpriseSvc.SearchRecordedCalls(RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchRecordedCallsRequest request) {
            return base.Channel.SearchRecordedCalls(request);
        }
        
        public RevCord.DataContracts.VoiceRecEntities.CallInfo[] SearchRecordedCalls(int pageSize, int pageIndex, string startDate, string endDate, string startTime, string endTime, bool isGlobalSearch, string duration, string criteria, RevCord.DataContracts.VoiceRecEntities.Recorder recorder, out int totalPages, out long totalRecords) {
            RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchRecordedCallsRequest inValue = new RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchRecordedCallsRequest();
            inValue.pageSize = pageSize;
            inValue.pageIndex = pageIndex;
            inValue.startDate = startDate;
            inValue.endDate = endDate;
            inValue.startTime = startTime;
            inValue.endTime = endTime;
            inValue.isGlobalSearch = isGlobalSearch;
            inValue.duration = duration;
            inValue.criteria = criteria;
            inValue.recorder = recorder;
            RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchRecordedCallsResponse retVal = ((RevCord.BusinessLogic.RevcordEnterpriseSvc.IRevEnterpriseSvc)(this)).SearchRecordedCalls(inValue);
            totalPages = retVal.totalPages;
            totalRecords = retVal.totalRecords;
            return retVal.SearchRecordedCallsResult;
        }
        
        public System.Threading.Tasks.Task<RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchRecordedCallsResponse> SearchRecordedCallsAsync(RevCord.BusinessLogic.RevcordEnterpriseSvc.SearchRecordedCallsRequest request) {
            return base.Channel.SearchRecordedCallsAsync(request);
        }
        
        public RevCord.DataContracts.VoiceRecEntities.CallInfoLite GetCallDetailsFromRecorder(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callId) {
            return base.Channel.GetCallDetailsFromRecorder(recorder, callId);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.VoiceRecEntities.CallInfoLite> GetCallDetailsFromRecorderAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callId) {
            return base.Channel.GetCallDetailsFromRecorderAsync(recorder, callId);
        }
        
        public RevCord.DataContracts.ReportEntities.RPTCallInfo[] GetSearchResults(string selectKey, string selectYear, string selectMonth, string selectDay, string fromDate, string toDate, string timeRange, string duration, string groupKey, string groupYear, string groupMonth, string groupDay, string optionString, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetSearchResults(selectKey, selectYear, selectMonth, selectDay, fromDate, toDate, timeRange, duration, groupKey, groupYear, groupMonth, groupDay, optionString, recorder);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.ReportEntities.RPTCallInfo[]> GetSearchResultsAsync(string selectKey, string selectYear, string selectMonth, string selectDay, string fromDate, string toDate, string timeRange, string duration, string groupKey, string groupYear, string groupMonth, string groupDay, string optionString, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetSearchResultsAsync(selectKey, selectYear, selectMonth, selectDay, fromDate, toDate, timeRange, duration, groupKey, groupYear, groupMonth, groupDay, optionString, recorder);
        }
        
        public RevCord.DataContracts.ReportEntities.RPTCallInfoDetail[] GetDetailSearchResults(string selectKey, string selectYear, string selectMonth, string selectDay, string fromDate, string toDate, string timeRange, string duration, string groupKey, string groupYear, string groupMonth, string groupDay, string optionString, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetDetailSearchResults(selectKey, selectYear, selectMonth, selectDay, fromDate, toDate, timeRange, duration, groupKey, groupYear, groupMonth, groupDay, optionString, recorder);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.ReportEntities.RPTCallInfoDetail[]> GetDetailSearchResultsAsync(string selectKey, string selectYear, string selectMonth, string selectDay, string fromDate, string toDate, string timeRange, string duration, string groupKey, string groupYear, string groupMonth, string groupDay, string optionString, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetDetailSearchResultsAsync(selectKey, selectYear, selectMonth, selectDay, fromDate, toDate, timeRange, duration, groupKey, groupYear, groupMonth, groupDay, optionString, recorder);
        }
        
        public RevCord.DataContracts.ReportEntities.RPTCallInfo[] GetSearchResultsMonthDayOfWeek(string selectKey, string selectYear, string selectMonth, string selectDay, string fromDate, string toDate, string timeRange, string duration, string groupKey, string groupYear, string groupMonth, string groupDay, string optionString, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetSearchResultsMonthDayOfWeek(selectKey, selectYear, selectMonth, selectDay, fromDate, toDate, timeRange, duration, groupKey, groupYear, groupMonth, groupDay, optionString, recorder);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.ReportEntities.RPTCallInfo[]> GetSearchResultsMonthDayOfWeekAsync(string selectKey, string selectYear, string selectMonth, string selectDay, string fromDate, string toDate, string timeRange, string duration, string groupKey, string groupYear, string groupMonth, string groupDay, string optionString, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetSearchResultsMonthDayOfWeekAsync(selectKey, selectYear, selectMonth, selectDay, fromDate, toDate, timeRange, duration, groupKey, groupYear, groupMonth, groupDay, optionString, recorder);
        }
        
        public RevCord.DataContracts.ReportEntities.RPTCallInfo[] GetSearchResultsDayOfWeek(string selectKey, string selectYear, string selectMonth, string selectDay, string fromDate, string toDate, string timeRange, string duration, string groupKey, string groupYear, string groupMonth, string groupDay, string optionString, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetSearchResultsDayOfWeek(selectKey, selectYear, selectMonth, selectDay, fromDate, toDate, timeRange, duration, groupKey, groupYear, groupMonth, groupDay, optionString, recorder);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.ReportEntities.RPTCallInfo[]> GetSearchResultsDayOfWeekAsync(string selectKey, string selectYear, string selectMonth, string selectDay, string fromDate, string toDate, string timeRange, string duration, string groupKey, string groupYear, string groupMonth, string groupDay, string optionString, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetSearchResultsDayOfWeekAsync(selectKey, selectYear, selectMonth, selectDay, fromDate, toDate, timeRange, duration, groupKey, groupYear, groupMonth, groupDay, optionString, recorder);
        }
        
        public RevCord.DataContracts.ReportEntities.RPTCallInfo[] GetSearchResultsHour(string selectKey, string selectYear, string selectMonth, string selectDay, string fromDate, string toDate, string timeRange, string duration, string groupKey, string groupYear, string groupMonth, string groupDay, string optionString, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetSearchResultsHour(selectKey, selectYear, selectMonth, selectDay, fromDate, toDate, timeRange, duration, groupKey, groupYear, groupMonth, groupDay, optionString, recorder);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.ReportEntities.RPTCallInfo[]> GetSearchResultsHourAsync(string selectKey, string selectYear, string selectMonth, string selectDay, string fromDate, string toDate, string timeRange, string duration, string groupKey, string groupYear, string groupMonth, string groupDay, string optionString, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetSearchResultsHourAsync(selectKey, selectYear, selectMonth, selectDay, fromDate, toDate, timeRange, duration, groupKey, groupYear, groupMonth, groupDay, optionString, recorder);
        }
        
        public RevCord.DataContracts.VoiceRecEntities.CallInfo GetCallByIdFromRecorder(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callId) {
            return base.Channel.GetCallByIdFromRecorder(recorder, callId);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.VoiceRecEntities.CallInfo> GetCallByIdFromRecorderAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string callId) {
            return base.Channel.GetCallByIdFromRecorderAsync(recorder, callId);
        }
        
        public RevCord.DataContracts.ReportEntities.RPTCallInfo[] GetSearchResults911(string selectKey, string selectYear, string selectMonth, string selectDay, string fromDate, string toDate, string timeRange, string duration, string groupKey, string groupYear, string groupMonth, string groupDay, string optionString, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetSearchResults911(selectKey, selectYear, selectMonth, selectDay, fromDate, toDate, timeRange, duration, groupKey, groupYear, groupMonth, groupDay, optionString, recorder);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.ReportEntities.RPTCallInfo[]> GetSearchResults911Async(string selectKey, string selectYear, string selectMonth, string selectDay, string fromDate, string toDate, string timeRange, string duration, string groupKey, string groupYear, string groupMonth, string groupDay, string optionString, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetSearchResults911Async(selectKey, selectYear, selectMonth, selectDay, fromDate, toDate, timeRange, duration, groupKey, groupYear, groupMonth, groupDay, optionString, recorder);
        }
        
        public RevCord.DataContracts.ReportEntities.RPTCallInfo[] GetCallAuditSearchResults(string fromDate, string toDate, string optionStr, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetCallAuditSearchResults(fromDate, toDate, optionStr, recorder);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.ReportEntities.RPTCallInfo[]> GetCallAuditSearchResultsAsync(string fromDate, string toDate, string optionStr, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetCallAuditSearchResultsAsync(fromDate, toDate, optionStr, recorder);
        }
        
        public RevCord.DataContracts.ReportEntities.RPTCallInfo[] GetCallsNotAuditedSearchResults(string fromDate, string toDate, string optionStr, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetCallsNotAuditedSearchResults(fromDate, toDate, optionStr, recorder);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.ReportEntities.RPTCallInfo[]> GetCallsNotAuditedSearchResultsAsync(string fromDate, string toDate, string optionStr, RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetCallsNotAuditedSearchResultsAsync(fromDate, toDate, optionStr, recorder);
        }
        
        public RevCord.DataContracts.ReportEntities.RPTEvaluation[] GetRPTEvaluationReportFromRecorder(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string criteria, int userId) {
            return base.Channel.GetRPTEvaluationReportFromRecorder(recorder, criteria, userId);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.ReportEntities.RPTEvaluation[]> GetRPTEvaluationReportFromRecorderAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, string criteria, int userId) {
            return base.Channel.GetRPTEvaluationReportFromRecorderAsync(recorder, criteria, userId);
        }
        
        public bool UpdateConfiguration(string ipAddress) {
            return base.Channel.UpdateConfiguration(ipAddress);
        }
        
        public System.Threading.Tasks.Task<bool> UpdateConfigurationAsync(string ipAddress) {
            return base.Channel.UpdateConfigurationAsync(ipAddress);
        }
        
        public bool CheckDBConnection(string ipAddress) {
            return base.Channel.CheckDBConnection(ipAddress);
        }
        
        public System.Threading.Tasks.Task<bool> CheckDBConnectionAsync(string ipAddress) {
            return base.Channel.CheckDBConnectionAsync(ipAddress);
        }
        
        public RevCord.DataContracts.VoiceRecEntities.Channel[] GetAudioChannelsFromRecorder(RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetAudioChannelsFromRecorder(recorder);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.VoiceRecEntities.Channel[]> GetAudioChannelsFromRecorderAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder) {
            return base.Channel.GetAudioChannelsFromRecorderAsync(recorder);
        }
        
        public RevCord.DataContracts.VoiceRecEntities.Channel GetAudioChannelFromRecorder(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int channelId) {
            return base.Channel.GetAudioChannelFromRecorder(recorder, channelId);
        }
        
        public System.Threading.Tasks.Task<RevCord.DataContracts.VoiceRecEntities.Channel> GetAudioChannelFromRecorderAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int channelId) {
            return base.Channel.GetAudioChannelFromRecorderAsync(recorder, channelId);
        }
        
        public int DeleteAudioChannels(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int[] channelIds) {
            return base.Channel.DeleteAudioChannels(recorder, channelIds);
        }
        
        public System.Threading.Tasks.Task<int> DeleteAudioChannelsAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int[] channelIds) {
            return base.Channel.DeleteAudioChannelsAsync(recorder, channelIds);
        }
        
        public int CreateAudioChannels(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int localRecorderId, int NoOfAnalogChannels, int NoOfVoIPChannels, string gatewayId) {
            return base.Channel.CreateAudioChannels(recorder, localRecorderId, NoOfAnalogChannels, NoOfVoIPChannels, gatewayId);
        }
        
        public System.Threading.Tasks.Task<int> CreateAudioChannelsAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, int localRecorderId, int NoOfAnalogChannels, int NoOfVoIPChannels, string gatewayId) {
            return base.Channel.CreateAudioChannelsAsync(recorder, localRecorderId, NoOfAnalogChannels, NoOfVoIPChannels, gatewayId);
        }
        
        public int UpdateAudioChannel(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, RevCord.DataContracts.VoiceRecEntities.Channel channel) {
            return base.Channel.UpdateAudioChannel(recorder, channel);
        }
        
        public System.Threading.Tasks.Task<int> UpdateAudioChannelAsync(RevCord.DataContracts.VoiceRecEntities.Recorder recorder, RevCord.DataContracts.VoiceRecEntities.Channel channel) {
            return base.Channel.UpdateAudioChannelAsync(recorder, channel);
        }
    }
}
