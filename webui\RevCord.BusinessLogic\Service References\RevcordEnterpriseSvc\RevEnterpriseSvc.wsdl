<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:tns="http://tempuri.org/" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="RevEnterpriseSvc" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <xsd:schema targetNamespace="http://tempuri.org/Imports">
      <xsd:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd0" namespace="http://tempuri.org/" />
      <xsd:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
      <xsd:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd2" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ReportEntities" />
      <xsd:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd3" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" />
      <xsd:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd4" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.DTO" />
      <xsd:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd5" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" />
      <xsd:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd6" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.MessageBase" />
      <xsd:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd7" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.Criteria" />
      <xsd:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd8" namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
      <xsd:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd9" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts" />
      <xsd:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd10" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.Response" />
      <xsd:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd11" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.RoleManagement" />
      <xsd:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd12" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ViewModelEntities" />
      <xsd:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd13" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.Messages" />
      <xsd:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd14" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.IQ3" />
      <xsd:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd15" namespace="http://schemas.datacontract.org/2004/07/System" />
      <xsd:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd16" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.SurveyEntities" />
      <xsd:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd17" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.EvaluationEntities" />
      <xsd:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd18" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.TenantEntities" />
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="IRevEnterpriseSvc_CallAuditSave_InputMessage">
    <wsdl:part name="parameters" element="tns:CallAuditSave" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_CallAuditSave_OutputMessage">
    <wsdl:part name="parameters" element="tns:CallAuditSaveResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetCallAuditTrail_InputMessage">
    <wsdl:part name="parameters" element="tns:GetCallAuditTrail" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetCallAuditTrail_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetCallAuditTrailResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetAuditedCallsByExtension_InputMessage">
    <wsdl:part name="parameters" element="tns:GetAuditedCallsByExtension" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetAuditedCallsByExtension_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetAuditedCallsByExtensionResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetUserActivities_InputMessage">
    <wsdl:part name="parameters" element="tns:GetUserActivities" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetUserActivities_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetUserActivitiesResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetCallsByLocation_InputMessage">
    <wsdl:part name="parameters" element="tns:GetCallsByLocation" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetCallsByLocation_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetCallsByLocationResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetDefaultSearchResultsDTO_InputMessage">
    <wsdl:part name="parameters" element="tns:GetDefaultSearchResultsDTO" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetDefaultSearchResultsDTO_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetDefaultSearchResultsDTOResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetAdvanceSearchResultsDTO_InputMessage">
    <wsdl:part name="parameters" element="tns:GetAdvanceSearchResultsDTO" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetAdvanceSearchResultsDTO_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetAdvanceSearchResultsDTOResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_PerformSearchChainedDBs_InputMessage">
    <wsdl:part name="parameters" element="tns:PerformSearchChainedDBs" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_PerformSearchChainedDBs_OutputMessage">
    <wsdl:part name="parameters" element="tns:PerformSearchChainedDBsResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetDefaultQASearchResultsDTO_InputMessage">
    <wsdl:part name="parameters" element="tns:GetDefaultQASearchResultsDTO" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetDefaultQASearchResultsDTO_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetDefaultQASearchResultsDTOResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetAdvanceQASearchResultsDTO_InputMessage">
    <wsdl:part name="parameters" element="tns:GetAdvanceQASearchResultsDTO" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetAdvanceQASearchResultsDTO_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetAdvanceQASearchResultsDTOResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_PerformQASearchChainedDBs_InputMessage">
    <wsdl:part name="parameters" element="tns:PerformQASearchChainedDBs" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_PerformQASearchChainedDBs_OutputMessage">
    <wsdl:part name="parameters" element="tns:PerformQASearchChainedDBsResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetUserExtensionInfos_InputMessage">
    <wsdl:part name="parameters" element="tns:GetUserExtensionInfos" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetUserExtensionInfos_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetUserExtensionInfosResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetAppUsers_InputMessage">
    <wsdl:part name="parameters" element="tns:GetAppUsers" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetAppUsers_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetAppUsersResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetAppUserAccount_InputMessage">
    <wsdl:part name="parameters" element="tns:GetAppUserAccount" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetAppUserAccount_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetAppUserAccountResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_RecoverAppUserAccount_InputMessage">
    <wsdl:part name="parameters" element="tns:RecoverAppUserAccount" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_RecoverAppUserAccount_OutputMessage">
    <wsdl:part name="parameters" element="tns:RecoverAppUserAccountResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_UpdateAppUserAccount_InputMessage">
    <wsdl:part name="parameters" element="tns:UpdateAppUserAccount" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_UpdateAppUserAccount_OutputMessage">
    <wsdl:part name="parameters" element="tns:UpdateAppUserAccountResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_DeleteAppUserAccount_InputMessage">
    <wsdl:part name="parameters" element="tns:DeleteAppUserAccount" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_DeleteAppUserAccount_OutputMessage">
    <wsdl:part name="parameters" element="tns:DeleteAppUserAccountResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_SaveAssignedGroup_InputMessage">
    <wsdl:part name="parameters" element="tns:SaveAssignedGroup" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_SaveAssignedGroup_OutputMessage">
    <wsdl:part name="parameters" element="tns:SaveAssignedGroupResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_DeleteUserGroup_InputMessage">
    <wsdl:part name="parameters" element="tns:DeleteUserGroup" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_DeleteUserGroup_OutputMessage">
    <wsdl:part name="parameters" element="tns:DeleteUserGroupResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetUserData_InputMessage">
    <wsdl:part name="parameters" element="tns:GetUserData" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetUserData_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetUserDataResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_FetchAllActiveUsers_InputMessage">
    <wsdl:part name="parameters" element="tns:FetchAllActiveUsers" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_FetchAllActiveUsers_OutputMessage">
    <wsdl:part name="parameters" element="tns:FetchAllActiveUsersResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetTreeViewFromRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:GetTreeViewFromRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetTreeViewFromRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetTreeViewFromRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetGroupsTree_InputMessage">
    <wsdl:part name="parameters" element="tns:GetGroupsTree" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetGroupsTree_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetGroupsTreeResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetGroupsTreeFromRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:GetGroupsTreeFromRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetGroupsTreeFromRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetGroupsTreeFromRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetGroupsTreeNonAdminFromRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:GetGroupsTreeNonAdminFromRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetGroupsTreeNonAdminFromRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetGroupsTreeNonAdminFromRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetUsersTreeFromRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:GetUsersTreeFromRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetUsersTreeFromRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetUsersTreeFromRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetInquireGroupsTreeforEvaluationReportFromRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:GetInquireGroupsTreeforEvaluationReportFromRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetInquireGroupsTreeforEvaluationReportFromRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetInquireGroupsTreeforEvaluationReportFromRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetMDGroupsTreeForEvaluationReportFromRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:GetMDGroupsTreeForEvaluationReportFromRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetMDGroupsTreeForEvaluationReportFromRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetMDGroupsTreeForEvaluationReportFromRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetEnterpriseUserRightsData_InputMessage">
    <wsdl:part name="parameters" element="tns:GetEnterpriseUserRightsData" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetEnterpriseUserRightsData_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetEnterpriseUserRightsDataResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetEnterpriseUserTree_InputMessage">
    <wsdl:part name="parameters" element="tns:GetEnterpriseUserTree" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetEnterpriseUserTree_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetEnterpriseUserTreeResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetChannelsToMonitorFromRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:GetChannelsToMonitorFromRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetChannelsToMonitorFromRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetChannelsToMonitorFromRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetSurveysFromRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:GetSurveysFromRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetSurveysFromRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetSurveysFromRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetPublishedSurveysFromRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:GetPublishedSurveysFromRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetPublishedSurveysFromRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetPublishedSurveysFromRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_DeleteAndGetSurveysFromRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:DeleteAndGetSurveysFromRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_DeleteAndGetSurveysFromRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:DeleteAndGetSurveysFromRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_PublishAndGetSurveysFromRecorders_InputMessage">
    <wsdl:part name="parameters" element="tns:PublishAndGetSurveysFromRecorders" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_PublishAndGetSurveysFromRecorders_OutputMessage">
    <wsdl:part name="parameters" element="tns:PublishAndGetSurveysFromRecordersResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetSurveyDetailsFromRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:GetSurveyDetailsFromRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetSurveyDetailsFromRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetSurveyDetailsFromRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_IsSurveyExistOnRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:IsSurveyExistOnRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_IsSurveyExistOnRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:IsSurveyExistOnRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_CreateSurveyOnRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:CreateSurveyOnRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_CreateSurveyOnRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:CreateSurveyOnRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_CreateQuestionOnRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:CreateQuestionOnRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_CreateQuestionOnRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:CreateQuestionOnRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_CreateAndGetSectionsOnRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:CreateAndGetSectionsOnRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_CreateAndGetSectionsOnRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:CreateAndGetSectionsOnRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetQuestionsBySurveyIdFromRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:GetQuestionsBySurveyIdFromRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetQuestionsBySurveyIdFromRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetQuestionsBySurveyIdFromRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetSectionsBySurveyIdFromRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:GetSectionsBySurveyIdFromRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetSectionsBySurveyIdFromRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetSectionsBySurveyIdFromRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_UpdateQuestionSectionOnRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:UpdateQuestionSectionOnRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_UpdateQuestionSectionOnRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:UpdateQuestionSectionOnRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_UpdateAndGetSectionsFromRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:UpdateAndGetSectionsFromRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_UpdateAndGetSectionsFromRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:UpdateAndGetSectionsFromRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_DeleteAndGetSectionsFromRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:DeleteAndGetSectionsFromRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_DeleteAndGetSectionsFromRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:DeleteAndGetSectionsFromRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_AssignUnAssignQuestionsAndGetSectionsFromRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:AssignUnAssignQuestionsAndGetSectionsFromRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_AssignUnAssignQuestionsAndGetSectionsFromRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:AssignUnAssignQuestionsAndGetSectionsFromRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetSurveyFromRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:GetSurveyFromRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetSurveyFromRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetSurveyFromRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_UpdateSurveyOnRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:UpdateSurveyOnRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_UpdateSurveyOnRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:UpdateSurveyOnRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_UpdateQuestionOnRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:UpdateQuestionOnRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_UpdateQuestionOnRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:UpdateQuestionOnRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_DeleteQuestionFromRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:DeleteQuestionFromRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_DeleteQuestionFromRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:DeleteQuestionFromRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_UpdateIsPublishedOnRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:UpdateIsPublishedOnRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_UpdateIsPublishedOnRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:UpdateIsPublishedOnRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_UpdateQuestionsOrderOnRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:UpdateQuestionsOrderOnRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_UpdateQuestionsOrderOnRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:UpdateQuestionsOrderOnRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_SearchCallsPrimaryDB_InputMessage">
    <wsdl:part name="parameters" element="tns:SearchCallsPrimaryDB" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_SearchCallsPrimaryDB_OutputMessage">
    <wsdl:part name="parameters" element="tns:SearchCallsPrimaryDBResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_SearchRandomCallsPrimaryDB_InputMessage">
    <wsdl:part name="parameters" element="tns:SearchRandomCallsPrimaryDB" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_SearchRandomCallsPrimaryDB_OutputMessage">
    <wsdl:part name="parameters" element="tns:SearchRandomCallsPrimaryDBResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_SearchCallsChainedDBs_InputMessage">
    <wsdl:part name="parameters" element="tns:SearchCallsChainedDBs" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_SearchCallsChainedDBs_OutputMessage">
    <wsdl:part name="parameters" element="tns:SearchCallsChainedDBsResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_UpdateCallsCustomFields_InputMessage">
    <wsdl:part name="parameters" element="tns:UpdateCallsCustomFields" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_UpdateCallsCustomFields_OutputMessage">
    <wsdl:part name="parameters" element="tns:UpdateCallsCustomFieldsResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_UpdateCallCustomFields_InputMessage">
    <wsdl:part name="parameters" element="tns:UpdateCallCustomFields" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_UpdateCallCustomFields_OutputMessage">
    <wsdl:part name="parameters" element="tns:UpdateCallCustomFieldsResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_UpdateCallRetention_InputMessage">
    <wsdl:part name="parameters" element="tns:UpdateCallRetention" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_UpdateCallRetention_OutputMessage">
    <wsdl:part name="parameters" element="tns:UpdateCallRetentionResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_Inquiremarkerupdate_InputMessage">
    <wsdl:part name="parameters" element="tns:Inquiremarkerupdate" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_Inquiremarkerupdate_OutputMessage">
    <wsdl:part name="parameters" element="tns:InquiremarkerupdateResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetCallsByIds_InputMessage">
    <wsdl:part name="parameters" element="tns:GetCallsByIds" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetCallsByIds_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetCallsByIdsResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_InsertBookmarkAndGetByCallId_InputMessage">
    <wsdl:part name="parameters" element="tns:InsertBookmarkAndGetByCallId" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_InsertBookmarkAndGetByCallId_OutputMessage">
    <wsdl:part name="parameters" element="tns:InsertBookmarkAndGetByCallIdResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_UpdateBookmarkAndGetByCallId_InputMessage">
    <wsdl:part name="parameters" element="tns:UpdateBookmarkAndGetByCallId" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_UpdateBookmarkAndGetByCallId_OutputMessage">
    <wsdl:part name="parameters" element="tns:UpdateBookmarkAndGetByCallIdResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetCallInfoExportResults_InputMessage">
    <wsdl:part name="parameters" element="tns:GetCallInfoExportResults" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetCallInfoExportResults_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetCallInfoExportResultsResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetCallInfoExportResultsByIds_InputMessage">
    <wsdl:part name="parameters" element="tns:GetCallInfoExportResultsByIds" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetCallInfoExportResultsByIds_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetCallInfoExportResultsByIdsResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetAllDrilldownChartsFromRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:GetAllDrilldownChartsFromRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetAllDrilldownChartsFromRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetAllDrilldownChartsFromRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_InsertCallsForEvaluation_InputMessage">
    <wsdl:part name="parameters" element="tns:InsertCallsForEvaluation" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_InsertCallsForEvaluation_OutputMessage">
    <wsdl:part name="parameters" element="tns:InsertCallsForEvaluationResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_InsertEnterpriseEvaluations_InputMessage">
    <wsdl:part name="parameters" element="tns:InsertEnterpriseEvaluations" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_InsertEnterpriseEvaluations_OutputMessage">
    <wsdl:part name="parameters" element="tns:InsertEnterpriseEvaluationsResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_AddEnterpriseEvaluations_InputMessage">
    <wsdl:part name="parameters" element="tns:AddEnterpriseEvaluations" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_AddEnterpriseEvaluations_OutputMessage">
    <wsdl:part name="parameters" element="tns:AddEnterpriseEvaluationsResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetEvaluations_InputMessage">
    <wsdl:part name="parameters" element="tns:GetEvaluations" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetEvaluations_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetEvaluationsResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetEnterpriseAssociatedEvaluations_InputMessage">
    <wsdl:part name="parameters" element="tns:GetEnterpriseAssociatedEvaluations" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetEnterpriseAssociatedEvaluations_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetEnterpriseAssociatedEvaluationsResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetUsers_InputMessage">
    <wsdl:part name="parameters" element="tns:GetUsers" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetUsers_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetUsersResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_PerformActionAndGetEnterpriseEvaluationDTO_InputMessage">
    <wsdl:part name="parameters" element="tns:PerformActionAndGetEnterpriseEvaluationDTO" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_PerformActionAndGetEnterpriseEvaluationDTO_OutputMessage">
    <wsdl:part name="parameters" element="tns:PerformActionAndGetEnterpriseEvaluationDTOResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_ShareUnshareAndGetEvaluationDTO_InputMessage">
    <wsdl:part name="parameters" element="tns:ShareUnshareAndGetEvaluationDTO" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_ShareUnshareAndGetEvaluationDTO_OutputMessage">
    <wsdl:part name="parameters" element="tns:ShareUnshareAndGetEvaluationDTOResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetCallEvaluationDetailsById_InputMessage">
    <wsdl:part name="parameters" element="tns:GetCallEvaluationDetailsById" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetCallEvaluationDetailsById_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetCallEvaluationDetailsByIdResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_UpdateCallEvaluation_InputMessage">
    <wsdl:part name="parameters" element="tns:UpdateCallEvaluation" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_UpdateCallEvaluation_OutputMessage">
    <wsdl:part name="parameters" element="tns:UpdateCallEvaluationResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_UpdateAndGetAssociatedUser_InputMessage">
    <wsdl:part name="parameters" element="tns:UpdateAndGetAssociatedUser" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_UpdateAndGetAssociatedUser_OutputMessage">
    <wsdl:part name="parameters" element="tns:UpdateAndGetAssociatedUserResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetEvaluationId_InputMessage">
    <wsdl:part name="parameters" element="tns:GetEvaluationId" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetEvaluationId_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetEvaluationIdResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_PerformActionOnRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:PerformActionOnRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_PerformActionOnRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:PerformActionOnRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_SearchRecordedCalls_InputMessage">
    <wsdl:part name="parameters" element="tns:SearchRecordedCalls" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_SearchRecordedCalls_OutputMessage">
    <wsdl:part name="parameters" element="tns:SearchRecordedCallsResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetCallDetailsFromRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:GetCallDetailsFromRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetCallDetailsFromRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetCallDetailsFromRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetSearchResults_InputMessage">
    <wsdl:part name="parameters" element="tns:GetSearchResults" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetSearchResults_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetSearchResultsResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetDetailSearchResults_InputMessage">
    <wsdl:part name="parameters" element="tns:GetDetailSearchResults" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetDetailSearchResults_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetDetailSearchResultsResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetSearchResultsMonthDayOfWeek_InputMessage">
    <wsdl:part name="parameters" element="tns:GetSearchResultsMonthDayOfWeek" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetSearchResultsMonthDayOfWeek_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetSearchResultsMonthDayOfWeekResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetSearchResultsDayOfWeek_InputMessage">
    <wsdl:part name="parameters" element="tns:GetSearchResultsDayOfWeek" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetSearchResultsDayOfWeek_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetSearchResultsDayOfWeekResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetSearchResultsHour_InputMessage">
    <wsdl:part name="parameters" element="tns:GetSearchResultsHour" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetSearchResultsHour_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetSearchResultsHourResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetCallByIdFromRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:GetCallByIdFromRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetCallByIdFromRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetCallByIdFromRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetSearchResults911_InputMessage">
    <wsdl:part name="parameters" element="tns:GetSearchResults911" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetSearchResults911_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetSearchResults911Response" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetCallAuditSearchResults_InputMessage">
    <wsdl:part name="parameters" element="tns:GetCallAuditSearchResults" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetCallAuditSearchResults_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetCallAuditSearchResultsResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetCallsNotAuditedSearchResults_InputMessage">
    <wsdl:part name="parameters" element="tns:GetCallsNotAuditedSearchResults" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetCallsNotAuditedSearchResults_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetCallsNotAuditedSearchResultsResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetRPTEvaluationReportFromRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:GetRPTEvaluationReportFromRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetRPTEvaluationReportFromRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetRPTEvaluationReportFromRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_UpdateConfiguration_InputMessage">
    <wsdl:part name="parameters" element="tns:UpdateConfiguration" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_UpdateConfiguration_OutputMessage">
    <wsdl:part name="parameters" element="tns:UpdateConfigurationResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_CheckDBConnection_InputMessage">
    <wsdl:part name="parameters" element="tns:CheckDBConnection" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_CheckDBConnection_OutputMessage">
    <wsdl:part name="parameters" element="tns:CheckDBConnectionResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetAudioChannelsFromRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:GetAudioChannelsFromRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetAudioChannelsFromRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetAudioChannelsFromRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetAudioChannelFromRecorder_InputMessage">
    <wsdl:part name="parameters" element="tns:GetAudioChannelFromRecorder" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_GetAudioChannelFromRecorder_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetAudioChannelFromRecorderResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_DeleteAudioChannels_InputMessage">
    <wsdl:part name="parameters" element="tns:DeleteAudioChannels" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_DeleteAudioChannels_OutputMessage">
    <wsdl:part name="parameters" element="tns:DeleteAudioChannelsResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_CreateAudioChannels_InputMessage">
    <wsdl:part name="parameters" element="tns:CreateAudioChannels" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_CreateAudioChannels_OutputMessage">
    <wsdl:part name="parameters" element="tns:CreateAudioChannelsResponse" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_UpdateAudioChannel_InputMessage">
    <wsdl:part name="parameters" element="tns:UpdateAudioChannel" />
  </wsdl:message>
  <wsdl:message name="IRevEnterpriseSvc_UpdateAudioChannel_OutputMessage">
    <wsdl:part name="parameters" element="tns:UpdateAudioChannelResponse" />
  </wsdl:message>
  <wsdl:portType name="IRevEnterpriseSvc">
    <wsdl:operation name="CallAuditSave">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/CallAuditSave" message="tns:IRevEnterpriseSvc_CallAuditSave_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/CallAuditSaveResponse" message="tns:IRevEnterpriseSvc_CallAuditSave_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetCallAuditTrail">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetCallAuditTrail" message="tns:IRevEnterpriseSvc_GetCallAuditTrail_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetCallAuditTrailResponse" message="tns:IRevEnterpriseSvc_GetCallAuditTrail_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetAuditedCallsByExtension">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetAuditedCallsByExtension" message="tns:IRevEnterpriseSvc_GetAuditedCallsByExtension_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetAuditedCallsByExtensionResponse" message="tns:IRevEnterpriseSvc_GetAuditedCallsByExtension_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetUserActivities">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetUserActivities" message="tns:IRevEnterpriseSvc_GetUserActivities_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetUserActivitiesResponse" message="tns:IRevEnterpriseSvc_GetUserActivities_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetCallsByLocation">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetCallsByLocation" message="tns:IRevEnterpriseSvc_GetCallsByLocation_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetCallsByLocationResponse" message="tns:IRevEnterpriseSvc_GetCallsByLocation_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetDefaultSearchResultsDTO">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetDefaultSearchResultsDTO" message="tns:IRevEnterpriseSvc_GetDefaultSearchResultsDTO_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetDefaultSearchResultsDTOResponse" message="tns:IRevEnterpriseSvc_GetDefaultSearchResultsDTO_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetAdvanceSearchResultsDTO">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetAdvanceSearchResultsDTO" message="tns:IRevEnterpriseSvc_GetAdvanceSearchResultsDTO_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetAdvanceSearchResultsDTOResponse" message="tns:IRevEnterpriseSvc_GetAdvanceSearchResultsDTO_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="PerformSearchChainedDBs">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/PerformSearchChainedDBs" message="tns:IRevEnterpriseSvc_PerformSearchChainedDBs_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/PerformSearchChainedDBsResponse" message="tns:IRevEnterpriseSvc_PerformSearchChainedDBs_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetDefaultQASearchResultsDTO">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetDefaultQASearchResultsDTO" message="tns:IRevEnterpriseSvc_GetDefaultQASearchResultsDTO_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetDefaultQASearchResultsDTOResponse" message="tns:IRevEnterpriseSvc_GetDefaultQASearchResultsDTO_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetAdvanceQASearchResultsDTO">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetAdvanceQASearchResultsDTO" message="tns:IRevEnterpriseSvc_GetAdvanceQASearchResultsDTO_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetAdvanceQASearchResultsDTOResponse" message="tns:IRevEnterpriseSvc_GetAdvanceQASearchResultsDTO_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="PerformQASearchChainedDBs">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/PerformQASearchChainedDBs" message="tns:IRevEnterpriseSvc_PerformQASearchChainedDBs_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/PerformQASearchChainedDBsResponse" message="tns:IRevEnterpriseSvc_PerformQASearchChainedDBs_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetUserExtensionInfos">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetUserExtensionInfos" message="tns:IRevEnterpriseSvc_GetUserExtensionInfos_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetUserExtensionInfosResponse" message="tns:IRevEnterpriseSvc_GetUserExtensionInfos_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetAppUsers">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetAppUsers" message="tns:IRevEnterpriseSvc_GetAppUsers_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetAppUsersResponse" message="tns:IRevEnterpriseSvc_GetAppUsers_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetAppUserAccount">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetAppUserAccount" message="tns:IRevEnterpriseSvc_GetAppUserAccount_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetAppUserAccountResponse" message="tns:IRevEnterpriseSvc_GetAppUserAccount_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="RecoverAppUserAccount">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/RecoverAppUserAccount" message="tns:IRevEnterpriseSvc_RecoverAppUserAccount_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/RecoverAppUserAccountResponse" message="tns:IRevEnterpriseSvc_RecoverAppUserAccount_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="UpdateAppUserAccount">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/UpdateAppUserAccount" message="tns:IRevEnterpriseSvc_UpdateAppUserAccount_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/UpdateAppUserAccountResponse" message="tns:IRevEnterpriseSvc_UpdateAppUserAccount_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="DeleteAppUserAccount">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/DeleteAppUserAccount" message="tns:IRevEnterpriseSvc_DeleteAppUserAccount_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/DeleteAppUserAccountResponse" message="tns:IRevEnterpriseSvc_DeleteAppUserAccount_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SaveAssignedGroup">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/SaveAssignedGroup" message="tns:IRevEnterpriseSvc_SaveAssignedGroup_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/SaveAssignedGroupResponse" message="tns:IRevEnterpriseSvc_SaveAssignedGroup_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="DeleteUserGroup">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/DeleteUserGroup" message="tns:IRevEnterpriseSvc_DeleteUserGroup_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/DeleteUserGroupResponse" message="tns:IRevEnterpriseSvc_DeleteUserGroup_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetUserData">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetUserData" message="tns:IRevEnterpriseSvc_GetUserData_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetUserDataResponse" message="tns:IRevEnterpriseSvc_GetUserData_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="FetchAllActiveUsers">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/FetchAllActiveUsers" message="tns:IRevEnterpriseSvc_FetchAllActiveUsers_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/FetchAllActiveUsersResponse" message="tns:IRevEnterpriseSvc_FetchAllActiveUsers_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetTreeViewFromRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetTreeViewFromRecorder" message="tns:IRevEnterpriseSvc_GetTreeViewFromRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetTreeViewFromRecorderResponse" message="tns:IRevEnterpriseSvc_GetTreeViewFromRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetGroupsTree">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetGroupsTree" message="tns:IRevEnterpriseSvc_GetGroupsTree_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetGroupsTreeResponse" message="tns:IRevEnterpriseSvc_GetGroupsTree_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetGroupsTreeFromRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetGroupsTreeFromRecorder" message="tns:IRevEnterpriseSvc_GetGroupsTreeFromRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetGroupsTreeFromRecorderResponse" message="tns:IRevEnterpriseSvc_GetGroupsTreeFromRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetGroupsTreeNonAdminFromRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetGroupsTreeNonAdminFromRecorder" message="tns:IRevEnterpriseSvc_GetGroupsTreeNonAdminFromRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetGroupsTreeNonAdminFromRecorderResponse" message="tns:IRevEnterpriseSvc_GetGroupsTreeNonAdminFromRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetUsersTreeFromRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetUsersTreeFromRecorder" message="tns:IRevEnterpriseSvc_GetUsersTreeFromRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetUsersTreeFromRecorderResponse" message="tns:IRevEnterpriseSvc_GetUsersTreeFromRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetInquireGroupsTreeforEvaluationReportFromRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetInquireGroupsTreeforEvaluationReportFromRecorder" message="tns:IRevEnterpriseSvc_GetInquireGroupsTreeforEvaluationReportFromRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetInquireGroupsTreeforEvaluationReportFromRecorderResponse" message="tns:IRevEnterpriseSvc_GetInquireGroupsTreeforEvaluationReportFromRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetMDGroupsTreeForEvaluationReportFromRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetMDGroupsTreeForEvaluationReportFromRecorder" message="tns:IRevEnterpriseSvc_GetMDGroupsTreeForEvaluationReportFromRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetMDGroupsTreeForEvaluationReportFromRecorderResponse" message="tns:IRevEnterpriseSvc_GetMDGroupsTreeForEvaluationReportFromRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetEnterpriseUserRightsData">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetEnterpriseUserRightsData" message="tns:IRevEnterpriseSvc_GetEnterpriseUserRightsData_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetEnterpriseUserRightsDataResponse" message="tns:IRevEnterpriseSvc_GetEnterpriseUserRightsData_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetEnterpriseUserTree">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetEnterpriseUserTree" message="tns:IRevEnterpriseSvc_GetEnterpriseUserTree_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetEnterpriseUserTreeResponse" message="tns:IRevEnterpriseSvc_GetEnterpriseUserTree_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetChannelsToMonitorFromRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetChannelsToMonitorFromRecorder" message="tns:IRevEnterpriseSvc_GetChannelsToMonitorFromRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetChannelsToMonitorFromRecorderResponse" message="tns:IRevEnterpriseSvc_GetChannelsToMonitorFromRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetSurveysFromRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetSurveysFromRecorder" message="tns:IRevEnterpriseSvc_GetSurveysFromRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetSurveysFromRecorderResponse" message="tns:IRevEnterpriseSvc_GetSurveysFromRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetPublishedSurveysFromRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetPublishedSurveysFromRecorder" message="tns:IRevEnterpriseSvc_GetPublishedSurveysFromRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetPublishedSurveysFromRecorderResponse" message="tns:IRevEnterpriseSvc_GetPublishedSurveysFromRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="DeleteAndGetSurveysFromRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/DeleteAndGetSurveysFromRecorder" message="tns:IRevEnterpriseSvc_DeleteAndGetSurveysFromRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/DeleteAndGetSurveysFromRecorderResponse" message="tns:IRevEnterpriseSvc_DeleteAndGetSurveysFromRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="PublishAndGetSurveysFromRecorders">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/PublishAndGetSurveysFromRecorders" message="tns:IRevEnterpriseSvc_PublishAndGetSurveysFromRecorders_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/PublishAndGetSurveysFromRecordersResponse" message="tns:IRevEnterpriseSvc_PublishAndGetSurveysFromRecorders_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetSurveyDetailsFromRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetSurveyDetailsFromRecorder" message="tns:IRevEnterpriseSvc_GetSurveyDetailsFromRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetSurveyDetailsFromRecorderResponse" message="tns:IRevEnterpriseSvc_GetSurveyDetailsFromRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="IsSurveyExistOnRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/IsSurveyExistOnRecorder" message="tns:IRevEnterpriseSvc_IsSurveyExistOnRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/IsSurveyExistOnRecorderResponse" message="tns:IRevEnterpriseSvc_IsSurveyExistOnRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="CreateSurveyOnRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/CreateSurveyOnRecorder" message="tns:IRevEnterpriseSvc_CreateSurveyOnRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/CreateSurveyOnRecorderResponse" message="tns:IRevEnterpriseSvc_CreateSurveyOnRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="CreateQuestionOnRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/CreateQuestionOnRecorder" message="tns:IRevEnterpriseSvc_CreateQuestionOnRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/CreateQuestionOnRecorderResponse" message="tns:IRevEnterpriseSvc_CreateQuestionOnRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="CreateAndGetSectionsOnRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/CreateAndGetSectionsOnRecorder" message="tns:IRevEnterpriseSvc_CreateAndGetSectionsOnRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/CreateAndGetSectionsOnRecorderResponse" message="tns:IRevEnterpriseSvc_CreateAndGetSectionsOnRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetQuestionsBySurveyIdFromRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetQuestionsBySurveyIdFromRecorder" message="tns:IRevEnterpriseSvc_GetQuestionsBySurveyIdFromRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetQuestionsBySurveyIdFromRecorderResponse" message="tns:IRevEnterpriseSvc_GetQuestionsBySurveyIdFromRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetSectionsBySurveyIdFromRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetSectionsBySurveyIdFromRecorder" message="tns:IRevEnterpriseSvc_GetSectionsBySurveyIdFromRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetSectionsBySurveyIdFromRecorderResponse" message="tns:IRevEnterpriseSvc_GetSectionsBySurveyIdFromRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="UpdateQuestionSectionOnRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/UpdateQuestionSectionOnRecorder" message="tns:IRevEnterpriseSvc_UpdateQuestionSectionOnRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/UpdateQuestionSectionOnRecorderResponse" message="tns:IRevEnterpriseSvc_UpdateQuestionSectionOnRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="UpdateAndGetSectionsFromRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/UpdateAndGetSectionsFromRecorder" message="tns:IRevEnterpriseSvc_UpdateAndGetSectionsFromRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/UpdateAndGetSectionsFromRecorderResponse" message="tns:IRevEnterpriseSvc_UpdateAndGetSectionsFromRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="DeleteAndGetSectionsFromRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/DeleteAndGetSectionsFromRecorder" message="tns:IRevEnterpriseSvc_DeleteAndGetSectionsFromRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/DeleteAndGetSectionsFromRecorderResponse" message="tns:IRevEnterpriseSvc_DeleteAndGetSectionsFromRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="AssignUnAssignQuestionsAndGetSectionsFromRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/AssignUnAssignQuestionsAndGetSectionsFromRecorder" message="tns:IRevEnterpriseSvc_AssignUnAssignQuestionsAndGetSectionsFromRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/AssignUnAssignQuestionsAndGetSectionsFromRecorderResponse" message="tns:IRevEnterpriseSvc_AssignUnAssignQuestionsAndGetSectionsFromRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetSurveyFromRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetSurveyFromRecorder" message="tns:IRevEnterpriseSvc_GetSurveyFromRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetSurveyFromRecorderResponse" message="tns:IRevEnterpriseSvc_GetSurveyFromRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="UpdateSurveyOnRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/UpdateSurveyOnRecorder" message="tns:IRevEnterpriseSvc_UpdateSurveyOnRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/UpdateSurveyOnRecorderResponse" message="tns:IRevEnterpriseSvc_UpdateSurveyOnRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="UpdateQuestionOnRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/UpdateQuestionOnRecorder" message="tns:IRevEnterpriseSvc_UpdateQuestionOnRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/UpdateQuestionOnRecorderResponse" message="tns:IRevEnterpriseSvc_UpdateQuestionOnRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="DeleteQuestionFromRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/DeleteQuestionFromRecorder" message="tns:IRevEnterpriseSvc_DeleteQuestionFromRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/DeleteQuestionFromRecorderResponse" message="tns:IRevEnterpriseSvc_DeleteQuestionFromRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="UpdateIsPublishedOnRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/UpdateIsPublishedOnRecorder" message="tns:IRevEnterpriseSvc_UpdateIsPublishedOnRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/UpdateIsPublishedOnRecorderResponse" message="tns:IRevEnterpriseSvc_UpdateIsPublishedOnRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="UpdateQuestionsOrderOnRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/UpdateQuestionsOrderOnRecorder" message="tns:IRevEnterpriseSvc_UpdateQuestionsOrderOnRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/UpdateQuestionsOrderOnRecorderResponse" message="tns:IRevEnterpriseSvc_UpdateQuestionsOrderOnRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SearchCallsPrimaryDB">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/SearchCallsPrimaryDB" message="tns:IRevEnterpriseSvc_SearchCallsPrimaryDB_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/SearchCallsPrimaryDBResponse" message="tns:IRevEnterpriseSvc_SearchCallsPrimaryDB_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SearchRandomCallsPrimaryDB">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/SearchRandomCallsPrimaryDB" message="tns:IRevEnterpriseSvc_SearchRandomCallsPrimaryDB_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/SearchRandomCallsPrimaryDBResponse" message="tns:IRevEnterpriseSvc_SearchRandomCallsPrimaryDB_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SearchCallsChainedDBs">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/SearchCallsChainedDBs" message="tns:IRevEnterpriseSvc_SearchCallsChainedDBs_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/SearchCallsChainedDBsResponse" message="tns:IRevEnterpriseSvc_SearchCallsChainedDBs_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="UpdateCallsCustomFields">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/UpdateCallsCustomFields" message="tns:IRevEnterpriseSvc_UpdateCallsCustomFields_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/UpdateCallsCustomFieldsResponse" message="tns:IRevEnterpriseSvc_UpdateCallsCustomFields_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="UpdateCallCustomFields">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/UpdateCallCustomFields" message="tns:IRevEnterpriseSvc_UpdateCallCustomFields_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/UpdateCallCustomFieldsResponse" message="tns:IRevEnterpriseSvc_UpdateCallCustomFields_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="UpdateCallRetention">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/UpdateCallRetention" message="tns:IRevEnterpriseSvc_UpdateCallRetention_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/UpdateCallRetentionResponse" message="tns:IRevEnterpriseSvc_UpdateCallRetention_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="Inquiremarkerupdate">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/Inquiremarkerupdate" message="tns:IRevEnterpriseSvc_Inquiremarkerupdate_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/InquiremarkerupdateResponse" message="tns:IRevEnterpriseSvc_Inquiremarkerupdate_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetCallsByIds">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetCallsByIds" message="tns:IRevEnterpriseSvc_GetCallsByIds_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetCallsByIdsResponse" message="tns:IRevEnterpriseSvc_GetCallsByIds_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="InsertBookmarkAndGetByCallId">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/InsertBookmarkAndGetByCallId" message="tns:IRevEnterpriseSvc_InsertBookmarkAndGetByCallId_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/InsertBookmarkAndGetByCallIdResponse" message="tns:IRevEnterpriseSvc_InsertBookmarkAndGetByCallId_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="UpdateBookmarkAndGetByCallId">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/UpdateBookmarkAndGetByCallId" message="tns:IRevEnterpriseSvc_UpdateBookmarkAndGetByCallId_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/UpdateBookmarkAndGetByCallIdResponse" message="tns:IRevEnterpriseSvc_UpdateBookmarkAndGetByCallId_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetCallInfoExportResults">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetCallInfoExportResults" message="tns:IRevEnterpriseSvc_GetCallInfoExportResults_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetCallInfoExportResultsResponse" message="tns:IRevEnterpriseSvc_GetCallInfoExportResults_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetCallInfoExportResultsByIds">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetCallInfoExportResultsByIds" message="tns:IRevEnterpriseSvc_GetCallInfoExportResultsByIds_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetCallInfoExportResultsByIdsResponse" message="tns:IRevEnterpriseSvc_GetCallInfoExportResultsByIds_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetAllDrilldownChartsFromRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetAllDrilldownChartsFromRecorder" message="tns:IRevEnterpriseSvc_GetAllDrilldownChartsFromRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetAllDrilldownChartsFromRecorderResponse" message="tns:IRevEnterpriseSvc_GetAllDrilldownChartsFromRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="InsertCallsForEvaluation">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/InsertCallsForEvaluation" message="tns:IRevEnterpriseSvc_InsertCallsForEvaluation_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/InsertCallsForEvaluationResponse" message="tns:IRevEnterpriseSvc_InsertCallsForEvaluation_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="InsertEnterpriseEvaluations">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/InsertEnterpriseEvaluations" message="tns:IRevEnterpriseSvc_InsertEnterpriseEvaluations_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/InsertEnterpriseEvaluationsResponse" message="tns:IRevEnterpriseSvc_InsertEnterpriseEvaluations_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="AddEnterpriseEvaluations">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/AddEnterpriseEvaluations" message="tns:IRevEnterpriseSvc_AddEnterpriseEvaluations_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/AddEnterpriseEvaluationsResponse" message="tns:IRevEnterpriseSvc_AddEnterpriseEvaluations_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetEvaluations">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetEvaluations" message="tns:IRevEnterpriseSvc_GetEvaluations_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetEvaluationsResponse" message="tns:IRevEnterpriseSvc_GetEvaluations_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetEnterpriseAssociatedEvaluations">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetEnterpriseAssociatedEvaluations" message="tns:IRevEnterpriseSvc_GetEnterpriseAssociatedEvaluations_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetEnterpriseAssociatedEvaluationsResponse" message="tns:IRevEnterpriseSvc_GetEnterpriseAssociatedEvaluations_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetUsers">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetUsers" message="tns:IRevEnterpriseSvc_GetUsers_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetUsersResponse" message="tns:IRevEnterpriseSvc_GetUsers_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="PerformActionAndGetEnterpriseEvaluationDTO">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/PerformActionAndGetEnterpriseEvaluationDTO" message="tns:IRevEnterpriseSvc_PerformActionAndGetEnterpriseEvaluationDTO_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/PerformActionAndGetEnterpriseEvaluationDTOResponse" message="tns:IRevEnterpriseSvc_PerformActionAndGetEnterpriseEvaluationDTO_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ShareUnshareAndGetEvaluationDTO">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/ShareUnshareAndGetEvaluationDTO" message="tns:IRevEnterpriseSvc_ShareUnshareAndGetEvaluationDTO_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/ShareUnshareAndGetEvaluationDTOResponse" message="tns:IRevEnterpriseSvc_ShareUnshareAndGetEvaluationDTO_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetCallEvaluationDetailsById">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetCallEvaluationDetailsById" message="tns:IRevEnterpriseSvc_GetCallEvaluationDetailsById_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetCallEvaluationDetailsByIdResponse" message="tns:IRevEnterpriseSvc_GetCallEvaluationDetailsById_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="UpdateCallEvaluation">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/UpdateCallEvaluation" message="tns:IRevEnterpriseSvc_UpdateCallEvaluation_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/UpdateCallEvaluationResponse" message="tns:IRevEnterpriseSvc_UpdateCallEvaluation_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="UpdateAndGetAssociatedUser">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/UpdateAndGetAssociatedUser" message="tns:IRevEnterpriseSvc_UpdateAndGetAssociatedUser_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/UpdateAndGetAssociatedUserResponse" message="tns:IRevEnterpriseSvc_UpdateAndGetAssociatedUser_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetEvaluationId">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetEvaluationId" message="tns:IRevEnterpriseSvc_GetEvaluationId_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetEvaluationIdResponse" message="tns:IRevEnterpriseSvc_GetEvaluationId_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="PerformActionOnRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/PerformActionOnRecorder" message="tns:IRevEnterpriseSvc_PerformActionOnRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/PerformActionOnRecorderResponse" message="tns:IRevEnterpriseSvc_PerformActionOnRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SearchRecordedCalls">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/SearchRecordedCalls" message="tns:IRevEnterpriseSvc_SearchRecordedCalls_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/SearchRecordedCallsResponse" message="tns:IRevEnterpriseSvc_SearchRecordedCalls_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetCallDetailsFromRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetCallDetailsFromRecorder" message="tns:IRevEnterpriseSvc_GetCallDetailsFromRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetCallDetailsFromRecorderResponse" message="tns:IRevEnterpriseSvc_GetCallDetailsFromRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetSearchResults">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetSearchResults" message="tns:IRevEnterpriseSvc_GetSearchResults_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetSearchResultsResponse" message="tns:IRevEnterpriseSvc_GetSearchResults_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetDetailSearchResults">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetDetailSearchResults" message="tns:IRevEnterpriseSvc_GetDetailSearchResults_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetDetailSearchResultsResponse" message="tns:IRevEnterpriseSvc_GetDetailSearchResults_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetSearchResultsMonthDayOfWeek">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetSearchResultsMonthDayOfWeek" message="tns:IRevEnterpriseSvc_GetSearchResultsMonthDayOfWeek_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetSearchResultsMonthDayOfWeekResponse" message="tns:IRevEnterpriseSvc_GetSearchResultsMonthDayOfWeek_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetSearchResultsDayOfWeek">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetSearchResultsDayOfWeek" message="tns:IRevEnterpriseSvc_GetSearchResultsDayOfWeek_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetSearchResultsDayOfWeekResponse" message="tns:IRevEnterpriseSvc_GetSearchResultsDayOfWeek_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetSearchResultsHour">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetSearchResultsHour" message="tns:IRevEnterpriseSvc_GetSearchResultsHour_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetSearchResultsHourResponse" message="tns:IRevEnterpriseSvc_GetSearchResultsHour_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetCallByIdFromRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetCallByIdFromRecorder" message="tns:IRevEnterpriseSvc_GetCallByIdFromRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetCallByIdFromRecorderResponse" message="tns:IRevEnterpriseSvc_GetCallByIdFromRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetSearchResults911">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetSearchResults911" message="tns:IRevEnterpriseSvc_GetSearchResults911_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetSearchResults911Response" message="tns:IRevEnterpriseSvc_GetSearchResults911_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetCallAuditSearchResults">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetCallAuditSearchResults" message="tns:IRevEnterpriseSvc_GetCallAuditSearchResults_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetCallAuditSearchResultsResponse" message="tns:IRevEnterpriseSvc_GetCallAuditSearchResults_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetCallsNotAuditedSearchResults">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetCallsNotAuditedSearchResults" message="tns:IRevEnterpriseSvc_GetCallsNotAuditedSearchResults_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetCallsNotAuditedSearchResultsResponse" message="tns:IRevEnterpriseSvc_GetCallsNotAuditedSearchResults_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetRPTEvaluationReportFromRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetRPTEvaluationReportFromRecorder" message="tns:IRevEnterpriseSvc_GetRPTEvaluationReportFromRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetRPTEvaluationReportFromRecorderResponse" message="tns:IRevEnterpriseSvc_GetRPTEvaluationReportFromRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="UpdateConfiguration">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/UpdateConfiguration" message="tns:IRevEnterpriseSvc_UpdateConfiguration_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/UpdateConfigurationResponse" message="tns:IRevEnterpriseSvc_UpdateConfiguration_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="CheckDBConnection">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/CheckDBConnection" message="tns:IRevEnterpriseSvc_CheckDBConnection_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/CheckDBConnectionResponse" message="tns:IRevEnterpriseSvc_CheckDBConnection_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetAudioChannelsFromRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetAudioChannelsFromRecorder" message="tns:IRevEnterpriseSvc_GetAudioChannelsFromRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetAudioChannelsFromRecorderResponse" message="tns:IRevEnterpriseSvc_GetAudioChannelsFromRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetAudioChannelFromRecorder">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetAudioChannelFromRecorder" message="tns:IRevEnterpriseSvc_GetAudioChannelFromRecorder_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/GetAudioChannelFromRecorderResponse" message="tns:IRevEnterpriseSvc_GetAudioChannelFromRecorder_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="DeleteAudioChannels">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/DeleteAudioChannels" message="tns:IRevEnterpriseSvc_DeleteAudioChannels_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/DeleteAudioChannelsResponse" message="tns:IRevEnterpriseSvc_DeleteAudioChannels_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="CreateAudioChannels">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/CreateAudioChannels" message="tns:IRevEnterpriseSvc_CreateAudioChannels_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/CreateAudioChannelsResponse" message="tns:IRevEnterpriseSvc_CreateAudioChannels_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="UpdateAudioChannel">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/UpdateAudioChannel" message="tns:IRevEnterpriseSvc_UpdateAudioChannel_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevEnterpriseSvc/UpdateAudioChannelResponse" message="tns:IRevEnterpriseSvc_UpdateAudioChannel_OutputMessage" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="BasicHttpBinding_IRevEnterpriseSvc" type="tns:IRevEnterpriseSvc">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="CallAuditSave">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/CallAuditSave" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCallAuditTrail">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetCallAuditTrail" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAuditedCallsByExtension">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetAuditedCallsByExtension" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetUserActivities">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetUserActivities" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCallsByLocation">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetCallsByLocation" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDefaultSearchResultsDTO">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetDefaultSearchResultsDTO" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAdvanceSearchResultsDTO">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetAdvanceSearchResultsDTO" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PerformSearchChainedDBs">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/PerformSearchChainedDBs" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDefaultQASearchResultsDTO">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetDefaultQASearchResultsDTO" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAdvanceQASearchResultsDTO">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetAdvanceQASearchResultsDTO" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PerformQASearchChainedDBs">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/PerformQASearchChainedDBs" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetUserExtensionInfos">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetUserExtensionInfos" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAppUsers">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetAppUsers" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAppUserAccount">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetAppUserAccount" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RecoverAppUserAccount">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/RecoverAppUserAccount" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateAppUserAccount">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/UpdateAppUserAccount" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteAppUserAccount">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/DeleteAppUserAccount" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SaveAssignedGroup">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/SaveAssignedGroup" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteUserGroup">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/DeleteUserGroup" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetUserData">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetUserData" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FetchAllActiveUsers">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/FetchAllActiveUsers" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetTreeViewFromRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetTreeViewFromRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetGroupsTree">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetGroupsTree" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetGroupsTreeFromRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetGroupsTreeFromRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetGroupsTreeNonAdminFromRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetGroupsTreeNonAdminFromRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetUsersTreeFromRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetUsersTreeFromRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetInquireGroupsTreeforEvaluationReportFromRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetInquireGroupsTreeforEvaluationReportFromRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMDGroupsTreeForEvaluationReportFromRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetMDGroupsTreeForEvaluationReportFromRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetEnterpriseUserRightsData">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetEnterpriseUserRightsData" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetEnterpriseUserTree">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetEnterpriseUserTree" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetChannelsToMonitorFromRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetChannelsToMonitorFromRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSurveysFromRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetSurveysFromRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetPublishedSurveysFromRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetPublishedSurveysFromRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteAndGetSurveysFromRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/DeleteAndGetSurveysFromRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PublishAndGetSurveysFromRecorders">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/PublishAndGetSurveysFromRecorders" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSurveyDetailsFromRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetSurveyDetailsFromRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IsSurveyExistOnRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/IsSurveyExistOnRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateSurveyOnRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/CreateSurveyOnRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateQuestionOnRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/CreateQuestionOnRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateAndGetSectionsOnRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/CreateAndGetSectionsOnRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetQuestionsBySurveyIdFromRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetQuestionsBySurveyIdFromRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSectionsBySurveyIdFromRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetSectionsBySurveyIdFromRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateQuestionSectionOnRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/UpdateQuestionSectionOnRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateAndGetSectionsFromRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/UpdateAndGetSectionsFromRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteAndGetSectionsFromRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/DeleteAndGetSectionsFromRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AssignUnAssignQuestionsAndGetSectionsFromRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/AssignUnAssignQuestionsAndGetSectionsFromRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSurveyFromRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetSurveyFromRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateSurveyOnRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/UpdateSurveyOnRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateQuestionOnRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/UpdateQuestionOnRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteQuestionFromRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/DeleteQuestionFromRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateIsPublishedOnRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/UpdateIsPublishedOnRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateQuestionsOrderOnRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/UpdateQuestionsOrderOnRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SearchCallsPrimaryDB">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/SearchCallsPrimaryDB" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SearchRandomCallsPrimaryDB">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/SearchRandomCallsPrimaryDB" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SearchCallsChainedDBs">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/SearchCallsChainedDBs" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateCallsCustomFields">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/UpdateCallsCustomFields" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateCallCustomFields">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/UpdateCallCustomFields" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateCallRetention">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/UpdateCallRetention" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Inquiremarkerupdate">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/Inquiremarkerupdate" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCallsByIds">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetCallsByIds" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="InsertBookmarkAndGetByCallId">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/InsertBookmarkAndGetByCallId" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateBookmarkAndGetByCallId">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/UpdateBookmarkAndGetByCallId" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCallInfoExportResults">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetCallInfoExportResults" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCallInfoExportResultsByIds">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetCallInfoExportResultsByIds" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAllDrilldownChartsFromRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetAllDrilldownChartsFromRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="InsertCallsForEvaluation">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/InsertCallsForEvaluation" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="InsertEnterpriseEvaluations">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/InsertEnterpriseEvaluations" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddEnterpriseEvaluations">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/AddEnterpriseEvaluations" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetEvaluations">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetEvaluations" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetEnterpriseAssociatedEvaluations">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetEnterpriseAssociatedEvaluations" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetUsers">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetUsers" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PerformActionAndGetEnterpriseEvaluationDTO">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/PerformActionAndGetEnterpriseEvaluationDTO" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ShareUnshareAndGetEvaluationDTO">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/ShareUnshareAndGetEvaluationDTO" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCallEvaluationDetailsById">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetCallEvaluationDetailsById" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateCallEvaluation">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/UpdateCallEvaluation" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateAndGetAssociatedUser">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/UpdateAndGetAssociatedUser" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetEvaluationId">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetEvaluationId" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PerformActionOnRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/PerformActionOnRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SearchRecordedCalls">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/SearchRecordedCalls" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCallDetailsFromRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetCallDetailsFromRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSearchResults">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetSearchResults" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDetailSearchResults">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetDetailSearchResults" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSearchResultsMonthDayOfWeek">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetSearchResultsMonthDayOfWeek" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSearchResultsDayOfWeek">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetSearchResultsDayOfWeek" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSearchResultsHour">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetSearchResultsHour" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCallByIdFromRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetCallByIdFromRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSearchResults911">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetSearchResults911" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCallAuditSearchResults">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetCallAuditSearchResults" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCallsNotAuditedSearchResults">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetCallsNotAuditedSearchResults" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetRPTEvaluationReportFromRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetRPTEvaluationReportFromRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateConfiguration">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/UpdateConfiguration" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckDBConnection">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/CheckDBConnection" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAudioChannelsFromRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetAudioChannelsFromRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAudioChannelFromRecorder">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/GetAudioChannelFromRecorder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteAudioChannels">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/DeleteAudioChannels" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateAudioChannels">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/CreateAudioChannels" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateAudioChannel">
      <soap:operation soapAction="http://tempuri.org/IRevEnterpriseSvc/UpdateAudioChannel" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="RevEnterpriseSvc">
    <wsdl:port name="BasicHttpBinding_IRevEnterpriseSvc" binding="tns:BasicHttpBinding_IRevEnterpriseSvc">
      <soap:address location="http://localhost:62195/RevEnterpriseSvc.svc" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>