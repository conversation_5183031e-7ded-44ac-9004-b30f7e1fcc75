<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.Messages" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.Messages" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd14" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.IQ3" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd4" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.DTO" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd5" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd11" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.RoleManagement" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd7" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.Criteria" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd3" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd9" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd6" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.MessageBase" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd12" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ViewModelEntities" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd15" namespace="http://schemas.datacontract.org/2004/07/System" />
  <xs:complexType name="UMRequest">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q1="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.MessageBase" base="q1:RequestBase">
        <xs:sequence>
          <xs:element xmlns:q2="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.IQ3" minOccurs="0" name="CustomField" nillable="true" type="q2:CustomField" />
          <xs:element xmlns:q3="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.DTO" minOccurs="0" name="EnterpriseGroupRight" nillable="true" type="q3:EnterpriseGroupRightDTO" />
          <xs:element xmlns:q4="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.DTO" minOccurs="0" name="EnterpriseUserNodes" nillable="true" type="q4:ArrayOfEnterpriseNodeDTO" />
          <xs:element minOccurs="0" name="IsDemoMode" type="xs:boolean" />
          <xs:element minOccurs="0" name="IsInquireView" type="xs:boolean" />
          <xs:element minOccurs="0" name="IsRoleBasedAccessEnabled" type="xs:boolean" />
          <xs:element xmlns:q5="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="Recorders" nillable="true" type="q5:ArrayOfRecorder" />
          <xs:element xmlns:q6="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.RoleManagement" minOccurs="0" name="Role" nillable="true" type="q6:Role" />
          <xs:element xmlns:q7="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.Criteria" minOccurs="0" name="TreeCriteria" nillable="true" type="q7:GroupTreeCriteria" />
          <xs:element xmlns:q8="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" minOccurs="0" name="User" nillable="true" type="q8:User" />
          <xs:element xmlns:q9="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" minOccurs="0" name="UserActivity" nillable="true" type="q9:UserActivity" />
          <xs:element minOccurs="0" name="UserId" type="xs:int" />
          <xs:element xmlns:q10="http://schemas.datacontract.org/2004/07/RevCord.DataContracts" minOccurs="0" name="UserType" type="q10:UserType" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="UMRequest" nillable="true" type="tns:UMRequest" />
  <xs:complexType name="UMResponse">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q11="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.MessageBase" base="q11:ResponseBase">
        <xs:sequence>
          <xs:element xmlns:q12="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ViewModelEntities" minOccurs="0" name="AllGroups" nillable="true" type="q12:ArrayOfGroupTree" />
          <xs:element xmlns:q13="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" minOccurs="0" name="AssignedRights" nillable="true" type="q13:ArrayOfAssignedSimpleUserRights" />
          <xs:element xmlns:q14="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ViewModelEntities" minOccurs="0" name="AudioGroup" nillable="true" type="q14:GroupTree" />
          <xs:element xmlns:q15="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.IQ3" minOccurs="0" name="CustomField" nillable="true" type="q15:CustomField" />
          <xs:element xmlns:q16="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.IQ3" minOccurs="0" name="CustomFields" nillable="true" type="q16:ArrayOfCustomField" />
          <xs:element xmlns:q17="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ViewModelEntities" minOccurs="0" name="DBExtension" nillable="true" type="q17:ArrayOfTreeviewData" />
          <xs:element xmlns:q18="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.DTO" minOccurs="0" name="EnterpriseNodeDTO" nillable="true" type="q18:ArrayOfEnterpriseNodeDTO" />
          <xs:element xmlns:q19="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ViewModelEntities" minOccurs="0" name="InquireRecorderGroups" nillable="true" type="q19:ArrayOfRecorderGroupTree" />
          <xs:element xmlns:q20="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ViewModelEntities" minOccurs="0" name="Inquire_group" nillable="true" type="q20:GroupTree" />
          <xs:element xmlns:q21="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ViewModelEntities" minOccurs="0" name="MD_group" nillable="true" type="q21:GroupTree" />
          <xs:element minOccurs="0" name="NoOfTreeNode" type="xs:int" />
          <xs:element xmlns:q22="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ViewModelEntities" minOccurs="0" name="RecorderGroup" nillable="true" type="q22:RecorderGroupTree" />
          <xs:element xmlns:q23="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ViewModelEntities" minOccurs="0" name="RecorderGroups" nillable="true" type="q23:ArrayOfRecorderGroupTree" />
          <xs:element xmlns:q24="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ViewModelEntities" minOccurs="0" name="RecorderUsersOld" nillable="true" type="q24:ArrayOfRecorderUserOld" />
          <xs:element minOccurs="0" name="TotalPages" type="xs:int" />
          <xs:element minOccurs="0" name="TotalRecords" type="xs:long" />
          <xs:element xmlns:q25="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" minOccurs="0" name="UserActivities" nillable="true" type="q25:ArrayOfUserActivity" />
          <xs:element minOccurs="0" name="UserActivityId" type="xs:long" />
          <xs:element minOccurs="0" name="UserNum" type="xs:int" />
          <xs:element xmlns:q26="http://schemas.datacontract.org/2004/07/System" minOccurs="0" name="UserRecordersGroups" nillable="true" type="q26:ArrayOfTupleOfintint" />
          <xs:element xmlns:q27="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" minOccurs="0" name="Users" nillable="true" type="q27:ArrayOfUser" />
          <xs:element xmlns:q28="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ViewModelEntities" minOccurs="0" name="gvData" nillable="true" type="q28:ArrayOfGridViewData" />
          <xs:element xmlns:q29="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ViewModelEntities" minOccurs="0" name="inquireDbNodes" nillable="true" type="q29:ArrayOfTreeviewData" />
          <xs:element xmlns:q30="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ViewModelEntities" minOccurs="0" name="lvData" nillable="true" type="q30:ArrayOfCustomMarkersData" />
          <xs:element xmlns:q31="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ViewModelEntities" minOccurs="0" name="lvData1" nillable="true" type="q31:ArrayOfCustomMarkersData" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="UMResponse" nillable="true" type="tns:UMResponse" />
</xs:schema>