<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.RoleManagement" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.RoleManagement" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:complexType name="ArrayOfRolePermission">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="RolePermission" nillable="true" type="tns:RolePermission" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfRolePermission" nillable="true" type="tns:ArrayOfRolePermission" />
  <xs:complexType name="RolePermission">
    <xs:sequence>
      <xs:element minOccurs="0" name="CreatedDate" type="xs:dateTime" />
      <xs:element minOccurs="0" name="Id" type="xs:int" />
      <xs:element minOccurs="0" name="IsDeleted" type="xs:boolean" />
      <xs:element minOccurs="0" name="PermissionId" type="xs:int" />
      <xs:element minOccurs="0" name="RoleId" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RolePermission" nillable="true" type="tns:RolePermission" />
  <xs:complexType name="Role">
    <xs:sequence>
      <xs:element minOccurs="0" name="AllocatedCount" type="xs:int" />
      <xs:element minOccurs="0" name="CreatedDate" type="xs:dateTime" />
      <xs:element minOccurs="0" name="Description" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Id" type="xs:int" />
      <xs:element minOccurs="0" name="IsDeleted" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsSystemRole" type="xs:boolean" />
      <xs:element minOccurs="0" name="Name" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PermissionsCount" type="xs:int" />
      <xs:element minOccurs="0" name="RoleType" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Role" nillable="true" type="tns:Role" />
  <xs:complexType name="ArrayOfRole">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="Role" nillable="true" type="tns:Role" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfRole" nillable="true" type="tns:ArrayOfRole" />
</xs:schema>