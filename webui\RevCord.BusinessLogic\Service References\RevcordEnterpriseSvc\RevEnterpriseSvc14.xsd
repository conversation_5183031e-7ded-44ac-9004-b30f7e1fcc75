<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.MessageBase" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.MessageBase" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd8" namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
  <xs:simpleType name="AcknowledgeType">
    <xs:annotation>
      <xs:appinfo>
        <ActualType Name="short" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
      </xs:appinfo>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="Failure" />
      <xs:enumeration value="Success" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="AcknowledgeType" nillable="true" type="tns:AcknowledgeType" />
  <xs:complexType name="ResponseBase">
    <xs:sequence>
      <xs:element minOccurs="0" name="Acknowledge" type="tns:AcknowledgeType" />
      <xs:element minOccurs="0" name="Message" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RowsAffected" type="xs:int" />
      <xs:element minOccurs="0" name="tagRule" type="tns:TagRuleInfo" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ResponseBase" nillable="true" type="tns:ResponseBase" />
  <xs:simpleType name="TagRuleInfo">
    <xs:annotation>
      <xs:appinfo>
        <ActualType Name="short" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
      </xs:appinfo>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="Yes">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="No">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">0</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="TagRuleInfo" nillable="true" type="tns:TagRuleInfo" />
  <xs:complexType name="RequestBase">
    <xs:sequence>
      <xs:element minOccurs="0" name="Action" nillable="true" type="xs:string" />
      <xs:element xmlns:q1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="LoadOptions" nillable="true" type="q1:ArrayOfstring" />
      <xs:element minOccurs="0" name="ModifiedDate" type="xs:dateTime" />
      <xs:element minOccurs="0" name="PersistType" type="tns:PersistType" />
      <xs:element minOccurs="0" name="TenantId" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RequestBase" nillable="true" type="tns:RequestBase" />
  <xs:simpleType name="PersistType">
    <xs:annotation>
      <xs:appinfo>
        <ActualType Name="short" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
      </xs:appinfo>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="Insert">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="Update">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="Delete">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">3</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="PersistType" nillable="true" type="tns:PersistType" />
</xs:schema>