<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.SurveyEntities" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.SurveyEntities" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:complexType name="ArrayOfSurvey">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="Survey" nillable="true" type="tns:Survey" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfSurvey" nillable="true" type="tns:ArrayOfSurvey" />
  <xs:complexType name="Survey">
    <xs:sequence>
      <xs:element name="_x003C_ActiveAfterEndDate_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_CreatedBy_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_CreatedDate_x003E_k__BackingField" type="xs:dateTime" />
      <xs:element name="_x003C_Description_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_EndDate_x003E_k__BackingField" nillable="true" type="xs:dateTime" />
      <xs:element name="_x003C_HasSections_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_Id_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_IsDeleted_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_IsPublished_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_IsSyncedFromClient_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_ModifiedBy_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_ModifiedDate_x003E_k__BackingField" type="xs:dateTime" />
      <xs:element name="_x003C_Name_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_NoOfQuestions_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_NoOfSections_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_PublishedDate_x003E_k__BackingField" type="xs:dateTime" />
      <xs:element name="_x003C_Questions_x003E_k__BackingField" nillable="true" type="tns:ArrayOfQuestion" />
      <xs:element name="_x003C_RecId_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_RecName_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_RevSyncSurveyId_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_Score_x003E_k__BackingField" type="xs:float" />
      <xs:element name="_x003C_Sections_x003E_k__BackingField" nillable="true" type="tns:ArrayOfSurveySection" />
      <xs:element name="_x003C_StartDate_x003E_k__BackingField" nillable="true" type="xs:dateTime" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Survey" nillable="true" type="tns:Survey" />
  <xs:complexType name="ArrayOfQuestion">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="Question" nillable="true" type="tns:Question" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfQuestion" nillable="true" type="tns:ArrayOfQuestion" />
  <xs:complexType name="Question">
    <xs:sequence>
      <xs:element name="_x003C_CreatedDate_x003E_k__BackingField" type="xs:dateTime" />
      <xs:element name="_x003C_Id_x003E_k__BackingField" type="xs:long" />
      <xs:element name="_x003C_IsDeleted_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_Options_x003E_k__BackingField" nillable="true" type="tns:ArrayOfOption" />
      <xs:element name="_x003C_Ordering_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_RevSyncQuestionId_x003E_k__BackingField" type="xs:long" />
      <xs:element name="_x003C_RevSyncSectionId_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_RevSyncSurveyId_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_Score_x003E_k__BackingField" type="xs:float" />
      <xs:element name="_x003C_SectionId_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_Statement_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_SurveyId_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_TypeId_x003E_k__BackingField" type="xs:short" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Question" nillable="true" type="tns:Question" />
  <xs:complexType name="ArrayOfOption">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="Option" nillable="true" type="tns:Option" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfOption" nillable="true" type="tns:ArrayOfOption" />
  <xs:complexType name="Option">
    <xs:sequence>
      <xs:element name="_x003C_CallEvaluationId_x003E_k__BackingField" type="xs:long" />
      <xs:element name="_x003C_CreatedDate_x003E_k__BackingField" type="xs:dateTime" />
      <xs:element name="_x003C_Id_x003E_k__BackingField" type="xs:long" />
      <xs:element name="_x003C_IsDeleted_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_Ordering_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_QuestionId_x003E_k__BackingField" type="xs:long" />
      <xs:element name="_x003C_RevSyncQuestionId_x003E_k__BackingField" type="xs:long" />
      <xs:element name="_x003C_Score_x003E_k__BackingField" type="xs:float" />
      <xs:element name="_x003C_Title_x003E_k__BackingField" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Option" nillable="true" type="tns:Option" />
  <xs:complexType name="ArrayOfSurveySection">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="SurveySection" nillable="true" type="tns:SurveySection" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfSurveySection" nillable="true" type="tns:ArrayOfSurveySection" />
  <xs:complexType name="SurveySection">
    <xs:sequence>
      <xs:element name="_x003C_CreatedDate_x003E_k__BackingField" type="xs:dateTime" />
      <xs:element name="_x003C_Id_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_IsDefaultSection_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_IsDeleted_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_NumberOfQuestion_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_RevSyncId_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_RevSyncSurveyId_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_SurveyId_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_TenantId_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_Title_x003E_k__BackingField" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SurveySection" nillable="true" type="tns:SurveySection" />
</xs:schema>