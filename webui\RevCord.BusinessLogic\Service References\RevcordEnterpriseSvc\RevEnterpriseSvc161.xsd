<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.EvaluationEntities" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.EvaluationEntities" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd3" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd5" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd9" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd16" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.SurveyEntities" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd4" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.DTO" />
  <xs:complexType name="CallEvaluation">
    <xs:sequence>
      <xs:element xmlns:q1="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" minOccurs="0" name="Agent" nillable="true" type="q1:User" />
      <xs:element minOccurs="0" name="Answers" nillable="true" type="tns:ArrayOfAnswer" />
      <xs:element minOccurs="0" name="AppUserId" type="xs:int" />
      <xs:element minOccurs="0" name="AssociatedAgent" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="AssociatedAgentCode" type="xs:int" />
      <xs:element minOccurs="0" name="AssociatedAgentEmail" nillable="true" type="xs:string" />
      <xs:element xmlns:q2="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="CallInfo" nillable="true" type="q2:CallInfo" />
      <xs:element xmlns:q3="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="CallSegments" nillable="true" type="q3:ArrayOfCallInfo" />
      <xs:element minOccurs="0" name="Code" type="xs:long" />
      <xs:element minOccurs="0" name="CompletedDate" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" name="CreatedBy" type="xs:int" />
      <xs:element minOccurs="0" name="CreatedDate" type="xs:dateTime" />
      <xs:element minOccurs="0" name="EvaluatedScore" type="xs:float" />
      <xs:element minOccurs="0" name="EvaluationType" type="xs:int" />
      <xs:element minOccurs="0" name="Id" type="xs:long" />
      <xs:element minOccurs="0" name="IsAgentAssociated" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsDeleted" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsSegmented" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsShared" type="xs:boolean" />
      <xs:element minOccurs="0" name="ModifiedBy" type="xs:int" />
      <xs:element minOccurs="0" name="ModifiedDate" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" name="MultiCallEvaluationId" type="xs:int" />
      <xs:element minOccurs="0" name="RecIP" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RecId" type="xs:int" />
      <xs:element minOccurs="0" name="RecName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RevSyncId" type="xs:long" />
      <xs:element xmlns:q4="http://schemas.datacontract.org/2004/07/RevCord.DataContracts" minOccurs="0" name="Status" type="q4:EvaluationStatus" />
      <xs:element minOccurs="0" name="StatusId" type="xs:short" />
      <xs:element xmlns:q5="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" minOccurs="0" name="Supervisor" nillable="true" type="q5:User" />
      <xs:element minOccurs="0" name="SupervisorComments" nillable="true" type="xs:string" />
      <xs:element xmlns:q6="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.SurveyEntities" minOccurs="0" name="Survey" nillable="true" type="q6:Survey" />
      <xs:element minOccurs="0" name="SurveyId" type="xs:int" />
      <xs:element minOccurs="0" name="UserId" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CallEvaluation" nillable="true" type="tns:CallEvaluation" />
  <xs:complexType name="ArrayOfAnswer">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="Answer" nillable="true" type="tns:Answer" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfAnswer" nillable="true" type="tns:ArrayOfAnswer" />
  <xs:complexType name="Answer">
    <xs:sequence>
      <xs:element minOccurs="0" name="AnswerValue" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CallEvaluationId" type="xs:long" />
      <xs:element xmlns:q7="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.SurveyEntities" minOccurs="0" name="Options" nillable="true" type="q7:ArrayOfOption" />
      <xs:element minOccurs="0" name="QuestionId" type="xs:long" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Answer" nillable="true" type="tns:Answer" />
  <xs:complexType name="ArrayOfUserEvaluation">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="UserEvaluation" nillable="true" type="tns:UserEvaluation" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfUserEvaluation" nillable="true" type="tns:ArrayOfUserEvaluation" />
  <xs:complexType name="UserEvaluation">
    <xs:sequence>
      <xs:element xmlns:q8="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" minOccurs="0" name="Agent" nillable="true" type="q8:User" />
      <xs:element minOccurs="0" name="Answers" nillable="true" type="tns:ArrayOfAnswer" />
      <xs:element minOccurs="0" name="AppUserId" type="xs:int" />
      <xs:element minOccurs="0" name="Code" type="xs:long" />
      <xs:element minOccurs="0" name="CompletedDate" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" name="CreatedBy" type="xs:int" />
      <xs:element minOccurs="0" name="CreatedDate" type="xs:dateTime" />
      <xs:element minOccurs="0" name="EvaluatedScore" type="xs:float" />
      <xs:element minOccurs="0" name="EvaluatorId" type="xs:int" />
      <xs:element minOccurs="0" name="Id" type="xs:long" />
      <xs:element minOccurs="0" name="IsDeleted" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsShared" type="xs:boolean" />
      <xs:element minOccurs="0" name="ModifiedBy" type="xs:int" />
      <xs:element minOccurs="0" name="ModifiedDate" nillable="true" type="xs:dateTime" />
      <xs:element xmlns:q9="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.DTO" minOccurs="0" name="QADTO" nillable="true" type="q9:QADTO" />
      <xs:element xmlns:q10="http://schemas.datacontract.org/2004/07/RevCord.DataContracts" minOccurs="0" name="Status" type="q10:EvaluationStatus" />
      <xs:element minOccurs="0" name="StatusId" type="xs:short" />
      <xs:element xmlns:q11="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" minOccurs="0" name="Supervisor" nillable="true" type="q11:User" />
      <xs:element minOccurs="0" name="SupervisorComments" nillable="true" type="xs:string" />
      <xs:element xmlns:q12="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.SurveyEntities" minOccurs="0" name="Survey" nillable="true" type="q12:Survey" />
      <xs:element minOccurs="0" name="SurveyId" nillable="true" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="UserEvaluation" nillable="true" type="tns:UserEvaluation" />
</xs:schema>