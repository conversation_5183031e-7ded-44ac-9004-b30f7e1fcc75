<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.IQ3" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.IQ3" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:complexType name="CustomField">
    <xs:sequence>
      <xs:element minOccurs="0" name="CreatedDate" type="xs:dateTime" />
      <xs:element minOccurs="0" name="Id" type="xs:int" />
      <xs:element minOccurs="0" name="IsDeleted" type="xs:boolean" />
      <xs:element minOccurs="0" name="Order" type="xs:int" />
      <xs:element minOccurs="0" name="Title" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="UserNum" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CustomField" nillable="true" type="tns:CustomField" />
  <xs:complexType name="ArrayOfCustomField">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="CustomField" nillable="true" type="tns:CustomField" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfCustomField" nillable="true" type="tns:ArrayOfCustomField" />
</xs:schema>