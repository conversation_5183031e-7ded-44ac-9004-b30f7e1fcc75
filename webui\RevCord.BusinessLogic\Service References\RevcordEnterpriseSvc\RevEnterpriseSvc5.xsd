<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/System" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/System" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:complexType name="ArrayOfTupleOfintint">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="TupleOfintint" nillable="true" type="tns:TupleOfintint" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfTupleOfintint" nillable="true" type="tns:ArrayOfTupleOfintint" />
  <xs:complexType name="TupleOfintint">
    <xs:annotation>
      <xs:appinfo>
        <GenericType Name="TupleOf{0}{1}{#}" Namespace="http://schemas.datacontract.org/2004/07/System" xmlns="http://schemas.microsoft.com/2003/10/Serialization/">
          <GenericParameter Name="int" Namespace="http://www.w3.org/2001/XMLSchema" />
          <GenericParameter Name="int" Namespace="http://www.w3.org/2001/XMLSchema" />
        </GenericType>
      </xs:appinfo>
    </xs:annotation>
    <xs:sequence>
      <xs:element name="m_Item1" type="xs:int" />
      <xs:element name="m_Item2" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="TupleOfintint" nillable="true" type="tns:TupleOfintint" />
  <xs:complexType name="DBNull">
    <xs:sequence />
  </xs:complexType>
  <xs:element name="DBNull" nillable="true" type="tns:DBNull" />
  <xs:complexType name="ArrayOfTupleOfintstringintstringstring">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="TupleOfintstringintstringstring" nillable="true" type="tns:TupleOfintstringintstringstring" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfTupleOfintstringintstringstring" nillable="true" type="tns:ArrayOfTupleOfintstringintstringstring" />
  <xs:complexType name="TupleOfintstringintstringstring">
    <xs:annotation>
      <xs:appinfo>
        <GenericType Name="TupleOf{0}{1}{2}{3}{4}{#}" Namespace="http://schemas.datacontract.org/2004/07/System" xmlns="http://schemas.microsoft.com/2003/10/Serialization/">
          <GenericParameter Name="int" Namespace="http://www.w3.org/2001/XMLSchema" />
          <GenericParameter Name="string" Namespace="http://www.w3.org/2001/XMLSchema" />
          <GenericParameter Name="int" Namespace="http://www.w3.org/2001/XMLSchema" />
          <GenericParameter Name="string" Namespace="http://www.w3.org/2001/XMLSchema" />
          <GenericParameter Name="string" Namespace="http://www.w3.org/2001/XMLSchema" />
        </GenericType>
      </xs:appinfo>
    </xs:annotation>
    <xs:sequence>
      <xs:element name="m_Item1" type="xs:int" />
      <xs:element name="m_Item2" nillable="true" type="xs:string" />
      <xs:element name="m_Item3" type="xs:int" />
      <xs:element name="m_Item4" nillable="true" type="xs:string" />
      <xs:element name="m_Item5" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="TupleOfintstringintstringstring" nillable="true" type="tns:TupleOfintstringintstringstring" />
  <xs:simpleType name="DayOfWeek">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Sunday" />
      <xs:enumeration value="Monday" />
      <xs:enumeration value="Tuesday" />
      <xs:enumeration value="Wednesday" />
      <xs:enumeration value="Thursday" />
      <xs:enumeration value="Friday" />
      <xs:enumeration value="Saturday" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="DayOfWeek" nillable="true" type="tns:DayOfWeek" />
</xs:schema>