<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd18" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.TenantEntities" />
  <xs:complexType name="ArrayOfCallInfo">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="CallInfo" nillable="true" type="tns:CallInfo" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfCallInfo" nillable="true" type="tns:ArrayOfCallInfo" />
  <xs:complexType name="CallInfo">
    <xs:sequence>
      <xs:element minOccurs="0" name="ANI" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ANIDetails" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ANIName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ANIPhone" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="AgentId" type="xs:int" />
      <xs:element minOccurs="0" name="BookmarkCSV" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BookmarkXML" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CallComments" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CallId" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CallType" type="xs:int" />
      <xs:element minOccurs="0" name="CallType_inq" type="xs:int" />
      <xs:element minOccurs="0" name="CalledID" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CallerID" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ChannelId" type="xs:int" />
      <xs:element minOccurs="0" name="ChannelName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Confidence" type="xs:decimal" />
      <xs:element minOccurs="0" name="CustName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DurationInMilliSeconds" type="xs:int" />
      <xs:element minOccurs="0" name="ErrorInMuxProcess" type="xs:int" />
      <xs:element minOccurs="0" name="FileName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GroupId" type="xs:int" />
      <xs:element minOccurs="0" name="GroupName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="IsPictureEvent" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsRevCell" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsRevView" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsSyncedFromClient" type="xs:int" />
      <xs:element minOccurs="0" name="Latitude" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Longitude" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MaxT1Ch" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MessageBody" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MinT1Ch" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RapidSOS" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RapidsosStatus" type="xs:int" />
      <xs:element minOccurs="0" name="RecorderIP" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RecorderId" type="xs:int" />
      <xs:element minOccurs="0" name="RecorderName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RetainValue" type="xs:int" />
      <xs:element minOccurs="0" name="RevViewAgentName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RevViewFileName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RevViewPhoneNumber" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RevViewStartTime" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RowNo" type="xs:long" />
      <xs:element minOccurs="0" name="SGroupID" type="xs:int" />
      <xs:element minOccurs="0" name="STTFileName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ScreenFileNames" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="StartTime" type="xs:dateTime" />
      <xs:element minOccurs="0" name="StartTimeString" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag1" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag10" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag11" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag12" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag13" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag14" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag15" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag16" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag2" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag3" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag4" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag5" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag6" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag7" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag8" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag9" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="TagRuleData" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Transcription" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="TranscriptionId" type="xs:int" />
      <xs:element minOccurs="0" name="UniqueId" type="xs:long" />
      <xs:element minOccurs="0" name="UserId" type="xs:int" />
      <xs:element minOccurs="0" name="VesselId" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="interview_DateTime" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="interview_GPS" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="interview_InterviewId" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="interview_Interviewee" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="interview_Interviewer" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="interview_MdInterviewee" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="interview_Notes" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CallInfo" nillable="true" type="tns:CallInfo" />
  <xs:complexType name="ArrayOfMediaInfo">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="MediaInfo" nillable="true" type="tns:MediaInfo" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfMediaInfo" nillable="true" type="tns:ArrayOfMediaInfo" />
  <xs:complexType name="MediaInfo">
    <xs:sequence>
      <xs:element minOccurs="0" name="ANI" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ANIDetails" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ANIName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ANINumber" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Bookmark" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BookmarkXML" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CallId" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CallTag" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CallType" type="xs:int" />
      <xs:element minOccurs="0" name="CalledId" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CallerId" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ChannelName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ChannelNo" type="xs:int" />
      <xs:element minOccurs="0" name="Comment" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CustInfo1" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CustInfo2" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CustInfo3" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CustInfo4" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CustInfo5" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CustName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Duration" type="xs:int" />
      <xs:element minOccurs="0" name="ECRecName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ErrorInMuxProcess" type="xs:int" />
      <xs:element minOccurs="0" name="EventName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="EventNameIQ3" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FileName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GroupName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Interview_DateTime" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Interview_GPS" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Interview_InterviewId" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Interview_Interviewee" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Interview_Interviewer" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Interview_MdInterviewee" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Interview_Notes" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="IsPictureEvent" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsRevCell" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsRevView" type="xs:boolean" />
      <xs:element minOccurs="0" name="RapidSOS" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RecorderIP" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RecorderId" type="xs:int" />
      <xs:element minOccurs="0" name="Report" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RetainCall" type="xs:boolean" />
      <xs:element minOccurs="0" name="RevViewAgentName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RevViewFileName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RevViewPhoneNumber" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RevViewStartTime" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RowNo" type="xs:long" />
      <xs:element minOccurs="0" name="ScreenFileNames" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="StartTime" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag10" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag11" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag12" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag13" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag14" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag15" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag16" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag5" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag6" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag7" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag8" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag9" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="TranscriptId" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Transcription" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="UniqueId" type="xs:long" />
      <xs:element minOccurs="0" name="UserId" type="xs:int" />
      <xs:element minOccurs="0" name="VesselId" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="MediaInfo" nillable="true" type="tns:MediaInfo" />
  <xs:complexType name="ArrayOfUserExtensionInfo">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="UserExtensionInfo" nillable="true" type="tns:UserExtensionInfo" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfUserExtensionInfo" nillable="true" type="tns:ArrayOfUserExtensionInfo" />
  <xs:complexType name="UserExtensionInfo">
    <xs:sequence>
      <xs:element minOccurs="0" name="ChannelType" type="xs:int" />
      <xs:element minOccurs="0" name="Ext" type="xs:int" />
      <xs:element minOccurs="0" name="ExtName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Id" type="xs:int" />
      <xs:element minOccurs="0" name="IsRevcell" type="xs:boolean" />
      <xs:element minOccurs="0" name="RecId" type="xs:int" />
      <xs:element minOccurs="0" name="UserID" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="UserName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="UserNum" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="UserExtensionInfo" nillable="true" type="tns:UserExtensionInfo" />
  <xs:complexType name="Recorder">
    <xs:sequence>
      <xs:element minOccurs="0" name="ConnectionString" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="IP" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Id" type="xs:int" />
      <xs:element minOccurs="0" name="IsPrimary" type="xs:boolean" />
      <xs:element minOccurs="0" name="Name" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Port" type="xs:int" />
      <xs:element minOccurs="0" name="SearchPageSize" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Recorder" nillable="true" type="tns:Recorder" />
  <xs:complexType name="ArrayOfRecorderAccessRight">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="RecorderAccessRight" nillable="true" type="tns:RecorderAccessRight" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfRecorderAccessRight" nillable="true" type="tns:ArrayOfRecorderAccessRight" />
  <xs:complexType name="RecorderAccessRight">
    <xs:sequence>
      <xs:element minOccurs="0" name="AccessRight" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RecId" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RecorderAccessRight" nillable="true" type="tns:RecorderAccessRight" />
  <xs:complexType name="ArrayOfRecorder">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="Recorder" nillable="true" type="tns:Recorder" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfRecorder" nillable="true" type="tns:ArrayOfRecorder" />
  <xs:complexType name="ArrayOfMonitorChannel">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="MonitorChannel" nillable="true" type="tns:MonitorChannel" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfMonitorChannel" nillable="true" type="tns:ArrayOfMonitorChannel" />
  <xs:complexType name="MonitorChannel">
    <xs:sequence>
      <xs:element minOccurs="0" name="AgentIP" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CallId" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Channeltype" type="xs:int" />
      <xs:element minOccurs="0" name="Duration" type="xs:int" />
      <xs:element minOccurs="0" name="EOD" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Ext" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ExtName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GroupName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GroupNum" type="xs:int" />
      <xs:element minOccurs="0" name="IsAvtecChannel" type="xs:int" />
      <xs:element minOccurs="0" name="IsRevStreamChannel" type="xs:int" />
      <xs:element minOccurs="0" name="IsRevView" type="xs:boolean" />
      <xs:element minOccurs="0" name="POD" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ROD" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RecIP" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RecId" type="xs:int" />
      <xs:element minOccurs="0" name="RecName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RecPort" type="xs:short" />
      <xs:element minOccurs="0" name="RevStreamID" type="xs:int" />
      <xs:element minOccurs="0" name="SOD" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Status" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="UserEmail" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="UserName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="UserNum" type="xs:int" />
      <xs:element minOccurs="0" name="UserPW" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="videourl" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="MonitorChannel" nillable="true" type="tns:MonitorChannel" />
  <xs:complexType name="Bookmark">
    <xs:sequence>
      <xs:element minOccurs="0" name="CallId" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ID" type="xs:long" />
      <xs:element minOccurs="0" name="Position" type="xs:long" />
      <xs:element minOccurs="0" name="Text" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Bookmark" nillable="true" type="tns:Bookmark" />
  <xs:complexType name="ArrayOfCallInfoExportResult">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="CallInfoExportResult" nillable="true" type="tns:CallInfoExportResult" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfCallInfoExportResult" nillable="true" type="tns:ArrayOfCallInfoExportResult" />
  <xs:complexType name="CallInfoExportResult">
    <xs:sequence>
      <xs:element minOccurs="0" name="ANIDetails" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ANIName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ANIPhone" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="AgentId" type="xs:int" />
      <xs:element minOccurs="0" name="BookmarkCSV" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CallComments" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CalledID" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ChannelName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DurationInMilliSeconds" type="xs:int" />
      <xs:element minOccurs="0" name="GroupName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RecorderName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RowNo" type="xs:long" />
      <xs:element minOccurs="0" name="StartTime" type="xs:dateTime" />
      <xs:element minOccurs="0" name="Tag1" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag10" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag11" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag12" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag13" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag14" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag15" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag16" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag2" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag3" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag4" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag5" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag6" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag7" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag8" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Tag9" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CallInfoExportResult" nillable="true" type="tns:CallInfoExportResult" />
  <xs:complexType name="CallInfoLite">
    <xs:sequence>
      <xs:element minOccurs="0" name="AgentId" type="xs:int" />
      <xs:element minOccurs="0" name="BookmarkXML" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CallComments" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CallId" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ChannelId" type="xs:int" />
      <xs:element minOccurs="0" name="ChannelName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DurationInMilliSeconds" type="xs:int" />
      <xs:element minOccurs="0" name="FileName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RecorderIP" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RecorderId" type="xs:int" />
      <xs:element minOccurs="0" name="RecorderName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RowNo" type="xs:long" />
      <xs:element minOccurs="0" name="StartTimeString" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="TagRule" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CallInfoLite" nillable="true" type="tns:CallInfoLite" />
  <xs:complexType name="ArrayOfChannel">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="Channel" nillable="true" type="tns:Channel" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfChannel" nillable="true" type="tns:ArrayOfChannel" />
  <xs:complexType name="Channel">
    <xs:sequence>
      <xs:element minOccurs="0" name="AudioOff" type="xs:int" />
      <xs:element minOccurs="0" name="AudioOffSub" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="AudioOn" type="xs:int" />
      <xs:element minOccurs="0" name="AudioOnSub" nillable="true" type="xs:string" />
      <xs:element xmlns:q1="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.TenantEntities" minOccurs="0" name="AvailableGateways" nillable="true" type="q1:ArrayOfTenantGateway" />
      <xs:element minOccurs="0" name="CallerIDMod" type="xs:int" />
      <xs:element minOccurs="0" name="ChNum" type="xs:int" />
      <xs:element minOccurs="0" name="Channeltype" type="xs:int" />
      <xs:element minOccurs="0" name="Create_T" type="xs:dateTime" />
      <xs:element minOccurs="0" name="Delete_T" type="xs:dateTime" />
      <xs:element minOccurs="0" name="Descr" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="EnableAGC" type="xs:int" />
      <xs:element minOccurs="0" name="EncType" type="xs:int" />
      <xs:element minOccurs="0" name="Ext" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ExtIP" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ExtName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FuncBtn" type="xs:int" />
      <xs:element minOccurs="0" name="FuncBtnSub" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Gain" type="xs:int" />
      <xs:element minOccurs="0" name="GatewayId" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="HangupDTRMVoltage" type="xs:int" />
      <xs:element minOccurs="0" name="Hold_Du" type="xs:int" />
      <xs:element minOccurs="0" name="IsRevCell" type="xs:boolean" />
      <xs:element minOccurs="0" name="LineHoldTime" type="xs:int" />
      <xs:element minOccurs="0" name="Max_Du" type="xs:int" />
      <xs:element minOccurs="0" name="Min_Du" type="xs:int" />
      <xs:element minOccurs="0" name="Modify_T" type="xs:dateTime" />
      <xs:element minOccurs="0" name="OffHook" type="xs:int" />
      <xs:element minOccurs="0" name="OffHookSub" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="OnHook" type="xs:int" />
      <xs:element minOccurs="0" name="OnHookSub" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RODMAC" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RecID" type="xs:int" />
      <xs:element minOccurs="0" name="RecName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ReleaseBtn" type="xs:int" />
      <xs:element minOccurs="0" name="ReleaseBtnSub" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Status" type="xs:int" />
      <xs:element minOccurs="0" name="Tf" type="xs:int" />
      <xs:element minOccurs="0" name="Tr" type="xs:int" />
      <xs:element minOccurs="0" name="Triger" type="xs:int" />
      <xs:element minOccurs="0" name="VoIP" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Channel" nillable="true" type="tns:Channel" />
</xs:schema>