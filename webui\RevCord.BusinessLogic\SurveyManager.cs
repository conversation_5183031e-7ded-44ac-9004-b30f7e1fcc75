﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.Response;
using RevCord.DataContracts.Request;
using RevCord.DataContracts.SurveyEntities;
using RevCord.DataAccess;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.Util;
using System.Data.SqlClient;
using System.Threading.Tasks;
using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using System.Web.Script.Serialization;

namespace RevCord.BusinessLogic
{
    public class SurveyManager
    {
        private readonly static string _serverIP = SiteConfig.HelperServiceAddress; //"127.0.0.1";

        #region Survey

        public SurveyResponse CreateSurvey(SurveyRequest surveyRequest)
        {
            try
            {
                var dal = new SurveyDAL(surveyRequest.TenantId);
                dal.CreateSurvey(surveyRequest.Survey);
                SurveyResponse sResponse = new SurveyResponse();
                sResponse.Survey = surveyRequest.Survey;
                sResponse.FlagStatus = (surveyRequest.Survey.Id != -1);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "CreateSurvey", surveyRequest.TenantId, " Survey has been created successfully.  " + new JavaScriptSerializer().Serialize(sResponse.Survey)));
                return sResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "CreateSurvey", surveyRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "CreateSurvey", surveyRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public SurveyResponse UpdateSurvey(SurveyRequest surveyRequest)
        {
            try
            {
                var dal = new SurveyDAL(surveyRequest.TenantId);
                dal.UpdateSurvey(surveyRequest.Survey);
                SurveyResponse response = new SurveyResponse();
                response.Survey = surveyRequest.Survey;
                response.FlagStatus = (surveyRequest.Survey.Id != -1);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "UpdateSurvey", surveyRequest.TenantId, " Survey has been updated successfully.  " + new JavaScriptSerializer().Serialize(response.Survey)));
                return response;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "UpdateSurvey", surveyRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "UpdateSurvey", surveyRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public SurveyResponse DeleteAndGetSurveys(int id, int tenantId, int revsyncSurveyId)
        {
            try
            {
                var surveyResponse = new SurveyResponse { Surveys = new SurveyDAL(tenantId).DeleteAndGetSurveys(id, revsyncSurveyId), FlagStatus = true };
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "DeleteAndGetSurveys", tenantId, " DeleteAndGetSurveys has been executed successfully.  " + new JavaScriptSerializer().Serialize(surveyResponse.Surveys)));
                return surveyResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "DeleteAndGetSurveys", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "DeleteAndGetSurveys", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public SurveyResponse PublishAndGetSurveys(int id, int tenantId, int revsyncSurveyId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "PublishAndGetSurveys", tenantId, " PublishAndGetSurveys has been called successfully.  "));
                return new SurveyResponse { Surveys = new SurveyDAL(tenantId).PublishAndGetSurveys(id, revsyncSurveyId), FlagStatus = true };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "PublishAndGetSurveys", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "PublishAndGetSurveys", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public List<Survey> GetSurveys(int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "GetSurveys", tenantId, " GetSurveys has been called successfully.  "));
                return new SurveyDAL(tenantId).GetSurveys();
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "GetSurveys", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "GetSurveys", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public List<Survey> GetSurveys(bool isPublished, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "GetSurveys", tenantId, " GetSurveys function has been called successfully.  "));
                return isPublished ? new SurveyDAL(tenantId).GetSurveys().Where(s => s.IsPublished == true).ToList() :
                new SurveyDAL(tenantId).GetSurveys().Where(s => s.IsPublished == false).ToList();
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "GetSurveys", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "GetSurveys", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public Survey GetSurvey(int id, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "GetSurvey", tenantId, " GetSurvey function has been called successfully.  Id = " + id));
                return new SurveyDAL(tenantId).GetSurveyWithDetails(id);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "GetSurvey", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "GetSurvey", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public Survey GetSurveyDetails(int id, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "GetSurveyDetails", tenantId, " GetSurveyDetails function has been called successfully.  Id = " + id));
                return new SurveyDAL(tenantId).GetSurveyWithDetails(id);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "GetSurveyDetails", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "GetSurveyDetails", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public bool IsSurveyExist(string surveyTitle, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "IsSurveyExist", tenantId, " IsSurveyExist function has been called successfully.  surveyTitle = " + surveyTitle));
                return new SurveyDAL(tenantId).IsSurveyExist(surveyTitle);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "IsSurveyExist", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "IsSurveyExist", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        #endregion

        #region Section

        public List<SurveySection> GetSectionsBySurveyId(int surveyId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "IsSurveyExist", tenantId, " GetSectionsBySurveyId function has been called successfully.  surveyId = " + surveyId));
                return new SurveyDAL(tenantId).GetSectionsBySurveyId(surveyId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "GetSectionsBySurveyId", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "GetSectionsBySurveyId", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public SurveyResponse CreateAndGetSections(SurveySection section, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "CreateAndGetSections", tenantId, " CreateAndGetSections function has been called successfully.  section.Title = " + section.Title));
                return new SurveyResponse { SurveySections = new SurveyDAL(tenantId).CreateAndGetSections(section) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "CreateAndGetSections", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "CreateAndGetSections", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public SurveyResponse UpateAndGetSections(SurveySection section, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "UpateAndGetSections", tenantId, " UpateAndGetSections function has been called successfully.  section.Id = " + section.Id + " - section.Title = " + section.Title));
                return new SurveyResponse { SurveySections = new SurveyDAL(tenantId).UpateAndGetSections(section), FlagStatus = true };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "UpateAndGetSections", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "UpateAndGetSections", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public SurveyResponse DeleteAndGetSections(int id, int surveyId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "DeleteAndGetSections", tenantId, " DeleteAndGetSections function has been called successfully.  surveyId = " + surveyId));
                return new SurveyResponse { SurveySections = new SurveyDAL(tenantId).DeleteAndGetSectionsBySurveyId(id, surveyId) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "DeleteAndGetSections", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "DeleteAndGetSections", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public SurveyResponse AssignUnAssignQuestionsAndGetSections(SurveyRequest sRequest)
        {
            try
            {
                var dal = new SurveyDAL(sRequest.TenantId);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "AssignUnAssignQuestionsAndGetSections", sRequest.TenantId, " AssignUnAssignQuestionsAndGetSections function has been called successfully."));
                return new SurveyResponse { SurveySections = dal.AssignUnAssignQuestionsAndGetSections(sRequest.SurveyId, sRequest.SectionId, sRequest.AssignedQuestions, sRequest.UnassignedQuestions) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "AssignUnAssignQuestionsAndGetSections", sRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "AssignUnAssignQuestionsAndGetSections", sRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        #endregion

        #region Question

        public SurveyResponse AssignUnAssignQuestionsToSection(SurveyRequest sRequest)
        {
            try
            {
                var dal = new SurveyDAL(sRequest.TenantId);
                long indicator = dal.AssignUnAssignQuestionsToSection(sRequest.SurveyId, sRequest.SectionId, sRequest.AssignedQuestions, sRequest.UnassignedQuestions);
                SurveyResponse response = new SurveyResponse();
                response.FlagStatus = (indicator != -1);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "AssignUnAssignQuestionsToSection", sRequest.TenantId, "AssignUnAssignQuestionsToSection function has been called successfully. sRequest.SurveyId = " + sRequest.SurveyId));
                return response;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "AssignUnAssignQuestionsToSection", sRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "AssignUnAssignQuestionsToSection", sRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public List<Question> GetQuestionsBySurveyId(int surveyId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "GetQuestionsBySurveyId", tenantId, "GetQuestionsBySurveyId function has been called successfully. surveyId = " + surveyId));
                return new SurveyDAL(tenantId).GetQuestionsBySurveyId(surveyId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "GetQuestionsBySurveyId", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "GetQuestionsBySurveyId", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public List<Option> GetQuestionOptionsByQID(long surveyId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "GetQuestionOptionsByQID", tenantId, "GetQuestionOptionsByQID function has been called successfully. surveyId = " + surveyId));
                return new SurveyDAL(tenantId).GetQuestionOptionsByQID(surveyId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "GetQuestionOptionsByQID", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "GetQuestionOptionsByQID", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }

        }
        public SurveyResponse CreateQuestion(SurveyRequest surveyRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "CreateQuestion", surveyRequest.TenantId, "CreateQuestion function has been called successfully. Statement = " + surveyRequest.Question.Statement));
                var dal = new SurveyDAL(surveyRequest.TenantId);
                surveyRequest.Question.Id = dal.CreateQuestion(surveyRequest.Question);

                SurveyResponse response = new SurveyResponse();
                response.Question = surveyRequest.Question;
                response.FlagStatus = (surveyRequest.Question.Id != -1);
                return response;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "CreateQuestion", surveyRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "CreateQuestion", surveyRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public SurveyResponse UpdateQuestion(SurveyRequest surveyRequest)
        {
            try
            {
                var dal = new SurveyDAL(surveyRequest.TenantId);
                dal.UpdateQuestion(surveyRequest.Question);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "UpdateQuestion", surveyRequest.TenantId, "UpdateQuestion function has been called successfully. Id = " + surveyRequest.Question.Id));
                SurveyResponse response = new SurveyResponse();
                response.Question = surveyRequest.Question;
                response.FlagStatus = (surveyRequest.Question.Id != -1);
                return response;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "UpdateQuestion", surveyRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "UpdateQuestion", surveyRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public bool UpdateQuestionsOrder(SurveyRequest surveyRequest)
        {
            try
            {
                var dal = new SurveyDAL(surveyRequest.TenantId);
                long indicator = dal.UpdateQuestionsOrder(surveyRequest.Questions);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "UpdateQuestionsOrder", surveyRequest.TenantId, "UpdateQuestionsOrder function has been called successfully. "));
                SurveyResponse response = new SurveyResponse();
                response.FlagStatus = (indicator != -1);
                return response.FlagStatus;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "UpdateQuestionsOrder", surveyRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "UpdateQuestionsOrder", surveyRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public bool DeleteQuestion(long id, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "DeleteQuestion", tenantId, "DeleteQuestion function has been called successfully. Id = " + id));
                new SurveyDAL(tenantId).DeleteQuestion(id);
                return true;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "DeleteQuestion", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "DeleteQuestion", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public bool UpdateQuestionSection(SurveyRequest surveyRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "UpdateQuestionSection", surveyRequest.TenantId, "UpdateQuestionSection function has been called successfully."));
                return new SurveyDAL(surveyRequest.TenantId).UpdateQuestionSection(Convert.ToInt32(surveyRequest.Question.Id), surveyRequest.SectionId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "UpdateQuestionSection", surveyRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "UpdateQuestionSection", surveyRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public void UpdateIsPublished(int surveyId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "UpdateIsPublished", tenantId, "UpdateIsPublished function has been called successfully."));
                new SurveyDAL(tenantId).UpdateIsPublished(surveyId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "UpdateIsPublished", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "UpdateIsPublished", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        #endregion

        #region EC
        public List<Survey> GetSurveysFromRecorders(List<Recorder> recorders)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "GetSurveysFromRecorders", 0, "GetSurveysFromRecorders function has been called successfully."));

                List<Survey> surveys = new List<Survey>();
                foreach (var recorder in recorders)
                {
                    bool bExceptionThrown = false;
                    try
                    {
                        if (recorder.IsPrimary)
                            surveys.AddRange(new SurveyDALEC().GetSurveysFromRecorder(recorder));
                        else
                        {
                            // Alternative approach
                            RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                            entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                            surveys.AddRange(entClient.GetSurveysFromRecorder(recorder));
                        }
                    }
                    catch (Exception ex)
                    {
                        bExceptionThrown = true;
                        var errorMsg = "An error has occurred while calling the function GetSurveysFromRecorders. Recorder Id = " + recorder.Id + ", Recorder Name = " + recorder.Name;
                        Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "GetSurveysFromRecorders", 0, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                    }
                    if (bExceptionThrown)
                        continue;
                }
                return surveys;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "GetSurveysFromRecorders", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "GetSurveysFromRecorders", 0, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            
        }
        public List<Survey> GetPublishedSurveysFromRecorder(Recorder recorder)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "GetPublishedSurveysFromRecorder", 0, "GetPublishedSurveysFromRecorder function has been called successfully."));
                if (recorder.IsPrimary)
                    return new SurveyDALEC().GetPublishedSurveysFromRecorder(recorder);
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    return entClient.GetPublishedSurveysFromRecorder(recorder).ToList(); ;
                }
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "GetPublishedSurveysFromRecorder", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "GetPublishedSurveysFromRecorder", 0, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public SurveyResponse DeleteAndGetSurveysFromRecorders(int surveyId, int actionToPerformOnRecorder, List<Recorder> recorders)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "DeleteAndGetSurveysFromRecorders", 0, "DeleteAndGetSurveysFromRecorders function has been called successfully. surveyId = " + surveyId));
                List<Survey> surveys = new List<Survey>();
                foreach (var recorder in recorders)
                {
                    bool bExceptionThrown = false;
                    try
                    {
                        if (recorder.Id == actionToPerformOnRecorder)
                        {
                            if (recorder.IsPrimary)
                                surveys.AddRange(new SurveyDALEC().DeleteAndGetSurveysFromRecorder(surveyId, recorder));
                            else
                            {
                                // Alternative approach
                                RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                                entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                                surveys.AddRange(entClient.DeleteAndGetSurveysFromRecorder(surveyId, recorder));
                            }
                        }
                        else
                        {
                            if (recorder.IsPrimary)
                                surveys.AddRange(new SurveyDALEC().GetSurveysFromRecorder(recorder));
                            else
                            {
                                // Alternative approach
                                RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                                entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                                surveys.AddRange(entClient.GetSurveysFromRecorder(recorder));
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        bExceptionThrown = true;
                        var errorMsg = "An error has occurred while calling the function DeleteAndGetSurveysFromRecorders. Survey Id = " + surveyId + ", Recorder Id = " + recorder.Id + ", Recorder Name = " + recorder.Name;
                        Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "DeleteAndGetSurveysFromRecorders", 0, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                    }
                    if (bExceptionThrown)
                        continue;
                }
                return new SurveyResponse { Surveys = surveys, FlagStatus = true };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "DeleteAndGetSurveysFromRecorders", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "DeleteAndGetSurveysFromRecorders", 0, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public SurveyResponse PublishAndGetSurveysFromRecorders(int surveyId, int actionToPerformOnRecorder, List<Recorder> recorders)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "PublishAndGetSurveysFromRecorders", 0, "PublishAndGetSurveysFromRecorders function has been called successfully. surveyId = " + surveyId));

                List<Survey> surveys = new List<Survey>();
                foreach (var recorder in recorders)
                {
                    bool bExceptionThrown = false;
                    try
                    {
                        if (recorder.Id == actionToPerformOnRecorder)
                        {
                            if (recorder.IsPrimary)
                                surveys.AddRange(new SurveyDALEC().PublishAndGetSurveysFromRecorders(surveyId, recorder));
                            else
                            {
                                // Alternative approach
                                RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                                entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                                surveys.AddRange(entClient.PublishAndGetSurveysFromRecorders(surveyId, recorder));
                            }
                        }
                        else
                        {
                            if (recorder.IsPrimary)
                                surveys.AddRange(new SurveyDALEC().GetSurveysFromRecorder(recorder));
                            else
                            {
                                // Alternative approach
                                RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                                entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                                surveys.AddRange(entClient.GetSurveysFromRecorder(recorder));
                            }
                        }

                    }
                    catch (Exception ex)
                    {
                        bExceptionThrown = true;
                        var errorMsg = "An error has occurred while calling the function PublishAndGetSurveysFromRecorders. Survey Id = " + surveyId + ", Recorder Id = " + recorder.Id + ", Recorder Name = " + recorder.Name;
                        Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "PublishAndGetSurveysFromRecorders", 0, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                    }
                    if (bExceptionThrown)
                        continue;
                }
                return new SurveyResponse { Surveys = surveys, FlagStatus = true };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "PublishAndGetSurveysFromRecorders", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "PublishAndGetSurveysFromRecorders", 0, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            
        }
        public Survey GetSurveyDetailsFromRecorder(int id, Recorder recorder)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "GetSurveyDetailsFromRecorder", 0, "GetSurveyDetailsFromRecorder function has been called successfully. Id = " + id));
                Survey survey = null;
                try
                {
                    if (recorder.IsPrimary)
                        survey = new SurveyDALEC().GetSurveyDetailsFromRecorder(id, recorder);
                    else
                    {
                        // Alternative approach
                        RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                        entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                        survey = entClient.GetSurveyDetailsFromRecorder(id, recorder);
                    }
                }
                catch (Exception ex)
                {
                    var errorMsg = "An error has occurred while calling the function GetSurveyDetailsFromRecorder. Survey Id = " + id + ", Recorder Id = " + recorder.Id + ", Recorder Name = " + recorder.Name;
                    Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "GetSurveyDetailsFromRecorder", 0, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                }
                return survey;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "GetSurveyDetailsFromRecorder", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "GetSurveyDetailsFromRecorder", 0, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public bool IsSurveyExistOnRecorder(string surveyTitle, Recorder recorder)
        {
            bool isSurveyExist = false;
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "IsSurveyExistOnRecorder", 0, "IsSurveyExistOnRecorder function has been called successfully. surveyTitle = " + surveyTitle));
                if (recorder.IsPrimary)
                    isSurveyExist = new SurveyDALEC().IsSurveyExistOnRecorder(surveyTitle, recorder);
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    isSurveyExist = entClient.IsSurveyExistOnRecorder(surveyTitle, recorder);
                }
                var msg = "Survey with title = " + surveyTitle + (isSurveyExist == true ? " already exists" : " doesn't exits") + " on recorder. Recorder Id = " + recorder.Id + ", Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "IsSurveyExistOnRecorder", 0, msg));
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "IsSurveyExistOnRecorder", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while calling the function IsSurveyExistOnRecorder. Survey Title = " + surveyTitle + ", Recorder Id = " + recorder.Id + ", Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "GetSurveyDetailsFromRecorder", 0, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return isSurveyExist;
        }
        public SurveyResponse CreateSurveyOnRecorder(SurveyRequest surveyRequest, Recorder recorder)
        {
            SurveyResponse sResponse = new SurveyResponse();
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "CreateSurveyOnRecorder", surveyRequest.TenantId, "CreateSurveyOnRecorder function has been called successfully."));
                if (recorder.IsPrimary)
                {
                    var surveyDALEC = new SurveyDALEC();
                    surveyDALEC.CreateSurveyOnRecorder(surveyRequest.Survey, recorder, surveyRequest.TenantId);
                    sResponse.Survey = surveyRequest.Survey;
                    sResponse.FlagStatus = (surveyRequest.Survey.Id != -1);
                }
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    sResponse.Survey = entClient.CreateSurveyOnRecorder(surveyRequest.Survey, recorder);
                    sResponse.FlagStatus = (surveyRequest.Survey.Id != -1);
                }
                var msg = "Survey has been created successfully on recorder. Survey Name = " + sResponse.Survey.Name + " , Recorder Id = " + recorder.Id + ", Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "CreateSurveyOnRecorder", 0, msg));
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "CreateSurveyOnRecorder", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while calling the function CreateSurveyOnRecorder. Survey Title = " + surveyRequest.Survey.Name + " , Recorder Id = " + recorder.Id + ", Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "CreateSurveyOnRecorder", 0, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return sResponse;
        }
        public SurveyResponse CreateQuestionOnRecorder(SurveyRequest surveyRequest, Recorder recorder)
        {
            SurveyResponse response = new SurveyResponse();
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "CreateQuestionOnRecorder", surveyRequest.TenantId, "CreateQuestionOnRecorder function has been called successfully."));
                var surveyDALEC = new SurveyDALEC();
                if (recorder.IsPrimary)
                {
                    surveyRequest.Question.Id = surveyDALEC.CreateQuestionOnRecorder(surveyRequest.Question, recorder, surveyRequest.TenantId);
                    response.Question = surveyRequest.Question;
                    response.FlagStatus = (surveyRequest.Question.Id != -1);
                }
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    surveyRequest.Question.Id = entClient.CreateQuestionOnRecorder(surveyRequest.Question, recorder);
                    response.Question = surveyRequest.Question;
                    response.FlagStatus = (surveyRequest.Question.Id != -1);
                }
                var msg = "Question created successfully on recorder. Question Id = " + response.Question.Id + " , Recorder Id = " + recorder.Id + ", Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "CreateQuestionOnRecorder", 0, msg));
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "CreateQuestionOnRecorder", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while calling the function CreateQuestionOnRecorder. Question Statement = " + surveyRequest.Question.Statement + " , Recorder Id = " + recorder.Id + ", Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "CreateQuestionOnRecorder", 0, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return response;
        }
        public SurveyResponse CreateAndGetSectionsOnRecorder(SurveySection section, Recorder recorder)
        {
            SurveyResponse surveyResponse = null;
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "CreateAndGetSectionsOnRecorder", 0, "CreateAndGetSectionsOnRecorder function has been called successfully."));
                if (recorder.IsPrimary)
                    surveyResponse = new SurveyResponse { SurveySections = new SurveyDALEC().CreateAndGetSectionsOnRecorder(section, recorder, section.TenantId), FlagStatus = true };
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    surveyResponse = new SurveyResponse { SurveySections = entClient.CreateAndGetSectionsOnRecorder(section, recorder).ToList(), FlagStatus = true };
                }
                var msg = "Section created successfully on recorder. Section Title = " + section.Title + ", Recorder Id = " + recorder.Id + ", Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "CreateAndGetSectionsOnRecorder", 0, msg));
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "CreateAndGetSectionsOnRecorder", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while calling the function CreateAndGetSectionsOnRecorder. Section Title = " + section.Title + " , Recorder Id = " + recorder.Id + ", Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "CreateAndGetSectionsOnRecorder", 0, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return surveyResponse;
        }
        public List<Question> GetQuestionsBySurveyIdFromRecorder(int surveyId, Recorder recorder)
        {
            List<Question> questions = null;
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "GetQuestionsBySurveyIdFromRecorder", 0, "GetQuestionsBySurveyIdFromRecorder function has been called successfully. surveyId = " + surveyId));
                if (recorder.IsPrimary)
                    questions = new SurveyDALEC().GetQuestionsBySurveyIdFromRecorder(surveyId, recorder);
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    questions = entClient.GetQuestionsBySurveyIdFromRecorder(surveyId, recorder).ToList();
                }
                var msg =  "Questions have been fetched successfully by survey id from recorder. Survey Id = " + surveyId + " , Recorder Id = " + recorder.Id + " , Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "GetQuestionsBySurveyIdFromRecorder", 0, msg));
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "GetQuestionsBySurveyIdFromRecorder", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while calling the function GetQuestionsBySurveyIdFromRecorder. Survey Id = " + surveyId + " , Recorder Id = " + recorder.Id + ", Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "GetQuestionsBySurveyIdFromRecorder", 0, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return questions;
        }
        public List<SurveySection> GetSectionsBySurveyIdFromRecorder(int surveyId, Recorder recorder)
        {
            List<SurveySection> surveySections = null;
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "GetSectionsBySurveyIdFromRecorder", 0, "GetSectionsBySurveyIdFromRecorder function has been called successfully. surveyId = " + surveyId));
                if (recorder.IsPrimary)
                    surveySections = new SurveyDALEC().GetSectionsBySurveyIdFromRecorder(surveyId, recorder);
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    surveySections = entClient.GetSectionsBySurveyIdFromRecorder(surveyId, recorder).ToList();
                }
                var msg =  "Survey sections have been fetched successfully by survey id from recorder. Survey Id = " + surveyId + " , Recorder Id = " + recorder.Id + " , Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "GetSectionsBySurveyIdFromRecorder", 0, msg));
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "GetQuestionsBySurveyIdFromRecorder", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while calling the function GetSectionsBySurveyIdFromRecorder. Survey Id = " + surveyId + " , Recorder Id = " + recorder.Id + ", Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "GetQuestionsBySurveyIdFromRecorder", 0, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return surveySections;
        }
        public bool UpdateQuestionSectionOnRecorder(SurveyRequest surveyRequest, Recorder recorder)
        {
            bool isQuestionSectionUpdated = false;
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "UpdateQuestionSectionOnRecorder", 0, "UpdateQuestionSectionOnRecorder function has been called successfully." ));
                if (recorder.IsPrimary)
                    isQuestionSectionUpdated = new SurveyDALEC().UpdateQuestionSectionOnRecorder(Convert.ToInt32(surveyRequest.Question.Id), surveyRequest.SectionId, recorder);
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    isQuestionSectionUpdated = entClient.UpdateQuestionSectionOnRecorder(Convert.ToInt32(surveyRequest.Question.Id), surveyRequest.SectionId, recorder);
                }
                var msg = "Question section has been updated successfully. Recorder Id = " + recorder.Id + " , Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "UpdateQuestionSectionOnRecorder", 0, msg));
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "UpdateQuestionSectionOnRecorder", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while calling the function UpdateQuestionSectionOnRecorder. Question Id = " + surveyRequest.Question.Id + " , Section Id = " + surveyRequest.SectionId + " , Recorder Id = " + recorder.Id + ", Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "UpdateQuestionSectionOnRecorder", 0, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return isQuestionSectionUpdated;
        }
        public SurveyResponse UpdateAndGetSectionsFromRecorder(SurveySection section, Recorder recorder)
        {
            SurveyResponse surveyResponse = null;
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "UpdateAndGetSectionsFromRecorder", 0, "UpdateAndGetSectionsFromRecorder function has been called successfully."));
                if (recorder.IsPrimary)
                    surveyResponse = new SurveyResponse { SurveySections = new SurveyDALEC().UpdateAndGetSectionsFromRecorder(section, recorder), FlagStatus = true };
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    surveyResponse = new SurveyResponse { SurveySections = entClient.UpdateAndGetSectionsFromRecorder(section, recorder).ToList(), FlagStatus = true };
                }
                var msg = "UpdateAndGetSectionsFromRecorder executed successfully. Recorder Id = " + recorder.Id + " , Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "UpdateAndGetSectionsFromRecorder", 0, msg));

            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "UpdateAndGetSectionsFromRecorder", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while calling the function UpdateAndGetSectionsFromRecorder.  Section Id = " + section.Title + " , Recorder Id = " + recorder.Id + ", Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "UpdateAndGetSectionsFromRecorder", 0, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return surveyResponse;
        }
        public SurveyResponse DeleteAndGetSectionsFromRecorder(int id, int surveyId, Recorder recorder)
        {
            SurveyResponse surveyResponse = null;
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "DeleteAndGetSectionsFromRecorder", 0, "DeleteAndGetSectionsFromRecorder function has been called successfully. id = " + id + " - surveyId = " + surveyId));
                if (recorder.IsPrimary)
                    surveyResponse = new SurveyResponse { SurveySections = new SurveyDALEC().DeleteAndGetSectionsFromRecorder(id, surveyId, recorder) };
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    surveyResponse = new SurveyResponse { SurveySections = entClient.DeleteAndGetSectionsFromRecorder(id, surveyId, recorder).ToList() };
                }
                var msg = "DeleteAndGetSectionsFromRecorder executed successfully. Survey Id = " + surveyId + " , Recorder Id = " + recorder.Id + " , Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "DeleteAndGetSectionsFromRecorder", 0, msg));
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "DeleteAndGetSectionsFromRecorder", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while calling the function DeleteAndGetSectionsFromRecorder.  Section Id = " + id + " , Survey Id = " + surveyId + " , Recorder Id = " + recorder.Id + ", Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "DeleteAndGetSectionsFromRecorder", 0, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return surveyResponse;
        }
        public SurveyResponse AssignUnAssignQuestionsAndGetSectionsFromRecorder(SurveyRequest sRequest, Recorder recorder)
        {
            SurveyResponse surveyResponse = null;
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "AssignUnAssignQuestionsAndGetSectionsFromRecorder", 0, "AssignUnAssignQuestionsAndGetSectionsFromRecorder function has been called successfully."));
                if (recorder.IsPrimary)
                    surveyResponse = new SurveyResponse { SurveySections = new SurveyDALEC().AssignUnAssignQuestionsAndGetSectionsFromRecorder(sRequest.SurveyId, sRequest.SectionId, sRequest.AssignedQuestions, sRequest.UnassignedQuestions, recorder) };
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    surveyResponse = new SurveyResponse { SurveySections = entClient.AssignUnAssignQuestionsAndGetSectionsFromRecorder(sRequest.SurveyId, sRequest.SectionId, sRequest.AssignedQuestions, sRequest.UnassignedQuestions, recorder).ToList() };
                }
                 var msg =  "AssignUnAssignQuestionsAndGetSectionsFromRecorder executed successfully. Section Id = " + sRequest.SectionId + " , Survey Id = " + sRequest.SurveyId + " , Recorder Id = " + recorder.Id + " , Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "AssignUnAssignQuestionsAndGetSectionsFromRecorder", 0, msg));
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "AssignUnAssignQuestionsAndGetSectionsFromRecorder", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while calling the function AssignUnAssignQuestionsAndGetSectionsFromRecorder.  Section Id = " + sRequest.SectionId + " , Survey Id = " + sRequest.SurveyId + " , Recorder Id = " + recorder.Id + ", Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "AssignUnAssignQuestionsAndGetSectionsFromRecorder", 0, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return surveyResponse;
        }
        public Survey GetSurveyFromRecorder(int surveyId, Recorder recorder)
        {
            Survey survey = null;
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "GetSurveyFromRecorder", 0, "GetSurveyFromRecorder function has been called successfully. surveyId = " + surveyId));
                if (recorder.IsPrimary)
                    survey = new SurveyDALEC().GetSurveyFromRecorder(surveyId, recorder);
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    survey = entClient.GetSurveyFromRecorder(surveyId, recorder);
                }
                var msg = "Survey has been fetched from recorder. Survey Id = " + surveyId + " , Recorder Id = " + recorder.Id + " , Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "GetSurveyFromRecorder", 0, msg));
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "GetSurveyFromRecorder", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while calling the function GetSurveyFromRecorder. Survey Id = " + surveyId + " , Recorder Id = " + recorder.Id + " , Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "GetSurveyFromRecorder", 0, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return survey;
        }
        public SurveyResponse UpdateSurveyOnRecorder(SurveyRequest surveyRequest, Recorder recorder)
        {
            SurveyResponse response = null;
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "UpdateSurveyOnRecorder", 0, "UpdateSurveyOnRecorder function has been called successfully. recorder.Id = " + recorder.Id));
                var surveyDALEC = new SurveyDALEC();
                if (recorder.IsPrimary)
                    surveyDALEC.UpdateSurveyOnRecorder(surveyRequest.Survey, recorder, surveyRequest.TenantId);
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    entClient.UpdateSurveyOnRecorder(surveyRequest.Survey, recorder);
                }
                response = new SurveyResponse();
                response.Survey = surveyRequest.Survey;
                response.FlagStatus = (surveyRequest.Survey.Id != -1);
                var msg = "Survey has been updated on recorder. Survey Id = " + surveyRequest.Survey.Id + " , Recorder Id = " + recorder.Id + " , Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "UpdateSurveyOnRecorder", 0, msg));
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "UpdateSurveyOnRecorder", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while calling the function UpdateSurveyOnRecorder. Survey Id = " + surveyRequest.Survey.Id + " , Recorder Id = " + recorder.Id + " , Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "UpdateSurveyOnRecorder", 0, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return response;
        }
        public SurveyResponse UpdateQuestionOnRecorder(SurveyRequest surveyRequest, Recorder recorder)
        {
            SurveyResponse response = null;
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "UpdateQuestionOnRecorder", 0, "UpdateQuestionOnRecorder function has been called successfully. recorder.Id = " + recorder.Id));
                var surveyDALEC = new SurveyDALEC();
                if (recorder.IsPrimary)
                    surveyDALEC.UpdateQuestionOnRecorder(surveyRequest.Question, recorder, surveyRequest.TenantId);
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    entClient.UpdateQuestionOnRecorder(surveyRequest.Question, recorder);
                }
                response = new SurveyResponse();
                response.Question = surveyRequest.Question;
                response.FlagStatus = (surveyRequest.Question.Id != -1);
                var msg = "Question has been updated on recorder. Question Id = " + surveyRequest.Question.Id + " , Recorder Id = " + recorder.Id + " , Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "UpdateQuestionOnRecorder", 0, msg));
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "UpdateQuestionOnRecorder", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while calling the function UpdateQuestionOnRecorder. Survey Id = " + surveyRequest.Survey.Id + " , Recorder Id = " + recorder.Id + " , Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "UpdateQuestionOnRecorder", 0, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return response;
        }
        public bool DeleteQuestionFromRecorder(long id, Recorder recorder)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "DeleteQuestionFromRecorder", 0, "DeleteQuestionFromRecorder function has been called successfully. id = " + id + " recorder.Id = " + recorder.Id));
                if (recorder.IsPrimary)
                    new SurveyDALEC().DeleteQuestionFromRecorder(id, recorder);
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    entClient.DeleteQuestionFromRecorder(id, recorder);
                }
                var msg = "Question has been deleted from recorder. Question Id = " + id + " , Recorder Id = " + recorder.Id + " , Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "DeleteQuestionFromRecorder", 0, msg));
                return true;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "DeleteQuestionFromRecorder", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while calling the function DeleteQuestionFromRecorder. Survey Id = " + id + " , Recorder Id = " + recorder.Id + " , Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "DeleteQuestionFromRecorder", 0, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public void UpdateIsPublishedOnRecorder(int surveyId, Recorder recorder)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "UpdateIsPublishedOnRecorder", 0, "UpdateIsPublishedOnRecorder function has been called successfully. surveyId = " + surveyId + " recorder.Id = " + recorder.Id));
                if (recorder.IsPrimary)
                    new SurveyDALEC().UpdateIsPublishedOnRecorder(surveyId, recorder);
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    entClient.UpdateIsPublishedOnRecorder(surveyId, recorder);
                }
                var msg =  "IsPublished status has been updated successfully. Survey Id = " + surveyId + " , Recorder Id = " + recorder.Id + " , Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "UpdateIsPublishedOnRecorder", 0, msg));
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "UpdateIsPublishedOnRecorder", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while calling the function UpdateIsPublishedOnRecorder. Survey Id = " + surveyId + " , Recorder Id = " + recorder.Id + " , Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "UpdateIsPublishedOnRecorder", 0, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public bool UpdateQuestionsOrderOnRecorder(SurveyRequest surveyRequest, Recorder recorder)
        {
            SurveyResponse response = null;
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "UpdateQuestionsOrderOnRecorder", 0, "UpdateQuestionsOrderOnRecorder function has been called successfully. recorder.Id = " + recorder.Id));
                var surveyDALEC = new SurveyDALEC();
                long indicator = 0;
                if (recorder.IsPrimary)
                    indicator = surveyDALEC.UpdateQuestionsOrderOnRecorder(surveyRequest.Questions, recorder);
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    indicator = entClient.UpdateQuestionsOrderOnRecorder(surveyRequest.Questions.ToArray(), recorder);
                }
                response = new SurveyResponse();
                response.FlagStatus = (indicator != -1);
                var msg = "Question order has been updated successfully. Recorder Id = " + recorder.Id + " , Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Survey, "UpdateQuestionsOrderOnRecorder", 0, msg));
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Survey, "UpdateQuestionsOrderOnRecorder", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while calling the function UpdateQuestionsOrderOnRecorder. Recorder Id = " + recorder.Id + " , Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteException(Originator.Survey, "UpdateQuestionsOrderOnRecorder", 0, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return response.FlagStatus;
        }
        #endregion
    }

}
