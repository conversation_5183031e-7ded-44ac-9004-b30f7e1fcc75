﻿using System.Text;
using RevCord.DataAccess;
using RevCord.DataContracts.Messages;
using RevCord.DataContracts.MessageBase;
using RevCord.Util;
using System;
using RevCord.DataContracts.TenantEntities;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Threading.Tasks;
using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.ViewModelEntities;
using RevCord.DataContracts.VoiceRecEntities;

namespace RevCord.BusinessLogic
{
    public class TenantManager
    {
        public TenantResponse GetColumnModel(int tenantId, int userNum, bool isOnlyIQ3ModeEnabled)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Tenant, "GetColumnModel", tenantId, "GetColumnModel function has been called. tenantId = " + tenantId));
                return new TenantResponse
                {
                    ColumnsModel = new TenantDAL(tenantId).GetColumnsModel(userNum, isOnlyIQ3ModeEnabled)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Tenant, "GetColumnModel", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Tenant, "GetColumnModel", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public TenantResponse GetColumnModelByWhereClause(TenantRequest request)
        {
            try
            {
                StringBuilder sbWhereClause = new StringBuilder();
                sbWhereClause.Append(" 1 = 1 ");
                sbWhereClause.AppendFormat(" AND ( UserNum = {0} )", request.UserNum);
                sbWhereClause.AppendLine();

                var criteria = request.TenantIdCriteria;
                #region ------- Search Criteria -------

                if (criteria != null)
                {
                    sbWhereClause.Append(" AND (Module IN ( ");
                    foreach (var moduleId in criteria)
                    {
                        sbWhereClause.AppendFormat("{0},", moduleId);
                    }
                    sbWhereClause.RemoveLast(",");
                    sbWhereClause.Append(" ) )");
                    sbWhereClause.AppendLine();
                }
                #endregion

                System.Diagnostics.Debug.WriteLine(sbWhereClause.ToString());

                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Tenant, "GetColumnModelByWhereClause", request.TenantId, "GetColumnModelByWhereClause function has been called. sbWhereClause = " + sbWhereClause.ToString()));

                return new TenantResponse
                {
                    ColumnsModel = new TenantDAL(request.TenantId).GetColumnsModel(sbWhereClause.ToString(), request.UserNum, request.IsOnlyIQ3ModeEnabled)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.SearchMedia, "GetColumnModelByWhereClause", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.SearchMedia, "GetColumnModelByWhereClause", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool ResetUserColumnModel(TenantRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Tenant, "ResetUserColumnModel", request.TenantId, "GetColumnModelByWhereClause function has been called. UserNum = " + request.UserNum.ToString()));
                return new TenantDAL(request.TenantId).ResetUserColumnModel(request.UserNum);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Tenant, "ResetUserColumnModel", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Tenant, "ResetUserColumnModel", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public TenantResponse UpdateColumnModel(TenantRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Tenant, "UpdateColumnModel", request.TenantId, "UpdateColumnModel function has been called. request.TenantId = " + request.TenantId));
                int rowsAffected = new TenantDAL(request.TenantId).UpdateColumnModel(request.TenantColumnsModel);
                return new TenantResponse
                {
                    Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    Message = string.Format("Column Model has been updated successfully.")
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.SearchMedia, "UpdateColumnModel", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.SearchMedia, "UpdateColumnModel", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public TenantResponse GetConfigurations(int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Tenant, "GetConfigurations", tenantId, "GetConfigurations function has been called. tenantId = " + tenantId));
                return new TenantResponse
                {
                    Configurations = new TenantDAL(tenantId).GetConfigurations()
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.SearchMedia, "GetConfigurations", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.SearchMedia, "GetConfigurations", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        public TenantResponse GetConfigurationsByWhereClause(TenantRequest request)
        {
            try
            {
                StringBuilder sbWhereClause = new StringBuilder();
                sbWhereClause.Append(" 1 = 1 ");

                var criteria = request.TenantConfigurationCriteria;
                #region ------- Search Criteria -------

                if (criteria != null)
                {
                    sbWhereClause.Append(" AND (c.Id IN ( ");
                    foreach (var configId in criteria)
                    {
                        sbWhereClause.AppendFormat("{0},", configId);
                    }
                    sbWhereClause.RemoveLast(",");
                    sbWhereClause.Append(" ) )");
                    sbWhereClause.AppendLine();
                }
                #endregion

                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Tenant, "GetConfigurationsByWhereClause", request.TenantId, "GetConfigurationsByWhereClause function has been called. request.TenantId = " + request.TenantId));


                return new TenantResponse
                {
                    Configurations = new TenantDAL(request.TenantId).GetConfigurations(sbWhereClause.ToString())
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.SearchMedia, "GetConfigurationsByWhereClause", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.SearchMedia, "GetConfigurationsByWhereClause", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }

        }


        public TenantResponse UpdateConfiguration(TenantRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Tenant, "UpdateConfiguration", request.TenantId, "UpdateConfiguration function has been called. request.TenantId = " + request.TenantId));

                int rowsAffected = new TenantDAL(request.TenantId).UpdateConfigurations(request.TenantCustomConfigurations);
                return new TenantResponse
                {
                    Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    RowsAffected = rowsAffected,
                    Message = string.Format("Configuration has been updated successfully.")
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.SearchMedia, "UpdateConfiguration", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.SearchMedia, "UpdateConfiguration", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }

        }

        public bool AuthenticateEmailFromLocalDB(string email)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Tenant, "AuthenticateEmailFromLocalDB", 0, "AuthenticateEmailFromLocalDB function has been called. email = " + email));
                return new TenantDAL(0).AuthenticateEmail(email);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.SearchMedia, "AuthenticateEmailFromLocalDB", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.SearchMedia, "AuthenticateEmailFromLocalDB", 0, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public bool AuthenticateEmail(string email, out int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Tenant, "AuthenticateEmail", 0, "AuthenticateEmail function has been called. email = " + email));
                return new TenantDAL(0).AuthenticateEmail(email, out tenantId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.SearchMedia, "AuthenticateEmail", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.SearchMedia, "AuthenticateEmail", 0, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public bool AuthenticateEmail(string email, out int tenantId, out string connection)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Tenant, "AuthenticateEmail", 0, "AuthenticateEmail function has been called. email = " + email));
                return new TenantDAL(0).AuthenticateEmail(email, out tenantId, out connection);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.SearchMedia, "AuthenticateEmail", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.SearchMedia, "AuthenticateEmail", 0, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public string GetDatabaseConnection(int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Tenant, "GetDatabaseConnection", tenantId, "GetDatabaseConnection function has been called. tenantId = " + tenantId));
                return new TenantDAL(0).GetTenantDbConnection(tenantId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.SearchMedia, "GetDatabaseConnection", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.SearchMedia, "GetDatabaseConnection", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public List<Tenant> AuthenticateEmailAndGetTenants(string email)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Tenant, "AuthenticateEmailAndGetTenants", 0, "AuthenticateEmailAndGetTenants function has been called. email = " + email));
                return new TenantDAL(0).AuthenticateEmailAndGetTenants(email);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.SearchMedia, "AuthenticateEmailAndGetTenants", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.SearchMedia, "AuthenticateEmailAndGetTenants", 0, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public List<CallTag> GetCalTags(int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Tenant, "GetCalTags", tenantId, "GetCalTags function has been called. tenantId = " + tenantId));

                return new TenantDAL(tenantId).GetCalTags();
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Tenant, "GetCalTags", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Tenant, "GetCalTags", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public List<Tenant> GetAllTenants()
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Tenant, "GetAllTenants", 0, "GetAllTenants function has been called. "));
                return new TenantDAL(0).GetAllTenants();
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.SearchMedia, "GetAllTenants", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.SearchMedia, "GetAllTenants", 0, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public List<TenantUser> FetchIQ3TenantUsers(int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Tenant, "FetchIQ3TenantUsers", 0, "FetchIQ3TenantUsers function has been called. tenantId = " + tenantId));
                return new TenantDAL(tenantId).FetchIQ3TenantUsers();
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.SearchMedia, "FetchIQ3TenantUsers", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.SearchMedia, "FetchIQ3TenantUsers", 0, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public List<CustomMarkersData> FetchIQ3TenantUserMarkers(int tenantId, int userNum)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Tenant, "FetchIQ3TenantUserMarkers", tenantId, "FetchIQ3TenantUserMarkers function has been called. tenantId = " + tenantId));
                return new UserManagerDAL(tenantId).GetInquireCustomMarkers(userNum);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.SearchMedia, "FetchIQ3TenantUserMarkers", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.SearchMedia, "FetchIQ3TenantUserMarkers", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public TenantResponse ReorderColumns(TenantRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Tenant, "ReorderColumns", request.TenantId, "ReorderColumns function has been called. request.TenantId = " + request.TenantId));
                int rowsAffected = new TenantDAL(request.TenantId).ReorderColumns(request.TenantColumnsModel);
                return new TenantResponse
                {
                    Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    Message = string.Format("Column Model has been reordered successfully.")
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.SearchMedia, "ReorderColumns", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.SearchMedia, "ReorderColumns", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        #region Tenant License
        public TenantResponse GetTenantInformation(int tenantId, out int noOfDomainUsers)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Tenant, "GetTenantInformation", 0, "GetTenantInformation function has been called. tenantId = " + tenantId));
                return new TenantResponse { TenantInformation = new TenantDAL(tenantId).GetTenantInformation(out noOfDomainUsers) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.SearchMedia, "GetTenantInformation", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.SearchMedia, "GetTenantInformation", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        #endregion
    }
}