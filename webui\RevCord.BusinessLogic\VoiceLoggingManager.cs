﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.Messages;
using RevCord.DataContracts.Criteria;
using RevCord.DataAccess;
using RevCord.DataContracts.MessageBase;
using RevCord.DataContracts;
using RevCord.Util;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.DataContracts.ViewModelEntities;
using RevCord.DataContracts.DTO;
using Newtonsoft.Json.Linq;
using System.IO;
using Newtonsoft.Json;
using System.Web.Script.Serialization;
using System.Data.SqlClient;
using System.Threading.Tasks;
using RevCord.DataAccess.Util;
using RevCord.DataContracts.ReportEntities;

namespace RevCord.BusinessLogic
{
    public class VoiceLoggingManager
    {
        //public static bool _isDemoMode = AppSettingsUtil.GetBool("isDemo");
        private readonly static string _serverIP = SiteConfig.HelperServiceAddress; //"127.0.0.1";

        #region ------- Calls -------

        public VRResponse SearchCalls(VRRequest request)
        {
            List<CallInfo> dbCalls = null;
            int totalPages = 0;
            long totalRecords = 0;
            string durationStr = QueryConstants.DURATION_DEFAULT;
            string queryOptionSTR = string.Empty;

            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "SearchCalls", request.TenantId, "SearchCalls function has been called successfully."));
                var criteria = request.Criteria as CallCriteria;
                int i = request.UserId;

                #region ------- Is Demo Search -------

                bool isDemoSearch = false;
                if (criteria != null && criteria.SearchType == SearchType.Advance)
                {

                    isDemoSearch = criteria.CategoryGroupExtensions.Where(cge => cge.GroupType == GroupType.Email || cge.GroupType == GroupType.Social || cge.GroupType == GroupType.Text || cge.GroupType == GroupType.Video).Count() > 0 ? true : false;
                }
                request.IsDemoBasedSearch = isDemoSearch;

                #endregion

                if (criteria != null)
                {
                    #region ------- Duration String -------
                    if (criteria.StartDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null) || criteria.EndDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null))
                    {
                        durationStr = QueryConstants.DURATION_BETWEEN + criteria.StartDuration.TotalMilliseconds.ToString() + "' AND '" + criteria.EndDuration.TotalMilliseconds.ToString() + "'";
                    }

                    #endregion

                    #region ------- Search Restriction -------

                    if (criteria.RestrictionInHours > 0)
                    {
                        var dt = DateTime.Now.AddHours(-criteria.RestrictionInHours);

                        criteria.StartDate = dt;
                        criteria.EndDate = DateTime.Now;
                        criteria.StartTime = dt.TimeOfDay;
                        criteria.EndTime = TimeSpan.Parse(DateTime.Now.ToString("HH:mm:ss"));// TimeSpan.Parse(DateTime.Now.TimeOfDay.ToString("HH:mm:ss"));
                    }

                    #endregion
                }

                if (request.LoadOptions.Contains("SearchCallsInPrimaryDB"))
                {
                    queryOptionSTR = buildCallSearchQuery(criteria);
                    if (!request.Criteria.IsRandom)
                    {
                        dbCalls = new VoiceLoggingDAL(request.TenantId).SearchCallsPrimaryDB(request.PageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria);
                    }
                    else
                        dbCalls = new VoiceLoggingDAL(request.TenantId).SearchCallsPrimaryDB(request.PageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria, criteria.NoOfCalls);
                }

                if (request.LoadOptions.Contains("SearchCallsInChainDBs"))
                {
                    queryOptionSTR = buildCallSearchQuery(criteria);
                    if (!request.Criteria.IsRandom)
                    {
                        dbCalls = new VoiceLoggingDAL(request.TenantId).SearchCallsChainedDBs(request.PageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria);
                    }
                    else
                        dbCalls = new VoiceLoggingDAL(request.TenantId).SearchCallsChainedDBs(request.PageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria, criteria.NoOfCalls);
                }

                if (request.LoadOptions.Contains("SearchCallsByIdsInChainedDBs"))
                {
                    #region SearchCallsByIdsInChainedDBs

                    StringBuilder sbWhereClause = new StringBuilder();
                    sbWhereClause.Append(" AND( ");
                    sbWhereClause.Append(" CI.CallId IN ( ");
                    foreach (var cid in request.CallIds)
                    {
                        sbWhereClause.AppendFormat("{0},", cid);
                    }
                    sbWhereClause.RemoveLast(",");
                    //sbWhereClause.AppendFormat(@"({0}.CallId IN ({1}))", "CI", grpExt.GroupId, grpExt.ExtensionIdsCSV);
                    sbWhereClause.Append(" ) ");
                    sbWhereClause.Append(" ) ");
                    #endregion
                    queryOptionSTR = sbWhereClause.ToString();
                    dbCalls = new VoiceLoggingDAL(request.TenantId).SearchCallsByIdsInChainedDBs(request.Criteria.StartDate.DateAsStringWithoutSlash(), request.Criteria.EndDate.DateAsStringWithoutSlash(), request.PageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR);
                }

                #region SearchCallsByIdsInPrimaryDB
                if (request.LoadOptions.Contains("SearchCallsByIdsInPrimaryDBForExport"))
                {
                    
                    string csvCallIds = string.Format("{0}", string.Join(",", request.CallIds));
                    dbCalls = new VoiceLoggingDAL(request.TenantId).GetCallInfosFromPrimaryDB(csvCallIds, request.Criteria.StartDate, request.Criteria.EndDate);
                }
                #endregion

                #region ------- Demo Mode -------
                if (request.IsDemoMode && request.IsSearchPageCall)
                {
                    //&& criteria.SearchType == SearchType.Advance && criteria.CategoryGroupExtensions.Where(cge => cge.GroupType == GroupType.Audio || cge.GroupType == GroupType.Screens).Count() == 0
                    List<CallInfo> democalls = new List<CallInfo>();
                    List<CallInfo> newCalls = new List<CallInfo>();
                    int demoRowCounter = 0;

                    // Anyone of the demo node selected
                    switch (request.IsDemoBasedSearch)
                    {
                        case true:
                            #region ------- Demo Based Search -------

                            foreach (var catGrpExt in criteria.CategoryGroupExtensions)
                            {
                                if (catGrpExt.GroupType == GroupType.Email || catGrpExt.GroupType == GroupType.Social || catGrpExt.GroupType == GroupType.Video)
                                {
                                    democalls.AddRange(DemoData.SearchInDemoData(catGrpExt));
                                }
                            }

                            int callsCount = request.PageSize - democalls.Count;

                            if (callsCount > dbCalls.Count)
                            {
                                callsCount = dbCalls.Count;
                            }

                            newCalls = dbCalls.Take(callsCount).ToList();

                            demoRowCounter = (request.PageNumber == 1) ? newCalls.Count + 1 : (request.PageSize) * (request.PageNumber - 1) + newCalls.Count + 1;
                            if (criteria.SearchType == SearchType.Advance && criteria.CategoryGroupExtensions.Where(cge => cge.GroupType == GroupType.Audio || cge.GroupType == GroupType.Text || cge.GroupType == GroupType.Screens || cge.GroupType == GroupType.IQ3 || cge.GroupType == GroupType.Teams).Count() == 0)
                            {
                                newCalls = new List<CallInfo>();
                                demoRowCounter = (request.PageNumber == 1) ? newCalls.Count + 1 : (request.PageSize) * (request.PageNumber - 1) + newCalls.Count + 1;
                                ////No Real Data Search
                                totalPages = 0;
                                totalRecords = 0;
                            }
                            //newCalls = dbCalls.Take(request.PageSize - democalls.Count).ToList();
                            //demoRowCounter = (request.PageNumber == 1) ? newCalls.Count + 1 : (request.PageSize) * (request.PageNumber - 1) + newCalls.Count + 1;

                            democalls.ForEach(c => c.RowNo = demoRowCounter++);
                            newCalls.AddRange(democalls);

                            #endregion
                            break;
                        case false: // None of the Demo node selected
                            #region ------- All Demo Data -------
                            // return all types of demo data
                            //if (criteria.SearchType == SearchType.Default || (criteria.SearchType == SearchType.Advance && criteria.CategoryGroupExtensions.Count == 0))
                            if (criteria.SearchType == SearchType.Default)
                            {
                                democalls = DemoData.GetDemoDataForPage();
                                newCalls = dbCalls.Take(request.PageSize - democalls.Count).ToList();
                                demoRowCounter = (request.PageNumber == 1) ? newCalls.Count + 1 : (request.PageSize) * (request.PageNumber - 1) + newCalls.Count + 1;

                                democalls.ForEach(c => c.RowNo = demoRowCounter++);
                                newCalls.AddRange(democalls);
                            }
                            else if (criteria.SearchType == SearchType.Advance && criteria.CategoryGroupExtensions.Count == 0)
                            {
                                //newCalls = dbCalls;
                                democalls = DemoData.GetDemoDataForPage();
                                newCalls = dbCalls.Take(request.PageSize - democalls.Count).ToList();
                                demoRowCounter = (request.PageNumber == 1) ? newCalls.Count + 1 : (request.PageSize) * (request.PageNumber - 1) + newCalls.Count + 1;

                                democalls.ForEach(c => c.RowNo = demoRowCounter++);
                                newCalls.AddRange(democalls);
                            }
                            // show only real data
                            else if (criteria.SearchType == SearchType.Advance && criteria.CategoryGroupExtensions.Count > 0)
                            {
                                newCalls = dbCalls;
                                //democalls = DemoData.GetDemoDataForPage();
                                //newCalls = dbCalls.Take(request.PageSize - democalls.Count).ToList();
                                //demoRowCounter = (request.PageNumber == 1) ? newCalls.Count + 1 : (request.PageSize) * (request.PageNumber - 1) + newCalls.Count + 1;

                                //democalls.ForEach(c => c.RowNo = demoRowCounter++);
                                //newCalls.AddRange(democalls);
                            }
                            //else if (criteria.SearchType == SearchType.Global && criteria.CategoryGroupExtensions.Count == 0)
                            else if (criteria.SearchType == SearchType.Global)
                            {
                                //newCalls = dbCalls;
                                democalls = DemoData.GetDemoDataForPage();
                                newCalls = dbCalls.Take(request.PageSize - democalls.Count).ToList();
                                demoRowCounter = (request.PageNumber == 1) ? newCalls.Count + 1 : (request.PageSize) * (request.PageNumber - 1) + newCalls.Count + 1;

                                democalls.ForEach(c => c.RowNo = demoRowCounter++);
                                newCalls.AddRange(democalls);
                            }
                            #endregion
                            break;
                    }
                    return new VRResponse
                    {
                        Acknowledge = AcknowledgeType.Success,
                        Calls = newCalls,
                        TotalPages = totalPages,
                        TotalRecords = totalRecords + democalls.Count,
                    };
                }
                #endregion

                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    Calls = dbCalls,
                    TotalPages = totalPages,
                    TotalRecords = totalRecords,
                    Message = queryOptionSTR
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "SearchCalls", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing the function SearchCalls().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "SearchCalls", request.TenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                ex.HelpLink = "<:>" + queryOptionSTR;
                throw ex;
            }
        }


        #region ---------- T1 Extension Filtering ----------------
        /*
      * Module : T1 Extension Filtering
      * Author : Arivu
      * Description : This method is used to filter the dynamic calls to display for IR-Lite.
      */

        public VRResponse TagRuleSearchCalls(VRRequest request, string TagRuleUser)
        {
            List<CallInfo> dbCalls = null;
            int totalPages = 0;
            long totalRecords = 0;
            string durationStr = QueryConstants.DURATION_DEFAULT;
            string queryOptionSTR = string.Empty;

            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "TagRuleSearchCalls", request.TenantId, "TagRuleSearchCalls function has been called successfully."));
                var criteria = request.Criteria as CallCriteria;
                int i = request.UserId;
                string T1MinCh = request.MinT1Ch;
                string T1MaxCh = request.MaxT1Ch;

                #region ------- Is Demo Search -------

                bool isDemoSearch = false;
                if (criteria != null && criteria.SearchType == SearchType.Advance)
                {
                    isDemoSearch = criteria.CategoryGroupExtensions.Where(cge => cge.GroupType == GroupType.Email || cge.GroupType == GroupType.Social || cge.GroupType == GroupType.Text || cge.GroupType == GroupType.Video).Count() > 0 ? true : false;
                }
                request.IsDemoBasedSearch = isDemoSearch;

                #endregion

                if (criteria != null)
                {
                    #region ------- Duration String -------
                    if (criteria.StartDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null) || criteria.EndDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null))
                    {
                        durationStr = QueryConstants.DURATION_BETWEEN + criteria.StartDuration.TotalMilliseconds.ToString() + "' AND '" + criteria.EndDuration.TotalMilliseconds.ToString() + "'";
                    }

                    #endregion

                    #region ------- Search Restriction -------

                    if (criteria.RestrictionInHours > 0)
                    {
                        var dt = DateTime.Now.AddHours(-criteria.RestrictionInHours);

                        criteria.StartDate = dt;
                        criteria.EndDate = DateTime.Now;
                        criteria.StartTime = dt.TimeOfDay;
                        criteria.EndTime = TimeSpan.Parse(DateTime.Now.ToString("HH:mm:ss"));// TimeSpan.Parse(DateTime.Now.TimeOfDay.ToString("HH:mm:ss"));
                    }

                    #endregion
                }

                if (request.LoadOptions.Contains("SearchCallsInPrimaryDB"))
                {
                    queryOptionSTR = TagRulebuildCallSearchQuery(criteria, TagRuleUser, request);
                    dbCalls = new VoiceLoggingDAL(request.TenantId).SearchCallsChainedDBs(request.PageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria);
                }

                if (request.LoadOptions.Contains("SearchCallsInChainDBs"))
                {
                    queryOptionSTR = TagRulebuildCallSearchQuery(criteria, TagRuleUser, request);
                    dbCalls = new VoiceLoggingDAL(request.TenantId).SearchCallsChainedDBs(request.PageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria);
                }

                if (request.LoadOptions.Contains("SearchCallsByIdsInChainedDBs"))
                {
                    #region SearchCallsByIdsInChainedDBs

                    StringBuilder sbWhereClause = new StringBuilder();
                    sbWhereClause.Append(" AND( ");
                    sbWhereClause.Append(" CI.CallId IN ( ");
                    foreach (var cid in request.CallIds)
                    {
                        sbWhereClause.AppendFormat("{0},", cid);
                    }
                    sbWhereClause.RemoveLast(",");
                    //sbWhereClause.AppendFormat(@"({0}.CallId IN ({1}))", "CI", grpExt.GroupId, grpExt.ExtensionIdsCSV);
                    sbWhereClause.Append(" ) ");
                    sbWhereClause.Append(" ) ");
                    #endregion
                    queryOptionSTR = sbWhereClause.ToString();
                    dbCalls = new VoiceLoggingDAL(request.TenantId).SearchCallsByIdsInChainedDBs(request.PageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR);
                }


                #region ------- Demo Mode -------
                if (request.IsDemoMode)
                {
                    //&& criteria.SearchType == SearchType.Advance && criteria.CategoryGroupExtensions.Where(cge => cge.GroupType == GroupType.Audio || cge.GroupType == GroupType.Screens).Count() == 0
                    List<CallInfo> democalls = new List<CallInfo>();
                    List<CallInfo> newCalls = new List<CallInfo>();
                    int demoRowCounter = 0;

                    // Anyone of the demo node selected
                    switch (request.IsDemoBasedSearch)
                    {
                        case true:
                            #region ------- Demo Based Search -------

                            foreach (var catGrpExt in criteria.CategoryGroupExtensions)
                            {
                                if (catGrpExt.GroupType == GroupType.Email || catGrpExt.GroupType == GroupType.Social || catGrpExt.GroupType == GroupType.Text || catGrpExt.GroupType == GroupType.Video)
                                {
                                    democalls.AddRange(DemoData.SearchInDemoData(catGrpExt));
                                }
                            }
                            newCalls = dbCalls.Take(request.PageSize - democalls.Count).ToList();
                            demoRowCounter = (request.PageNumber == 1) ? newCalls.Count + 1 : (request.PageSize) * (request.PageNumber - 1) + newCalls.Count + 1;
                            if (criteria.SearchType == SearchType.Advance && criteria.CategoryGroupExtensions.Where(cge => cge.GroupType == GroupType.Audio || cge.GroupType == GroupType.Screens || cge.GroupType == GroupType.IQ3 || cge.GroupType == GroupType.Teams).Count() == 0)
                            {
                                newCalls = new List<CallInfo>();
                                demoRowCounter = (request.PageNumber == 1) ? newCalls.Count + 1 : (request.PageSize) * (request.PageNumber - 1) + newCalls.Count + 1;
                                ////No Real Data Search
                                totalPages = 0;
                                totalRecords = 0;
                            }

                            democalls.ForEach(c => c.RowNo = demoRowCounter++);
                            newCalls.AddRange(democalls);

                            #endregion
                            break;
                        case false: // None of the Demo node selected
                            #region ------- All Demo Data -------
                            // return all types of demo data
                            //if (criteria.SearchType == SearchType.Default || (criteria.SearchType == SearchType.Advance && criteria.CategoryGroupExtensions.Count == 0))
                            if (criteria.SearchType == SearchType.Default)
                            {
                                democalls = DemoData.GetDemoDataForPage();
                                newCalls = dbCalls.Take(request.PageSize - democalls.Count).ToList();
                                demoRowCounter = (request.PageNumber == 1) ? newCalls.Count + 1 : (request.PageSize) * (request.PageNumber - 1) + newCalls.Count + 1;

                                democalls.ForEach(c => c.RowNo = demoRowCounter++);
                                newCalls.AddRange(democalls);
                            }
                            else if (criteria.SearchType == SearchType.Advance && criteria.CategoryGroupExtensions.Count == 0)
                            {
                                newCalls = dbCalls;
                            }
                            // show only real data
                            else if (criteria.SearchType == SearchType.Advance && criteria.CategoryGroupExtensions.Count > 0)
                            {
                                newCalls = dbCalls;
                            }
                            else if (criteria.SearchType == SearchType.Global && criteria.CategoryGroupExtensions.Count == 0)
                            {
                                newCalls = dbCalls;
                            }
                            #endregion
                            break;
                    }
                    return new VRResponse
                    {
                        Acknowledge = AcknowledgeType.Success,
                        Calls = newCalls,
                        TotalPages = totalPages,
                        TotalRecords = totalRecords + democalls.Count,
                    };
                }
                #endregion

                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    Calls = dbCalls,
                    TotalPages = totalPages,
                    TotalRecords = totalRecords,
                    Message = queryOptionSTR
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "TagRuleSearchCalls", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing the function TagRuleSearchCalls().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "TagRuleSearchCalls", request.TenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                ex.HelpLink = "<:>" + queryOptionSTR;
                throw ex;
            }
        }

        public VRResponse TagRuleGetCallFromPrimary(string callId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "TagRuleGetCallFromPrimary", tenantId, "TagRuleGetCallFromPrimary function has been called successfully."));
                var callLite = new VoiceLoggingDAL(tenantId).GetCallLiteFromPrimary(callId);
                return new VRResponse
                {
                    CallInfoLite = callLite,
                    Acknowledge = callLite != null ? AcknowledgeType.Success : AcknowledgeType.Failure,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "TagRuleGetCallFromPrimary", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure,
                };
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing the function TagRuleGetCallFromPrimary().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "TagRuleGetCallFromPrimary", tenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure,
                };
                throw ex;
            }
        }

        #endregion ---------- T1 Extension Filtering ----------------
        public VRResponse SearchCallsLite(VRRequest request)
        {
            List<CallInfo> dbCalls = null;
            int totalPages = 0;
            long totalRecords = 0;
            string durationStr = QueryConstants.DURATION_DEFAULT;
            string queryOptionSTR = string.Empty;

            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "SearchCallsLite", request.TenantId, "SearchCallsLite function has been called successfully."));
                var criteria = request.Criteria as CallCriteria;
                int i = request.UserId;

                if (criteria != null)
                {
                    #region ------- Duration String -------
                    if (criteria.StartDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null) || criteria.EndDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null))
                    {
                        durationStr = QueryConstants.DURATION_BETWEEN + criteria.StartDuration.TotalMilliseconds.ToString() + "' AND '" + criteria.EndDuration.TotalMilliseconds.ToString() + "'";
                    }

                    #endregion

                    #region ------- Search Restriction -------

                    if (criteria.RestrictionInHours > 0)
                    {
                        var dt = DateTime.Now.AddHours(-criteria.RestrictionInHours);

                        criteria.StartDate = dt;
                        criteria.EndDate = DateTime.Now;
                        criteria.StartTime = dt.TimeOfDay;
                        criteria.EndTime = TimeSpan.Parse(DateTime.Now.ToString("HH:mm:ss"));// TimeSpan.Parse(DateTime.Now.TimeOfDay.ToString("HH:mm:ss"));
                    }

                    #endregion
                }

                if (request.LoadOptions.Contains("SearchCallsInPrimaryDBLite"))
                {
                    queryOptionSTR = buildCallSearchQueryLite(criteria);
                    dbCalls = new VoiceLoggingDAL(request.TenantId).SearchCallsPrimaryDBLite(request.PageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria);
                }

                if (request.LoadOptions.Contains("SearchCallsInChainDBsLite"))
                {
                    queryOptionSTR = buildCallSearchQuery(criteria);
                    dbCalls = new VoiceLoggingDAL(request.TenantId).SearchCallsChainedDBsLite(request.PageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria);
                }

                if (request.LoadOptions.Contains("SearchCallsByIdsInChainedDBsLite"))
                {
                    #region SearchCallsByIdsInChainedDBsLite

                    StringBuilder sbWhereClause = new StringBuilder();
                    sbWhereClause.Append(" AND( ");
                    sbWhereClause.Append(" CI.CallId IN ( ");
                    foreach (var cid in request.CallIds)
                    {
                        sbWhereClause.AppendFormat("{0},", cid);
                    }
                    sbWhereClause.RemoveLast(",");
                    sbWhereClause.Append(" ) ");
                    sbWhereClause.Append(" ) ");
                    #endregion
                    queryOptionSTR = sbWhereClause.ToString();
                    dbCalls = new VoiceLoggingDAL(request.TenantId).SearchCallsByIdsInChainedDBsLite(request.PageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR);
                }
                #region ------- Demo Mode -------
                if (request.IsDemoMode)
                {
                    //&& criteria.SearchType == SearchType.Advance && criteria.CategoryGroupExtensions.Where(cge => cge.GroupType == GroupType.Audio || cge.GroupType == GroupType.Screens).Count() == 0
                    List<CallInfo> democalls = new List<CallInfo>();
                    List<CallInfo> newCalls = new List<CallInfo>();
                    int demoRowCounter = 0;

                    // Anyone of the demo node selected
                    switch (request.IsDemoBasedSearch)
                    {
                        case true:
                            #region ------- Demo Based Search -------

                            foreach (var catGrpExt in criteria.CategoryGroupExtensions)
                            {
                                if (catGrpExt.GroupType == GroupType.Email || catGrpExt.GroupType == GroupType.Social || catGrpExt.GroupType == GroupType.Video)
                                {
                                    democalls.AddRange(DemoData.SearchInDemoData(catGrpExt));
                                }
                            }
                            newCalls = dbCalls.Take(request.PageSize - democalls.Count).ToList();
                            demoRowCounter = (request.PageNumber == 1) ? newCalls.Count + 1 : (request.PageSize) * (request.PageNumber - 1) + newCalls.Count + 1;
                            if (criteria.SearchType == SearchType.Advance && criteria.CategoryGroupExtensions.Where(cge => cge.GroupType == GroupType.Audio || cge.GroupType == GroupType.Screens || cge.GroupType == GroupType.IQ3 || cge.GroupType == GroupType.Teams).Count() == 0)
                            {
                                newCalls = new List<CallInfo>();
                                demoRowCounter = (request.PageNumber == 1) ? newCalls.Count + 1 : (request.PageSize) * (request.PageNumber - 1) + newCalls.Count + 1;
                                ////No Real Data Search
                                totalPages = 0;
                                totalRecords = 0;
                            }
                            //newCalls = dbCalls.Take(request.PageSize - democalls.Count).ToList();
                            //demoRowCounter = (request.PageNumber == 1) ? newCalls.Count + 1 : (request.PageSize) * (request.PageNumber - 1) + newCalls.Count + 1;

                            democalls.ForEach(c => c.RowNo = demoRowCounter++);
                            newCalls.AddRange(democalls);

                            #endregion
                            break;
                        case false: // None of the Demo node selected
                            #region ------- All Demo Data -------
                            // return all types of demo data
                            //if (criteria.SearchType == SearchType.Default || (criteria.SearchType == SearchType.Advance && criteria.CategoryGroupExtensions.Count == 0))
                            if (criteria.SearchType == SearchType.Default)
                            {
                                democalls = DemoData.GetDemoDataForPage();
                                newCalls = dbCalls.Take(request.PageSize - democalls.Count).ToList();
                                demoRowCounter = (request.PageNumber == 1) ? newCalls.Count + 1 : (request.PageSize) * (request.PageNumber - 1) + newCalls.Count + 1;

                                democalls.ForEach(c => c.RowNo = demoRowCounter++);
                                newCalls.AddRange(democalls);
                            }
                            else if (criteria.SearchType == SearchType.Advance && criteria.CategoryGroupExtensions.Count == 0)
                            {
                                //newCalls = dbCalls;
                                democalls = DemoData.GetDemoDataForPage();
                                newCalls = dbCalls.Take(request.PageSize - democalls.Count).ToList();
                                demoRowCounter = (request.PageNumber == 1) ? newCalls.Count + 1 : (request.PageSize) * (request.PageNumber - 1) + newCalls.Count + 1;

                                democalls.ForEach(c => c.RowNo = demoRowCounter++);
                                newCalls.AddRange(democalls);
                            }
                            // show only real data
                            else if (criteria.SearchType == SearchType.Advance && criteria.CategoryGroupExtensions.Count > 0)
                            {
                                //newCalls = dbCalls;
                                democalls = DemoData.GetDemoDataForPage();
                                newCalls = dbCalls.Take(request.PageSize - democalls.Count).ToList();
                                demoRowCounter = (request.PageNumber == 1) ? newCalls.Count + 1 : (request.PageSize) * (request.PageNumber - 1) + newCalls.Count + 1;

                                democalls.ForEach(c => c.RowNo = demoRowCounter++);
                                newCalls.AddRange(democalls);
                            }
                            //else if (criteria.SearchType == SearchType.Global && criteria.CategoryGroupExtensions.Count == 0)
                            else if (criteria.SearchType == SearchType.Global)
                            {
                                //newCalls = dbCalls;
                                democalls = DemoData.GetDemoDataForPage();
                                newCalls = dbCalls.Take(request.PageSize - democalls.Count).ToList();
                                demoRowCounter = (request.PageNumber == 1) ? newCalls.Count + 1 : (request.PageSize) * (request.PageNumber - 1) + newCalls.Count + 1;

                                democalls.ForEach(c => c.RowNo = demoRowCounter++);
                                newCalls.AddRange(democalls);
                            }
                            #endregion
                            break;
                    }
                    return new VRResponse
                    {
                        Acknowledge = AcknowledgeType.Success,
                        Calls = newCalls,
                        TotalPages = totalPages,
                        TotalRecords = totalRecords + democalls.Count,
                    };
                }
                #endregion
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    Calls = dbCalls,
                    TotalPages = totalPages,
                    TotalRecords = totalRecords,
                    Message = queryOptionSTR
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "SearchCallsLite", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing the function SearchCallsLite().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "SearchCallsLite", request.TenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                ex.HelpLink = "<:>" + queryOptionSTR;
                throw ex;
            }
        }
        public VRResponse SearchCallsForExportData(VRRequest request)
        {
            List<CallInfo> dbCalls = null;
            int totalPages = 0;
            long totalRecords = 0;
            string durationStr = QueryConstants.DURATION_DEFAULT;
            string queryOptionSTR = string.Empty;
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "SearchCallsForExportData", request.TenantId, "SearchCallsForExportData function has been called successfully."));
                var criteria = request.Criteria as CallCriteria;
                int i = request.UserId;

                if (criteria != null)
                {
                    #region ------- Duration String -------
                    if (criteria.StartDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null) || criteria.EndDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null))
                    {
                        durationStr = QueryConstants.DURATION_BETWEEN + criteria.StartDuration.TotalMilliseconds.ToString() + "' AND '" + criteria.EndDuration.TotalMilliseconds.ToString() + "'";
                    }
                    #endregion

                    #region ------- Search Restriction -------
                    if (criteria.RestrictionInHours > 0)
                    {
                        var dt = DateTime.Now.AddHours(-criteria.RestrictionInHours);

                        criteria.StartDate = dt;
                        criteria.EndDate = DateTime.Now;
                        criteria.StartTime = dt.TimeOfDay;
                        criteria.EndTime = TimeSpan.Parse(DateTime.Now.ToString("HH:mm:ss"));// TimeSpan.Parse(DateTime.Now.TimeOfDay.ToString("HH:mm:ss"));
                    }
                    #endregion
                }

                queryOptionSTR = buildCallSearchQueryForExportData(criteria);
                dbCalls = new VoiceLoggingDAL(request.TenantId).SearchCallsForExportData(request.PageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria);

                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    Calls = dbCalls,
                    TotalPages = totalPages,
                    TotalRecords = totalRecords,
                    Message = queryOptionSTR
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "SearchCallsForExportData", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing the function SearchCallsForExportData().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "SearchCallsForExportData", request.TenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public VRResponse GetCallFromPrimary(string callId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetCallFromPrimary", tenantId, "GetCallFromPrimary function has been called successfully."));
                var callLite = new VoiceLoggingDAL(tenantId).GetCallLiteFromPrimary(callId);
                return new VRResponse
                {
                    CallInfoLite = callLite,
                    Acknowledge = callLite != null ? AcknowledgeType.Success : AcknowledgeType.Failure,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetCallFromPrimary", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing the function GetCallFromPrimary().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetCallFromPrimary", tenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public VRResponse GetCallDetailsFromRecorder(Recorder recorder, string callId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetCallDetailsFromRecorder", 0, "GetCallDetailsFromRecorder function has been called successfully."));
                var callLite = new CallInfoLite();
                if (recorder.IsPrimary)
                    callLite = VoiceLoggingDALEC.GetCallDetailsFromRecorder(recorder, callId);
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    callLite = entClient.GetCallDetailsFromRecorder(recorder, callId);
                }
                return new VRResponse
                {
                    CallInfoLite = callLite,
                    Acknowledge = callLite != null ? AcknowledgeType.Success : AcknowledgeType.Failure,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetCallDetailsFromRecorder", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure,
                };
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing the function GetCallDetailsFromRecorder().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetCallDetailsFromRecorder", 0, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure,
                };
            }
        }

        public VRResponse GetCallByIdFromRecorder(Recorder recorder, string callId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetCallByIdFromRecorder", 0, "GetCallByIdFromRecorder function has been called successfully."));
                var call = new CallInfo();
                if (recorder.IsPrimary)
                    call = VoiceLoggingDALEC.GetCallByIdFromRecorder(recorder, callId);
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    call = entClient.GetCallByIdFromRecorder(recorder, callId);
                }
                return new VRResponse
                {
                    CallInfo = call,
                    Acknowledge = call != null ? AcknowledgeType.Success : AcknowledgeType.Failure,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetCallDetailsFromRecorder", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure,
                };
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing the function GetCallDetailsFromRecorder().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetCallDetailsFromRecorder", 0, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure,
                };
            }
        }

        #endregion

        #region ------- Search Calls EC -------

        //private static List<Recorder> recorders = new List<Recorder> 
        //{ 
        //    new Recorder { Id = 1, Name = "SARFRAZ-REC", IP = "***********", ConnectionString = @"Data Source=Dell\REVCORD,49172;Initial Catalog=VoiceRec;User ID=sa;Password=******;Persist Security Info=True;" },
        //    new Recorder { Id = 2, Name = "SHER-REC", IP = "***********", ConnectionString = @"Data Source=.;Initial Catalog=VoiceRec_R9.3;Integrated Security=SSPI" },
        //    //new Recorder { Id = 2, Name = "SHER-REC", IP = "***********", ConnectionString = @"Data Source=.;Initial Catalog=VoiceRec_R9.2;Integrated Security=SSPI" },
        //};

        public VRResponse SearchCallsInAllRecorders(VRRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "SearchCallsInAllRecorders", request.TenantId, "SearchCallsInAllRecorders function has been called successfully."));
                if (request.LoadOptions.Contains("SearchCallsInPrimaryDBForExportData"))
                {
                    return this.searchCallsInAllRecordersForExport(request);
                }

                List<Recorder> recorders = request.Recorders;

                var criteria = request.Criteria as CallCriteria;

                int totalPages = 0;
                long totalRecords = 0;

                //if (criteria.SearchType == SearchType.Global)
                #region ------- Duration String -------

                string durationStr = QueryConstants.DURATION_DEFAULT;
                if (criteria.StartDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null) || criteria.EndDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null))
                    durationStr = QueryConstants.DURATION_BETWEEN + criteria.StartDuration.TotalMilliseconds.ToString() + "' AND '" + criteria.EndDuration.TotalMilliseconds.ToString() + "'";

                #endregion


                #region ------- Search Restriction -------

                if (criteria.RestrictionInHours > 0)
                {
                    var dt = DateTime.Now.AddHours(-criteria.RestrictionInHours);

                    criteria.StartDate = dt;
                    criteria.EndDate = DateTime.Now;
                    criteria.StartTime = dt.TimeOfDay;
                    //criteria.EndTime = DateTime.Now.TimeOfDay;
                    criteria.EndTime = TimeSpan.Parse(DateTime.Now.ToString("HH:mm:ss")); //TimeSpan.Parse(DateTime.Now.TimeOfDay.ToString("HH:mm:ss"));
                }

                #endregion


                string queryOptionSTR = "";

                var recCalls = new List<RecorderCallInfo>();

                if (request.LoadOptions.Contains("SearchCallsInPrimaryDB"))
                {
                    foreach (var rec in recorders)
                    {
                        bool bExceptionThrown = false;
                        try
                        {
                            totalPages = 0;
                            totalRecords = 0;

                            if (criteria.RecorderCategoryGroupExtensions.Count == 0)
                            {
                                queryOptionSTR = buildCallSearchQueryForRecorder(criteria, rec.Id, rec.IsPrimary);
                                var calls = new List<CallInfo>();
                                if (rec.IsPrimary)
                                {
                                    if (!request.Criteria.IsRandom)
                                    {
                                        calls = VoiceLoggingDALEC.SearchCallsPrimaryDB(rec.SearchPageSize == 0 ? 1 : rec.SearchPageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria, rec);
                                    }
                                    else
                                    {
                                        calls = VoiceLoggingDALEC.SearchCallsPrimaryDB(rec.SearchPageSize == 0 ? 1 : rec.SearchPageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria, criteria.NoOfCalls, rec);
                                    }
                                }
                                else
                                {
                                    // Alternative approach
                                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                                    string startDate = criteria.StartDate.DateAsStringWithoutSlash();
                                    string endDate = criteria.EndDate.DateAsStringWithoutSlash();
                                    string startTime = criteria.StartTime.ConvertTimeSpanToString();
                                    string endTime = criteria.EndTime.ConvertTimeSpanToString();
                                    bool isGlobalSearch = criteria.SearchType == SearchType.Global ? true : false;
                                    bool isRandom = criteria.IsRandom;
                                    bool isPercentage = criteria.IsPercentage;
                                    if (!criteria.IsRandom)
                                    {
                                        calls = entClient.SearchRecordedCalls(rec.SearchPageSize == 0 ? 1 : rec.SearchPageSize, request.PageNumber, startDate, endDate, startTime, endTime, isGlobalSearch, durationStr, queryOptionSTR, rec, out totalPages, out totalRecords).ToList();
                                    }
                                    else
                                    {
                                        calls = entClient.SearchRandomCallsPrimaryDB(rec.SearchPageSize == 0 ? 1 : rec.SearchPageSize, request.PageNumber, startDate, endDate, startTime, endTime, isGlobalSearch, isRandom, isPercentage, durationStr, queryOptionSTR, criteria.NoOfCalls, rec, out totalPages, out totalRecords).ToList();
                                    }
                                }
                                recCalls.Add(new RecorderCallInfo { RecorderId = rec.Id, RecorderName = rec.Name, Calls = calls, TotalPages = totalPages, TotalRecords = totalRecords });
                            }
                            else
                            {
                                var recNodeSelect = criteria.RecorderCategoryGroupExtensions.FirstOrDefault(r => r.RecorderId == rec.Id);
                                if (recNodeSelect != null)
                                {

                                    queryOptionSTR = buildCallSearchQueryForRecorder(criteria, rec.Id, rec.IsPrimary);
                                    List<CallInfo> calls = new List<CallInfo>();
                                    if (rec.IsPrimary)
                                    {
                                        if (!criteria.IsRandom)
                                            calls = VoiceLoggingDALEC.SearchCallsPrimaryDB(rec.SearchPageSize == 0 ? 1 : rec.SearchPageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria, rec);
                                        else
                                            calls = VoiceLoggingDALEC.SearchCallsPrimaryDB(rec.SearchPageSize == 0 ? 1 : rec.SearchPageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria, criteria.NoOfCalls, rec);
                                    }
                                    else
                                    {
                                        // Alternative approach
                                        RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                                        entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                                        string startDate = criteria.StartDate.DateAsStringWithoutSlash();
                                        string endDate = criteria.EndDate.DateAsStringWithoutSlash();
                                        string startTime = criteria.StartTime.ConvertTimeSpanToString();
                                        string endTime = criteria.EndTime.ConvertTimeSpanToString();
                                        bool isGlobalSearch = criteria.SearchType == SearchType.Global ? true : false;
                                        bool isRandom = criteria.IsRandom;
                                        bool isPercentage = criteria.IsPercentage;
                                        queryOptionSTR = queryOptionSTR + "  AND (CI.CallType = 1 OR CI.CallType = 6)";
                                        if (!criteria.IsRandom)
                                        {
                                            calls = entClient.SearchRecordedCalls(rec.SearchPageSize == 0 ? 1 : rec.SearchPageSize, request.PageNumber, startDate, endDate, startTime, endTime, isGlobalSearch, durationStr, queryOptionSTR, rec, out totalPages, out totalRecords).ToList();
                                        }
                                        else
                                        {
                                            calls = entClient.SearchRandomCallsPrimaryDB(rec.SearchPageSize == 0 ? 1 : rec.SearchPageSize, request.PageNumber, startDate, endDate, startTime, endTime, isGlobalSearch, isRandom, isPercentage, durationStr, queryOptionSTR, criteria.NoOfCalls, rec, out totalPages, out totalRecords).ToList();
                                        }
                                    }
                                    recCalls.Add(new RecorderCallInfo { RecorderId = rec.Id, RecorderName = rec.Name, Calls = calls, TotalPages = totalPages, TotalRecords = totalRecords });
                                    Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "SearchCallsInAllRecorders", request.TenantId, "SearchCallsInAllRecorders(). Calls searched successfully from recorder. Recorder Id = " + rec.Id + " , Recorder Name = " + rec.Name));
                                }
                                else
                                {
                                    recCalls.Add(new RecorderCallInfo { RecorderId = rec.Id, RecorderName = rec.Name, Calls = null, TotalPages = totalPages, TotalRecords = totalRecords });
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            bExceptionThrown = true;
                            Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "SearchCallsInAllRecorders", request.TenantId, "An error has occurred while searching calls from Recorder Id = " + rec.Id + ", Recorder Name = " + rec.Name + " exception = " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                            //AppLogHelper.PrintEnterpriseExceptionPattern(ex);
                        }
                        if (bExceptionThrown)
                            continue;
                        //queryOptionSTR = buildCallSearchQueryForRecorder(criteria, rec.Id);
                        //var calls = VoiceLoggingDALEC.SearchCallsPrimaryDB(request.PageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria, rec);
                        //recCalls.Add(new RecorderCallInfo { RecorderId = rec.Id, RecorderName = rec.Name, Calls = calls, TotalPages = totalPages, TotalRecords = totalRecords });
                    }
                }


                if (request.LoadOptions.Contains("SearchCallsInChainDBs"))
                {
                    try
                    {
                        //queryOptionSTR = buildCallSearchQuery(criteria);
                        //dbCalls = new VoiceLoggingDAL(request.TenantId).SearchCallsChainedDBs(request.PageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria);

                        foreach (var rec in recorders)
                        {
                            bool bExceptionThrown = false;
                            try
                            {
                                totalPages = 0;
                                totalRecords = 0;

                                if (criteria.RecorderCategoryGroupExtensions.Count == 0 && (criteria.ArchiveGroup == null || criteria.ArchiveGroup.Length == 0))
                                {
                                    queryOptionSTR = buildCallSearchQueryForRecorder(criteria, rec.Id, rec.IsPrimary);
                                    var calls = new List<CallInfo>();
                                    if (rec.IsPrimary)
                                    {
                                        calls = VoiceLoggingDALEC.SearchCallsChainedDBs(rec.SearchPageSize == 0 ? 1 : rec.SearchPageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria, rec);
                                        Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "SearchCallsInAllRecorders", request.TenantId, "VoiceLoggingManager : SearchCallsInChainDBs RecorderCategoryGroupExtensions.Count =" + criteria.RecorderCategoryGroupExtensions.Count + " rec.IsPrimary = " + rec.IsPrimary + " calls.count = " + calls.Count + " totalRecords = " + totalRecords + " totalPages = " + totalPages));
                                    }
                                    else
                                    {
                                        // Alternative approach
                                        RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                                        entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                                        entClient.Endpoint.Binding.SendTimeout = new TimeSpan(0, 10, 0);
                                        entClient.Endpoint.Binding.CloseTimeout = new TimeSpan(0, 10, 0);
                                        entClient.Endpoint.Binding.OpenTimeout = new TimeSpan(0, 10, 0);
                                        entClient.Endpoint.Binding.ReceiveTimeout = new TimeSpan(0, 10, 0);
                                        string startDate = criteria.StartDate.DateAsStringWithoutSlash();
                                        string endDate = criteria.EndDate.DateAsStringWithoutSlash();
                                        string startTime = criteria.StartTime.ConvertTimeSpanToString();
                                        string endTime = criteria.EndTime.ConvertTimeSpanToString();
                                        bool isGlobalSearch = criteria.SearchType == SearchType.Global ? true : false;
                                        bool isRandom = criteria.IsRandom;
                                        bool isPercentage = criteria.IsPercentage;
                                        calls = entClient.SearchCallsChainedDBs(rec.SearchPageSize == 0 ? 1 : rec.SearchPageSize, request.PageNumber, startDate, endDate, startTime, endTime, isGlobalSearch, durationStr, queryOptionSTR, rec, out totalPages, out totalRecords).ToList();
                                        if (calls != null)
                                            calls = calls.Select(c => { c.RecorderId = rec.Id; c.RecorderName = rec.Name; return c; }).ToList();
                                        Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "SearchCallsInAllRecorders", request.TenantId, "VoiceLoggingManager : SearchCallsInChainDBs RecorderCategoryGroupExtensions.Count =" + criteria.RecorderCategoryGroupExtensions.Count + " rec.IsPrimary = " + rec.IsPrimary + " calls.count = " + calls.Count + " totalRecords = " + totalRecords + " totalPages = " + totalPages));
                                    }
                                    recCalls.Add(new RecorderCallInfo { RecorderId = rec.Id, RecorderName = rec.Name, Calls = calls, TotalPages = totalPages, TotalRecords = totalRecords });
                                }
                                else
                                {
                                    var recNodeSelect = criteria.RecorderCategoryGroupExtensions.FirstOrDefault(r => r.RecorderId == rec.Id);
                                    if (recNodeSelect != null || (rec.IsPrimary && criteria.ArchiveGroup != null && criteria.ArchiveGroup.Length > 0))
                                    {

                                        queryOptionSTR = buildCallSearchQueryForRecorder(criteria, rec.Id, rec.IsPrimary);
                                        List<CallInfo> calls = new List<CallInfo>();
                                        if (rec.IsPrimary)
                                        {
                                            calls = VoiceLoggingDALEC.SearchCallsChainedDBs(rec.SearchPageSize == 0 ? 1 : rec.SearchPageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria, rec);
                                            Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "SearchCallsInAllRecorders", request.TenantId, "VoiceLoggingManager : SearchCallsInChainDBs RecorderCategoryGroupExtensions.Count =" + criteria.RecorderCategoryGroupExtensions.Count + " rec.IsPrimary = " + rec.IsPrimary + " calls.count = " + calls.Count + " totalRecords = " + totalRecords + " totalPages = " + totalPages));
                                        }
                                        else
                                        {
                                            // Alternative approach
                                            RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                                            entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                                            entClient.Endpoint.Binding.SendTimeout = new TimeSpan(0, 10, 0);
                                            entClient.Endpoint.Binding.CloseTimeout = new TimeSpan(0, 10, 0);
                                            entClient.Endpoint.Binding.OpenTimeout = new TimeSpan(0, 10, 0);
                                            entClient.Endpoint.Binding.ReceiveTimeout = new TimeSpan(0, 10, 0);
                                            string startDate = criteria.StartDate.DateAsStringWithoutSlash();
                                            string endDate = criteria.EndDate.DateAsStringWithoutSlash();
                                            string startTime = criteria.StartTime.ConvertTimeSpanToString();
                                            string endTime = criteria.EndTime.ConvertTimeSpanToString();
                                            bool isGlobalSearch = criteria.SearchType == SearchType.Global ? true : false;
                                            bool isRandom = criteria.IsRandom;
                                            bool isPercentage = criteria.IsPercentage;
                                            queryOptionSTR = queryOptionSTR + "  AND (CI.CallType = 1 OR CI.CallType = 6)";
                                            calls = entClient.SearchCallsChainedDBs(rec.SearchPageSize == 0 ? 1 : rec.SearchPageSize, request.PageNumber, startDate, endDate, startTime, endTime, isGlobalSearch, durationStr, queryOptionSTR, rec, out totalPages, out totalRecords).ToList();
                                            if (calls != null)
                                                calls = calls.Select(c => { c.RecorderId = rec.Id; c.RecorderName = rec.Name; return c; }).ToList();
                                            Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "SearchCallsInAllRecorders", request.TenantId, "VoiceLoggingManager : SearchCallsInChainDBs RecorderCategoryGroupExtensions.Count =" + criteria.RecorderCategoryGroupExtensions.Count + " rec.IsPrimary = " + rec.IsPrimary + " calls.count = " + calls.Count + " totalRecords = " + totalRecords + " totalPages = " + totalPages));

                                        }
                                        recCalls.Add(new RecorderCallInfo { RecorderId = rec.Id, RecorderName = rec.Name, Calls = calls, TotalPages = totalPages, TotalRecords = totalRecords });
                                        Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "SearchCallsInAllRecorders", request.TenantId, "VoiceLoggingManager : SearchCallsInChainDBs RecorderCategoryGroupExtensions.Count =" + criteria.RecorderCategoryGroupExtensions.Count + " rec.IsPrimary = " + rec.IsPrimary + " calls.count = " + calls.Count + " totalRecords = " + totalRecords + " totalPages = " + totalPages));
                                    }
                                    else
                                    {
                                        recCalls.Add(new RecorderCallInfo { RecorderId = rec.Id, RecorderName = rec.Name, Calls = null, TotalPages = totalPages, TotalRecords = totalRecords });
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                bExceptionThrown = true;
                                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "SearchCallsInAllRecorders", request.TenantId, "An error has occurred while searching calls , LoadOptions = SearchCallsInChainDBs , Recorder Id = " + rec.Id + ", Recorder Name = " + rec.Name + " exception = " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                            }
                            if (bExceptionThrown)
                                continue;
                        }
                    }
                    catch (Exception ex)
                    {
                        Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "SearchCallsInAllRecorders", request.TenantId, "An error has occurred while searching calls.  LoadOptions = SearchCallsInChainDBs , exception = " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                        throw ex;
                    }
                }

                //if (request.LoadOptions.Contains("SearchCallsByIdsInChainedDBs"))
                //{
                //    #region SearchCallsByIdsInChainedDBs 

                //    StringBuilder sbWhereClause = new StringBuilder();
                //    sbWhereClause.Append(" AND( ");
                //    sbWhereClause.Append(" CI.CallId IN ( ");
                //    foreach (var cid in request.CallIds)
                //    {
                //        sbWhereClause.AppendFormat("{0},", cid);
                //    }
                //    sbWhereClause.RemoveLast(",");
                //    //sbWhereClause.AppendFormat(@"({0}.CallId IN ({1}))", "CI", grpExt.GroupId, grpExt.ExtensionIdsCSV);
                //    sbWhereClause.Append(" ) ");
                //    sbWhereClause.Append(" ) ");
                //    #endregion
                //    queryOptionSTR = sbWhereClause.ToString();
                //    dbCalls = new VoiceLoggingDAL(request.TenantId).SearchCallsByIdsInChainedDBs(request.Criteria.StartDate.DateAsStringWithoutSlash(), request.Criteria.EndDate.DateAsStringWithoutSlash(), request.PageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR);
                //}

                // Calls Processing
                //long allRecTotalRecords = recCalls[0].TotalRecords + recCalls[1].TotalRecords;
                //var allRecTotalRecords = recCalls.Where(w => w.Calls != null).Select(x => new { RecorderId = x.RecorderId, Count = x.TotalRecords });
                long allRecTotalRecords = recCalls.Sum(c => c.TotalRecords);
                int allRecTotalPages = recCalls.Sum(c => c.TotalPages);

                List<CallInfo> callResults = new List<CallInfo>();

                foreach (var rc in recCalls.Where(w => w.Calls != null))
                {
                    double rCallCount = (double)(rc.TotalRecords * request.PageSize) / allRecTotalRecords; //rc.TotalRecords / hcf;
                                                                                                           //double rCallCount1 = Math.Round(Convert.ToDouble((double)rc.TotalRecords * 100 / allRecTotalRecords), 2);
                    int count = (int)Math.Round(rCallCount);

                    //====== Next Page Size ======//
                    rc.PageSize = count;
                    //====== Next Page Size ======//

                    if (count == request.PageSize)
                        count = (int)allRecTotalRecords;//or rc.TotalRecords;
                    callResults.AddRange(rc.Calls.Take(count));
                }

                callResults = callResults.OrderByDescending(o => o.StartTime).ToList();


                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    Calls = callResults,
                    TotalPages = allRecTotalPages,
                    TotalRecords = allRecTotalRecords,
                    RecorderCalls = recCalls
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "SearchCallsInAllRecorders", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "SearchCallsInAllRecorders", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public VRResponse FindCallsInAllRecorders(VRRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "FindCallsInAllRecorders", request.TenantId, "FindCallsInAllRecorders function has been called successfully."));
                List<Recorder> recorders = request.Recorders;
                var criteria = request.Criteria as CallCriteria;
                int totalPages = 0;
                long totalRecords = 0;

                #region ------- Duration String -------
                string durationStr = QueryConstants.DURATION_DEFAULT;
                if (criteria.StartDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null) || criteria.EndDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null))
                    durationStr = QueryConstants.DURATION_BETWEEN + criteria.StartDuration.TotalMilliseconds.ToString() + "' AND '" + criteria.EndDuration.TotalMilliseconds.ToString() + "'";
                #endregion
                #region ------- Search Restriction -------
                if (criteria.RestrictionInHours > 0)
                {
                    var dt = DateTime.Now.AddHours(-criteria.RestrictionInHours);
                    criteria.StartDate = dt;
                    criteria.EndDate = DateTime.Now;
                    criteria.StartTime = dt.TimeOfDay;
                    criteria.EndTime = TimeSpan.Parse(DateTime.Now.ToString("HH:mm:ss"));
                }
                #endregion
                string queryOptionSTR = "";
                var recCalls = new List<RecorderCallInfo>();
                foreach (var rec in recorders)
                {
                    bool bExceptionThrown = false;
                    try
                    {
                        totalPages = 0;
                        totalRecords = 0;
                        if (criteria.RecorderCategoryGroupExtensions.Count == 0)
                        {
                            queryOptionSTR = buildCallSearchQueryForRecorder(criteria, rec.Id, rec.IsPrimary);
                            var calls = new List<CallInfo>();
                            if (rec.IsPrimary)
                            {
                                if (!request.Criteria.IsRandom)
                                {
                                    calls = VoiceLoggingDALEC.SearchCallsPrimaryDB(rec.SearchPageSize == 0 ? 1 : rec.SearchPageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria, rec);
                                }
                                else
                                {
                                    calls = VoiceLoggingDALEC.SearchCallsPrimaryDB(rec.SearchPageSize == 0 ? 1 : rec.SearchPageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria, criteria.NoOfCalls, rec);
                                }
                            }
                            else
                            {
                                // Alternative approach
                                RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                                entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                                string startDate = criteria.StartDate.DateAsStringWithoutSlash();
                                string endDate = criteria.EndDate.DateAsStringWithoutSlash();
                                string startTime = criteria.StartTime.ConvertTimeSpanToString();
                                string endTime = criteria.EndTime.ConvertTimeSpanToString();
                                bool isGlobalSearch = criteria.SearchType == SearchType.Global ? true : false;
                                bool isRandom = criteria.IsRandom;
                                bool isPercentage = criteria.IsPercentage;
                                if (!criteria.IsRandom)
                                {
                                    calls = entClient.SearchCallsPrimaryDB(rec.SearchPageSize == 0 ? 1 : rec.SearchPageSize, request.PageNumber, startDate, endDate, startTime, endTime, isGlobalSearch, durationStr, queryOptionSTR, rec, out totalPages, out totalRecords).ToList();
                                }
                                else
                                {
                                    calls = entClient.SearchRandomCallsPrimaryDB(rec.SearchPageSize == 0 ? 1 : rec.SearchPageSize, request.PageNumber, startDate, endDate, startTime, endTime, isGlobalSearch, isRandom, isPercentage, durationStr, queryOptionSTR, criteria.NoOfCalls, rec, out totalPages, out totalRecords).ToList();
                                }
                            }
                            recCalls.Add(new RecorderCallInfo { RecorderId = rec.Id, RecorderName = rec.Name, Calls = calls, TotalPages = totalPages, TotalRecords = totalRecords });
                        }
                        else
                        {
                            var recNodeSelect = criteria.RecorderCategoryGroupExtensions.FirstOrDefault(r => r.RecorderId == rec.Id);
                            if (recNodeSelect != null)
                            {
                                queryOptionSTR = buildCallSearchQueryForRecorder(criteria, rec.Id, rec.IsPrimary);
                                List<CallInfo> calls = new List<CallInfo>();
                                if (rec.IsPrimary)
                                {
                                    if (!criteria.IsRandom)
                                        calls = VoiceLoggingDALEC.SearchCallsPrimaryDB(rec.SearchPageSize == 0 ? 1 : rec.SearchPageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria, rec);
                                    else
                                        calls = VoiceLoggingDALEC.SearchCallsPrimaryDB(rec.SearchPageSize == 0 ? 1 : rec.SearchPageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria, criteria.NoOfCalls, rec);
                                }
                                else
                                {
                                    // Alternative approach
                                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                                    string startDate = criteria.StartDate.DateAsStringWithoutSlash();
                                    string endDate = criteria.EndDate.DateAsStringWithoutSlash();
                                    string startTime = criteria.StartTime.ConvertTimeSpanToString();
                                    string endTime = criteria.EndTime.ConvertTimeSpanToString();
                                    bool isGlobalSearch = criteria.SearchType == SearchType.Global ? true : false;
                                    bool isRandom = criteria.IsRandom;
                                    bool isPercentage = criteria.IsPercentage;
                                    if (!criteria.IsRandom)
                                    {
                                        calls = entClient.SearchCallsPrimaryDB(rec.SearchPageSize == 0 ? 1 : rec.SearchPageSize, request.PageNumber, startDate, endDate, startTime, endTime, isGlobalSearch, durationStr, queryOptionSTR, rec, out totalPages, out totalRecords).ToList();
                                    }
                                    else
                                        calls = entClient.SearchRandomCallsPrimaryDB(rec.SearchPageSize == 0 ? 1 : rec.SearchPageSize, request.PageNumber, startDate, endDate, startTime, endTime, isGlobalSearch, isRandom, isPercentage, durationStr, queryOptionSTR, criteria.NoOfCalls, rec, out totalPages, out totalRecords).ToList();
                                }
                                recCalls.Add(new RecorderCallInfo { RecorderId = rec.Id, RecorderName = rec.Name, Calls = calls, TotalPages = totalPages, TotalRecords = totalRecords });
                                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "FindCallsInAllRecorders", request.TenantId, "Calls searched successfully from recorder. Recorder Id = " + rec.Id + " , Recorder Name = " + rec.Name));
                            }
                            else
                            {
                                recCalls.Add(new RecorderCallInfo { RecorderId = rec.Id, RecorderName = rec.Name, Calls = null, TotalPages = totalPages, TotalRecords = totalRecords });
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        bExceptionThrown = true;
                        Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "SearchCallsInAllRecorders", request.TenantId, "An error has occurred while searching calls , Recorder Id = " + rec.Id + ", Recorder Name = " + rec.Name + " exception = " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                    }
                    if (bExceptionThrown)
                        continue;
                }
                // Calls Processing

                long allRecTotalRecords = recCalls.Sum(c => c.TotalRecords);
                int allRecTotalPages = recCalls.Sum(c => c.TotalPages);
                List<CallInfo> callResults = new List<CallInfo>();
                foreach (var rc in recCalls.Where(w => w.Calls != null))
                {
                    double rCallCount = (double)(rc.TotalRecords * request.PageSize) / allRecTotalRecords; //rc.TotalRecords / hcf;
                                                                                                           //double rCallCount1 = Math.Round(Convert.ToDouble((double)rc.TotalRecords * 100 / allRecTotalRecords), 2);
                    int count = (int)Math.Round(rCallCount);
                    //====== Next Page Size ======//
                    rc.PageSize = count;
                    //====== Next Page Size ======//
                    if (count == request.PageSize)
                        count = (int)allRecTotalRecords;//or rc.TotalRecords;
                    callResults.AddRange(rc.Calls.Take(count));
                }
                callResults = callResults.OrderByDescending(o => o.StartTime).ToList();
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    Calls = callResults,
                    TotalPages = allRecTotalPages,
                    TotalRecords = allRecTotalRecords,
                    RecorderCalls = recCalls
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "FindCallsInAllRecorders", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "FindCallsInAllRecorders", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public VRResponse GetCallInfoExportResults(VRRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetCallInfoExportResults", request.TenantId, "GetCallInfoExportResults function has been called successfully."));
                List<Recorder> recorders = request.Recorders;
                List<CallInfoExportResult> callInfoExportResults = new List<CallInfoExportResult>();

                // Get By Call Ids
                if (request.CallIds != null)
                {
                    foreach (var rec in recorders)
                    {
                        string csvCallIds = string.Format("{0}", string.Join(",", request.CallIds));
                        List<CallInfoExportResult> callInfoResults = new List<CallInfoExportResult>();
                        if (rec.IsPrimary)
                            callInfoResults = VoiceLoggingDALEC.GetCallInfoExportResultsByIds(rec, csvCallIds, request.Criteria.StartDate, request.Criteria.EndDate);
                        else
                        {
                            // Alternative approach
                            RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                            entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                            callInfoResults = entClient.GetCallInfoExportResultsByIds(rec, csvCallIds, request.Criteria.StartDate, request.Criteria.EndDate).ToList();
                        }
                        callInfoExportResults.AddRange(callInfoResults);
                        Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetCallInfoExportResults", request.TenantId, "Call data by id exported successfully from Recorder Id = " + rec.Id + " , Recorder Name = " + rec.Name + " Count = " + callInfoResults.Count));
                    }
                    return new VRResponse
                    {
                        Acknowledge = AcknowledgeType.Success,
                        CallExportResults = callInfoExportResults,
                    };
                }
                else
                {
                    var criteria = request.Criteria as CallCriteria;

                    int totalPages = 0;
                    long totalRecords = 0;

                    #region ------- Duration String -------
                    string durationStr = QueryConstants.DURATION_DEFAULT;
                    if (criteria.StartDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null) || criteria.EndDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null))
                        durationStr = QueryConstants.DURATION_BETWEEN + criteria.StartDuration.TotalMilliseconds.ToString() + "' AND '" + criteria.EndDuration.TotalMilliseconds.ToString() + "'";
                    #endregion


                    #region ------- Search Restriction -------
                    if (criteria.RestrictionInHours > 0)
                    {
                        var dt = DateTime.Now.AddHours(-criteria.RestrictionInHours);

                        criteria.StartDate = dt;
                        criteria.EndDate = DateTime.Now;
                        criteria.StartTime = dt.TimeOfDay;
                        criteria.EndTime = TimeSpan.Parse(DateTime.Now.TimeOfDay.ToString("HH:mm:ss"));
                    }
                    #endregion

                    string queryOptionSTR = "";

                    var recCalls = new List<RecorderCallInfo>();
                    foreach (var rec in recorders)
                    {
                        totalPages = 0;
                        totalRecords = 0;

                        if (criteria.RecorderCategoryGroupExtensions.Count == 0)
                        {
                            queryOptionSTR = buildCallSearchQueryForRecorder(criteria, rec.Id, rec.IsPrimary);
                            List<CallInfoExportResult> callExportResults = new List<CallInfoExportResult>();
                            if (rec.IsPrimary)
                                callExportResults = new VoiceLoggingDALEC(request.TenantId).GetCallInfoExportResults(request.PageSize, request.PageNumber,
                                    criteria.StartDate.DateAsStringWithoutSlash(), criteria.EndDate.DateAsStringWithoutSlash(),
                                    criteria.StartTime.ConvertTimeSpanToString(), criteria.EndTime.ConvertTimeSpanToString(),
                                    (criteria.SearchType == SearchType.Global ? true : false), durationStr, queryOptionSTR, rec);
                            else
                            {
                                // Alternative approach
                                RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                                entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                                string startDate = criteria.StartDate.DateAsStringWithoutSlash();
                                string endDate = criteria.EndDate.DateAsStringWithoutSlash();
                                string startTime = criteria.StartTime.ConvertTimeSpanToString();
                                string endTime = criteria.EndTime.ConvertTimeSpanToString();
                                bool isGlobalSearch = criteria.SearchType == SearchType.Global ? true : false;
                                bool isRandom = criteria.IsRandom;
                                bool isPercentage = criteria.IsPercentage;
                                //calls = entClient.SearchRecordedCalls(rec.SearchPageSize == 0 ? 1 : rec.SearchPageSize, request.PageNumber, startDate, endDate, startTime, endTime, isGlobalSearch, durationStr, queryOptionSTR, rec, out totalPages, out totalRecords).ToList();
                                callExportResults = entClient.GetCallInfoExportResults(request.PageSize, request.PageNumber, startDate, endDate, startTime, endTime, isGlobalSearch, durationStr, queryOptionSTR, rec).ToList();
                            }
                            recCalls.Add(new RecorderCallInfo { RecorderId = rec.Id, RecorderName = rec.Name, CallExportResults = callExportResults, TotalPages = totalPages, TotalRecords = totalRecords });
                            Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetCallInfoExportResults", request.TenantId, "Call data by current/all pages exported successfully from Recorder Id = " + rec.Id + " , Recorder Name = " + rec.Name + " Count = " + callExportResults.Count));
                        }
                        else
                        {
                            var recNodeSelect = criteria.RecorderCategoryGroupExtensions.FirstOrDefault(r => r.RecorderId == rec.Id);
                            if (recNodeSelect != null)
                            {
                                queryOptionSTR = buildCallSearchQueryForRecorder(criteria, rec.Id, rec.IsPrimary);
                                callInfoExportResults = new List<CallInfoExportResult>();
                                if (rec.IsPrimary)
                                    callInfoExportResults = new VoiceLoggingDALEC(request.TenantId).GetCallInfoExportResults(request.PageSize, request.PageNumber,
                                    criteria.StartDate.DateAsStringWithoutSlash(), criteria.EndDate.DateAsStringWithoutSlash(),
                                    criteria.StartTime.ConvertTimeSpanToString(), criteria.EndTime.ConvertTimeSpanToString(),
                                    (criteria.SearchType == SearchType.Global ? true : false), durationStr, queryOptionSTR, rec);
                                else
                                {
                                    // Alternative approach
                                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                                    string startDate = criteria.StartDate.DateAsStringWithoutSlash();
                                    string endDate = criteria.EndDate.DateAsStringWithoutSlash();
                                    string startTime = criteria.StartTime.ConvertTimeSpanToString();
                                    string endTime = criteria.EndTime.ConvertTimeSpanToString();
                                    bool isGlobalSearch = criteria.SearchType == SearchType.Global ? true : false;
                                    bool isRandom = criteria.IsRandom;
                                    bool isPercentage = criteria.IsPercentage;
                                    //calls = entClient.SearchRecordedCalls(rec.SearchPageSize == 0 ? 1 : rec.SearchPageSize, request.PageNumber, startDate, endDate, startTime, endTime, isGlobalSearch, durationStr, queryOptionSTR, rec, out totalPages, out totalRecords).ToList();
                                    callInfoExportResults = entClient.GetCallInfoExportResults(request.PageSize, request.PageNumber, startDate, endDate, startTime, endTime, isGlobalSearch, durationStr, queryOptionSTR, rec).ToList();
                                }
                                recCalls.Add(new RecorderCallInfo { RecorderId = rec.Id, RecorderName = rec.Name, CallExportResults = callInfoExportResults, TotalPages = totalPages, TotalRecords = totalRecords });
                                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetCallInfoExportResults", request.TenantId, "GetCallInfoExportResults(). Call data by current/all pages exported successfully from Recorder Id = " + rec.Id + " , Recorder Name = " + rec.Name + " Count = " + callInfoExportResults.Count));
                            }
                            else
                            {
                                recCalls.Add(new RecorderCallInfo { RecorderId = rec.Id, RecorderName = rec.Name, Calls = null, CallExportResults = null, TotalPages = totalPages, TotalRecords = totalRecords });
                            }
                        }
                    }


                    // Calls Processing
                    long allRecTotalRecords = recCalls.Sum(c => c.TotalRecords);
                    int allRecTotalPages = recCalls.Sum(c => c.TotalPages);
                    List<CallInfoExportResult> cInfoExportResults = new List<CallInfoExportResult>();
                    //foreach (var rc in recCalls.Where(w => w.Calls != null))
                    foreach (var rc in recCalls.Where(w => w.CallExportResults != null))
                    {
                        //double rCallCount = (double)(rc.TotalRecords * request.PageSize) / allRecTotalRecords;
                        //int count = (int)Math.Round(rCallCount);

                        //if (count == request.PageSize)
                        //    count = (int)allRecTotalRecords;
                        //callInfoExportResults.AddRange(rc.CallExportResults.Take(count));
                        cInfoExportResults.AddRange(rc.CallExportResults);
                    }
                    return new VRResponse
                    {
                        Acknowledge = AcknowledgeType.Success,
                        CallExportResults = cInfoExportResults,
                        TotalPages = allRecTotalPages,
                        TotalRecords = allRecTotalRecords,
                        RecorderCalls = recCalls
                    };
                }
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetCallInfoExportResults", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetCallInfoExportResults", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public VRResponse SearchRecordedCalls(VRRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "SearchRecordedCalls", request.TenantId, "SearchRecordedCalls function has been called successfully."));
                List<Recorder> recorders = request.Recorders;
                var criteria = request.Criteria as CallCriteria;
                int totalPages = 0;
                long totalRecords = 0;

                #region ------- Duration String -------
                string durationStr = QueryConstants.DURATION_DEFAULT;
                if (criteria.StartDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null) || criteria.EndDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null))
                    durationStr = QueryConstants.DURATION_BETWEEN + criteria.StartDuration.TotalMilliseconds.ToString() + "' AND '" + criteria.EndDuration.TotalMilliseconds.ToString() + "'";
                #endregion
                #region ------- Search Restriction -------
                if (criteria.RestrictionInHours > 0)
                {
                    var dt = DateTime.Now.AddHours(-criteria.RestrictionInHours);
                    criteria.StartDate = dt;
                    criteria.EndDate = DateTime.Now;
                    criteria.StartTime = dt.TimeOfDay;
                    criteria.EndTime = TimeSpan.Parse(DateTime.Now.ToString("HH:mm:ss"));
                }
                #endregion
                string queryOptionSTR = "";
                var recCalls = new List<RecorderCallInfo>();
                foreach (var rec in recorders)
                {
                    bool bExceptionThrown = false;
                    try
                    {
                        if (rec.Id == request.Criteria.RecId)
                        {
                            totalPages = 0;
                            totalRecords = 0;
                            if (criteria.RecorderCategoryGroupExtensions.Count == 0)
                            {
                                queryOptionSTR = buildCallSearchQueryForRecorder(criteria, rec.Id, rec.IsPrimary);
                                var calls = new List<CallInfo>();
                                if (rec.IsPrimary)
                                {
                                    if (!request.Criteria.IsRandom)
                                    {
                                        calls = VoiceLoggingDALEC.SearchCallsPrimaryDB(rec.SearchPageSize == 0 ? 1 : rec.SearchPageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria, rec);
                                    }
                                    else
                                    {
                                        calls = VoiceLoggingDALEC.SearchCallsPrimaryDB(rec.SearchPageSize == 0 ? 1 : rec.SearchPageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria, criteria.NoOfCalls, rec);
                                    }
                                }
                                else
                                {
                                    // Alternative approach
                                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                                    string startDate = criteria.StartDate.DateAsStringWithoutSlash();
                                    string endDate = criteria.EndDate.DateAsStringWithoutSlash();
                                    string startTime = criteria.StartTime.ConvertTimeSpanToString();
                                    string endTime = criteria.EndTime.ConvertTimeSpanToString();
                                    bool isGlobalSearch = criteria.SearchType == SearchType.Global ? true : false;
                                    bool isRandom = criteria.IsRandom;
                                    bool isPercentage = criteria.IsPercentage;
                                    if (!criteria.IsRandom)
                                    {
                                        calls = entClient.SearchRecordedCalls(rec.SearchPageSize == 0 ? 1 : rec.SearchPageSize, request.PageNumber, startDate, endDate, startTime, endTime, isGlobalSearch, durationStr, queryOptionSTR, rec, out totalPages, out totalRecords).ToList();
                                    }
                                    else
                                    {
                                        calls = entClient.SearchRandomCallsPrimaryDB(rec.SearchPageSize == 0 ? 1 : rec.SearchPageSize, request.PageNumber, startDate, endDate, startTime, endTime, isGlobalSearch, isRandom, isPercentage, durationStr, queryOptionSTR, criteria.NoOfCalls, rec, out totalPages, out totalRecords).ToList();
                                    }
                                }
                                recCalls.Add(new RecorderCallInfo { RecorderId = rec.Id, RecorderName = rec.Name, Calls = calls, TotalPages = totalPages, TotalRecords = totalRecords });
                            }
                            else
                            {
                                var recNodeSelect = criteria.RecorderCategoryGroupExtensions.FirstOrDefault(r => r.RecorderId == rec.Id);
                                if (recNodeSelect != null)
                                {
                                    queryOptionSTR = buildCallSearchQueryForRecorder(criteria, rec.Id, rec.IsPrimary);
                                    List<CallInfo> calls = new List<CallInfo>();
                                    if (rec.IsPrimary)
                                    {
                                        if (!criteria.IsRandom)
                                            calls = VoiceLoggingDALEC.SearchCallsPrimaryDB(rec.SearchPageSize == 0 ? 1 : rec.SearchPageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria, rec);
                                        else
                                            calls = VoiceLoggingDALEC.SearchCallsPrimaryDB(rec.SearchPageSize == 0 ? 1 : rec.SearchPageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria, criteria.NoOfCalls, rec);
                                    }
                                    else
                                    {
                                        // Alternative approach
                                        RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                                        entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                                        string startDate = criteria.StartDate.DateAsStringWithoutSlash();
                                        string endDate = criteria.EndDate.DateAsStringWithoutSlash();
                                        string startTime = criteria.StartTime.ConvertTimeSpanToString();
                                        string endTime = criteria.EndTime.ConvertTimeSpanToString();
                                        bool isGlobalSearch = criteria.SearchType == SearchType.Global ? true : false;
                                        bool isRandom = criteria.IsRandom;
                                        bool isPercentage = criteria.IsPercentage;
                                        if (!criteria.IsRandom)
                                        {
                                            calls = entClient.SearchRecordedCalls(rec.SearchPageSize == 0 ? 1 : rec.SearchPageSize, request.PageNumber, startDate, endDate, startTime, endTime, isGlobalSearch, durationStr, queryOptionSTR, rec, out totalPages, out totalRecords).ToList();
                                        }
                                        else
                                            calls = entClient.SearchRandomCallsPrimaryDB(rec.SearchPageSize == 0 ? 1 : rec.SearchPageSize, request.PageNumber, startDate, endDate, startTime, endTime, isGlobalSearch, isRandom, isPercentage, durationStr, queryOptionSTR, criteria.NoOfCalls, rec, out totalPages, out totalRecords).ToList();
                                    }
                                    recCalls.Add(new RecorderCallInfo { RecorderId = rec.Id, RecorderName = rec.Name, Calls = calls, TotalPages = totalPages, TotalRecords = totalRecords });
                                    Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "SearchRecordedCalls", request.TenantId, "SearchRecordedCalls(). Calls searched successfully from recorder. Recorder Id = " + rec.Id + " , Recorder Name = " + rec.Name));
                                }
                                else
                                {
                                    recCalls.Add(new RecorderCallInfo { RecorderId = rec.Id, RecorderName = rec.Name, Calls = null, TotalPages = totalPages, TotalRecords = totalRecords });
                                }
                            }
                            break;
                        }

                    }
                    catch (Exception ex)
                    {
                        bExceptionThrown = true;
                        Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "SearchRecordedCalls", request.TenantId, "An error has occurred while searching calls from Recorder Id = " + rec.Id + ", Recorder Name = " + rec.Name + " exception = " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                    }
                    if (bExceptionThrown)
                        continue;
                }
                // Calls Processing

                long allRecTotalRecords = recCalls.Sum(c => c.TotalRecords);
                int allRecTotalPages = recCalls.Sum(c => c.TotalPages);
                List<CallInfo> callResults = new List<CallInfo>();
                foreach (var rc in recCalls.Where(w => w.Calls != null))
                {
                    double rCallCount = (double)(rc.TotalRecords * request.PageSize) / allRecTotalRecords; //rc.TotalRecords / hcf;
                                                                                                           //double rCallCount1 = Math.Round(Convert.ToDouble((double)rc.TotalRecords * 100 / allRecTotalRecords), 2);
                    int count = (int)Math.Round(rCallCount);
                    //====== Next Page Size ======//
                    rc.PageSize = count;
                    //====== Next Page Size ======//
                    if (count == request.PageSize)
                        count = (int)allRecTotalRecords;//or rc.TotalRecords;
                    callResults.AddRange(rc.Calls.Take(count));
                }
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    Calls = callResults,
                    TotalPages = allRecTotalPages,
                    TotalRecords = allRecTotalRecords,
                    RecorderCalls = recCalls
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "SearchRecordedCalls", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "SearchRecordedCalls", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        private VRResponse searchCallsInAllRecordersForExport(VRRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "searchCallsInAllRecordersForExport", request.TenantId, "searchCallsInAllRecordersForExport function has been called successfully."));
                List<Recorder> recorders = request.Recorders;
                List<CallInfo> callResults = new List<CallInfo>();

                // Get By Call Ids
                if (request.CallIds != null)
                {
                    foreach (var rec in recorders)
                    {
                        string csvCallIds = string.Format("{0}", string.Join(",", request.CallIds));//.Select(i => i.Replace("'", "''"))));
                                                                                                    //if (!string.IsNullOrEmpty(csvCallIds) && csvCallIds.Length != 2)
                        List<CallInfo> calls = new List<CallInfo>();
                        if (rec.IsPrimary)
                            calls = VoiceLoggingDALEC.GetCallsByIds(rec, csvCallIds);
                        else
                        {
                            // Alternative approach
                            RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                            entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                            calls = entClient.GetCallsByIds(rec, csvCallIds).ToList();
                        }
                        callResults.AddRange(calls);
                    }
                    return new VRResponse
                    {
                        Acknowledge = AcknowledgeType.Success,
                        Calls = callResults,
                    };
                }
                else
                {
                    var criteria = request.Criteria as CallCriteria;

                    int totalPages = 0;
                    long totalRecords = 0;

                    //if (criteria.SearchType == SearchType.Global)
                    #region ------- Duration String -------

                    string durationStr = QueryConstants.DURATION_DEFAULT;
                    if (criteria.StartDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null) || criteria.EndDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null))
                        durationStr = QueryConstants.DURATION_BETWEEN + criteria.StartDuration.TotalMilliseconds.ToString() + "' AND '" + criteria.EndDuration.TotalMilliseconds.ToString() + "'";

                    #endregion


                    #region ------- Search Restriction -------

                    if (criteria.RestrictionInHours > 0)
                    {
                        var dt = DateTime.Now.AddHours(-criteria.RestrictionInHours);

                        criteria.StartDate = dt;
                        criteria.EndDate = DateTime.Now;
                        criteria.StartTime = dt.TimeOfDay;
                        criteria.EndTime = TimeSpan.Parse(DateTime.Now.TimeOfDay.ToString("HH:mm:ss"));
                    }

                    #endregion

                    string queryOptionSTR = "";

                    var recCalls = new List<RecorderCallInfo>();
                    foreach (var rec in recorders)
                    {
                        totalPages = 0;
                        totalRecords = 0;

                        if (criteria.RecorderCategoryGroupExtensions.Count == 0)
                        {
                            queryOptionSTR = buildCallSearchQueryForRecorder(criteria, rec.Id, rec.IsPrimary);
                            List<CallInfo> calls = new List<CallInfo>();
                            if (rec.IsPrimary)
                                calls = VoiceLoggingDALEC.SearchCallsPrimaryDB(request.PageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria, rec);
                            else
                            {
                                // Alternative approach
                                RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                                entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                                string startDate = criteria.StartDate.DateAsStringWithoutSlash();
                                string endDate = criteria.EndDate.DateAsStringWithoutSlash();
                                string startTime = criteria.StartTime.ConvertTimeSpanToString();
                                string endTime = criteria.EndTime.ConvertTimeSpanToString();
                                bool isGlobalSearch = criteria.SearchType == SearchType.Global ? true : false;
                                bool isRandom = criteria.IsRandom;
                                bool isPercentage = criteria.IsPercentage;
                                //calls = entClient.SearchRecordedCalls(rec.SearchPageSize == 0 ? 1 : rec.SearchPageSize, request.PageNumber, startDate, endDate, startTime, endTime, isGlobalSearch, durationStr, queryOptionSTR, rec, out totalPages, out totalRecords).ToList();
                                calls = entClient.SearchRecordedCalls(request.PageSize, request.PageNumber, startDate, endDate, startTime, endTime, isGlobalSearch, durationStr, queryOptionSTR, rec, out totalPages, out totalRecords).ToList();
                            }
                            recCalls.Add(new RecorderCallInfo { RecorderId = rec.Id, RecorderName = rec.Name, Calls = calls, TotalPages = totalPages, TotalRecords = totalRecords });
                        }
                        else
                        {
                            var recNodeSelect = criteria.RecorderCategoryGroupExtensions.FirstOrDefault(r => r.RecorderId == rec.Id);
                            if (recNodeSelect != null)
                            {
                                queryOptionSTR = buildCallSearchQueryForRecorder(criteria, rec.Id, rec.IsPrimary);
                                List<CallInfo> calls = new List<CallInfo>();
                                if (rec.IsPrimary)
                                    calls = VoiceLoggingDALEC.SearchCallsPrimaryDB(request.PageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria, rec);
                                else
                                {
                                    // Alternative approach
                                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                                    string startDate = criteria.StartDate.DateAsStringWithoutSlash();
                                    string endDate = criteria.EndDate.DateAsStringWithoutSlash();
                                    string startTime = criteria.StartTime.ConvertTimeSpanToString();
                                    string endTime = criteria.EndTime.ConvertTimeSpanToString();
                                    bool isGlobalSearch = criteria.SearchType == SearchType.Global ? true : false;
                                    bool isRandom = criteria.IsRandom;
                                    bool isPercentage = criteria.IsPercentage;
                                    //calls = entClient.SearchRecordedCalls(rec.SearchPageSize == 0 ? 1 : rec.SearchPageSize, request.PageNumber, startDate, endDate, startTime, endTime, isGlobalSearch, durationStr, queryOptionSTR, rec, out totalPages, out totalRecords).ToList();
                                    calls = entClient.SearchRecordedCalls(request.PageSize, request.PageNumber, startDate, endDate, startTime, endTime, isGlobalSearch, durationStr, queryOptionSTR, rec, out totalPages, out totalRecords).ToList();
                                }
                                recCalls.Add(new RecorderCallInfo { RecorderId = rec.Id, RecorderName = rec.Name, Calls = calls, TotalPages = totalPages, TotalRecords = totalRecords });
                            }
                            else
                            {
                                recCalls.Add(new RecorderCallInfo { RecorderId = rec.Id, RecorderName = rec.Name, Calls = null, TotalPages = totalPages, TotalRecords = totalRecords });
                            }
                        }
                    }


                    // Calls Processing
                    long allRecTotalRecords = recCalls.Sum(c => c.TotalRecords);
                    int allRecTotalPages = recCalls.Sum(c => c.TotalPages);

                    foreach (var rc in recCalls.Where(w => w.Calls != null))
                    {
                        double rCallCount = (double)(rc.TotalRecords * request.PageSize) / allRecTotalRecords;
                        int count = (int)Math.Round(rCallCount);

                        if (count == request.PageSize)
                            count = (int)allRecTotalRecords;//or rc.TotalRecords;
                        callResults.AddRange(rc.Calls.Take(count));
                    }
                    return new VRResponse
                    {
                        Acknowledge = AcknowledgeType.Success,
                        Calls = callResults,
                        TotalPages = allRecTotalPages,
                        TotalRecords = allRecTotalRecords,
                        RecorderCalls = recCalls
                    };
                }
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "searchCallsInAllRecordersForExport", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "searchCallsInAllRecordersForExport", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #endregion

        #region ------- Build Search Call Query -------


        private string buildCallSearchQuery(CallCriteria criteria)
        {
            StringBuilder sbWhereClause = new StringBuilder();

            //sbWhereClause.Append(" 1 = 1 ");

            #region ------- Advance Search -------
            long uniqueId;
            if (!string.IsNullOrEmpty(criteria.CustomText))
            {
                bool successfullyParsed = Int64.TryParse(criteria.CustomText, out uniqueId);
                // 
                sbWhereClause.Append(" AND ");
                sbWhereClause.Append(" ( ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Ext LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.GroupNum = (SELECT TOP 1 GroupNum FROM t_Group WHERE GroupName LIKE N'%" + criteria.CustomText + "%'))");
                sbWhereClause.Append(" OR ");
                sbWhereClause.Append(" (CI.RetainValue LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.CustName LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.ANI LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.ANI_PH LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.ANI_NAME LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.ANI_DETAILS LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag2 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag3 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag4 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag5 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag6 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag7 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag8 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag9 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag10 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag11 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag12 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag13 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag14 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag15 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag16 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                if (successfullyParsed)
                {
                    sbWhereClause.Append(" (CI.UniqueId = " + criteria.CustomText + ")");
                    sbWhereClause.Append(" OR ");
                    sbWhereClause.AppendLine();
                }
                sbWhereClause.Append(" (CI.CalledID LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.CallerID LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                /*if (criteria.CustomText.Length == 10)
                {
                    var callGUIDRange = AppSettingsUtil.GetString("callGUIDRange", "1,32").Split(',').Select(int.Parse).ToList();
                    sbWhereClause.Append(string.Format(" (SUBSTRING(CI.CallID, {0}, {1}) = N'" + criteria.CustomText + "')", callGUIDRange[0] + 1, callGUIDRange[1]));
                    sbWhereClause.Append(" OR ");
                    sbWhereClause.AppendLine();
                }*/

                sbWhereClause.Append(" ((SELECT COUNT(*) FROM t_RevRec_Interviews interview where CI.CallID = interview.InterviewId and Interviewee LIKE N'%" + criteria.CustomText + "%') > 0)");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();

                sbWhereClause.Append(" (CI.UserNum =(select TOP 1 UserNum from t_Account where UserName LIKE N'%" + criteria.CustomText + "%'))");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.CALL_COMMENT LIKE N'%" + criteria.CustomText + "%' )");
                sbWhereClause.Append(" OR ");
                sbWhereClause.Append(" ((select count(*) from t_BookMark BM where CI.CallID = BM.CallID and BM.bookmarktext LIKE N'%" + criteria.CustomText + "%' ) > 0)");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" OR ");
                sbWhereClause.Append(" ((select count(*) from vrCallTranscription T where CI.CallID = T.CallId and T.Transcription LIKE N'%" + criteria.CustomText + "%' ) > 0)");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" ) ");

                /*if (criteria.CustomText.Length == 10)
                {
                    sbWhereClause = new StringBuilder();

                    sbWhereClause.Append(" AND ");
                    sbWhereClause.Append(" ( ");
                    sbWhereClause.AppendLine();

                    sbWhereClause.Append(" (CI.ANI_PH LIKE N'%" + criteria.CustomText + "%')");
                    sbWhereClause.Append(" OR ");
                    sbWhereClause.AppendLine();
                    sbWhereClause.Append(" (CI.ANI_NAME LIKE N'%" + criteria.CustomText + "%')");
                    sbWhereClause.Append(" OR ");
                    sbWhereClause.AppendLine();

                    sbWhereClause.Append(" (CI.Tag3 LIKE N'%" + criteria.CustomText + "%')");
                    sbWhereClause.Append(" OR ");
                    sbWhereClause.AppendLine();
                    sbWhereClause.Append(" (CI.Tag4 LIKE N'%" + criteria.CustomText + "%')");
                    sbWhereClause.Append(" OR ");
                    sbWhereClause.AppendLine();

                    var callGUIDRange = AppSettingsUtil.GetString("callGUIDRange", "1,32").Split(',').Select(int.Parse).ToList();
                    sbWhereClause.Append(string.Format(" (SUBSTRING(CI.CallID, {0}, {1}) = N'" + criteria.CustomText + "')", callGUIDRange[0] + 1, callGUIDRange[1]));
                    sbWhereClause.Append(" OR ");
                    sbWhereClause.AppendLine();

                    sbWhereClause.Append(" (CI.UserNum =(select TOP 1 UserNum from t_Account where UserName LIKE N'%" + criteria.CustomText + "%'))");
                    sbWhereClause.Append(" OR ");
                    sbWhereClause.AppendLine();
                    sbWhereClause.Append(" (CI.CALL_COMMENT LIKE N'%" + criteria.CustomText + "%' )");
                    sbWhereClause.Append(" OR ");
                    sbWhereClause.Append(" ((select count(*) from t_BookMark BM where CI.CallID = BM.CallID and BM.bookmarktext LIKE N'%" + criteria.CustomText + "%' ) > 0)");
                    sbWhereClause.AppendLine();
                    sbWhereClause.Append(" ) ");
                }*/

            }

            if (criteria.RadioTalkGroup != null && criteria.RadioTalkGroup.Length > 0)
            {
                if (!criteria.RadioTalkGroup.Contains(';'))
                {
                    sbWhereClause.Append(" AND ");
                    sbWhereClause.Append(" ( ");
                    //sbWhereClause.Append(" (CI.Tag8 LIKE N'%" + criteria.RadioTalkGroup + "%')");
                    sbWhereClause.Append(" (CI.Tag6 LIKE N'%" + criteria.RadioTalkGroup + "%')");
                    sbWhereClause.Append(" ) ");

                }
                else if (criteria.RadioTalkGroup.Contains(';'))
                {
                    string[] RadioTalkGroupList = criteria.RadioTalkGroup.Split(';');
                    for (int i = 0; i < RadioTalkGroupList.Length; i++)
                    {
                        if (i == 0)
                        {
                            sbWhereClause.Append(" AND ");
                            sbWhereClause.Append(" ( ");
                            //sbWhereClause.Append(" (CI.Tag8 LIKE N'%" + RadioTalkGroupList[i] + "%')");
                            sbWhereClause.Append(" (CI.Tag6 LIKE N'%" + RadioTalkGroupList[i] + "%')");
                        }
                        else
                        {
                            sbWhereClause.Append(" OR ");
                            //sbWhereClause.Append(" (CI.Tag8 LIKE N'%" + RadioTalkGroupList[i] + "%')");
                            sbWhereClause.Append(" (CI.Tag6 LIKE N'%" + RadioTalkGroupList[i] + "%')");
                        }
                    }
                    sbWhereClause.Append(" ) ");
                }
            }

            if (criteria.IsNotAuditCallsRequest)
            {
                sbWhereClause.Append(" AND ");
                sbWhereClause.Append(" ( ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.CallId Not IN (SELECT CallId FROM vrCallAudit))");
                sbWhereClause.Append(" ) ");
            }
            #endregion

            if (criteria.IsGroupBasedSearchOnReport)
            {
                sbWhereClause.Append(" AND ");
                sbWhereClause.Append(" ( ");
                sbWhereClause.AppendLine();
                sbWhereClause.AppendFormat(" CI.GroupNum = {0} ", criteria.ReportGroupId);
                sbWhereClause.Append(" ) ");
            }

            #region ------- Group Extensions -------
            bool secCat = false;
            if (criteria.CategoryGroupExtensions != null && criteria.CategoryGroupExtensions.Count() > 0)
            {

                var audioNscreenCategoryGroupExtensions = criteria.CategoryGroupExtensions.Where(cge => cge.GroupType == GroupType.Audio || cge.GroupType == GroupType.Screens || cge.GroupType == GroupType.Text || cge.GroupType == GroupType.IQ3 || cge.GroupType == GroupType.Teams);
                if (audioNscreenCategoryGroupExtensions.Count() != 0)
                {
                    sbWhereClause.Append(" AND ( "); //1
                    sbWhereClause.AppendLine();

                    //foreach (var catGrpExt in criteria.CategoryGroupExtensions)
                    foreach (var catGrpExt in audioNscreenCategoryGroupExtensions)
                    {

                        if (secCat && catGrpExt.GroupExtensions.Count > 0)
                            sbWhereClause.Append(" OR ( "); //3
                        if (catGrpExt.GroupExtensions.Count > 0)
                        {
                            sbWhereClause.Append(" ( ");//2

                            foreach (var grpExt in catGrpExt.GroupExtensions)
                            {
                                //sbWhereClause.AppendFormat(@"({0}.GroupNum = {1} or {0}.Ext IN ({2}))", "CI", grpExt.GroupId, grpExt.ExtensionIdsCSV);

                                sbWhereClause.AppendFormat(@"({0}.Ext IN ({2}))", "CI", grpExt.GroupId, grpExt.ExtensionIdsCSV);
                                sbWhereClause.Append(" OR ");
                                sbWhereClause.AppendLine();
                            }
                            sbWhereClause.RemoveLast(" OR ");
                            sbWhereClause.Append(" ) ");//2
                            sbWhereClause.AppendLine();

                            sbWhereClause.AppendFormat(" AND CI.CallType = {0} ", (int)catGrpExt.GroupType);//Call Type
                            sbWhereClause.AppendLine();
                            //sbWhereClause.Append(" ) ");//2
                        }
                        if (secCat && catGrpExt.GroupExtensions.Count > 0)
                            sbWhereClause.Append(" ) "); //3
                        if (catGrpExt.GroupExtensions.Count > 0)
                            secCat = true;
                    }
                    // Added for IQ3

                    if (sbWhereClause.Equals("{ AND (  OR (  )  OR (  ) }"))
                    {
                        sbWhereClause.Replace("{ AND (  OR (  )  OR (  ) }", "");
                        sbWhereClause.Append("AND CI.CallType = 7 ");
                    }
                    else
                    {
                        //  sbWhereClause.Append("or CI.CallType = 7 ");
                    }

                    sbWhereClause.Append(" ) "); //1
                }
            }

            #endregion



            #region Commented Code
            //if (criteria.CategoryGroupExtensions != null && criteria.CategoryGroupExtensions.Count() > 0)
            //{
            //    foreach (var catGrpExt in criteria.CategoryGroupExtensions)
            //    {
            //        if (catGrpExt.GroupExtensions.Count > 0)
            //        {
            //            sbWhereClause.Append(" AND ( "); //1
            //            sbWhereClause.Append(" ( ");//2
            //            foreach (var grpExt in catGrpExt.GroupExtensions)
            //            {
            //                sbWhereClause.AppendFormat(@"({0}.GroupNum = {1} AND {0}.Ext IN ({2}))", "CI", grpExt.GroupId, grpExt.ExtensionIdsCSV);
            //                sbWhereClause.Append(" OR ");
            //            }
            //            sbWhereClause.RemoveLast(" OR ");
            //            sbWhereClause.Append(" ) ");//2
            //            sbWhereClause.AppendFormat(" AND CI.CallType = {0} ", (int)catGrpExt.GroupType);//Type
            //            sbWhereClause.Append(" ) "); //1
            //        }
            //    }
            //}
            #endregion

            if (criteria.IsCustomDataSearch)
            {
                switch (criteria.CustomSearchType)
                {
                    case CustomSearchType.InspectionTemplate:
                        sbWhereClause.Append(" AND ( insp.InspectionTemplateId = " + criteria.InspectionTemplateId + " ) ");
                        criteria.JOINString = @" INNER JOIN iq3Inspection insp on insp.EventId = CI.CallId ";
                        break;
                    case CustomSearchType.BookmarkText:
                        sbWhereClause.Append(" AND ( im.MarkerAnswer LIKE N'%" + criteria.BookmarkText + "%' ) ");
                        criteria.JOINString = @" INNER JOIN  iq3InspectionMarker im ON im.EventId =  CI.CallId ";
                        break;
                    case CustomSearchType.BookmarkNote:
                        sbWhereClause.Append(" AND ( im.Note LIKE N'%" + criteria.BookmarkNotes + "%' ) ");
                        criteria.JOINString = @" INNER JOIN  iq3InspectionMarker im ON im.EventId =  CI.CallId ";
                        break;
                    case CustomSearchType.PreInspectionTitle:
                        sbWhereClause.Append(" AND ( pid.PreInspectionTitle LIKE N'%" + criteria.PreInspectionTitle + "%' ) ");
                        criteria.JOINString = @" INNER JOIN iq3PreInspectionData pid ON pid.EventId = CI.CallId ";
                        break;
                    case CustomSearchType.PreInspectionData:
                        sbWhereClause.Append(" AND ( pid.PreInspectionText LIKE N'%" + criteria.PreInspectionData + "%' ) ");
                        criteria.JOINString = @" INNER JOIN iq3PreInspectionData pid ON pid.EventId = CI.CallId ";
                        break;
                    default:
                        break;
                }
            }
            sbWhereClause.Append(" and ci.[IsShow] = '1' ");
            System.Diagnostics.Debug.WriteLine(sbWhereClause.ToString());
            //if (string.IsNullOrEmpty(sbWhereClause.ToString()) || sbWhereClause.ToString() == " AND ( \r\n ) ")
            //    return " AND ( CI.Ext=-999 ) ";
            return sbWhereClause.ToString();
        }
        #region ---------- T1 Extension Filtering ----------------
        /*
         * Module : T1 Extension Filtering
         * Author : Arivu
         * Description : This method is used to get calls on page load  to display at IR-Lite.
         */
        public string TagRulebuildCallSearchQuery(CallCriteria criteria, string TagRuleUser, VRRequest request)
        {
            StringBuilder sbWhereClause = new StringBuilder();

            #region ------- Advance Search -------
            if (!string.IsNullOrEmpty(criteria.CustomText))
            {
                sbWhereClause.Append(" AND ");
                sbWhereClause.Append(" ( ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Ext LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.RetainValue LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.CustName LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.ANI LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.ANI_PH LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.ANI_NAME LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag2 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag3 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag4 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.CalledID LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();

                sbWhereClause.Append(" ((SELECT COUNT(*) FROM t_RevRec_Interviews interview where CI.CallID = interview.InterviewId and Interviewee LIKE N'%" + criteria.CustomText + "%') > 0)");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();

                sbWhereClause.Append(" (CI.CALL_COMMENT LIKE N'%" + criteria.CustomText + "%' )");
                sbWhereClause.Append(" OR ");
                sbWhereClause.Append(" ((select count(*) from t_BookMark BM where CI.CallID = BM.CallID and BM.bookmarktext LIKE N'%" + criteria.CustomText + "%' ) > 0)");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" OR ");
                sbWhereClause.Append(" ((select count(*) from vrCallTranscription T where CI.CallID = T.CallId and T.Transcription LIKE N'%" + criteria.CustomText + "%' ) > 0)");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" ) ");


            }

            if (criteria.RadioTalkGroup != null && criteria.RadioTalkGroup.Length > 0)
            {
                if (!criteria.RadioTalkGroup.Contains(';'))
                {
                    sbWhereClause.Append(" AND ");
                    sbWhereClause.Append(" ( ");
                    //sbWhereClause.Append(" (CI.Tag8 LIKE N'%" + criteria.RadioTalkGroup + "%')");
                    sbWhereClause.Append(" (CI.Tag6 LIKE N'%" + criteria.RadioTalkGroup + "%')");
                    sbWhereClause.Append(" ) ");

                }
                else if (criteria.RadioTalkGroup.Contains(';'))
                {
                    string[] RadioTalkGroupList = criteria.RadioTalkGroup.Split(';');
                    for (int i = 0; i < RadioTalkGroupList.Length; i++)
                    {
                        if (i == 0)
                        {
                            sbWhereClause.Append(" AND ");
                            sbWhereClause.Append(" ( ");
                            //sbWhereClause.Append(" (CI.Tag8 LIKE N'%" + RadioTalkGroupList[i] + "%')");
                            sbWhereClause.Append(" (CI.Tag6 LIKE N'%" + RadioTalkGroupList[i] + "%')");
                        }
                        else
                        {
                            sbWhereClause.Append(" OR ");
                            //sbWhereClause.Append(" (CI.Tag8 LIKE N'%" + RadioTalkGroupList[i] + "%')");
                            sbWhereClause.Append(" (CI.Tag6 LIKE N'%" + RadioTalkGroupList[i] + "%')");
                        }
                    }
                    sbWhereClause.Append(" ) ");
                }
            }

            #endregion

            #region ------- Group Extensions -------
            string s_channel_list = "";
            bool secCat = false;
            if (criteria.CategoryGroupExtensions != null && criteria.CategoryGroupExtensions.Count() > 0)
            {
                var audioNscreenCategoryGroupExtensions = criteria.CategoryGroupExtensions.Where(cge => cge.GroupType == GroupType.Audio || cge.GroupType == GroupType.Screens || cge.GroupType == GroupType.IQ3 || cge.GroupType == GroupType.Text || cge.GroupType == GroupType.Teams);
                if (audioNscreenCategoryGroupExtensions.Count() != 0)
                {
                    sbWhereClause.Append(" AND ( "); //1
                    sbWhereClause.AppendLine();


                    foreach (var catGrpExt in audioNscreenCategoryGroupExtensions)
                    {
                        if (secCat)
                            sbWhereClause.Append(" OR ( "); //3
                        if (catGrpExt.GroupExtensions.Count > 0)
                        {
                            sbWhereClause.Append(" ( ");//2

                            foreach (var grpExt in catGrpExt.GroupExtensions)
                            {
                                s_channel_list = s_channel_list + "," + grpExt.ExtensionIdsCSV;
                                sbWhereClause.AppendFormat(@"({0}.GroupNum = {1} or {0}.Ext IN ({2}))", "CI", grpExt.GroupId, grpExt.ExtensionIdsCSV);
                                sbWhereClause.Append(" OR ");
                                sbWhereClause.AppendLine();
                            }
                            sbWhereClause.RemoveLast(" OR ");
                            sbWhereClause.Append(" ) ");//2
                            sbWhereClause.AppendLine();


                            sbWhereClause.AppendFormat(" AND CI.CallType = {0} ", (int)catGrpExt.GroupType);//Call Type
                            sbWhereClause.AppendLine();

                        }
                        if (secCat)
                            sbWhereClause.Append(" ) "); //3
                        secCat = true;
                    }
                    sbWhereClause.Append(" ) "); //1
                }
            }

            #endregion

            #region Commented Code
            //if (criteria.CategoryGroupExtensions != null && criteria.CategoryGroupExtensions.Count() > 0)
            //{
            //    foreach (var catGrpExt in criteria.CategoryGroupExtensions)
            //    {
            //        if (catGrpExt.GroupExtensions.Count > 0)
            //        {
            //            sbWhereClause.Append(" AND ( "); //1
            //            sbWhereClause.Append(" ( ");//2
            //            foreach (var grpExt in catGrpExt.GroupExtensions)
            //            {
            //                sbWhereClause.AppendFormat(@"({0}.GroupNum = {1} AND {0}.Ext IN ({2}))", "CI", grpExt.GroupId, grpExt.ExtensionIdsCSV);
            //                sbWhereClause.Append(" OR ");
            //            }
            //            sbWhereClause.RemoveLast(" OR ");
            //            sbWhereClause.Append(" ) ");//2
            //            sbWhereClause.AppendFormat(" AND CI.CallType = {0} ", (int)catGrpExt.GroupType);//Type
            //            sbWhereClause.Append(" ) "); //1
            //        }
            //    }
            //}
            #endregion

            if (TagRuleUser != null && TagRuleUser.Trim().Length > 1 && !TagRuleUser.Contains(','))
            {
                sbWhereClause.Append(" and");
                sbWhereClause.Append(" ( ");
                sbWhereClause.Append(" ( CI.TagRule like '%" + TagRuleUser + "%' ) ");
            }
            else if (TagRuleUser != null && TagRuleUser.Trim().Length > 1 && TagRuleUser.Contains(','))
            {
                sbWhereClause.Append(" and ");
                sbWhereClause.Append(" ( ");
                string[] tagsplit = TagRuleUser.Split(',');
                for (int i = 0; i < tagsplit.Length; i++)
                {
                    if (i == 0 && tagsplit[i].Trim().Length > 0)
                    {
                        sbWhereClause.Append("  (CI.TagRule like '%" + tagsplit[i] + "%'");
                    }
                    else if (tagsplit[i].Trim().Length > 0)
                    {
                        sbWhereClause.Append(" OR CI.TagRule like '%" + tagsplit[i] + "%' ");
                    }
                }
                sbWhereClause.Append(" ) ");

            }

            String[] s_list_of_channels = s_channel_list.Split(',');

            StringBuilder sbWhereClause_list = new StringBuilder();

            for (int i = 0; i < s_list_of_channels.Length; i++)
            {
                if (s_list_of_channels[i].Trim().Length > 0 && Int16.Parse(s_list_of_channels[i]) > Convert.ToInt16(request.MaxT1Ch))
                {
                    sbWhereClause_list.Append(" or ci.ext = '" + s_list_of_channels[i].Trim() + "'");
                }
            }

            StringBuilder sbWhereClause_1 = new StringBuilder();

            if (sbWhereClause.ToString().Trim().Length > 20)
            {
                sbWhereClause_1.Append(sbWhereClause);
                sbWhereClause_1.Append(sbWhereClause_list);
                sbWhereClause_1.Append(" ) ");
                sbWhereClause_1.Append(" and ci.[IsShow] = '1' ");
            }
            else
            {
                sbWhereClause_1.Clear();
                if (s_channel_list.Trim().Length > 1)
                {
                    sbWhereClause_1.Append(sbWhereClause_list);
                }
                sbWhereClause_1.Append(" and ci.[IsShow] = '1' ");

            }


            System.Diagnostics.Debug.WriteLine(sbWhereClause_1.ToString());
            //if (string.IsNullOrEmpty(sbWhereClause.ToString()) || sbWhereClause.ToString() == " AND ( \r\n ) ")
            //    return " AND ( CI.Ext=-999 ) ";
            return sbWhereClause_1.ToString();
        }

        #endregion ---------- T1 Extension Filtering ----------------
        private string buildCallSearchQueryLite(CallCriteria criteria)
        {
            StringBuilder sbWhereClause = new StringBuilder();

            #region ------- Advance Search -------

            long uniqueId;
            if (!string.IsNullOrEmpty(criteria.CustomText))
            {
                bool successfullyParsed = Int64.TryParse(criteria.CustomText, out uniqueId);
                // 
                sbWhereClause.Append(" AND ");
                sbWhereClause.Append(" ( ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Ext LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.GroupNum = (SELECT TOP 1 GroupNum FROM t_Group WHERE GroupName LIKE N'%" + criteria.CustomText + "%'))");
                sbWhereClause.Append(" OR ");
                sbWhereClause.Append(" (CI.RetainValue LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.CustName LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.ANI LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.ANI_PH LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.ANI_NAME LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.ANI_DETAILS LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag2 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag3 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag4 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag5 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag6 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag7 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag8 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag9 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag10 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag11 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag12 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag13 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag14 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag15 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag16 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                if (successfullyParsed)
                {
                    sbWhereClause.Append(" (CI.UniqueId = " + criteria.CustomText + ")");
                    sbWhereClause.Append(" OR ");
                    sbWhereClause.AppendLine();
                }
                sbWhereClause.Append(" (CI.CalledID LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.CallerID LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();

                sbWhereClause.Append(" (CI.UserNum =(select TOP 1 UserNum from t_Account where UserName LIKE N'%" + criteria.CustomText + "%'))");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.CALL_COMMENT LIKE N'%" + criteria.CustomText + "%' )");
                sbWhereClause.Append(" OR ");
                sbWhereClause.Append(" ((select count(*) from t_BookMark BM where CI.CallID = BM.CallID and BM.bookmarktext LIKE N'%" + criteria.CustomText + "%' ) > 0)");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" OR ");
                sbWhereClause.Append(" ((select count(*) from vrCallTranscription T where CI.CallID = T.CallId and T.Transcription LIKE N'%" + criteria.CustomText + "%' ) > 0)");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" ) ");
            }

            if (criteria.RadioTalkGroup != null && criteria.RadioTalkGroup.Length > 0)
            {
                if (!criteria.RadioTalkGroup.Contains(';'))
                {
                    sbWhereClause.Append(" AND ");
                    sbWhereClause.Append(" ( ");
                    //sbWhereClause.Append(" (CI.Tag8 LIKE N'%" + criteria.RadioTalkGroup + "%')");
                    sbWhereClause.Append(" (CI.Tag6 LIKE N'%" + criteria.RadioTalkGroup + "%')");
                    sbWhereClause.Append(" ) ");

                }
                else if (criteria.RadioTalkGroup.Contains(';'))
                {
                    string[] RadioTalkGroupList = criteria.RadioTalkGroup.Split(';');
                    for (int i = 0; i < RadioTalkGroupList.Length; i++)
                    {
                        if (i == 0)
                        {
                            sbWhereClause.Append(" AND ");
                            sbWhereClause.Append(" ( ");
                            //sbWhereClause.Append(" (CI.Tag8 LIKE N'%" + RadioTalkGroupList[i] + "%')");
                            sbWhereClause.Append(" (CI.Tag6 LIKE N'%" + RadioTalkGroupList[i] + "%')");
                        }
                        else
                        {
                            sbWhereClause.Append(" OR ");
                            //sbWhereClause.Append(" (CI.Tag8 LIKE N'%" + RadioTalkGroupList[i] + "%')");
                            sbWhereClause.Append(" (CI.Tag6 LIKE N'%" + RadioTalkGroupList[i] + "%')");
                        }
                    }
                    sbWhereClause.Append(" ) ");
                }
            }

            #endregion

            #region ------- Group Extensions -------
            bool secCat = false;
            if (criteria.CategoryGroupExtensions != null && criteria.CategoryGroupExtensions.Count() > 0)
            {

                var audioNscreenCategoryGroupExtensions = criteria.CategoryGroupExtensions.Where(cge => cge.GroupType == GroupType.Audio || cge.GroupType == GroupType.Screens || cge.GroupType == GroupType.Text || cge.GroupType == GroupType.IQ3 || cge.GroupType == GroupType.Teams);
                if (audioNscreenCategoryGroupExtensions.Count() != 0)
                {
                    sbWhereClause.Append(" AND ( "); //1
                    sbWhereClause.AppendLine();

                    //foreach (var catGrpExt in criteria.CategoryGroupExtensions)
                    foreach (var catGrpExt in audioNscreenCategoryGroupExtensions)
                    {

                        if (secCat && catGrpExt.GroupExtensions.Count > 0)
                            sbWhereClause.Append(" OR ( "); //3
                        if (catGrpExt.GroupExtensions.Count > 0)
                        {
                            sbWhereClause.Append(" ( ");//2


                            foreach (var grpExt in catGrpExt.GroupExtensions)
                            {
                                //sbWhereClause.AppendFormat(@"({0}.GroupNum = {1} or {0}.Ext IN ({2}))", "CI", grpExt.GroupId, grpExt.ExtensionIdsCSV);
                                sbWhereClause.AppendFormat(@"({0}.Ext IN ({2}))", "CI", grpExt.GroupId, grpExt.ExtensionIdsCSV);
                                sbWhereClause.Append(" OR ");
                                sbWhereClause.AppendLine();
                            }
                            sbWhereClause.RemoveLast(" OR ");
                            sbWhereClause.Append(" ) ");//2
                            sbWhereClause.AppendLine();


                            sbWhereClause.AppendFormat(" AND CI.CallType = {0} ", (int)catGrpExt.GroupType);//Call Type
                            sbWhereClause.AppendLine();
                            //sbWhereClause.Append(" ) ");//2
                        }
                        if (secCat && catGrpExt.GroupExtensions.Count > 0)
                            sbWhereClause.Append(" ) "); //3
                        if (catGrpExt.GroupExtensions.Count > 0)
                            secCat = true;
                    }
                    // Added for IQ3

                    if (sbWhereClause.Equals("{ AND (  OR (  )  OR (  ) }"))
                    {
                        sbWhereClause.Replace("{ AND (  OR (  )  OR (  ) }", "");
                        sbWhereClause.Append("AND CI.CallType = 7 ");
                    }
                    else
                    {
                        //  sbWhereClause.Append("or CI.CallType = 7 ");
                    }

                    sbWhereClause.Append(" ) "); //1
                }
            }

            #endregion

            sbWhereClause.Append(" and ci.[IsShow] = '1' ");
            System.Diagnostics.Debug.WriteLine(sbWhereClause.ToString());
            return sbWhereClause.ToString();
        }

        private string buildCallSearchQueryForExportData(CallCriteria criteria)
        {
            StringBuilder sbWhereClause = new StringBuilder();
            #region ------- Advance Search -------
            long uniqueId;
            if (!string.IsNullOrEmpty(criteria.CustomText))
            {
                bool successfullyParsed = Int64.TryParse(criteria.CustomText, out uniqueId);
                sbWhereClause.Append(" AND ");
                sbWhereClause.Append(" ( ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Ext LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.GroupNum = (SELECT TOP 1 GroupNum FROM t_Group WHERE GroupName LIKE N'%" + criteria.CustomText + "%'))");
                sbWhereClause.Append(" OR ");
                sbWhereClause.Append(" (CI.RetainValue LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.CustName LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.ANI LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.ANI_PH LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.ANI_NAME LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.ANI_DETAILS LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag2 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag3 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag4 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag5 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag6 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag7 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag8 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag9 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag10 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag11 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag12 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag13 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag14 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag15 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag16 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                if (successfullyParsed)
                {
                    sbWhereClause.Append(" (CI.UniqueId = " + criteria.CustomText + ")");
                    sbWhereClause.Append(" OR ");
                    sbWhereClause.AppendLine();
                }
                sbWhereClause.Append(" (CI.CalledID LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.CallerID LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();

                sbWhereClause.Append(" (CI.UserNum =(select TOP 1 UserNum from t_Account where UserName LIKE N'%" + criteria.CustomText + "%'))");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.CALL_COMMENT LIKE N'%" + criteria.CustomText + "%' )");
                sbWhereClause.Append(" OR ");
                sbWhereClause.Append(" ((select count(*) from t_BookMark BM where CI.CallID = BM.CallID and BM.bookmarktext LIKE N'%" + criteria.CustomText + "%' ) > 0)");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" ) ");
            }
            if (criteria.RadioTalkGroup != null && criteria.RadioTalkGroup.Length > 0)
            {
                if (!criteria.RadioTalkGroup.Contains(';'))
                {
                    sbWhereClause.Append(" AND ");
                    sbWhereClause.Append(" ( ");
                    sbWhereClause.Append(" (CI.Tag6 LIKE N'%" + criteria.RadioTalkGroup + "%')");
                    sbWhereClause.Append(" ) ");

                }
                else if (criteria.RadioTalkGroup.Contains(';'))
                {
                    string[] RadioTalkGroupList = criteria.RadioTalkGroup.Split(';');
                    for (int i = 0; i < RadioTalkGroupList.Length; i++)
                    {
                        if (i == 0)
                        {
                            sbWhereClause.Append(" AND ");
                            sbWhereClause.Append(" ( ");
                            sbWhereClause.Append(" (CI.Tag6 LIKE N'%" + RadioTalkGroupList[i] + "%')");
                        }
                        else
                        {
                            sbWhereClause.Append(" OR ");
                            //sbWhereClause.Append(" (CI.Tag8 LIKE N'%" + RadioTalkGroupList[i] + "%')");
                            sbWhereClause.Append(" (CI.Tag6 LIKE N'%" + RadioTalkGroupList[i] + "%')");
                        }
                    }
                    sbWhereClause.Append(" ) ");
                }
            }

            #endregion

            #region ------- Group Extensions -------
            bool secCat = false;
            if (criteria.CategoryGroupExtensions != null && criteria.CategoryGroupExtensions.Count() > 0)
            {
                var audioNscreenCategoryGroupExtensions = criteria.CategoryGroupExtensions.Where(cge => cge.GroupType == GroupType.Audio || cge.GroupType == GroupType.Screens || cge.GroupType == GroupType.Text || cge.GroupType == GroupType.IQ3 || cge.GroupType == GroupType.Teams);
                if (audioNscreenCategoryGroupExtensions.Count() != 0)
                {
                    sbWhereClause.Append(" AND ( ");
                    sbWhereClause.AppendLine();

                    foreach (var catGrpExt in audioNscreenCategoryGroupExtensions)
                    {
                        if (secCat && catGrpExt.GroupExtensions.Count > 0)
                            sbWhereClause.Append(" OR ( "); //3
                        if (catGrpExt.GroupExtensions.Count > 0)
                        {
                            sbWhereClause.Append(" ( ");
                            foreach (var grpExt in catGrpExt.GroupExtensions)
                            {
                                sbWhereClause.AppendFormat(@"({0}.Ext IN ({2}))", "CI", grpExt.GroupId, grpExt.ExtensionIdsCSV);
                                sbWhereClause.Append(" OR ");
                                sbWhereClause.AppendLine();
                            }
                            sbWhereClause.RemoveLast(" OR ");
                            sbWhereClause.Append(" ) ");//2
                            sbWhereClause.AppendLine();
                            sbWhereClause.AppendFormat(" AND CI.CallType = {0} ", (int)catGrpExt.GroupType);//Call Type
                            sbWhereClause.AppendLine();
                        }
                        if (secCat && catGrpExt.GroupExtensions.Count > 0)
                            sbWhereClause.Append(" ) "); //3
                        if (catGrpExt.GroupExtensions.Count > 0)
                            secCat = true;
                    }
                    if (sbWhereClause.Equals("{ AND (  OR (  )  OR (  ) }"))
                    {
                        sbWhereClause.Replace("{ AND (  OR (  )  OR (  ) }", "");
                        sbWhereClause.Append("AND CI.CallType = 7 ");
                    }
                    sbWhereClause.Append(" ) ");
                }
            }

            #endregion
            sbWhereClause.Append(" and ci.[IsShow] = '1' ");
            System.Diagnostics.Debug.WriteLine(sbWhereClause.ToString());
            return sbWhereClause.ToString();
        }


        #endregion

        #region ------- Build Search Call Query for EC -------

        private string buildCallSearchQueryForRecorder(CallCriteria criteria, int recId, bool isRecPrimary)
        {
            StringBuilder sbWhereClause = new StringBuilder();
            //sbWhereClause.Append(" 1 = 1 ");

            #region ------- Advance Search -------
            long uniqueId;
            if (!string.IsNullOrEmpty(criteria.CustomText))
            {
                bool successfullyParsed = Int64.TryParse(criteria.CustomText, out uniqueId);
                sbWhereClause.Append(" AND ");
                sbWhereClause.Append(" ( ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Ext LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                //sbWhereClause.Append(" (grp.GroupName LIKE N'%" + dataToSearch + "%')");
                //sbWhereClause.Append(" OR ");
                sbWhereClause.Append(" (CI.RetainValue LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.CustName LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.ANI LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.ANI_PH LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.ANI_NAME LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.ANI_DETAILS LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag2 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag3 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag4 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag5 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag6 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag7 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag8 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag9 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag10 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag11 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag12 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag13 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag14 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag15 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" (CI.Tag16 LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();
                if (successfullyParsed)
                {
                    sbWhereClause.Append(" (CI.UniqueId = " + criteria.CustomText + ")");
                    sbWhereClause.Append(" OR ");
                    sbWhereClause.AppendLine();
                }
                sbWhereClause.Append(" (CI.CalledID LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();

                sbWhereClause.Append(" ((SELECT COUNT(*) FROM t_RevRec_Interviews interview where CI.CallID = interview.InterviewId and Interviewee LIKE N'%" + criteria.CustomText + "%') > 0)");
                sbWhereClause.Append(" OR ");
                sbWhereClause.AppendLine();

                sbWhereClause.Append(" (CI.CALL_COMMENT LIKE N'%" + criteria.CustomText + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.Append(" ((select count(*) from t_BookMark BM where CI.CallID = BM.CallID and BM.bookmarktext LIKE N'%" + criteria.CustomText + "%' ) > 0)");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" OR ");
                sbWhereClause.Append(" ((select count(*) from vrCallTranscription T where CI.CallID = T.CallId and T.Transcription LIKE N'%" + criteria.CustomText + "%' ) > 0)");
                sbWhereClause.AppendLine();
                sbWhereClause.Append(" ) ");
            }

            if (criteria.RadioTalkGroup != null && criteria.RadioTalkGroup.Length > 0 && isRecPrimary)
            {
                if (!criteria.RadioTalkGroup.Contains(';'))
                {
                    sbWhereClause.Append(" AND ");
                    sbWhereClause.Append(" ( ");
                    //sbWhereClause.Append(" (CI.Tag8 LIKE N'%" + criteria.RadioTalkGroup + "%')");
                    sbWhereClause.Append(" (CI.Tag6 LIKE N'%" + criteria.RadioTalkGroup + "%')");
                    sbWhereClause.Append(" ) ");

                }
                else if (criteria.RadioTalkGroup.Contains(';'))
                {
                    string[] RadioTalkGroupList = criteria.RadioTalkGroup.Split(';');
                    for (int i = 0; i < RadioTalkGroupList.Length; i++)
                    {
                        if (i == 0)
                        {
                            sbWhereClause.Append(" AND ");
                            sbWhereClause.Append(" ( ");
                            //sbWhereClause.Append(" (CI.Tag8 LIKE N'%" + RadioTalkGroupList[i] + "%')");
                            sbWhereClause.Append(" (CI.Tag6 LIKE N'%" + RadioTalkGroupList[i] + "%')");
                        }
                        else
                        {
                            sbWhereClause.Append(" OR ");
                            //sbWhereClause.Append(" (CI.Tag8 LIKE N'%" + RadioTalkGroupList[i] + "%')");
                            sbWhereClause.Append(" (CI.Tag6 LIKE N'%" + RadioTalkGroupList[i] + "%')");
                        }
                    }
                    sbWhereClause.Append(" ) ");
                }
            }

            #endregion

            //#region ------- Group Extensions -------
            //bool secCat = false;
            //if (criteria.RecorderCategoryGroupExtensions != null && criteria.RecorderCategoryGroupExtensions.Count() > 0)
            //{
            //    var recResult = criteria.RecorderCategoryGroupExtensions.FirstOrDefault(r => r.RecorderId == recId);
            //    if (recResult != null)
            //    {
            //        var recCatGrpExt = recResult.CategoryGroupExtensions;
            //        sbWhereClause.Append(" AND ( "); //1
            //        sbWhereClause.AppendLine();
            //        foreach (var catGrpExt in recCatGrpExt)
            //        {
            //            if (secCat && catGrpExt.GroupExtensions.Count > 0)
            //                sbWhereClause.Append(" OR ( "); //3
            //            if (catGrpExt.GroupExtensions.Count > 0)
            //            {
            //                sbWhereClause.Append(" ( ");//2
            //                foreach (var grpExt in catGrpExt.GroupExtensions)
            //                {
            //                    sbWhereClause.AppendFormat(@"({0}.GroupNum = {1} AND {0}.Ext IN ({2}))", "CI", grpExt.GroupId, grpExt.ExtensionIdsCSV);
            //                    sbWhereClause.Append(" OR ");
            //                    sbWhereClause.AppendLine();
            //                }
            //                sbWhereClause.RemoveLast(" OR ");
            //                sbWhereClause.Append(" ) ");//2
            //                sbWhereClause.AppendLine();

            //                sbWhereClause.AppendFormat(" AND CI.CallType = {0} ", (int)catGrpExt.GroupType);//Call Type
            //                sbWhereClause.AppendLine();
            //                //sbWhereClause.Append(" ) ");//2
            //            }
            //            if (secCat && catGrpExt.GroupExtensions.Count > 0)
            //                sbWhereClause.Append(" ) "); //3
            //            secCat = true;
            //        }
            //        sbWhereClause.Append(" ) "); //1
            //    }
            //}

            //#endregion

            if (criteria.IsGroupBasedSearchOnReport)
            {
                sbWhereClause.Append(" AND ");
                sbWhereClause.Append(" ( ");
                sbWhereClause.AppendLine();
                sbWhereClause.AppendFormat(" CI.GroupNum = {0} ", criteria.ReportGroupId);
                sbWhereClause.Append(" ) ");
            }

            #region ------- Group Extensions -------
            bool secCat = false;
            if (criteria.RecorderCategoryGroupExtensions != null && criteria.RecorderCategoryGroupExtensions.Count() > 0)
            {
                var recResult = criteria.RecorderCategoryGroupExtensions.FirstOrDefault(r => r.RecorderId == recId);
                //var audioNscreenCategoryGroupExtensions = criteria.CategoryGroupExtensions.Where(cge => cge.GroupType == GroupType.Audio || cge.GroupType == GroupType.Screens || cge.GroupType == GroupType.Text || cge.GroupType == GroupType.IQ3 || cge.GroupType == GroupType.MD);
                //var audioNscreenCategoryGroupExtensions = criteria.CategoryGroupExtensions.Where(cge => cge.GroupType == GroupType.Audio || cge.GroupType == GroupType.Screens);
                //if (audioNscreenCategoryGroupExtensions.Count() != 0)
                if (recResult != null && recResult.CategoryGroupExtensions.Count() != 0)
                {
                    sbWhereClause.Append(" AND ( ( ");
                    sbWhereClause.AppendLine();

                    foreach (var catGrpExt in recResult.CategoryGroupExtensions)
                    {
                        if (secCat && catGrpExt.GroupExtensions.Count > 0)
                            sbWhereClause.Append(" OR ( "); //3
                        if (catGrpExt.GroupExtensions.Count > 0)
                        {
                            sbWhereClause.Append(" ( ");
                            foreach (var grpExt in catGrpExt.GroupExtensions)
                            {
                                sbWhereClause.AppendFormat(@"({0}.Ext IN ({2}))", "CI", grpExt.GroupId, grpExt.ExtensionIdsCSV);
                                sbWhereClause.Append(" OR ");
                                sbWhereClause.AppendLine();
                            }
                            sbWhereClause.RemoveLast(" OR ");
                            sbWhereClause.Append(" ) ");//2
                            sbWhereClause.AppendLine();
                            sbWhereClause.AppendFormat(" AND CI.CallType = {0} ", (int)catGrpExt.GroupType);//Call Type
                            sbWhereClause.AppendLine();
                        }
                        if (secCat && catGrpExt.GroupExtensions.Count > 0)
                            sbWhereClause.Append(" ) "); //3
                        if (catGrpExt.GroupExtensions.Count > 0)
                            secCat = true;
                    }
                    if (sbWhereClause.Equals("{ AND (  OR (  )  OR (  ) }"))
                    {
                        sbWhereClause.Replace("{ AND (  OR (  )  OR (  ) }", "");
                        sbWhereClause.Append("AND CI.CallType = 7 ");
                    }
                    sbWhereClause.Append(" ) ");
                }
            }


            if (criteria.ArchiveGroup != null && criteria.ArchiveGroup.Length > 0 && isRecPrimary)
            {
                if (!criteria.ArchiveGroup.Contains(';'))
                {
                    if (secCat)
                        sbWhereClause.Append(" OR ");
                    else
                        sbWhereClause.Append(" AND ");
                    sbWhereClause.Append(" ( ");
                    //sbWhereClause.Append(" (CI.ExtName LIKE N'%" + criteria.ArchiveGroup + "%')");
                    sbWhereClause.Append(" (CI.ExtName = '" + criteria.ArchiveGroup + "')");
                    sbWhereClause.Append(" ) ");

                }
                else if (criteria.ArchiveGroup.Contains(';'))
                {
                    string[] ArchiveGroupList = criteria.ArchiveGroup.Split(';');
                    for (int j = 0; j < ArchiveGroupList.Length; j++)
                    {
                        if (j == 0)
                        {
                            if (secCat)
                                sbWhereClause.Append(" OR ");
                            else
                                sbWhereClause.Append(" AND ");
                            sbWhereClause.Append(" ( ");
                            sbWhereClause.Append(" (CI.ExtName = '" + ArchiveGroupList[j] + "')");
                        }
                        else
                        {
                            sbWhereClause.Append(" OR ");
                            sbWhereClause.Append(" (CI.ExtName = '" + ArchiveGroupList[j] + "')");
                        }
                    }
                    sbWhereClause.Append(" ) ");
                }
            }

            if (secCat)
                sbWhereClause.Append(" ) "); //4

            #endregion

            System.Diagnostics.Debug.WriteLine(sbWhereClause.ToString());
            return sbWhereClause.ToString();
        }

        #endregion

        #region ------- Custom Fields & Bookmarks -------

        public VRResponse UpdateCallsCustomFields(string callIds, string fieldName, string fieldText, int userId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "UpdateCallsCustomFields", tenantId, "UpdateCallsCustomFields function has been called successfully."));
                int fieldType = -1;
                switch (fieldName)
                {
                    case "colCallComments": //CALL_COMMENT
                    case "colComment": //CALL_COMMENT
                    case "Comment": //CALL_COMMENT
                        fieldType = 0;
                        break;
                    case "colCustInfo1": //CustName
                    case "CustInfo1": //CustName
                        fieldType = 5;
                        break;
                    case "colCustInfo2": //ANI
                    case "CustInfo2": //ANI
                        fieldType = 6;
                        break;
                    case "colCustInfo3": //Tag2
                    case "CustInfo3": //Tag2
                        fieldType = 2;
                        break;
                    case "colCustInfo4": //Tag3
                    case "CustInfo4": //Tag3
                        fieldType = 3;
                        break;
                    case "colCustInfo5": //Tag4
                    case "CustInfo5": //Tag4
                        fieldType = 4;
                        break;
                    case "colCallTag": //Tag1
                    case "CallTag": //Tag1
                        fieldType = 1;
                        break;
                    case "colCalledId": //CalledId
                    case "CalledId": //CalledId
                        fieldType = 7;
                        break;
                        //default:
                        //    fieldType = -1;
                        //    break;
                }

                //Handling Escape Characters
                fieldText = fieldText.Replace("'", "''");

                int rowsAffected = new VoiceLoggingDAL(tenantId).UpdateCallsCustomFields(callIds, fieldType, fieldText, userId);
                return new VRResponse
                {
                    Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    RowsAffected = rowsAffected
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "UpdateCallsCustomFields", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure,
                    RowsAffected = 0
                };
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function UpdateCallsCustomFields().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "UpdateCallsCustomFields", tenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure,
                    RowsAffected = 0
                };
            }
        }

        public VRResponse UpdateCallsCustomFieldsByRecorders(VRRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "UpdateCallsCustomFieldsByRecorders", request.TenantId, "UpdateCallsCustomFieldsByRecorders function has been called successfully."));
                List<RecCall> recCallIds = request.CallCustomField.RecCallIds;

                int fieldType = -1;
                switch (request.CallCustomField.FieldName)
                {
                    case "colCallComments": //CALL_COMMENT
                    case "colComment": //CALL_COMMENT
                    case "Comment": //CALL_COMMENT
                        fieldType = 0;
                        break;
                    case "colCustInfo1": //CustName
                    case "CustInfo1": //CustName
                        fieldType = 5;
                        break;
                    case "colCustInfo2": //ANI
                    case "CustInfo2": //ANI
                        fieldType = 6;
                        break;
                    case "colCustInfo3": //Tag2
                    case "CustInfo3": //Tag2
                        fieldType = 2;
                        break;
                    case "colCustInfo4": //Tag3
                    case "CustInfo4": //Tag3
                        fieldType = 3;
                        break;
                    case "colCustInfo5": //Tag4
                    case "CustInfo5": //Tag4
                        fieldType = 4;
                        break;
                    case "colCallTag": //Tag1
                    case "CallTag": //Tag1
                        fieldType = 1;
                        break;
                    case "colCalledId": //CalledId
                    case "CalledId": //CalledId
                        fieldType = 7;
                        break;
                        //default:
                        //    fieldType = -1;
                        //    break;
                }
                int rowsAffected = 0;
                if (fieldType != -1)
                {
                    foreach (var rec in request.Recorders)
                    {
                        if (rec.IsPrimary)
                        {

                            //string callIds = "";
                            string csvCallIds = string.Format("'{0}'", string.Join("','", recCallIds.Where(r => r.RecId == rec.Id).Select(i => i.CallId.Replace("'", "''"))));
                            if (!string.IsNullOrEmpty(csvCallIds) && csvCallIds.Length != 2)
                                rowsAffected += VoiceLoggingDALEC.UpdateCallsCustomFields(rec, csvCallIds, fieldType, request.CallCustomField.FieldText, request.CallCustomField.UserId);
                        }
                        else
                        {
                            // Alternative approach
                            RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                            entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });

                            string csvCallIds = string.Format("'{0}'", string.Join("','", recCallIds.Where(r => r.RecId == rec.Id).Select(i => i.CallId.Replace("'", "''"))));
                            string fieldText = request.CallCustomField.FieldText;
                            int userId = request.CallCustomField.UserId;

                            if (!string.IsNullOrEmpty(csvCallIds) && csvCallIds.Length != 2)
                            {
                                rowsAffected += entClient.UpdateCallsCustomFields(rec, csvCallIds, fieldType, fieldText, userId);
                            }
                        }
                    }
                }
                return new VRResponse
                {
                    Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    RowsAffected = rowsAffected
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "UpdateCallsCustomFieldsByRecorders", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure,
                    RowsAffected = 0
                };
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function UpdateCallsCustomFieldsByRecorders().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "UpdateCallsCustomFieldsByRecorders", request.TenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure,
                    RowsAffected = 0
                };
            }
        }

        public VRResponse UpdateCallCustomFields(VRRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "UpdateCallCustomFields", request.TenantId, "UpdateCallCustomFields function has been called successfully."));

                int rowsAffected = 0;

                if (request.Recorder == null || request.Recorder.IsPrimary)
                {
                    rowsAffected += new VoiceLoggingDAL(request.TenantId).UpdateCallCustomFields(request.CallInfo);
                }
                else
                {
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + request.Recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });

                    rowsAffected += entClient.UpdateCallCustomFields(request.Recorder, request.CallInfo.CallId, request.CallInfo.Tag1, request.CallInfo.Tag2, request.CallInfo.Tag3, request.CallInfo.Tag4, request.CallInfo.CustName, request.CallInfo.CallComments);
                }

                return new VRResponse
                {
                    Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    RowsAffected = rowsAffected
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "UpdateCallCustomFields", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure,
                    RowsAffected = 0
                };
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function UpdateCallCustomFields().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "UpdateCallCustomFields", request.TenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure,
                    RowsAffected = 0
                };
            }
        }

        public VRResponse UpdateCallTags(string callIds, string TagcolorID, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "UpdateCallTags", tenantId, "UpdateCallRetention function has been called successfully."));
                int rowsAffected = new VoiceLoggingDAL(tenantId).UpdateCallTag(callIds, TagcolorID);
                return new VRResponse
                {
                    Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    RowsAffected = rowsAffected
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "UpdateCallTags", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure,
                    RowsAffected = 0
                };
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function UpdateCallTags().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "UpdateCallTags", tenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure,
                    RowsAffected = 0
                };
            }
        }

        public VRResponse UpdateBookmarkFlags(string BookmarkID, string FlagColorID, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "UpdateCallTags", tenantId, "UpdateCallRetention function has been called successfully."));
                int rowsAffected = new VoiceLoggingDAL(tenantId).UpdateBookmarkFlags(BookmarkID, FlagColorID);
                return new VRResponse { Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure, RowsAffected = rowsAffected };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "UpdateCallTags", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                return new VRResponse { Acknowledge = AcknowledgeType.Failure, RowsAffected = 0 };
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function UpdateCallTags().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "UpdateCallTags", tenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                return new VRResponse { Acknowledge = AcknowledgeType.Failure, RowsAffected = 0 };
            }
        }

        public VRResponse UpdateCallRetention(string callId, bool retainValue, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "UpdateCallRetention", tenantId, "UpdateCallRetention function has been called successfully."));
                int rowsAffected = new VoiceLoggingDAL(tenantId).UpdateCallRetention(callId, retainValue);
                return new VRResponse
                {
                    Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    RowsAffected = rowsAffected
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "UpdateCallRetention", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure,
                    RowsAffected = 0
                };
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function UpdateCallRetention().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "UpdateCallRetention", tenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure,
                    RowsAffected = 0
                };
            }
        }

        public VRResponse UpdateCallRetentionByRecorder(VRRequest request, string callId, bool retainValue)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "UpdateCallRetentionByRecorder", request.TenantId, "UpdateCallRetentionByRecorder function has been called successfully."));
                int rowsAffected = 0;
                if (request.Recorder.IsPrimary)
                {
                    rowsAffected = VoiceLoggingDALEC.UpdateCallRetention(request.Recorder, callId, retainValue);
                }
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + request.Recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });

                    rowsAffected += entClient.UpdateCallRetention(request.Recorder, callId, retainValue);
                }
                return new VRResponse
                {
                    Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    RowsAffected = rowsAffected
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "UpdateCallRetention", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure,
                    RowsAffected = 0
                };
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function UpdateCallRetention().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "UpdateCallRetention", request.TenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure,
                    RowsAffected = 0
                };
            }
        }

        //Inquiremarkerupdate
        public VRResponse Inquiremarkerupdate(VRRequest request, string callId, int markerid, string markertext, string markernotes)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "Inquiremarkerupdate", request.TenantId, "Inquiremarkerupdate function has been called successfully."));
                int rowsAffected = 0;
                if (request.Recorder.IsPrimary)
                {
                    rowsAffected = VoiceLoggingDALEC.Inquiremarkerupdate(request.Recorder, callId, markerid, markertext, markernotes);
                }
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + request.Recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });

                    rowsAffected += entClient.Inquiremarkerupdate(request.Recorder, callId, markerid, markertext, markernotes);
                }
                return new VRResponse
                {
                    Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    RowsAffected = rowsAffected
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "Inquiremarkerupdate", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure,
                    RowsAffected = 0
                };
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function Inquiremarkerupdate().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "Inquiremarkerupdate", request.TenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure,
                    RowsAffected = 0
                };
            }
        }

        public VRResponse SaveBookmark(VRRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "SaveBookmark", request.TenantId, "SaveBookmark function has been called successfully."));
                int rowsAffected = -1;
                List<string> availableBookmarks = null;
                switch (request.PersistType)
                {
                    case PersistType.Insert:
                        if (request.Recorder == null)
                        {
                            return new VRResponse
                            {
                                AvailableBookmarks = new VoiceLoggingDAL(request.TenantId).InsertBookmarkAndGetByCallId(request.Bookmark, out rowsAffected),
                                Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                                RowsAffected = rowsAffected
                            };
                        }
                        else
                        {
                            if (request.Recorder.IsPrimary)
                            {
                                availableBookmarks = VoiceLoggingDALEC.InsertBookmarkAndGetByCallId(request.Recorder, request.Bookmark, out rowsAffected);
                            }
                            else
                            {
                                // Alternative approach
                                RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                                entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + request.Recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });

                                availableBookmarks = entClient.InsertBookmarkAndGetByCallId(request.Recorder, request.Bookmark, out rowsAffected).ToList();
                            }
                            return new VRResponse
                            {
                                AvailableBookmarks = availableBookmarks,
                                Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                                RowsAffected = rowsAffected
                            };
                        }
                    case PersistType.Update:
                        if (request.Recorder == null)
                        {
                            return new VRResponse
                            {
                                AvailableBookmarks = new VoiceLoggingDAL(request.TenantId).UpdateBookmarkAndGetByCallId(request.Bookmark, out rowsAffected),
                                Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                                RowsAffected = rowsAffected
                            };
                        }
                        else
                        {
                            if (request.Recorder.IsPrimary)
                            {
                                availableBookmarks = VoiceLoggingDALEC.UpdateBookmarkAndGetByCallId(request.Recorder, request.Bookmark, out rowsAffected);
                            }
                            else
                            {
                                // Alternative approach
                                RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                                entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + request.Recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });

                                availableBookmarks = entClient.UpdateBookmarkAndGetByCallId(request.Recorder, request.Bookmark, out rowsAffected).ToList();
                            }
                            return new VRResponse
                            {
                                AvailableBookmarks = availableBookmarks,
                                Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                                RowsAffected = rowsAffected
                            };
                        }

                    case PersistType.Delete:
                        break;
                    default:
                        return new VRResponse
                        {
                            Acknowledge = AcknowledgeType.Failure,
                            Message = "Unable to save bookmark because you haven't specified the operation"
                        };
                }
                return null;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "SaveBookmark", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure,
                    RowsAffected = 0,
                    Message = string.Format("An error has occurred while processing function SaveBookmark(). Exception: {0}", sqle)
                };
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function SaveBookmark().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "SaveBookmark", request.TenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure,
                    RowsAffected = 0,
                    Message = string.Format("An error has occurred while processing function SaveBookmark(). Exception: {0}", ex)
                };
            }
        }

        #endregion

        #region Load Search Page Data on pageload event

        //public VRResponse GetSearchPageData(VRRequest request)
        //{
        //    try
        //    {
        //        //return new PageDAL().GetSearchPageData(voiceRecRequest);
        //        bool isDemoMode = AppSettingsUtil.GetBool("isDemo");

        //        var vrResponse = new PageDAL().GetSearchPageData(request);

        //        var treeNodes = vrResponse.TreeviewData.BuildGroupsTree();

        //        var vrResponse_inquire = new PageDAL().GetSearchPageData_Inquire(request);

        //        var treeNodes_inquire = vrResponse_inquire.TreeviewData.BuildGroupsTree();
        //        List<GroupTree> groupsAll;

        //        bool b_isinquire = false;
        //        foreach (TreeviewData item in treeNodes_inquire.Childrens)
        //            if (!item.IsGroup) b_isinquire = true;

        //        if (treeNodes_inquire.Childrens.Count > 0 && b_isinquire)
        //        {
        //            groupsAll = new List<GroupTree>
        //        {
        //        new GroupTree{ Id= 1, GroupType = GroupType.Audio, CssName = "audio", TreeviewData = treeNodes},
        //        new GroupTree{ Id= 6, GroupType = GroupType.Screens, CssName = "screen", TreeviewData = treeNodes},
        //        new GroupTree{ Id= 7, GroupType = GroupType.Inquire, CssName = "inquire", TreeviewData = treeNodes_inquire},
        //        };
        //        }
        //        else
        //        {
        //            groupsAll = new List<GroupTree>
        //        {
        //            new GroupTree{ Id= 1, GroupType = GroupType.Audio, CssName = "audio", TreeviewData = treeNodes},
        //            new GroupTree{ Id= 6, GroupType = GroupType.Screens, CssName = "screen", TreeviewData = treeNodes},
        //        };
        //        }

        //        if (isDemoMode)
        //            groupsAll.AddRange(DemoData.GetDemoGroups());

        //        vrResponse.AllGroups = groupsAll.OrderBy(g => g.Id).ToList();
        //        return vrResponse;
        //    }
        //    catch (Exception ex)
        //    {
        //        return null;
        //    }
        //}

        public VRResponse GetSearchPageData(VRRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetSearchPageData", request.TenantId, "GetSearchPageData function has been called successfully."));
                //return new PageDAL().GetSearchPageData(voiceRecRequest);
                bool isDemoMode = AppSettingsUtil.GetBool("isDemo");

                var vrResponse = new PageDAL(request.TenantId).GetRevcordAndInquireSearchPageData(request);

                if (request.UserType == 0)
                {
                    // Ability to grab Revcell extensions from inquire and add under audio category
                    //List<TreeviewData> revcellNodes = new List<TreeviewData>();
                    //if (vrResponse.TreeviewDataInquire != null)
                    //{
                    //    revcellNodes = vrResponse.TreeviewDataInquire.Where(rc => rc.IsRevCell == true).ToList();
                    //    if (vrResponse.TreeviewData != null)
                    //        vrResponse.TreeviewData.AddRange(revcellNodes);
                    //}

                    // Ability to grab inquire extensions from Audio and add under IQ3 category
                    List<TreeviewData> iq3Nodes = new List<TreeviewData>();
                    if (vrResponse.TreeviewData != null)
                    {
                        iq3Nodes = vrResponse.TreeviewData.Where(rc => rc.IsIQ3 == true).ToList();
                        if (vrResponse.TreeviewDataInquire != null)
                            vrResponse.TreeviewDataInquire.AddRange(iq3Nodes);
                    }
                }

                var revcordTreeNodes = vrResponse.TreeviewData.BuildGroupsTree();
                var teamsTreeNodes = vrResponse.TreeviewDataTeams.BuildGroupsTree();
                var revcellTreeNodes = vrResponse.TreeviewDataRevcell.BuildGroupsTree();
                var inquireTreeNodes = vrResponse.TreeviewDataInquire.BuildGroupsTree();
                var mdTreeNodes = vrResponse.TreeviewDataMD.BuildGroupsTree();

                var iwbTreeNodes = vrResponse.TreeviewDataIwb.BuildGroupsTree();

                //recDbNodes.AddRange(rcNodes);

                List<GroupTree> groupsAll;
                if (request.IsInquireView) // inquireView check
                {
                    groupsAll = new List<GroupTree>
                        {
                            new GroupTree{ Id= 7, GroupType = GroupType.IQ3, CssName = "inquire", TreeviewData = inquireTreeNodes},
                        };
                }
                else if (request.IsOnlyIWBModeEnabled) {
                    groupsAll = new List<GroupTree>
                        {
                            new GroupTree{ Id= 23, GroupType = GroupType.Iwb, CssName = "iwb", TreeviewData = iwbTreeNodes},
                        };
                }
                else if (vrResponse.TreeviewDataInquire.Any(c => c.IsGroup == false) || vrResponse.TreeviewDataMD.Any(c => c.IsGroup == false))
                {
                    if (request.HideTextAndScreensTree)
                    {
                        groupsAll = new List<GroupTree>
                        {
                            new GroupTree{ Id= 1, GroupType = GroupType.Audio, CssName = "audio", TreeviewData = revcordTreeNodes},
                            new GroupTree{ Id= 7, GroupType = GroupType.IQ3, CssName = "inquire", TreeviewData = inquireTreeNodes},
                            new GroupTree{ Id= 13, GroupType = GroupType.Teams, CssName = "teams", TreeviewData = teamsTreeNodes},
                            new GroupTree{ Id= 14, GroupType = GroupType.Revcell, CssName = "revcell", TreeviewData = revcellTreeNodes},
                            new GroupTree{ Id= 23, GroupType = GroupType.Iwb, CssName = "iwb", TreeviewData = iwbTreeNodes},
                        };
                    }
                    else
                    {
                        groupsAll = new List<GroupTree>
                        {
                            new GroupTree{ Id= 1, GroupType = GroupType.Audio, CssName = "audio", TreeviewData = revcordTreeNodes},
                            new GroupTree { Id = 3, GroupType =  GroupType.Text, CssName = "text", TreeviewData = revcordTreeNodes },
                            new GroupTree{ Id= 6, GroupType = GroupType.Screens, CssName = "screen", TreeviewData = revcordTreeNodes},
                            new GroupTree{ Id= 7, GroupType = GroupType.IQ3, CssName = "inquire", TreeviewData = inquireTreeNodes},
                            new GroupTree{ Id= 13, GroupType = GroupType.Teams, CssName = "teams", TreeviewData = teamsTreeNodes},
                            new GroupTree{ Id= 14, GroupType = GroupType.Revcell, CssName = "revcell", TreeviewData = revcellTreeNodes},
                            new GroupTree{ Id= 23, GroupType = GroupType.Iwb, CssName = "iwb", TreeviewData = iwbTreeNodes},
                        };
                    }
                }
                else
                {
                    groupsAll = new List<GroupTree>
                        {
                            new GroupTree{ Id= 1, GroupType = GroupType.Audio, CssName = "audio", TreeviewData = revcordTreeNodes},
                            new GroupTree { Id = 3, GroupType =  GroupType.Text, CssName = "text", TreeviewData = revcordTreeNodes },
                            new GroupTree{ Id= 6, GroupType = GroupType.Screens, CssName = "screen", TreeviewData = revcordTreeNodes},
                            new GroupTree{ Id= 13, GroupType = GroupType.Teams, CssName = "teams", TreeviewData = teamsTreeNodes},
                            new GroupTree{ Id= 14, GroupType = GroupType.Revcell, CssName = "revcell", TreeviewData = revcellTreeNodes},
                            new GroupTree{ Id= 23, GroupType = GroupType.Iwb, CssName = "iwb", TreeviewData = iwbTreeNodes},
                        };
                }

                bool b_isTalkgroup = false;
                foreach (TreeviewData item in vrResponse.TreeviewDataRadioTalkGroup.BuildGroupsTree().Childrens)
                    if (!item.IsGroup) b_isTalkgroup = true;

                if (b_isTalkgroup && !request.IsInquireView)
                {
                    var RadioTalkGroupTreeNodes = vrResponse.TreeviewDataRadioTalkGroup.BuildGroupsTree();
                    groupsAll.Add(new GroupTree { Id = 8, GroupType = GroupType.Filter, CssName = "FILTER", TreeviewData = RadioTalkGroupTreeNodes });
                }

                bool b_isArchivegroup = false;
                if (vrResponse.TreeviewDataArchive != null && vrResponse.TreeviewDataArchive.Count > 0)
                    foreach (TreeviewData item in vrResponse.TreeviewDataArchive.BuildGroupsTree().Childrens)
                        if (!item.IsGroup) b_isArchivegroup = true;

                if (b_isArchivegroup && !request.IsInquireView)
                {
                    var ArchiveGroupTreeNodes = vrResponse.TreeviewDataArchive.BuildGroupsTree();
                    groupsAll.Add(new GroupTree { Id = 9, GroupType = GroupType.Archive, CssName = "ARCHIVE", TreeviewData = ArchiveGroupTreeNodes });
                }

                if (!request.IsTeamsEnabled)
                    groupsAll.RemoveAll(x => x.GroupType == GroupType.Teams);

                if (!request.IsRevcellEnabled)
                    groupsAll.RemoveAll(x => x.GroupType == GroupType.Revcell);

                if (!request.IsIwbModeEnabled)
                    groupsAll.RemoveAll(x => x.GroupType == GroupType.Iwb);

                if (request.IsIwbModeEnabled)
                    groupsAll.RemoveAll(x => x.GroupType == GroupType.Archive);

                if (isDemoMode && !request.IsInquireView)
                    groupsAll.AddRange(DemoData.GetDemoGroups());


                vrResponse.AllGroups = groupsAll.OrderBy(g => g.Id).ToList();
                return vrResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetSearchPageData", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                return null;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetSearchPageData", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                return null;
            }
        }

        public VRResponse GetSearchPageDataLite(VRRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetSearchPageDataLite", request.TenantId, "GetSearchPageDataLite function has been called successfully."));
                var vrResponse = new PageDAL(request.TenantId).GetSearchPageData(request);
                var treeNodes = vrResponse.TreeviewData.BuildGroupsTree();
                var teamsTreeNodes = vrResponse.TreeviewDataTeams.BuildGroupsTree();
                var revcellTreeNodes = vrResponse.TreeviewDataRevcell.BuildGroupsTree();
                var iwbTreeNodes = vrResponse.TreeviewDataIwb.BuildGroupsTree();
                List<GroupTree> groupsAll;
                groupsAll = new List<GroupTree>
                {
                    new GroupTree{ Id= 1, GroupType = GroupType.Audio, CssName = "audio", TreeviewData = treeNodes},
                    new GroupTree { Id = 3, GroupType =  GroupType.Text, CssName = "text", TreeviewData = treeNodes },
                    new GroupTree{ Id= 6, GroupType = GroupType.Screens, CssName = "screen", TreeviewData = treeNodes},
                    new GroupTree{ Id= 13, GroupType = GroupType.Teams, CssName = "teams", TreeviewData = teamsTreeNodes},
                    new GroupTree{ Id= 14, GroupType = GroupType.Revcell, CssName = "revcell", TreeviewData = revcellTreeNodes},
                    new GroupTree{ Id= 23, GroupType = GroupType.Iwb, CssName = "iwb", TreeviewData = iwbTreeNodes},
                };

                bool b_isTalkgroup = false;
                foreach (TreeviewData item in vrResponse.TreeviewDataRadioTalkGroup.BuildGroupsTree().Childrens)
                    if (!item.IsGroup) b_isTalkgroup = true;

                if (b_isTalkgroup)
                {
                    var RadioTalkGroupTreeNodes = vrResponse.TreeviewDataRadioTalkGroup.BuildGroupsTree();
                    groupsAll.Add(new GroupTree { Id = 8, GroupType = GroupType.Filter, CssName = "FILTER", TreeviewData = RadioTalkGroupTreeNodes });
                }

                bool b_isArchivegroup = false;
                if (vrResponse.TreeviewDataArchive != null)
                {
                    foreach (TreeviewData item in vrResponse.TreeviewDataArchive.BuildGroupsTree().Childrens)
                        if (!item.IsGroup) b_isArchivegroup = true;

                    if (b_isArchivegroup)
                    {
                        var ArchiveGroupTreeNodes = vrResponse.TreeviewDataArchive.BuildGroupsTree();
                        groupsAll.Add(new GroupTree { Id = 9, GroupType = GroupType.Archive, CssName = "ARCHIVE", TreeviewData = ArchiveGroupTreeNodes });
                    }
                }

                if (!request.IsTeamsEnabled)
                    groupsAll.RemoveAll(x => x.GroupType == GroupType.Teams);

                if (!request.IsRevcellEnabled)
                    groupsAll.RemoveAll(x => x.GroupType == GroupType.Revcell);

                vrResponse.AllGroups = groupsAll.OrderBy(g => g.Id).ToList();
                return vrResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetSearchPageDataLite", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                return null;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetSearchPageDataLite", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                return null;
            }
        }
        #endregion

        public VRResponse GetTreeViewData(VRRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetTreeViewData", request.TenantId, "GetTreeViewData function has been called successfully."));
                bool isDemoMode = AppSettingsUtil.GetBool("isDemo");

                var vrResponse = new PageDAL(request.TenantId).GetRevcordAndInquireTreeView(request);

                if (request.UserType == 0)
                {
                    // Ability to grab Revcell extensions from inquire and add under audio category
                    List<TreeviewData> revcellNodes = new List<TreeviewData>();
                    if (vrResponse.TreeviewDataInquire != null)
                    {
                        revcellNodes = vrResponse.TreeviewDataInquire.Where(rc => rc.IsRevCell == true).ToList();
                        if (vrResponse.TreeviewData != null)
                            vrResponse.TreeviewData.AddRange(revcellNodes);
                    }

                    // Ability to grab inquire extensions from Audio and add under IQ3 category
                    List<TreeviewData> iq3Nodes = new List<TreeviewData>();
                    if (vrResponse.TreeviewData != null)
                    {
                        iq3Nodes = vrResponse.TreeviewData.Where(rc => rc.IsIQ3 == true).ToList();
                        if (vrResponse.TreeviewDataInquire != null)
                            vrResponse.TreeviewDataInquire.AddRange(iq3Nodes);
                    }
                }

                var revcordTreeNodes = vrResponse.TreeviewData.BuildGroupsTree();
                var teamsTreeNodes = (vrResponse.TreeviewDataTeams != null) ? vrResponse.TreeviewDataTeams.BuildGroupsTree() : null;
                var revcellTreeNodes = (vrResponse.TreeviewDataRevcell != null) ? vrResponse.TreeviewDataRevcell.BuildGroupsTree() : null;
                var inquireTreeNodes = vrResponse.TreeviewDataInquire.BuildGroupsTree();
                var mdTreeNodes = vrResponse.TreeviewDataMD.BuildGroupsTree();

                List<GroupTree> groupsAll;

                if (request.IsInquireView)
                {
                    groupsAll = new List<GroupTree>
                        {
                            new GroupTree{ Id= 7, GroupType = GroupType.IQ3, CssName = "inquire", TreeviewData = inquireTreeNodes},
                        };
                }
                else if (vrResponse.TreeviewDataInquire.Any(c => c.IsGroup == false))
                {
                    groupsAll = new List<GroupTree>
                        {
                            new GroupTree{ Id= 1, GroupType = GroupType.Audio, CssName = "audio", TreeviewData = revcordTreeNodes},
                            new GroupTree { Id = 3, GroupType =  GroupType.Text, CssName = "text", TreeviewData = revcordTreeNodes },
                            new GroupTree{ Id= 6, GroupType = GroupType.Screens, CssName = "screen", TreeviewData = revcordTreeNodes},
                            new GroupTree{ Id= 7, GroupType = GroupType.IQ3, CssName = "inquire", TreeviewData = inquireTreeNodes},
                            new GroupTree{ Id= 13, GroupType = GroupType.Teams, CssName = "teams", TreeviewData = teamsTreeNodes},
                            new GroupTree{ Id= 14, GroupType = GroupType.Revcell, CssName = "revcell", TreeviewData = revcellTreeNodes},
                        };
                }
                else
                {
                    groupsAll = new List<GroupTree>
                        {
                            new GroupTree{ Id= 1, GroupType = GroupType.Audio, CssName = "audio", TreeviewData = revcordTreeNodes},
                            new GroupTree { Id = 3, GroupType =  GroupType.Text, CssName = "text", TreeviewData = revcordTreeNodes },
                            new GroupTree{ Id= 6, GroupType = GroupType.Screens, CssName = "screen", TreeviewData = revcordTreeNodes},
                            new GroupTree{ Id= 13, GroupType = GroupType.Teams, CssName = "teams", TreeviewData = teamsTreeNodes},
                            new GroupTree{ Id= 14, GroupType = GroupType.Revcell, CssName = "revcell", TreeviewData = revcellTreeNodes},
                        };
                }

                if (!request.IsTeamsEnabled)
                    groupsAll.RemoveAll(x => x.GroupType == GroupType.Teams);

                if (!request.IsRevcellEnabled)
                    groupsAll.RemoveAll(x => x.GroupType == GroupType.Revcell);

                bool b_isTalkgroup = false;
                if (vrResponse.TreeviewDataRadioTalkGroup != null)
                {
                    foreach (TreeviewData item in vrResponse.TreeviewDataRadioTalkGroup.BuildGroupsTree().Childrens)
                        if (!item.IsGroup) b_isTalkgroup = true;

                    if (b_isTalkgroup && !request.IsInquireView)
                    {
                        var RadioTalkGroupTreeNodes = vrResponse.TreeviewDataRadioTalkGroup.BuildGroupsTree();
                        groupsAll.Add(new GroupTree { Id = 8, GroupType = GroupType.Filter, CssName = "FILTER", TreeviewData = RadioTalkGroupTreeNodes });
                    }
                }

                /*

                bool b_isArchivegroup = false;
                foreach (TreeviewData item in vrResponse.TreeviewDataArchive.BuildGroupsTree().Childrens)
                    if (!item.IsGroup) b_isArchivegroup = true;

                if (b_isArchivegroup && !request.IsInquireView)
                {
                    var ArchiveGroupTreeNodes = vrResponse.TreeviewDataArchive.BuildGroupsTree();
                    groupsAll.Add(new GroupTree { Id = 9, GroupType = GroupType.Archive, CssName = "ARCHIVE", TreeviewData = ArchiveGroupTreeNodes });
                }
                */

                if (isDemoMode && request.IsSearchPageCall)
                    groupsAll.AddRange(DemoData.GetDemoGroups());

                vrResponse.AllGroups = groupsAll.OrderBy(g => g.Id).ToList();
                return vrResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetTreeViewData", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                return null;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetTreeViewData", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                return null;
            }
        }


        #region ------- Channels -------
        public VRResponse GetAudioChannels(VRRequest request)
        {
            var response = new VRResponse();
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetAudioChannels", request.TenantId, "GetAudioChannels function has been called successfully."));
                //response.Channels = new VoiceLoggingDAL(request.TenantId).GetAudioChannels();
                if (request.Recorder.IsPrimary)
                {
                    response.Channels = new VoiceLoggingDAL(request.TenantId).GetAudioChannels();
                }
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + request.Recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });

                    response.Channels = entClient.GetAudioChannelsFromRecorder(request.Recorder).ToList();
                }
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetAudioChannels", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetAudioChannels", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return response;
        }

        public VRResponse GetAudioChannel(VRRequest request)
        {
            var response = new VRResponse();
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetAudioChannel", request.TenantId, "GetAudioChannel function has been called successfully."));
                if (request.Recorder.IsPrimary)
                {
                    response.Channel = new VoiceLoggingDAL(request.TenantId).GetAudioChannel(request.ChannelId);
                }
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + request.Recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });

                    response.Channel = entClient.GetAudioChannelFromRecorder(request.Recorder, request.ChannelId);
                }
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetAudioChannel", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetAudioChannel", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return response;
        }

        public VRResponse CreateAudioChannels(VRRequest request)
        {

            var response = new VRResponse();
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "CreateAudioChannels", request.TenantId, "CreateAudioChannels function has been called successfully."));
                if (request.Recorder.IsPrimary)
                {
                    List<Channel> channels = new List<Channel>();
                    channels.AddRange(Channel.AnalogChannelsProvider(request.NoOfAnalogChannels, request.TenantGateway.GatewayId));
                    channels.AddRange(Channel.VoIPChannelsProvider(request.NoOfVoIPChannels, request.TenantGateway.GatewayId));
                    
                    int rowsAffected = new VoiceLoggingDAL(request.TenantId).InsertAudioChannels(channels, request.RecorderId);

                    return new VRResponse
                    {
                        Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                        RowsAffected = rowsAffected,
                        Message = string.Format("{0} Channels has been created successfully.", channels.Count())
                    };
                }
                else
                {
                    int remoteRecorderLocalId = 1;
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + request.Recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });

                    int rowsAffected = entClient.CreateAudioChannels(request.Recorder, remoteRecorderLocalId, request.NoOfAnalogChannels, request.NoOfVoIPChannels, request.TenantGateway.GatewayId);

                    return new VRResponse
                    {
                        Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                        RowsAffected = rowsAffected,
                        Message = string.Format("Channels has been created successfully.")
                    };
                }
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "CreateAudioChannels", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "CreateAudioChannels", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public VRResponse UpdateAudioChannel(VRRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "UpdateAudioChannel", request.TenantId, "UpdateAudioChannel function has been called successfully."));
                if (request.Recorder.IsPrimary)
                {
                    int rowsAffected = new VoiceLoggingDAL(request.TenantId).UpdateAudioChannels(request.Channel, request.RecorderId);
                    return new VRResponse
                    {
                        Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                        RowsAffected = rowsAffected,
                        Message = string.Format("Channel # {0} has been Updated successfully.", request.Channel.Ext)
                    };
                }
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + request.Recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });

                    int rowsAffected = entClient.UpdateAudioChannel(request.Recorder, request.Channel);
                    return new VRResponse
                    {
                        Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                        RowsAffected = rowsAffected,
                        Message = string.Format("Channel # {0} has been Updated successfully.", request.Channel.Ext)
                    };
                }
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "UpdateAudioChannel", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "UpdateAudioChannel", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        public VRResponse DeleteAudioChannels(VRRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "DeleteAudioChannels", request.TenantId, "DeleteAudioChannels function has been called successfully."));
                if (request.Recorder.IsPrimary)
                {
                    int rowsAffected = new VoiceLoggingDAL(request.TenantId).DeleteAudioChannels(request.ChannelIds);

                    return new VRResponse
                    {
                        Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                        RowsAffected = rowsAffected,
                        Message = string.Format("Channels has been deleted successfully against ExtId:{0}.", string.Join(", ", request.ChannelIds))
                    };
                }
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + request.Recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });

                    int rowsAffected = entClient.DeleteAudioChannels(request.Recorder, request.ChannelIds.ToArray());
                    return new VRResponse
                    {
                        Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                        RowsAffected = rowsAffected,
                        //Message = string.Format("Channel # {0} has been Deleted successfully.", request.Channel.Ext)
                        Message = string.Format("Channels has been deleted successfully against ExtId:{0}.", string.Join(", ", request.ChannelIds))
                    };
                }
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "DeleteAudioChannels", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "DeleteAudioChannels", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public VRResponse GetChannelsToMonitor(VRRequest request)
        {
            Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetChannelsToMonitor", request.TenantId, "GetChannelsToMonitor function has been called successfully."));
            var response = new VRResponse();

            try
            {
                StringBuilder sbWhereClause = new StringBuilder();
                sbWhereClause.Append(" 1 = 1 ");

                var criteria = request.ChannelCriteria as ChannelCriteria;

                if (!criteria.GetAllChannels)
                {
                    #region ------- Where Clause String -------
                    if (criteria.CategoryGroupExtension != null && criteria.CategoryGroupExtension.GroupExtensions.Count > 0)
                    {
                        sbWhereClause.Append(" AND ( "); //1
                        sbWhereClause.AppendLine();
                        foreach (var grpExt in criteria.CategoryGroupExtension.GroupExtensions)
                        {
                            //@"({0}.GroupNum = {1} AND {2}.Ext = {3})"
                            //sbWhereClause.AppendFormat(@"({0}.GroupNum = {2} AND {1}.Ext IN ({3}))", "AG", "A", grpExt.GroupId, grpExt.ExtensionIdsCSV);
                            sbWhereClause.AppendFormat(@"({1}.Ext IN ({3}))", "AG", "A", grpExt.GroupId, grpExt.ExtensionIdsCSV);
                            sbWhereClause.Append(" OR ");
                            sbWhereClause.AppendLine();
                        }
                        sbWhereClause.RemoveLast(" OR ");
                        sbWhereClause.Append(" ) "); //1
                    }
                    #endregion
                }
                System.Diagnostics.Debug.WriteLine(sbWhereClause.ToString());
                response.MonitorChannels = new VoiceLoggingDAL(request.TenantId).GetChannelsToMonitor(sbWhereClause.ToString());
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetChannelsToMonitor", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetChannelsToMonitor", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return response;
        }


        #endregion

        #region Teams Channel

        public VRResponse GetTeamsChannels(VRRequest request)
        {
            var response = new VRResponse();
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetTeamsChannels", request.TenantId, "GetTeamsChannels function has been called successfully."));
                
                if (request.Recorder.IsPrimary)
                {
                    response.Channels = new VoiceLoggingDAL(request.TenantId).GetTeamsChannels();
                }
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + request.Recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });

                    //response.Channels = entClient.GetAudioChannelsFromRecorder(request.Recorder).ToList();
                }
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetTeamsChannels", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetTeamsChannels", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return response;
        }

        public VRResponse GetTeamsChannel(VRRequest request)
        {
            var response = new VRResponse();
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetTeamsChannel", request.TenantId, "GetTeamsChannel function has been called successfully."));

                if (request.Recorder.IsPrimary)
                {
                    response.Channel = new VoiceLoggingDAL(request.TenantId).GetTeamsChannel(request.ChannelId);
                }
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + request.Recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });

                    //response.Channel = entClient.GetAudioChannelFromRecorder(request.Recorder, request.ChannelId);
                }
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetTeamsChannel", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetTeamsChannel", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return response;
        }

        public VRResponse CreateTeamsChannel(VRRequest request)
        {

            var response = new VRResponse();
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "CreateTeamsChannel", request.TenantId, "CreateTeamsChannel function has been called successfully."));
                
                if (request.Recorder.IsPrimary)
                {
                    int rowsAffected = new VoiceLoggingDAL(request.TenantId).CreateTeamsChannel(request.Channel, request.RecorderId);

                    return new VRResponse
                    {
                        Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                        RowsAffected = rowsAffected,
                        Message = string.Format("Teams Channel has been created successfully.")
                    };
                }
                else
                {
                    int remoteRecorderLocalId = 1;
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + request.Recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });

                    int rowsAffected = entClient.CreateAudioChannels(request.Recorder, remoteRecorderLocalId, request.NoOfAnalogChannels, request.NoOfVoIPChannels, null);

                    return new VRResponse
                    {
                        Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                        RowsAffected = rowsAffected,
                        Message = string.Format("Channels has been created successfully.")
                    };
                }
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "CreateAudioChannels", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "CreateAudioChannels", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public VRResponse UpdateTeamsChannel(VRRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "UpdateTeamsChannel", request.TenantId, "UpdateTeamsChannel function has been called successfully."));
                if (request.Recorder.IsPrimary)
                {
                    int rowsAffected = new VoiceLoggingDAL(request.TenantId).UpdateTeamsChannel(request.Channel, request.RecorderId);
                    return new VRResponse
                    {
                        Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                        RowsAffected = rowsAffected,
                        Message = string.Format("Channel # {0} has been Updated successfully.", request.Channel.Ext)
                    };
                }
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + request.Recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });

                    int rowsAffected = 0; //entClient.UpdateAudioChannel(request.Recorder, request.Channel);
                    return new VRResponse
                    {
                        Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                        RowsAffected = rowsAffected,
                        Message = string.Format("Channel # {0} has been Updated successfully.", request.Channel.Ext)
                    };
                }
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "UpdateTeamsChannel", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "UpdateTeamsChannel", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        public VRResponse DeleteTeamsChannel(VRRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "DeleteTeamsChannel", request.TenantId, "DeleteTeamsChannel function has been called successfully."));
                if (request.Recorder.IsPrimary)
                {
                    int rowsAffected = new VoiceLoggingDAL(request.TenantId).DeleteTeamsChannel(request.ChannelIds);

                    return new VRResponse
                    {
                        Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                        RowsAffected = rowsAffected,
                        Message = string.Format("Teams Channel has been deleted successfully against ExtId:{0}.", string.Join(", ", request.ChannelIds))
                    };
                }
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + request.Recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });

                    int rowsAffected = 0; //entClient.DeleteAudioChannels(request.Recorder, request.ChannelIds.ToArray());
                    return new VRResponse
                    {
                        Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                        RowsAffected = rowsAffected,
                        Message = string.Format("Teams Channel has been deleted successfully against ExtId:{0}.", string.Join(", ", request.ChannelIds))
                    };
                }
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "DeleteTeamsChannel", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "DeleteTeamsChannel", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #endregion

        #region ------- Channels EC-------

        public VRResponse GetChannelsToMonitorFromAllRecorders(VRRequest request)
        {
            Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetChannelsToMonitorFromAllRecorders", request.TenantId, "GetChannelsToMonitorFromAllRecorders function has been called successfully."));

            List<Recorder> recorders = request.Recorders;
            List<RecorderMonitorChannel> recorderChannels = new List<RecorderMonitorChannel>();

            var response = new VRResponse();


            foreach (var recorder in recorders)
            {
                bool bExceptionThrown = false;
                try
                {
                    if (recorder.Id == request.ChannelCriteria.RecorderId)
                    {
                        string criteria = buildChannelQuery(request.ChannelCriteria, recorder.Id);
                        List<MonitorChannel> recChannels;
                        if (recorder.IsPrimary)
                        {
                            recChannels = VoiceLoggingDALEC.GetChannelsToMonitorFromAllRecorders(recorder, criteria);
                        }
                        else
                        {
                            //recChannels = VoiceLoggingDALEC.GetChannelsToMonitorFromAllRecorders(recorder, criteria);
                            // Alternative approach
                            RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                            entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });

                            recChannels = entClient.GetChannelsToMonitorFromRecorder(recorder, criteria).ToList();
                            recChannels.ForEach(s => { s.RecId = recorder.Id; s.RecIP = recorder.IP; });
                        }
                        recorderChannels.Add(new RecorderMonitorChannel { RecorderId = recorder.Id, RecorderName = recorder.Name, RecorderRTIP = recorder.RTIP, MonitorChannels = recChannels });
                        Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetChannelsToMonitorFromRecorder", request.TenantId, "Channel data fetched succeddfully from RecId = " + recorder.Id + " RecorderName = " + recorder.Name));
                        break;
                    }

                }
                catch (SqlException sqle)
                {
                    bExceptionThrown = true;
                    Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetChannelsToMonitorFromRecorder", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                }
                catch (Exception ex)
                {
                    bExceptionThrown = true;
                    var errorMsg = "An error has occurred while fetching channel data from recorder. RecorderId = " + recorder.Id + ", Recorder Name = " + recorder.Name;
                    Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetChannelsToMonitorFromRecorder", request.TenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                }
                if (bExceptionThrown)
                    continue;
            }
            response.RecorderMonitorChannels = recorderChannels;
            return response;
        }
        public VRResponse GetChannelsToMonitorFromAllRecordersIRLite(VRRequest request)
        {
            Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetChannelsToMonitorFromAllRecordersIRLite", request.TenantId, "GetChannelsToMonitorFromAllRecordersIRLite function has been called successfully."));

            List<Recorder> recorders = request.Recorders;
            List<RecorderMonitorChannel> recorderChannels = new List<RecorderMonitorChannel>();

            var response = new VRResponse();


            foreach (var recorder in recorders)
            {
                bool bExceptionThrown = false;
                try
                {
                    // if (recorder.Id == request.ChannelCriteria.RecorderId)
                    //  {
                    string criteria = buildChannelQuery(request.ChannelCriteria, recorder.Id);
                    List<MonitorChannel> recChannels;
                    if (recorder.IsPrimary)
                    {
                        recChannels = VoiceLoggingDALEC.GetChannelsToMonitorFromAllRecorders(recorder, criteria);
                    }
                    else
                    {
                        //recChannels = VoiceLoggingDALEC.GetChannelsToMonitorFromAllRecorders(recorder, criteria);
                        // Alternative approach
                        RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                        entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });

                        recChannels = entClient.GetChannelsToMonitorFromRecorder(recorder, criteria).ToList();
                        recChannels.ForEach(s => { s.RecId = recorder.Id; s.RecIP = recorder.IP; });
                    }
                    recorderChannels.Add(new RecorderMonitorChannel { RecorderId = recorder.Id, RecorderName = recorder.Name, RecorderRTIP = recorder.RTIP, MonitorChannels = recChannels });
                    Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetChannelsToMonitorFromAllRecordersIRLite", request.TenantId, "Channel data fetched succeddfully from RecId = " + recorder.Id + " RecorderName = " + recorder.Name));
                }
                catch (SqlException sqle)
                {
                    bExceptionThrown = true;
                    Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetChannelsToMonitorFromAllRecordersIRLite", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                }
                catch (Exception ex)
                {
                    bExceptionThrown = true;
                    var errorMsg = "An error has occurred while fetching channel data from recorder. RecorderId = " + recorder.Id + ", Recorder Name = " + recorder.Name;
                    Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetChannelsToMonitorFromAllRecordersIRLite", request.TenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                }
                if (bExceptionThrown)
                    continue;
            }

            //response.MonitorChannels = VoiceLoggingDAL.GetChannelsToMonitor(sbWhereClause.ToString());
            response.RecorderMonitorChannels = recorderChannels;
            return response;
        }
        private string buildChannelQuery(ChannelCriteria channelCriteria, int recorderId)
        {
            StringBuilder sbWhereClause = new StringBuilder();
            sbWhereClause.Append(" 1 = 1 ");

            if (!channelCriteria.GetAllChannels)
            {
                if (channelCriteria.RecorderCategoryGroupExtensions != null && channelCriteria.RecorderCategoryGroupExtensions.Count() > 0)
                {
                    var recResult = channelCriteria.RecorderCategoryGroupExtensions.FirstOrDefault(r => r.RecorderId == recorderId);
                    if (recResult != null && recResult.CategoryGroupExtensions.Count > 0)
                    {
                        var recCatGrpExt = recResult.CategoryGroupExtensions;
                        sbWhereClause.Append(" AND ( "); //1
                        sbWhereClause.AppendLine();
                        foreach (var catGrpExt in recCatGrpExt)
                        {
                            if (catGrpExt.GroupExtensions.Count() == 0)
                                sbWhereClause.RemoveLast(" AND ( ");
                            foreach (var grpExt in catGrpExt.GroupExtensions)
                            {
                                sbWhereClause.AppendFormat(@"({0}.GroupNum = {2} AND {1}.Ext IN ({3}))", "AG", "A", grpExt.GroupId, grpExt.ExtensionIdsCSV);
                                sbWhereClause.Append(" OR ");
                                sbWhereClause.AppendLine();
                            }
                        }
                    }
                    else
                    {
                        return "1<>1";
                    }
                    if (sbWhereClause.ToString().Contains(" OR "))
                    {
                        sbWhereClause.RemoveLast(" OR ");
                    }
                    sbWhereClause.Append(" ) "); //1
                    if (sbWhereClause.ToString().Equals(" 1 = 1 \r\n ) "))
                    {
                        sbWhereClause = new StringBuilder();
                        sbWhereClause.Append(" 1 = 1 ");
                    }

                }
            }
            System.Diagnostics.Debug.WriteLine(sbWhereClause.ToString());
            return sbWhereClause.ToString();
        }

        #endregion


        public VRResponse SaveTranscription(VRRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "SaveTranscription", request.TenantId, "SaveTranscription function has been called successfully."));
                switch (request.PersistType)
                {
                    case PersistType.Insert:
                        int lastId = new VoiceLoggingDAL(request.TenantId).InsertTranscription(request.CallId, request.Transcription, request.ConfidenceScore, DateTime.Now, request.UserId);
                        return new VRResponse
                        {
                            Acknowledge = lastId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                            TranscriptionId = lastId,
                            Message = string.Format("Transcription has been saved successfully against callId:{0}.", request.CallId)
                        };
                    case PersistType.Update:
                        //int updatedId = VoiceLoggingDAL.UpdateTranscription(49, "038124003cd6006492e18e1fc9130000", "I don't get the water", 0.698597133159637f, DateTime.Now, 1000);
                        int updatedId = new VoiceLoggingDAL(request.TenantId).UpdateTranscription(request.TranscriptionId, request.CallId, request.Transcription, request.ConfidenceScore, DateTime.Now, request.UserId);
                        return new VRResponse
                        {
                            Acknowledge = updatedId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                            TranscriptionId = updatedId,
                            Message = string.Format("Transcription has been updated successfully against callId:{0}.", request.CallId)
                        };
                    default:
                        break;
                }
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "SaveTranscription", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "SaveTranscription", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return null;
        }


        #region InqFileVideoBookmarks

        public VRResponse InqFileVideoBookmarks(string callId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "InqFileVideoBookmarks", tenantId, "InqFileVideoBookmarks function has been called successfully."));
                return new PageDAL(tenantId).InqFileVideoBookmarks(callId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "InqFileVideoBookmarks", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function InqFileVideoBookmarks().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "InqFileVideoBookmarks", tenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public VRResponse GetInqInspectionBookmark(string callId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetInqInspectionBookmark", tenantId, "GetInqInspectionBookmark function has been called successfully."));
                return new VRResponse { Inspection = new InquireRxDAL(tenantId).GetEventDetailsById(callId).Inspection };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetInqInspectionBookmark", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function GetInqInspectionBookmark().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetInqInspectionBookmark", tenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public VRResponse AddInqFileVideoBookmark(string callId, int position, string bookmark, string notes, int tenantId, int parentId, string fullPath)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "AddInqFileVideoBookmark", tenantId, "AddInqFileVideoBookmark function has been called successfully."));
                int bookmarkId = new PageDAL(tenantId).AddInqFileVideoBookmark(callId, position, bookmark, notes, parentId, fullPath);
                return new VRResponse
                {
                    Acknowledge = bookmarkId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    BookmarkId = bookmarkId
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "AddInqFileVideoBookmark", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure
                };
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function AddInqFileVideoBookmark().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "AddInqFileVideoBookmark", tenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure
                };
            }
        }

        public VRResponse AddInqInspectionBookmark(string callId, int position, string bookmark, string MarkerAnswer, string PictureName, string notes, int sectionid, int InspectionTemplateId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "AddInqInspectionBookmark", tenantId, "AddInqInspectionBookmark function has been called successfully."));
                int bookmarkId = new PageDAL(tenantId).AddInqInspectionBookmark(callId, position, bookmark, MarkerAnswer, PictureName, notes, sectionid, InspectionTemplateId);
                return new VRResponse { Acknowledge = bookmarkId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure, BookmarkId = bookmarkId };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "AddInqInspectionBookmark", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                return new VRResponse { Acknowledge = AcknowledgeType.Failure  };
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function AddInqInspectionBookmark().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "AddInqInspectionBookmark", tenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                return new VRResponse { Acknowledge = AcknowledgeType.Failure };
            }
        }
        
        public VRResponse AddIQ3VirtualBookmark(MarkerIQ3Virtual marker)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "AddIQ3VirtualBookmark", marker.TenantID, "AddIQ3VirtualBookmark function has been called successfully."));
                int bookmarkId = new PageDAL(marker.TenantID).AddIQ3VirtualBookmark(marker);
                return new VRResponse
                {
                    Acknowledge = bookmarkId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    BookmarkId = bookmarkId
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "AddIQ3VirtualBookmark", marker.TenantID, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure
                };
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function AddIQ3VirtualBookmark().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "AddIQ3VirtualBookmark", marker.TenantID, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure
                };
            }
        }

        public VRResponse InqFileVideoSaveEditedBookmark(string editedBookmark, int markerId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "InqFileVideoSaveEditedBookmark", tenantId, "InqFileVideoSaveEditedBookmark function has been called successfully."));
                bool success = new PageDAL(tenantId).InqFileVideoSaveEditedBookmark(editedBookmark, markerId);
                return new VRResponse
                {
                    Acknowledge = success ? AcknowledgeType.Success : AcknowledgeType.Failure
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "InqFileVideoSaveEditedBookmark", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure
                };
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function InqFileVideoSaveEditedBookmark().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "InqFileVideoSaveEditedBookmark", tenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure
                };
            }
        }
        public VRResponse InqFileVideoSaveEditedBookmarkNotes(string editedBookmark, int markerId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "InqFileVideoSaveEditedBookmarkNotes", tenantId, "InqFileVideoSaveEditedBookmarkNotes function has been called successfully."));
                bool success = new PageDAL(tenantId).InqFileVideoSaveEditedBookmarkNotes(editedBookmark, markerId);
                return new VRResponse
                {
                    Acknowledge = success ? AcknowledgeType.Success : AcknowledgeType.Failure
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "InqFileVideoSaveEditedBookmarkNotes", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure
                };
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function InqFileVideoSaveEditedBookmarkNotes().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "InqFileVideoSaveEditedBookmarkNotes", tenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure
                };
            }
        }
        public VRResponse DeleteInqFileVideoBookmark(int markerId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "DeleteInqFileVideoBookmark", tenantId, "DeleteInqFileVideoBookmark function has been called successfully."));
                bool success = new PageDAL(tenantId).DeleteInqFileVideoBookmark(markerId);
                return new VRResponse
                {
                    Acknowledge = success ? AcknowledgeType.Success : AcknowledgeType.Failure
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "DeleteInqFileVideoBookmark", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure
                };
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function DeleteInqFileVideoBookmark().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "DeleteInqFileVideoBookmark", tenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure
                };
            }
        }

        public VRResponse saveeventnotes(string saveeventnotes, string CallID, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "saveeventnotes", tenantId, "saveeventnotes function has been called successfully."));
                bool success = new PageDAL(tenantId).saveeventnotes(saveeventnotes, CallID);
                return new VRResponse
                {
                    Acknowledge = success ? AcknowledgeType.Success : AcknowledgeType.Failure
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "saveeventnotes", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure
                };
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function saveeventnotes().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "saveeventnotes", tenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure
                };
            }
        }

        #endregion

        #region Twilio
        public VRResponse GetChatTranscript(string eventId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetChatTranscript", tenantId, "GetChatTranscript function has been called successfully."));
                return new VRResponse
                {
                    ChatTranscript = new VoiceLoggingDAL(tenantId).GetChatTranscript(eventId)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetChatTranscript", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function GetChatTranscript().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetChatTranscript", tenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        #endregion

        public VRResponse GetAllSites(VRRequest vrRequest)
        {
            List<Recorder> sites = null;
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetAllSites", vrRequest.TenantId, "GetAllSites function has been called successfully."));
                sites = new VoiceLoggingDAL(vrRequest.TenantId).GetAllSites(vrRequest.IsEnterpriseRecorder);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetAllSites", vrRequest.TenantId, "GetAllSites(). All sites have been fetched successfully."));
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetAllSites", vrRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function GetAllSites().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetAllSites", vrRequest.TenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return new VRResponse { Sites = sites };
        }

        public VRResponse GetCallsByEventId(int eventId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetCallsByEventId", tenantId, "GetCallsByEventId function has been called successfully."));
                var dbCalls = new VoiceLoggingDAL(tenantId).GetCallsByEventId(eventId);
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    Calls = dbCalls,
                    Message = string.Format("Your eventId was {0}.", eventId)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetCallsByEventId", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function GetCallsByEventId().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetCallsByEventId", tenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public VRResponse GetEventByEventId(string eventId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetEventByEventId", tenantId, "GetEventByEventId function has been called successfully."));
                var eventInfo = new VoiceLoggingDAL(tenantId).GetEventByEventId(eventId);
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    CallInfo = eventInfo,
                    Message = string.Format("Your eventId was {0}.", eventId)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetEventByEventId", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function GetEventByEventId().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetEventByEventId", tenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public VRResponse FetchConversation(string jsonFilePath, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "FetchConversation", tenantId, "FetchConversation function has been called successfully."));
                VRResponse vrResponse = new VRResponse();
                vrResponse.Conversation = this.getConversation(jsonFilePath, tenantId);
                vrResponse.Acknowledge = AcknowledgeType.Success;
                return vrResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "FetchConversation", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function FetchConversation().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "FetchConversation", tenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        private Conversation getConversation(string jsonFilePath, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "getConversation", tenantId, "getConversation function has been called successfully."));

                JObject conversationJSONObject = JObject.Parse(File.ReadAllText(jsonFilePath));
                return JsonConvert.DeserializeObject<Conversation>(File.ReadAllText(jsonFilePath));
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "getConversation", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function getConversation().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "getConversation", tenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #region Schedule Event
        public VRResponse GetAllScheduledEvents(VRRequest vrRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetAllScheduledEvents", vrRequest.TenantId, "GetAllScheduledEvents function has been called successfully."));
                return new VRResponse
                {
                    ScheduledEvents = new VoiceLoggingDAL(vrRequest.TenantId).GetAllScheduledEvents(vrRequest.ScheduleEvent.ScheduledBy)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetAllScheduledEvents", vrRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function GetAllScheduledEvents().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetAllScheduledEvents", vrRequest.TenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }

        }
        public VRResponse GetScheduledEventDetails(VRRequest vrRequest)
        {
            Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetScheduledEventDetails", vrRequest.TenantId, "GetScheduledEventDetails function has been called successfully."));
            return new VRResponse
            {
                ScheduledEvent = new VoiceLoggingDAL(vrRequest.TenantId).GetScheduledEventDetails(vrRequest.ScheduleEvent.ScheduledBy, vrRequest.ScheduleEvent.EventId)
            };
        }
        public VRResponse DeleteAndGetAllScheduledEvents(VRRequest vrRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "DeleteAndGetAllScheduledEvents", vrRequest.TenantId, "DeleteAndGetAllScheduledEvents function has been called successfully."));
                return new VRResponse
                {
                    ScheduledEvents = new VoiceLoggingDAL(vrRequest.TenantId).DeleteAndGetAllScheduledEvents(vrRequest.ScheduleEvent.EventId, vrRequest.ScheduleEvent.ScheduledBy)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "DeleteAndGetAllScheduledEvents", vrRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function DeleteAndGetAllScheduledEvents().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "DeleteAndGetAllScheduledEvents", vrRequest.TenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public VRResponse SaveScheduledEventAndGetAll(VRRequest vrRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "SaveScheduledEventAndGetAll", vrRequest.TenantId, "SaveScheduledEventAndGetAll function has been called successfully."));
                int lastEventId = 0;
                var scheduledEvents = new VoiceLoggingDAL(vrRequest.TenantId).SaveScheduledEventAndGetAll(vrRequest.ScheduleEvent.EventId, out lastEventId, vrRequest.ScheduleEvent.EventName, vrRequest.ScheduleEvent.Duration, vrRequest.ScheduleEvent.ScheduledDateTime, vrRequest.ScheduleEvent.ScheduledBy, vrRequest.ScheduleEvent.ScheduledByEmail, vrRequest.ScheduleEvent.IsActive, vrRequest.ScheduleEvent.Participants, vrRequest.ScheduleEvent.IsVirtualInspection, vrRequest.ScheduleEvent.IsOnDemand, vrRequest.ScheduleEvent.EndUserPhoneNumber, vrRequest.ScheduleEvent.EndUserEmail, vrRequest.ScheduleEvent.RemoteInvitationLink, vrRequest.ScheduleEvent.MarkerTypeId, vrRequest.ScheduleEvent.CustomFields, vrRequest.ScheduleEvent.Notes, vrRequest.ScheduleEvent.InspectionTemplateId, vrRequest.ScheduleEvent.InspectionTemplateType, vrRequest.ScheduleEvent.PreInspectionDetails, vrRequest.ScheduleEvent.MGOReportData);

                return new VRResponse
                {
                    LastSavedId = lastEventId,
                    ScheduledEvents = scheduledEvents
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "SaveScheduledEventAndGetAll", vrRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "SaveScheduledEventAndGetAll", vrRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public VRResponse UpdateScheduledEventAndGetAll(VRRequest vrRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "UpdateScheduledEventAndGetAll", vrRequest.TenantId, "UpdateScheduledEventAndGetAll function has been called successfully."));
                return new VRResponse
                {
                    ScheduledEvents = new VoiceLoggingDAL(vrRequest.TenantId).UpdateScheduledEventAndGetAll(vrRequest.ScheduleEvent.EventId, vrRequest.ScheduleEvent.EventName, vrRequest.ScheduleEvent.Duration, vrRequest.ScheduleEvent.ScheduledDateTime, vrRequest.ScheduleEvent.ScheduledBy, vrRequest.ScheduleEvent.ScheduledByEmail, vrRequest.ScheduleEvent.IsActive, vrRequest.ScheduleEvent.Participants, vrRequest.ScheduleEvent.IsVirtualInspection, vrRequest.ScheduleEvent.EndUserPhoneNumber, vrRequest.ScheduleEvent.EndUserEmail, vrRequest.ScheduleEvent.RemoteInvitationLink, vrRequest.ScheduleEvent.MarkerTypeId, vrRequest.ScheduleEvent.CustomFields, vrRequest.ScheduleEvent.Notes, vrRequest.ScheduleEvent.InspectionTemplateId, vrRequest.ScheduleEvent.InspectionTemplateType, vrRequest.ScheduleEvent.PreInspectionDetails)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "UpdateScheduledEventAndGetAll", vrRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "UpdateScheduledEventAndGetAll", vrRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public List<ScheduleEventParticipant> FetchAllParticipant(VRRequest vrRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "FetchAllParticipant", vrRequest.TenantId, "FetchAllParticipant function has been called successfully."));
                return new VoiceLoggingDAL(vrRequest.TenantId).FetchAllParticipant(vrRequest.UserId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "FetchAllParticipant", vrRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "FetchAllParticipant", vrRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public List<ScheduleEventParticipant> FetchParticipantsByEventId(VRRequest vrRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "FetchParticipantsByEventId", vrRequest.TenantId, "FetchParticipantsByEventId function has been called successfully."));
                return new VoiceLoggingDAL(vrRequest.TenantId).FetchParticipantsByEventId(vrRequest.UserId, vrRequest.ScheduleEvent.EventId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "FetchParticipantsByEventId", vrRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "FetchParticipantsByEventId", vrRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public bool DeleteScheduledEvent(VRRequest vrRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "DeleteScheduledEvent", vrRequest.TenantId, "DeleteScheduledEvent function has been called successfully."));
                return new VoiceLoggingDAL(vrRequest.TenantId).DeleteScheduledEvent(vrRequest.ScheduleEvent.EventId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "DeleteScheduledEvent", vrRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "DeleteScheduledEvent", vrRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool IsScheduledEventInvitationActive(VRRequest vrRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "IsScheduledEventInvitationActive", vrRequest.TenantId, "IsScheduledEventInvitationActive function has been called successfully."));
                return new VoiceLoggingDAL(vrRequest.TenantId).IsScheduledEventInvitationActive(vrRequest.ScheduleEvent.EventId, vrRequest.ScheduleEvent.Participant.ParticipantEmail, vrRequest.ScheduleEvent.Participant.ParticipantPhoneNumber);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "IsScheduledEventInvitationActive", vrRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "IsScheduledEventInvitationActive", vrRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public string GetCalendarId(VRRequest vrRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetCalendarId", vrRequest.TenantId, "GetCalendarId function has been called successfully."));
                return new VoiceLoggingDAL(vrRequest.TenantId).GetCalendarId();
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetCalendarId", vrRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetCalendarId", vrRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public int InsertCalendarId(string calendarId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "InsertCalendarId", tenantId, "InsertCalendarId function has been called successfully."));
                return new VoiceLoggingDAL(tenantId).InsertCalendarId(calendarId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "InsertCalendarId", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "InsertCalendarId", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool UpdateCalendarEventId(int schduleEventId, string eventId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "UpdateCalendarEventId", tenantId, "UpdateCalendarEventId function has been called successfully."));
                return new VoiceLoggingDAL(tenantId).UpdateCalendarEventId(schduleEventId, eventId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "UpdateCalendarEventId", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "UpdateCalendarEventId", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        #endregion

        public VRResponse CallAuditSave(VRRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "CallAuditSave", request.TenantId, "CallAuditSave function has been called successfully."));
                string callId = request.CallAudit.CallId;
                int userNum = request.CallAudit.UserNum;
                bool isSaved = request.CallAudit.IsSaved;
                string ipAddress = request.CallAudit.IPAddress == null ? string.Empty : request.CallAudit.IPAddress;
                int tenantId = request.TenantId;

                if (request.Recorder.IsPrimary)
                {
                    int rowsAffected = new VoiceLoggingDAL(tenantId).CallAuditSave(callId, userNum, ipAddress, isSaved);
                    return new VRResponse { RowsAffected = rowsAffected, Acknowledge = AcknowledgeType.Success };
                }
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + request.Recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    int rowsAffected = entClient.CallAuditSave(callId, userNum, ipAddress, isSaved);
                    return new VRResponse { RowsAffected = rowsAffected, Acknowledge = AcknowledgeType.Success };
                }
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "CallAuditSave", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function CallAuditSave().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "CallAuditSave", request.TenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #region User Searches

        public VRResponse SaveUserSearch(VRRequest vRRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "SaveUserSearch", vRRequest.TenantId, "SaveUserSearch function has been called successfully."));
                int rowsAffected = new VoiceLoggingDAL(vRRequest.TenantId).InsertUserSearch(vRRequest.UserSearch);
                return new VRResponse
                {
                    Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    RowsAffected = rowsAffected,
                    UserSearch = vRRequest.UserSearch,
                    Message = string.Format("{0} saved the user searches", rowsAffected > 0 ? "Successfully" : "Fail to")
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "SaveUserSearch", vRRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "SaveUserSearch", vRRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public VRResponse GetUserSearches(VRRequest vRRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetUserSearches", vRRequest.TenantId, "GetUserSearches function has been called successfully."));
                var userSearches = new VoiceLoggingDAL(vRRequest.TenantId).GetUserSearches(vRRequest.UserId);
                return new VRResponse
                {
                    Acknowledge = userSearches != null ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    Message = string.Format("User Searches has been fetched against UserId:{0}.", vRRequest.UserId),
                    UserSearches = userSearches,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetUserSearches", vRRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetUserSearches", vRRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public VRResponse GetUserSearchById(int searchId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetUserSearchById", tenantId, "GetUserSearchById function has been called successfully."));
                var userSearchDetail = new VoiceLoggingDAL(tenantId).GetUserSearchById(searchId);
                return new VRResponse
                {
                    Acknowledge = userSearchDetail != null ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    Message = string.Format("User Search has been fetched against SearchId:{0}.", searchId),
                    UserSearch = userSearchDetail,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetUserSearchById", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetUserSearchById", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool DeleteUserSearch(int id, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "DeleteUserSearch", tenantId, "DeleteUserSearch function has been called successfully."));
                return new VoiceLoggingDAL(tenantId).DeleteUserSearch(id);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "DeleteUserSearch", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "DeleteUserSearch", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #endregion


        #region Custom Search

        public VRResponse PerformCustomSearch(VRRequest request)
        {
            List<CallInfo> dbCalls = null;
            int totalPages = 0;
            long totalRecords = 0;
            string durationStr = QueryConstants.DURATION_DEFAULT;
            string queryOptionSTR = string.Empty;

            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "PerformCustomSearch", request.TenantId, "PerformCustomSearch function has been called successfully."));

                var criteria = request.Criteria as CallCriteria;
                int i = request.UserId;

                if (criteria != null)
                {
                    #region ------- Duration String -------
                    if (criteria.StartDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null) || criteria.EndDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null))
                    {
                        durationStr = QueryConstants.DURATION_BETWEEN + criteria.StartDuration.TotalMilliseconds.ToString() + "' AND '" + criteria.EndDuration.TotalMilliseconds.ToString() + "'";
                    }

                    #endregion

                    #region ------- Search Restriction -------

                    if (criteria.RestrictionInHours > 0)
                    {
                        var dt = DateTime.Now.AddHours(-criteria.RestrictionInHours);

                        criteria.StartDate = dt;
                        criteria.EndDate = DateTime.Now;
                        criteria.StartTime = dt.TimeOfDay;
                        criteria.EndTime = TimeSpan.Parse(DateTime.Now.ToString("HH:mm:ss"));// TimeSpan.Parse(DateTime.Now.TimeOfDay.ToString("HH:mm:ss"));
                    }

                    #endregion
                }

                if (request.LoadOptions.Contains("SearchCallsInPrimaryDB"))
                {
                    queryOptionSTR = CustomSearchQueryBuilder.BuildQuery(request.CustomSearchCriteria.Conditions, request.CustomSearchCriteria.Operator); //buildCustomSearchCriteria(criteria);
                    dbCalls = new VoiceLoggingDAL(request.TenantId).SearchCallsPrimaryDB(request.PageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria, criteria.NoOfCalls);
                }

                if (request.LoadOptions.Contains("SearchCallsInChainDBs"))
                {
                    queryOptionSTR = CustomSearchQueryBuilder.BuildQuery(request.CustomSearchCriteria.Conditions, request.CustomSearchCriteria.Operator); //buildCustomSearchCriteria(criteria);
                    //dbCalls = new VoiceLoggingDAL(request.TenantId).SearchCallsChainedDBs(request.PageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria, criteria.NoOfCalls);
                    dbCalls = new VoiceLoggingDAL(request.TenantId).CustomSearchInPrimaryDB(request.PageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria, criteria.NoOfCalls);
                }

                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    Calls = dbCalls,
                    TotalPages = totalPages,
                    TotalRecords = totalRecords,
                    Message = queryOptionSTR
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "PerformCustomSearch", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "PerformCustomSearch", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                ex.HelpLink = "<:>" + queryOptionSTR;
                throw ex;
            }
        }

        public VRResponse PerformCustomSearchNewSP(VRRequest request)
        {
            List<CallInfo> dbCalls = null;
            int totalPages = 0;
            long totalRecords = 0;
            string durationStr = QueryConstants.DURATION_DEFAULT;
            string queryOptionSTR = string.Empty;

            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "PerformCustomSearchNewSP", request.TenantId, "PerformCustomSearchNewSP function has been called successfully."));
                var criteria = request.Criteria as CallCriteria;
                int i = request.UserId;

                if (criteria != null)
                {
                    #region ------- Duration String -------
                    if (criteria.StartDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null) || criteria.EndDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null))
                    {
                        durationStr = QueryConstants.DURATION_BETWEEN + criteria.StartDuration.TotalMilliseconds.ToString() + "' AND '" + criteria.EndDuration.TotalMilliseconds.ToString() + "'";
                    }

                    #endregion

                    #region ------- Search Restriction -------

                    if (criteria.RestrictionInHours > 0)
                    {
                        var dt = DateTime.Now.AddHours(-criteria.RestrictionInHours);

                        criteria.StartDate = dt;
                        criteria.EndDate = DateTime.Now;
                        criteria.StartTime = dt.TimeOfDay;
                        criteria.EndTime = TimeSpan.Parse(DateTime.Now.ToString("HH:mm:ss"));// TimeSpan.Parse(DateTime.Now.TimeOfDay.ToString("HH:mm:ss"));
                    }

                    #endregion
                }

                if (request.LoadOptions.Contains("SearchCallsPrimary"))
                {
                    queryOptionSTR = CustomSearchQueryBuilder.BuildQuery(request.CustomSearchCriteria.Conditions, request.CustomSearchCriteria.Operator); //buildCustomSearchCriteria(criteria);
                    dbCalls = new VoiceLoggingDAL(request.TenantId).CustomSearchInPrimaryDB(request.PageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria, criteria.NoOfCalls);
                }

                if (request.LoadOptions.Contains("SearchCallsInChainDBs"))
                {
                    queryOptionSTR = CustomSearchQueryBuilder.BuildQuery(request.CustomSearchCriteria.Conditions, request.CustomSearchCriteria.Operator); //buildCustomSearchCriteria(criteria);
                    //dbCalls = new VoiceLoggingDAL(request.TenantId).CustomSearchInPrimaryDB(request.PageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria, criteria.NoOfCalls);
                    dbCalls = new VoiceLoggingDAL(request.TenantId).CustomSearchInPrimaryDBSA(request.PageSize, request.PageNumber, out totalPages, out totalRecords, durationStr, queryOptionSTR, criteria, request.CustomSearchCriteria.Operator);
                }

                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    Calls = dbCalls,
                    TotalPages = totalPages,
                    TotalRecords = totalRecords,
                    Message = queryOptionSTR
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "PerformCustomSearchNewSP", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "PerformCustomSearchNewSP", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                ex.HelpLink = "<:>" + queryOptionSTR;
                throw ex;
            }
        }

        #endregion

        public VRResponse UpdateBookmarkNotes(int bookmarkId, string modifiedBookMarkNotes, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "UpdateBookmarkNotes", tenantId, "UpdateBookmarkNotes function has been called successfully."));
                bool success = new PageDAL(tenantId).UpdateBookmarkNotes(bookmarkId, modifiedBookMarkNotes);
                return new VRResponse
                {
                    Acknowledge = success ? AcknowledgeType.Success : AcknowledgeType.Failure
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "UpdateBookmarkNotes", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure
                };
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function UpdateBookmarkNotes().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "UpdateBookmarkNotes", tenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure
                };
            }
        }

        public VRResponse UpdateInspectionBookmarkNotes(int bookmarkId, string modifiedBookMarkNotes, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "UpdateBookmarkNotes", tenantId, "UpdateBookmarkNotes function has been called successfully."));
                bool success = new PageDAL(tenantId).UpdateInspectionBookmarkNotes(bookmarkId, modifiedBookMarkNotes);
                return new VRResponse
                {
                    Acknowledge = success ? AcknowledgeType.Success : AcknowledgeType.Failure
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "UpdateBookmarkNotes", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure
                };
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function UpdateBookmarkNotes().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "UpdateBookmarkNotes", tenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure
                };
            }

        }
        

        public VRResponse UpdateBookmark(int bookmarkId, string modifiedBookMark, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "UpdateBookmark", tenantId, "UpdateBookmark function has been called successfully."));
                bool success = new PageDAL(tenantId).UpdateBookmark(bookmarkId, modifiedBookMark);
                return new VRResponse
                {
                    Acknowledge = success ? AcknowledgeType.Success : AcknowledgeType.Failure
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "UpdateBookmark", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure
                };
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function UpdateBookmark().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "UpdateBookmark", tenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure
                };
            }
        }
        public VRResponse UpdateBookmarkMarkerAnswer(int bookmarkId, string modifiedMarkerAnswer, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "UpdateBookmarkMarkerAnswer", tenantId, "UpdateBookmark function has been called successfully."));
                bool success = new PageDAL(tenantId).UpdateBookmarkMarkerAnswer(bookmarkId, modifiedMarkerAnswer);
                return new VRResponse
                {
                    Acknowledge = success ? AcknowledgeType.Success : AcknowledgeType.Failure
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "UpdateBookmarkMarkerAnswer", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure
                };
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function UpdateBookmarkMarkerAnswer().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "UpdateBookmarkMarkerAnswer", tenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure
                };
            }
        }
        

        public VRResponse DeleteBookmark(int bookmarkId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "DeleteBookmark", tenantId, "DeleteBookmark function has been called successfully."));
                bool success = new PageDAL(tenantId).DeleteBookmark(bookmarkId);
                return new VRResponse
                {
                    Acknowledge = success ? AcknowledgeType.Success : AcknowledgeType.Failure
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "DeleteBookmark", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure
                };
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function DeleteBookmark().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "DeleteBookmark", tenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure
                };
            }
        }
        public VRResponse DeleteInspectionBookmark(int bookmarkId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "DeleteInspectionBookmark", tenantId, "DeleteBookmark function has been called successfully."));
                bool success = new PageDAL(tenantId).DeleteInspectionBookmark(bookmarkId);
                return new VRResponse
                {
                    Acknowledge = success ? AcknowledgeType.Success : AcknowledgeType.Failure
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "DeleteInspectionBookmark", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure
                };
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function DeleteInspectionBookmark().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "DeleteInspectionBookmark", tenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure
                };
            }
        }
        

        public VRResponse UpdateEventName(string eventId, string eventName, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "UpdateEventName", tenantId, "UpdateEventName function has been called successfully."));
                bool success = new PageDAL(tenantId).UpdateEventName(eventId, eventName);
                return new VRResponse
                {
                    Acknowledge = success ? AcknowledgeType.Success : AcknowledgeType.Failure
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "UpdateEventName", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure
                };
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function UpdateEventName().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "UpdateEventName", tenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure
                };
            }
        }
        

        public VRResponse GetEventCurrentStatus(string eventId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetEventCurrentStatus", tenantId, "GetEventCurrentStatus function has been called successfully."));
                string EventStatus = new PageDAL(tenantId).GetEventCurrentStatus(eventId);
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    EventStatus = EventStatus,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetEventCurrentStatus", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure
                };
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing function GetEventCurrentStatus().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetEventCurrentStatus", tenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Failure
                };
            }
        }

        public Tuple<string, string> GetAppUserCredentialsByExt(int extId, int tenantId)
        {
            return new VoiceLoggingDAL(tenantId).GetAppUserCredentialsByExt(extId);
        }

        #region Multi-Call Evaluation
        public VRResponse SaveMultiCallEvaluation(VRRequest vRRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "SaveMultiCallEvaluation", vRRequest.TenantId, "SaveMultiCallEvaluation function has been called successfully."));
                int rowsAffected = new VoiceLoggingDAL(vRRequest.TenantId).SaveMultiCallEvaluation(vRRequest.MultiCallEvaluationGroup);
                return new VRResponse
                {
                    Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    RowsAffected = rowsAffected,
                    GroupId = vRRequest.MultiCallEvaluationGroup.Id,
                    Message = string.Format("{0} saved the multi-call evaluations", rowsAffected > 0 ? "Successfully" : "Fail to")
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "SaveMultiCallEvaluation", vRRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "SaveMultiCallEvaluation", vRRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        #endregion
        #region Channel Configuration
        public VRResponse FetchTenantGateways(VRRequest vrRequest)
        {
            Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "FetchTenantGateways", vrRequest.TenantId, "FetchTenantGateways function has been called successfully."));
            return new VRResponse
            {
                TenantGateways = new VoiceLoggingDAL(vrRequest.TenantId).FetchTenantGateways()
            };
        }
        public VRResponse SaveTenantGateway(VRRequest vRRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "SaveTenantGateway", vRRequest.TenantId, "SaveTenantGateway function has been called successfully."));
                int rowsAffected = new VoiceLoggingDAL(vRRequest.TenantId).SaveTenantGateway(vRRequest.TenantGateway);
                vRRequest.TenantGateway.CreatedDate = DateTime.Now;
                return new VRResponse
                {
                    Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    RowsAffected = rowsAffected,
                    TenantGateway = vRRequest.TenantGateway,
                    Message = string.Format("{0} saved the tenant gateway", rowsAffected > 0 ? "Successfully" : "Fail to")
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "SaveTenantGateway", vRRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "SaveTenantGateway", vRRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public VRResponse GetCustomFields(VRRequest vRRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetCustomFields", vRRequest.TenantId, "SaveMultiCallEvaluation function has been called successfully."));
                var result = new VoiceLoggingDAL(vRRequest.TenantId).GetCustomFields(vRRequest.UserId);
                return new VRResponse
                {
                    Acknowledge = result.Count > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    CustomFields = result
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetCustomFields", vRRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetCustomFields", vRRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public VRResponse UpdateTenantGateway(VRRequest vRRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "UpdateTenantGateway", vRRequest.TenantId, "UpdateTenantGateway function has been called successfully."));
                int rowsAffected = new VoiceLoggingDAL(vRRequest.TenantId).UpdateTenantGateway(vRRequest.TenantGateway);
                return new VRResponse
                {
                    Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    RowsAffected = rowsAffected,
                    TenantGateway = vRRequest.TenantGateway,
                    Message = string.Format("{0} saved the tenant gateway", rowsAffected > 0 ? "Successfully" : "Fail to")
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "UpdateTenantGateway", vRRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "UpdateTenantGateway", vRRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public VRResponse DeleteTenantGateway(VRRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "DeleteTenantGateway", request.TenantId, "DeleteTenantGateway function has been called successfully."));
                int rowsAffected = new VoiceLoggingDAL(request.TenantId).DeleteTenantGateway(request.TenantGateway.Id, request.TenantGateway.GatewayId, request.TenantGateway.TenantId);

                return new VRResponse
                {
                    Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    RowsAffected = rowsAffected,
                    Message = rowsAffected > 0 ? string.Format("Tenant gateway has been deleted successfully against Id:{0}.", request.TenantGateway.Id) : "Something went wrong."
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "DeleteTenantGateway", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "DeleteTenantGateway", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public VRResponse FetchAvailableTenantGateways(VRRequest vrRequest)
        {
            Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "FetchAvailableTenantGateways", vrRequest.TenantId, "FetchAvailableTenantGateways function has been called successfully."));
            return new VRResponse
            {
                TenantGateways = new VoiceLoggingDAL(vrRequest.TenantId).FetchAvailableTenantGateways()
            };
        }
        #endregion

        public bool VerifyInvitationAccessCode(int invitationId, int tenatId, string accessCode)
        {
            try
            {
                return new VoiceLoggingDAL(tenatId).VerifyInvitationAccessCode(invitationId, accessCode);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public VRResponse FetchPreInspectionData(VRRequest vrRequest)
        {
            try
            {
                var preInspectionDataList = new VoiceLoggingDAL(vrRequest.TenantId).FetchPreInspectionData(vrRequest.EventId);
                return new VRResponse { PreInspectionDataList = preInspectionDataList, Acknowledge = AcknowledgeType.Success };
            }
            catch (Exception ex)
            {
                return new VRResponse { PreInspectionDataList = null, Acknowledge = AcknowledgeType.Failure, Message = ex.Message };
            }
        }


        public VRResponse GetEventsByAssetId(VRRequest request)
        {
            List<CallInfo> dbCalls = null;
            int totalPages = 0;
            long totalRecords = 0;

            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceLogging, "GetEventsByAssetId", request.TenantId, "GetEventsByAssetId function has been called successfully."));
                
                dbCalls = new VoiceLoggingDAL(request.TenantId).GetEventsByAssetId(request.AssetId, request.PageSize, request.PageNumber, out totalPages, out totalRecords);

                return new VRResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    Calls = dbCalls,
                    TotalPages = totalPages,
                    TotalRecords = totalRecords,
                    Message = "GetEventsByAssetId executed successfully",
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetEventsByAssetId", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while processing the function GetEventsByAssetId().";
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetEventsByAssetId", request.TenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                ex.HelpLink = "<:>" + "GetEventsByAssetId";
                throw ex;
            }
        }
    }
}