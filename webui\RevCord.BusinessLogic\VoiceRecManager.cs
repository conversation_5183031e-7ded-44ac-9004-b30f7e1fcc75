﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.DataAccess;
using RevCord.DataContracts.Request;
using RevCord.DataContracts.Response;
using RevCord.DataContracts.DTO;
using RevCord.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.ViewModelEntities;
using System.Threading.Tasks;
using System.Data.SqlClient;
using RevCord.DataAccess.Util;

namespace RevCord.BusinessLogic
{
    public class VoiceRecManager
    {
        private readonly static string _serverIP = SiteConfig.HelperServiceAddress; //"127.0.0.1";

        #region CallInfo

        public bool SaveVoiceRecDBCallsInSurveyDB(string callIds, int userId, int surveyId, int tenantId, int revsyncSurveyId) //TODO Will be deleted
        {
            try
            {
                //1. Get List of CallsById's from VoiceRecDB
                //List<CallInfo> voicerecDBCalls = new VoiceRecDAL().GetCallInfosByCallIds(callIds);
                //2. Pass this List<Calls> to EvaluationDAL
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "SaveVoiceRecDBCallsInSurveyDB", tenantId, "SaveVoiceRecDBCallsInSurveyDB function has been called successfully."));

                if (new VoiceRecDAL(tenantId).InsertCalls(callIds, userId, surveyId, revsyncSurveyId))
                    return true;

                return false;
            }
            catch (SqlException sqle)
            {   
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "SaveVoiceRecDBCallsInSurveyDB", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "SaveVoiceRecDBCallsInSurveyDB", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool SaveVoiceRecDBCallsInSurveyDBOnRecorder(string callIds, int userId, int surveyId, Recorder recorder, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "SaveVoiceRecDBCallsInSurveyDBOnRecorder", tenantId, "SaveVoiceRecDBCallsInSurveyDBOnRecorder function has been called successfully."));
                if (recorder.IsPrimary)
                {
                    if (new VoiceLoggingDALEC(tenantId).InsertCallsOnRecorder(callIds, userId, surveyId, recorder))
                        return true;
                }
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    // Instead of passing userId, pass here user num of administrator.
                    int adminUserNum = 1000;
                    if (entClient.InsertCallsForEvaluation(callIds, adminUserNum, surveyId))
                        return true;
                    Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "SaveVoiceRecDBCallsInSurveyDBOnRecorder", tenantId, "SaveVoiceRecDBCallsInSurveyDBOnRecorder(). Calls added successfully for evaluation on recorder. Recorder Id = " + recorder.Id + " , Recorder Name = " + recorder.Name));
                }
                return false;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "SaveVoiceRecDBCallsInSurveyDBOnRecorder", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An exception has occurred while calling the function SaveVoiceRecDBCallsInSurveyDBOnRecorder. Recorder Id = " + recorder.Id + ", Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "SaveVoiceRecDBCallsInSurveyDBOnRecorder", tenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public VoiceRecResponse GetSimpleUserSearchValues(VoiceRecRequest voiceRecRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "GetSimpleUserSearchValues", voiceRecRequest.TenantId, "GetSimpleUserSearchValues function has been called successfully."));
                return new VoiceRecResponse
                {
                    SimpleUserSearchValues = new VoiceRecDAL(voiceRecRequest.TenantId).GetSimpleUserSearchCriteria(voiceRecRequest.SimpleUserSearchCriteria)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetSimpleUserSearchValues", voiceRecRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetSimpleUserSearchValues", voiceRecRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public VoiceRecResponse CreateAndGetCallSearchResults(VoiceRecRequest voiceRecRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "CreateAndGetCallSearchResults", voiceRecRequest.TenantId, "CreateAndGetCallSearchResults function has been called successfully."));
                int totalPages = 0;
                DateTime startDate = DateTime.Now;
                string sur = null;

                if (voiceRecRequest.SearchCriteria.SearchRest > 0)
                {
                    voiceRecRequest.SearchCriteria.EndTime = TimeSpan.Parse(DateTime.Now.ToString("HH:mm:ss"));
                    voiceRecRequest.SearchCriteria.StartTime = this.GetStartTime(voiceRecRequest.SearchCriteria.SearchRest, out startDate, voiceRecRequest.TenantId);
                    voiceRecRequest.SearchCriteria.StartDate = startDate;
                }
                else
                {
                    if (voiceRecRequest.SearchCriteria.StartTime == TimeSpan.Parse("00:00:00") && voiceRecRequest.SearchCriteria.EndTime == TimeSpan.Parse("00:00:00"))
                    {
                        voiceRecRequest.SearchCriteria.EndTime = TimeSpan.Parse("23:59:59");
                    }
                }

                voiceRecRequest.SearchCriteria.Criteria = " AND ( CI.StartTime IS NOT NULL ) ";
                if (voiceRecRequest.UserType == 1)
                {
                    sur = string.Format(" AND {0} IN ({1})", voiceRecRequest.SimpleUserSearchCriteria.Type == 4 ? "CI.Ext " : "CI.UserNum",
                        voiceRecRequest.SimpleUserSearchValues);
                    voiceRecRequest.SearchCriteria.Criteria += sur;
                }
                else if (voiceRecRequest.UserType == 0)
                    voiceRecRequest.SearchCriteria.Criteria += " AND (" + createSearchGroupClause(voiceRecRequest.GroupExtensions, voiceRecRequest.TenantId) + ")";
                return new VoiceRecResponse
                {
                    CallInfoSearchResultsDTO = new VoiceRecDAL(voiceRecRequest.TenantId).CreateAndGetCallSearchResults(voiceRecRequest.PageSize, voiceRecRequest.PageNumber, out totalPages, voiceRecRequest.SearchCriteria),
                    TotalPages = totalPages
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "CreateAndGetCallSearchResults", voiceRecRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "CreateAndGetCallSearchResults", voiceRecRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public VoiceRecResponse GetCallSearchResults(VoiceRecRequest voiceRecRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "GetCallSearchResults", voiceRecRequest.TenantId, "GetCallSearchResults function has been called successfully."));

                voiceRecRequest.SearchCriteria.StartDate = new DateTime(voiceRecRequest.SearchCriteria.StartDate.Year,
                                                                  voiceRecRequest.SearchCriteria.StartDate.Month,
                                                                  voiceRecRequest.SearchCriteria.StartDate.Day,
                                                                  voiceRecRequest.SearchCriteria.StartTime.Hours,
                                                                  voiceRecRequest.SearchCriteria.StartTime.Minutes,
                                                                  voiceRecRequest.SearchCriteria.StartTime.Seconds);

                if (voiceRecRequest.SearchCriteria.EndTime == TimeSpan.Parse("00:00:00"))
                {
                    voiceRecRequest.SearchCriteria.EndDate = new DateTime(voiceRecRequest.SearchCriteria.EndDate.Year,
                                                                      voiceRecRequest.SearchCriteria.EndDate.Month,
                                                                      voiceRecRequest.SearchCriteria.EndDate.Day,
                                                                      23,
                                                                      59,
                                                                      59);
                }
                else
                {
                    voiceRecRequest.SearchCriteria.EndDate = new DateTime(voiceRecRequest.SearchCriteria.EndDate.Year,
                                                                      voiceRecRequest.SearchCriteria.EndDate.Month,
                                                                      voiceRecRequest.SearchCriteria.EndDate.Day,
                                                                      voiceRecRequest.SearchCriteria.EndTime.Hours,
                                                                      voiceRecRequest.SearchCriteria.EndTime.Minutes,
                                                                      voiceRecRequest.SearchCriteria.EndTime.Seconds);
                }



                voiceRecRequest.SearchCriteria.DurationStr = QueryConstants.DURATION_DEFAULT;
                if (voiceRecRequest.SearchCriteria.StartDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null) || voiceRecRequest.SearchCriteria.EndDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null))
                    voiceRecRequest.SearchCriteria.DurationStr = QueryConstants.DURATION_BETWEEN + Convert.ToDateTime(voiceRecRequest.SearchCriteria.StartDuration).TimeOfDay.TotalMilliseconds.ToString() + "' AND '" + Convert.ToDateTime(voiceRecRequest.SearchCriteria.EndDuration).TimeOfDay.TotalMilliseconds.ToString() + "'";

                voiceRecRequest.SearchCriteria.Criteria = " AND(CI.StartTime IS NOT NULL)";

                return new VoiceRecResponse
                {
                    CallInfoSearchResults = new VoiceRecDAL(voiceRecRequest.TenantId).GetCallSearchResults(voiceRecRequest.SearchCriteria),
                    TotalPages = 0
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetCallSearchResults", voiceRecRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetCallSearchResults", voiceRecRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public VoiceRecResponse GetCallSearchResultsPaged(VoiceRecRequest voiceRecRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "GetCallSearchResultsPaged", voiceRecRequest.TenantId, "GetCallSearchResultsPaged function has been called successfully."));
                int totalPages = 0;
                long totalRecords = 0;
                DateTime startDate = DateTime.Now;

                bool extExists = false;

                if (voiceRecRequest.GroupExtensions != null)
                    extExists = IsExtExist(voiceRecRequest.GroupExtensions, voiceRecRequest.TenantId);

                voiceRecRequest.SearchCriteria.DurationStr = QueryConstants.DURATION_DEFAULT;

                if (voiceRecRequest.SearchCriteria.StartDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null) || voiceRecRequest.SearchCriteria.EndDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null))
                    voiceRecRequest.SearchCriteria.DurationStr = QueryConstants.DURATION_BETWEEN + voiceRecRequest.SearchCriteria.StartDuration.TotalMilliseconds.ToString() + "' AND '" + voiceRecRequest.SearchCriteria.EndDuration.TotalMilliseconds.ToString() + "'";
                /*Shift Restriction is Enable*/
                if (voiceRecRequest.SearchCriteria.HasShiftRest)
                {
                    if (voiceRecRequest.SearchCriteria.SearchRest == 0)
                    {
                        voiceRecRequest.SearchCriteria.EndTime = TimeSpan.Parse(DateTime.Now.ToString("HH:mm:ss"));
                        voiceRecRequest.SearchCriteria.StartTime = TimeSpan.Parse(voiceRecRequest.SearchCriteria.LoginTime.ToString("HH:mm:ss"));
                        if (Convert.ToInt16(DateTime.Now.ToString("HH")) - Convert.ToInt16(voiceRecRequest.SearchCriteria.StartTime.Hours) < 0)
                        {
                            int totalHours = Convert.ToInt16(DateTime.Now.ToString("HH")) - Convert.ToInt16(voiceRecRequest.SearchCriteria.StartTime.Hours) + 24;
                            voiceRecRequest.SearchCriteria.StartDate = DateTime.Now.AddDays(-1);
                        }
                    }
                    else
                    {
                        voiceRecRequest.SearchCriteria.EndTime = TimeSpan.Parse(DateTime.Now.ToString("HH:mm:ss"));
                        voiceRecRequest.SearchCriteria.StartTime = TimeSpan.Parse(voiceRecRequest.SearchCriteria.LoginTime.ToString("HH:mm:ss"));
                        if (Convert.ToInt16(DateTime.Now.ToString("HH")) - Convert.ToInt16(voiceRecRequest.SearchCriteria.StartTime.Hours) < 0)
                        {
                            int totalHours = Convert.ToInt16(DateTime.Now.ToString("HH")) - Convert.ToInt16(voiceRecRequest.SearchCriteria.StartTime.Hours) + 24;
                            voiceRecRequest.SearchCriteria.StartDate = DateTime.Now.AddDays(-1);
                        }
                    }
                }
                /*Search Restriction is Enable*/
                else
                {
                    if (voiceRecRequest.SearchCriteria.SearchRest > 0)
                    {
                        voiceRecRequest.SearchCriteria.EndTime = TimeSpan.Parse(DateTime.Now.ToString("HH:mm:ss"));
                        voiceRecRequest.SearchCriteria.StartTime = this.GetStartTime(voiceRecRequest.SearchCriteria.SearchRest, out startDate, voiceRecRequest.TenantId);
                        voiceRecRequest.SearchCriteria.StartDate = startDate;
                    }
                    else
                    {
                        if (voiceRecRequest.SearchCriteria.StartTime == TimeSpan.Parse("00:00:00") && voiceRecRequest.SearchCriteria.EndTime == TimeSpan.Parse("00:00:00"))
                        {
                            voiceRecRequest.SearchCriteria.EndTime = TimeSpan.Parse("23:59:59");
                        }
                    }
                }

                voiceRecRequest.SearchCriteria.Criteria = " AND(CI.StartTime IS NOT NULL)";
                if (voiceRecRequest.UseGroup && voiceRecRequest.UserType != 1)
                    voiceRecRequest.SearchCriteria.Criteria += " AND (" + createSearchGroupClause(voiceRecRequest.GroupExtensions, voiceRecRequest.TenantId) + ")";
                //start: Used in case of Simple user rights
                else if (voiceRecRequest.UserType == 1)
                {
                    if (extExists)
                        voiceRecRequest.SearchCriteria.Criteria += " AND (" + createSearchGroupClause(voiceRecRequest.GroupExtensions.FindAll(e => e.Length >= 7), voiceRecRequest.TenantId) + ")";
                    else if (voiceRecRequest.UseGroup && !extExists)
                    {
                        string simpleUserAssingedChnCrieteria = string.Format(" AND {0} IN ({1})",
                                                                                voiceRecRequest.SimpleUserSearchCriteria.Type == 4 ? "CI.Ext " : "CI.UserNum",
                                                                                "-9999");
                        voiceRecRequest.SearchCriteria.Criteria += simpleUserAssingedChnCrieteria;

                    }
                    else
                    {
                        string simpleUserAssingedChnCrieteria = string.Format(" AND {0} IN ({1})",
                                                                                voiceRecRequest.SimpleUserSearchCriteria.Type == 4 ? "CI.Ext " : "CI.UserNum",
                                                                                voiceRecRequest.SimpleUserSearchValues);
                        voiceRecRequest.SearchCriteria.Criteria += simpleUserAssingedChnCrieteria;
                    }
                }

                if (voiceRecRequest.UseAdvanceSearch)
                    voiceRecRequest.SearchCriteria.Criteria += " AND (" + createAdvanceSearchClause(voiceRecRequest.SearchCriteria.AdvanceSearchData, voiceRecRequest.TenantId) + ")";

                if (!string.IsNullOrEmpty(voiceRecRequest.SearchCriteria.StartTimeString))
                    voiceRecRequest.SearchCriteria.Criteria += " AND CI.StartTime < " + voiceRecRequest.SearchCriteria.StartTimeString;

                return new VoiceRecResponse
                {
                    CallInfoSearchResults = new VoiceRecDAL(voiceRecRequest.TenantId).GetCallSearchResultsPaged(voiceRecRequest.PageSize, voiceRecRequest.PageNumber, out totalPages, out totalRecords, voiceRecRequest.SearchCriteria),
                    TotalPages = totalPages,
                    TotalRecords = totalRecords
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetCallSearchResultsPaged", voiceRecRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetCallSearchResultsPaged", voiceRecRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public VoiceRecResponse GetCallsByLocation(VoiceRecRequest voiceRecRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "GetCallsByLocation", voiceRecRequest.TenantId, "GetCallsByLocation function has been called successfully."));
                voiceRecRequest.SearchCriteria.DurationStr = QueryConstants.DURATION_DEFAULT;
                if (voiceRecRequest.SearchCriteria.StartDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null) || voiceRecRequest.SearchCriteria.EndDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null))
                    voiceRecRequest.SearchCriteria.DurationStr = QueryConstants.DURATION_BETWEEN + voiceRecRequest.SearchCriteria.StartDuration.TotalMilliseconds.ToString() + "' AND '" + voiceRecRequest.SearchCriteria.EndDuration.TotalMilliseconds.ToString() + "'";
                if (voiceRecRequest.SearchCriteria.StartTime == TimeSpan.Parse("00:00:00") && voiceRecRequest.SearchCriteria.EndTime == TimeSpan.Parse("00:00:00"))
                    voiceRecRequest.SearchCriteria.EndTime = TimeSpan.Parse("23:59:59");

                voiceRecRequest.SearchCriteria.Criteria = " AND(CI.StartTime IS NOT NULL)";

                return new VoiceRecResponse
                {
                    CallInfoSearchResults = new VoiceRecDAL(voiceRecRequest.TenantId).GetCallsByLocation(voiceRecRequest.SearchCriteria),
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetCallsByLocation", voiceRecRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetCallsByLocation", voiceRecRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        public List<CallInfo> GetCallsByIds(string callIds, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "GetCallsByIds", tenantId, "GetCallsByIds function has been called successfully."));
                return new VoiceRecDAL(tenantId).GetCallsByIds(callIds);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetCallsByIds", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetCallsByIds", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public VoiceRecResponse GetCallInfosByCommaSeperatedIds(string callIds, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "GetCallInfosByCommaSeperatedIds", tenantId, "GetCallInfosByCommaSeperatedIds function has been called successfully."));

                List<CallInfo> calls = new VoiceRecDAL(tenantId).GetCallsByCommaSeperatedIds(callIds);
                return new VoiceRecResponse
                {
                    CallInfoSearchResults = calls,
                    FlagStatus = calls.Count > 0 ? true : false //ci.Duration > 0 ? true : false,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetCallInfosByCommaSeperatedIds", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetCallInfosByCommaSeperatedIds", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public VoiceRecResponse GetCallInfoById(string callId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "GetCallInfoById", tenantId, "GetCallInfoById function has been called successfully."));
                CallInfo ci = new VoiceRecDAL(tenantId).GetCallInfoById(callId);
                //if (ci != null){
                return new VoiceRecResponse
                {
                    CallInfo = ci,
                    FlagStatus = ci != null ? true : false //ci.Duration > 0 ? true : false,
                };
                //}
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetCallInfoById", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetCallInfoById", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public VoiceRecResponse getRapidSOSMapDetails(string callId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "getRapidSOSMapDetails", tenantId, "getRapidSOSMapDetails function has been called successfully."));
                List<RapidSOSGPSData> RapidSOSGPSData = new VoiceRecDAL(tenantId).getRapidSOSMapDetails(callId);
                //if (ci != null){
                return new VoiceRecResponse
                {
                    RapidSOSGPSDatas = RapidSOSGPSData
                };
                //}
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "getRapidSOSMapDetails", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "getRapidSOSMapDetails", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        
        public bool UpdateCallInfoCustomFields(VoiceRecRequest voiceRecRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "UpdateCallInfoCustomFields", voiceRecRequest.TenantId, "UpdateCallInfoCustomFields function has been called successfully."));
                return new VoiceRecDAL(voiceRecRequest.TenantId).UpdateCallInfoCustomFields(voiceRecRequest.CallInfo);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "UpdateCallInfoCustomFields", voiceRecRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "UpdateCallInfoCustomFields", voiceRecRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public VoiceRecResponse UpdateCallInfoFields(string callIds, string fieldName, string fieldText, int userId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "UpdateCallInfoFields", tenantId, "UpdateCallInfoFields function has been called successfully."));
                int fieldType = -1;
                switch (fieldName)
                {
                    case "colCallComments": //CALL_COMMENT
                        fieldType = 0;
                        break;
                    case "colCustInfo1": //CustName
                        fieldType = 5;
                        break;
                    case "colCustInfo2": //ANI
                        fieldType = 6;
                        break;
                    case "colCustInfo3": //Tag2
                        fieldType = 2;
                        break;
                    case "colCustInfo4": //Tag3
                        fieldType = 3;
                        break;
                    case "colCustInfo5": //Tag4
                        fieldType = 4;
                        break;
                    case "colCallTag": //Tag1
                        fieldType = 1;
                        break;
                    case "colCalledId": //CalledId
                        fieldType = 7;
                        break;
                        //default:
                        //    fieldType = -1;
                        //    break;
                }
                return new VoiceRecResponse
                {
                    StatusFromDB = new VoiceRecDAL(tenantId).UpdateCallInfoFields(callIds, fieldType, fieldText, userId)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "UpdateCallInfoFields", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "UpdateCallInfoFields", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public VoiceRecResponse UpdateCallInfoRetainValue(string callId, bool retainValue, int userId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "UpdateCallInfoRetainValue", tenantId, "UpdateCallInfoRetainValue function has been called successfully. callId = " + callId));
                return new VoiceRecResponse
                {
                    StatusReceivedFromDB = new VoiceRecDAL(tenantId).UpdateCallInfoRetainValue(callId, retainValue, userId)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "UpdateCallInfoRetainValue", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "UpdateCallInfoRetainValue", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        private string createSearchGroupClause(List<string> groupExtKeys, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "createSearchGroupClause", tenantId, "createSearchGroupClause function has been called successfully. "));
                return QueryHelper.BuildGroupExtWhereClause(groupExtKeys, "CI", "CI");
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "createSearchGroupClause", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "createSearchGroupClause", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        private string createSearchGroupClause(List<ExtensionCallInfo> groupExtKeys, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "createSearchGroupClause", tenantId, "createSearchGroupClause function has been called successfully. "));
                return IntermediateQueryHelper.BuildGroupExtWhereClause(groupExtKeys, "CI", "CI");
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "createSearchGroupClause", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "createSearchGroupClause", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        private string createAdvanceSearchClause(string dataToSearch, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "createAdvanceSearchClause", tenantId, "createAdvanceSearchClause function has been called successfully. dataToSearch = " + dataToSearch));
                string whereClause = string.Empty;
                StringBuilder sbWhereClause = new StringBuilder();
                sbWhereClause.Append(" (CI.Ext LIKE N'%" + dataToSearch + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.Append(" (CI.RetainValue LIKE N'%" + dataToSearch + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.Append(" (CI.CustName LIKE N'%" + dataToSearch + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.Append(" (CI.ANI LIKE N'%" + dataToSearch + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.Append(" (CI.Tag2 LIKE N'%" + dataToSearch + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.Append(" (CI.Tag3 LIKE N'%" + dataToSearch + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.Append(" (CI.Tag4 LIKE N'%" + dataToSearch + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.Append(" (CI.CalledID LIKE N'%" + dataToSearch + "%')");
                sbWhereClause.Append(" OR ");

                sbWhereClause.Append(" (CI.CALL_COMMENT LIKE N'%" + dataToSearch + "%')");
                sbWhereClause.Append(" OR ");
                sbWhereClause.Append(" ((select count(*) from t_BookMark BM where CI.CallID = BM.CallID and BM.bookmarktext LIKE N'%" + dataToSearch + "%' ) > 0)");

                return sbWhereClause.ToString();
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "createSearchGroupClause", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "createSearchGroupClause", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #endregion


        #region Mix Call's With E-MAIL, SOCIAL, SMS -{for demo purpose only-will be deleted in future}

        ///// Mix Dummy Data with CallInfo -{for demo purpose only}

        public VoiceRecResponse GetCallSearchResultsPagedMixedWithDummyData(VoiceRecRequest voiceRecRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "GetCallSearchResultsPagedMixedWithDummyData", voiceRecRequest.TenantId, "GetCallSearchResultsPagedMixedWithDummyData function has been called successfully. "));
                int totalPages = 0;
                long totalRecords = 0;

                int totalPagesDemo = 0;
                long totalRecordsDemo = 0;
                DateTime startDate = DateTime.Now;
                int actualPageSize = GetActualDataPageSize(voiceRecRequest.PageSize, voiceRecRequest.GroupExtensions, voiceRecRequest.TenantId);

                bool extExists = false;

                if (voiceRecRequest.GroupExtensions != null)
                    extExists = IsExtExist(voiceRecRequest.GroupExtensions, voiceRecRequest.TenantId);

                voiceRecRequest.SearchCriteria.DurationStr = QueryConstants.DURATION_DEFAULT;


                if (voiceRecRequest.SearchCriteria.StartDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null) || voiceRecRequest.SearchCriteria.EndDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null))
                    voiceRecRequest.SearchCriteria.DurationStr = QueryConstants.DURATION_BETWEEN + voiceRecRequest.SearchCriteria.StartDuration.TotalMilliseconds.ToString() + "' AND '" + voiceRecRequest.SearchCriteria.EndDuration.TotalMilliseconds.ToString() + "'";
                //else if (voiceRecRequest.SearchCriteria.StartDuration == TimeSpan.Parse("00:00:00") && voiceRecRequest.SearchCriteria.EndDuration == TimeSpan.Parse("00:00:00"))
                //    voiceRecRequest.SearchCriteria.DurationStr = QueryConstants.DURATION_BETWEEN + "000000" + "' AND '" + "235959" + "'";

                if (voiceRecRequest.SearchCriteria.SearchRest > 0)
                {
                    voiceRecRequest.SearchCriteria.EndTime = TimeSpan.Parse(DateTime.Now.ToString("HH:mm:ss"));
                    voiceRecRequest.SearchCriteria.StartTime = this.GetStartTime(voiceRecRequest.SearchCriteria.SearchRest, out startDate, voiceRecRequest.TenantId);
                    voiceRecRequest.SearchCriteria.StartDate = startDate;
                }
                else
                {
                    if (voiceRecRequest.SearchCriteria.StartTime == TimeSpan.Parse("00:00:00") && voiceRecRequest.SearchCriteria.EndTime == TimeSpan.Parse("00:00:00"))
                    {
                        voiceRecRequest.SearchCriteria.EndTime = TimeSpan.Parse("23:59:59");

                    }
                }
                voiceRecRequest.SearchCriteria.Criteria = " AND(CI.StartTime IS NOT NULL)";

                if (voiceRecRequest.UseGroup && voiceRecRequest.UserType != 1)
                    voiceRecRequest.SearchCriteria.Criteria += " AND (" + createSearchGroupClause(voiceRecRequest.GroupExtensions, voiceRecRequest.TenantId) + ")";
                //start: Used in case of Simple user rights
                else if (voiceRecRequest.UserType == 1)
                {
                    if (extExists)
                        voiceRecRequest.SearchCriteria.Criteria += " AND (" + createSearchGroupClause(voiceRecRequest.GroupExtensions.FindAll(e => e.Length >= 7), voiceRecRequest.TenantId) + ")";
                    else if (voiceRecRequest.UseGroup && !extExists)
                    {
                        string simpleUserAssingedChnCrieteria = string.Format(" AND {0} IN ({1})",
                                                                                voiceRecRequest.SimpleUserSearchCriteria.Type == 4 ? "CI.Ext " : "CI.UserNum",
                                                                                "-9999");
                        voiceRecRequest.SearchCriteria.Criteria += simpleUserAssingedChnCrieteria;

                    }
                    else
                    {
                        string simpleUserAssingedChnCrieteria = string.Format(" AND {0} IN ({1})",
                                                                                voiceRecRequest.SimpleUserSearchCriteria.Type == 4 ? "CI.Ext " : "CI.UserNum",
                                                                                voiceRecRequest.SimpleUserSearchValues);
                        voiceRecRequest.SearchCriteria.Criteria += simpleUserAssingedChnCrieteria;
                    }
                }
                //end: Used in case of Simple user rights

                if (voiceRecRequest.UseAdvanceSearch)
                    voiceRecRequest.SearchCriteria.Criteria += " AND ( " + createAdvanceSearchClause(voiceRecRequest.SearchCriteria.AdvanceSearchData, voiceRecRequest.TenantId) + " )";

                VoiceRecResponse res = new VoiceRecResponse();
                List<CallInfo> results = new VoiceRecDAL(voiceRecRequest.TenantId).GetCallSearchResultsPagedDemo(actualPageSize, voiceRecRequest.PageSize, voiceRecRequest.PageNumber, out totalPages, out totalRecords, out totalPagesDemo, out totalRecordsDemo, voiceRecRequest.SearchCriteria);
                res.TotalPages = totalPages;
                res.TotalRecords = totalRecords;

                res.TotalPagesDemo = totalPagesDemo;
                res.TotalRecordsDemo = totalRecordsDemo;

                //long minNo = results.Count > 0 ? results.Min(c => c.RowNo) : 0;
                //long maxNo = results.Max(c => c.RowNo);
                //res.CallInfoSearchResults = results.OrderBy(ci => ci.StartTime).Take(voiceRecRequest.PageSize).ToList();
                //return res;

                //long minNo = results.Count > 0 ? results.Where(c => c.CallType == 1).Min(c => c.RowNo) : 0;
                //long minNo = results.Count > 0 ? results.Where(c => c.CallType == 1).DefaultIfEmpty().Min(c => c.RowNo) : 0;
                var minNo = results.Count > 0 ? results.Where(c => c.CallType == 1).DefaultIfEmpty().Min(c => c == null ? 1 : c.RowNo) : 0;
                var rowCounter = (Convert.ToInt32(minNo) / actualPageSize * voiceRecRequest.PageSize) + Convert.ToInt32(minNo) % actualPageSize;
                //long maxNo = results.Max(c => c.RowNo);
                var reorder = results.OrderByDescending(ci => ci.StartTime).Take(voiceRecRequest.PageSize).ToList();
                reorder.ForEach(c =>
                {
                    c.RowNo = rowCounter++;
                    //if (!string.IsNullOrEmpty(c.ScreenFileNames) && c.CallType == 1) c.CallType = 6;
                });
                res.CallInfoSearchResults = reorder;
                return res;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetCallSearchResultsPagedMixedWithDummyData", voiceRecRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetCallSearchResultsPagedMixedWithDummyData", voiceRecRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        public VoiceRecResponse GetCallSearchResultsPagedMixedWithScreenData(VoiceRecRequest voiceRecRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "GetCallSearchResultsPagedMixedWithScreenData", voiceRecRequest.TenantId, "GetCallSearchResultsPagedMixedWithScreenData function has been called successfully. "));
                int totalPages = 0;
                long totalRecords = 0;

                int totalPagesDemo = 0;
                long totalRecordsDemo = 0;
                DateTime startDate = DateTime.Now;
                int actualPageSize = GetActualDataPageSize(voiceRecRequest.PageSize, voiceRecRequest.ExtensionCallInfos, voiceRecRequest.TenantId);

                bool extExists = false;

                if (voiceRecRequest.ExtensionCallInfos != null)
                    extExists = IsExtExist(voiceRecRequest.ExtensionCallInfos, voiceRecRequest.TenantId);

                voiceRecRequest.SearchCriteria.DurationStr = QueryConstants.DURATION_DEFAULT;


                if (voiceRecRequest.SearchCriteria.StartDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null) || voiceRecRequest.SearchCriteria.EndDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null))
                    voiceRecRequest.SearchCriteria.DurationStr = QueryConstants.DURATION_BETWEEN + voiceRecRequest.SearchCriteria.StartDuration.TotalMilliseconds.ToString() + "' AND '" + voiceRecRequest.SearchCriteria.EndDuration.TotalMilliseconds.ToString() + "'";
                //else if (voiceRecRequest.SearchCriteria.StartDuration == TimeSpan.Parse("00:00:00") && voiceRecRequest.SearchCriteria.EndDuration == TimeSpan.Parse("00:00:00"))
                //    voiceRecRequest.SearchCriteria.DurationStr = QueryConstants.DURATION_BETWEEN + "000000" + "' AND '" + "235959" + "'";

                if (voiceRecRequest.SearchCriteria.SearchRest > 0)
                {
                    voiceRecRequest.SearchCriteria.EndTime = TimeSpan.Parse(DateTime.Now.ToString("HH:mm:ss"));
                    voiceRecRequest.SearchCriteria.StartTime = this.GetStartTime(voiceRecRequest.SearchCriteria.SearchRest, out startDate, voiceRecRequest.TenantId);
                    voiceRecRequest.SearchCriteria.StartDate = startDate;
                }
                else
                {
                    if (voiceRecRequest.SearchCriteria.StartTime == TimeSpan.Parse("00:00:00") && voiceRecRequest.SearchCriteria.EndTime == TimeSpan.Parse("00:00:00"))
                    {
                        voiceRecRequest.SearchCriteria.EndTime = TimeSpan.Parse("23:59:59");

                    }
                }
                voiceRecRequest.SearchCriteria.Criteria = " AND(CI.StartTime IS NOT NULL)";

                if (voiceRecRequest.UseGroup && voiceRecRequest.UserType != 1)
                    voiceRecRequest.SearchCriteria.Criteria += " AND (" + createSearchGroupClause(voiceRecRequest.ExtensionCallInfos, voiceRecRequest.TenantId) + ")";
                //start: Used in case of Simple user rights
                else if (voiceRecRequest.UserType == 1)
                {
                    if (extExists)
                        voiceRecRequest.SearchCriteria.Criteria += " AND (" + createSearchGroupClause(voiceRecRequest.ExtensionCallInfos.FindAll(e => e.GroupExtension.Length >= 7), voiceRecRequest.TenantId) + ")";
                    else if (voiceRecRequest.UseGroup && !extExists)
                    {
                        string simpleUserAssingedChnCrieteria = string.Format(" AND {0} IN ({1})",
                                                                                voiceRecRequest.SimpleUserSearchCriteria.Type == 4 ? "CI.Ext " : "CI.UserNum",
                                                                                "-9999");
                        voiceRecRequest.SearchCriteria.Criteria += simpleUserAssingedChnCrieteria;

                    }
                    else
                    {
                        string simpleUserAssingedChnCrieteria = string.Format(" AND {0} IN ({1})",
                                                                                voiceRecRequest.SimpleUserSearchCriteria.Type == 4 ? "CI.Ext " : "CI.UserNum",
                                                                                voiceRecRequest.SimpleUserSearchValues);
                        voiceRecRequest.SearchCriteria.Criteria += simpleUserAssingedChnCrieteria;
                    }
                }
                //end: Used in case of Simple user rights

                if (voiceRecRequest.UseAdvanceSearch)
                    voiceRecRequest.SearchCriteria.Criteria += " AND ( " + createAdvanceSearchClause(voiceRecRequest.SearchCriteria.AdvanceSearchData, voiceRecRequest.TenantId) + " )";

                VoiceRecResponse res = new VoiceRecResponse();
                List<CallInfo> results = new VoiceRecDAL(voiceRecRequest.TenantId).GetCallSearchResultsPagedDemo(actualPageSize, voiceRecRequest.PageSize, voiceRecRequest.PageNumber, out totalPages, out totalRecords, out totalPagesDemo, out totalRecordsDemo, voiceRecRequest.SearchCriteria);
                res.TotalPages = totalPages;
                res.TotalRecords = totalRecords;

                res.TotalPagesDemo = totalPagesDemo;
                res.TotalRecordsDemo = totalRecordsDemo;

                var minNo = results.Count > 0 ? results.Where(c => c.CallType == 1).DefaultIfEmpty().Min(c => c == null ? 1 : c.RowNo) : 0;
                var rowCounter = (Convert.ToInt32(minNo) / actualPageSize * voiceRecRequest.PageSize) + Convert.ToInt32(minNo) % actualPageSize;

                var reorder = results.OrderByDescending(ci => ci.StartTime).Take(voiceRecRequest.PageSize).ToList();
                reorder.ForEach(c =>
                {
                    c.RowNo = rowCounter++;
                    //if (!string.IsNullOrEmpty(c.ScreenRecFile) && c.CallType == 1) c.CallType = 6;
                });
                res.CallInfoSearchResults = reorder;
                return res;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetCallSearchResultsPagedMixedWithScreenData", voiceRecRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetCallSearchResultsPagedMixedWithScreenData", voiceRecRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        #endregion


        #region Table Fields


        //public List<RPTTableField> GetTableFields()
        //{
        //    return new RPTTableFieldDAL().GetTableFields("iso-8859-1", 1).FindAll(tf => tf.Width > 0);
        //}

        public List<CustomTableFieldHead> GetTableFieldsStatic()
        {
            //IsShow	Caption	                Width	Height	Align	KEY	                DisplayNum
            //0	    idx	                    0	    0	    0	    idx	                0
            //0	    Channel	                60	    25	    0	    ExtIDX	            1
            //0	    CallID	                0	    0	    0	    CallIDIDX	        2
            //0	    ChannelName	            120	    25	    0	    ExtNameIDX	        3
            //0	    FileName	            0	    0	    0	    FileNameIDX	        4
            //0	    StartTimeIndex	        0	    0	    0	    StartTimeIDX	    5
            //0	    TimeIndex	            0	    0	    0	    TimeIDX	            6
            //0	    RecorderIP	            0	    0	    0	    RecorderIPIDX	    7
            //0	    InHDD	                0	    0	    0	    InHDDIDX	        8
            //0	    CallIndex	            0	    0	    0	    CallIndexIDX	    9
            //0	    BackupID	            0	    0	    0	    BackupIDIDX	        10
            //0	    GroupName	            100	    25	    0	    GroupNameIDX	    11
            //0	    TimeInfo	            120	    25	    0	    TimeInfoIDX	        13
            //0	    Duration	            80	    25	    0	    DurationIDX	        14
            //0	    Call Comment	        440	    25	    0	    CALL_COMMENTIDX	    15
            //0	    CallerID/DialedNumber	140	    25	    0	    CalledIDX	        16
            //0	    CallTag	                120	    25	    0	    CallTagIDX	        17
            //0	    VideoFileName	        0	    25	    0	    VideoFileNameIDX	18
            //0	    BookMark	            120	    25	    0	    BookmarksIDX	    19
            //0	    Retain File	            60	    25	    0	    RetainValueIDX	    20
            //0	    Call Comment	        120	    25	    0	    CallCommentIDX	    21
            List<CustomTableFieldHead> tfs = new List<CustomTableFieldHead>();
            tfs.Add(new CustomTableFieldHead { IsShow = false, Width = 0, Height = 0, DisplayNum = 0, KEY = "idx", Caption = "idx" });
            tfs.Add(new CustomTableFieldHead { IsShow = false, Width = 60, Height = 25, DisplayNum = 1, KEY = "ExtIDX", Caption = "Channel" });
            tfs.Add(new CustomTableFieldHead { IsShow = false, Width = 0, Height = 0, DisplayNum = 2, KEY = "CallIDIDX", Caption = "CallID" });
            tfs.Add(new CustomTableFieldHead { IsShow = false, Width = 120, Height = 25, DisplayNum = 3, KEY = "ExtNameIDX", Caption = "ChannelName" });
            tfs.Add(new CustomTableFieldHead { IsShow = false, Width = 0, Height = 0, DisplayNum = 4, KEY = "FileNameIDX", Caption = "FileName" });
            tfs.Add(new CustomTableFieldHead { IsShow = false, Width = 0, Height = 0, DisplayNum = 5, KEY = "StartTimeIDX", Caption = "StartTimeIndex" });
            tfs.Add(new CustomTableFieldHead { IsShow = false, Width = 0, Height = 0, DisplayNum = 6, KEY = "TimeIDX", Caption = "TimeIndex" });
            tfs.Add(new CustomTableFieldHead { IsShow = false, Width = 0, Height = 0, DisplayNum = 7, KEY = "RecorderIPIDX", Caption = "RecorderIP" });
            tfs.Add(new CustomTableFieldHead { IsShow = false, Width = 0, Height = 0, DisplayNum = 8, KEY = "InHDDIDX", Caption = "InHDD" });
            tfs.Add(new CustomTableFieldHead { IsShow = false, Width = 0, Height = 0, DisplayNum = 9, KEY = "CallIndexIDX", Caption = "CallIndex" });
            tfs.Add(new CustomTableFieldHead { IsShow = false, Width = 0, Height = 0, DisplayNum = 10, KEY = "BackupIDIDX", Caption = "BackupID" });
            tfs.Add(new CustomTableFieldHead { IsShow = false, Width = 100, Height = 25, DisplayNum = 11, KEY = "GroupNameIDX", Caption = "GroupName" });
            tfs.Add(new CustomTableFieldHead { IsShow = false, Width = 120, Height = 25, DisplayNum = 13, KEY = "TimeInfoIDX", Caption = "TimeInfo" });
            tfs.Add(new CustomTableFieldHead { IsShow = false, Width = 80, Height = 25, DisplayNum = 14, KEY = "DurationIDX", Caption = "Duration" });
            tfs.Add(new CustomTableFieldHead { IsShow = false, Width = 440, Height = 25, DisplayNum = 15, KEY = "CALL_COMMENTIDX", Caption = "Call Comment" });
            tfs.Add(new CustomTableFieldHead { IsShow = false, Width = 140, Height = 25, DisplayNum = 16, KEY = "CalledIDX", Caption = "CallerID/DialedNumber" });
            tfs.Add(new CustomTableFieldHead { IsShow = false, Width = 120, Height = 25, DisplayNum = 17, KEY = "CallTagIDX", Caption = "CallTag" });
            tfs.Add(new CustomTableFieldHead { IsShow = false, Width = 0, Height = 25, DisplayNum = 18, KEY = "VideoFileNameIDX", Caption = "VideoFileName" });
            tfs.Add(new CustomTableFieldHead { IsShow = false, Width = 120, Height = 25, DisplayNum = 19, KEY = "BookmarksIDX", Caption = "BookMark" });
            tfs.Add(new CustomTableFieldHead { IsShow = false, Width = 60, Height = 25, DisplayNum = 20, KEY = "RetainValueIDX", Caption = "Retain File" });
            tfs.Add(new CustomTableFieldHead { IsShow = false, Width = 120, Height = 25, DisplayNum = 21, KEY = "CallCommentIDX", Caption = "Call Comment" });


            return tfs;
        }

        #endregion




        #region BookMark

        //public bool SaveBookMark(VoiceRecRequest voiceRecRequest)
        //{
        //    switch (voiceRecRequest.Operation)
        //    {
        //        case ActionType.Update:
        //            return new VoiceRecDAL().UpdateBookMark(voiceRecRequest.BookMark);
        //            break;
        //        case ActionType.Insert:
        //            return new VoiceRecDAL().SaveBookMark(voiceRecRequest.BookMark);
        //            break;
        //        case ActionType.Delete:
        //            break;
        //        default:
        //            break;
        //    }
        //    return false;
        //}

        public VoiceRecResponse SaveBookMarkAndGet(VoiceRecRequest voiceRecRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "SaveBookMarkAndGet", voiceRecRequest.TenantId, "SaveBookMarkAndGet function has been called successfully. "));
                VoiceRecResponse resp = null;
                switch (voiceRecRequest.Operation)
                {
                    case ActionType.Update:
                        resp = new VoiceRecResponse
                        {
                            FlagStatus = true,
                            AvailableData = new VoiceRecDAL(voiceRecRequest.TenantId).UpdateBookMarkAndGetByCallId(voiceRecRequest.Bookmark)
                        };
                        return resp;
                    case ActionType.Insert:
                        //return new VoiceRecDAL().SaveBookMark(voiceRecRequest.BookMark);
                        resp = new VoiceRecResponse
                        {
                            FlagStatus = true,
                            AvailableData = new VoiceRecDAL(voiceRecRequest.TenantId).SaveBookMarkAndGetByCallId(voiceRecRequest.Bookmark)
                        };
                        return resp;
                    case ActionType.Delete:
                        return resp;
                    default:
                        return resp;
                }
                return resp;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "SaveBookMarkAndGet", voiceRecRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "SaveBookMarkAndGet", voiceRecRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        #endregion

        #region Channels

        public VoiceRecResponse GetMonitorChannels(VoiceRecRequest voiceRecRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "GetMonitorChannels", voiceRecRequest.TenantId, "GetMonitorChannels function has been called successfully. "));
                string whereClause = string.Empty;
                if (!voiceRecRequest.GetAllChannels && voiceRecRequest.UserType == 1)
                {
                    string simpleUserAssingedChnCrieteria = string.Format(" {0} IN ({1})",
                                                                           voiceRecRequest.SimpleUserSearchCriteria.Type == 4 ? "A.Ext " : "CI.UserNum",
                                                                           voiceRecRequest.SimpleUserSearchValues);
                    whereClause = simpleUserAssingedChnCrieteria;

                }
                else if (voiceRecRequest.GetAllChannels)
                    whereClause = " 1=1 ";//" EI.ExtName <> '' "; existing system query
                else
                    whereClause = createGroupClause(voiceRecRequest.GroupExtensions, voiceRecRequest.TenantId);
                return new VoiceRecResponse
                {
                    MonitorChannels = new VoiceRecDAL(voiceRecRequest.TenantId).GetMonitorChannels(whereClause),
                    FlagStatus = true
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetMonitorChannels", voiceRecRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetMonitorChannels", voiceRecRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        private string createGroupClause(List<string> groupExtKeys, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "createGroupClause", tenantId, "createGroupClause function has been called successfully. "));
                return QueryHelper.BuildGroupExtWhereClause(groupExtKeys, "AG", "A");
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "createGroupClause", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "createGroupClause", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #endregion


        #region Playlist

        //public List<PlaylistDetail> GetTracksInsidePlaylistMixedWithDummyData(int playlistId)
        //{
        //    List<PlaylistDetail> plDetails = new VoiceRecDAL().GetTracksInsidePlaylist(playlistId);
        //    //List<CallInfo> plDetailsSmsEmailSocial = new List<CallInfo>();
        //    //plDetails.ForEach(pld =>
        //    //    {
        //    //        var callInfo = DummyData.getDummyListSMS().FirstOrDefault(dummy => dummy.CallId == pld.CallInfo.CallId);
        //    //        if (callInfo != null) pld.CallInfo = callInfo;
        //    //        //if (callInfo != null) plDetailsSmsEmailSocial.Add(callInfo);
        //    //    });
        //    return plDetails;
        //}

        //public VoiceRecResponse SavePlaylist(VoiceRecRequest voiceRecRequest)
        //{
        //    try
        //    {
        //        int statusValue = new VoiceRecDAL().SavePlaylist(voiceRecRequest.Playlist, voiceRecRequest.UserId);
        //        if (statusValue != -1)
        //            voiceRecRequest.Playlist.Id = statusValue > 0 ? statusValue : voiceRecRequest.Playlist.Id;

        //        return new VoiceRecResponse
        //        {
        //            StatusReceivedFromDB = statusValue,
        //            Playlist = voiceRecRequest.Playlist,
        //        };
        //    }
        //    catch (Exception ex) { throw ex; }
        //}

        //public bool DeletePlaylist(int playlistId)
        //{
        //    return new VoiceRecDAL().DeletePlaylist(playlistId);
        //}

        //public List<Playlist> GetPlaylist(int userId)
        //{
        //    return new VoiceRecDAL().GetPlaylist(userId);
        //}

        //public VoiceRecResponse AddTracksInsidePlaylist(int playlistId, string callIds, int userId, int MaxItems)
        //{
        //    return new VoiceRecResponse
        //    {
        //        StatusFromDB = new VoiceRecDAL().AddTracksInsidePlaylist(playlistId, callIds, userId, MaxItems)
        //    };
        //}

        //public List<PlaylistDetail> GetTracksInsidePlaylist(int playlistId)
        //{
        //    return new VoiceRecDAL().GetTracksInsidePlaylist(playlistId);
        //}

        //public bool DeletePlaylistDetails(int playlistId)
        //{
        //    return new VoiceRecDAL().DeletePlaylistDetails(playlistId);
        //}

        //public bool DeletePlaylistDetail(int playlistDetailId)
        //{
        //    return new VoiceRecDAL().DeletePlaylistDetail(playlistDetailId);
        //}

        #endregion



        #region Utility Methods

        public int GetActualDataPageSize(int pageSize, List<string> dummyExtGroups, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "GetActualDataPageSize", tenantId, "GetActualDataPageSize function has been called successfully. "));
                bool dExtExists = false;
                int aCallPageSize = pageSize;
                if (dummyExtGroups != null)
                    dExtExists = IsDummyExtExist(dummyExtGroups, tenantId);
                if (dExtExists)
                    aCallPageSize = pageSize * 80 / 100;
                return aCallPageSize;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "createGroupClause", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "createGroupClause", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public int GetActualDataPageSize(int pageSize, List<ExtensionCallInfo> extCallInfos, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "GetActualDataPageSize", tenantId, "GetActualDataPageSize function has been called successfully. "));
                bool dExtExists = false;
                int aCallPageSize = pageSize;
                if (extCallInfos != null)
                    dExtExists = IsDummyExtExist(extCallInfos, tenantId);
                if (dExtExists)
                    aCallPageSize = pageSize * 80 / 100;
                return aCallPageSize;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetActualDataPageSize", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetActualDataPageSize", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public TimeSpan GetStartTime(int searchRest, out DateTime startDate, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "GetStartTime", tenantId, "GetStartTime function has been called successfully. "));
                DateTime dt = DateTime.Now;
                int hr = 0;
                if (Convert.ToInt16(DateTime.Now.ToString("HH")) - searchRest < 0)
                {

                    hr = Convert.ToInt16(DateTime.Now.ToString("HH")) - searchRest + 24;
                    startDate = DateTime.Now.AddDays(-1);
                }
                else
                {
                    hr = Convert.ToInt16(DateTime.Now.ToString("HH")) - searchRest;
                    startDate = dt;
                }
                return TimeSpan.Parse(string.Format("{0}:{1}", hr, dt.ToString("mm:ss")));
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetStartTime", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetStartTime", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool IsExtExist(List<string> extensionGroups, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "IsExtExist", tenantId, "IsExtExist function has been called successfully. "));
                foreach (string extGrp in extensionGroups)
                {
                    if (extGrp.Contains('E'))
                    {
                        return true;
                    }
                }
                return false;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "IsExtExist", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "IsExtExist", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool IsExtExist(List<ExtensionCallInfo> extensionGroups, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "IsExtExist", tenantId, "IsExtExist function has been called successfully. "));
                foreach (ExtensionCallInfo extGrp in extensionGroups)
                {
                    if (extGrp.GroupExtension.Contains('E'))
                    {
                        return true;
                    }
                }
                return false;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "IsExtExist", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "IsExtExist", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool IsDummyExtExist(List<string> extensionGroups, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "IsDummyExtExist", tenantId, "IsDummyExtExist function has been called successfully. "));
                foreach (string extGrp in extensionGroups)
                {
                    if (extGrp.Length == 3 || extGrp.Length == 7)
                    {
                        return true;
                    }
                }
                return false;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "IsDummyExtExist", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "IsDummyExtExist", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            

        }

        public bool IsDummyExtExist(List<ExtensionCallInfo> extCallInfos, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "IsDummyExtExist", tenantId, "IsDummyExtExist function has been called successfully. "));
                foreach (ExtensionCallInfo extCI in extCallInfos)
                {
                    if (extCI.GroupExtension.Length == 3 || extCI.GroupExtension.Length == 7)
                    {
                        return true;
                    }
                }
                return false;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "IsExtExist", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "IsExtExist", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #endregion


        //#region Load Search Page Data on pageload event

        //public VoiceRecResponse GetSearchPageData(VoiceRecRequest voiceRecRequest)
        //{
        //    //return new PageDAL().GetSearchPageData(voiceRecRequest);
        //    bool isDemoMode = AppSettingsUtil.GetBool("isDemo");

        //    var vrResponse = new PageDAL().GetSearchPageData(voiceRecRequest);

        //    var treeNodes = vrResponse.TreeviewData.BuildGroupsTree();

        //    List<GroupTree> groupsAll = new List<GroupTree>
        //    {
        //        new GroupTree{ Id= 1, GroupType = GroupType.Audio, CssName = "audio", TreeviewData = treeNodes},
        //        new GroupTree{ Id= 6, GroupType = GroupType.Screens, CssName = "screen", TreeviewData = treeNodes},
        //    };
        //    if (isDemoMode)
        //        groupsAll.AddRange(DemoData.GetDemoGroups());

        //    vrResponse.AllGroups = groupsAll.OrderBy(g => g.Id).ToList();
        //    return vrResponse;
        //}
        //#endregion
        //#region Load IR Full Data on pageload event
        //public VoiceRecResponse GetIRFullPageData(VoiceRecRequest voiceRecRequest)
        //{
        //    return new PageDAL().GetIRFullPageData(voiceRecRequest);
        //}
        //#endregion

        #region build query for screens

        #endregion

        public VoiceRecResponse GetCallsByLocationFromRecorder(VoiceRecRequest voiceRecRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "GetCallsByLocationFromRecorder", voiceRecRequest.TenantId, "GetCallsByLocationFromRecorder function has been called successfully."));
                var recorder = voiceRecRequest.Recorder;
                voiceRecRequest.SearchCriteria.DurationStr = QueryConstants.DURATION_DEFAULT;
                if (voiceRecRequest.SearchCriteria.StartDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null) || voiceRecRequest.SearchCriteria.EndDuration != TimeSpan.ParseExact("00:00:00", "hh':'mm':'ss", null))
                    voiceRecRequest.SearchCriteria.DurationStr = QueryConstants.DURATION_BETWEEN + voiceRecRequest.SearchCriteria.StartDuration.TotalMilliseconds.ToString() + "' AND '" + voiceRecRequest.SearchCriteria.EndDuration.TotalMilliseconds.ToString() + "'";
                if (voiceRecRequest.SearchCriteria.StartTime == TimeSpan.Parse("00:00:00") && voiceRecRequest.SearchCriteria.EndTime == TimeSpan.Parse("00:00:00"))
                    voiceRecRequest.SearchCriteria.EndTime = TimeSpan.Parse("23:59:59");

                voiceRecRequest.SearchCriteria.Criteria = " AND(CI.StartTime IS NOT NULL)";
                if (recorder.IsPrimary)
                {
                    return new VoiceRecResponse
                    {
                        CallInfoSearchResults = new VoiceRecDAL(voiceRecRequest.TenantId).GetCallsByLocation(voiceRecRequest.SearchCriteria),
                    };
                }
                else
                {
                    Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "GetCallsByLocationFromRecorder", voiceRecRequest.TenantId, "GetCallsByLocationFromRecorder function has been called successfully. Recorder Id = " + recorder.Id + " , Recorder Name = " + recorder.Name));
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    return new VoiceRecResponse
                    {
                        CallInfoSearchResults = entClient.GetCallsByLocation(voiceRecRequest.SearchCriteria).ToList(),
                    };
                }
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetCallsByLocationFromRecorder", voiceRecRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetCallsByLocationFromRecorder", voiceRecRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        #region Onsite Contact Information

        public VoiceRecResponse UpdateOnsiteContactInfoConfirmation(int UserNum, string Comments, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "UpdateOnsiteContactInfoConfirmation", tenantId, "UpdateOnsiteContactInfoConfirmation function has been called successfully. UserNum = " + UserNum));
                return new VoiceRecResponse
                {
                    StatusReceivedFromDB = new VoiceRecDAL(tenantId).UpdateOnsiteContactInfoConfirmation(UserNum, Comments)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "UpdateOnsiteContactInfoConfirmation", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "UpdateOnsiteContactInfoConfirmation", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public VoiceRecResponse GetOnsiteContactInfoConfirmation(int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.VoiceRec, "GetOnsiteContactInfoConfirmation", tenantId, "GetOnsiteContactInfoConfirmation function has been called successfully."));
                return new VoiceRecResponse
                {
                    OnsiteContactInfo = new VoiceRecDAL(tenantId).GetOnsiteContactInfoConfirmation()
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.VoiceLogging, "GetOnsiteContactInfoConfirmation", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.VoiceLogging, "GetOnsiteContactInfoConfirmation", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #endregion
    }
}

