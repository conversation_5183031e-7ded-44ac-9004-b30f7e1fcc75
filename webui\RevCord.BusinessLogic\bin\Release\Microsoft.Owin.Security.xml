<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Owin.Security</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Owin.Security.AppBuilderSecurityExtensions">
            <summary>
            Provides extensions methods for app.Property values that are only needed by implementations of authentication middleware.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.AppBuilderSecurityExtensions.GetDefaultSignInAsAuthenticationType(Owin.IAppBuilder)">
            <summary>
            Returns the previously set AuthenticationType that external sign in middleware should use when the
            browser navigates back to their return url.
            </summary>
            <param name="app">App builder passed to the application startup code</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Owin.Security.AppBuilderSecurityExtensions.SetDefaultSignInAsAuthenticationType(Owin.IAppBuilder,System.String)">
            <summary>
            Called by middleware to change the name of the AuthenticationType that external middleware should use
            when the browser navigates back to their return url.
            </summary>
            <param name="app">App builder passed to the application startup code</param>
            <param name="authenticationType">AuthenticationType that external middleware should sign in as.</param>
        </member>
        <member name="T:Microsoft.Owin.Security.AuthenticationMode">
            <summary>
            Controls the behavior of authentication middleware
            </summary>
        </member>
        <member name="F:Microsoft.Owin.Security.AuthenticationMode.Active">
            <summary>
            In Active mode the authentication middleware will alter the user identity as the request arrives, and
            will also alter a plain 401 as the response leaves.
            </summary>
        </member>
        <member name="F:Microsoft.Owin.Security.AuthenticationMode.Passive">
            <summary>
            In Passive mode the authentication middleware will only provide user identity when asked, and will only
            alter 401 responses where the authentication type named in the extra challenge data.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.AuthenticationOptions">
            <summary>
            Base Options for all authentication middleware
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.AuthenticationOptions.#ctor(System.String)">
            <summary>
            Initialize properties of AuthenticationOptions base class
            </summary>
            <param name="authenticationType">Assigned to the AuthenticationType property</param>
        </member>
        <member name="P:Microsoft.Owin.Security.AuthenticationOptions.AuthenticationType">
            <summary>
            The AuthenticationType in the options corresponds to the IIdentity AuthenticationType property. A different
            value may be assigned in order to use the same authentication middleware type more than once in a pipeline.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.AuthenticationOptions.AuthenticationMode">
            <summary>
            If Active the authentication middleware alter the request user coming in and
            alter 401 Unauthorized responses going out. If Passive the authentication middleware will only provide
            identity and alter responses when explicitly indicated by the AuthenticationType.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.AuthenticationOptions.Description">
            <summary>
            Additional information about the authentication type which is made available to the application.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.Constants">
            <summary>
            String constants used only by the Security assembly
            </summary>
        </member>
        <member name="F:Microsoft.Owin.Security.Constants.DefaultSignInAsAuthenticationType">
            <summary>
            Used by middleware extension methods to coordinate the default value Options property SignInAsAuthenticationType
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.DataProtection.IDataProtectionProvider">
            <summary>
            Factory used to create IDataProtection instances
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.DataProtection.IDataProtectionProvider.Create(System.String[])">
            <summary>
            Returns a new instance of IDataProtection for the provider.
            </summary>
            <param name="purposes">Additional entropy used to ensure protected data may only be unprotected for the correct purposes.</param>
            <returns>An instance of a data protection service</returns>
        </member>
        <member name="T:Microsoft.Owin.Security.DataProtection.IDataProtector">
            <summary>
            Service used to protect and unprotect data
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.DataProtection.IDataProtector.Protect(System.Byte[])">
            <summary>
            Called to protect user data.
            </summary>
            <param name="userData">The original data that must be protected</param>
            <returns>A different byte array that may be unprotected or altered only by software that has access to 
            the an identical IDataProtection service.</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.DataProtection.IDataProtector.Unprotect(System.Byte[])">
            <summary>
            Called to unprotect user data
            </summary>
            <param name="protectedData">The byte array returned by a call to Protect on an identical IDataProtection service.</param>
            <returns>The byte array identical to the original userData passed to Protect.</returns>
        </member>
        <member name="T:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler`1">
            <summary>
            Base class for the per-request work performed by most authentication middleware.
            </summary>
            <typeparam name="TOptions">Specifies which type for of AuthenticationOptions property</typeparam>
        </member>
        <member name="T:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler">
            <summary>
            Base class for the per-request work performed by most authentication middleware.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler.TeardownAsync">
            <summary>
            Called once per request after Initialize and Invoke. 
            </summary>
            <returns>async completion</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler.InvokeAsync">
            <summary>
            Called once by common code after initialization. If an authentication middleware responds directly to
            specifically known paths it must override this virtual, compare the request path to it's known paths, 
            provide any response information as appropriate, and true to stop further processing.
            </summary>
            <returns>Returning false will cause the common code to call the next middleware in line. Returning true will
            cause the common code to begin the async completion journey without calling the rest of the middleware
            pipeline.</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler.AuthenticateAsync">
            <summary>
            Causes the authentication logic in AuthenticateCore to be performed for the current request 
            at most once and returns the results. Calling Authenticate more than once will always return 
            the original value. 
            
            This method should always be called instead of calling AuthenticateCore directly.
            </summary>
            <returns>The ticket data provided by the authentication logic</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler.AuthenticateCoreAsync">
            <summary>
            The core authentication logic which must be provided by the handler. Will be invoked at most
            once per request. Do not call directly, call the wrapping Authenticate method instead.
            </summary>
            <returns>The ticket data provided by the authentication logic</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler.ApplyResponseAsync">
            <summary>
            Causes the ApplyResponseCore to be invoked at most once per request. This method will be
            invoked either earlier, when the response headers are sent as a result of a response write or flush,
            or later, as the last step when the original async call to the middleware is returning.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler.ApplyResponseCoreAsync">
            <summary>
            Core method that may be overridden by handler. The default behavior is to call two common response 
            activities, one that deals with sign-in/sign-out concerns, and a second to deal with 401 challenges.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler.ApplyResponseGrantAsync">
            <summary>
            Override this method to dela with sign-in/sign-out concerns, if an authentication scheme in question
            deals with grant/revoke as part of it's request flow. (like setting/deleting cookies)
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler.ApplyResponseChallengeAsync">
            <summary>
            Override this method to deal with 401 challenge concerns, if an authentication scheme in question
            deals an authentication interaction as part of it's request flow. (like adding a response header, or
            changing the 401 result to 302 of a login page or external sign-in location.)
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler`1.Initialize(`0,Microsoft.Owin.IOwinContext)">
            <summary>
            Initialize is called once per request to contextualize this instance with appropriate state.
            </summary>
            <param name="options">The original options passed by the application control behavior</param>
            <param name="context">The utility object to observe the current request and response</param>
            <returns>async completion</returns>
        </member>
        <member name="T:Microsoft.Owin.Security.AuthenticationTicket">
            <summary>
            Contains user identity information as well as additional authentication state.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.AuthenticationTicket.#ctor(System.Security.Claims.ClaimsIdentity,Microsoft.Owin.Security.AuthenticationProperties)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Owin.Security.AuthenticationTicket"/> class
            </summary>
            <param name="identity"></param>
            <param name="properties"></param>
        </member>
        <member name="P:Microsoft.Owin.Security.AuthenticationTicket.Identity">
            <summary>
            Gets the authenticated user identity.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.AuthenticationTicket.Properties">
            <summary>
            Additional state values for the authentication session.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.ICertificateValidator">
            <summary>
            Interface for providing pinned certificate validation, which checks HTTPS 
            communication against a known good list of certificates to protect against 
            compromised or rogue CAs issuing certificates for hosts without the 
            knowledge of the host owner.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.ICertificateValidator.Validate(System.Object,System.Security.Cryptography.X509Certificates.X509Certificate,System.Security.Cryptography.X509Certificates.X509Chain,System.Net.Security.SslPolicyErrors)">
            <summary>
            Verifies the remote Secure Sockets Layer (SSL) certificate used for authentication.
            </summary>
            <param name="sender">An object that contains state information for this validation.</param>
            <param name="certificate">The certificate used to authenticate the remote party.</param>
            <param name="chain">The chain of certificate authorities associated with the remote certificate.</param>
            <param name="sslPolicyErrors">One or more errors associated with the remote certificate.</param>
            <returns>A Boolean value that determines whether the specified certificate is accepted for authentication.</returns>
        </member>
        <member name="T:Microsoft.Owin.Security.CertificateThumbprintValidator">
            <summary>
            Provides pinned certificate validation based on the certificate thumbprint.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.CertificateThumbprintValidator.#ctor(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Owin.Security.CertificateThumbprintValidator"/> class.
            </summary>
            <param name="validThumbprints">A set of thumbprints which are valid for an HTTPS request.</param>
        </member>
        <member name="M:Microsoft.Owin.Security.CertificateThumbprintValidator.Validate(System.Object,System.Security.Cryptography.X509Certificates.X509Certificate,System.Security.Cryptography.X509Certificates.X509Chain,System.Net.Security.SslPolicyErrors)">
            <summary>
            Validates that the certificate thumbprints in the signing chain match at least one whitelisted thumbprint.
            </summary>
            <param name="sender">An object that contains state information for this validation.</param>
            <param name="certificate">The certificate used to authenticate the remote party.</param>
            <param name="chain">The chain of certificate authorities associated with the remote certificate.</param>
            <param name="sslPolicyErrors">One or more errors associated with the remote certificate.</param>
            <returns>A Boolean value that determines whether the specified certificate is accepted for authentication.</returns>
        </member>
        <member name="T:Microsoft.Owin.Security.Provider.BaseContext`1">
            <summary>
            Base class used for certain event contexts
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Notifications.BaseNotification`1.HandleResponse">
            <summary>
            Discontinue all processing for this request and return to the client.
            The caller is responsible for generating the full response.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Notifications.BaseNotification`1.SkipToNextMiddleware">
            <summary>
            Discontinue processing the request in the current middleware and pass control to the next one.
            </summary>
        </member>
        <member name="F:Microsoft.Owin.Security.Notifications.NotificationResultState.Continue">
            <summary>
            Continue with normal processing.
            </summary>
        </member>
        <member name="F:Microsoft.Owin.Security.Notifications.NotificationResultState.Skipped">
            <summary>
            Discontinue processing the request in the current middleware and pass control to the next one.
            </summary>
        </member>
        <member name="F:Microsoft.Owin.Security.Notifications.NotificationResultState.HandledResponse">
            <summary>
            Discontinue all processing for this request.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Notifications.RedirectToIdentityProviderNotification`2.HandleResponse">
            <summary>
            Discontinue all processing for this request and return to the client.
            The caller is responsible for generating the full response.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Notifications.SecurityTokenValidatedNotification`2.AuthenticationTicket">
            <summary>
            Gets or set the <see cref="P:Microsoft.Owin.Security.Notifications.SecurityTokenValidatedNotification`2.AuthenticationTicket"/>
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Notifications.SecurityTokenValidatedNotification`2.ProtocolMessage">
            <summary>
            Gets or sets the Protocol message
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.DataProtection.DpapiDataProtectionProvider">
            <summary>
            Used to provide the data protection services that are derived from the Data Protection API. It is the best choice of
            data protection when you application is not hosted by ASP.NET and all processes are running as the same domain identity. 
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.DataProtection.DpapiDataProtectionProvider.#ctor">
            <summary>
            Initializes a new DpapiDataProtectionProvider with a random application
            name. This is only useful to protect data for the duration of the
            current application execution.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.DataProtection.DpapiDataProtectionProvider.#ctor(System.String)">
            <summary>
            Initializes a new DpapiDataProtectionProvider which uses the given
            appName as part of the protection algorithm
            </summary>
            <param name="appName">A user provided value needed to round-trip secured
            data. The default value comes from the IAppBuilder.Properties["owin.AppName"] 
            when self-hosted.</param>
        </member>
        <member name="M:Microsoft.Owin.Security.DataProtection.DpapiDataProtectionProvider.Create(System.String[])">
            <summary>
            Returns a new instance of IDataProtection for the provider.
            </summary>
            <param name="purposes">Additional entropy used to ensure protected data may only be unprotected for the correct purposes.</param>
            <returns>An instance of a data protection service</returns>
        </member>
        <member name="T:Microsoft.Owin.Security.Infrastructure.SecurityHelper">
            <summary>
            Helper code used when implementing authentication middleware
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Infrastructure.SecurityHelper.#ctor(Microsoft.Owin.IOwinContext)">
            <summary>
            Helper code used when implementing authentication middleware
            </summary>
            <param name="context"></param>
        </member>
        <member name="M:Microsoft.Owin.Security.Infrastructure.SecurityHelper.AddUserIdentity(System.Security.Principal.IIdentity)">
            <summary>
            Add an additional ClaimsIdentity to the ClaimsPrincipal in the "server.User" environment key
            </summary>
            <param name="identity"></param>
        </member>
        <member name="M:Microsoft.Owin.Security.Infrastructure.SecurityHelper.LookupChallenge(System.String,Microsoft.Owin.Security.AuthenticationMode)">
            <summary>
            Find response challenge details for a specific authentication middleware
            </summary>
            <param name="authenticationType">The authentication type to look for</param>
            <param name="authenticationMode">The authentication mode the middleware is running under</param>
            <returns>The information instructing the middleware how it should behave</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.Infrastructure.SecurityHelper.LookupSignIn(System.String)">
            <summary>
            Find response sign-in details for a specific authentication middleware
            </summary>
            <param name="authenticationType">The authentication type to look for</param>
            <returns>The information instructing the middleware how it should behave</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.Infrastructure.SecurityHelper.LookupSignOut(System.String,Microsoft.Owin.Security.AuthenticationMode)">
            <summary>
            Find response sign-out details for a specific authentication middleware
            </summary>
            <param name="authenticationType">The authentication type to look for</param>
            <param name="authenticationMode">The authentication mode the middleware is running under</param>
            <returns>The information instructing the middleware how it should behave</returns>
        </member>
        <member name="T:Microsoft.Owin.Security.Provider.EndpointContext`1">
            <summary>
            Base class used for certain event contexts
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Provider.EndpointContext`1.#ctor(Microsoft.Owin.IOwinContext,`0)">
            <summary>
            Creates an instance of this context
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Provider.EndpointContext`1.RequestCompleted">
            <summary>
            Prevents the request from being processed further by other components. 
            IsRequestCompleted becomes true after calling.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Provider.EndpointContext`1.IsRequestCompleted">
            <summary>
            True if the request should not be processed further by other components.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Resources.Exception_AuthenticationTokenDoesNotProvideSyncMethods">
            <summary>
              Looks up a localized string similar to The AuthenticationTokenProvider&apos;s required synchronous events have not been registered..
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Resources.Exception_DefaultDpapiRequiresAppNameKey">
            <summary>
              Looks up a localized string similar to The default data protection provider may only be used when the IAppBuilder.Properties contains an appropriate &apos;host.AppName&apos; key..
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Resources.Exception_MissingDefaultSignInAsAuthenticationType">
            <summary>
              Looks up a localized string similar to A default value for SignInAsAuthenticationType was not found in IAppBuilder Properties. This can happen if your authentication middleware are added in the wrong order, or if one is missing..
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Resources.Exception_UnhookAuthenticationStateType">
            <summary>
              Looks up a localized string similar to The state passed to UnhookAuthentication may only be the return value from HookAuthentication..
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.CertificateSubjectKeyIdentifierValidator">
            <summary>
            Provides pinned certificate validation based on the subject key identifier of the certificate.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.CertificateSubjectKeyIdentifierValidator.#ctor(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Owin.Security.CertificateSubjectKeyIdentifierValidator"/> class.
            </summary>
            <param name="validSubjectKeyIdentifiers">A set of subject key identifiers which are valid for an HTTPS request.</param>
        </member>
        <member name="M:Microsoft.Owin.Security.CertificateSubjectKeyIdentifierValidator.Validate(System.Object,System.Security.Cryptography.X509Certificates.X509Certificate,System.Security.Cryptography.X509Certificates.X509Chain,System.Net.Security.SslPolicyErrors)">
            <summary>
            Verifies the remote Secure Sockets Layer (SSL) certificate used for authentication.
            </summary>
            <param name="sender">An object that contains state information for this validation.</param>
            <param name="certificate">The certificate used to authenticate the remote party.</param>
            <param name="chain">The chain of certificate authorities associated with the remote certificate.</param>
            <param name="sslPolicyErrors">One or more errors associated with the remote certificate.</param>
            <returns>A Boolean value that determines whether the specified certificate is accepted for authentication.</returns>
        </member>
        <member name="T:Microsoft.Owin.Security.SubjectPublicKeyInfoAlgorithm">
            <summary>
            The algorithm used to generate the subject public key information blob hashes.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.CertificateSubjectPublicKeyInfoValidator">
            <summary>
            Implements a cert pinning validator passed on 
            http://datatracker.ietf.org/doc/draft-ietf-websec-key-pinning/?include_text=1
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.CertificateSubjectPublicKeyInfoValidator.#ctor(System.Collections.Generic.IEnumerable{System.String},Microsoft.Owin.Security.SubjectPublicKeyInfoAlgorithm)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Owin.Security.CertificateSubjectPublicKeyInfoValidator"/> class.
            </summary>
            <param name="validBase64EncodedSubjectPublicKeyInfoHashes">A collection of valid base64 encoded hashes of the certificate public key information blob.</param>
            <param name="algorithm">The algorithm used to generate the hashes.</param>
        </member>
        <member name="M:Microsoft.Owin.Security.CertificateSubjectPublicKeyInfoValidator.Validate(System.Object,System.Security.Cryptography.X509Certificates.X509Certificate,System.Security.Cryptography.X509Certificates.X509Chain,System.Net.Security.SslPolicyErrors)">
            <summary>
            Validates at least one SPKI hash is known.
            </summary>
            <param name="sender">An object that contains state information for this validation.</param>
            <param name="certificate">The certificate used to authenticate the remote party.</param>
            <param name="chain">The chain of certificate authorities associated with the remote certificate.</param>
            <param name="sslPolicyErrors">One or more errors associated with the remote certificate.</param>
            <returns>A Boolean value that determines whether the specified certificate is accepted for authentication.</returns>
        </member>
        <member name="M:Microsoft.Win32.NativeMethods.CryptEncodeObject(System.UInt32,System.IntPtr,Microsoft.Win32.NativeMethods.CERT_PUBLIC_KEY_INFO@,System.Byte[],System.UInt32@)">
            <summary>
            Encodes a structure of the type indicated by the value of the lpszStructType parameter.
            </summary>
            <param name="dwCertEncodingType">Type of encoding used.</param>
            <param name="lpszStructType">The high-order word is zero, the low-order word specifies the integer identifier for the type of the specified structure so
            we can use the constants in http://msdn.microsoft.com/en-us/library/windows/desktop/aa378145%28v=vs.85%29.aspx</param>
            <param name="pvStructInfo">A pointer to the structure to be encoded.</param>
            <param name="pbEncoded">A pointer to a buffer to receive the encoded structure. This parameter can be NULL to retrieve the size of this information for memory allocation purposes.</param>
            <param name="pcbEncoded">A pointer to a DWORD variable that contains the size, in bytes, of the buffer pointed to by the pbEncoded parameter.</param>
            <returns></returns>
        </member>
    </members>
</doc>
