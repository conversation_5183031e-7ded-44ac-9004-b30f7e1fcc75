﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="JWT" version="6.1.4" targetFramework="net46" />
  <package id="Newtonsoft.Json" version="10.0.1" targetFramework="net46" />
  <package id="Plivo" version="4.15.1" targetFramework="net46" />
  <package id="System.IO.FileSystem" version="4.3.0" targetFramework="net46" />
  <package id="System.IO.FileSystem.Primitives" version="4.3.0" targetFramework="net46" />
  <package id="System.Net.Http" version="4.3.4" targetFramework="net46" />
  <package id="System.Security.Cryptography.Algorithms" version="4.3.0" targetFramework="net46" />
  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="net46" />
  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="net46" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.3.0" targetFramework="net46" />
  <package id="System.Text.Encoding.Extensions" version="4.3.0" targetFramework="net46" />
</packages>