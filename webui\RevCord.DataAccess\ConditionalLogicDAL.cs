﻿using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.IQ3ConditionalLogic;
using RevCord.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataAccess
{
    public class ConditionalLogicDAL
    {
        private int _tenantId;
        public ConditionalLogicDAL(int tenantId)
        {
            this._tenantId = tenantId;
        }

        public MarkerLogic SaveMarkerLogic(MarkerLogic markerLogic)
        {
            int markerLogicId = -1;
            //int logicTriggerId = -1;
            //int triggerActionId = -1;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.ConditionalLogic.CONDITIONALLOGIC_SAVE_MARKER_LOGIC;
                    cmd.Parameters.AddWithValue("@MarkerId", markerLogic.MarkerId);
                    cmd.Parameters.AddWithValue("@MarkerOptionId", markerLogic.MarkerOptionId);
                    cmd.Parameters.AddWithValue("@MatchText", markerLogic.MatchText);
                    cmd.Parameters.AddWithValue("@CreatedBy", markerLogic.CreatedBy);
                    //cmd.Parameters.AddWithValue("@MarkerTypeId", markerLogic.MarkerTypeId);
                    cmd.Parameters.AddWithValue("@LogicTypeId", (int)markerLogic.LogicType);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.ConditionalLogics, "SaveMarkerLogic-->SaveLogic", _tenantId));

                    markerLogicId = Convert.ToInt32(cmd.ExecuteScalar());
                    markerLogic.Id = markerLogicId;
                    conn.Close();
                }

                //var logicTriggers = markerLogic.LogicTriggers;

                //foreach (var logicTrigger in logicTriggers)
                //{
                //    logicTrigger.LogicId = markerLogicId;

                //    using (var conn = DALHelper.GetConnection(_tenantId))
                //    using (var cmd = conn.CreateCommand())
                //    {
                //        cmd.CommandType = CommandType.StoredProcedure;
                //        cmd.CommandText = DBConstants.ConditionalLogic.CONDITIONALLOGIC_SAVE_LOGIC_TRIGGER;
                //        cmd.Parameters.AddWithValue("@LogicId", logicTrigger.LogicId);
                //        cmd.Parameters.AddWithValue("@TriggerTypeId", (int)logicTrigger.TriggerType);
                //        conn.Open();
                //        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.ConditionalLogics, "SaveMarkerLogic-->SaveTrigger", _tenantId));
                //        logicTriggerId = Convert.ToInt32(cmd.ExecuteScalar());
                //        logicTrigger.Id = logicTriggerId;
                //        conn.Close();
                //    }

                //    var triggerAction = logicTrigger.TriggerAction;
                //    triggerAction.MarkerId = markerLogic.MarkerId;
                //    triggerAction.MarkerLogicId = markerLogicId;
                //    triggerAction.TriggerId = logicTriggerId;

                //    using (var conn = DALHelper.GetConnection(_tenantId))
                //    using (var cmd = conn.CreateCommand())
                //    {
                //        cmd.CommandType = CommandType.StoredProcedure;
                //        cmd.CommandText = DBConstants.ConditionalLogic.CONDITIONALLOGIC_SAVE_TRIGGER_ACTION;
                //        cmd.Parameters.AddWithValue("@MarkerId", triggerAction.MarkerId);
                //        cmd.Parameters.AddWithValue("@MarkerLogicId", triggerAction.MarkerLogicId);
                //        cmd.Parameters.AddWithValue("@TriggerId", triggerAction.TriggerId);
                //        cmd.Parameters.AddWithValue("@TriggerTypeId", (int)triggerAction.TriggerType);
                //        cmd.Parameters.AddWithValue("@CSVEmail", triggerAction.CSVEmail);
                //        cmd.Parameters.AddWithValue("@CSVPhoneNumber", triggerAction.CSVPhoneNumber);
                //        cmd.Parameters.AddWithValue("@MarkerToFocusJSON", triggerAction.MarkerToFocusJSON);
                //        cmd.Parameters.AddWithValue("@MarkersToHideJSON", triggerAction.MarkersToHideJSON);
                //        cmd.Parameters.AddWithValue("@MarkersToUnHideJSON", triggerAction.MarkersToUnHideJSON);
                //        conn.Open();
                //        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.ConditionalLogics, "SaveMarkerLogic-->SaveTriggerAction", _tenantId));
                //        triggerActionId = Convert.ToInt32(cmd.ExecuteScalar());
                //        triggerAction.Id = triggerActionId;
                //        conn.Close();
                //    }
                //}
            }
            catch (Exception ex) { throw ex; }
            return markerLogic;
        }

        public LogicTrigger SaveLogicTrigger(LogicTrigger logicTrigger)
        {
            int logicTriggerId = -1;
            int triggerActionId = -1;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                    using (var cmd = conn.CreateCommand())
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandText = DBConstants.ConditionalLogic.CONDITIONALLOGIC_SAVE_LOGIC_TRIGGER;
                        cmd.Parameters.AddWithValue("@LogicId", logicTrigger.LogicId);
                        cmd.Parameters.AddWithValue("@TriggerTypeId", (int)logicTrigger.TriggerType);
                        conn.Open();
                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.ConditionalLogics, "SaveLogicTrigger", _tenantId));
                        logicTriggerId = Convert.ToInt32(cmd.ExecuteScalar());
                        logicTrigger.Id = logicTriggerId;
                        conn.Close();
                    }

                    var triggerAction = logicTrigger.TriggerAction;
                    
                    triggerAction.TriggerId = logicTriggerId;

                    using (var conn = DALHelper.GetConnection(_tenantId))
                    using (var cmd = conn.CreateCommand())
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandText = DBConstants.ConditionalLogic.CONDITIONALLOGIC_SAVE_TRIGGER_ACTION;
                        cmd.Parameters.AddWithValue("@MarkerId", triggerAction.MarkerId);
                        cmd.Parameters.AddWithValue("@MarkerLogicId", triggerAction.MarkerLogicId);
                        cmd.Parameters.AddWithValue("@TriggerId", triggerAction.TriggerId);
                        cmd.Parameters.AddWithValue("@TriggerTypeId", (int)triggerAction.TriggerType);
                        cmd.Parameters.AddWithValue("@CSVEmail", triggerAction.CSVEmail);
                        cmd.Parameters.AddWithValue("@CSVPhoneNumber", triggerAction.CSVPhoneNumber);
                        cmd.Parameters.AddWithValue("@MarkerToFocusJSON", triggerAction.MarkerToFocusJSON);
                        cmd.Parameters.AddWithValue("@MarkersToHideJSON", triggerAction.MarkersToHideJSON);
                        cmd.Parameters.AddWithValue("@MarkersToUnHideJSON", triggerAction.MarkersToUnHideJSON);
                        conn.Open();
                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.ConditionalLogics, "SaveLogicTrigger-->SaveTriggerAction", _tenantId));
                        triggerActionId = Convert.ToInt32(cmd.ExecuteScalar());
                        triggerAction.Id = triggerActionId;
                        conn.Close();
                    }
            }
            catch (Exception ex) { throw ex; }
            return logicTrigger;
        }

        public List<MarkerLogic> GetMarkerLogics(int markerId)
        {
            List<MarkerLogic> markerLogics = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "select * from iq3MarkerLogic where MarkerId = @MarkerId and IsDeleted = 0;";
                    cmd.Parameters.AddWithValue("@MarkerId", markerId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.ConditionalLogics, "GetMarkerLogics-->select logic", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        markerLogics = ORMapper.GetAllMarkerLogics(dr);
                    }
                }

                if (markerLogics != null)
                {
                    foreach (var markerLogic in markerLogics)
                    {
                        using (var conn = DALHelper.GetConnection(_tenantId))
                        using (var cmd = conn.CreateCommand())
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.CommandText = "select * from iq3LogicTrigger where LogicId = @LogicId and IsDeleted = 0;";
                            cmd.Parameters.AddWithValue("@LogicId", markerLogic.Id);
                            conn.Open();
                            Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.ConditionalLogics, "GetMarkerLogics-->select trigger", _tenantId));

                            using (SqlDataReader dr = cmd.ExecuteReader())
                            {
                                markerLogic.LogicTriggers = ORMapper.GetLogicTriggers(dr);
                            }
                        }

                        if (markerLogic.LogicTriggers != null && markerLogic.LogicTriggers.Count > 0)
                        {
                            foreach (var logicTrigger in markerLogic.LogicTriggers)
                            {
                                using (var conn = DALHelper.GetConnection(_tenantId))
                                using (var cmd = conn.CreateCommand())
                                {
                                    cmd.CommandType = CommandType.Text;
                                    cmd.CommandText = "select * from iq3TriggerAction where TriggerId = @TriggerId and IsDeleted = 0;";
                                    cmd.Parameters.AddWithValue("@TriggerId", logicTrigger.Id);
                                    conn.Open();
                                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.ConditionalLogics, "GetMarkerLogics-->select trigger action", _tenantId));

                                    using (SqlDataReader dr = cmd.ExecuteReader())
                                    {
                                        logicTrigger.TriggerAction = ORMapper.GetTriggerAction(dr);
                                    }
                                }
                            }
                            
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return markerLogics;
        }

        public bool DeleteMarkerLogic(int markerLogicId)
        {
            try
            {
                var returnValue = false;
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();

                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;

                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.ConditionalLogic.CONDITIONALLOGIC_DELETE_MARKER_LOGIC;
                    cmd.Parameters.AddWithValue("@MarkerLogicId", markerLogicId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.ConditionalLogics, "DeleteMarkerLogic", _tenantId));

                    returnValue = Convert.ToBoolean(cmd.ExecuteNonQuery());

                    tran.Commit();

                    return returnValue;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public bool DeleteLogicTrigger(int triggerId)
        {
            try
            {
                var returnValue = false;
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();

                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;

                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.ConditionalLogic.CONDITIONALLOGIC_DELETE_LOGIC_TRIGGER;
                    cmd.Parameters.AddWithValue("@TriggerId", triggerId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.ConditionalLogics, "DeleteLogicTrigger", _tenantId));

                    returnValue = Convert.ToBoolean(cmd.ExecuteNonQuery());

                    tran.Commit();

                    return returnValue;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        //public MarkerLogic UpdateMarkerLogic(MarkerLogic markerLogic)
        //{
        //    int markerLogicId = -1;
        //    int logicTriggerId = -1;
        //    int triggerActionId = -1;
        //    try
        //    {
        //        using (var conn = DALHelper.GetConnection(_tenantId))
        //        using (var cmd = conn.CreateCommand())
        //        {
        //            cmd.CommandType = CommandType.StoredProcedure;
        //            cmd.CommandText = DBConstants.ConditionalLogic.CONDITIONALLOGIC_UPDATE_MARKER_LOGIC;
        //            cmd.Parameters.AddWithValue("@MarkerId", markerLogic.MarkerId);
        //            cmd.Parameters.AddWithValue("@MarkerOptionId", markerLogic.MarkerOptionId);
        //            cmd.Parameters.AddWithValue("@MatchText", markerLogic.MatchText);
        //            cmd.Parameters.AddWithValue("@LogicId", markerLogic.Id);
        //            cmd.Parameters.AddWithValue("@LogicTypeId", (int)markerLogic.LogicType);
        //            conn.Open();
        //            Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.ConditionalLogics, "UpdateMarkerLogic-->UpdateLogic Id = " + markerLogic.Id.ToString(), _tenantId));

        //            markerLogicId = Convert.ToInt32(cmd.ExecuteScalar());
        //            //markerLogic.Id = markerLogicId;
        //            conn.Close();
        //        }

        //        var logicTriggers = markerLogic.LogicTriggers;

        //        foreach (var logicTrigger in logicTriggers)
        //        {
        //            using (var conn = DALHelper.GetConnection(_tenantId))
        //            using (var cmd = conn.CreateCommand())
        //            {
        //                cmd.CommandType = CommandType.StoredProcedure;
        //                cmd.CommandText = DBConstants.ConditionalLogic.CONDITIONALLOGIC_UPDATE_LOGIC_TRIGGER;
        //                cmd.Parameters.AddWithValue("@LogicTriggerId", logicTrigger.Id);
        //                cmd.Parameters.AddWithValue("@TriggerTypeId", (int)logicTrigger.TriggerType);
        //                conn.Open();
        //                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.ConditionalLogics, "UpdateMarkerLogic-->UpdateTrigger Id = " + logicTrigger.Id.ToString(), _tenantId));
        //                logicTriggerId = Convert.ToInt32(cmd.ExecuteScalar());
        //                conn.Close();
        //            }

        //            var triggerAction = logicTrigger.TriggerAction;

        //            using (var conn = DALHelper.GetConnection(_tenantId))
        //            using (var cmd = conn.CreateCommand())
        //            {
        //                cmd.CommandType = CommandType.StoredProcedure;
        //                cmd.CommandText = DBConstants.ConditionalLogic.CONDITIONALLOGIC_SAVE_TRIGGER_ACTION;
        //                cmd.Parameters.AddWithValue("@TriggerActionId", triggerAction.Id);
        //                cmd.Parameters.AddWithValue("@TriggerId", triggerAction.TriggerId);
        //                cmd.Parameters.AddWithValue("@TriggerTypeId", (int)triggerAction.TriggerType);
        //                cmd.Parameters.AddWithValue("@CSVEmail", triggerAction.CSVEmail);
        //                cmd.Parameters.AddWithValue("@CSVPhoneNumber", triggerAction.CSVPhoneNumber);
        //                cmd.Parameters.AddWithValue("@MarkerToFocusJSON", triggerAction.MarkerToFocusJSON);
        //                cmd.Parameters.AddWithValue("@MarkersToHideJSON", triggerAction.MarkersToHideJSON);
        //                cmd.Parameters.AddWithValue("@MarkersToUnHideJSON", triggerAction.MarkersToUnHideJSON);
        //                conn.Open();
        //                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.ConditionalLogics, "UpdateMarkerLogic-->UpdateTriggerAction", _tenantId));
        //                triggerActionId = Convert.ToInt32(cmd.ExecuteScalar());
        //                //triggerAction.Id = triggerActionId;
        //                conn.Close();
        //            }
        //        }
        //    }
        //    catch (Exception ex) { throw ex; }
        //    return markerLogic;
        //}

        public MarkerLogic UpdateMarkerLogicType(MarkerLogic markerLogic)
        {
            int markerLogicId = -1;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.ConditionalLogic.CONDITIONALLOGIC_UPDATE_MARKER_LOGIC_TYPE;
                    cmd.Parameters.AddWithValue("@LogicId", markerLogic.Id);
                    cmd.Parameters.AddWithValue("@LogicTypeId", (int)markerLogic.LogicType);
                    cmd.Parameters.AddWithValue("@MatchText", markerLogic.MatchText);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.ConditionalLogics, "UpdateMarkerLogicType-->UpdateLogic Id = " + markerLogic.Id.ToString(), _tenantId));

                    markerLogicId = Convert.ToInt32(cmd.ExecuteScalar());
                    //markerLogic.Id = markerLogicId;
                    conn.Close();
                }
            }
            catch (Exception ex) { throw ex; }
            return markerLogic;
        }

        public MarkerLogic UpdateMarkerLogicMatchText(MarkerLogic markerLogic)
        {
            int markerLogicId = -1;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.ConditionalLogic.CONDITIONALLOGIC_UPDATE_MARKER_LOGIC_MATCH_TEXT;
                    cmd.Parameters.AddWithValue("@LogicId", markerLogic.Id);
                    cmd.Parameters.AddWithValue("@MatchText", markerLogic.MatchText);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.ConditionalLogics, "UpdateMarkerLogicMatchText-->UpdateLogic Id = " + markerLogic.Id.ToString(), _tenantId));

                    markerLogicId = Convert.ToInt32(cmd.ExecuteScalar());
                    conn.Close();
                }
            }
            catch (Exception ex) { throw ex; }
            return markerLogic;
        }

        public TriggerAction FetchTriggerAction(int logicTriggerId, int triggerTypeId) {
            TriggerAction triggerAction = new TriggerAction();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "select * from iq3TriggerAction where TriggerId = @TriggerId AND TriggerTypeId = @TriggerTypeId and IsDeleted = 0;";
                    cmd.Parameters.AddWithValue("@TriggerId", logicTriggerId);
                    cmd.Parameters.AddWithValue("@TriggerTypeId", triggerTypeId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.ConditionalLogics, "FetchTriggerAction", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        triggerAction = ORMapper.GetTriggerAction(dr);
                    }
                }
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.ConditionalLogics, "FetchTriggerAction", _tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return triggerAction;
        }

        public TriggerAction UpdateTriggerAction(TriggerAction triggerAction)
        {
            int triggerActionId = -1;
            try
            {
                    using (var conn = DALHelper.GetConnection(_tenantId))
                    using (var cmd = conn.CreateCommand())
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandText = DBConstants.ConditionalLogic.CONDITIONALLOGIC_UPDATE_TRIGGER_ACTION;
                        cmd.Parameters.AddWithValue("@TriggerActionId", triggerAction.Id);
                        cmd.Parameters.AddWithValue("@CSVEmail", triggerAction.CSVEmail);
                        cmd.Parameters.AddWithValue("@CSVPhoneNumber", triggerAction.CSVPhoneNumber);
                        cmd.Parameters.AddWithValue("@MarkerToFocusJSON", triggerAction.MarkerToFocusJSON);
                        cmd.Parameters.AddWithValue("@MarkersToHideJSON", triggerAction.MarkersToHideJSON);
                        cmd.Parameters.AddWithValue("@MarkersToUnHideJSON", triggerAction.MarkersToUnHideJSON);
                        conn.Open();
                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.ConditionalLogics, "UpdateMarkerLogic-->UpdateTriggerAction", _tenantId));
                        triggerActionId = Convert.ToInt32(cmd.ExecuteScalar());
                        conn.Close();
                    }
                }
            catch (Exception ex) { throw ex; }
            return triggerAction;
        }
    }
}