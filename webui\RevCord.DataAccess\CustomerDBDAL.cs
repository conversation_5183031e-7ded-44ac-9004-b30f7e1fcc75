﻿using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.CustomerDBEntities;
using RevCord.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataAccess
{
    public class CustomerDBDAL
    {
        private int _tenantId;
        public CustomerDBDAL(int tenantId)
        {
            this._tenantId = tenantId;
        }

        #region IQ3 Asset Model

        public List<IQ3AssetModel> GetIQ3AssetsModel()
        {
            List<IQ3AssetModel> iq3AssetsModel = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.CustomerDB.IQ3_ASSET_MODEL_GET;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.CustomerDB, "GetIQ3AssetsModel", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            iq3AssetsModel = ORMapper.GetIQ3AssetsModel(dr);
                        }
                    }
                }

                return iq3AssetsModel;
            }
            catch (Exception ex) { throw ex; }
        }

        public List<IQ3AssetModel> GetIQ3AssetsModelForDelete()
        {
            List<IQ3AssetModel> iq3AssetsModel = new List<IQ3AssetModel>();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.CustomerDB.IQ3_ASSET_MODEL_GET_FOR_DELETE;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.CustomerDB, "GetIQ3AssetsModelForDelete", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            iq3AssetsModel = ORMapper.GetIQ3AssetsModel(dr);
                        }
                    }
                }

                return iq3AssetsModel;
            }
            catch (Exception ex) { throw ex; }
        }

        public List<IQ3AssetModel> GetIQ3AssetsModelForRecover()
        {
            List<IQ3AssetModel> iq3AssetsModel = new List<IQ3AssetModel>();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.CustomerDB.IQ3_ASSET_MODEL_GET_FOR_RECOVER;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.CustomerDB, "GetIQ3AssetsModelForRecover", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            iq3AssetsModel = ORMapper.GetIQ3AssetsModel(dr);
                        }
                    }
                }

                return iq3AssetsModel;
            }
            catch (Exception ex) { throw ex; }
        }

        public int UpdateIQ3AssetModel(List<IQ3AssetModel> model)
        {
            try
            {
                int rowAffected = 0;
                using (var conn = DALHelper.GetConnection(_tenantId))
                {
                    conn.Open();
                    using (var tran = conn.BeginTransaction("TransMT"))
                    {
                        using (var cmd = conn.CreateCommand())
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.CommandText = DBConstants.CustomerDB.IQ3_ASSET_MODEL_UPDATE;
                            cmd.Transaction = tran as SqlTransaction;
                            foreach (var field in model)
                            {
                                cmd.Parameters.AddWithValue("@IQ3AssetModelId", field.IQ3AssetModelId);
                                cmd.Parameters.AddWithValue("@Serial", field.Serial);
                                cmd.Parameters.AddWithValue("@Alignment", field.Alignment);
                                cmd.Parameters.AddWithValue("@Caption", field.Caption);
                                cmd.Parameters.AddWithValue("@IsVisible", field.IsVisible);

                                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.CustomerDB, "UpdateIQ3AssetModel", _tenantId));

                                cmd.ExecuteNonQuery();
                                cmd.Parameters.Clear();
                                rowAffected++;
                            }
                        }
                        tran.Commit();
                    }
                    return rowAffected;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public bool AddFieldIQ3AssetModel(IQ3AssetModel model)
        {
            bool isFieldAdded = false;
            try
            {
                int fieldCount = GetModelFieldCount();
                string ColumnName = string.Format("Field{0}", fieldCount + 1);


                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.CustomerDB.IQ3_ASSET_MODEL_ADD_FIELD;
                    cmd.Parameters.AddWithValue("@ColumnName", ColumnName);
                    cmd.Parameters.AddWithValue("@Caption", model.Caption);
                    conn.Open();

                    int count = (int)cmd.ExecuteNonQuery();
                    isFieldAdded = (count > 0) ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
            return isFieldAdded;
        }

        public bool DeleteIQ3AssetModel(string IQ3AssetModelIds)
        {
            bool isDeleted = false;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.CustomerDB.IQ3_ASSET_MODEL_DELETE;
                    cmd.Parameters.AddWithValue("@IQ3AssetModelIds", IQ3AssetModelIds);
                    conn.Open();

                    int count = (int)cmd.ExecuteNonQuery();
                    isDeleted = (count > 0) ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
            return isDeleted;
        }

        public bool UnDeleteIQ3AssetModel(string IQ3AssetModelIds)
        {
            bool isDeleted = false;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.CustomerDB.IQ3_ASSET_MODEL_UNDELETE;
                    cmd.Parameters.AddWithValue("@IQ3AssetModelIds", IQ3AssetModelIds);
                    conn.Open();

                    int count = (int)cmd.ExecuteNonQuery();
                    isDeleted = (count > 0) ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
            return isDeleted;
        }

        public int ReorderModel(List<IQ3AssetModel> model)
        {
            try
            {
                int rowAffected = 0;
                using (var conn = DALHelper.GetConnection(_tenantId))
                {
                    conn.Open();
                    using (var tran = conn.BeginTransaction("TransMT"))
                    {
                        using (var cmd = conn.CreateCommand())
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.CommandText = DBConstants.CustomerDB.IQ3_ASSET_MODEL_REORDER;
                            cmd.Transaction = tran as SqlTransaction;
                            foreach (var field in model)
                            {
                                cmd.Parameters.AddWithValue("@Serial", field.Serial);
                                cmd.Parameters.AddWithValue("@ColumnName", field.Name);

                                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Tenant, "ReorderModel", _tenantId));
                                cmd.ExecuteNonQuery();
                                cmd.Parameters.Clear();
                                rowAffected++;
                            }
                        }
                        tran.Commit();
                    }
                    return rowAffected;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public int GetModelFieldCount()
        {
            int iFieldCount = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT COUNT(*) AS Count FROM cd_iq3Asset_Model ";
                    conn.Open();

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            if (dr.Read())
                            {
                                iFieldCount = Convert.ToInt32(dr["Count"]);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }

            return iFieldCount;
        }

        #endregion IQ3 Asset Model

        #region IQ3 Asset

        public List<Object> GetAllIQ3Assets()
        {
            List<Object> iq3AssetList = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.CustomerDB.IQ3_ASSET_DATA_GET_ALL;
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.CustomerDB, "GetAllIQ3Assets", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            int NumFields = 0;
                            if (dr.Read())
                            {
                                NumFields = Convert.ToInt32(dr["Count"]);
                            }

                            dr.NextResult();
                            iq3AssetList = ORMapper.GetIQ3Assets(dr, NumFields);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return iq3AssetList;
        }

        public List<Object> SearchAllIQ3Assets(string SearchText)
        {
            List<Object> iq3AssetList = null;

            try
            {
                string OptionStr = " AND 1 = 1";
                if (string.Equals(SearchText, "Available", StringComparison.OrdinalIgnoreCase))
                {
                    OptionStr = " OR cd.Status = 1 ";
                }
                else if (string.Equals(SearchText, "Unavailable", StringComparison.OrdinalIgnoreCase))
                {
                    OptionStr = " OR cd.Status = 2 ";
                }

                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.CustomerDB.IQ3_ASSET_DATA_GET_SEARCH_ALL;
                    cmd.Parameters.AddWithValue("@SearchText", SearchText);
                    cmd.Parameters.AddWithValue("@OptionStr", OptionStr);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.CustomerDB, "SearchAllIQ3Assets", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            int NumFields = 0;
                            if (dr.Read())
                            {
                                NumFields = Convert.ToInt32(dr["Count"]);
                            }

                            dr.NextResult();
                            iq3AssetList = ORMapper.GetIQ3Assets(dr, NumFields);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return iq3AssetList;
        }

        public Object GetIQ3AssetById(int Id)
        {
            Object iq3Asset = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.CustomerDB.IQ3_ASSET_DATA_GET_BY_ID;
                    cmd.Parameters.AddWithValue("@Id", Id);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InspectionTemplate, "GetIQ3AssetById", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            int NumFields = 0;
                            if (dr.Read())
                            {
                                NumFields = Convert.ToInt32(dr["Count"]);
                            }

                            dr.NextResult();
                            iq3Asset = ORMapper.GetIQ3Asset(dr, NumFields);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return iq3Asset;
        }

        public bool AddIQ3Asset(IQ3AssetAdd iq3Asset, int UserNum)
        {
            try
            {
                bool isAdded = false;

                if (!CheckIfAssetIdAlreadyExists(iq3Asset.AssetId))
                {

                    using (var conn = DALHelper.GetConnection(_tenantId))
                    {
                        conn.Open();
                        using (var tran = conn.BeginTransaction("TransMT"))
                        {
                            using (var cmd = conn.CreateCommand())
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.CommandText = DBConstants.CustomerDB.IQ3_ASSET_DATA_ADD;
                                cmd.Transaction = tran as SqlTransaction;
                                cmd.Parameters.AddWithValue("@AssetId", iq3Asset.AssetId);
                                cmd.Parameters.AddWithValue("@UserNum", UserNum);

                                int assetId = Convert.ToInt32(cmd.ExecuteScalar());

                                if (iq3Asset.IQ3AssetData != null)
                                {
                                    cmd.Parameters.Clear();
                                    cmd.CommandType = CommandType.Text;
                                    cmd.Transaction = tran as SqlTransaction;

                                    string CommandText = "";


                                    foreach (var data in iq3Asset.IQ3AssetData)
                                    {
                                        CommandText = CommandText + string.Format(", {0} = @{0}", data.Field);
                                        cmd.Parameters.AddWithValue(string.Format("@{0}", data.Field), data.Value);
                                    }

                                    cmd.CommandText = string.Format("UPDATE cd_iq3Asset_Data SET Status = 1 {0} WHERE Id = @Id", CommandText);
                                    cmd.Parameters.AddWithValue("@Id", assetId);

                                    int count = cmd.ExecuteNonQuery();
                                    isAdded = (assetId > 0 && count > 0) ? true : false;
                                }

                                if (iq3Asset.IQ3AssetDetails != null)
                                {
                                    foreach (var details in iq3Asset.IQ3AssetDetails)
                                    {
                                        cmd.Parameters.Clear();
                                        cmd.CommandType = CommandType.StoredProcedure;
                                        cmd.CommandText = DBConstants.CustomerDB.IQ3_ASSET_DATA_DETAILS;
                                        cmd.Parameters.AddWithValue("@AssetId", assetId);
                                        cmd.Parameters.AddWithValue("@Name", details.Name);
                                        cmd.Parameters.AddWithValue("@Value", details.Value);

                                        cmd.Transaction = tran as SqlTransaction;

                                        cmd.ExecuteNonQuery();
                                    }
                                }


                            }
                            tran.Commit();
                        }
                        return isAdded;
                    }
                }
                else
                {
                    throw new Exception("Asset Id already exists.");
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public bool UpdateIQ3Asset(IQ3AssetUpdate iq3AssetUpdate)
        {
            bool isUpdated = false;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                {
                    conn.Open();
                    using (var tran = conn.BeginTransaction("TransMT"))
                    {
                        using (var cmd = conn.CreateCommand())
                        {

                            if (iq3AssetUpdate.IQ3AssetData != null)
                            {
                                cmd.Parameters.Clear();
                                cmd.CommandType = CommandType.Text;
                                cmd.Transaction = tran as SqlTransaction;

                                string CommandText = "";


                                foreach (var data in iq3AssetUpdate.IQ3AssetData)
                                {
                                    CommandText = CommandText + string.Format(", {0} = @{0}", data.Field);
                                    cmd.Parameters.AddWithValue(string.Format("@{0}", data.Field), data.Value);
                                }

                                cmd.CommandText = string.Format("UPDATE cd_iq3Asset_Data SET Status = 1 {0} WHERE Id = @Id", CommandText);
                                cmd.Parameters.AddWithValue("@Id", iq3AssetUpdate.AssetId);

                                int count = cmd.ExecuteNonQuery();
                                isUpdated = (count > 0) ? true : false;
                            }

                            if (iq3AssetUpdate.IQ3AssetDetails != null)
                            {
                                foreach (var details in iq3AssetUpdate.IQ3AssetDetails)
                                {
                                    cmd.Parameters.Clear();
                                    cmd.CommandType = CommandType.StoredProcedure;
                                    cmd.CommandText = DBConstants.CustomerDB.IQ3_ASSET_DATA_DETAILS;
                                    cmd.Parameters.AddWithValue("@AssetId", iq3AssetUpdate.AssetId);
                                    cmd.Parameters.AddWithValue("@Id", details.Id);
                                    cmd.Parameters.AddWithValue("@Name", details.Name);
                                    cmd.Parameters.AddWithValue("@Value", details.Value);

                                    cmd.Transaction = tran as SqlTransaction;

                                    int count = cmd.ExecuteNonQuery();
                                    isUpdated = (count > 0) ? true : false;
                                }
                            }
                        }
                        tran.Commit();
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return isUpdated;
        }

        public bool UpdateStatusIQ3Asset(int AssetId, int Status)
        {
            bool isUpdated = false;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = string.Format("UPDATE cd_iq3Asset_Data SET Status = @Status WHERE Id = @Id ");
                    cmd.Parameters.AddWithValue("@Id", AssetId);
                    cmd.Parameters.AddWithValue("@Status", Status);
                    conn.Open();

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InspectionTemplate, "UpdateStatusIQ3Asset", _tenantId));

                    int count = (int)cmd.ExecuteNonQuery();
                    isUpdated = (count > 0) ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
            return isUpdated;
        }

        public bool UpdateAssetPhoto(int id, string AssetPhoto)
        {
            bool isUpdated = false;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = string.Format("UPDATE cd_iq3Asset_Data SET AssetPhoto = @AssetPhoto WHERE Id = @Id ");
                    cmd.Parameters.AddWithValue("@Id", id);
                    cmd.Parameters.AddWithValue("@AssetPhoto", AssetPhoto);
                    conn.Open();

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InspectionTemplate, "UpdateAssetPhoto", _tenantId));

                    int count = (int)cmd.ExecuteNonQuery();
                    isUpdated = (count > 0) ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
            return isUpdated;
        }

        public bool CheckIfAssetIdAlreadyExists(string AssetId)
        {
            bool isExists = false;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT COUNT(*) AS Count FROM cd_iq3Asset_Data WHERE AssetId = @AssetId ";
                    cmd.Parameters.AddWithValue("@AssetId", AssetId);
                    conn.Open();

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            if (dr.Read())
                            {
                                int count = Convert.ToInt32(dr["Count"]);
                                isExists = (count > 0) ? true : false;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }

            return isExists;
        }



        public List<IQ3AssetHistory> GetIQ3AssetHistoryByAssetId(int AssetId)
        {
            List<IQ3AssetHistory> iq3AssetAuditHistory = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.CustomerDB.IQ3_ASSET_HISTORY_GET_BY_ASSET_ID;
                    cmd.Parameters.AddWithValue("@AssetId", AssetId);
                    conn.Open();

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InspectionTemplate, "GetIQ3AssetHistoryByAssetId", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            iq3AssetAuditHistory = ORMapper.GetIQ3AssetHistory(dr);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return iq3AssetAuditHistory;
        }

        public bool LogIQ3AssetHistory(IQ3AssetHistory iq3AssetHistory)
        {
            bool isUpdated = false;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.CustomerDB.IQ3_ASSET_HISTORY_INSERT;
                    cmd.Parameters.AddWithValue("@AssetId", iq3AssetHistory.AssetId);
                    cmd.Parameters.AddWithValue("@UserNum", iq3AssetHistory.UserNum);
                    cmd.Parameters.AddWithValue("@HistoryEvent", iq3AssetHistory.HistoryEvent);
                    cmd.Parameters.AddWithValue("@Field", iq3AssetHistory.Field);
                    cmd.Parameters.AddWithValue("@FieldTitle", iq3AssetHistory.FieldTitle);
                    cmd.Parameters.AddWithValue("@ChangedFrom", iq3AssetHistory.ChangedFrom);
                    cmd.Parameters.AddWithValue("@ChangedTo", iq3AssetHistory.ChangedTo);
                    conn.Open();

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InspectionTemplate, "LogIQ3AssetHistory", _tenantId));

                    int count = (int)cmd.ExecuteNonQuery();
                    isUpdated = (count > 0) ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
            return isUpdated;
        }

        #endregion IQ3 Asset

        #region Assets
        public List<Asset>FetchAllAssets()
        {
            List<Asset> iq3AssetList = new List<Asset>();

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.CustomerDB.IQ3_ASSET_FETCH_ALL;
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.CustomerDB, "FetchAllAssets", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            iq3AssetList = ORMapper.FetchAllAssets(dr);

                            if (iq3AssetList.Count > 0) {
                                int firstAssetId = iq3AssetList[0].Id;
                                iq3AssetList.FirstOrDefault().AssetDetails = FetchAssetDetailsById(firstAssetId);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return iq3AssetList;
        }

        public List<AssetDetail> FetchAssetDetailsById(int assetId)
        {
            List<AssetDetail> assetDetails = new List<AssetDetail>();

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.CustomerDB.IQ3_ASSET_FETCH_DETAILS_BY_ID;
                    cmd.Parameters.AddWithValue("@AssetId", assetId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.CustomerDB, "FetchAssetDetailsById", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            assetDetails = ORMapper.FetchAssetDetailsById(dr);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return assetDetails;
        }

        public bool UpdateVisibleStatus(int id, bool Status)
        {
            bool isUpdated = false;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = string.Format("UPDATE cd_iq3Asset_Details SET IsVisible = @VisibleStatus WHERE Id = @Id ");
                    cmd.Parameters.AddWithValue("@Id", id);
                    cmd.Parameters.AddWithValue("@VisibleStatus", Status);
                    conn.Open();

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InspectionTemplate, "UpdateVisibleStatus", _tenantId));

                    int count = (int)cmd.ExecuteNonQuery();
                    isUpdated = (count > 0) ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
            return isUpdated;
        }
        #endregion
    }
}
