﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.InquireEntities;
using System.Threading.Tasks;
using System.Data;
using RevCord.DataContracts;
using System.Data.SqlClient;
using RevCord.Util;
using RevCord.DataAccess.Util;

namespace RevCord.DataAccess
{
    public class InquireDAL
    {
        private int _tenantId;
        public InquireDAL(int tenantId)
        {
            _tenantId = tenantId;
        }

        public bool CheckAlreadyInvited(string email, InvitationStatus status)
        {
            using (var conn = DALHelper.GetConnection(_tenantId))
            using (var cmd = conn.CreateCommand())
            {
                cmd.CommandType = CommandType.Text;
                cmd.CommandText = "SELECT * FROM umInvitation where (SentByEmail = @email) AND (StatusId = @status) AND (IsDeleted = 0)";
                cmd.Parameters.AddWithValue("@email", email);
                cmd.Parameters.AddWithValue("@status", (int)status);
                conn.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inquire, "CheckAlreadyInvited", _tenantId));
                using (var reader = cmd.ExecuteReader())
                {
                    return !reader.HasRows;
                }
            }
        }

        public int InsertInvitation(Invitation invitation)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inquire.INVITATION_INSERT;

                    cmd.Parameters.AddWithValue("@InvitationURL", invitation.InvitationURL);
                    cmd.Parameters.AddWithValue("@InviteCode", invitation.InviteCode);
                    cmd.Parameters.AddWithValue("@SentBy", invitation.SentBy);
                    cmd.Parameters.AddWithValue("@SentTo", invitation.SentTo);
                    cmd.Parameters.AddWithValue("@SentByEmail", invitation.SentByEmail);
                    cmd.Parameters.AddWithValue("@SentToEmail", invitation.SentToEmail);
                    cmd.Parameters.AddWithValue("@StatusId", (int)invitation.Status);
                    cmd.Parameters.AddWithValue("@SentOn", invitation.SentOn);
                    //cmd.Parameters.AddWithValue("@AcceptedOn", invitation.AcceptedOn);
                    cmd.Parameters.AddWithValue("@Comments", invitation.Comments);
                    //cmd.Parameters.AddWithValue("@IsDeleted", 0);
                    cmd.Parameters.AddWithValue("@CreatedDate", invitation.CreatedDate);
                    //cmd.Parameters.AddWithValue("@LastModifiedDate", invitation.
                    cmd.Parameters.AddWithValue("@CreatedBy", invitation.CreatedBy);
                    cmd.Parameters.AddWithValue("@ISDeviceUser", 1);

                    cmd.Parameters.AddWithValue("@TenantId", this._tenantId); //Added due to exception - Ankur Gupta
                    //cmd.Parameters.AddWithValue("@LastModifiedBy", invitation.
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inquire, "InsertInvitation", _tenantId));
                    int lastId = (int)cmd.ExecuteScalar();
                    return lastId;

                }
            }
            catch (Exception ex) { throw ex; }
        }

        public int ChangeInvitationStatus(int invitationId, InvitationStatus status, int? userId, DateTime? modifiedDate)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inquire.INVITATION_STATUS_UPDATE;
                    cmd.Parameters.AddWithValue("@InvitationId", invitationId);
                    cmd.Parameters.AddWithValue("@StatusId", (int)status);
                    cmd.Parameters.AddWithValue("@LastModifiedDate", modifiedDate);
                    cmd.Parameters.AddWithValue("@LastModifiedBy", userId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inquire, "ChangeInvitationStatus", _tenantId));

                    return cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public int CancelInvitation(int invitationId, InvitationStatus status, int userId, DateTime modifiedDate)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inquire.INVITATION_STATUS_UPDATE;
                    cmd.Parameters.AddWithValue("@InvitationId", invitationId);
                    cmd.Parameters.AddWithValue("@StatusId", (int)status);
                    cmd.Parameters.AddWithValue("@IsDeleted", 1);
                    cmd.Parameters.AddWithValue("@LastModifiedDate", modifiedDate);
                    cmd.Parameters.AddWithValue("@LastModifiedBy", userId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inquire, "CancelInvitation", _tenantId));
                    return cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public int RejectInvitation(int invitationId, InvitationStatus status, DateTime modifiedDate)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inquire.INVITATION_STATUS_UPDATE;
                    cmd.Parameters.AddWithValue("@InvitationId", invitationId);
                    cmd.Parameters.AddWithValue("@StatusId", (int)status);
                    cmd.Parameters.AddWithValue("@LastModifiedDate", modifiedDate);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inquire, "RejectInvitation", _tenantId));
                    return cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public Invitation GetInvitationById(int invitationId)
        {
            Invitation invitation = null;

            using (var conn = DALHelper.GetConnection(_tenantId))
            using (var cmd = conn.CreateCommand())
            {
                cmd.CommandType = CommandType.Text;
                cmd.CommandText = "SELECT * FROM umInvitation where (Id = @invitationId) AND (IsDeleted = 0)";
                cmd.Parameters.AddWithValue("@invitationId", invitationId);

                conn.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inquire, "GetInvitationById", _tenantId));
                using (var dr = cmd.ExecuteReader())
                {
                    //if (dr.HasRows)
                    if (dr.Read())
                    {
                        invitation = new Invitation();
                        invitation.Id = Convert.ToInt32(dr["Id"]);
                        invitation.InvitationURL = Convert.ToString(dr["InvitationURL"]);
                        invitation.InviteCode = Convert.ToString(dr["InviteCode"]);
                        invitation.SentBy = Convert.ToInt32(dr["SentBy"]);
                        //invitation.SentTo 
                        invitation.SentByEmail = Convert.ToString(dr["SentByEmail"]);
                        invitation.SentToEmail = Convert.ToString(dr["SentToEmail"]);
                        invitation.SentOn = Convert.ToDateTime(dr["SentOn"]);
                        //invitation.AcceptedOn 
                        invitation.Comments = Convert.ToString(dr["InvitationURL"]);
                        invitation.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                        invitation.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                        //invitation.LastModifiedDate 
                        //invitation.CreatedBy 
                        //invitation.LastModifiedBy 
                        invitation.Status = (InvitationStatus)Enum.Parse(typeof(InvitationStatus), Convert.ToString(dr["StatusId"]));
                    }
                }
            }
            return invitation;
        }

        public List<Invitation> GetInvitationsByWhereClause(string whereClause)
        {
            List<Invitation> invitations = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inquire.INVITATION_GETBY_WHERECLAUSE;
                    cmd.Parameters.AddWithValue("@WhereClause", whereClause + " and ISDeviceUser = 1");

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inquire, "GetInvitationsByWhereClause", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        invitations = new List<Invitation>();
                        while (dr.Read())
                        {
                            var invitation = new Invitation
                            {
                                Id = Convert.ToInt32(dr["Id"]),
                                InvitationURL = Convert.ToString(dr["InvitationURL"]),
                                InviteCode = Convert.ToString(dr["InviteCode"]),
                                SentBy = Convert.ToInt32(dr["SentBy"]),
                                //SentTo 
                                SentByEmail = Convert.ToString(dr["SentByEmail"]),
                                SentToEmail = Convert.ToString(dr["SentToEmail"]),
                                //SentOn = Convert.ToDateTime(dr["SentOn"]);
                                //AcceptedOn 
                                Comments = Convert.ToString(dr["InvitationURL"]),
                                IsDeleted = Convert.ToBoolean(dr["IsDeleted"]),
                                CreatedDate = Convert.ToDateTime(dr["CreatedDate"]),
                                //LastModifiedDate 
                                //CreatedBy 
                                //LastModifiedBy 
                                Status = (InvitationStatus)Enum.Parse(typeof(InvitationStatus), Convert.ToString(dr["StatusId"]))
                            };
                            invitations.Add(invitation);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return invitations;
        }

        public int AcceptInvitation(RegisterUser user, int invitationId, InvitationStatus status, DateTime modifiedDate)
        {
            int lastUserId = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                {
                    conn.Open();
                    using (var tran = conn.BeginTransaction("Inq"))
                    {
                        using (var cmdInvitation = conn.CreateCommand())
                        //using (var cmdInvitation = new SqlCommand(DBConstants.Inquire.INVITATION_ACCEPT, conn))
                        {
                            cmdInvitation.CommandText = DBConstants.Inquire.INVITATION_ACCEPT;
                            cmdInvitation.CommandType = CommandType.StoredProcedure;
                            cmdInvitation.Transaction = tran as SqlTransaction;

                            cmdInvitation.Parameters.AddWithValue("@InvitationId", invitationId);
                            cmdInvitation.Parameters.AddWithValue("@StatusId", (int)status);
                            cmdInvitation.Parameters.AddWithValue("@AcceptedOn", modifiedDate);
                            Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmdInvitation), Originator.Inquire, "AcceptInvitation", _tenantId));
                            int rowsAffected = cmdInvitation.ExecuteNonQuery();
                        }
                        //2. User Registration
                        using (var cmdUserManager = conn.CreateCommand())
                        {
                            cmdUserManager.CommandText = DBConstants.Inquire.REGISTER_INVITED_USER;
                            cmdUserManager.CommandType = CommandType.StoredProcedure;
                            cmdUserManager.Transaction = tran as SqlTransaction;

                            //TODO: Remove @UserID parameter
                            cmdUserManager.Parameters.AddWithValue("@UserID", user.Email);
                            cmdUserManager.Parameters.AddWithValue("@UserEmail", user.Email);
                            cmdUserManager.Parameters.AddWithValue("@UserName", user.UserName);
                            cmdUserManager.Parameters.AddWithValue("@UserPW", user.Password);
                            Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmdUserManager), Originator.Inquire, "CheckAlreadyInvited", _tenantId));
                            lastUserId = (int)cmdUserManager.ExecuteScalar();
                        }
                        tran.Commit();
                    }
                }
                //return lastUserId;
            }
            catch (Exception ex) { throw ex; }
            return lastUserId;
        }

        public string getcustomerid()
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inquire.GET_CUSTOMER_ID;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inquire, "getcustomerid", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            return Convert.ToString(dr["CustomerId"]);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return null;
        }

        public string getexpirydate()
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inquire.GET_EXPIRY_DATE;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inquire, "getexpirydate", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            return Convert.ToString(dr["Expiry_Date"]);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return null;
        }

        public string getextensionusingusernum(string s_usernum)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inquire.GET_EXT_USING_USER_NUM;

                    conn.Open();
                    cmd.Parameters.AddWithValue("@USERNUM", s_usernum);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inquire, "getextensionusingusernum", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            return Convert.ToString(dr["Ext"]);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return null;
        }




        //isalreadyexistscheck
        public int isalreadyexistscheck(string s_mailid)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inquire.checkifalreadyexistscheck;

                    conn.Open();
                    cmd.Parameters.AddWithValue("@MailID", s_mailid);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inquire, "isalreadyexistscheck", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            return Convert.ToInt32(dr["value"]);
                        }
                    }

                }
            }
            catch (Exception ex) { throw ex; }
            return 0;
        }


        public bool licansevalueinsert(License_Information Obj_license_information)
        {
            bool status = true;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                {
                    conn.Open();
                    using (var tran = conn.BeginTransaction("Inq"))
                    {
                        using (var cmdInvitation = conn.CreateCommand())
                        {
                            cmdInvitation.CommandText = DBConstants.Inquire.UPDATE_LICENSE_INFORMATION;
                            cmdInvitation.CommandType = CommandType.StoredProcedure;
                            cmdInvitation.Transaction = tran as SqlTransaction;

                            cmdInvitation.Parameters.AddWithValue("@USERNUM", 0);
                            cmdInvitation.Parameters.AddWithValue("@USERNAME", Obj_license_information.obj_USERNAME);
                            cmdInvitation.Parameters.AddWithValue("@EMAILID", Obj_license_information.obj_EMAILID);
                            cmdInvitation.Parameters.AddWithValue("@CUSTOMERID", Obj_license_information.obj_CUSTOMERID);
                            cmdInvitation.Parameters.AddWithValue("@LICENSEID", Obj_license_information.obj_LICENSEID);
                            cmdInvitation.Parameters.AddWithValue("@LICENSEPASSWORD", Obj_license_information.obj_LICENSEPASSWORD);
                            cmdInvitation.Parameters.AddWithValue("@LOGINID", Obj_license_information.obj_LOGINID);
                            cmdInvitation.Parameters.AddWithValue("@PASSWORD", Obj_license_information.obj_PASSWORD);
                            cmdInvitation.Parameters.AddWithValue("@LICENSESTATUS", Obj_license_information.obj_LICENSESTATUS);
                            cmdInvitation.Parameters.AddWithValue("@EXPIREDATE", Obj_license_information.obj_EXPIREDATE);
                            cmdInvitation.Parameters.AddWithValue("@LICCREATEDDATE", Obj_license_information.obj_LICCREATEDDATE);
                            cmdInvitation.Parameters.AddWithValue("@LICLASTMODIFIEDDATE", Obj_license_information.obj_LICLASTMODIFIEDDATE);
                            cmdInvitation.Parameters.AddWithValue("@Company_Name", Obj_license_information.obj_Company_Name);
                            cmdInvitation.Parameters.AddWithValue("@DeviceTypeId", Obj_license_information.obj_DeviceTypeId);
                            Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmdInvitation), Originator.Inquire, "licansevalueinsert", _tenantId));

                            int rowsAffected = cmdInvitation.ExecuteNonQuery();

                        }
                        tran.Commit();
                    }
                }
            }
            catch (Exception ex) { status = false; throw ex; }
            return status;
        }

        public void AddUserToGroup(int groupNum, int Ext)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    //cmd.CommandText=
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inquire, "AddUserToGroup", _tenantId));

                }
            }
            catch (Exception ex)
            { }
        }

        public static int SaveEventInvitation(EventInvitationGroup eventInvitationGroup, DateTime modifiedDate, int tenantId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inquire.INVITATION_GROUP_INSERT;
                    cmd.Parameters.AddWithValue("@Name", eventInvitationGroup.Name);
                    cmd.Parameters.AddWithValue("@UserId", eventInvitationGroup.UserId);
                    cmd.Parameters.AddWithValue("@StatusId", (int)eventInvitationGroup.Status);
                    cmd.Parameters.AddWithValue("@Comments", eventInvitationGroup.Comments);
                    cmd.Parameters.AddWithValue("@IsDeleted", 0);
                    cmd.Parameters.AddWithValue("@CreatedDate", eventInvitationGroup.DateCreated);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inquire, "SaveEventInvitation", tenantId));

                    int lastId = Convert.ToInt32(cmd.ExecuteScalar());
                    return lastId;
                }
            }
            catch (Exception ex) { throw ex; }

        }

        public static int SaveEventInvitation(EventInvitationGroup eventInvitationGroup, int tenantId)
        {
            int eventId = 0;
            int lastEventDetailId = 0;
            bool saved;
            try
            {
                using (var conn = DALHelper.GetConnection(tenantId))
                {
                    conn.Open();
                    using (var tran = conn.BeginTransaction("TransInq"))
                    {
                        //Step 1: 
                        using (var cmd = conn.CreateCommand())
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.CommandText = DBConstants.Inquire.INVITATION_GROUP_INSERT;
                            cmd.Transaction = tran as SqlTransaction;

                            cmd.Parameters.AddWithValue("@Name", eventInvitationGroup.Name);
                            cmd.Parameters.AddWithValue("@CreatedBy", eventInvitationGroup.UserId);
                            cmd.Parameters.AddWithValue("@SentToEmail", eventInvitationGroup.SentToEmail);
                            cmd.Parameters.AddWithValue("@RandomPassword", eventInvitationGroup.AccessCode);
                            cmd.Parameters.AddWithValue("@StatusId", (int)eventInvitationGroup.Status);
                            cmd.Parameters.AddWithValue("@Comments", eventInvitationGroup.Comments);
                            cmd.Parameters.AddWithValue("@IsDeleted", 0);
                            cmd.Parameters.AddWithValue("@CreatedDate", eventInvitationGroup.DateCreated);
                            cmd.Parameters.AddWithValue("@ExpiryDate", eventInvitationGroup.DateExpired);
                            Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inquire, "SaveEventInvitation", tenantId));
                            int eventGroupId = (int)cmd.ExecuteScalar();
                            eventInvitationGroup.Id = eventGroupId;
                        }

                        //Step 2
                        using (var cmd = conn.CreateCommand())
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.CommandText = DBConstants.Inquire.INVITATION_GROUP_INSERT_Details;
                            cmd.Transaction = tran as SqlTransaction;
                            foreach (var inqEvent in eventInvitationGroup.EventDetails)
                            {
                                cmd.Parameters.AddWithValue("@EventId", eventInvitationGroup.Id);
                                cmd.Parameters.AddWithValue("@CallId", inqEvent.CallId);
                                cmd.Parameters.AddWithValue("@IsDeleted", 0);
                                cmd.Parameters.AddWithValue("@CreatedDate", eventInvitationGroup.DateCreated);
                                //cmd.Parameters.AddWithValue("@ModifiedDate", modifiedDate);
                                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inquire, "CheckAlreadyInvited", tenantId));

                                //int noOfRowsAffected = Convert.ToInt32(cmd.ExecuteNonQuery());
                                //return noOfRowsAffected;
                                lastEventDetailId = (int)cmd.ExecuteScalar();
                                cmd.Parameters.Clear();
                            }
                        }
                        saved = true;
                        tran.Commit();
                    }
                }
                //return lastUserId;
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return eventInvitationGroup.Id;
        }

        public static int UpdateEventInvitation(EventInvitationGroup eventInvitationGroup, DateTime modifiedDate, int tenantId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inquire.INVITATION_GROUP_UPDATE;
                    cmd.Parameters.AddWithValue("@Id", eventInvitationGroup.Id);
                    cmd.Parameters.AddWithValue("@StatusId", (int)eventInvitationGroup.Status);
                    cmd.Parameters.AddWithValue("@IsDeleted", 0);
                    cmd.Parameters.AddWithValue("@ModifiedDate", modifiedDate);
                    cmd.Parameters.AddWithValue("@AcceptedDate", eventInvitationGroup.DateAccepted);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inquire, "UpdateEventInvitation", tenantId));

                    return cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public static int ChangeEventInvitationStatus(int invitationId, InvitationStatus status, int tenantId, DateTime? acceptedDate)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    /*cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inquire.INVITATION_STATUS_UPDATE;
                    cmd.Parameters.AddWithValue("@Id", invitationId);
                    cmd.Parameters.AddWithValue("@StatusId", (int)status);
                    cmd.Parameters.AddWithValue("@AcceptedDate", acceptedDate);*/
                    cmd.CommandText = "UPDATE umShareEvent SET StatusId = @StatusId, AcceptedOn = @AcceptedDate Where Id = @Id";
                    cmd.CommandType = CommandType.Text;
                    cmd.Parameters.AddWithValue("@Id", invitationId);
                    cmd.Parameters.AddWithValue("@StatusId", (int)status);
                    cmd.Parameters.AddWithValue("@AcceptedDate", acceptedDate.HasValue ? acceptedDate : (object)DBNull.Value);
                    //cmd.Parameters.AddWithValue("@AcceptedDate", acceptedDate ?? DBNull.Value);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inquire, "ChangeEventInvitationStatus", tenantId));

                    return cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public static EventInvitationGroup GetEventInvitationById(int eventInvitationId, int tenantId, bool includeDetails = false)
        {
            EventInvitationGroup invitation = null;

            using (var conn = DALHelper.GetConnection(tenantId))
            using (var cmd = conn.CreateCommand())
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandText = DBConstants.Inquire.INVITATION_GROUP_GET_BY_ID;
                cmd.Parameters.AddWithValue("@Id", eventInvitationId);

                conn.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inquire, "GetEventInvitationById", tenantId));

                using (var dr = cmd.ExecuteReader())
                {
                    //if (dr.HasRows)
                    if (dr.Read())
                    {
                        invitation = new EventInvitationGroup();
                        invitation.Id = Convert.ToInt32(dr["Id"]);
                        invitation.Name = Convert.ToString(dr["Name"]);
                        invitation.Comments = Convert.ToString(dr["Comments"]);
                        invitation.DateCreated = Convert.ToDateTime(dr["CreatedDate"]);
                        invitation.DateModified = dr["LastModifiedDate"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["LastModifiedDate"]); //Convert.ToDateTime(dr["LastModifiedDate"]);
                        invitation.DateExpired = Convert.ToDateTime(dr["ExpiryDate"]);
                        invitation.DateAccepted = dr["AcceptedOn"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["AcceptedOn"]); //Convert.ToDateTime(dr["AcceptedOn"]);
                        //invitation.InviteCode = Convert.ToString(dr["InviteCode"]);
                        invitation.SentToEmail = Convert.ToString(dr["SentToEmail"]);
                        invitation.AccessCode = Convert.ToString(dr["RandomPassword"]);
                        invitation.UserId = Convert.ToInt32(dr["CreatedBy"]);
                        invitation.Status = (InvitationStatus)Enum.Parse(typeof(InvitationStatus), Convert.ToString(dr["StatusId"]));
                    }
                }
            }
            return invitation;
        }

        public PictureEvent GetPictureEventsByCallId(string callId)
        {
            PictureEvent objPictureEvent = null;
            List<PictureEventDetail> objPictureEventDetails = null;

            using (var conn = DALHelper.GetConnection(_tenantId))
            using (var cmd = conn.CreateCommand())
            {
                cmd.CommandType = CommandType.StoredProcedure;
                //cmd.CommandText = "SELECT * FROM mmEvent where (Id = @eventId) AND (IsDeleted = 0)";
                cmd.CommandText = DBConstants.Inquire.PICTURE_EVENT_GET_BY_CALL_ID;
                cmd.Parameters.AddWithValue("@callId", callId);

                conn.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inquire, "GetPictureEventsByCallId", _tenantId));

                using (var dr = cmd.ExecuteReader())
                {
                    //if (dr.Read())
                    //pe = new PictureEvent();
                    // 1.PictureEvent ResultSet

                    if (dr.HasRows)
                    {
                        objPictureEventDetails = new List<PictureEventDetail>();
                        while (dr.Read())
                        {
                            var ped = new PictureEventDetail();
                            ped.Id = Convert.ToInt32(dr["Id"]);
                            ped.CallId = Convert.ToString(dr["CallId"]);
                            ped.FileName = Convert.ToString(dr["FileName"]);
                            ped.FileType = FileType.Image;
                            ped.FileDuration = Convert.ToInt32(dr["Duration"]);
                            ped.FileNotes = Convert.ToString(dr["FileDescription"]);
                            ped.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                            //ped.StartTimeString 
                            //ped.ModifiedDate

                            objPictureEventDetails.Add(ped);
                        }
                    }

                    dr.NextResult();

                    if (dr.Read())
                    {
                        objPictureEvent = new PictureEvent();
                        objPictureEvent.PictureEventDetails = objPictureEventDetails;
                        objPictureEvent.CallId = Convert.ToString(dr["CallId"]);
                        objPictureEvent.EventName = Convert.ToString(dr["EventName"]);
                        objPictureEvent.Notes = Convert.ToString(dr["Notes"]);
                        objPictureEvent.FileName = Convert.ToString(dr["FileName"]);
                        objPictureEvent.StartTime = Convert.ToString(dr["StartTime"]);
                    }

                }
            }
            return objPictureEvent;
        }

    }
}
