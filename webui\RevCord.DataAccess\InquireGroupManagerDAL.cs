﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RevCord.DataContracts.ViewModelEntities;
using System.Data.SqlClient;
using System.Data;
using RevCord.DataContracts.UserManagement;
using RevCord.DataAccess.Util;
using RevCord.Util;
using RevCord.DataContracts;

namespace RevCord.DataAccess
{
    public class InquireGroupManagerDAL
    {
        private int _tenantId;
        public InquireGroupManagerDAL(int tenantId)
        {
            this._tenantId = tenantId;
        }

        #region Fields

        public static Dictionary<int, int> dUserInfo = new Dictionary<int, int>();
        public static Dictionary<string, int> dUsersInfo = new Dictionary<string, int>();
        #endregion

        public List<UserGroup> GetGroups()
        {
            List<UserGroup> lGroups = null;
            UserGroup groups = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.GROUP_GETLIST;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InquireGroup, "GetGroups", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            lGroups = new List<UserGroup>();

                            while (dr.Read())
                            {
                                groups = new UserGroup();
                                groups.GroupName = Convert.ToString(dr["GroupName"]);

                                lGroups.Add(groups);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return lGroups;
        }
        public List<InquireUserInfo> GetInqGroupUsers(int groupNum)
        {
            List<InquireUserInfo> lUsers = null;
            InquireUserInfo userinfoData = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.INQ_GetUSERS_BY_GROUPID;
                    cmd.Parameters.AddWithValue("@GROUPNUM", groupNum);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InquireGroup, "GetInqGroupUsers", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            lUsers = new List<InquireUserInfo>();

                            while (dr.Read())
                            {
                                userinfoData = new InquireUserInfo();
                                userinfoData.UserName = Convert.ToString(dr["UserName"]);
                                userinfoData.Ext = Convert.ToInt32(dr["Ext"]);
                                userinfoData.UserNum = Convert.ToInt32(dr["UserNum"]);
                                userinfoData.UserType = Convert.ToInt32(dr["UserType"]);
                                lUsers.Add(userinfoData);
                                if (!dUsersInfo.ContainsValue(userinfoData.UserNum) && !dUsersInfo.ContainsKey(userinfoData.UserName))
                                    dUsersInfo.Add(userinfoData.UserName, userinfoData.UserNum);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {

            }
            return lUsers;
        }
        public List<GlobalGroup> InqCreateGroup(GlobalGroup globalGroup, int uId = 1000)
        {
            List<GlobalGroup> gGroups = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.GROUP_INSERT_N_GET;
                    cmd.Parameters.AddWithValue("@ParentId", globalGroup.ParentId);
                    cmd.Parameters.AddWithValue("@GroupName", globalGroup.Name);
                    cmd.Parameters.AddWithValue("@Description", globalGroup.Description);
                    cmd.Parameters.AddWithValue("@UserId", uId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InquireGroup, "InqCreateGroup", _tenantId));
                    cmd.ExecuteNonQuery();

                }
            }
            catch (Exception ex) { throw ex; }
            return gGroups;
        }
        public List<GlobalGroup> GetGroupDepth(GlobalGroup globalGroup, int uId = 1000)
        {
            List<GlobalGroup> lGroups = null;
            GlobalGroup groups = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.INQ_GET_GROUP_DEPTH;
                    cmd.Parameters.AddWithValue("@ParentId", globalGroup.ParentId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InquireGroup, "GetGroupDepth", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            lGroups = new List<GlobalGroup>();

                            while (dr.Read())
                            {
                                groups = new GlobalGroup();
                                groups.Depth = Convert.ToInt32(dr["Depth"]);
                                lGroups.Add(groups);

                            }
                        }
                    }

                }
            }
            catch (Exception ex) { throw ex; }
            return lGroups;
        }
        public List<GlobalGroup> InqDeleteGroup(int id, int userId = 1000)
        {
            List<GlobalGroup> gGroups = null;
            try
            {
                //using (var conn = GetConnection())
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.INQ_DEL_GROUPS;
                    cmd.Parameters.AddWithValue("@GroupNum ", id);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InquireGroup, "InqDeleteGroup", _tenantId));
                    cmd.ExecuteNonQuery();

                }
            }
            catch (Exception ex) { throw ex; }
            return gGroups;
        }
        public bool InqSaveGroupUser(UserGroup uGroup)
        {
            try
            {
                //using (var conn = GetConnection())
                foreach (var user in dUsersInfo)
                {
                    if (user.Key == uGroup.UserName)
                    {
                        uGroup.UserNum = user.Value;
                        break;
                    }
                }
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;

                    cmd.CommandText = DBConstants.UserManagement.INQ_ADD_USERS_TO_GROUP;
                    cmd.Parameters.AddWithValue("@GroupNum", uGroup.GroupNum);
                    cmd.Parameters.AddWithValue("@UserNum", uGroup.UserNum);
                    cmd.Parameters.AddWithValue("@AssignAuth", uGroup.AssignAuth);
                    cmd.Parameters.AddWithValue("@Descr", uGroup.Descr == null ? Convert.DBNull : uGroup.Descr);
                    cmd.Parameters.AddWithValue("@Ext", uGroup.ExtId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InquireGroup, "InqSaveGroupUser", _tenantId));
                    cmd.ExecuteNonQuery();
                    return true;
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public bool InqDeleteGroupUser(UserGroup uGroup)
        {
            try
            {
                //foreach (var user in dUsersInfo)
                //{
                //    if (user.Key == uGroup.UserName)
                //    {
                //        uGroup.UserNum = user.Value;
                //    }
                //}
                //using (var conn = GetConnection())
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.INQ_DEL_USERS_FROM_GROUP;
                    cmd.Parameters.AddWithValue("@GroupNum", uGroup.GroupNum);
                    cmd.Parameters.AddWithValue("@UserNum", uGroup.UserNum);
                    cmd.Parameters.AddWithValue("@Ext", uGroup.ExtId.ToString());
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InquireGroup, "InqDeleteGroupUser", _tenantId));
                    cmd.ExecuteNonQuery();
                    return true;
                }
            }
            catch (Exception ex) { throw ex; }
        }
    }
}