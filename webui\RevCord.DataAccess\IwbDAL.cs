﻿using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.Criteria;
using RevCord.DataContracts.IWBEntities;
using RevCord.DataContracts.Messages;
using RevCord.DataContracts.UserManagement;
using RevCord.DataContracts.Utilities;
using RevCord.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Net.Http;
using System.Net.Http.Headers;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Xml;
using System.Configuration;
using System.Net;
using System.IO;




namespace RevCord.DataAccess
{
    public class IwbDAL
    {
        private int _tenantId;
        private static int CMD_TIMEOUT = AppSettingsUtil.GetInt("commandTimeout", 60);

        public IwbDAL(int tenantId)
        {
            this._tenantId = tenantId;
        }

        #region WPS

        public int InsertWPS(Wps wps)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_WPS_INSERT;

                    //cmd.Parameters.AddWithValue("@TenantId", _tenantId);
                    //cmd.Parameters.AddWithValue("@fileName", wps.FileName);
                    //cmd.Parameters.AddWithValue("@weldingStandard", wps.WeldingStandard);
                    cmd.Parameters.AddWithValue("@recordNo", wps.RecordNo);
                    cmd.Parameters.AddWithValue("@dateQualified", wps.DateQualified);
                    cmd.Parameters.AddWithValue("@companyName", wps.CompanyName);
                    cmd.Parameters.AddWithValue("@Comments", wps.Comments);
                    cmd.Parameters.AddWithValue("@CreatedBy", wps.CreatedBy);
                    cmd.Parameters.AddWithValue("@CreatedDate", wps.CreatedDate);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "InsertWPS", _tenantId));

                    //int lastId = (int)cmd.ExecuteScalar();
                    int lastId = Convert.ToInt32(cmd.ExecuteScalar());
                    return lastId;
                }
            }
            catch (Exception ex) { throw ex; }
        }



        public List<Wps> GetWpsList()
        {
            List<Wps> lstWps = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    //cmd.CommandText = "SELECT * FROM iwbWPS WHERE (IsDeleted = 0)";
                    cmd.CommandText = "SELECT * FROM iwbWPS";
                    //cmd.Parameters.AddWithValue("@OrganizationId", orgId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "GetWpsList", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        lstWps = new List<Wps>();
                        while (dr.Read())
                        {
                            var wps = new Wps();
                            wps.Id = (int)dr["Id"];
                            wps.RecordNo = Convert.ToString(dr["RecordNumber"]);
                            wps.DateQualified = Convert.ToDateTime(dr["DateQualified"]);
                            wps.CompanyName = Convert.ToString(dr["CompanyName"]);
                            wps.ReferenceDocs = Convert.ToString(dr["ReferenceDocs"]);
                            wps.Scope = Convert.ToString(dr["Scope"]);

                            lstWps.Add(wps);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return lstWps;
        }

        public Wps GetWpsById(int wpsId)
        {
            Wps wps = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = "SELECT * FROM iwbWPS WHERE (Id = @wpsId) AND (IsDeleted = 0)";
                    cmd.Parameters.AddWithValue("@wpsId", wpsId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "GetWpsById", _tenantId));

                    using (var dr = cmd.ExecuteReader())
                    {
                        if (dr.Read())
                        {
                            wps = new Wps();
                            wps.Id = (int)dr["Id"];
                            wps.ReferenceDocs = Convert.ToString(dr["ReferenceDocs"]);
                            wps.Scope = Convert.ToString(dr["Scope"]);
                            wps.RecordNo = Convert.ToString(dr["RecordNo"]);
                            wps.DateQualified = Convert.ToDateTime(dr["DateQualified"]);
                            wps.CompanyName = Convert.ToString(dr["CompanyName"]);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return wps;
        }




        #endregion

        #region Document

        public int InsertScannedDocument(IwbDocument document)
        {
            try
            {
                int rowAffected = 0;
                using (var conn = DALHelper.GetConnection(_tenantId))
                {
                    conn.Open();
                    using (var tran = conn.BeginTransaction())
                    {
                        try
                        {
                            using (var cmd = conn.CreateCommand())
                            {
                                //  1. Document

                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.CommandText = DBConstants.Iwb.IWB_DOCUMENT_INSERT;

                                cmd.Transaction = tran as SqlTransaction;
                                cmd.CommandTimeout = CMD_TIMEOUT;

                                cmd.Parameters.AddWithValue("@documentTypeId", (int)document.Type);
                                cmd.Parameters.AddWithValue("@alias", document.Alias);
                                cmd.Parameters.AddWithValue("@name", document.Name);
                                cmd.Parameters.AddWithValue("@fileName", document.FileName);
                                cmd.Parameters.AddWithValue("@filePath", document.FilePath);
                                cmd.Parameters.AddWithValue("@fileExtension", document.FileExtension);
                                cmd.Parameters.AddWithValue("@fileSize", document.FileSize);
                                cmd.Parameters.AddWithValue("@uploadDate", document.UploadDate);
                                cmd.Parameters.AddWithValue("@description", document.Description);
                                cmd.Parameters.AddWithValue("@userId", document.UserId);
                                cmd.Parameters.AddWithValue("@organizationId", document.OrganizationId);
                                cmd.Parameters.AddWithValue("@createdBy", document.CreatedBy);
                                cmd.Parameters.AddWithValue("@createdDate", document.CreatedDate);


                                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "InsertScannedDocument", _tenantId));

                                int lastSavedId = Convert.ToInt32(cmd.ExecuteScalar());
                                document.Id = lastSavedId;

                                rowAffected++;
                                cmd.Parameters.Clear();

                                //  2. Document Sections

                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.CommandText = DBConstants.Iwb.IWB_DOCUMENT_SECTION_INSERT;

                                foreach (var section in document.Sections)
                                {
                                    cmd.Parameters.AddWithValue("@documentId", document.Id);
                                    cmd.Parameters.AddWithValue("@name", section.Name);
                                    cmd.Parameters.AddWithValue("@description", section.Description);

                                    int lastSavedSectionId = Convert.ToInt32(cmd.ExecuteScalar());
                                    section.Id = lastSavedSectionId;

                                    rowAffected++;

                                    foreach (var field in section.Fields)
                                    {
                                        cmd.Parameters.Clear();
                                        //  3. Document Sections Fields
                                        cmd.CommandType = CommandType.StoredProcedure;
                                        cmd.CommandText = DBConstants.Iwb.IWB_DOCUMENT_SECTION_FIELD_INSERT;
                                        cmd.Transaction = tran as SqlTransaction;

                                        cmd.Parameters.AddWithValue("@documentSectionId", section.Id);
                                        cmd.Parameters.AddWithValue("@name", field.Name);
                                        cmd.Parameters.AddWithValue("@reportValue", field.ReportValue);
                                        cmd.Parameters.AddWithValue("@standardValue", field.StandardValue);
                                        cmd.Parameters.AddWithValue("@complianceStatus", field.ComplianceStatus);
                                        cmd.Parameters.AddWithValue("@description", field.Description);

                                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "InsertScannedDocument", _tenantId));

                                        lastSavedId = Convert.ToInt32(cmd.ExecuteScalar());
                                        rowAffected++;
                                    }
                                }
                            }
                            tran.Commit();
                        }
                        catch
                        {
                            tran.Rollback();
                            throw;
                        }
                    }
                    return rowAffected;
                }
            }
            catch (Exception ex) { throw ex; }

        }

        public int InsertScannedDocumentMTR(IwbDocument document)
        {
            try
            {
                int rowAffected = 0;
                var documentId = document.Id;
                using (var conn = DALHelper.GetConnection(_tenantId))
                {
                    conn.Open();
                    using (var tran = conn.BeginTransaction())
                    {
                        try
                        {
                            using (var cmd = conn.CreateCommand())
                            {
                                //  1. Document

                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.CommandText = DBConstants.Iwb.IWB_DOCUMENT_INSERT;

                                cmd.Transaction = tran as SqlTransaction;
                                cmd.CommandTimeout = CMD_TIMEOUT;

                                cmd.Parameters.AddWithValue("@documentId", documentId);
                                cmd.Parameters.AddWithValue("@documentTypeId", (int)document.Type);
                                cmd.Parameters.AddWithValue("@alias", document.Alias);
                                cmd.Parameters.AddWithValue("@name", document.Name);
                                cmd.Parameters.AddWithValue("@fileName", document.FileName);
                                cmd.Parameters.AddWithValue("@filePath", document.FilePath);
                                cmd.Parameters.AddWithValue("@fileExtension", document.FileExtension);
                                cmd.Parameters.AddWithValue("@fileSize", document.FileSize);
                                cmd.Parameters.AddWithValue("@uploadDate", document.UploadDate);
                                cmd.Parameters.AddWithValue("@description", document.Description);
                                cmd.Parameters.AddWithValue("@userId", document.UserId);
                                cmd.Parameters.AddWithValue("@organizationId", document.OrganizationId);
                                cmd.Parameters.AddWithValue("@createdBy", document.CreatedBy);
                                cmd.Parameters.AddWithValue("@createdDate", document.CreatedDate);
                                cmd.Parameters.AddWithValue("@JsonFileName", document.JsonFileName);
                                cmd.Parameters.AddWithValue("@OutputFileName", document.OutputFileName);
                                cmd.Parameters.AddWithValue("@OCRResult", document.OCRResult);
                                cmd.Parameters.AddWithValue("@IsSaved", document.IsSaved);

                                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "InsertScannedDocumentMTR", _tenantId));

                                int lastSavedId = Convert.ToInt32(cmd.ExecuteScalar());
                                document.Id = lastSavedId;

                                rowAffected++;
                                cmd.Parameters.Clear();

                                //  2. MTR Document
                                if (documentId == 0)
                                {
                                    cmd.CommandType = CommandType.StoredProcedure;
                                    cmd.CommandText = DBConstants.Iwb.IWB_MTR_DOCUMENT_INSERT;

                                    cmd.Parameters.AddWithValue("@documentId", document.Id);
                                    cmd.Parameters.AddWithValue("@companyName", document.MTR.CompanyName);
                                    cmd.Parameters.AddWithValue("@certificateName", document.MTR.CertificateName);
                                    cmd.Parameters.AddWithValue("@orderNo", document.MTR.OrderNo);
                                    cmd.Parameters.AddWithValue("@heatPieceNo", document.MTR.HeatPieceNo);
                                    cmd.Parameters.AddWithValue("@date", document.MTR.Date == DateTime.MinValue ? document.MTR.Date : (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@addressSoldTo", document.MTR.AddressSoldTo);
                                    cmd.Parameters.AddWithValue("@addressShipTo", document.MTR.AddressShipTo);
                                    cmd.Parameters.AddWithValue("@complianceStatus", !string.IsNullOrEmpty(document.MTR.ComplianceStatus) ? document.MTR.ComplianceStatus : (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@remarks", document.MTR.Remarks);
                                    cmd.Parameters.AddWithValue("@asmeCode", document.MTR.ASME);
                                    cmd.Parameters.AddWithValue("@astmCode", document.MTR.ASTM);
                                    cmd.Parameters.AddWithValue("@uns", document.MTR.Uns);
                                    cmd.Parameters.AddWithValue("@grade", document.MTR.Grade);

                                    lastSavedId = Convert.ToInt32(cmd.ExecuteScalar());
                                    document.MTR.Id = lastSavedId;
                                }


                                //  3. MTR Document Sections
                                foreach (var section in document.MTR.MtrSections)
                                {
                                    cmd.Parameters.Clear();
                                    cmd.CommandType = CommandType.StoredProcedure;
                                    cmd.CommandText = DBConstants.Iwb.IWB_MTR_DOCUMENT_SECTION_INSERT;
                                    cmd.Transaction = tran as SqlTransaction;

                                    cmd.Parameters.AddWithValue("@mtrId", document.MTR.Id);
                                    cmd.Parameters.AddWithValue("@sectionName", section.Name);

                                    int lastSavedSectionId = Convert.ToInt32(cmd.ExecuteScalar());
                                    section.Id = lastSavedSectionId;

                                    rowAffected++;

                                    //  3. MTR Document Section Fields
                                    foreach (var field in section.Elements)
                                    {
                                        cmd.Parameters.Clear();
                                        //  3. Document Sections Fields
                                        cmd.CommandType = CommandType.StoredProcedure;
                                        cmd.CommandText = DBConstants.Iwb.IWB_MTR_DOCUMENT_SECTION_FIELD_INSERT;
                                        cmd.Transaction = tran as SqlTransaction;

                                        cmd.Parameters.AddWithValue("@mtrId", document.MTR.Id);
                                        cmd.Parameters.AddWithValue("@mtrSectionId", section.Id);
                                        cmd.Parameters.AddWithValue("@kDP", field.Name);
                                        cmd.Parameters.AddWithValue("@symbol", field.Symbol);
                                        cmd.Parameters.AddWithValue("@InputValue", field.Input);
                                        cmd.Parameters.AddWithValue("@standardValue", field.Standard);
                                        cmd.Parameters.AddWithValue("@complianceStatus", field.Status);
                                        cmd.Parameters.AddWithValue("@reason", field.Reason);

                                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "InsertScannedDocumentMTR", _tenantId));

                                        lastSavedId = Convert.ToInt32(cmd.ExecuteScalar());
                                        rowAffected++;
                                    }
                                }
                            }
                            tran.Commit();
                        }
                        catch
                        {
                            tran.Rollback();
                            throw;
                        }
                    }
                    //return rowAffected;
                    return document.Id;
                }
            }
            catch (Exception ex) { throw ex; }

        }

        public int InsertScannedDocumentWPS(IwbDocument document)
        {
            try
            {
                int rowAffected = 0;
                using (var conn = DALHelper.GetConnection(_tenantId))
                {
                    conn.Open();
                    using (var tran = conn.BeginTransaction())
                    {
                        try
                        {
                            using (var cmd = conn.CreateCommand())
                            {
                                //  1. Document

                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.CommandText = DBConstants.Iwb.IWB_DOCUMENT_INSERT;

                                cmd.Transaction = tran as SqlTransaction;
                                cmd.CommandTimeout = CMD_TIMEOUT;

                                cmd.Parameters.AddWithValue("@documentTypeId", (int)document.Type);
                                cmd.Parameters.AddWithValue("@alias", document.Alias);
                                cmd.Parameters.AddWithValue("@name", document.Name);
                                cmd.Parameters.AddWithValue("@fileName", document.FileName);
                                cmd.Parameters.AddWithValue("@filePath", document.FilePath);
                                cmd.Parameters.AddWithValue("@fileExtension", document.FileExtension);
                                cmd.Parameters.AddWithValue("@fileSize", document.FileSize);
                                cmd.Parameters.AddWithValue("@uploadDate", document.UploadDate);
                                cmd.Parameters.AddWithValue("@description", document.Description);
                                cmd.Parameters.AddWithValue("@userId", document.UserId);
                                cmd.Parameters.AddWithValue("@organizationId", document.OrganizationId);
                                cmd.Parameters.AddWithValue("@createdBy", document.CreatedBy);
                                cmd.Parameters.AddWithValue("@createdDate", document.CreatedDate);
                                cmd.Parameters.AddWithValue("@OutputFileName", document.OutputFileName);


                                //Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "InsertScannedDocumentWPS", _tenantId));

                                int lastSavedId = Convert.ToInt32(cmd.ExecuteScalar());
                                document.Id = lastSavedId;

                                rowAffected++;
                                cmd.Parameters.Clear();

                                //  2. WPS Document

                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.CommandText = DBConstants.Iwb.IWB_WPS_DOCUMENT_INSERT;
                                cmd.Transaction = tran as SqlTransaction;

                                cmd.Parameters.AddWithValue("@recordNo", document.WPS.RecordNumber);
                                cmd.Parameters.AddWithValue("@dateQualified", document.WPS.DateQualified);
                                cmd.Parameters.AddWithValue("@companyName", document.WPS.CompanyName);
                                //cmd.Parameters.AddWithValue("@comments", document.WPS.Comments);
                                cmd.Parameters.AddWithValue("@supportingPQRs", document.WPS.SupportingPQRs);
                                cmd.Parameters.AddWithValue("@referenceDocs", document.WPS.ReferenceDocs);
                                var scope = string.Join(",", document.WPS.Scope.Select(x => x.ToString()));
                                cmd.Parameters.AddWithValue("@scope", scope);
                                cmd.Parameters.AddWithValue("@joint", document.WPS.Joint);


                                lastSavedId = Convert.ToInt32(cmd.ExecuteScalar());
                                document.WPS.Id = lastSavedId;

                                //  3. WPS Details
                                cmd.Parameters.Clear();
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.CommandText = DBConstants.Iwb.IWB_WPS_DOCUMENT_DETAIL_INSERT;
                                cmd.Transaction = tran as SqlTransaction;

                                cmd.Parameters.AddWithValue("@WpsId", document.WPS.Id);
                                cmd.Parameters.AddWithValue("@BaseMetalTypeMaterial", document.WPS.BaseMetal.Type.Material);
                                cmd.Parameters.AddWithValue("@BaseMetalTypePNo", string.Join(",", document.WPS.BaseMetal.Type.PNo.ToArray<string>()));
                                cmd.Parameters.AddWithValue("@BaseMetalTypeGroupNo", string.Join(",", document.WPS.BaseMetal.Type.GroupNo.ToArray<string>()));
                                cmd.Parameters.AddWithValue("@BaseMetalWeldedToMaterial", document.WPS.BaseMetal.WeldedTo.Material);
                                cmd.Parameters.AddWithValue("@BaseMetalWeldedToPNo", string.Join(",", document.WPS.BaseMetal.WeldedTo.PNo.ToArray<string>()));
                                cmd.Parameters.AddWithValue("@BaseMetalWeldedToGroupNo", string.Join(",", document.WPS.BaseMetal.WeldedTo.GroupNo.ToArray<string>()));
                                cmd.Parameters.AddWithValue("@BaseMetalBackingMaterial", document.WPS.BaseMetal.Backing.Material);
                                cmd.Parameters.AddWithValue("@BaseMetalBackingPNo", string.Join(",", document.WPS.BaseMetal.Backing.PNo.ToArray<string>()));
                                cmd.Parameters.AddWithValue("@BaseMetalBackingGroupNo", string.Join(",", document.WPS.BaseMetal.Backing.GroupNo.ToArray<string>()));
                                cmd.Parameters.AddWithValue("@BaseMetalRetainers", document.WPS.BaseMetal.Retainers);
                                cmd.Parameters.AddWithValue("@ThickNessRangeCompletePenAsWeldMin", document.WPS.ThickNessRangeQualified.CompletePen.AsWelded.Min);
                                cmd.Parameters.AddWithValue("@ThickNessRangeCompletePenAsWeldMax", document.WPS.ThickNessRangeQualified.CompletePen.AsWelded.Max);
                                cmd.Parameters.AddWithValue("@ThickNessRangeCompletePenWithPWHTMin", document.WPS.ThickNessRangeQualified.CompletePen.WithPWHT.Min);
                                cmd.Parameters.AddWithValue("@ThickNessRangeCompletePenWithPWHTMax", document.WPS.ThickNessRangeQualified.CompletePen.WithPWHT.Max);
                                cmd.Parameters.AddWithValue("@ThickNessRangeImpactTestAsWeldMin", document.WPS.ThickNessRangeQualified.ImpactTested.AsWelded.Min);
                                cmd.Parameters.AddWithValue("@ThickNessRangeImpactTestAsWeldMax", document.WPS.ThickNessRangeQualified.ImpactTested.AsWelded.Max);
                                cmd.Parameters.AddWithValue("@ThickNessRangeImpactTestWithPWHTMin", document.WPS.ThickNessRangeQualified.ImpactTested.WithPWHT.Min);
                                cmd.Parameters.AddWithValue("@ThickNessRangeImpactTestWithPWHTMax", document.WPS.ThickNessRangeQualified.ImpactTested.WithPWHT.Max);
                                cmd.Parameters.AddWithValue("@ThickNessRangePartialPenAsWeldMin", document.WPS.ThickNessRangeQualified.PartialPen.AsWelded.Min);
                                cmd.Parameters.AddWithValue("@ThickNessRangePartialPenAsWeldMax", document.WPS.ThickNessRangeQualified.PartialPen.AsWelded.Max);
                                cmd.Parameters.AddWithValue("@ThickNessRangeFilletWeldAsWeldMin", document.WPS.ThickNessRangeQualified.FilletWelds.AsWelded.Min);
                                cmd.Parameters.AddWithValue("@ThickNessRangeFilletWeldAsWeldMax", document.WPS.ThickNessRangeQualified.FilletWelds.AsWelded.Max);

                                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "InsertScannedDocumentWPS", _tenantId));

                                int lastSavedSectionId = Convert.ToInt32(cmd.ExecuteScalar());
                                //section.Id = lastSavedSectionId;

                                rowAffected++;
                            }
                            tran.Commit();
                        }
                        catch
                        {
                            tran.Rollback();
                            throw;
                        }
                    }
                    //return rowAffected;
                    return document.Id;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public int InsertDocument(IwbDocument document)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_DOCUMENT_INSERT;

                    //cmd.Parameters.AddWithValue("@TenantId", _tenantId);
                    cmd.Parameters.AddWithValue("@documentTypeId", (int)document.Type);
                    cmd.Parameters.AddWithValue("@alias", document.Alias);
                    cmd.Parameters.AddWithValue("@name", document.Name);
                    cmd.Parameters.AddWithValue("@fileName", document.FileName);
                    cmd.Parameters.AddWithValue("@filePath", document.FilePath);
                    cmd.Parameters.AddWithValue("@fileExtension", document.FileExtension);
                    cmd.Parameters.AddWithValue("@fileSize", document.FileSize);
                    cmd.Parameters.AddWithValue("@uploadDate", document.UploadDate);
                    cmd.Parameters.AddWithValue("@description", document.Description);
                    cmd.Parameters.AddWithValue("@userId", document.UserId);
                    cmd.Parameters.AddWithValue("@organizationId", document.OrganizationId);
                    cmd.Parameters.AddWithValue("@createdBy", document.CreatedBy);
                    cmd.Parameters.AddWithValue("@createdDate", document.CreatedDate);
                    cmd.Parameters.AddWithValue("@JsonFileName", document.JsonFileName);
                    cmd.Parameters.AddWithValue("@OutputFileName", document.OutputFileName);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "InsertDocument", _tenantId));

                    int lastId = Convert.ToInt32(cmd.ExecuteScalar());
                    return lastId;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public IwbDocument GetDocumentById(int documentId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_GET_DOCUMENT_BY_ID;

                    cmd.Parameters.AddWithValue("@documentId", documentId);

                    conn.Open();
                    using (var reader = cmd.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new IwbDocument
                            {
                                Id = Convert.ToInt32(reader["Id"]),
                                Type = (IwbDocumentType)Convert.ToInt32(reader["DocumentTypeId"]),
                                Alias = reader["Alias"]?.ToString(),
                                Name = reader["Name"]?.ToString(),
                                FileName = reader["FileName"]?.ToString(),
                                FilePath = reader["FilePath"]?.ToString(),
                                FileExtension = reader["FileExtension"]?.ToString(),
                                FileSize = Convert.ToInt64(reader["FileSize"]),
                                UploadDate = Convert.ToDateTime(reader["UploadDate"]),
                                Description = reader["Description"]?.ToString(),
                                UserId = Convert.ToInt32(reader["UserId"]),
                                OrganizationId = Convert.ToInt32(reader["OrganizationId"]),
                                CreatedBy = Convert.ToInt32(reader["CreatedBy"]),
                                CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                                JsonFileName = reader["JsonFileName"]?.ToString(),
                                OutputFileName = reader["OutputFileName"]?.ToString()
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }

            return null;
        }


        public void VerifyDocument(IwbDocument document)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_DOCUMENTS_VERIFY;

                    cmd.Parameters.AddWithValue("@DocumentId", document.Id);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "VerifyDocument", _tenantId));

                    cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public List<IwbDocument> GetDocumentsByWhereClause(string whereClause, out int totalPages, out long totalRecords, int pageIndex = 1, int pageSize = 10)
        {
            List<IwbDocument> documents = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_DOCUMENTS_GETBY_WHERECLAUSE;
                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);
                    cmd.Parameters.AddWithValue("@WhereClause", whereClause);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;


                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "GetDocumentsByWhereClause", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        documents = new List<IwbDocument>();
                        while (dr.Read())
                        {
                            var doc = new IwbDocument
                            {
                                Id = Convert.ToInt32(dr["Id"]),
                                Alias = Convert.ToString(dr["Alias"]),
                                Name = Convert.ToString(dr["Name"]),
                                Type = (IwbDocumentType)Enum.Parse(typeof(IwbDocumentType), Convert.ToString(dr["DocumentTypeId"])),
                                FileName = Convert.ToString(dr["FileName"]),
                                FilePath = Convert.ToString(dr["FilePath"]),
                                FileExtension = Convert.ToString(dr["FileExtension"]),
                                FileSize = Convert.ToInt64(dr["FileSize"]),
                                UploadDate = Convert.ToDateTime(dr["UploadDate"]),
                                Description = Convert.ToString(dr["Description"]),
                                UserId = Convert.ToInt32(dr["UserId"]),
                                OrganizationId = Convert.ToInt32(dr["OrganizationId"]),
                                OutputFileName = Convert.ToString(dr["OutputFileName"]),
                                UploadedBy = Convert.ToString(dr["UploadedBy"])
                            };
                            doc.MTR = new IwbMTR();
                            doc.MTR.ComplianceStatus = !DBRecordExtensions.HasColumn(dr, "ComplianceStatus") ? string.Empty : Convert.ToString(dr["ComplianceStatus"]);

                            doc.SignDocumentId = Convert.ToInt32(dr["SignDocumentId"]);
                            doc.OwnerEmail = Convert.ToString(dr["OwnerEmail"]);
                            documents.Add(doc);
                        }
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }

            return documents;
        }



        #endregion

        #region Job

        public int InsertJob(Job job)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_JOB_INSERT;

                    //cmd.Parameters.AddWithValue("@TenantId", _tenantId);
                    cmd.Parameters.AddWithValue("@CallId", job.CallId);
                    cmd.Parameters.AddWithValue("@InitiatorId", job.InitiatorId);
                    cmd.Parameters.AddWithValue("@PerformerId", job.PerformerId);
                    cmd.Parameters.AddWithValue("@WpsId", job.WpsId);
                    cmd.Parameters.AddWithValue("@Title", job.Title);
                    cmd.Parameters.AddWithValue("@StartDate", job.StartDate);
                    cmd.Parameters.AddWithValue("@EndDate", job.EndDate);
                    //cmd.Parameters.AddWithValue("@CompletionDate", job.CompletionDate);
                    cmd.Parameters.AddWithValue("@CurrentStatus", job.CurrentStatus);
                    cmd.Parameters.AddWithValue("@CreatedBy", job.CreatedBy);
                    cmd.Parameters.AddWithValue("@CreatedDate", job.CreatedDate);
                    cmd.Parameters.AddWithValue("@WPSData", job.WPSData);
                    cmd.Parameters.AddWithValue("@Location", job.Location);
                    cmd.Parameters.AddWithValue("@noOfPositions", job.NoOfPositions);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "InsertJob", _tenantId));

                    int lastId = Convert.ToInt32(cmd.ExecuteScalar());
                    return lastId;
                }
            }
            catch (Exception ex) { throw ex; }
        }


        public int AssignJob(int jobId, int welderId, DateTime createdDate, int createdBy = 1000, int jobApplicationStatus = 2)//, int jobStatusId = 1)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_JOB_Assign;

                    //cmd.Parameters.AddWithValue("@TenantId", _tenantId);
                    cmd.Parameters.AddWithValue("@jobId", jobId);
                    cmd.Parameters.AddWithValue("@userId", welderId);
                    cmd.Parameters.AddWithValue("@jobApplicationStatus", jobApplicationStatus);
                    cmd.Parameters.AddWithValue("@createdBy", createdBy);
                    cmd.Parameters.AddWithValue("@createdDate", createdDate);
                    //cmd.Parameters.AddWithValue("@jobStatusId", jobStatusId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "AssignJob", _tenantId));

                    int lastId = Convert.ToInt32(cmd.ExecuteScalar());
                    return lastId;
                }
            }
            catch (Exception ex) { throw ex; }
        }


        public List<Job> GetJobsByWhereClause(string whereClause, out int totalPages, out long totalRecords, int pageIndex = 1, int pageSize = 10, string jobWps = "")
        {
            List<Job> jobs = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_JOBS_GETBY_WHERECLAUSE;

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);
                    cmd.Parameters.AddWithValue("@WhereClause", whereClause);
                    cmd.Parameters.AddWithValue("@WpsRecordNumber", string.IsNullOrEmpty(jobWps) ? DBNull.Value : (object)jobWps.Trim());

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "GetJobsByWhereClause", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        jobs = new List<Job>();
                        while (dr.Read())
                        {
                            var job = new Job
                            {
                                Id = Convert.ToInt32(dr["Id"]),
                                CallId = Convert.ToString(dr["CallId"]),
                                InitiatorId = Convert.ToInt32(dr["InitiatorId"]),
                                PerformerId = Convert.ToInt32(dr["PerformerId"]),
                                Title = Convert.ToString(dr["Title"]),
                                StartDate = Convert.ToDateTime(dr["StartDate"]),
                                EndDate = Convert.ToDateTime(dr["EndDate"]),
                                CreatedDate = Convert.ToDateTime(dr["CreatedDate"]),
                                CurrentStatus = (IwbJobStatus)Enum.Parse(typeof(IwbJobStatus), Convert.ToString(dr["CurrentStatus"])),
                                WpsId = Convert.ToInt32(dr["Id"]),
                                WpsName = Convert.ToString(dr["WPSRecordNumber"]),
                                Location = dr["Location"] != DBNull.Value ? Convert.ToString(dr["Location"]) : "",
                                NoOfPositions = Convert.ToInt32(dr["NoOfApplicants"]),
                                NoOfApplicants = Convert.ToInt32(dr["NoOfApplicants"]),
                            };
                            jobs.Add(job);
                        }
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }

            return jobs;
        }

        public int UpdateJobStatus(int jobId, int jobStatusId)
        {
            int rowsAffected = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_JOB_UPDATE_STATUS;
                    cmd.Parameters.AddWithValue("@jobId", jobId);
                    cmd.Parameters.AddWithValue("@jobStatusId", jobStatusId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "UpdateJobStatus", _tenantId));

                    rowsAffected = cmd.ExecuteNonQuery();
                }
                return rowsAffected;
            }
            catch (Exception ex) { throw ex; }
        }

        public int UpdateJobApplicantStatus(JobApplicant applicantData)
        {
            int rowsAffected = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_JOB_UPDATE_APPLICANT_STATUS;
                    cmd.Parameters.AddWithValue("@JobId", applicantData.JobId);
                    cmd.Parameters.AddWithValue("@ApplicantId", applicantData.ApplicantId);
                    cmd.Parameters.AddWithValue("@StatusId", (int)applicantData.CurrentStatus);
                    cmd.Parameters.AddWithValue("@LastModifiedBy", applicantData.CreatedBy);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "UpdateJobApplicantStatus", _tenantId));

                    rowsAffected = cmd.ExecuteNonQuery();
                }
                return rowsAffected;
            }
            catch (Exception ex) { throw ex; }
        }

        public int ApplyForJob(JobApplicant jobApplicant)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_JOB_APPLY;

                    //cmd.Parameters.AddWithValue("@TenantId", _tenantId);
                    cmd.Parameters.AddWithValue("@jobId", jobApplicant.JobId);
                    cmd.Parameters.AddWithValue("@userId", jobApplicant.ApplicantId);
                    cmd.Parameters.AddWithValue("@statusId", jobApplicant.CurrentStatus);
                    cmd.Parameters.AddWithValue("@CreatedBy", jobApplicant.CreatedBy);
                    cmd.Parameters.AddWithValue("@CreatedDate", jobApplicant.CreatedDate);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "ApplyForJob", _tenantId));

                    int lastId = Convert.ToInt32(cmd.ExecuteScalar());
                    return lastId;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public List<JobApplicant> GetJobApplicants(int jobId)
        {
            List<JobApplicant> jobApplicants = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_JOB_APPLICANTS;
                    cmd.Parameters.AddWithValue("@jobId", jobId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "GetJobApplicants", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        jobApplicants = new List<JobApplicant>();
                        while (dr.Read())
                        {
                            var applicant = new JobApplicant();
                            applicant.Id = (int)dr["Id"];
                            applicant.JobId = Convert.ToInt32(dr["JobId"]);
                            applicant.JobName = Convert.ToString(dr["JobName"]);
                            applicant.ApplicantId = Convert.ToInt32(dr["UserId"]);
                            applicant.ApplicantName = Convert.ToString(dr["UserName"]);
                            applicant.ApplyDate = Convert.ToDateTime(dr["CreatedDate"]);
                            applicant.CurrentStatus = (IwbJobApplicationStatus)Enum.Parse(typeof(IwbJobApplicationStatus), Convert.ToString(dr["StatusId"]));

                            applicant.User = new User
                            {
                                UserNum = (int)dr["UserNum"],
                                UserType = (int)dr["UserType"],
                                UserName = Convert.ToString(dr["UserName"]),
                                UserID = Convert.ToString(dr["UserID"]),
                                UserEmail = Convert.ToString(dr["UserEmail"]),
                                Status = Convert.ToInt32(dr["Status"]),
                                SelectType = Convert.ToInt32(dr["SelectType"]),

                                CreatedBy = !DBRecordExtensions.HasColumn(dr, "CreatedBy") ? 0 : dr["CreatedBy"] == DBNull.Value ? 0 : Convert.ToInt32(dr["CreatedBy"]),
                                IsIwbUser = dr["IsIwbUser"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsIwbUser"]),
                                OrganizationId = dr["OrganizationId"] == DBNull.Value ? 0 : Convert.ToInt32(dr["OrganizationId"]),
                                DOB = dr["DOB"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["DOB"]),
                                City = Convert.ToString(dr["City"]),
                                State = Convert.ToString(dr["State"]),
                                SocialSecurityNumber = Convert.ToString(dr["SSN"]),
                                WelderStencilNumber = Convert.ToString(dr["StencilNumber"]),
                                CustomerIdZoho = !DBRecordExtensions.HasColumn(dr, "CustomerIdZoho") ? string.Empty : Convert.ToString(dr["CustomerIdZoho"]),
                                Descr = !DBRecordExtensions.HasColumn(dr, "OrganizationName") ? string.Empty : Convert.ToString(dr["OrganizationName"])
                            };

                            if (applicant.User.UserType == 1)
                                applicant.User.UMUserType = "Admin";
                            else if (applicant.User.UserType == 2)
                                applicant.User.UMUserType = "Manufacturers";
                            else if (applicant.User.UserType == 3)
                                applicant.User.UMUserType = "FabricationShops";
                            else if (applicant.User.UserType == 4)
                                applicant.User.UMUserType = "Operators";
                            else if (applicant.User.UserType == 5)
                                applicant.User.UMUserType = "Insurance";
                            else if (applicant.User.UserType == 6)
                                applicant.User.UMUserType = "Owner";
                            else if (applicant.User.UserType == 7)
                                applicant.User.UMUserType = "Contractor";
                            else if (applicant.User.UserType == 8)
                                applicant.User.UMUserType = "Welder";
                            else if (applicant.User.UserType == 9)
                                applicant.User.UMUserType = "Testing";
                            /*else if (applicant.User.UserType == 11)
                                applicant.User.UMUserType = "Insurance";*/
                            jobApplicants.Add(applicant);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return jobApplicants;
        }


        #endregion

        #region Welders

        public List<User> GetWelders(string whereClause)
        {
            List<User> users = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_WELDER_GETBY_WHERECLAUSE;
                    cmd.Parameters.AddWithValue("@WhereClause", whereClause);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "GetOrganizations", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        users = new List<User>();
                        while (dr.Read())
                        {
                            var user = new User();
                            user.UserNum = (int)dr["UserNum"];
                            user.UserType = (int)dr["UserType"];
                            user.UserID = Convert.ToString(dr["UserID"]);
                            user.UserName = Convert.ToString(dr["UserName"]);
                            user.UserEmail = Convert.ToString(dr["UserEmail"]);
                            user.Status = Convert.ToInt32(dr["Status"]);
                            user.Create_T = Convert.ToString(dr["Create_T"]).ConvertStringDateTimeToDateTime();
                            user.Modify_T = dr["Modify_T"] == DBNull.Value || Convert.ToString(dr["Modify_T"]).Trim() == String.Empty
                                ? default(DateTime) : Convert.ToString(dr["Modify_T"]).ConvertStringDateTimeToDateTime();
                            user.Delete_T = dr["Delete_T"] == DBNull.Value || Convert.ToString(dr["Delete_T"]).Trim() == ""
                                ? default(DateTime) : Convert.ToString(dr["Delete_T"]).ConvertStringDateTimeToDateTime();
                            user.UserPhone = Convert.ToString(dr["UserPhone"]);
                            user.UserFax = Convert.ToString(dr["UserFax"]);
                            user.IdentityNumber = Convert.ToString(dr["IdentityNumber"]);
                            user.SelectType = Convert.ToInt32(dr["SelectType"]);
                            user.UserPic = Convert.ToString(dr["UserPic"]);
                            user.IsGroupBased = Convert.ToBoolean(dr["IsGroupBased"]);
                            user.IsDeviceUser = Convert.ToInt32(dr["IsDeviceUser"]); //Arivu : For Inquire Custom Marker Filtering

                            user.CreatedBy = !DBRecordExtensions.HasColumn(dr, "CreatedBy") ? 0 : dr["CreatedBy"] == DBNull.Value ? 0 : Convert.ToInt32(dr["CreatedBy"]);
                            user.IsIwbUser = dr["IsIwbUser"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsIwbUser"]);
                            user.OrganizationId = dr["OrganizationId"] == DBNull.Value ? 0 : Convert.ToInt32(dr["OrganizationId"]);
                            user.DOB = dr["DOB"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["DOB"]);
                            user.City = Convert.ToString(dr["City"]);
                            user.State = Convert.ToString(dr["State"]);
                            user.SocialSecurityNumber = Convert.ToString(dr["SSN"]);
                            user.WelderStencilNumber = Convert.ToString(dr["StencilNumber"]);
                            user.CustomerIdZoho = !DBRecordExtensions.HasColumn(dr, "CustomerIdZoho") ? string.Empty : Convert.ToString(dr["CustomerIdZoho"]);
                            user.Descr = !DBRecordExtensions.HasColumn(dr, "OrganizationName") ? string.Empty : Convert.ToString(dr["OrganizationName"]);
                            user.IsLockedOut = !DBRecordExtensions.HasColumn(dr, "IsLockedOut") ? false : dr["IsLockedOut"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsLockedOut"]);
                            if (user.UserType == 1)
                                user.UMUserType = "Admin";
                            else if (user.UserType == 0 && user.Ext == "0")
                                user.UMUserType = "User";
                            else if (user.UserType == 0 && user.IsDeviceUser == 0)
                                user.UMUserType = "Channel";
                            else if (user.UserType == 0 && user.IsDeviceUser == 1)
                                user.UMUserType = "User";
                            else if (user.IsRevCell)
                                user.UMUserType = "User";
                            else if (user.UserType == 6)
                                user.UMUserType = "Owner";
                            else if (user.UserType == 7)
                                user.UMUserType = "Contractor";
                            else if (user.UserType == 8)
                                user.UMUserType = "Welder";

                            users.Add(user);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return users;
        }


        #endregion

        #region User Work History

        public IwbResponse GetWorkHistory(int userId)
        {
            List<UserWorkHistory> workHistories = null;
            User user = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_WORK_HISTORY_GETBYUSER;
                    cmd.Parameters.AddWithValue("@userId", userId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "GetWorkHistories", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        workHistories = new List<UserWorkHistory>();
                        while (dr.Read())
                        {
                            var workHistory = new UserWorkHistory();
                            workHistory.Id = (int)dr["Id"];
                            workHistory.UserId = Convert.ToInt32(dr["UserId"]);
                            //workHistory.OrganizationName = Convert.ToString(dr["OrganizationName"]);
                            workHistory.OrganizationName = dr["OrganizationName"] == DBNull.Value ? string.Empty : Convert.ToString(dr["OrganizationName"]);
                            workHistory.FromDate = Convert.ToDateTime(dr["StartDate"]);
                            workHistory.ToDate = Convert.ToDateTime(dr["EndDate"]);
                            //workHistory.Rating = Convert.ToInt32(dr["Rating"]);
                            workHistory.Rating = dr["Rating"] == DBNull.Value ? 0 : Convert.ToInt32(dr["Rating"]);
                            workHistory.FileName = !DBRecordExtensions.HasColumn(dr, "FileName") ? string.Empty : Convert.ToString(dr["FileName"]);


                            workHistories.Add(workHistory);
                        }

                        dr.NextResult();
                        if (dr.Read())
                        {
                            user = new User();

                            user.UserNum = (int)dr["UserNum"];
                            user.UserType = (int)dr["UserType"];
                            user.UserName = Convert.ToString(dr["UserName"]);
                            user.UserPic = Convert.ToString(dr["UserPic"]);
                            user.SelectType = Convert.ToInt32(dr["SelectType"]);
                            user.UserPhone = Convert.ToString(dr["UserPhone"]);
                            user.IsIwbUser = dr["IsIwbUser"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsIwbUser"]);
                            user.DOB = dr["DOB"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["DOB"]);
                            user.City = Convert.ToString(dr["City"]);
                            user.State = Convert.ToString(dr["State"]);
                            user.SocialSecurityNumber = Convert.ToString(dr["SSN"]);
                            user.WelderStencilNumber = Convert.ToString(dr["StencilNumber"]);
                            user.CustomerIdZoho = !DBRecordExtensions.HasColumn(dr, "CustomerIdZoho") ? string.Empty : Convert.ToString(dr["CustomerIdZoho"]);

                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return new IwbResponse { UserWorkHistories = workHistories, User = user, };
        }


        public int InsertWorkHistory(UserWorkHistory workHistory)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_WORK_HISTORY_INSERT;

                    //cmd.Parameters.AddWithValue("@TenantId", _tenantId);
                    cmd.Parameters.AddWithValue("@UserId", workHistory.OrganizationId);
                    cmd.Parameters.AddWithValue("@OrganizationName", workHistory.OrganizationId);
                    cmd.Parameters.AddWithValue("@StartDate", workHistory.FromDate);
                    cmd.Parameters.AddWithValue("@EndDate", workHistory.ToDate);
                    cmd.Parameters.AddWithValue("@Rating", workHistory.Rating);
                    cmd.Parameters.AddWithValue("@JobId", workHistory.JobId);
                    cmd.Parameters.AddWithValue("@CreatedBy", workHistory.CreatedBy);
                    cmd.Parameters.AddWithValue("@CreatedDate", workHistory.CreatedDate);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "InsertWorkHistory", _tenantId));

                    int lastId = Convert.ToInt32(cmd.ExecuteScalar());
                    return lastId;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        #endregion


        #region Organization

        public List<Organization> GetOrganizations(string whereClause)
        {
            List<Organization> organizations = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_ORGANIZATION_GETBY_WHERECLAUSE;
                    cmd.Parameters.AddWithValue("@WhereClause", whereClause);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "GetOrganizations", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        organizations = new List<Organization>();
                        while (dr.Read())
                        {
                            var org = new Organization();
                            org.Id = (int)dr["Id"];
                            org.Name = Convert.ToString(dr["Name"]);
                            org.Type = (OrganizationType)Enum.Parse(typeof(OrganizationType), Convert.ToString(dr["Type"]));
                            org.Email = Convert.ToString(dr["Email"]);
                            org.Address = Convert.ToString(dr["Address"]);
                            //org.State = Convert.ToString(dr["State"]);
                            //org.City = Convert.ToDateTime(dr["City"]);
                            org.Phone = Convert.ToString(dr["Phone"]);

                            organizations.Add(org);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return organizations;
        }

        public int InsertOrganization(Organization organization)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_ORGANIZATION_INSERT;

                    //cmd.Parameters.AddWithValue("@TenantId", _tenantId);
                    cmd.Parameters.AddWithValue("@Name", organization.Name);
                    cmd.Parameters.AddWithValue("@Type", organization.Type);
                    cmd.Parameters.AddWithValue("@Email", organization.Email);
                    cmd.Parameters.AddWithValue("@Address", organization.Address);
                    cmd.Parameters.AddWithValue("@City", "");
                    cmd.Parameters.AddWithValue("@State", "");
                    cmd.Parameters.AddWithValue("@Phone", organization.Phone);
                    cmd.Parameters.AddWithValue("@CreatedBy", organization.CreatedBy);
                    cmd.Parameters.AddWithValue("@CreatedDate", organization.CreatedDate);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "InsertOrganization", _tenantId));

                    int lastId = Convert.ToInt32(cmd.ExecuteScalar());
                    return lastId;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public int InsertOrganizationLocation(OrganizationLocation location)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_ORGANIZATION_LOCATION_INSERT;

                    //cmd.Parameters.AddWithValue("@TenantId", _tenantId);
                    cmd.Parameters.AddWithValue("@OrganizationId", location.OrganizationId);
                    cmd.Parameters.AddWithValue("@Address", location.Address);
                    cmd.Parameters.AddWithValue("@City", location.City);
                    cmd.Parameters.AddWithValue("@State", location.State);
                    cmd.Parameters.AddWithValue("@CreatedBy", location.CreatedBy);
                    cmd.Parameters.AddWithValue("@CreatedDate", location.CreatedDate);
                    cmd.Parameters.AddWithValue("@LocationId", location.Id);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "InsertOrganizationLocation", _tenantId));

                    int lastId = Convert.ToInt32(cmd.ExecuteScalar());
                    return lastId;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public List<OrganizationLocation> GetLocations(int organizationId)
        {
            List<OrganizationLocation> locations = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT * FROM iwbOrganizationLocation WHERE OrganizationId = @OrganizationId AND (IsDeleted = 0)";
                    cmd.Parameters.AddWithValue("@OrganizationId", organizationId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "GetLocations", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        locations = new List<OrganizationLocation>();
                        while (dr.Read())
                        {
                            var location = new OrganizationLocation();
                            location.Id = (int)dr["Id"];
                            location.Address = Convert.ToString(dr["Address"]);
                            location.City = Convert.ToString(dr["City"]);
                            location.State = Convert.ToString(dr["State"]);

                            locations.Add(location);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return locations;
        }

        public int DeleteLocation(int id, DateTime modifiedDate, int userId)
        {
            using (var conn = DALHelper.GetConnection(_tenantId))
            using (var cmd = conn.CreateCommand())
            {
                conn.Open();

                cmd.CommandType = CommandType.Text;
                cmd.CommandText = "UPDATE iwbOrganizationLocation SET IsDeleted = 1, LastModifiedDate = @ModifiedDate, LastModifiedBy = @userId Where Id = @Id";
                cmd.Parameters.AddWithValue("@Id", id);
                cmd.Parameters.AddWithValue("@ModifiedDate", modifiedDate);
                cmd.Parameters.AddWithValue("@userId", userId);
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "DeleteLocation", _tenantId));

                int rowsaffected = cmd.ExecuteNonQuery();

                return rowsaffected;
            }
        }

        #endregion
        public List<JobWelder> GetJobsByWelder(int UserId)
        {
            List<JobWelder> jobWelders = new List<JobWelder>();

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = "GetJobByWelder";
                    cmd.Parameters.AddWithValue("@welderId", UserId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "GetJobsByWelder", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            var jobWelder = new JobWelder
                            {
                                Id = dr["Id"] != DBNull.Value ? Convert.ToInt32(dr["Id"]) : 0,
                                Name = Convert.ToString(dr["name"])
                            };

                            jobWelders.Add(jobWelder);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "GetJobsByWelder", _tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw;
            }

            return jobWelders;
        }

        public UserInfoData GetUserDataByUser(int CurrentUserId)
        {
            UserInfoData user = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = "GetUserDataByUser";
                    cmd.Parameters.AddWithValue("@CurrentUserId", CurrentUserId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "GetUserDataByUser", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.Read())
                        {
                            user = new UserInfoData
                            {
                                //Id = dr["Id"] != DBNull.Value ? Convert.ToInt32(dr["UserNum"]) : 0,
                                UserName = dr["UserName"]?.ToString(),
                                Email = dr["UserEmail"]?.ToString(),
                                SSN = dr["SSN"]?.ToString(),
                                StencilNumber = dr["StencilNumber"]?.ToString(),
                                State = dr["State"]?.ToString(),
                                //Role = dr["Role"]?.ToString(),
                                City = dr["City"]?.ToString(),
                                Phone = dr["UserPhone"]?.ToString() ?? string.Empty,
                                DOB = dr["DOB"] != DBNull.Value ? Convert.ToDateTime(dr["DOB"]) : (DateTime?)null
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.IWB, "GetUserDataByUser", _tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw;
            }

            return user;
        }




        #region WPQ

        public int InsertScannedDocumentWPQ(IwbDocument document)
        {
            try
            {
                int rowAffected = 0;
                using (var conn = DALHelper.GetConnection(_tenantId))
                {
                    conn.Open();
                    using (var tran = conn.BeginTransaction())
                    {
                        try
                        {
                            using (var cmd = conn.CreateCommand())
                            {
                                //  1. Document

                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.CommandText = DBConstants.Iwb.IWB_DOCUMENT_INSERT;

                                cmd.Transaction = tran as SqlTransaction;
                                cmd.CommandTimeout = CMD_TIMEOUT;

                                cmd.Parameters.AddWithValue("@documentTypeId", (int)document.Type);
                                cmd.Parameters.AddWithValue("@alias", document.Alias);
                                cmd.Parameters.AddWithValue("@name", document.Name);
                                cmd.Parameters.AddWithValue("@fileName", document.FileName);
                                cmd.Parameters.AddWithValue("@filePath", document.FilePath);
                                cmd.Parameters.AddWithValue("@fileExtension", document.FileExtension);
                                cmd.Parameters.AddWithValue("@fileSize", document.FileSize);
                                cmd.Parameters.AddWithValue("@uploadDate", document.UploadDate);
                                cmd.Parameters.AddWithValue("@description", document.Description);
                                cmd.Parameters.AddWithValue("@userId", document.UserId);
                                cmd.Parameters.AddWithValue("@organizationId", document.OrganizationId);
                                cmd.Parameters.AddWithValue("@createdBy", document.CreatedBy);
                                cmd.Parameters.AddWithValue("@createdDate", document.CreatedDate);


                                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "InsertScannedDocumentWPQ", _tenantId));

                                int lastSavedId = Convert.ToInt32(cmd.ExecuteScalar());
                                document.Id = lastSavedId;

                                rowAffected++;
                                cmd.Parameters.Clear();

                                //  2. WPQ Document

                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.CommandText = DBConstants.Iwb.IWB_WPQ_DOCUMENT_INSERT;

                                cmd.Parameters.AddWithValue("@documentId", document.Id);
                                cmd.Parameters.AddWithValue("@userId", document.WPQ.UserId);
                                cmd.Parameters.AddWithValue("@jobId", document.WPQ.JobId);
                                cmd.Parameters.AddWithValue("@wpsId", document.WPQ.WpsId);
                                cmd.Parameters.AddWithValue("@startDate", document.WPQ.StartDate == DateTime.MinValue ? document.WPQ.StartDate : (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@endDate", document.WPQ.EndDate == DateTime.MinValue ? document.WPQ.EndDate : (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@expiryDate", document.WPQ.ExpiryDate == DateTime.MinValue ? document.WPQ.ExpiryDate : (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@certifiedAuthority", document.WPQ.CertifiedAuthority);
                                cmd.Parameters.AddWithValue("@certificateDate", document.WPQ.CertificateDate == DateTime.MinValue ? document.WPQ.CertificateDate : (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@inspectorName", document.WPQ.InspectorName);
                                cmd.Parameters.AddWithValue("@labNo", document.WPQ.LabNo);
                                cmd.Parameters.AddWithValue("@notes", document.WPQ.Notes);


                                lastSavedId = Convert.ToInt32(cmd.ExecuteScalar());
                                document.WPQ.Id = lastSavedId;

                                //  3. WPQ Document Sections
                                foreach (var section in document.WPQ.Sections)
                                {
                                    cmd.Parameters.Clear();
                                    cmd.CommandType = CommandType.StoredProcedure;
                                    cmd.CommandText = DBConstants.Iwb.IWB_WPQ_DOCUMENT_SECTION_INSERT;
                                    cmd.Transaction = tran as SqlTransaction;

                                    cmd.Parameters.AddWithValue("@wpqId", document.WPQ.Id);
                                    cmd.Parameters.AddWithValue("@sectionName", section.Name);

                                    int lastSavedSectionId = Convert.ToInt32(cmd.ExecuteScalar());
                                    section.Id = lastSavedSectionId;

                                    rowAffected++;

                                    //  3. WPQ Document Section Fields
                                    foreach (var field in section.Fields)
                                    {
                                        cmd.Parameters.Clear();
                                        //  3. Document Sections Fields
                                        cmd.CommandType = CommandType.StoredProcedure;
                                        cmd.CommandText = DBConstants.Iwb.IWB_WPQ_DOCUMENT_SECTION_FIELD_INSERT;
                                        cmd.Transaction = tran as SqlTransaction;

                                        cmd.Parameters.AddWithValue("@wpqId", document.WPQ.Id);
                                        cmd.Parameters.AddWithValue("@wpqSectionId", section.Id);
                                        cmd.Parameters.AddWithValue("@kDP", field.KDP);
                                        cmd.Parameters.AddWithValue("@InputValue", field.InputValue);
                                        cmd.Parameters.AddWithValue("@standardValue", field.StandardValue);
                                        cmd.Parameters.AddWithValue("@complianceStatus", field.ComplianceStatus);
                                        cmd.Parameters.AddWithValue("@reason", field.Reason);

                                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "InsertScannedDocumentWPQ", _tenantId));

                                        lastSavedId = Convert.ToInt32(cmd.ExecuteScalar());
                                        rowAffected++;
                                    }
                                }
                            }
                            tran.Commit();
                        }
                        catch
                        {
                            tran.Rollback();
                            throw;
                        }
                    }
                    return rowAffected;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public int InsertWPQ(Wpq wpq)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_WPQ_INSERT;

                    cmd.Parameters.AddWithValue("@Id", wpq.Id);
                    cmd.Parameters.AddWithValue("@UserId", wpq.UserId);
                    cmd.Parameters.AddWithValue("@JobId", wpq.JobId);
                    cmd.Parameters.AddWithValue("@TestId", wpq.TestId);
                    cmd.Parameters.AddWithValue("@FileName", wpq.FileName);
                    cmd.Parameters.AddWithValue("@StartDate", wpq.StartDate);
                    cmd.Parameters.AddWithValue("@EndDate", wpq.EndDate);
                    cmd.Parameters.AddWithValue("@ExpiryDate", wpq.ExpiryDate);
                    cmd.Parameters.AddWithValue("@CertifiedAuthority", wpq.CertifiedAuthority);
                    cmd.Parameters.AddWithValue("@CertificateDate", wpq.CertificateDate);
                    cmd.Parameters.AddWithValue("@InspectorName", wpq.InspectorName);
                    cmd.Parameters.AddWithValue("@LabNo", wpq.LabNo);
                    cmd.Parameters.AddWithValue("@Notes", wpq.Notes);
                    cmd.Parameters.AddWithValue("@UpdatedBy", wpq.CreatedBy);
                    cmd.Parameters.AddWithValue("@IsDraft", wpq.IsDraft);
                    cmd.Parameters.AddWithValue("@FieldData", wpq.FieldData);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "InsertWPQ", _tenantId));

                    int lastId = Convert.ToInt32(cmd.ExecuteScalar());
                    return lastId;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }


        public List<Wpq> GetWPQsByUserId(int userId)
        {
            List<Wpq> wpqs = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_WPQ_GETBY_USERID;

                    cmd.Parameters.AddWithValue("@userId", userId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "GetWpqsByUserId", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        wpqs = new List<Wpq>();
                        while (dr.Read())
                        {
                            var wpq = new Wpq();
                            wpq.Id = (int)dr["Id"];
                            wpq.JobId = Convert.ToInt32(dr["JobId"]);
                            wpq.UserId = Convert.ToInt32(dr["UserId"]);
                            wpq.FileName = Convert.ToString(dr["FileName"]);
                            wpq.StartDate = Convert.ToDateTime(dr["StartDate"]);
                            wpq.EndDate = Convert.ToDateTime(dr["EndDate"]);
                            wpq.ExpiryDate = Convert.ToDateTime(dr["ExpiryDate"]);
                            wpq.CertifiedAuthority = Convert.ToString(dr["CertifiedAuthority"]);
                            wpq.CertificateDate = Convert.ToDateTime(dr["CertificateDate"]);
                            wpq.InspectorName = Convert.ToString(dr["InspectorName"]);
                            wpq.LabNo = Convert.ToString(dr["LabNo"]);
                            wpq.Notes = Convert.ToString(dr["Notes"]);

                            wpqs.Add(wpq);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return wpqs;
        }

        public List<Wpq> GetWPQList(int userId)
        {
            List<Wpq> wpqs = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_WPQ_GETLIST;

                    cmd.Parameters.AddWithValue("@UserId", userId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "PRO_GetMyWPQList", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        wpqs = new List<Wpq>();
                        while (dr.Read())
                        {
                            var wpq = new Wpq();
                            wpq.Id = (int)dr["Id"];
                            wpq.JobId = Convert.ToInt32(dr["JobId"]);
                            wpq.UserId = Convert.ToInt32(dr["UserId"]);
                            wpq.FileName = Convert.ToString(dr["FileName"]);
                            wpq.StartDate = Convert.ToDateTime(dr["StartDate"]);
                            wpq.EndDate = Convert.ToDateTime(dr["EndDate"]);
                            wpq.ExpiryDate = Convert.ToDateTime(dr["ExpiryDate"]);
                            wpq.FormattedExpiryDate = wpq.ExpiryDate.ToString("MM/dd/yyyy hh:mm tt");
                            wpq.CertifiedAuthority = Convert.ToString(dr["CertifiedAuthority"]);
                            wpq.CertificateDate = Convert.ToDateTime(dr["CertificateDate"]);
                            wpq.InspectorName = Convert.ToString(dr["InspectorName"]);
                            wpq.LabNo = Convert.ToString(dr["LabNo"]);
                            wpq.Notes = Convert.ToString(dr["Notes"]);

                            wpqs.Add(wpq);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return wpqs;
        }

        public Wpq GetWPQDetail(Wpq wpqDetail)
        {
            Wpq data = new Wpq();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_WPQ_GETDETAIL;

                    cmd.Parameters.AddWithValue("@Id", wpqDetail.Id);
                    cmd.Parameters.AddWithValue("@TestId", wpqDetail.TestId);
                    cmd.Parameters.AddWithValue("@WelderId", wpqDetail.UserId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "PRO_GetWPQDetail", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            data.FieldData = ConversionUtility.ToString(dr["FieldData"]);
                            data.UserName = ConversionUtility.ToString(dr["UserName"]);
                            data.UserNum = ConversionUtility.ToString(dr["UserNum"]);
                            data.TestId = ConversionUtility.ToInteger(dr["TestId"]);
                            data.JobId = ConversionUtility.ToInteger(dr["JobId"]);
                            data.CertificateDate = ConversionUtility.ToDateTime(dr["CertificateDate"]);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return data;
        }
        #endregion


        #region Testing

        public List<IwbTestType> GetTestTypeList()
        {
            List<IwbTestType> lstTestTypes = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT Id, CostCode FROM iwbTestType WHERE (CostCode is NOT NULL)";

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "GetTestTypeList", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        lstTestTypes = new List<IwbTestType>();
                        while (dr.Read())
                        {
                            var testType = new IwbTestType();
                            testType.Id = (int)dr["Id"];
                            testType.CostCode = Convert.ToString(dr["CostCode"]);

                            lstTestTypes.Add(testType);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return lstTestTypes;
        }

        public int InsertTest(IwbTest test)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_TEST_INSERT;

                    //cmd.Parameters.AddWithValue("@TenantId", _tenantId);
                    cmd.Parameters.AddWithValue("@typeId", test.TypeId);
                    cmd.Parameters.AddWithValue("@currentStatusId", test.CurrentStatus);
                    cmd.Parameters.AddWithValue("@organizationId", test.OrganizationId);
                    cmd.Parameters.AddWithValue("@name", test.Name);
                    cmd.Parameters.AddWithValue("@startDate", test.StartDate);
                    cmd.Parameters.AddWithValue("@deadline", test.Deadline);
                    cmd.Parameters.AddWithValue("@noOfPositions", test.NoOfPositions);
                    cmd.Parameters.AddWithValue("@description", test.Description);
                    cmd.Parameters.AddWithValue("@createdBy", test.CreatedBy);
                    cmd.Parameters.AddWithValue("@createdDate", test.CreatedDate);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "InsertTest", _tenantId));

                    int lastId = Convert.ToInt32(cmd.ExecuteScalar());
                    return lastId;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public List<IwbTest> GetTestsByWhereClause(string whereClause, out int totalPages, out long totalRecords, int pageIndex = 1, int pageSize = 10)
        {
            List<IwbTest> tests = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_TEST_GETBY_WHERECLAUSE;
                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);
                    cmd.Parameters.AddWithValue("@WhereClause", whereClause);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "GetTestsByWhereClause", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        tests = new List<IwbTest>();
                        while (dr.Read())
                        {
                            var test = new IwbTest
                            {
                                Id = Convert.ToInt32(dr["Id"]),
                                Name = Convert.ToString(dr["Name"]),
                                StartDate = Convert.ToDateTime(dr["StartDate"]),
                                Deadline = Convert.ToDateTime(dr["Deadline"]),
                                NoOfPositions = Convert.ToInt32(dr["NoOfPositions"]),
                                TypeId = Convert.ToInt32(dr["TypeId"]),
                                TypeCode = Convert.ToString(dr["CostCode"]),
                                CurrentStatus = (IwbTestStatus)Enum.Parse(typeof(IwbTestStatus), Convert.ToString(dr["CurrentStatusId"])),
                                Description = Convert.ToString(dr["Description"]),
                                NoOfApplicants = Convert.ToInt32(dr["NoOfPositions"]),
                            };

                            tests.Add(test);
                        }
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }

            return tests;
        }

        public int RegisterWelderForTest(int welderId, int testId, int createdBy, DateTime createdDate, int registrationStatusId = 2, int passStatusId = 1)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    //cmd.CommandText = "INSERT INTO [iwbTestAttendees] ([WelderId] ,[TestId], [RegistrationStatusId], [PassStatusId], [CreatedBy] ,[CreatedDate]) VALUES (@welderId, @testId, @registrationStatusId, @passStatusId, @createdBy, @createdDate);SELECT SCOPE_IDENTITY();";
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_TEST_ATTENDEES_INSERT;
                    //cmd.Parameters.AddWithValue("@TenantId", _tenantId);
                    cmd.Parameters.AddWithValue("@welderId", welderId);
                    cmd.Parameters.AddWithValue("@testId", testId);
                    cmd.Parameters.AddWithValue("@registrationStatusId", registrationStatusId);
                    cmd.Parameters.AddWithValue("@passStatusId", passStatusId);
                    cmd.Parameters.AddWithValue("@createdBy", createdBy);
                    cmd.Parameters.AddWithValue("@createdDate", createdDate);//DateTime.Now

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "RegisterWelderForTest", _tenantId));

                    int lastId = Convert.ToInt32(cmd.ExecuteScalar());
                    return lastId;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public List<IwbTestAttendee> GetTestAttendees(int testId)
        {
            List<IwbTestAttendee> tests = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_TEST_ATTENDEES_GETBY_TESTID;
                    cmd.Parameters.AddWithValue("@testId", testId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "GetTestAttendees", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        tests = new List<IwbTestAttendee>();
                        while (dr.Read())
                        {

                            var user = new User
                            {
                                UserNum = (int)dr["UserNum"],
                                UserType = (int)dr["UserType"],
                                UserName = Convert.ToString(dr["UserName"]),
                                UserID = Convert.ToString(dr["UserID"]),
                                UserEmail = Convert.ToString(dr["UserEmail"]),

                                CreatedBy = !DBRecordExtensions.HasColumn(dr, "CreatedBy") ? 0 : dr["CreatedBy"] == DBNull.Value ? 0 : Convert.ToInt32(dr["CreatedBy"]),
                                IsIwbUser = dr["IsIwbUser"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsIwbUser"]),
                                OrganizationId = dr["OrganizationId"] == DBNull.Value ? 0 : Convert.ToInt32(dr["OrganizationId"]),
                                DOB = dr["DOB"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["DOB"]),
                                City = Convert.ToString(dr["City"]),
                                State = Convert.ToString(dr["State"]),
                                SocialSecurityNumber = Convert.ToString(dr["SSN"]),
                                WelderStencilNumber = Convert.ToString(dr["StencilNumber"]),
                                CustomerIdZoho = !DBRecordExtensions.HasColumn(dr, "CustomerIdZoho") ? string.Empty : Convert.ToString(dr["CustomerIdZoho"]),
                                Descr = !DBRecordExtensions.HasColumn(dr, "OrganizationName") ? string.Empty : Convert.ToString(dr["OrganizationName"]),
                                UMUserType = "Welder",
                            };



                            var test = new IwbTestAttendee
                            {
                                Id = Convert.ToInt32(dr["Id"]),
                                TestId = Convert.ToInt32(dr["TestId"]),
                                RegistrationStatus = (IwbTestRegistrationStatus)Enum.Parse(typeof(IwbTestRegistrationStatus), Convert.ToString(dr["RegistrationStatusId"])),
                                PassStatus = (IwbTestPassStatus)Enum.Parse(typeof(IwbTestPassStatus), Convert.ToString(dr["PassStatusId"])),
                                Welder = user
                            };

                            test.WPQData = new Wpq();
                            test.WPQData.Id = Convert.ToInt32(dr["WPQId"]);
                            test.WPQData.IsDraft = Convert.ToBoolean(dr["IsDraft"]);
                            test.WPQData.FileName = Convert.ToString(dr["FileName"]);
                            tests.Add(test);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return tests;
        }

        public Dictionary<string, object> GetSignOffRequestUsers(int documentId)
        {
            Dictionary<string, object> resultSet = new Dictionary<string, object>();
            List<SignOffRequestUser> userList = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_GETSIGNOFF_REQUESTUSERS;
                    cmd.Parameters.AddWithValue("@DocumentId", documentId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "GetSignOffRequestUsers", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        userList = new List<SignOffRequestUser>();

                        while (dr.Read())
                        {
                            var user = new SignOffRequestUser
                            {
                                Id = Convert.ToInt32(dr["Id"]),
                                Name = Convert.ToString(dr["Name"]),
                                Email = Convert.ToString(dr["Email"]),
                                SignStatus = (SignStatus)Convert.ToInt32(dr["SignStatus"])
                            };

                            userList.Add(user);
                        }

                        dr.NextResult();
                        string ownerEmail = "";
                        while (dr.Read())
                        {
                            ownerEmail = ConversionUtility.ToString(dr["UserEmail"]);
                        }

                        resultSet.Add("userList", userList);
                        resultSet.Add("ownerEmail", ownerEmail);
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }


            return resultSet;
        }


        public int UpdateTestStatus(IwbTest test)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_TEST_UPDATE_STATUS;

                    cmd.Parameters.AddWithValue("@Id", test.Id);
                    cmd.Parameters.AddWithValue("@CurrentStatus", test.CurrentStatus);
                    cmd.Parameters.AddWithValue("@UpdatedBy", test.CreatedBy);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "UpdateTestStatus", _tenantId));

                    int lastId = Convert.ToInt32(cmd.ExecuteScalar());
                    return lastId;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public int InsertTestInvitation(IwbTestInvitation testInvite)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_TEST_INVITATION_INSERT;

                    //cmd.Parameters.AddWithValue("@TenantId", _tenantId);
                    cmd.Parameters.AddWithValue("@testId", testInvite.TestId);
                    cmd.Parameters.AddWithValue("@welderId", testInvite.WelderId);
                    cmd.Parameters.AddWithValue("@organizationId", testInvite.OrganizationId);
                    cmd.Parameters.AddWithValue("@description", testInvite.Description);
                    cmd.Parameters.AddWithValue("@createdBy", testInvite.CreatedBy);
                    cmd.Parameters.AddWithValue("@createdDate", testInvite.CreatedDate);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "InsertTestInvitation", _tenantId));

                    int lastId = Convert.ToInt32(cmd.ExecuteScalar());
                    return lastId;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        #endregion

        #region Dashboard

        public IwbResponse GetWelderDashboardData(int userId)
        {
            List<Job> jobs = null;
            List<IwbTest> tests = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_WELDER_DASHBOARD;
                    cmd.Parameters.AddWithValue("@userId", userId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "GetWorkHistories", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        jobs = new List<Job>();
                        while (dr.Read())
                        {
                            var job = new Job
                            {
                                Id = Convert.ToInt32(dr["Id"]),
                                WpsId = Convert.ToInt32(dr["WpsId"]),
                                WpsName = Convert.ToString(dr["WPSName"]),
                                InitiatorId = Convert.ToInt32(dr["InitiatorId"]),
                                PerformerId = Convert.ToInt32(dr["PerformerId"]),
                                Title = Convert.ToString(dr["Title"]),
                                StartDate = Convert.ToDateTime(dr["StartDate"]),
                                EndDate = Convert.ToDateTime(dr["EndDate"]),
                                CreatedDate = Convert.ToDateTime(dr["CreatedDate"]),
                                CurrentStatus = (IwbJobStatus)Enum.Parse(typeof(IwbJobStatus), Convert.ToString(dr["CurrentStatus"]))
                            };
                            jobs.Add(job);
                        }
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            tests = new List<IwbTest>();
                            while (dr.Read())
                            {
                                var test = new IwbTest
                                {
                                    Id = Convert.ToInt32(dr["Id"]),
                                    Name = Convert.ToString(dr["Name"]),
                                    StartDate = Convert.ToDateTime(dr["StartDate"]),
                                    Deadline = Convert.ToDateTime(dr["Deadline"]),
                                    NoOfPositions = Convert.ToInt32(dr["NoOfPositions"]),
                                    TypeId = Convert.ToInt32(dr["TypeId"]),
                                    TypeCode = Convert.ToString(dr["CostCode"]),
                                    CurrentStatus = (IwbTestStatus)Enum.Parse(typeof(IwbTestStatus), Convert.ToString(dr["CurrentStatusId"])),
                                    Description = Convert.ToString(dr["Description"]),
                                };

                                tests.Add(test);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return new IwbResponse { Jobs = jobs, Tests = tests, };
        }

        #endregion

        #region Reporting related methods
        public List<Job> GetcontractorReport(int userId, IwbCriteria criteria, int selectedUserId)
        {
            List<Job> jobList = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_GET_CONTRACT_REPORT;

                    cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@FromDate", criteria.StartDate);
                    cmd.Parameters.AddWithValue("@ToDate", criteria.EndDate);
                    cmd.Parameters.AddWithValue("@SelectedUserId", selectedUserId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "PRO_GetMyWPQList", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        jobList = new List<Job>();
                        while (dr.Read())
                        {
                            var jobData = new Job();
                            jobData.Title = Convert.ToString(dr["Title"]);
                            jobData.StartDate = Convert.ToDateTime(dr["StartDate"]);
                            jobData.EndDate = Convert.ToDateTime(dr["EndDate"]);
                            jobData.InitiatorName = Convert.ToString(dr["InitiatorName"]);

                            jobList.Add(jobData);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return jobList;
        }

        public List<IwbDocument> GetOwnerReportdata(int userId, IwbCriteria criteria, int selectedUserId)
        {
            List<IwbDocument> jobList = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_GET_OWNER_REPORT;

                    cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@FromDate", criteria.StartDate);
                    cmd.Parameters.AddWithValue("@ToDate", criteria.EndDate);
                    cmd.Parameters.AddWithValue("@SelectedUserId", selectedUserId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "PRO_GetOwnerReportData", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        jobList = new List<IwbDocument>();
                        while (dr.Read())
                        {
                            var jobData = new IwbDocument();
                            jobData.Name = Convert.ToString(dr["Title"]);
                            jobData.UploadDate = Convert.ToDateTime(dr["StartDate"]);
                            jobData.ExpiryDate = Convert.ToDateTime(dr["ExpiryDate"]);
                            jobData.Description = Convert.ToString(dr["InitiatorName"]);

                            jobList.Add(jobData);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return jobList;
        }

        public List<IwbDocument> GetInsuranceReportdata(int userId, IwbCriteria criteria, int selectedUserId)
        {
            List<IwbDocument> jobList = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_GET_INSURANCE_REPORT;

                    cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@FromDate", criteria.StartDate);
                    cmd.Parameters.AddWithValue("@ToDate", criteria.EndDate);
                    cmd.Parameters.AddWithValue("@SelectedUserId", selectedUserId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "PRO_GetOwnerReportData", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        jobList = new List<IwbDocument>();
                        while (dr.Read())
                        {
                            var jobData = new IwbDocument();
                            jobData.Name = Convert.ToString(dr["Title"]);
                            jobData.UploadDate = Convert.ToDateTime(dr["StartDate"]);
                            jobData.ExpiryDate = Convert.ToDateTime(dr["ExpiryDate"]);
                            jobData.Description = Convert.ToString(dr["InitiatorName"]);

                            jobList.Add(jobData);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return jobList;
        }

        public List<IwbTest> GetTestingReportdata(int userId, IwbCriteria criteria, int selectedUserId)
        {
            List<IwbTest> jobList = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_GET_TESTING_REPORT;

                    cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@FromDate", criteria.StartDate);
                    cmd.Parameters.AddWithValue("@ToDate", criteria.EndDate);
                    cmd.Parameters.AddWithValue("@SelectedUserId", selectedUserId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "GetTestingReportdata", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        jobList = new List<IwbTest>();
                        while (dr.Read())
                        {
                            var jobData = new IwbTest();
                            jobData.Name = Convert.ToString(dr["Title"]);
                            jobData.StartDate = Convert.ToDateTime(dr["StartDate"]);
                            jobData.Description = Convert.ToString(dr["Description"]);
                            jobData.TypeCode = Convert.ToString(dr["InitiatorName"]);

                            jobList.Add(jobData);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return jobList;
        }
        #endregion


        #region Worker history related methods
        public List<UserWorkHistory> GetContractorWorkHistory(int userId)
        {
            List<UserWorkHistory> historyList = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_GET_CONTRACTOR_WORK_HISTORY;

                    cmd.Parameters.AddWithValue("@UserId", userId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "PRO_GetWorkerHistory", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        historyList = new List<UserWorkHistory>();
                        while (dr.Read())
                        {
                            var history = new UserWorkHistory();
                            history.Title = ConversionUtility.ToString(dr["Title"]);
                            history.OrganizationName = ConversionUtility.ToString(dr["Username"]);
                            history.StartDate = ConversionUtility.ToString(dr["StartDate"]);
                            history.EndDate = ConversionUtility.ToString(dr["EndDate"]);
                            history.Location = ConversionUtility.ToString(dr["Location"]);
                            history.Rating = ConversionUtility.ToInteger(dr["Rating"]);
                            historyList.Add(history);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return historyList;
        }

        public object GetWelderWorkHistory(int userId, bool isActive)
        {
            List<User> historyList = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_GET_WELDER_WORK_HISTORY;

                    cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@IsActive", isActive);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "PRO_GetWelderHistory", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        historyList = new List<User>();
                        while (dr.Read())
                        {
                            var history = new User();
                            history.UserName = ConversionUtility.ToString(dr["Username"]);
                            history.UserEmail = ConversionUtility.ToString(dr["UserEmail"]);
                            history.SocialSecurityNumber = ConversionUtility.ToString(dr["SSN"]);
                            history.WelderStencilNumber = ConversionUtility.ToString(dr["StencilNumber"]);
                            history.City = ConversionUtility.ToString(dr["City"]);
                            history.ExtName = ConversionUtility.ToString(dr["Title"]);
                            history.UserNum = ConversionUtility.ToInteger(dr["UserNum"]);
                            history.CustomerIdZoho = !DBRecordExtensions.HasColumn(dr, "CustomerIdZoho") ? string.Empty : Convert.ToString(dr["CustomerIdZoho"]);
                            historyList.Add(history);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return historyList;
        }

        public object GetWelderInfo(int Id)
        {
            User welderData = new User();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_GET_WELDER_INFO;

                    cmd.Parameters.AddWithValue("@Id", Id);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "PRO_GetWelderInfo", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            welderData.UserName = ConversionUtility.ToString(dr["Username"]);
                            welderData.UserEmail = ConversionUtility.ToString(dr["UserEmail"]);
                            welderData.SocialSecurityNumber = ConversionUtility.ToString(dr["SSN"]);
                            welderData.WelderStencilNumber = ConversionUtility.ToString(dr["StencilNumber"]);
                            welderData.City = ConversionUtility.ToString(dr["City"]);
                            welderData.UserPhone = ConversionUtility.ToString(dr["UserPhone"]);
                            welderData.DOB = ConversionUtility.ToDateTime(dr["DOB"]);
                            welderData.State = ConversionUtility.ToString(dr["State"]);
                            welderData.City = ConversionUtility.ToString(dr["City"]);
                            welderData.UserPic = ConversionUtility.ToString(dr["UserPic"]);
                            welderData.CustomerIdZoho = !DBRecordExtensions.HasColumn(dr, "CustomerIdZoho") ? string.Empty : Convert.ToString(dr["CustomerIdZoho"]);

                            if (string.IsNullOrEmpty(welderData.UserPic))
                            {
                                welderData.UserPic = "../Uploads/UserImages/user.jpg";
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return welderData;
        }

        public int UpdateWelderTestStatus(IwbTestAttendee attendeeData, int userId)
        {
            int rowsAffected = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_UPDATE_WELDER_TEST_STATUS;
                    cmd.Parameters.AddWithValue("@Id", attendeeData.Id);
                    cmd.Parameters.AddWithValue("@IsPass", (int)attendeeData.PassStatus);
                    cmd.Parameters.AddWithValue("@LastModifiedBy", userId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "UpdateWelderTestStatus", _tenantId));

                    rowsAffected = cmd.ExecuteNonQuery();
                }
                return rowsAffected;
            }
            catch (Exception ex) { throw ex; }
        }
        #endregion


        #region Capture AI REsposne
        public int InsertAIResponse(AIResponse reqData)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_INSERT_AI_RESPONSE;

                    //cmd.Parameters.AddWithValue("@TenantId", _tenantId);
                    cmd.Parameters.AddWithValue("@RequestUrl", reqData.RequestUrl);
                    cmd.Parameters.AddWithValue("@RequestJson", reqData.RequestJson);
                    cmd.Parameters.AddWithValue("@ResponseJson", reqData.ResponseJson);
                    cmd.Parameters.AddWithValue("@IsError", reqData.IsError);
                    cmd.Parameters.AddWithValue("@ErrorDescription", reqData.ErrorDescription);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "InsertAIResponse", _tenantId));

                    int lastId = Convert.ToInt32(cmd.ExecuteScalar());
                    return lastId;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        #endregion



        #region IWB User Registration

        public int RegisterUser(IwbUser user, int organizationId, string password)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.IWB_USER_INSERT;

                    cmd.Parameters.AddWithValue("@UserID ", user.Email);
                    cmd.Parameters.AddWithValue("@UserPW", password);
                    cmd.Parameters.AddWithValue("@UserName", user.Name);
                    //cmd.Parameters.AddWithValue("@Ext", user.Ext);
                    //cmd.Parameters.AddWithValue("@SearchRest", user.SearchRest);
                    cmd.Parameters.AddWithValue("@UserEmail", user.Email);
                    cmd.Parameters.AddWithValue("@IdentityNumber", user.SocialSecurityNumber);
                    cmd.Parameters.AddWithValue("@UserPhone", user.Phone);
                    //cmd.Parameters.AddWithValue("@UserFax", user.Phone);
                    //cmd.Parameters.AddWithValue("@JoinBeginDate", user.JoinBeginDate.ToString("yyyyMMdd"));
                    //cmd.Parameters.AddWithValue("@JoinEndDate", user.JoinEndDate.ToString("yyyyMMdd"));
                    //cmd.Parameters.AddWithValue("@Descr", user.Descr);
                    //cmd.Parameters.AddWithValue("@GroupNum", user.GroupNum);
                    //cmd.Parameters.AddWithValue("@POD", user.POD);
                    //cmd.Parameters.AddWithValue("@EOD", user.EOD);
                    //cmd.Parameters.AddWithValue("@Pause", user.Pause);
                    //cmd.Parameters.AddWithValue("@CompanyName", user.CompanyName);
                    //cmd.Parameters.AddWithValue("@IsEnterpriseUser", user.IsEnterpriseUser);
                    cmd.Parameters.AddWithValue("@UserType", user.Type);
                    //cmd.Parameters.AddWithValue("@IsRevCell", user.IsRevCell);
                    cmd.Parameters.AddWithValue("@RoleId", user.Type);// user.RoleId);
                    //cmd.Parameters.AddWithValue("@IsOnlyIQ3ModeEnabled", isOnlyIQ3ModeEnabled);
                    cmd.Parameters.AddWithValue("@createdBy", user.CreatedBy);
                    cmd.Parameters.AddWithValue("@isIwbUser", true);
                    cmd.Parameters.AddWithValue("@organizationId", organizationId);
                    cmd.Parameters.AddWithValue("@dob", user.DOB);
                    cmd.Parameters.AddWithValue("@city", user.City);
                    cmd.Parameters.AddWithValue("@state", user.State);
                    cmd.Parameters.AddWithValue("@SocialSecurityNumber", user.SocialSecurityNumber);
                    cmd.Parameters.AddWithValue("@StencilNumber", user.WelderStencilNumber);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "RegisterUser", _tenantId));

                    int lastId = Convert.ToInt32(cmd.ExecuteScalar());
                    return lastId;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        
        public int SetCustomerIdForUser(int userNum, string customerId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = $"UPDATE t_Account SET CustomerIdZoho = '{customerId}', Descr = {customerId} WHERE UserNum = {userNum};";
                    //cmd.Parameters.AddWithValue("@CustomerIdZoho", customerId);
                    //cmd.Parameters.AddWithValue("@UserNum", userNum);

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "SetCustomerIdForUser", _tenantId));

                    conn.Open();
                    int rowsAffected = cmd.ExecuteNonQuery();

                    return rowsAffected;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        
        #endregion

        public int InsertWelderRatings(WelderRatingModel reqData)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.WELDERRATINGS_INSERT;

                    cmd.Parameters.AddWithValue("@WelderId", reqData.WelderId);
                    cmd.Parameters.AddWithValue("@PerformanceRating", reqData.PerformanceRating);
                    cmd.Parameters.AddWithValue("@PerformanceComment", reqData.PerformanceComment ?? string.Empty);
                    cmd.Parameters.AddWithValue("@WeldCount", reqData.WeldCount);
                    cmd.Parameters.AddWithValue("@WeldCountComment", reqData.WeldCountComment ?? string.Empty);
                    cmd.Parameters.AddWithValue("@Repairs", reqData.Repairs);
                    cmd.Parameters.AddWithValue("@RepairsComment", reqData.RepairsComment ?? string.Empty);
                    cmd.Parameters.AddWithValue("@WorkEthicRating", reqData.WorkEthicRating);
                    cmd.Parameters.AddWithValue("@WorkEthicComment", reqData.WorkEthicComment ?? string.Empty);
                    cmd.Parameters.AddWithValue("@CreatedBy", reqData.CreatedBy);

                    conn.Open();

                    Task.Run(() =>
                        RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "InsertWelderRatings", _tenantId));

                    int lastId = Convert.ToInt32(cmd.ExecuteScalar());
                    return lastId;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public List<WelderRatingModel> GetWelderRatingsById(int welderId)
        {
            List<WelderRatingModel> ratings = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.VIEW_WELDERRATINGS_GETBY_ID;
                    cmd.Parameters.AddWithValue("@WelderId", welderId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "GetWelderRatingsById", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        ratings = new List<WelderRatingModel>();
                        while (dr.Read())
                        {
                            var rating = new WelderRatingModel
                            {
                                Id = Convert.ToInt32(dr["Id"]),
                                WelderId = Convert.ToInt32(dr["WelderId"]),
                                PerformanceRating = Convert.ToInt32(dr["PerformanceRating"]),
                                PerformanceComment = Convert.ToString(dr["PerformanceComment"]),
                                WeldCount = Convert.ToInt32(dr["WeldCount"]),
                                WeldCountComment = Convert.ToString(dr["WeldCountComment"]),
                                Repairs = Convert.ToInt32(dr["Repairs"]),
                                RepairsComment = Convert.ToString(dr["RepairsComment"]),
                                WorkEthicRating = Convert.ToInt32(dr["WorkEthicRating"]),
                                WorkEthicComment = Convert.ToString(dr["WorkEthicComment"])
                            };

                            ratings.Add(rating);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }

            return ratings;
        }

        public List<JobTitleModel> GetJobTitles()
        {
            List<JobTitleModel> jobTitles = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.GET_JOB_TITLES;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "GetJobTitles", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        jobTitles = new List<JobTitleModel>();

                        while (dr.Read())
                        {
                            jobTitles.Add(new JobTitleModel
                            {
                                Id = Convert.ToInt32(dr["Id"]),
                                Title = Convert.ToString(dr["Title"])
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }

            return jobTitles;
        }


        public JobDetailsModel GetJobDetailsById(int jobId)
        {
            JobDetailsModel jobDetails = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Iwb.GET_JOB_DETAILS_BY_ID;

                    // Add the jobId parameter to the command
                    cmd.Parameters.Add(new SqlParameter("@jobId", SqlDbType.Int) { Value = jobId });

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "GetJobDetailsById", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.Read())
                        {
                            jobDetails = new JobDetailsModel
                            {
                                Id = Convert.ToInt32(dr["Id"]),
                                StartDate = Convert.ToDateTime(dr["StartDate"]),
                                EndDate = Convert.ToDateTime(dr["EndDate"]),
                                Location = Convert.ToString(dr["Location"]),
                                RecordNumber = Convert.ToString(dr["RecordNumber"]),
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }

            return jobDetails;
        }



        public Tuple<bool, string> PostToZohoInvoiceAPI(ZohoInvoicePayload payload, string accessToken, string orgId, string endpoint)
        {
            try
            {
                
                var response = SendZohoInvoiceRequest(endpoint, payload, accessToken, orgId);

                if (response.Item1)
                {
                    var zohoResponse = JsonConvert.DeserializeObject<ZohoInvoiceResponse>(response.Item2);
                    var invoice = zohoResponse?.invoice;

                    if (invoice != null)
                    {
                        using (var conn = DALHelper.GetConnection(_tenantId))
                        using (var cmd = conn.CreateCommand())
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.CommandText = DBConstants.Zoho.INSERT_ZOHO_INVOICE;

                            cmd.Parameters.AddWithValue("@InvoiceId", invoice.invoice_id);
                            cmd.Parameters.AddWithValue("@InvoiceNumber", invoice.invoice_number);
                            cmd.Parameters.AddWithValue("@CustomerId", invoice.customer_id);
                            cmd.Parameters.AddWithValue("@CustomerName", invoice.customer_name ?? "");
                            cmd.Parameters.AddWithValue("@Email", invoice.email ?? "");
                            cmd.Parameters.AddWithValue("@TotalAmount", invoice.total);
                            cmd.Parameters.AddWithValue("@Status", invoice.status ?? "sent");
                            cmd.Parameters.AddWithValue("@DueDate", invoice.due_date);
                            cmd.Parameters.AddWithValue("@InvoiceUrl", invoice.invoice_url ?? "");
                            cmd.Parameters.AddWithValue("@CreatedDate", DateTime.Now);
                            cmd.Parameters.AddWithValue("@UpdatedOn", DateTime.Now);

                            conn.Open();
                            Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.IWB, "InsertZohoInvoice", _tenantId));
                            cmd.ExecuteNonQuery();
                        }

                        SendInvoiceEmail(invoice.invoice_id, invoice.invoice_number, invoice.email, accessToken);
                    }
                }

                return Tuple.Create(response.Item1, response.Item2);
            }
            catch (Exception ex)
            {
                return Tuple.Create(false, $"Exception: {ex.Message}");
            }
        }


        private Tuple<bool, string, string> SendZohoInvoiceRequest(string url, ZohoInvoicePayload payload, string accessToken, string orgId)
        {
            using (var client = new HttpClient())
            {
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Zoho-oauthtoken", accessToken);
                client.DefaultRequestHeaders.Add("X-com-zoho-invoice-organizationid", orgId);

                var invoiceData = new
                {
                    customer_id = payload.customer_id,
                    reference_number = payload.reference_number,
                    date = payload.date,
                    due_date = payload.due_date,
                    payment_terms = payload.payment_terms,
                    discount = payload.discount,                   
                    invoice_items = payload.invoice_items?.Select(item => new
                    {
                        //name = item.name,
                        product_id = item.product_id,
                        description = item.description,
                        quantity = item.quantity,
                        rate = item.rate
                    }).ToList(),
                        payment_gateways = new[]
                        {
                            new { configured_gateway_id = "stripe" }  // Or use payload.gateway_id if dynamic
                        },

                            redirect_url = "https://www.zohoapis.com/payment-success"
                };

                string json = JsonConvert.SerializeObject(invoiceData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = client.PostAsync(url, content).Result;
                var body = response.Content.ReadAsStringAsync().Result;
                Console.WriteLine($"Status: {response.StatusCode}");
                Console.WriteLine($"Body: {body}");


                return Tuple.Create(response.IsSuccessStatusCode, body, body);
            }
        }


        private void SendInvoiceEmail(string invoiceId, string invoiceNumber, string toEmail, string accessToken)
        {
            try
            {
                string orgId = ConfigurationManager.AppSettings["zohoBillingOrganizationId"];
                string endpoint = $"https://www.zohoapis.com/subscriptions/v1/invoices/{invoiceId}/email";

                var emailBody = new
                {
                    to_mail_ids = new[] { toEmail },
                    subject = "Auto-send invoice",
                    body = $"Dear Customer,\n\nThank you for your business.\n\nPlease find attached your invoice {invoiceNumber}. You can view or download the invoice using the link below.\n\nIf you have any questions, feel free to reach out to us.\n\nBest regards,\nAccounts Team.",
                    send = true
                };

                var content = new StringContent(JsonConvert.SerializeObject(emailBody), Encoding.UTF8, "application/json");

                using (var client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Zoho-oauthtoken", accessToken);
                    client.DefaultRequestHeaders.Add("X-com-zoho-subscriptions-organizationid", orgId);

                    var response = client.PostAsync(endpoint, content).Result;
                    var responseBody = response.Content.ReadAsStringAsync().Result;

                    Console.WriteLine("SendInvoiceEmail Response:");
                    Console.WriteLine($"Status: {response.StatusCode}");
                    Console.WriteLine($"Body: {responseBody}");

                    if (!response.IsSuccessStatusCode)
                    {
                        Console.WriteLine("Failed to send invoice email.");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in SendInvoiceEmail: " + ex.Message);
            }
        }


        public List<ZohoTransaction> GetZohoTransactionHistory(string customerId)
        {
            try
            {
                // This will call Zoho API to get transaction history

                string accessToken = ConfigurationManager.AppSettings["zohoBillingAccessToken"];
                string orgId = ConfigurationManager.AppSettings["zohoBillingOrganizationId"];
                string url = $"https://www.zohoapis.in/subscriptions/v1/customers/{customerId}/payments";
                var request = (HttpWebRequest)WebRequest.Create(url);
                request.Method = "GET";
                request.Headers.Add("Authorization", $"Zoho-oauthtoken {accessToken}");
                request.Headers.Add("X-com-zoho-subscriptions-organizationid", orgId);
                var response = (HttpWebResponse)request.GetResponse();
                using (var reader = new StreamReader(response.GetResponseStream()))
                {
                    var result = reader.ReadToEnd();
                    return ParseZohoTransactions(result);
                }
            }
            catch (Exception ex)
            {
                throw new Exception("Error fetching Zoho transaction history: " + ex.Message);
            }
        }

        private List<ZohoTransaction> ParseZohoTransactions(string jsonResponse)
        {
            var transactions = new List<ZohoTransaction>();
            try
            {
                dynamic zohoResponse = JsonConvert.DeserializeObject(jsonResponse);

                if (zohoResponse?.payments != null)
                {
                    foreach (var payment in zohoResponse.payments)
                    {
                        transactions.Add(new ZohoTransaction
                        {
                            InvoiceId = payment.invoice_id,
                            InvoiceNumber = payment.invoice_number,
                            Date = DateTime.Parse(payment.date.ToString()),
                            Total = decimal.Parse(payment.amount.ToString()),
                            Status = payment.status,
                            PaymentMethod = payment.payment_mode ?? "N/A",
                            InvoiceUrl = "", // Optional: You could look up the invoice_url in a second call if needed
                            CurrencySymbol = payment.currency_symbol,
                            CustomerName = payment.customer_name
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception("Error parsing Zoho payment response: " + ex.Message);
            }

            return transactions;
        }



    }
}