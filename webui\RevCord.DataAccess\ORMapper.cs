using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.SurveyEntities;
using System.Data.SqlClient;
using RevCord.DataContracts;
using RevCord.DataContracts.EvaluationEntities;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.DataContracts.DTO;
using RevCord.DataContracts.UserManagement;
using RevCord.Util;
using RevCord.DataContracts.ReportEntities;
using System.Text.RegularExpressions;
using RevCord.DataContracts.ScheduleReportEntities;
using RevCord.DataContracts.RevcellEntities;
using RevCord.DataContracts.RoleManagement;
using RevCord.DataContracts.IQ3InspectionEntities;
using Inspection = RevCord.DataContracts.IQ3InspectionEntities.Inspection;
using RevCord.DataContracts.CustomerDBEntities;
using RevCord.DataContracts.MGODataEntities;
using RevCord.DataContracts.IQ3ConditionalLogic;

namespace RevCord.DataAccess
{
    public static class ORMapper
    {
        #region Survey

        public static Survey MapSurvey(SqlDataReader dr)
        {
            Survey survey = null;

            if (dr.<PERSON>)
            {
                dr.Read();

                survey = new Survey();
                survey.Id = (int)dr["Id"];
                survey.RevSyncSurveyId = (int)dr["RevSyncServerID"];
                survey.Name = Convert.ToString(dr["Title"]);
                survey.Description = Convert.ToString(dr["Description"]);
                survey.HasSections = Convert.ToBoolean(dr["HasSections"]);
                survey.StartDate = Convert.ToDateTime(dr["StartDate"]);
                survey.EndDate = Convert.ToDateTime(dr["EndDate"]);
                survey.ActiveAfterEndDate = Convert.ToBoolean(dr["ActiveAfterEndDate"]);
                survey.IsPublished = Convert.ToBoolean(dr["IsPublished"]);
                //survey.PublishedDate = Convert.ToDateTime(dr["PublishedDate"]);
                survey.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                survey.CreatedBy = Convert.ToInt32(dr["CreatedBy"]);
                //survey.ModifiedBy = Convert.ToInt32(dr["ModifiedBy"]);
                survey.ModifiedDate = Convert.ToDateTime(dr["ModifiedDate"]);
                survey.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                survey.NoOfQuestions = Convert.ToInt32(dr["NoOfQuestions"]);
                survey.NoOfSections = Convert.ToInt32(dr["NoOfSections"]);
                survey.Score = dr["Score"] == DBNull.Value ? 0 : Convert.ToSingle(dr["Score"]);
            }
            return survey;
        }


        public static List<Survey> MapSurveys(SqlDataReader dr)
        {
            List<Survey> surveys = new List<Survey>();
            Survey survey = null;
            while (dr.Read())
            {
                survey = new Survey();
                survey.Id = (int)dr["Id"];
                survey.Name = Convert.ToString(dr["Title"]);
                survey.Description = Convert.ToString(dr["Description"]);
                survey.HasSections = Convert.ToBoolean(dr["HasSections"]);
                survey.StartDate = Convert.ToDateTime(dr["StartDate"]);
                survey.EndDate = Convert.ToDateTime(dr["EndDate"]);
                survey.ActiveAfterEndDate = Convert.ToBoolean(dr["ActiveAfterEndDate"]);
                survey.IsPublished = Convert.ToBoolean(dr["IsPublished"]);
                //survey.PublishedDate = Convert.ToDateTime(dr["PublishedDate"]);
                survey.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                survey.CreatedBy = Convert.ToInt32(dr["CreatedBy"]);
                //survey.ModifiedBy = Convert.ToInt32(dr["ModifiedBy"]);
                survey.ModifiedDate = Convert.ToDateTime(dr["ModifiedDate"]);
                survey.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                survey.NoOfQuestions = Convert.ToInt32(dr["NoOfQuestions"]);
                survey.NoOfSections = Convert.ToInt32(dr["NoOfSections"]);
                survey.Score = dr["Score"] == DBNull.Value ? 0 : Convert.ToSingle(dr["Score"]);
                survey.RevSyncSurveyId = (int)dr["RevSyncServerID"];
                survey.IsSyncedFromClient = dr.IsDBNull(dr.GetOrdinal("IsSyncedFromClient")) ? 0 : Convert.ToInt32(dr["IsSyncedFromClient"]);

                surveys.Add(survey);
            }
            return surveys;
        }

        public static List<Survey> MapSurveys(SqlDataReader dr, Recorder recorder)
        {
            List<Survey> surveys = new List<Survey>();
            Survey survey = null;
            while (dr.Read())
            {
                survey = new Survey();
                survey.Id = (int)dr["Id"];
                survey.RevSyncSurveyId = (int)dr["RevSyncServerID"];
                survey.Name = Convert.ToString(dr["Title"]);
                survey.Description = Convert.ToString(dr["Description"]);
                survey.HasSections = Convert.ToBoolean(dr["HasSections"]);
                survey.StartDate = Convert.ToDateTime(dr["StartDate"]);
                survey.EndDate = Convert.ToDateTime(dr["EndDate"]);
                survey.ActiveAfterEndDate = Convert.ToBoolean(dr["ActiveAfterEndDate"]);
                survey.IsPublished = Convert.ToBoolean(dr["IsPublished"]);
                //survey.PublishedDate = Convert.ToDateTime(dr["PublishedDate"]);
                survey.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                survey.CreatedBy = Convert.ToInt32(dr["CreatedBy"]);
                //survey.ModifiedBy = Convert.ToInt32(dr["ModifiedBy"]);
                survey.ModifiedDate = Convert.ToDateTime(dr["ModifiedDate"]);
                survey.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                survey.NoOfQuestions = Convert.ToInt32(dr["NoOfQuestions"]);
                survey.NoOfSections = Convert.ToInt32(dr["NoOfSections"]);
                survey.Score = dr["Score"] == DBNull.Value ? 0 : Convert.ToSingle(dr["Score"]);
                survey.RecId = recorder.Id;
                survey.RecName = recorder.Name;

                surveys.Add(survey);
            }
            return surveys;
        }

        public static List<SurveySection> MapSections(SqlDataReader dr)
        {
            List<SurveySection> surveySections = new List<SurveySection>();
            SurveySection surveySection = null;
            while (dr.Read())
            {
                //if (surveySections == null) surveySections = new List<SurveySection>();
                surveySection = new SurveySection();
                surveySection.Id = (int)dr["Id"];
                surveySection.RevSyncId = Convert.ToInt32(dr["RevSyncServerID"] == DBNull.Value ? 0 : dr["RevSyncServerID"]);//Convert.ToInt32(dr["RevSyncServerID"]);
                surveySection.Title = Convert.ToString(dr["Title"]);
                surveySection.SurveyId = (int)dr["SurveyId"];
                surveySection.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                surveySection.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                surveySection.IsDefaultSection = Convert.ToBoolean(dr["IsDefaultSection"]);
                surveySection.NumberOfQuestion = Convert.ToInt32(dr["NoOfQuestions"]);

                surveySections.Add(surveySection);
            }
            return surveySections;
        }

        public static List<Question> MapQuestions(SqlDataReader dr)
        {
            List<Question> questions = new List<Question>();
            Question question = null;
            while (dr.Read())
            {
                //if (questions == null) questions = new List<Question>();
                question = new Question();
                question.Id = (long)dr["Id"];
                question.RevSyncQuestionId = Convert.ToInt32(dr["RevSyncServerID"] == DBNull.Value ? 0 : dr["RevSyncServerID"]);//Convert.ToInt32(dr["RevSyncServerID"]);
                question.Statement = Convert.ToString(dr["Title"]);
                question.SurveyId = (int)dr["SurveyId"];
                question.SectionId = (int)(dr["SectionId"]);
                question.TypeId = (short)dr["QuestionTypeId"];
                question.Type = (ControlType)Enum.Parse(typeof(ControlType), Convert.ToString(dr["QuestionTypeId"]));
                question.Ordering = (int)dr["Ordering"];
                question.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                question.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                questions.Add(question);
            }
            return questions;

        }

        public static List<Option> MapOptions(SqlDataReader dr)
        {
            List<Option> options = new List<Option>();
            Option option = null;
            while (dr.Read())
            {
                //if (options == null) options = new List<Option>();
                option = new Option();
                option.Id = (long)dr["Id"];
                option.Title = Convert.ToString(dr["Title"]);
                option.QuestionId = (long)dr["QuestionId"];
                option.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                option.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                option.Score = Convert.ToSingle(dr["Score"]);
                option.Ordering = (int)(dr["Ordering"]);

                options.Add(option);
            }
            return options;
        }

        #endregion

        #region Call Evaluation

        public static CallEvaluation MapCallEvaluation(SqlDataReader dr)
        {
            CallEvaluation callEvaluation = null;// new CallEvaluation();
            if (dr.HasRows)
            {
                dr.Read();

                callEvaluation = new CallEvaluation();
                callEvaluation.Id = Convert.ToInt64(dr["Id"]);
                callEvaluation.RevSyncId = Convert.ToInt64(dr["RevSyncServerId"]);
                callEvaluation.Code = Convert.ToInt64(dr["Id"]);
                callEvaluation.StatusId = Convert.ToInt16(dr["StatusId"]);
                callEvaluation.SupervisorComments = Convert.ToString(dr["ScorerComments"]);
                callEvaluation.IsShared = Convert.ToBoolean(dr["IsShared"]);
                callEvaluation.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                callEvaluation.IsAgentAssociated = Convert.ToBoolean(dr["IsAgentAssociated"]);
                callEvaluation.AssociatedAgent = dr["AssociatedAgent"] == DBNull.Value ? "" : Convert.ToString(dr["AssociatedAgent"]);
                callEvaluation.AssociatedAgentCode = dr["AssociatedAgentCode"] == DBNull.Value ? 0 : Convert.ToInt32(dr["AssociatedAgentCode"]);
                callEvaluation.AssociatedAgentEmail = dr["AssociatedAgentEmail"] == DBNull.Value ? "" : Convert.ToString(dr["AssociatedAgentEmail"]);
                callEvaluation.EvaluatedScore = Convert.ToSingle(dr["EvaluatedScore"]);

                callEvaluation.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                callEvaluation.ModifiedDate = dr["ModifiedDate"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["ModifiedDate"]);
                callEvaluation.CompletedDate = dr["CompletedDate"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["CompletedDate"]);
                //callEvaluation.CreatedBy=;
                //callEvaluation.ModifiedBy=;
            }
            return callEvaluation;
        }

        public static List<CallEvaluation> MapCallEvaluations(SqlDataReader dr)
        {
            List<CallEvaluation> callEvaluations = new List<CallEvaluation>();
            CallEvaluation callEvaluation = null;
            //if (callEvaluationDTOs == null) callEvaluationDTOs = new List<CallEvaluationDTO>();

            while (dr.Read())
            {
                callEvaluation = new CallEvaluation();

                callEvaluation = new CallEvaluation();
                callEvaluation.Id = Convert.ToInt64(dr["Id"]);
                callEvaluation.Code = Convert.ToInt64(dr["Id"]);
                callEvaluation.UserId = Convert.ToInt32(dr["UserId"]);
                callEvaluation.AppUserId = Convert.ToInt32(dr["AppUserId"]);
                callEvaluation.SurveyId = Convert.ToInt32(dr["SurveyId"]);
                callEvaluation.StatusId = Convert.ToInt16(dr["StatusId"]);
                callEvaluation.SupervisorComments = Convert.ToString(dr["ScorerComments"]);
                callEvaluation.IsShared = Convert.ToBoolean(dr["IsShared"]);
                callEvaluation.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                callEvaluation.IsAgentAssociated = Convert.ToBoolean(dr["IsAgentAssociated"]);
                callEvaluation.AssociatedAgent = dr["AssociatedAgent"] == DBNull.Value ? "" : Convert.ToString(dr["AssociatedAgent"]);
                callEvaluation.AssociatedAgentCode = dr["AssociatedAgentCode"] == DBNull.Value ? 0 : Convert.ToInt32(dr["AssociatedAgentCode"]);
                callEvaluation.AssociatedAgentEmail = dr["AssociatedAgentEmail"] == DBNull.Value ? "" : Convert.ToString(dr["AssociatedAgentEmail"]);
                callEvaluation.EvaluatedScore = Convert.ToSingle(dr["EvaluatedScore"]);

                callEvaluation.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                callEvaluation.ModifiedDate = dr["ModifiedDate"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["ModifiedDate"]);
                callEvaluation.CompletedDate = dr["CompletedDate"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["CompletedDate"]);
                //callEvaluation.CreatedBy=;
                //callEvaluation.ModifiedBy=;


                callEvaluations.Add(callEvaluation);
            }
            return callEvaluations;
        }

        //Arivu
        public static List<CallEvaluation> MapCallEvaluationsForPublishedForm(SqlDataReader dr)
        {
            List<CallEvaluation> callEvaluations = new List<CallEvaluation>();
            CallEvaluation callEvaluation = null;

            while (dr.Read())
            {
                callEvaluation = new CallEvaluation();

                callEvaluation.Id = Convert.ToInt32(dr["id"]);
                callEvaluation.StatusId = Convert.ToInt16(dr["StatusId"]);


                callEvaluations.Add(callEvaluation);
            }
            return callEvaluations;
        }
        public static List<Answer> MapCallEvauationAnswerMaster(SqlDataReader dr)
        {
            List<Answer> answers = new List<Answer>();
            Answer answer = null;
            while (dr.Read())
            {
                answer = new Answer();
                answer.CallEvaluationId = Convert.ToInt32(dr["CallEvaluationId"]);
                answer.QuestionId = Convert.ToInt32(dr["QuestionId"]);
                answer.AnswerValue = Convert.ToString(dr["AnswerText"]);

                answers.Add(answer);
            }
            return answers;
        }

        public static List<Option> MapCallEvauationAnswerChild(SqlDataReader dr)
        {
            List<Option> options = new List<Option>();
            Option option = null;
            while (dr.Read())
            {
                option = new Option();
                option.Id = Convert.ToInt32(dr["QuestionOptionId"]);
                option.CallEvaluationId = (long)dr["CallEvaluationId"];
                //option.CallEvaluationId = !DBRecordExtensions.HasColumn(dr, "CallEvaluationId") ? default(long) : (long)dr["CallEvaluationId"];
                option.QuestionId = Convert.ToInt32(dr["QuestionId"]);
                option.Score = Convert.ToSingle(dr["Score"]);
                option.Ordering = (int)(dr["Ordering"]);

                options.Add(option);
            }
            return options;
        }

        #endregion

        #region Call Evaluation Feedback
        public static List<CallEvaluationFeedback> MapCallEvaluationFeedbacks(SqlDataReader dr)
        {
            List<CallEvaluationFeedback> callEvaluationFeedbacks = new List<CallEvaluationFeedback>();
            CallEvaluationFeedback callEvaluationFeedback = null;
            while (dr.Read())
            {
                callEvaluationFeedback.CallEvaluationId = Convert.ToInt32(dr["CallEvaluationId"]);
                callEvaluationFeedback.RespondantId = Convert.ToInt32(dr["RespondantId"]);
                callEvaluationFeedback.RespondantComments = Convert.ToString(dr["RespondantComments"]);
                callEvaluationFeedback.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                callEvaluationFeedback.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);

                callEvaluationFeedbacks.Add(callEvaluationFeedback);
            }
            return callEvaluationFeedbacks;
        }
        #endregion

        #region User Evaluation
        public static UserEvaluation MapUserEvaluation(SqlDataReader dr)
        {
            UserEvaluation userEvaluation = new UserEvaluation(); //null;// new CallEvaluation();
            if (dr.HasRows)
            {
                dr.Read();

                userEvaluation = new UserEvaluation();
                userEvaluation.Id = Convert.ToInt64(dr["Id"]);
                userEvaluation.Code = Convert.ToInt64(dr["Id"]);
                userEvaluation.StatusId = Convert.ToInt16(dr["StatusId"]);
                userEvaluation.SupervisorComments = Convert.ToString(dr["ScorerComments"]);
                userEvaluation.IsShared = Convert.ToBoolean(dr["IsShared"]);
                userEvaluation.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                userEvaluation.EvaluatedScore = Convert.ToSingle(dr["EvaluatedScore"]);

                userEvaluation.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                userEvaluation.ModifiedDate = dr["ModifiedDate"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["ModifiedDate"]);
                userEvaluation.CompletedDate = dr["CompletedDate"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["CompletedDate"]);
                //callEvaluation.CreatedBy=;
                //callEvaluation.ModifiedBy=;
            }
            return userEvaluation;
        }

        public static List<UserEvaluation> MapUserEvaluations(SqlDataReader dr)
        {
            List<UserEvaluation> userEvals = new List<UserEvaluation>();
            UserEvaluation userEval = null;
            //if (callEvaluationDTOs == null) callEvaluationDTOs = new List<CallEvaluationDTO>();

            while (dr.Read())
            {

                userEval = new UserEvaluation();

                userEval.Id = Convert.ToInt64(dr["Id"]);
                userEval.Code = Convert.ToInt64(dr["Id"]);
                userEval.SurveyId = dr["SurveyId"] != DBNull.Value ? Convert.ToInt32(dr["SurveyId"]) : (int?)null;
                userEval.AppUserId = Convert.ToInt32(dr["AppUserId"]);
                userEval.EvaluatorId = Convert.ToInt32(dr["EvaluatorId"]);
                userEval.StatusId = Convert.ToInt16(dr["StatusId"]);
                userEval.SupervisorComments = Convert.ToString(dr["ScorerComments"]);
                userEval.EvaluatedScore = dr["EvaluatedScore"] == DBNull.Value ? default(float) : Convert.ToSingle(dr["EvaluatedScore"]);
                userEval.IsShared = Convert.ToBoolean(dr["IsShared"]);
                userEval.CompletedDate = dr["CompletedDate"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["CompletedDate"]);
                userEval.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                //userEval.CreatedBy=;
                userEval.ModifiedDate = dr["ModifiedDate"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["ModifiedDate"]);
                //userEval.ModifiedBy=;
                //userEval.IsDeleted
                if (dr["SurveyId"] != DBNull.Value)
                {
                    userEval.Survey = new Survey
                    {
                        Id = Convert.ToInt32(dr["SurveyId"]),
                        Name = Convert.ToString(dr["SurveyTitle"])
                    };
                }
                userEval.Status = (EvaluationStatus)Enum.Parse(typeof(EvaluationStatus), Convert.ToString(dr["StatusId"]));

                userEval.QADTO = new QADTO();
                userEval.QADTO.EvaluatedScore = dr["EvaluatedScore"] == DBNull.Value ? 0 : Convert.ToSingle(dr["EvaluatedScore"]);
                userEval.QADTO.TotalScore = dr["TotalScore"] == DBNull.Value ? 0 : Convert.ToSingle(dr["TotalScore"]);
                userEval.QADTO.TotalQuestions = Convert.ToInt32(dr["TotalQuestions"]);
                userEval.QADTO.AnsweredQuestions = Convert.ToInt32(dr["EvaluatedQuestions"]);

                userEval.Agent = new User
                {
                    UserNum = Convert.ToInt32(dr["UserNum"]),
                    UserName = Convert.ToString(dr["UserName"])   /*JoinBeginDate*/
                };
                userEval.Supervisor = new User
                {
                    UserNum = Convert.ToInt32(dr["EvaluatorId"]),
                    UserName = Convert.ToString(dr["Evaluator"]) /*JoinBeginDate*/
                };

                userEvals.Add(userEval);
            }
            return userEvals;
        }

        #endregion

        #region Call Evaluation DTO

        public static List<CallEvaluationDTO> CallEvaluationDTOs(SqlDataReader dr)
        {
            List<CallEvaluationDTO> callEvaluationDTOs = new List<CallEvaluationDTO>();
            CallEvaluationDTO callEvaluationDTO = null;
            //if (callEvaluationDTOs == null) callEvaluationDTOs = new List<CallEvaluationDTO>();

            while (dr.Read())
            {

                callEvaluationDTO = new CallEvaluationDTO();
                callEvaluationDTO.RowNo = Convert.ToInt32(dr["RowNo"]);
                callEvaluationDTO.CallEvaluationId = Convert.ToInt32(dr["Id"]);
                callEvaluationDTO.CallEvaluationCode = Convert.ToInt32(dr["Id"]);
                callEvaluationDTO.CallEvaluationRevSyncId = Convert.ToInt32(dr["RevSyncServerID"]);
                callEvaluationDTO.SurveyId = dr["SurveyId"] != DBNull.Value ? Convert.ToInt32(dr["SurveyId"]) : (int?)null;
                callEvaluationDTO.SurveyName = Convert.ToString(dr["SurveyTitle"]);
                callEvaluationDTO.StatusId = Convert.ToInt16(dr["StatusId"]);
                //callEvaluationDTO.Status = (EvaluationStatus)Enum.Parse(typeof(EvaluationStatus), Convert.ToString(dr["StatusId"]));
                callEvaluationDTO.AppUserId = Convert.ToInt32(dr["AppUserId"]);
                callEvaluationDTO.IsAgentAssociated = Convert.ToBoolean(dr["IsAgentAssociated"]);
                callEvaluationDTO.AssociatedAgent = dr["AssociatedAgent"] == DBNull.Value ? "" : Convert.ToString(dr["AssociatedAgent"]);
                callEvaluationDTO.AssociatedAgentCode = dr["AssociatedAgentCode"] == DBNull.Value ? 0 : Convert.ToInt32(dr["AssociatedAgentCode"]);
                callEvaluationDTO.AssociatedAgentEmail = dr["AssociatedAgentEmail"] == DBNull.Value ? "" : Convert.ToString(dr["AssociatedAgentEmail"]);
                callEvaluationDTO.Feedback = dr["Feedback"] == DBNull.Value ? "" : Convert.ToString(dr["Feedback"]);
                callEvaluationDTO.EvaluatedScore = dr["EvaluatedScore"] == DBNull.Value ? default(float) : Convert.ToSingle(dr["EvaluatedScore"]);
                callEvaluationDTO.TotalScore = dr["TotalScore"] == DBNull.Value ? 0 : Convert.ToSingle(dr["TotalScore"]);
                callEvaluationDTO.TotalQuestions = Convert.ToInt32(dr["TotalQuestions"]);
                callEvaluationDTO.AnsweredQuestions = Convert.ToInt32(dr["EvaluatedQuestions"]);
                callEvaluationDTO.IsShared = Convert.ToBoolean(dr["IsShared"]);
                callEvaluationDTO.SharedWith = Convert.ToString(dr["EvalSharedWith"]);

                //callEvaluationDTO.LocalCallId = Convert.ToInt32(dr["LocalCallId"]);
                callEvaluationDTO.CallID = Convert.ToString(dr["CallID"]);
                callEvaluationDTO.GroupNum = Convert.ToInt32(dr["GroupNum"]);
                callEvaluationDTO.GroupName = Convert.ToString(dr["GroupName"]);
                callEvaluationDTO.UserNum = Convert.ToInt32(dr["UserNum"]);
                callEvaluationDTO.UserName = Convert.ToString(dr["UserName"]);
                callEvaluationDTO.UserEmail = Convert.ToString(dr["UserEmail"]);
                callEvaluationDTO.Evaluator = Convert.ToString(dr["Evaluator"]);
                callEvaluationDTO.EvaluatorEmail = Convert.ToString(dr["EvaluatorEmail"]);
                callEvaluationDTO.EvaluatorComments = Convert.ToString(dr["ScorerComments"]);
                callEvaluationDTO.Channel = Convert.ToString(dr["Ext"]);
                callEvaluationDTO.ExtName = Convert.ToString(dr["ExtName"]);
                callEvaluationDTO.FileName = Convert.ToString(dr["FileName"]);
                //callEvaluationDTO.CallType = Convert.ToInt32(dr["CallType"]);
                callEvaluationDTO.StartTime = Convert.ToString(dr["StartTime"]);
                callEvaluationDTO.Duration = Convert.ToInt32(dr["Duration"]);
                callEvaluationDTO.IsPictureEvent = !DBRecordExtensions.HasColumn(dr, "IsPictureEvent") ? false : Convert.ToBoolean(dr["IsPictureEvent"]);
                callEvaluationDTO.IsVirtualInspection = !DBRecordExtensions.HasColumn(dr, "IsVirtualInspection") ? false : Convert.ToBoolean(dr["IsVirtualInspection"]);
                callEvaluationDTO.IsRevView = !DBRecordExtensions.HasColumn(dr, "IsRevView") ? false : Convert.ToBoolean(dr["IsRevView"]);
                callEvaluationDTO.RevViewFileName = !DBRecordExtensions.HasColumn(dr, "RevViewFileName") ? string.Empty : Convert.ToString(dr["RevViewFileName"]);
                callEvaluationDTO.RevViewStartTime = !DBRecordExtensions.HasColumn(dr, "RevViewStartTime") ? string.Empty : Convert.ToString(dr["RevViewStartTime"]);
                callEvaluationDTO.RevViewPhoneNumber = !DBRecordExtensions.HasColumn(dr, "RevViewPhoneNumber") ? string.Empty : Convert.ToString(dr["RevViewPhoneNumber"]);
                callEvaluationDTO.RevViewAgentName = !DBRecordExtensions.HasColumn(dr, "RevViewAgentName") ? string.Empty : Convert.ToString(dr["RevViewAgentName"]);
                callEvaluationDTO.EventName = !DBRecordExtensions.HasColumn(dr, "Interviewee") ? string.Empty : Convert.ToString(dr["Interviewee"]);
                callEvaluationDTO.ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]);
                callEvaluationDTO.CallType = Convert.ToInt32(dr["CallType"]);
                callEvaluationDTO.EvaluationType = Convert.ToInt32(dr["EvaluationType"]);

                if (callEvaluationDTO.CallType != 7)
                {
                    callEvaluationDTO.CallType_inq = callEvaluationDTO.CallType;
                }
                else
                {
                    if (Regex.IsMatch(callEvaluationDTO.FileName, "mp4", RegexOptions.IgnoreCase) || Regex.IsMatch(callEvaluationDTO.FileName, "mov", RegexOptions.IgnoreCase) || Regex.IsMatch(callEvaluationDTO.FileName, "m4v", RegexOptions.IgnoreCase))
                    {
                        callEvaluationDTO.CallType_inq = 8;
                    }
                    else if (Regex.IsMatch(callEvaluationDTO.FileName, "zip", RegexOptions.IgnoreCase))
                    {
                        callEvaluationDTO.CallType_inq = 12;
                    }
                    else
                    {
                        callEvaluationDTO.CallType_inq = 7;
                    }
                }

                callEvaluationDTO.IsRevCell = Convert.ToBoolean(dr["IsRevCell"]);

                callEvaluationDTO.RecId = Convert.ToInt32(dr["RecID"]);
                callEvaluationDTO.RecName = Convert.ToString(dr["RecName"]);

                if (dr.FieldExists("BookMarkXML"))
                {
                    callEvaluationDTO.BookmarkXML = Convert.ToString(dr["BookMarkXML"]);
                }

                callEvaluationDTO.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                callEvaluationDTO.ModifiedDate = dr["ModifiedDate"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["ModifiedDate"]);
                callEvaluationDTO.CompletedDate = dr["CompletedDate"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["CompletedDate"]);
                //callEvaluation.CreatedBy=;
                //callEvaluation.ModifiedBy=;

                //callEvaluationDTO.UserId = Convert.ToInt32(dr["UserId"]);


                callEvaluationDTOs.Add(callEvaluationDTO);
            }
            return callEvaluationDTOs;
        }
        public static List<CallEvaluationDTO> CallEvaluationLiteDTOs(SqlDataReader dr)
        {
            List<CallEvaluationDTO> callEvaluationDTOs = new List<CallEvaluationDTO>();
            CallEvaluationDTO callEvaluationDTO = null;

            while (dr.Read())
            {

                callEvaluationDTO = new CallEvaluationDTO();
                callEvaluationDTO.RowNo = Convert.ToInt32(dr["RowNo"]);
                callEvaluationDTO.CallEvaluationId = Convert.ToInt32(dr["Id"]);
                callEvaluationDTO.CallEvaluationCode = Convert.ToInt32(dr["Id"]);
                callEvaluationDTO.SurveyId = dr["SurveyId"] != DBNull.Value ? Convert.ToInt32(dr["SurveyId"]) : (int?)null;
                callEvaluationDTO.SurveyName = Convert.ToString(dr["SurveyTitle"]);
                callEvaluationDTO.StatusId = Convert.ToInt16(dr["StatusId"]);
                callEvaluationDTO.AppUserId = Convert.ToInt32(dr["AppUserId"]);
                callEvaluationDTO.IsAgentAssociated = Convert.ToBoolean(dr["IsAgentAssociated"]);
                callEvaluationDTO.AssociatedAgent = dr["AssociatedAgent"] == DBNull.Value ? "" : Convert.ToString(dr["AssociatedAgent"]);
                callEvaluationDTO.AssociatedAgentCode = dr["AssociatedAgentCode"] == DBNull.Value ? 0 : Convert.ToInt32(dr["AssociatedAgentCode"]);
                callEvaluationDTO.AssociatedAgentEmail = dr["AssociatedAgentEmail"] == DBNull.Value ? "" : Convert.ToString(dr["AssociatedAgentEmail"]);
                callEvaluationDTO.Feedback = dr["Feedback"] == DBNull.Value ? "" : Convert.ToString(dr["Feedback"]);
                callEvaluationDTO.EvaluatedScore = dr["EvaluatedScore"] == DBNull.Value ? default(float) : Convert.ToSingle(dr["EvaluatedScore"]);
                callEvaluationDTO.TotalScore = dr["TotalScore"] == DBNull.Value ? 0 : Convert.ToSingle(dr["TotalScore"]);
                callEvaluationDTO.TotalQuestions = Convert.ToInt32(dr["TotalQuestions"]);
                callEvaluationDTO.AnsweredQuestions = Convert.ToInt32(dr["EvaluatedQuestions"]);
                callEvaluationDTO.IsShared = Convert.ToBoolean(dr["IsShared"]);

                callEvaluationDTO.CallID = Convert.ToString(dr["CallID"]);
                callEvaluationDTO.GroupNum = Convert.ToInt32(dr["GroupNum"]);
                callEvaluationDTO.GroupName = Convert.ToString(dr["GroupName"]);
                callEvaluationDTO.UserNum = Convert.ToInt32(dr["UserNum"]);
                callEvaluationDTO.UserName = Convert.ToString(dr["UserName"]);
                callEvaluationDTO.UserEmail = Convert.ToString(dr["UserEmail"]);
                callEvaluationDTO.Evaluator = Convert.ToString(dr["Evaluator"]);
                callEvaluationDTO.EvaluatorEmail = Convert.ToString(dr["EvaluatorEmail"]);
                callEvaluationDTO.EvaluatorComments = Convert.ToString(dr["ScorerComments"]);
                callEvaluationDTO.Channel = Convert.ToString(dr["Ext"]);
                callEvaluationDTO.ExtName = Convert.ToString(dr["ExtName"]);
                callEvaluationDTO.FileName = Convert.ToString(dr["FileName"]);
                callEvaluationDTO.StartTime = Convert.ToString(dr["StartTime"]);
                callEvaluationDTO.Duration = Convert.ToInt32(dr["Duration"]);
                callEvaluationDTO.IsPictureEvent = !DBRecordExtensions.HasColumn(dr, "IsPictureEvent") ? false : Convert.ToBoolean(dr["IsPictureEvent"]);
                callEvaluationDTO.IsVirtualInspection = !DBRecordExtensions.HasColumn(dr, "IsVirtualInspection") ? false : Convert.ToBoolean(dr["IsVirtualInspection"]);
                callEvaluationDTO.IsRevView = !DBRecordExtensions.HasColumn(dr, "IsRevView") ? false : Convert.ToBoolean(dr["IsRevView"]);
                callEvaluationDTO.RevViewFileName = !DBRecordExtensions.HasColumn(dr, "RevViewFileName") ? string.Empty : Convert.ToString(dr["RevViewFileName"]);
                callEvaluationDTO.RevViewStartTime = !DBRecordExtensions.HasColumn(dr, "RevViewStartTime") ? string.Empty : Convert.ToString(dr["RevViewStartTime"]);
                callEvaluationDTO.RevViewPhoneNumber = !DBRecordExtensions.HasColumn(dr, "RevViewPhoneNumber") ? string.Empty : Convert.ToString(dr["RevViewPhoneNumber"]);
                callEvaluationDTO.RevViewAgentName = !DBRecordExtensions.HasColumn(dr, "RevViewAgentName") ? string.Empty : Convert.ToString(dr["RevViewAgentName"]);
                callEvaluationDTO.EventName = !DBRecordExtensions.HasColumn(dr, "Interviewee") ? string.Empty : Convert.ToString(dr["Interviewee"]);
                callEvaluationDTO.ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]);
                callEvaluationDTO.CallType = Convert.ToInt32(dr["CallType"]);
                callEvaluationDTO.IsRevCell = Convert.ToBoolean(dr["IsRevCell"]);

                if (dr.FieldExists("BookMarkXML"))
                {
                    callEvaluationDTO.BookmarkXML = Convert.ToString(dr["BookMarkXML"]);
                }

                if (callEvaluationDTO.CallType != 7)
                {
                    callEvaluationDTO.CallType_inq = callEvaluationDTO.CallType;
                }
                else
                {
                    if (Regex.IsMatch(callEvaluationDTO.FileName, "mp4", RegexOptions.IgnoreCase) || Regex.IsMatch(callEvaluationDTO.FileName, "mov", RegexOptions.IgnoreCase) || Regex.IsMatch(callEvaluationDTO.FileName, "m4v", RegexOptions.IgnoreCase))
                    {
                        callEvaluationDTO.CallType_inq = 8;
                    }
                    else if (Regex.IsMatch(callEvaluationDTO.FileName, "zip", RegexOptions.IgnoreCase))
                    {
                        callEvaluationDTO.CallType_inq = 12;
                    }
                    else
                    {
                        callEvaluationDTO.CallType_inq = 7;
                    }
                }

                callEvaluationDTO.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                callEvaluationDTO.ModifiedDate = dr["ModifiedDate"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["ModifiedDate"]);
                callEvaluationDTO.CompletedDate = dr["CompletedDate"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["CompletedDate"]);

                callEvaluationDTOs.Add(callEvaluationDTO);
            }
            return callEvaluationDTOs;
        }
        public static List<CallEvaluationDTO> CallEvaluationDTOs(SqlDataReader dr, Recorder recorder)
        {
            List<CallEvaluationDTO> callEvaluationDTOs = new List<CallEvaluationDTO>();
            CallEvaluationDTO callEvaluationDTO = null;
            //if (callEvaluationDTOs == null) callEvaluationDTOs = new List<CallEvaluationDTO>();

            while (dr.Read())
            {

                callEvaluationDTO = new CallEvaluationDTO();
                callEvaluationDTO.RowNo = Convert.ToInt32(dr["RowNo"]);
                callEvaluationDTO.CallEvaluationId = Convert.ToInt32(dr["Id"]);
                callEvaluationDTO.CallEvaluationCode = Convert.ToInt32(dr["Id"]);
                callEvaluationDTO.SurveyId = dr["SurveyId"] != DBNull.Value ? Convert.ToInt32(dr["SurveyId"]) : (int?)null;
                callEvaluationDTO.SurveyName = Convert.ToString(dr["SurveyTitle"]);
                callEvaluationDTO.StatusId = Convert.ToInt16(dr["StatusId"]);
                //callEvaluationDTO.Status = (EvaluationStatus)Enum.Parse(typeof(EvaluationStatus), Convert.ToString(dr["StatusId"]));
                callEvaluationDTO.AppUserId = Convert.ToInt32(dr["AppUserId"]);
                callEvaluationDTO.IsAgentAssociated = Convert.ToBoolean(dr["IsAgentAssociated"]);
                callEvaluationDTO.AssociatedAgent = dr["AssociatedAgent"] == DBNull.Value ? "" : Convert.ToString(dr["AssociatedAgent"]);
                callEvaluationDTO.AssociatedAgentCode = dr["AssociatedAgentCode"] == DBNull.Value ? 0 : Convert.ToInt32(dr["AssociatedAgentCode"]);
                callEvaluationDTO.AssociatedAgentEmail = dr["AssociatedAgentEmail"] == DBNull.Value ? "" : Convert.ToString(dr["AssociatedAgentEmail"]);
                callEvaluationDTO.Feedback = dr["Feedback"] == DBNull.Value ? "" : Convert.ToString(dr["Feedback"]);
                callEvaluationDTO.EvaluatedScore = dr["EvaluatedScore"] == DBNull.Value ? default(float) : Convert.ToSingle(dr["EvaluatedScore"]);
                callEvaluationDTO.TotalScore = dr["TotalScore"] == DBNull.Value ? 0 : Convert.ToSingle(dr["TotalScore"]);
                callEvaluationDTO.TotalQuestions = Convert.ToInt32(dr["TotalQuestions"]);
                callEvaluationDTO.AnsweredQuestions = Convert.ToInt32(dr["EvaluatedQuestions"]);
                callEvaluationDTO.IsShared = Convert.ToBoolean(dr["IsShared"]);
                callEvaluationDTO.SharedWith = Convert.ToString(dr["EvalSharedWith"]);

                //callEvaluationDTO.LocalCallId = Convert.ToInt32(dr["LocalCallId"]);
                callEvaluationDTO.CallID = Convert.ToString(dr["CallID"]);
                callEvaluationDTO.GroupNum = Convert.ToInt32(dr["GroupNum"]);
                callEvaluationDTO.GroupName = Convert.ToString(dr["GroupName"]);
                callEvaluationDTO.UserNum = Convert.ToInt32(dr["UserNum"]);
                callEvaluationDTO.UserName = Convert.ToString(dr["UserName"]);
                callEvaluationDTO.UserEmail = Convert.ToString(dr["UserEmail"]);
                callEvaluationDTO.Evaluator = Convert.ToString(dr["Evaluator"]);
                callEvaluationDTO.EvaluatorEmail = Convert.ToString(dr["EvaluatorEmail"]);
                callEvaluationDTO.EvaluatorComments = Convert.ToString(dr["ScorerComments"]);
                callEvaluationDTO.Channel = Convert.ToString(dr["Ext"]);
                callEvaluationDTO.ExtName = Convert.ToString(dr["ExtName"]);
                callEvaluationDTO.FileName = Convert.ToString(dr["FileName"]);
                //callEvaluationDTO.CallType = Convert.ToInt32(dr["CallType"]);
                callEvaluationDTO.StartTime = Convert.ToString(dr["StartTime"]);
                callEvaluationDTO.Duration = Convert.ToInt32(dr["Duration"]);
                callEvaluationDTO.IsPictureEvent = !DBRecordExtensions.HasColumn(dr, "IsPictureEvent") ? false : Convert.ToBoolean(dr["IsPictureEvent"]);
                callEvaluationDTO.IsVirtualInspection = !DBRecordExtensions.HasColumn(dr, "IsVirtualInspection") ? false : Convert.ToBoolean(dr["IsVirtualInspection"]);
                callEvaluationDTO.IsRevView = !DBRecordExtensions.HasColumn(dr, "IsRevView") ? false : Convert.ToBoolean(dr["IsRevView"]);
                callEvaluationDTO.RevViewFileName = !DBRecordExtensions.HasColumn(dr, "RevViewFileName") ? string.Empty : Convert.ToString(dr["RevViewFileName"]);
                callEvaluationDTO.RevViewStartTime = !DBRecordExtensions.HasColumn(dr, "RevViewStartTime") ? string.Empty : Convert.ToString(dr["RevViewStartTime"]);
                callEvaluationDTO.RevViewPhoneNumber = !DBRecordExtensions.HasColumn(dr, "RevViewPhoneNumber") ? string.Empty : Convert.ToString(dr["RevViewPhoneNumber"]);
                callEvaluationDTO.RevViewAgentName = !DBRecordExtensions.HasColumn(dr, "RevViewAgentName") ? string.Empty : Convert.ToString(dr["RevViewAgentName"]);
                callEvaluationDTO.EventName = !DBRecordExtensions.HasColumn(dr, "Interviewee") ? string.Empty : Convert.ToString(dr["Interviewee"]);
                callEvaluationDTO.ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]);
                callEvaluationDTO.CallType = Convert.ToInt32(dr["CallType"]);
                if (callEvaluationDTO.CallType != 7)
                {
                    callEvaluationDTO.CallType_inq = callEvaluationDTO.CallType;
                }
                else
                {
                    if (Regex.IsMatch(callEvaluationDTO.FileName, "mp4", RegexOptions.IgnoreCase) || Regex.IsMatch(callEvaluationDTO.FileName, "mov", RegexOptions.IgnoreCase) || Regex.IsMatch(callEvaluationDTO.FileName, "m4v", RegexOptions.IgnoreCase))
                    {
                        callEvaluationDTO.CallType_inq = 8;
                    }
                    else if (Regex.IsMatch(callEvaluationDTO.FileName, "zip", RegexOptions.IgnoreCase))
                    {
                        callEvaluationDTO.CallType_inq = 12;
                    }
                    else
                    {
                        callEvaluationDTO.CallType_inq = 7;
                    }
                }
                callEvaluationDTO.IsRevCell = Convert.ToBoolean(dr["IsRevCell"]);
                callEvaluationDTO.RecId = recorder.Id;
                callEvaluationDTO.RecName = recorder.Name;
                if (dr.FieldExists("BookMarkXML"))
                {
                    callEvaluationDTO.BookmarkXML = Convert.ToString(dr["BookMarkXML"]);
                }
                callEvaluationDTO.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                callEvaluationDTO.ModifiedDate = dr["ModifiedDate"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["ModifiedDate"]);
                callEvaluationDTO.CompletedDate = dr["CompletedDate"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["CompletedDate"]);
                //callEvaluation.CreatedBy=;
                //callEvaluation.ModifiedBy=;

                //callEvaluationDTO.UserId = Convert.ToInt32(dr["UserId"]);


                callEvaluationDTOs.Add(callEvaluationDTO);
            }
            return callEvaluationDTOs;
        }

        #endregion

        #region Multi Call Evaluation
        public static List<MultiCallEvaluationGroup> MapMultiCallEvaluations(SqlDataReader dr)
        {
            List<MultiCallEvaluationGroup> multiCallEvaluations = new List<MultiCallEvaluationGroup>();
            MultiCallEvaluationGroup multiCallEvaluation = null;

            while (dr.Read())
            {
                multiCallEvaluation = new MultiCallEvaluationGroup();
                multiCallEvaluation.RowNo = Convert.ToInt32(dr["RowNo"]);
                multiCallEvaluation.Id = Convert.ToInt32(dr["Id"]);
                multiCallEvaluation.Name = Convert.ToString(dr["Name"]);
                multiCallEvaluation.Comment = Convert.ToString(dr["Comment"]);
                multiCallEvaluation.SurveyId = Convert.ToInt32(dr["SurveyId"]);
                multiCallEvaluation.EvaluatorId = Convert.ToInt32(dr["EvaluatorId"]);
                multiCallEvaluation.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                multiCallEvaluation.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                multiCallEvaluation.PrimaryEvalId = Convert.ToInt32(dr["PrimaryEvalId"]);
                multiCallEvaluation.PrimaryEvalStatus = Convert.ToInt32(dr["PrimaryEvalStatus"]);
                multiCallEvaluation.EvaluatorName = Convert.ToString(dr["EvaluatorName"]);
                multiCallEvaluation.SurveyTitle = Convert.ToString(dr["SurveyTitle"]);
                multiCallEvaluation.NoOfCalls = Convert.ToInt32(dr["NoOfCalls"]);

                multiCallEvaluations.Add(multiCallEvaluation);
            }
            return multiCallEvaluations;
        }
        public static MultiCallEvaluationGroup MapMultiCallEvaluationGroupDetails(SqlDataReader dr)
        {
            MultiCallEvaluationGroup multiCallEvaluation = null;
            while (dr.Read())
            {
                multiCallEvaluation = new MultiCallEvaluationGroup();
                multiCallEvaluation.Id = Convert.ToInt32(dr["Id"]);
                multiCallEvaluation.Name = Convert.ToString(dr["Name"]);
                multiCallEvaluation.Comment = Convert.ToString(dr["Comment"]);
                multiCallEvaluation.SurveyId = Convert.ToInt32(dr["SurveyId"]);
                multiCallEvaluation.EvaluatorId = Convert.ToInt32(dr["EvaluatorId"]);
                multiCallEvaluation.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                multiCallEvaluation.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                multiCallEvaluation.CallId = Convert.ToString(dr["CallId"]);
                multiCallEvaluation.PrimaryEvalId = Convert.ToInt32(dr["PrimaryEvalId"]);
                multiCallEvaluation.PrimaryEvalStatus = Convert.ToInt32(dr["PrimaryEvalStatus"]);
                multiCallEvaluation.EvaluatorName = Convert.ToString(dr["EvaluatorName"]);
                multiCallEvaluation.SurveyTitle = Convert.ToString(dr["SurveyTitle"]);
                multiCallEvaluation.NoOfCalls = Convert.ToInt32(dr["NoOfCalls"]);
            }
            return multiCallEvaluation;
        }
        #endregion

        #region Call Info

        public static CallInfo MapCallInfoTable(SqlDataReader dr)
        {
            CallInfo callInfo = null;

            if (dr.HasRows)
            {
                dr.Read();
                callInfo = new CallInfo();

                callInfo.CallId = Convert.ToString(dr["CallID"]);
                callInfo.UniqueId = dr.IsDBNull(dr.GetOrdinal("UniqueId")) ? 0 : Convert.ToInt64(dr["UniqueId"]); //Convert.ToInt64(dr["UniqueId"]);
                callInfo.RecorderId = Convert.ToInt32(dr["RecID"]);
                callInfo.GroupId = Convert.ToInt32(dr["GroupNum"]);
                callInfo.AgentId = Convert.ToInt32(dr["UserNum"]);
                callInfo.ChannelId = Convert.ToInt32(dr["Ext"]);
                callInfo.CallType = Convert.ToInt32(dr["CallType"]);
                callInfo.StartTime = Convert.ToString(dr["StartTime"]).ConvertStringDateTimeToDateTime();
                callInfo.StartTimeString = Convert.ToString(dr["StartTime"]);
                callInfo.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);
                callInfo.FileName = Convert.ToString(dr["FileName"]);

                if (callInfo.CallType != 7)
                {
                    callInfo.CallType_inq = callInfo.CallType;
                }
                else
                {
                    if (Regex.IsMatch(callInfo.FileName, "mp4", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "mov", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "m4v", RegexOptions.IgnoreCase))
                    {
                        callInfo.CallType_inq = 8;
                    }
                    else if (Regex.IsMatch(callInfo.FileName, "zip", RegexOptions.IgnoreCase))
                    {
                        callInfo.CallType_inq = 12;
                    }
                    else
                    {
                        callInfo.CallType_inq = 7;
                    }
                }

                callInfo.CustName = Convert.ToString(dr["CustName"]);
                callInfo.ANI = Convert.ToString(dr["ANI"]);
                callInfo.CallerID = Convert.ToString(dr["CallerID"]);
                callInfo.CalledID = Convert.ToString(dr["CalledID"]);
                callInfo.SGroupID = dr.IsDBNull(dr.GetOrdinal("SGroupID")) ? 0 : Convert.ToInt32(dr["SGroupID"]);
                callInfo.Tag1 = Convert.ToString(dr["Tag1"]);
                callInfo.Tag2 = Convert.ToString(dr["Tag2"]);
                callInfo.Tag3 = Convert.ToString(dr["Tag3"]);
                callInfo.Tag4 = Convert.ToString(dr["Tag4"]);
                callInfo.ANIPhone = Convert.ToString(dr["ANI_PH"]);
                callInfo.ANIName = Convert.ToString(dr["ANI_NAME"]);
                callInfo.ANIDetails = Convert.ToString(dr["ANI_DETAILS"]);
                callInfo.CallComments = Convert.ToString(dr["CALL_COMMENT"]);
                callInfo.MessageBody = Convert.ToString(dr["Tag2"]);
                //callInfo.ScreenRecFile = Convert.ToString(dr["Screen_Rec_File"]);
                callInfo.ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]);//dr.IsDBNull(dr.GetOrdinal("ScreenFileNames")) ? null : Convert.ToString(dr["ScreenFileNames"]).Split(',').ToList();
                callInfo.RetainValue = Convert.ToInt32(dr["RetainValue"]);
                callInfo.IsPictureEvent = !DBRecordExtensions.HasColumn(dr, "IsPictureEvent") ? false : Convert.ToBoolean(dr["IsPictureEvent"]);
                callInfo.IsVirtualInspection = !DBRecordExtensions.HasColumn(dr, "IsVirtualInspection") ? false : Convert.ToBoolean(dr["IsVirtualInspection"]);
                callInfo.IsRevView = !DBRecordExtensions.HasColumn(dr, "IsRevView") ? false : Convert.ToBoolean(dr["IsRevView"]);
                callInfo.RevViewFileName = DBRecordExtensions.HasColumn(dr, "RevViewFileName") ? Convert.ToString(dr["RevViewFileName"]) : string.Empty;
                callInfo.RevViewStartTime = DBRecordExtensions.HasColumn(dr, "RevViewStartTime") ? Convert.ToString(dr["RevViewStartTime"]) : string.Empty;
                callInfo.RevViewPhoneNumber = DBRecordExtensions.HasColumn(dr, "RevViewPhoneNumber") ? Convert.ToString(dr["RevViewPhoneNumber"]) : string.Empty;
                callInfo.RevViewAgentName = DBRecordExtensions.HasColumn(dr, "RevViewAgentName") ? Convert.ToString(dr["RevViewAgentName"]) : string.Empty;
                callInfo.STTFileName = DBRecordExtensions.HasColumn(dr, "STTFilePath") ? Convert.ToString(dr["STTFilePath"]) : string.Empty;
                callInfo.CallTagging = DBRecordExtensions.HasColumn(dr, "TagName") ? Convert.ToString(dr["TagName"]) : string.Empty;
                callInfo.TagName = DBRecordExtensions.HasColumn(dr, "TagName") ? Convert.ToString(dr["TagName"]) : string.Empty;
                callInfo.TagColorID = DBRecordExtensions.HasColumn(dr, "TagColorID") ? Convert.ToString(dr["TagColorID"]) : string.Empty;
            }
            return callInfo;
        }

        public static CallInfo MapCallInfo(SqlDataReader dr)
        {
            CallInfo callInfo = null;

            if (dr.HasRows)
            {
                dr.Read();
                callInfo = new CallInfo();
                callInfo.CallId = Convert.ToString(dr["CallID"]);
                callInfo.UniqueId = dr.IsDBNull(dr.GetOrdinal("UniqueId")) ? 0 : Convert.ToInt64(dr["UniqueId"]); //Convert.ToInt64(dr["UniqueId"]);
                callInfo.RecorderId = Convert.ToInt32(dr["RecID"]);
                callInfo.GroupId = Convert.ToInt32(dr["GroupNum"]);
                callInfo.GroupName = Convert.ToString(dr["GroupName"]);
                callInfo.AgentId = Convert.ToInt32(dr["UserNum"]);
                callInfo.ChannelId = Convert.ToInt32(dr["Ext"]);
                callInfo.ChannelName = Convert.ToString(dr["ExtName"]);
                callInfo.CallType = Convert.ToInt32(dr["CallType"]);
                callInfo.StartTime = Convert.ToString(dr["StartTime"]).ConvertStringDateTimeToDateTime();
                callInfo.StartTimeString = Convert.ToString(dr["StartTime"]);
                callInfo.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);
                callInfo.FileName = Convert.ToString(dr["FileName"]);

                if (callInfo.CallType != 7)
                {
                    callInfo.CallType_inq = callInfo.CallType;
                }
                else
                {
                    if (Regex.IsMatch(callInfo.FileName, "mp4", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "mov", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "m4v", RegexOptions.IgnoreCase))
                    {
                        callInfo.CallType_inq = 8;
                    }
                    else if (Regex.IsMatch(callInfo.FileName, "zip", RegexOptions.IgnoreCase))
                    {
                        callInfo.CallType_inq = 12;
                    }
                    else
                    {
                        callInfo.CallType_inq = 7;
                    }
                }

                callInfo.CustName = Convert.ToString(dr["CustName"]);
                callInfo.ANI = Convert.ToString(dr["ANI"]);
                callInfo.CallerID = Convert.ToString(dr["CallerID"]);
                callInfo.CalledID = Convert.ToString(dr["CalledID"]);
                callInfo.SGroupID = dr.IsDBNull(dr.GetOrdinal("SGroupID")) ? 0 : Convert.ToInt32(dr["SGroupID"]);
                callInfo.Tag1 = Convert.ToString(dr["Tag1"]);
                callInfo.Tag2 = Convert.ToString(dr["Tag2"]);
                callInfo.Tag3 = Convert.ToString(dr["Tag3"]);
                callInfo.Tag4 = Convert.ToString(dr["Tag4"]);
                callInfo.ANIPhone = Convert.ToString(dr["ANI_PH"]);
                callInfo.ANIName = Convert.ToString(dr["ANI_NAME"]);
                callInfo.ANIDetails = Convert.ToString(dr["ANI_DETAILS"]);
                callInfo.CallComments = Convert.ToString(dr["CALL_COMMENT"]);
                callInfo.MessageBody = Convert.ToString(dr["Tag2"]);
                //callInfo.ScreenRecFile = Convert.ToString(dr["Screen_Rec_File"]);
                callInfo.ScreenFileNames = !dr.FieldExists("ScreenFileNames") ? null : Convert.ToString(dr["ScreenFileNames"]);
                callInfo.RetainValue = Convert.ToInt32(dr["RetainValue"]);
                callInfo.IsPictureEvent = !DBRecordExtensions.HasColumn(dr, "IsPictureEvent") ? false : Convert.ToBoolean(dr["IsPictureEvent"]);
                callInfo.IsVirtualInspection = !DBRecordExtensions.HasColumn(dr, "IsVirtualInspection") ? false : Convert.ToBoolean(dr["IsVirtualInspection"]);
                callInfo.IsRevView = !DBRecordExtensions.HasColumn(dr, "IsRevView") ? false : Convert.ToBoolean(dr["IsRevView"]);
                callInfo.RevViewFileName = DBRecordExtensions.HasColumn(dr, "RevViewFileName") ? Convert.ToString(dr["RevViewFileName"]) : string.Empty;
                callInfo.RevViewStartTime = DBRecordExtensions.HasColumn(dr, "RevViewStartTime") ? Convert.ToString(dr["RevViewStartTime"]) : string.Empty;
                callInfo.RevViewPhoneNumber = DBRecordExtensions.HasColumn(dr, "RevViewPhoneNumber") ? Convert.ToString(dr["RevViewPhoneNumber"]) : string.Empty;
                callInfo.RevViewAgentName = DBRecordExtensions.HasColumn(dr, "RevViewAgentName") ? Convert.ToString(dr["RevViewAgentName"]) : string.Empty;
                callInfo.STTFileName = DBRecordExtensions.HasColumn(dr, "STTFilePath") ? Convert.ToString(dr["STTFilePath"]) : string.Empty;
                callInfo.CallTagging = DBRecordExtensions.HasColumn(dr, "TagName") ? Convert.ToString(dr["TagName"]) : string.Empty;
                callInfo.TagName = DBRecordExtensions.HasColumn(dr, "TagName") ? Convert.ToString(dr["TagName"]) : string.Empty;
                callInfo.TagColorID = DBRecordExtensions.HasColumn(dr, "TagColorID") ? Convert.ToString(dr["TagColorID"]) : string.Empty;
            }
            return callInfo;
        }

        public static CallInfo MapCallInfoForEvaluation(SqlDataReader dr)
        {
            CallInfo callInfo = null;

            if (dr.HasRows)
            {
                dr.Read();
                callInfo = new CallInfo();
                callInfo.CallId = Convert.ToString(dr["CallID"]);
                callInfo.UniqueId = dr.IsDBNull(dr.GetOrdinal("UniqueId")) ? 0 : Convert.ToInt64(dr["UniqueId"]); //Convert.ToInt64(dr["UniqueId"]);
                callInfo.RecorderId = Convert.ToInt32(dr["RecID"]);
                callInfo.GroupId = Convert.ToInt32(dr["GroupNum"]);
                callInfo.GroupName = Convert.ToString(dr["GroupName"]);
                callInfo.AgentId = Convert.ToInt32(dr["UserNum"]);
                callInfo.ChannelId = Convert.ToInt32(dr["Ext"]);
                callInfo.ChannelName = Convert.ToString(dr["ExtName"]);
                callInfo.CallType = Convert.ToInt32(dr["CallType"]);
                callInfo.StartTime = Convert.ToString(dr["StartTime"]).ConvertStringDateTimeToDateTime();
                callInfo.StartTimeString = Convert.ToString(dr["StartTime"]);
                callInfo.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);
                callInfo.FileName = Convert.ToString(dr["FileName"]);

                if (callInfo.CallType != 7)
                {
                    callInfo.CallType_inq = callInfo.CallType;
                }
                else
                {
                    if (Regex.IsMatch(callInfo.FileName, "mp4", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "mov", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "m4v", RegexOptions.IgnoreCase))
                    {
                        callInfo.CallType_inq = 8;
                    }
                    else if (Regex.IsMatch(callInfo.FileName, "zip", RegexOptions.IgnoreCase))
                    {
                        callInfo.CallType_inq = 12;
                    }
                    else
                    {
                        callInfo.CallType_inq = 7;
                    }
                }
                callInfo.IsRevCell = dr["IsRevCell"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsRevCell"]);
                callInfo.CustName = Convert.ToString(dr["CustName"]);
                callInfo.ANI = Convert.ToString(dr["ANI"]);
                callInfo.CallerID = Convert.ToString(dr["CallerID"]);
                callInfo.CalledID = Convert.ToString(dr["CalledID"]);
                callInfo.SGroupID = dr.IsDBNull(dr.GetOrdinal("SGroupID")) ? 0 : Convert.ToInt32(dr["SGroupID"]);
                callInfo.Tag1 = Convert.ToString(dr["Tag1"]);
                callInfo.Tag2 = Convert.ToString(dr["Tag2"]);
                callInfo.Tag3 = Convert.ToString(dr["Tag3"]);
                callInfo.Tag4 = Convert.ToString(dr["Tag4"]);
                callInfo.ANIPhone = Convert.ToString(dr["ANI_PH"]);
                callInfo.ANIName = Convert.ToString(dr["ANI_NAME"]);
                callInfo.ANIDetails = Convert.ToString(dr["ANI_DETAILS"]);
                callInfo.CallComments = Convert.ToString(dr["CALL_COMMENT"]);
                callInfo.MessageBody = Convert.ToString(dr["Tag2"]);
                //callInfo.ScreenRecFile = Convert.ToString(dr["Screen_Rec_File"]);
                callInfo.ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]);//dr.IsDBNull(dr.GetOrdinal("ScreenFileNames")) ? null : Convert.ToString(dr["ScreenFileNames"]).Split(',').ToList();
                if (dr.FieldExists("BookMarkXML"))
                {
                    callInfo.BookmarkXML = Convert.ToString(dr["BookMarkXML"]);
                }
                callInfo.RetainValue = Convert.ToInt32(dr["RetainValue"]);
                callInfo.IsPictureEvent = !DBRecordExtensions.HasColumn(dr, "IsPictureEvent") ? false : Convert.ToBoolean(dr["IsPictureEvent"]);
                callInfo.IsVirtualInspection = !DBRecordExtensions.HasColumn(dr, "IsVirtualInspection") ? false : Convert.ToBoolean(dr["IsVirtualInspection"]);
                callInfo.IsRevView = !DBRecordExtensions.HasColumn(dr, "IsRevView") ? false : Convert.ToBoolean(dr["IsRevView"]);
                callInfo.RevViewFileName = DBRecordExtensions.HasColumn(dr, "RevViewFileName") ? Convert.ToString(dr["RevViewFileName"]) : string.Empty;
                callInfo.RevViewStartTime = DBRecordExtensions.HasColumn(dr, "RevViewStartTime") ? Convert.ToString(dr["RevViewStartTime"]) : string.Empty;
                callInfo.RevViewPhoneNumber = DBRecordExtensions.HasColumn(dr, "RevViewPhoneNumber") ? Convert.ToString(dr["RevViewPhoneNumber"]) : string.Empty;
                callInfo.RevViewAgentName = DBRecordExtensions.HasColumn(dr, "RevViewAgentName") ? Convert.ToString(dr["RevViewAgentName"]) : string.Empty;
                callInfo.STTFileName = DBRecordExtensions.HasColumn(dr, "STTFilePath") ? Convert.ToString(dr["STTFilePath"]) : string.Empty;
                callInfo.CallTagging = DBRecordExtensions.HasColumn(dr, "TagName") ? Convert.ToString(dr["TagName"]) : string.Empty;
                callInfo.TagName = DBRecordExtensions.HasColumn(dr, "TagName") ? Convert.ToString(dr["TagName"]) : string.Empty;
                callInfo.TagColorID = DBRecordExtensions.HasColumn(dr, "TagColorID") ? Convert.ToString(dr["TagColorID"]) : string.Empty;
                callInfo.interview_Interviewee = !DBRecordExtensions.HasColumn(dr, "Interviewee") ? string.Empty : Convert.ToString(dr["Interviewee"]);
            }
            return callInfo;
        }

        public static List<CallInfo> MapCallInfosForEvaluation(SqlDataReader dr)
        {
            CallInfo callInfo = null;
            List<CallInfo> calls = new List<CallInfo>();
            try
            {
                while (dr.Read())
                {
                    callInfo = new CallInfo();

                    callInfo = new CallInfo();
                    callInfo.CallId = Convert.ToString(dr["CallID"]);
                    callInfo.UniqueId = dr.IsDBNull(dr.GetOrdinal("UniqueId")) ? 0 : Convert.ToInt64(dr["UniqueId"]); //Convert.ToInt64(dr["UniqueId"]);
                    callInfo.RecorderId = Convert.ToInt32(dr["RecID"]);
                    callInfo.GroupId = Convert.ToInt32(dr["GroupNum"]);
                    callInfo.GroupName = Convert.ToString(dr["GroupName"]);
                    callInfo.AgentId = Convert.ToInt32(dr["UserNum"]);
                    callInfo.ChannelId = Convert.ToInt32(dr["Ext"]);
                    callInfo.ChannelName = Convert.ToString(dr["ExtName"]);
                    callInfo.CallType = Convert.ToInt32(dr["CallType"]);
                    callInfo.StartTime = Convert.ToString(dr["StartTime"]).ConvertStringDateTimeToDateTime();
                    callInfo.StartTimeString = Convert.ToString(dr["StartTime"]);
                    callInfo.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);
                    callInfo.FileName = Convert.ToString(dr["FileName"]);

                    if (callInfo.CallType != 7)
                    {
                        callInfo.CallType_inq = callInfo.CallType;
                    }
                    else
                    {
                        if (Regex.IsMatch(callInfo.FileName, "mp4", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "mov", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "m4v", RegexOptions.IgnoreCase))
                        {
                            callInfo.CallType_inq = 8;
                        }
                        else if (Regex.IsMatch(callInfo.FileName, "zip", RegexOptions.IgnoreCase))
                        {
                            callInfo.CallType_inq = 12;
                        }
                        else
                        {
                            callInfo.CallType_inq = 7;
                        }
                    }
                    callInfo.IsRevCell = dr["IsRevCell"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsRevCell"]);
                    callInfo.CustName = Convert.ToString(dr["CustName"]);
                    callInfo.ANI = Convert.ToString(dr["ANI"]);
                    callInfo.CallerID = Convert.ToString(dr["CallerID"]);
                    callInfo.CalledID = Convert.ToString(dr["CalledID"]);
                    callInfo.SGroupID = dr.IsDBNull(dr.GetOrdinal("SGroupID")) ? 0 : Convert.ToInt32(dr["SGroupID"]);
                    callInfo.Tag1 = Convert.ToString(dr["Tag1"]);
                    callInfo.Tag2 = Convert.ToString(dr["Tag2"]);
                    callInfo.Tag3 = Convert.ToString(dr["Tag3"]);
                    callInfo.Tag4 = Convert.ToString(dr["Tag4"]);
                    callInfo.ANIPhone = Convert.ToString(dr["ANI_PH"]);
                    callInfo.ANIName = Convert.ToString(dr["ANI_NAME"]);
                    callInfo.ANIDetails = Convert.ToString(dr["ANI_DETAILS"]);
                    callInfo.CallComments = Convert.ToString(dr["CALL_COMMENT"]);
                    callInfo.MessageBody = Convert.ToString(dr["Tag2"]);
                    //callInfo.ScreenRecFile = Convert.ToString(dr["Screen_Rec_File"]);
                    callInfo.ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]);//dr.IsDBNull(dr.GetOrdinal("ScreenFileNames")) ? null : Convert.ToString(dr["ScreenFileNames"]).Split(',').ToList();
                    if (dr.FieldExists("BookMarkXML"))
                    {
                        callInfo.BookmarkXML = Convert.ToString(dr["BookMarkXML"]);
                    }
                    callInfo.RetainValue = Convert.ToInt32(dr["RetainValue"]);
                    callInfo.IsPictureEvent = !DBRecordExtensions.HasColumn(dr, "IsPictureEvent") ? false : Convert.ToBoolean(dr["IsPictureEvent"]);
                    callInfo.IsVirtualInspection = !DBRecordExtensions.HasColumn(dr, "IsVirtualInspection") ? false : Convert.ToBoolean(dr["IsVirtualInspection"]);
                    callInfo.IsRevView = !DBRecordExtensions.HasColumn(dr, "IsRevView") ? false : Convert.ToBoolean(dr["IsRevView"]);
                    callInfo.RevViewFileName = DBRecordExtensions.HasColumn(dr, "RevViewFileName") ? Convert.ToString(dr["RevViewFileName"]) : string.Empty;
                    callInfo.RevViewStartTime = DBRecordExtensions.HasColumn(dr, "RevViewStartTime") ? Convert.ToString(dr["RevViewStartTime"]) : string.Empty;
                    callInfo.RevViewPhoneNumber = DBRecordExtensions.HasColumn(dr, "RevViewPhoneNumber") ? Convert.ToString(dr["RevViewPhoneNumber"]) : string.Empty;
                    callInfo.RevViewAgentName = DBRecordExtensions.HasColumn(dr, "RevViewAgentName") ? Convert.ToString(dr["RevViewAgentName"]) : string.Empty;
                    callInfo.STTFileName = DBRecordExtensions.HasColumn(dr, "STTFilePath") ? Convert.ToString(dr["STTFilePath"]) : string.Empty;
                    callInfo.CallTagging = DBRecordExtensions.HasColumn(dr, "TagName") ? Convert.ToString(dr["TagName"]) : string.Empty;
                    callInfo.TagName = DBRecordExtensions.HasColumn(dr, "TagName") ? Convert.ToString(dr["TagName"]) : string.Empty;
                    callInfo.TagColorID = DBRecordExtensions.HasColumn(dr, "TagColorID") ? Convert.ToString(dr["TagColorID"]) : string.Empty;

                    calls.Add(callInfo);
                }
                return calls;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public static List<CallInfo> MapCallInfos(SqlDataReader dr)
        {
            try
            {
                CallInfo callInfo = null;
                List<CallInfo> calls = new List<CallInfo>();
                while (dr.Read())
                {
                    callInfo = new CallInfo();

                    callInfo.RowNo = (long)dr["RowNo"];

                    callInfo.CallId = Convert.ToString(dr["CallID"]);
                    //callInfo.UniqueId = Convert.ToInt64(dr["UniqueId"]);
                    callInfo.UniqueId = dr.IsDBNull(dr.GetOrdinal("UniqueId")) ? 0 : Convert.ToInt64(dr["UniqueId"]);
                    callInfo.RecorderId = Convert.ToInt32(dr["RecID"]);
                    //if (!dr.IsDBNull(dr.GetOrdinal("RecName")))
                    //    callInfo.RecorderName = dr.GetString(dr.GetOrdinal("RecName"));
                    callInfo.RecorderName = Convert.ToString(dr["RecName"]);
                    callInfo.GroupId = Convert.ToInt32(dr["GroupNum"]);
                    callInfo.GroupName = Convert.ToString(dr["GroupName"]);
                    callInfo.AgentId = Convert.ToInt32(dr["UserNum"]);
                    callInfo.ChannelId = Convert.ToInt32(dr["Ext"]);
                    callInfo.ChannelName = Convert.ToString(dr["ExtName"]);
                    callInfo.CallType = Convert.ToInt32(dr["CallType"]);
                    //callInfo.StartTime = Convert.ToDateTime(dr["StartTime"]);
                    callInfo.StartTime = dr["StartTime"].ToString().ConvertStringDateTimeToDateTime();
                    callInfo.StartTimeString = Convert.ToString(dr["StartTime"]);
                    callInfo.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);
                    callInfo.FileName = Convert.ToString(dr["FileName"]);

                    if (callInfo.CallType != 7)
                        callInfo.CallType_inq = callInfo.CallType;
                    else
                    {
                        if (Regex.IsMatch(callInfo.FileName, "mp4", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "mov", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "m4v", RegexOptions.IgnoreCase))
                            callInfo.CallType_inq = 8;
                        else if (Regex.IsMatch(callInfo.FileName, "zip", RegexOptions.IgnoreCase))
                            callInfo.CallType_inq = 12;
                        else
                            callInfo.CallType_inq = 7;
                    }

                    callInfo.CustName = Convert.ToString(dr["CustName"]);
                    callInfo.ANI = Convert.ToString(dr["ANI"]);
                    callInfo.CallerID = Convert.ToString(dr["CallerID"]);
                    callInfo.CalledID = Convert.ToString(dr["CalledID"]);
                    callInfo.SGroupID = dr.IsDBNull(dr.GetOrdinal("SGroupID")) ? 0 : Convert.ToInt32(dr["SGroupID"]);
                    callInfo.Tag1 = Convert.ToString(dr["Tag1"]);
                    callInfo.Tag2 = Convert.ToString(dr["Tag2"]);
                    callInfo.Tag3 = Convert.ToString(dr["Tag3"]);
                    callInfo.Tag4 = Convert.ToString(dr["Tag4"]);
                    callInfo.Tag5 = Convert.ToString(dr["Tag5"]);
                    callInfo.Tag6 = Convert.ToString(dr["Tag6"]);
                    callInfo.Tag7 = Convert.ToString(dr["Tag7"]);
                    callInfo.Tag8 = Convert.ToString(dr["Tag8"]);
                    callInfo.Tag9 = Convert.ToString(dr["Tag9"]);
                    callInfo.Tag10 = Convert.ToString(dr["Tag10"]);
                    callInfo.Tag11 = Convert.ToString(dr["Tag11"]);
                    callInfo.Tag12 = Convert.ToString(dr["Tag12"]);
                    callInfo.Tag13 = Convert.ToString(dr["Tag13"]);
                    callInfo.Tag14 = Convert.ToString(dr["Tag14"]);
                    callInfo.Tag15 = Convert.ToString(dr["Tag15"]);
                    callInfo.Tag16 = Convert.ToString(dr["Tag16"]);

                    callInfo.ANIPhone = Convert.ToString(dr["ANI_PH"]);
                    callInfo.ANIName = Convert.ToString(dr["ANI_NAME"]);
                    callInfo.ANIDetails = Convert.ToString(dr["ANI_DETAILS"]);
                    callInfo.CallComments = Convert.ToString(dr["CALL_COMMENT"]);
                    callInfo.MessageBody = Convert.ToString(dr["Tag2"]);
                    //callInfo.ScreenRecFile = Convert.ToString(dr["Screen_Rec_File"]);
                    callInfo.ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]);//dr.IsDBNull(dr.GetOrdinal("ScreenFileNames")) ? null : Convert.ToString(dr["ScreenFileNames"]).Split(',').ToList();

                    callInfo.RetainValue = Convert.ToInt32(dr["RetainValue"]);
                    callInfo.IsRevCell = dr["IsRevCell"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsRevCell"]);

                    callInfo.Transcription = Convert.ToString(dr["Transcription"]);
                    callInfo.TranscriptionId = dr["TranscriptionId"] == DBNull.Value ? 0 : Convert.ToInt32(dr["TranscriptionId"]);
                    callInfo.Confidence = dr["Confidence"] == DBNull.Value ? 0 : Convert.ToDecimal(dr["Confidence"]);

                    callInfo.IsSyncedFromClient = dr.IsDBNull(dr.GetOrdinal("IsSyncedFromClient")) ? 0 : Convert.ToInt32(dr["IsSyncedFromClient"]); //Convert.ToInt32(dr["IsSyncedFromClient"]);

                    callInfo.interview_DateTime = Convert.ToString(dr["DateTime"]);
                    callInfo.interview_Interviewee = Convert.ToString(dr["Interviewee"]);
                    callInfo.interview_MdInterviewee = dr.IsDBNull(dr.GetOrdinal("MDInterviewee")) ? null : Convert.ToString(dr["MDInterviewee"]); //Convert.ToString(dr["MdInterviewee"]);

                    callInfo.interview_Interviewee = callInfo.CallType == 7 ? callInfo.interview_Interviewee : callInfo.interview_MdInterviewee;

                    callInfo.interview_Interviewer = Convert.ToString(dr["Interviewer"]);
                    callInfo.interview_InterviewId = Convert.ToString(dr["InterviewId"]);
                    callInfo.interview_GPS = Convert.ToString(dr["GPS"]);
                    callInfo.interview_Notes = Convert.ToString(dr["Notes"]);

                    callInfo.ErrorInMuxProcess = 0;
                    if (dr["ErrorinMuxProcess"] != DBNull.Value)
                        callInfo.ErrorInMuxProcess = Convert.ToInt32(dr["ErrorinMuxProcess"]);

                    callInfo.BookmarkCSV = string.Empty;
                    if (dr.FieldExists("BookMarkXML"))
                    {
                        callInfo.BookmarkXML = Convert.ToString(dr["BookMarkXML"]);
                        if (!String.IsNullOrEmpty(callInfo.BookmarkXML))
                            callInfo.BookmarkCSV = callInfo.BookmarkXML.Inquire_ConvertXmlStringToCsvString(callInfo.CallType);
                    }
                    callInfo.IsPictureEvent = !DBRecordExtensions.HasColumn(dr, "IsPictureEvent") ? false : Convert.ToBoolean(dr["IsPictureEvent"]);
                    callInfo.IsVirtualInspection = !DBRecordExtensions.HasColumn(dr, "IsVirtualInspection") ? false : Convert.ToBoolean(dr["IsVirtualInspection"]);
                    callInfo.IsRevView = !DBRecordExtensions.HasColumn(dr, "IsRevView") ? false : Convert.ToBoolean(dr["IsRevView"]);
                    callInfo.RevViewFileName = DBRecordExtensions.HasColumn(dr, "RevViewFileName") ? Convert.ToString(dr["RevViewFileName"]) : string.Empty;
                    callInfo.RevViewStartTime = DBRecordExtensions.HasColumn(dr, "RevViewStartTime") ? Convert.ToString(dr["RevViewStartTime"]) : string.Empty;
                    callInfo.RevViewPhoneNumber = DBRecordExtensions.HasColumn(dr, "RevViewPhoneNumber") ? Convert.ToString(dr["RevViewPhoneNumber"]) : string.Empty;
                    callInfo.RevViewAgentName = DBRecordExtensions.HasColumn(dr, "RevViewAgentName") ? Convert.ToString(dr["RevViewAgentName"]) : string.Empty;
                    callInfo.STTFileName = DBRecordExtensions.HasColumn(dr, "STTFilePath") ? Convert.ToString(dr["STTFilePath"]) : string.Empty;
                    callInfo.VesselId = DBRecordExtensions.HasColumn(dr, "VesselID") ? Convert.ToString(dr["VesselID"]) : string.Empty;
                    callInfo.CallTagging = DBRecordExtensions.HasColumn(dr, "TagName") ? Convert.ToString(dr["TagName"]) : string.Empty;
                    callInfo.TagName = DBRecordExtensions.HasColumn(dr, "TagName") ? Convert.ToString(dr["TagName"]) : string.Empty;
                    callInfo.TagColorID = DBRecordExtensions.HasColumn(dr, "TagColorID") ? Convert.ToString(dr["TagColorID"]) : string.Empty;
                    callInfo.AssetId = DBRecordExtensions.HasColumn(dr, "AssetId") ? Convert.ToString(dr["AssetId"]) : string.Empty;

                    calls.Add(callInfo);
                }
                return calls;
            }
            catch (Exception ex)
            {
                throw ex;
            }

        }

        public static List<CallInfo> MapCallInfosLite(SqlDataReader dr)
        {
            CallInfo callInfo = null;
            List<CallInfo> calls = new List<CallInfo>();
            while (dr.Read())
            {
                callInfo = new CallInfo();

                callInfo.RowNo = (long)dr["RowNo"];

                callInfo.CallId = Convert.ToString(dr["CallID"]);
                callInfo.UniqueId = dr.IsDBNull(dr.GetOrdinal("UniqueId")) ? 0 : Convert.ToInt64(dr["UniqueId"]); //Convert.ToInt64(dr["UniqueId"]);
                callInfo.RecorderId = Convert.ToInt32(dr["RecID"]);
                callInfo.RecorderName = Convert.ToString(dr["RecName"]);
                callInfo.GroupId = Convert.ToInt32(dr["GroupNum"]);
                callInfo.GroupName = Convert.ToString(dr["GroupName"]);
                callInfo.AgentId = Convert.ToInt32(dr["UserNum"]);
                callInfo.ChannelId = Convert.ToInt32(dr["Ext"]);
                callInfo.ChannelName = Convert.ToString(dr["ExtName"]);
                callInfo.CallType = Convert.ToInt32(dr["CallType"]);
                callInfo.StartTime = dr["StartTime"].ToString().ConvertStringDateTimeToDateTime();
                callInfo.StartTimeString = Convert.ToString(dr["StartTime"]);
                callInfo.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);
                callInfo.FileName = Convert.ToString(dr["FileName"]);
                callInfo.CallType_inq = callInfo.CallType;

                if (callInfo.CallType != 7)
                    callInfo.CallType_inq = callInfo.CallType;
                else
                {
                    if (Regex.IsMatch(callInfo.FileName, "mp4", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "mov", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "m4v", RegexOptions.IgnoreCase))
                        callInfo.CallType_inq = 8;
                    else if (Regex.IsMatch(callInfo.FileName, "zip", RegexOptions.IgnoreCase))
                        callInfo.CallType_inq = 12;
                    else
                        callInfo.CallType_inq = 7;
                }

                callInfo.CustName = Convert.ToString(dr["CustName"]);
                callInfo.ANI = Convert.ToString(dr["ANI"]);
                callInfo.CallerID = Convert.ToString(dr["CallerID"]);
                callInfo.CalledID = Convert.ToString(dr["CalledID"]);
                callInfo.SGroupID = dr.IsDBNull(dr.GetOrdinal("SGroupID")) ? 0 : Convert.ToInt32(dr["SGroupID"]);
                callInfo.Tag1 = Convert.ToString(dr["Tag1"]);
                callInfo.Tag2 = Convert.ToString(dr["Tag2"]);
                callInfo.Tag3 = Convert.ToString(dr["Tag3"]);
                callInfo.Tag4 = Convert.ToString(dr["Tag4"]);
                callInfo.Tag5 = Convert.ToString(dr["Tag5"]);
                callInfo.Tag6 = Convert.ToString(dr["Tag6"]);
                callInfo.Tag7 = Convert.ToString(dr["Tag7"]);
                callInfo.Tag8 = Convert.ToString(dr["Tag8"]);
                callInfo.Tag9 = Convert.ToString(dr["Tag9"]);
                callInfo.Tag10 = Convert.ToString(dr["Tag10"]);
                callInfo.Tag11 = Convert.ToString(dr["Tag11"]);
                callInfo.Tag12 = Convert.ToString(dr["Tag12"]);
                callInfo.Tag13 = Convert.ToString(dr["Tag13"]);
                callInfo.Tag14 = Convert.ToString(dr["Tag14"]);
                callInfo.Tag15 = Convert.ToString(dr["Tag15"]);
                callInfo.Tag16 = Convert.ToString(dr["Tag16"]);
                callInfo.IsRevCell = Convert.ToBoolean(dr["IsRevCell"]);

                callInfo.ANIPhone = Convert.ToString(dr["ANI_PH"]);
                callInfo.ANIName = Convert.ToString(dr["ANI_NAME"]);
                callInfo.ANIDetails = Convert.ToString(dr["ANI_DETAILS"]);
                callInfo.CallComments = Convert.ToString(dr["CALL_COMMENT"]);
                callInfo.MessageBody = Convert.ToString(dr["Tag2"]);
                callInfo.ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]);
                callInfo.RetainValue = Convert.ToInt32(dr["RetainValue"]);
                callInfo.Transcription = Convert.ToString(dr["Transcription"]);
                callInfo.TranscriptionId = dr["TranscriptionId"] == DBNull.Value ? 0 : Convert.ToInt32(dr["TranscriptionId"]);
                callInfo.Confidence = dr["Confidence"] == DBNull.Value ? 0 : Convert.ToDecimal(dr["Confidence"]);
                callInfo.IsSyncedFromClient = dr.IsDBNull(dr.GetOrdinal("IsSyncedFromClient")) ? 0 : Convert.ToInt32(dr["IsSyncedFromClient"]); //Convert.ToInt32(dr["IsSyncedFromClient"]);

                callInfo.BookmarkCSV = string.Empty;
                if (dr.FieldExists("BookMarkXML"))
                {
                    callInfo.BookmarkXML = Convert.ToString(dr["BookMarkXML"]);
                    if (!String.IsNullOrEmpty(callInfo.BookmarkXML))
                        callInfo.BookmarkCSV = callInfo.BookmarkXML.Inquire_ConvertXmlStringToCsvString(callInfo.CallType);
                }
                calls.Add(callInfo);
                callInfo.IsPictureEvent = !DBRecordExtensions.HasColumn(dr, "IsPictureEvent") ? false : Convert.ToBoolean(dr["IsPictureEvent"]);
                callInfo.IsVirtualInspection = !DBRecordExtensions.HasColumn(dr, "IsVirtualInspection") ? false : Convert.ToBoolean(dr["IsVirtualInspection"]);
                callInfo.IsRevView = !DBRecordExtensions.HasColumn(dr, "IsRevView") ? false : Convert.ToBoolean(dr["IsRevView"]);
                callInfo.RevViewFileName = DBRecordExtensions.HasColumn(dr, "RevViewFileName") ? Convert.ToString(dr["RevViewFileName"]) : string.Empty;
                callInfo.RevViewStartTime = DBRecordExtensions.HasColumn(dr, "RevViewStartTime") ? Convert.ToString(dr["RevViewStartTime"]) : string.Empty;
                callInfo.RevViewPhoneNumber = DBRecordExtensions.HasColumn(dr, "RevViewPhoneNumber") ? Convert.ToString(dr["RevViewPhoneNumber"]) : string.Empty;
                callInfo.RevViewAgentName = DBRecordExtensions.HasColumn(dr, "RevViewAgentName") ? Convert.ToString(dr["RevViewAgentName"]) : string.Empty;
                callInfo.STTFileName = DBRecordExtensions.HasColumn(dr, "STTFilePath") ? Convert.ToString(dr["STTFilePath"]) : string.Empty;
                callInfo.CallTagging = DBRecordExtensions.HasColumn(dr, "TagName") ? Convert.ToString(dr["TagName"]) : string.Empty;
                callInfo.TagName = DBRecordExtensions.HasColumn(dr, "TagName") ? Convert.ToString(dr["TagName"]) : string.Empty;
                callInfo.TagColorID = DBRecordExtensions.HasColumn(dr, "TagColorID") ? Convert.ToString(dr["TagColorID"]) : string.Empty;
            }
            return calls;
        }

        public static List<CallInfo> MapCallsForExportData(SqlDataReader dr)
        {
            CallInfo callInfo = null;
            List<CallInfo> calls = new List<CallInfo>();
            while (dr.Read())
            {
                callInfo = new CallInfo();
                callInfo.RowNo = (long)dr["RowNo"];
                callInfo.GroupId = Convert.ToInt32(dr["GroupNum"]);
                callInfo.GroupName = Convert.ToString(dr["GroupName"]);
                callInfo.AgentId = Convert.ToInt32(dr["UserNum"]);
                callInfo.ChannelId = Convert.ToInt32(dr["Ext"]);
                callInfo.ChannelName = Convert.ToString(dr["ExtName"]);
                callInfo.StartTime = dr["StartTime"].ToString().ConvertStringDateTimeToDateTime();
                callInfo.ANI = Convert.ToString(dr["ANI"]);
                callInfo.CalledID = Convert.ToString(dr["CalledID"]);
                callInfo.Tag1 = Convert.ToString(dr["Tag1"]);
                callInfo.Tag2 = Convert.ToString(dr["Tag2"]);
                callInfo.Tag3 = Convert.ToString(dr["Tag3"]);
                callInfo.Tag4 = Convert.ToString(dr["Tag4"]);
                callInfo.Tag5 = Convert.ToString(dr["Tag5"]);
                callInfo.Tag6 = Convert.ToString(dr["Tag6"]);
                callInfo.Tag7 = Convert.ToString(dr["Tag7"]);
                callInfo.Tag8 = Convert.ToString(dr["Tag8"]);
                callInfo.Tag9 = Convert.ToString(dr["Tag9"]);
                callInfo.Tag10 = Convert.ToString(dr["Tag10"]);
                callInfo.Tag11 = Convert.ToString(dr["Tag11"]);
                callInfo.Tag12 = Convert.ToString(dr["Tag12"]);
                callInfo.Tag13 = Convert.ToString(dr["Tag13"]);
                callInfo.Tag14 = Convert.ToString(dr["Tag14"]);
                callInfo.Tag15 = Convert.ToString(dr["Tag15"]);
                callInfo.Tag16 = Convert.ToString(dr["Tag16"]);

                callInfo.ANIPhone = Convert.ToString(dr["ANI_PH"]);
                callInfo.ANIName = Convert.ToString(dr["ANI_NAME"]);
                callInfo.ANIDetails = Convert.ToString(dr["ANI_DETAILS"]);
                callInfo.CallComments = Convert.ToString(dr["CALL_COMMENT"]);
                callInfo.BookmarkCSV = string.Empty;
                if (dr.FieldExists("BookMarkXML"))
                {
                    callInfo.BookmarkXML = Convert.ToString(dr["BookMarkXML"]);
                    if (!String.IsNullOrEmpty(callInfo.BookmarkXML))
                        callInfo.BookmarkCSV = callInfo.BookmarkXML.Inquire_ConvertXmlStringToCsvString(callInfo.CallType);
                }
                callInfo.RecorderName = Convert.ToString(dr["RecName"]);
                callInfo.IsPictureEvent = !DBRecordExtensions.HasColumn(dr, "IsPictureEvent") ? false : Convert.ToBoolean(dr["IsPictureEvent"]);
                callInfo.IsVirtualInspection = !DBRecordExtensions.HasColumn(dr, "IsVirtualInspection") ? false : Convert.ToBoolean(dr["IsVirtualInspection"]);
                callInfo.IsRevView = !DBRecordExtensions.HasColumn(dr, "IsRevView") ? false : Convert.ToBoolean(dr["IsRevView"]);
                callInfo.RevViewFileName = DBRecordExtensions.HasColumn(dr, "RevViewFileName") ? Convert.ToString(dr["RevViewFileName"]) : string.Empty;
                callInfo.RevViewStartTime = DBRecordExtensions.HasColumn(dr, "RevViewStartTime") ? Convert.ToString(dr["RevViewStartTime"]) : string.Empty;
                callInfo.RevViewPhoneNumber = DBRecordExtensions.HasColumn(dr, "RevViewPhoneNumber") ? Convert.ToString(dr["RevViewPhoneNumber"]) : string.Empty;
                callInfo.RevViewAgentName = DBRecordExtensions.HasColumn(dr, "RevViewAgentName") ? Convert.ToString(dr["RevViewAgentName"]) : string.Empty;
                callInfo.STTFileName = DBRecordExtensions.HasColumn(dr, "STTFilePath") ? Convert.ToString(dr["STTFilePath"]) : string.Empty;
                callInfo.CallTagging = DBRecordExtensions.HasColumn(dr, "TagName") ? Convert.ToString(dr["TagName"]) : string.Empty;
                callInfo.TagName = DBRecordExtensions.HasColumn(dr, "TagName") ? Convert.ToString(dr["TagName"]) : string.Empty;
                callInfo.TagColorID = DBRecordExtensions.HasColumn(dr, "TagColorID") ? Convert.ToString(dr["TagColorID"]) : string.Empty;

                calls.Add(callInfo);
            }
            return calls;
        }


        public static List<CallInfo> MapCallInfos(SqlDataReader dr, int recId, string recName)
        {
            CallInfo callInfo = null;
            List<CallInfo> calls = new List<CallInfo>();
            while (dr.Read())
            {
                callInfo = new CallInfo();

                callInfo.RowNo = (long)dr["RowNo"];

                callInfo.CallId = Convert.ToString(dr["CallID"]);
                callInfo.UniqueId = dr.IsDBNull(dr.GetOrdinal("UniqueId")) ? 0 : Convert.ToInt64(dr["UniqueId"]); //Convert.ToInt64(dr["UniqueId"]);
                callInfo.RecorderId = recId;// Convert.ToInt32(dr["RecID"]);
                //if (!dr.IsDBNull(dr.GetOrdinal("RecName")))
                //    callInfo.RecorderName = dr.GetString(dr.GetOrdinal("RecName"));
                callInfo.RecorderName = recName;// Convert.ToString(dr["RecName"]);
                callInfo.GroupId = Convert.ToInt32(dr["GroupNum"]);
                callInfo.GroupName = Convert.ToString(dr["GroupName"]);
                callInfo.AgentId = Convert.ToInt32(dr["UserNum"]);
                callInfo.ChannelId = Convert.ToInt32(dr["Ext"]);
                callInfo.ChannelName = Convert.ToString(dr["ExtName"]);
                callInfo.CallType = Convert.ToInt32(dr["CallType"]);
                //callInfo.StartTime = Convert.ToDateTime(dr["StartTime"]);
                callInfo.StartTime = dr["StartTime"].ToString().ConvertStringDateTimeToDateTime();
                callInfo.StartTimeString = Convert.ToString(dr["StartTime"]);
                callInfo.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);
                callInfo.FileName = Convert.ToString(dr["FileName"]);

                if (callInfo.CallType != 7)
                    callInfo.CallType_inq = callInfo.CallType;
                else
                {
                    if (Regex.IsMatch(callInfo.FileName, "mp4", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "mov", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "m4v", RegexOptions.IgnoreCase))
                        callInfo.CallType_inq = 8;
                    else if (Regex.IsMatch(callInfo.FileName, "zip", RegexOptions.IgnoreCase))
                        callInfo.CallType_inq = 12;
                    else
                        callInfo.CallType_inq = 7;
                }

                callInfo.CustName = Convert.ToString(dr["CustName"]);
                callInfo.ANI = Convert.ToString(dr["ANI"]);
                callInfo.CallerID = Convert.ToString(dr["CallerID"]);
                callInfo.CalledID = Convert.ToString(dr["CalledID"]);
                callInfo.SGroupID = dr.IsDBNull(dr.GetOrdinal("SGroupID")) ? 0 : Convert.ToInt32(dr["SGroupID"]);
                callInfo.Tag1 = Convert.ToString(dr["Tag1"]);
                callInfo.Tag2 = Convert.ToString(dr["Tag2"]);
                callInfo.Tag3 = Convert.ToString(dr["Tag3"]);
                callInfo.Tag4 = Convert.ToString(dr["Tag4"]);
                callInfo.ANIPhone = Convert.ToString(dr["ANI_PH"]);
                callInfo.ANIName = Convert.ToString(dr["ANI_NAME"]);
                callInfo.ANIDetails = Convert.ToString(dr["ANI_DETAILS"]);
                callInfo.CallComments = Convert.ToString(dr["CALL_COMMENT"]);
                callInfo.MessageBody = Convert.ToString(dr["Tag2"]);
                //callInfo.ScreenRecFile = Convert.ToString(dr["Screen_Rec_File"]);
                callInfo.ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]);// dr.IsDBNull(dr.GetOrdinal("ScreenFileNames")) ? null : Convert.ToString(dr["ScreenFileNames"]).Split(',').ToList();
                callInfo.RetainValue = Convert.ToInt32(dr["RetainValue"]);

                //if (!string.IsNullOrEmpty(callInfo.ScreenFileNames))//TODO: delete this in version 10
                //    callInfo.CallType = 6;

                callInfo.BookmarkCSV = string.Empty;
                if (dr.FieldExists("BookMarkXML"))
                {
                    callInfo.BookmarkXML = Convert.ToString(dr["BookMarkXML"]);
                    if (!String.IsNullOrEmpty(callInfo.BookmarkXML))
                    {
                        callInfo.BookmarkCSV = callInfo.BookmarkXML.ConvertXmlStringToCsvString();
                    }
                }
                callInfo.IsPictureEvent = !DBRecordExtensions.HasColumn(dr, "IsPictureEvent") ? false : Convert.ToBoolean(dr["IsPictureEvent"]);
                callInfo.IsVirtualInspection = !DBRecordExtensions.HasColumn(dr, "IsVirtualInspection") ? false : Convert.ToBoolean(dr["IsVirtualInspection"]);
                callInfo.IsRevView = !DBRecordExtensions.HasColumn(dr, "IsRevView") ? false : Convert.ToBoolean(dr["IsRevView"]);
                callInfo.RevViewFileName = DBRecordExtensions.HasColumn(dr, "RevViewFileName") ? Convert.ToString(dr["RevViewFileName"]) : string.Empty;
                callInfo.RevViewStartTime = DBRecordExtensions.HasColumn(dr, "RevViewStartTime") ? Convert.ToString(dr["RevViewStartTime"]) : string.Empty;
                callInfo.RevViewPhoneNumber = DBRecordExtensions.HasColumn(dr, "RevViewPhoneNumber") ? Convert.ToString(dr["RevViewPhoneNumber"]) : string.Empty;
                callInfo.RevViewAgentName = DBRecordExtensions.HasColumn(dr, "RevViewAgentName") ? Convert.ToString(dr["RevViewAgentName"]) : string.Empty;
                callInfo.STTFileName = DBRecordExtensions.HasColumn(dr, "STTFilePath") ? Convert.ToString(dr["STTFilePath"]) : string.Empty;
                callInfo.CallTagging = DBRecordExtensions.HasColumn(dr, "TagName") ? Convert.ToString(dr["TagName"]) : string.Empty;
                callInfo.TagName = DBRecordExtensions.HasColumn(dr, "TagName") ? Convert.ToString(dr["TagName"]) : string.Empty;
                callInfo.TagColorID = DBRecordExtensions.HasColumn(dr, "TagColorID") ? Convert.ToString(dr["TagColorID"]) : string.Empty;

                calls.Add(callInfo);
            }
            return calls;
        }

        public static CallInfo MapCallInfo(SqlDataReader dr, int recId, string recName)
        {
            CallInfo callInfo = null;
            while (dr.Read())
            {
                callInfo = new CallInfo();

                callInfo.RowNo = (long)dr["RowNo"];

                callInfo.CallId = Convert.ToString(dr["CallID"]);
                callInfo.UniqueId = dr.IsDBNull(dr.GetOrdinal("UniqueId")) ? 0 : Convert.ToInt64(dr["UniqueId"]); //Convert.ToInt64(dr["UniqueId"]);
                callInfo.RecorderId = recId;
                callInfo.RecorderName = recName;// Convert.ToString(dr["RecName"]);
                callInfo.GroupId = Convert.ToInt32(dr["GroupNum"]);
                callInfo.GroupName = Convert.ToString(dr["GroupName"]);
                callInfo.AgentId = Convert.ToInt32(dr["UserNum"]);
                callInfo.ChannelId = Convert.ToInt32(dr["Ext"]);
                callInfo.ChannelName = Convert.ToString(dr["ExtName"]);
                callInfo.CallType = Convert.ToInt32(dr["CallType"]);
                //callInfo.StartTime = Convert.ToDateTime(dr["StartTime"]);
                callInfo.StartTime = dr["StartTime"].ToString().ConvertStringDateTimeToDateTime();
                callInfo.StartTimeString = Convert.ToString(dr["StartTime"]);
                callInfo.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);
                callInfo.FileName = Convert.ToString(dr["FileName"]);

                if (callInfo.CallType != 7)
                {
                    callInfo.CallType_inq = callInfo.CallType;
                }
                else
                {
                    if (Regex.IsMatch(callInfo.FileName, "mp4", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "mov", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "m4v", RegexOptions.IgnoreCase))
                    {
                        callInfo.CallType_inq = 8;
                    }
                    else if (Regex.IsMatch(callInfo.FileName, "zip", RegexOptions.IgnoreCase))
                    {
                        callInfo.CallType_inq = 12;
                    }
                    else
                    {
                        callInfo.CallType_inq = 7;
                    }
                }

                callInfo.CustName = Convert.ToString(dr["CustName"]);
                callInfo.ANI = Convert.ToString(dr["ANI"]);
                callInfo.CallerID = Convert.ToString(dr["CallerID"]);
                callInfo.CalledID = Convert.ToString(dr["CalledID"]);
                callInfo.SGroupID = dr.IsDBNull(dr.GetOrdinal("SGroupID")) ? 0 : Convert.ToInt32(dr["SGroupID"]);
                callInfo.Tag1 = Convert.ToString(dr["Tag1"]);
                callInfo.Tag2 = Convert.ToString(dr["Tag2"]);
                callInfo.Tag3 = Convert.ToString(dr["Tag3"]);
                callInfo.Tag4 = Convert.ToString(dr["Tag4"]);
                callInfo.ANIPhone = Convert.ToString(dr["ANI_PH"]);
                callInfo.ANIName = Convert.ToString(dr["ANI_NAME"]);
                callInfo.ANIDetails = Convert.ToString(dr["ANI_DETAILS"]);
                callInfo.CallComments = Convert.ToString(dr["CALL_COMMENT"]);
                callInfo.MessageBody = Convert.ToString(dr["Tag2"]);
                callInfo.ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]);// dr.IsDBNull(dr.GetOrdinal("ScreenFileNames")) ? null : Convert.ToString(dr["ScreenFileNames"]).Split(',').ToList();
                callInfo.RetainValue = Convert.ToInt32(dr["RetainValue"]);
                callInfo.BookmarkCSV = string.Empty;
                if (dr.FieldExists("BookMarkXML"))
                {
                    callInfo.BookmarkXML = Convert.ToString(dr["BookMarkXML"]);
                    if (!String.IsNullOrEmpty(callInfo.BookmarkXML))
                    {
                        callInfo.BookmarkCSV = callInfo.BookmarkXML.ConvertXmlStringToCsvString();
                    }
                }
                callInfo.IsPictureEvent = !DBRecordExtensions.HasColumn(dr, "IsPictureEvent") ? false : Convert.ToBoolean(dr["IsPictureEvent"]);
                callInfo.IsVirtualInspection = !DBRecordExtensions.HasColumn(dr, "IsVirtualInspection") ? false : Convert.ToBoolean(dr["IsVirtualInspection"]);
                callInfo.IsRevView = !DBRecordExtensions.HasColumn(dr, "IsRevView") ? false : Convert.ToBoolean(dr["IsRevView"]);
                callInfo.RevViewFileName = DBRecordExtensions.HasColumn(dr, "RevViewFileName") ? Convert.ToString(dr["RevViewFileName"]) : string.Empty;
                callInfo.RevViewStartTime = DBRecordExtensions.HasColumn(dr, "RevViewStartTime") ? Convert.ToString(dr["RevViewStartTime"]) : string.Empty;
                callInfo.RevViewPhoneNumber = DBRecordExtensions.HasColumn(dr, "RevViewPhoneNumber") ? Convert.ToString(dr["RevViewPhoneNumber"]) : string.Empty;
                callInfo.RevViewAgentName = DBRecordExtensions.HasColumn(dr, "RevViewAgentName") ? Convert.ToString(dr["RevViewAgentName"]) : string.Empty;
                callInfo.STTFileName = DBRecordExtensions.HasColumn(dr, "STTFilePath") ? Convert.ToString(dr["STTFilePath"]) : string.Empty;
                callInfo.CallTagging = DBRecordExtensions.HasColumn(dr, "TagName") ? Convert.ToString(dr["TagName"]) : string.Empty;
                callInfo.TagName = DBRecordExtensions.HasColumn(dr, "TagName") ? Convert.ToString(dr["TagName"]) : string.Empty;
                callInfo.TagColorID = DBRecordExtensions.HasColumn(dr, "TagColorID") ? Convert.ToString(dr["TagColorID"]) : string.Empty;
            }
            return callInfo;
        }
        public static List<CallInfo> MapCallInfosForPrimaryDB(SqlDataReader dr)
        {
            CallInfo callInfo = null;
            List<CallInfo> callInfos = new List<CallInfo>();
            while (dr.Read())
            {
                callInfo = new CallInfo();

                callInfo.RowNo = (long)dr["RowNo"];

                callInfo.GroupName = Convert.ToString(dr["GroupName"]);
                callInfo.AgentId = Convert.ToInt32(dr["UserNum"]);
                callInfo.ChannelName = Convert.ToString(dr["ChannelName"]);
                callInfo.StartTime = dr["StartTime"].ToString().ConvertStringDateTimeToDateTime();
                callInfo.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);
                callInfo.CalledID = Convert.ToString(dr["CalledID"]);
                callInfo.Tag1 = Convert.ToString(dr["Tag1"]);
                callInfo.Tag2 = Convert.ToString(dr["Tag2"]);
                callInfo.Tag3 = Convert.ToString(dr["Tag3"]);
                callInfo.Tag4 = Convert.ToString(dr["Tag4"]);
                callInfo.Tag5 = Convert.ToString(dr["Tag5"]);
                callInfo.Tag6 = Convert.ToString(dr["Tag6"]);
                callInfo.Tag7 = Convert.ToString(dr["Tag7"]);
                callInfo.Tag8 = Convert.ToString(dr["Tag8"]);
                callInfo.Tag9 = Convert.ToString(dr["Tag9"]);
                callInfo.Tag10 = Convert.ToString(dr["Tag10"]);
                callInfo.Tag11 = Convert.ToString(dr["Tag11"]);
                callInfo.Tag12 = Convert.ToString(dr["Tag12"]);
                callInfo.Tag13 = Convert.ToString(dr["Tag13"]);
                callInfo.Tag14 = Convert.ToString(dr["Tag14"]);
                callInfo.Tag15 = Convert.ToString(dr["Tag15"]);
                callInfo.Tag16 = Convert.ToString(dr["Tag16"]);

                callInfo.ANIPhone = Convert.ToString(dr["ANI_PH"]);
                callInfo.ANIName = Convert.ToString(dr["ANI_NAME"]);
                callInfo.ANIDetails = Convert.ToString(dr["ANI_DETAILS"]);
                callInfo.CallComments = Convert.ToString(dr["CALL_COMMENT"]);
                callInfo.BookmarkCSV = Convert.ToString(dr["BookmarkCSV"]);
                callInfo.RecorderName = Convert.ToString(dr["RecName"]);

                callInfo.interview_Interviewer = !dr.FieldExists("Interviewer") ? "" : Convert.ToString(dr["Interviewer"]);
                callInfo.interview_Interviewee = !dr.FieldExists("Interviewee") ? "" : Convert.ToString(dr["Interviewee"]);
                callInfo.interview_Notes = !dr.FieldExists("Notes") ? "" : Convert.ToString(dr["Notes"]);
                callInfo.VesselId = !dr.FieldExists("VesselID") ? "" : Convert.ToString(dr["VesselID"]);
                callInfo.AssetId = DBRecordExtensions.HasColumn(dr, "AssetId") ? Convert.ToString(dr["AssetId"]) : string.Empty;
                callInfos.Add(callInfo);
            }
            return callInfos;
        }
        public static List<CallInfoExportResult> MapCallInfoExportResults(SqlDataReader dr)
        {
            CallInfoExportResult callInfoExportResult = null;
            List<CallInfoExportResult> callInfoExportResults = new List<CallInfoExportResult>();
            while (dr.Read())
            {
                callInfoExportResult = new CallInfoExportResult();

                callInfoExportResult.RowNo = (long)dr["RowNo"];

                callInfoExportResult.GroupName = Convert.ToString(dr["GroupName"]);
                callInfoExportResult.AgentId = Convert.ToInt32(dr["UserNum"]);
                callInfoExportResult.ChannelName = Convert.ToString(dr["ChannelName"]);
                callInfoExportResult.StartTime = dr["StartTime"].ToString().ConvertStringDateTimeToDateTime();
                callInfoExportResult.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);
                callInfoExportResult.CalledID = Convert.ToString(dr["CalledID"]);
                callInfoExportResult.Tag1 = Convert.ToString(dr["Tag1"]);
                callInfoExportResult.Tag2 = Convert.ToString(dr["Tag2"]);
                callInfoExportResult.Tag3 = Convert.ToString(dr["Tag3"]);
                callInfoExportResult.Tag4 = Convert.ToString(dr["Tag4"]);
                callInfoExportResult.Tag5 = Convert.ToString(dr["Tag5"]);
                callInfoExportResult.Tag6 = Convert.ToString(dr["Tag6"]);
                callInfoExportResult.Tag7 = Convert.ToString(dr["Tag7"]);
                callInfoExportResult.Tag8 = Convert.ToString(dr["Tag8"]);
                callInfoExportResult.Tag9 = Convert.ToString(dr["Tag9"]);
                callInfoExportResult.Tag10 = Convert.ToString(dr["Tag10"]);
                callInfoExportResult.Tag11 = Convert.ToString(dr["Tag11"]);
                callInfoExportResult.Tag12 = Convert.ToString(dr["Tag12"]);
                callInfoExportResult.Tag13 = Convert.ToString(dr["Tag13"]);
                callInfoExportResult.Tag14 = Convert.ToString(dr["Tag14"]);
                callInfoExportResult.Tag15 = Convert.ToString(dr["Tag15"]);
                callInfoExportResult.Tag16 = Convert.ToString(dr["Tag16"]);

                callInfoExportResult.ANIPhone = Convert.ToString(dr["ANI_PH"]);
                callInfoExportResult.ANIName = Convert.ToString(dr["ANI_NAME"]);
                callInfoExportResult.ANIDetails = Convert.ToString(dr["ANI_DETAILS"]);
                callInfoExportResult.CallComments = Convert.ToString(dr["CALL_COMMENT"]);

                callInfoExportResult.BookmarkCSV = Convert.ToString(dr["BookmarkCSV"]);
                callInfoExportResult.RecorderName = Convert.ToString(dr["RecName"]);

                callInfoExportResults.Add(callInfoExportResult);
            }
            return callInfoExportResults;
        }
        public static List<CallInfoExportResult> MapCallInfoExportResults(string recorderName, SqlDataReader dr)
        {
            CallInfoExportResult callInfoExportResult = null;
            List<CallInfoExportResult> callInfoExportResults = new List<CallInfoExportResult>();
            while (dr.Read())
            {
                callInfoExportResult = new CallInfoExportResult();

                callInfoExportResult.RowNo = (long)dr["RowNo"];

                callInfoExportResult.GroupName = Convert.ToString(dr["GroupName"]);
                callInfoExportResult.AgentId = Convert.ToInt32(dr["UserNum"]);
                callInfoExportResult.ChannelName = Convert.ToString(dr["ChannelName"]);
                callInfoExportResult.StartTime = dr["StartTime"].ToString().ConvertStringDateTimeToDateTime();
                callInfoExportResult.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);
                callInfoExportResult.CalledID = Convert.ToString(dr["CalledID"]);
                callInfoExportResult.Tag4 = Convert.ToString(dr["Tag4"]);
                callInfoExportResult.Tag5 = Convert.ToString(dr["Tag5"]);
                callInfoExportResult.Tag6 = Convert.ToString(dr["Tag6"]);
                callInfoExportResult.Tag7 = Convert.ToString(dr["Tag7"]);
                callInfoExportResult.Tag8 = Convert.ToString(dr["Tag8"]);
                callInfoExportResult.Tag9 = Convert.ToString(dr["Tag9"]);
                callInfoExportResult.Tag10 = Convert.ToString(dr["Tag10"]);
                callInfoExportResult.Tag11 = Convert.ToString(dr["Tag11"]);
                callInfoExportResult.Tag12 = Convert.ToString(dr["Tag12"]);
                callInfoExportResult.Tag13 = Convert.ToString(dr["Tag13"]);
                callInfoExportResult.Tag14 = Convert.ToString(dr["Tag14"]);
                callInfoExportResult.Tag15 = Convert.ToString(dr["Tag15"]);
                callInfoExportResult.Tag16 = Convert.ToString(dr["Tag16"]);

                callInfoExportResult.ANIPhone = Convert.ToString(dr["ANI_PH"]);
                callInfoExportResult.ANIName = Convert.ToString(dr["ANI_NAME"]);
                callInfoExportResult.ANIDetails = Convert.ToString(dr["ANI_DETAILS"]);
                callInfoExportResult.CallComments = Convert.ToString(dr["CALL_COMMENT"]);

                callInfoExportResult.BookmarkCSV = Convert.ToString(dr["BookmarkCSV"]);
                callInfoExportResult.RecorderName = recorderName;

                callInfoExportResults.Add(callInfoExportResult);
            }
            return callInfoExportResults;
        }
        public static List<CallInfo> MapDummyCallInfos(SqlDataReader dr)
        {
            CallInfo callInfo = null;
            List<CallInfo> calls = new List<CallInfo>();
            while (dr.Read())
            {
                callInfo = new CallInfo();

                callInfo.RowNo = (long)dr["RowNo"];

                callInfo.CallId = Convert.ToString(dr["CallID"]);
                callInfo.UniqueId = dr.IsDBNull(dr.GetOrdinal("UniqueId")) ? 0 : Convert.ToInt64(dr["UniqueId"]); //Convert.ToInt64(dr["UniqueId"]);
                callInfo.RecorderId = Convert.ToInt32(dr["RecID"]);
                callInfo.GroupId = Convert.ToInt32(dr["GroupNum"]);
                callInfo.GroupName = Convert.ToString(dr["GroupName"]);
                callInfo.AgentId = Convert.ToInt32(dr["UserNum"]);
                callInfo.ChannelId = Convert.ToInt32(dr["Ext"]);
                callInfo.ChannelName = Convert.ToString(dr["ExtName"]);
                callInfo.CallType = Convert.ToInt32(dr["CallType"]);
                //callInfo.StartTime = Convert.ToDateTime(dr["StartTime"]);
                callInfo.StartTime = dr["StartTime"].ToString().ConvertStringDateTimeToDateTime();
                callInfo.StartTimeString = Convert.ToString(dr["StartTime"]);
                callInfo.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);
                callInfo.FileName = Convert.ToString(dr["FileName"]);
                if (callInfo.CallType != 7)
                {
                    callInfo.CallType_inq = callInfo.CallType;
                }
                else
                {
                    if (Regex.IsMatch(callInfo.FileName, "mp4", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "mov", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "m4v", RegexOptions.IgnoreCase))
                    {
                        callInfo.CallType_inq = 8;
                    }
                    else if (Regex.IsMatch(callInfo.FileName, "zip", RegexOptions.IgnoreCase))
                    {
                        callInfo.CallType_inq = 12;
                    }
                    else
                    {
                        callInfo.CallType_inq = 7;
                    }
                }
                callInfo.CustName = Convert.ToString(dr["CustName"]);
                callInfo.ANI = Convert.ToString(dr["ANI"]);
                callInfo.CallerID = Convert.ToString(dr["CallerID"]);
                callInfo.CalledID = Convert.ToString(dr["CalledID"]);
                callInfo.SGroupID = dr.IsDBNull(dr.GetOrdinal("SGroupID")) ? 0 : Convert.ToInt32(dr["SGroupID"]);
                callInfo.Tag1 = Convert.ToString(dr["Tag1"]);
                callInfo.Tag2 = Convert.ToString(dr["Tag2"]);
                callInfo.Tag3 = Convert.ToString(dr["Tag3"]);
                callInfo.Tag4 = Convert.ToString(dr["Tag4"]);
                callInfo.ANIPhone = Convert.ToString(dr["ANI_PH"]);
                callInfo.ANIName = Convert.ToString(dr["ANI_NAME"]);
                callInfo.ANIDetails = Convert.ToString(dr["ANI_DETAILS"]);
                callInfo.MessageBody = Convert.ToString(dr["CALL_COMMENT"]);
                callInfo.CallComments = Convert.ToString(dr["CALL_COMMENT"]);

                if (callInfo.CallType > 1)
                {
                    callInfo.CallComments = string.Empty;
                    callInfo.MessageBody = Convert.ToString(dr["CALL_COMMENT"]);
                }

                //callInfo.ScreenRecFile = Convert.ToString(dr["Screen_Rec_File"]);
                callInfo.ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]);// dr.IsDBNull(dr.GetOrdinal("ScreenFileNames")) ? null : Convert.ToString(dr["ScreenFileNames"]).Split(',').ToList();
                callInfo.RetainValue = Convert.ToInt32(dr["RetainValue"]);

                if (dr.FieldExists("BookMarkXML"))
                    callInfo.BookmarkXML = Convert.ToString(dr["BookMarkXML"]);

                callInfo.IsPictureEvent = false;
                callInfo.IsVirtualInspection = false;
                callInfo.IsRevView = false;
                calls.Add(callInfo);
            }
            return calls;
        }


        #endregion

        #region Playlist Details

        //public static List<PlaylistDetail> MapPlayListDetails(SqlDataReader dr)
        //{
        //    PlaylistDetail pld = null;
        //    List<PlaylistDetail> plds = new List<PlaylistDetail>();
        //    while (dr.Read())
        //    {
        //        pld = new PlaylistDetail();

        //        pld.Id = Convert.ToInt32(dr["PlaylistDetailId"]);

        //        pld.CallInfo = new CallInfo();
        //        pld.CallInfo.CallId = Convert.ToString(dr["CallID"]);
        //        pld.CallInfo.RecorderId = Convert.ToInt32(dr["RecID"]);
        //        pld.CallInfo.GroupId = Convert.ToInt32(dr["GroupNum"]);
        //        pld.CallInfo.GroupName = Convert.ToString(dr["GroupName"]);
        //        pld.CallInfo.AgentId = Convert.ToInt32(dr["UserNum"]);
        //        pld.CallInfo.ChannelId = Convert.ToInt32(dr["Ext"]);
        //        pld.CallInfo.ChannelName = Convert.ToString(dr["ExtName"]);
        //        pld.CallInfo.CallType = Convert.ToInt32(dr["CallType"]);
        //        //pld.CallInfo.StartTime = Convert.ToDateTime(dr["StartTime"]);
        //        pld.CallInfo.StartTime = dr["StartTime"].ToString().ConvertStringDateTimeToDateTime();
        //        pld.CallInfo.StartTimeString = Convert.ToString(dr["StartTime"]);
        //        pld.CallInfo.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);
        //        pld.CallInfo.FileName = Convert.ToString(dr["FileName"]);
        //        pld.CallInfo.CustName = Convert.ToString(dr["CustName"]);
        //        pld.CallInfo.ANI = Convert.ToString(dr["ANI"]);
        //        pld.CallInfo.CallerID = Convert.ToString(dr["CallerID"]);
        //        pld.CallInfo.CalledID = Convert.ToString(dr["CalledID"]);
        //        pld.CallInfo.SGroupID = dr.IsDBNull(dr.GetOrdinal("SGroupID")) ? 0 : Convert.ToInt32(dr["SGroupID"]);
        //        pld.CallInfo.Tag1 = Convert.ToString(dr["Tag1"]);
        //        pld.CallInfo.Tag2 = Convert.ToString(dr["Tag2"]);
        //        pld.CallInfo.Tag3 = Convert.ToString(dr["Tag3"]);
        //        pld.CallInfo.Tag4 = Convert.ToString(dr["Tag4"]);
        //        pld.CallInfo.ANIPhone = Convert.ToString(dr["ANI_PH"]);
        //        pld.CallInfo.ANIName = Convert.ToString(dr["ANI_NAME"]);
        //        pld.CallInfo.ANIDetails = Convert.ToString(dr["ANI_DETAILS"]);
        //        pld.CallInfo.CallComments = Convert.ToString(dr["CALL_COMMENT"]);

        //        if (pld.CallInfo.CallType > 1)
        //        {
        //            pld.CallInfo.CallComments = string.Empty;
        //            pld.CallInfo.MessageBody = Convert.ToString(dr["CALL_COMMENT"]);
        //        }
        //        pld.CallInfo.ScreenRecFile = Convert.ToString(dr["Screen_Rec_File"]);
        //        if (!string.IsNullOrEmpty(pld.CallInfo.ScreenRecFile) && pld.CallInfo.CallType == 1)
        //            pld.CallInfo.CallType = 6;

        //        pld.CallInfo.RetainValue = Convert.ToInt32(dr["RetainValue"]);

        //        plds.Add(pld);
        //    }
        //    return plds;
        //}


        ////// For Demo Purpose only
        //public static List<PlaylistDetail> MapPlayListDetailsForDemo(SqlDataReader dr)
        //{
        //    PlaylistDetail pld = null;
        //    List<PlaylistDetail> plds = new List<PlaylistDetail>();
        //    while (dr.Read())
        //    {
        //        pld = new PlaylistDetail();

        //        pld.Id = Convert.ToInt32(dr["PlaylistDetailId"]);

        //        pld.CallInfo = new CallInfo();
        //        //pld.CallInfo.CallId = Convert.ToString(dr["CallID"]);//null - restore after demo
        //        pld.CallInfo.CallId = Convert.ToString(dr["PldCallId"]);
        //        pld.CallInfo.RecorderId = dr.IsDBNull(dr.GetOrdinal("RecID")) ? 0 : Convert.ToInt32(dr["RecID"]);
        //        pld.CallInfo.GroupId = dr.IsDBNull(dr.GetOrdinal("GroupNum")) ? 0 : Convert.ToInt32(dr["GroupNum"]);
        //        pld.CallInfo.GroupName = Convert.ToString(dr["GroupName"]);
        //        pld.CallInfo.AgentId = dr.IsDBNull(dr.GetOrdinal("UserNum")) ? 1000 : Convert.ToInt32(dr["UserNum"]);
        //        pld.CallInfo.ChannelId = dr.IsDBNull(dr.GetOrdinal("Ext")) ? 0 : Convert.ToInt32(dr["Ext"]);
        //        pld.CallInfo.ChannelName = Convert.ToString(dr["ExtName"]);
        //        pld.CallInfo.FileName = !dr.IsDBNull(dr.GetOrdinal("CallType")) ? Convert.ToString(dr["FileName"]) : "";

        //        pld.CallInfo.CallType = dr.IsDBNull(dr.GetOrdinal("CallType")) ? 0 : Convert.ToInt32(dr["CallType"]);
        //        pld.CallInfo.StartTime = dr.IsDBNull(dr.GetOrdinal("StartTime")) ? DateTime.Now : dr["StartTime"].ToString().ConvertStringDateTimeToDateTime();
        //        pld.CallInfo.StartTimeString = Convert.ToString(dr["StartTime"]);
        //        pld.CallInfo.DurationInMilliSeconds = dr.IsDBNull(dr.GetOrdinal("Duration")) ? 0 : Convert.ToInt32(dr["Duration"]);
        //        //pld.CallInfo.BackupID = Convert.ToInt32(dr["BackupID"]);
        //        pld.CallInfo.CustName = Convert.ToString(dr["CustName"]);
        //        pld.CallInfo.ANI = Convert.ToString(dr["ANI"]);
        //        pld.CallInfo.CallerID = Convert.ToString(dr["CallerID"]);
        //        pld.CallInfo.CalledID = Convert.ToString(dr["CalledID"]);
        //        pld.CallInfo.SGroupID = dr.IsDBNull(dr.GetOrdinal("SGroupID")) ? 0 : Convert.ToInt32(dr["SGroupID"]);
        //        pld.CallInfo.Tag1 = Convert.ToString(dr["Tag1"]);
        //        pld.CallInfo.Tag2 = Convert.ToString(dr["Tag2"]);
        //        pld.CallInfo.Tag3 = Convert.ToString(dr["Tag3"]);
        //        pld.CallInfo.Tag4 = Convert.ToString(dr["Tag4"]);
        //        pld.CallInfo.ANIPhone = Convert.ToString(dr["ANI_PH"]);
        //        pld.CallInfo.ANIName = Convert.ToString(dr["ANI_NAME"]);
        //        pld.CallInfo.ANIDetails = Convert.ToString(dr["ANI_DETAILS"]);
        //        pld.CallInfo.CallComments = Convert.ToString(dr["CALL_COMMENT"]);
        //        if (pld.CallInfo.CallType > 1)
        //        {
        //            pld.CallInfo.CallComments = string.Empty;
        //            pld.CallInfo.MessageBody = Convert.ToString(dr["CALL_COMMENT"]);
        //        }
        //        pld.CallInfo.ScreenRecFile = Convert.ToString(dr["Screen_Rec_File"]);
        //        if (!string.IsNullOrEmpty(pld.CallInfo.ScreenRecFile) && pld.CallInfo.CallType == 1)
        //            pld.CallInfo.CallType = 6;
        //        //pld.CallInfo.RetainValue = Convert.ToInt32(dr["RetainValue"]);
        //        pld.CallInfo.RetainValue = dr.IsDBNull(dr.GetOrdinal("RetainValue")) ? 0 : Convert.ToInt32(dr["RetainValue"]);

        //        plds.Add(pld);
        //    }
        //    return plds;
        //}



        #endregion

        #region User Management

        public static List<GlobalGroup> MapGlobalGroups(SqlDataReader dr)
        {
            List<GlobalGroup> groups = new List<GlobalGroup>();
            GlobalGroup group = null;
            while (dr.Read())
            {
                group = new GlobalGroup();
                group.Id = (int)dr["GroupNum"];
                group.ParentId = Convert.ToInt32(dr["ParentGroup"]);
                group.Name = Convert.ToString(dr["GroupName"]);
                group.Description = Convert.ToString(dr["Descr"]);
                group.Depth = Convert.ToInt32(dr["Depth"]);
                group.Ordering = Convert.ToInt32(dr["IndexNum"]);
                group.IsActive = Convert.ToBoolean(dr["Status"]);
                //group.CreatedDate = Convert.ToDateTime(dr["Create_T"]);
                //group.ModifiedDate = dr["Modify_T"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["ModifiedDate"]);
                //group.DeletedDate = dr["Delete_T"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["DeletedDate"]);
                group.IsDeleteable = Convert.ToBoolean(dr["IsDeleteable"]);
                group.IsDeleted = Convert.ToBoolean(dr["Status"]);


                groups.Add(group);
            }
            return groups;
        }

        public static List<AppUser> MapAppUsers(SqlDataReader dr)
        {
            List<AppUser> appUsers = new List<AppUser>();
            AppUser appUser = null;
            while (dr.Read())
            {
                appUser = new AppUser();

                appUser.Id = (int)dr["Id"];
                appUser.GroupId = dr["GroupId"] == DBNull.Value ? 0 : (int)dr["GroupId"];
                appUser.UserName = Convert.ToString(dr["UserName"]);
                appUser.Email = Convert.ToString(dr["Email"]);
                appUser.Password = Convert.ToString(dr["Password"]);
                appUser.PasswordQuestion = Convert.ToString(dr["PasswordQuestion"]);
                appUser.PasswordAnswer = Convert.ToString(dr["PasswordAnswer"]);
                appUser.IsApproved = Convert.ToBoolean(dr["IsApproved"]);
                appUser.LastLoginDate = dr["LastLoginDate"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["LastLoginDate"]);
                appUser.IsLockOut = Convert.ToBoolean(dr["IsLockOut"]);
                appUser.LastLockOutDate = dr["LastLockOutDate"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["LastLockOutDate"]);
                appUser.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                appUser.ModifiedDate = dr["ModifiedDate"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["ModifiedDate"]);
                appUser.DeletedDate = dr["DeletedDate"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["DeletedDate"]);
                //appUser.CreatedBy = Convert.ToInt64(dr["CreatedBy"]);
                //appUser.ModifiedBy = Convert.ToInt64(dr["ModifiedBy"]);
                appUser.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);

                appUsers.Add(appUser);
            }
            return appUsers;
        }

        public static List<User> MapUsers(SqlDataReader dr)
        {
            List<User> users = new List<User>();
            User user = null;
            try
            {
                while (dr.Read())
                {
                    user = new User();
                    user.UserNum = (int)dr["UserNum"];
                    user.GroupNum = dr["GroupNum"] == DBNull.Value ? 0 : (int)dr["GroupNum"];
                    user.UserType = (int)dr["UserType"];
                    user.UserName = Convert.ToString(dr["UserName"]);
                    user.UserID = Convert.ToString(dr["UserID"]);
                    user.UserPW = Convert.ToString(dr["UserPW"]);
                    user.UserName = Convert.ToString(dr["UserName"]);
                    user.ViewID = Convert.ToInt32(dr["ViewID"]);
                    user.Ext = Convert.ToString(dr["Ext"]);
                    user.UserEmail = Convert.ToString(dr["UserEmail"]);
                    user.Status = Convert.ToInt32(dr["Status"]);
                    user.JoinBeginDate = dr["JoinBeginDate"] == DBNull.Value || Convert.ToString(dr["JoinBeginDate"]).Trim() == ""
                        ? default(DateTime) : (Convert.ToString(dr["JoinBeginDate"]) + "000000").ConvertStringDateTimeToDateTime();
                    user.JoinEndDate = dr["JoinEndDate"] == DBNull.Value || Convert.ToString(dr["JoinBeginDate"]).Trim() == ""
                        ? default(DateTime) : (Convert.ToString(dr["JoinEndDate"]) + "000000").ConvertStringDateTimeToDateTime();
                    user.Create_T = Convert.ToString(dr["Create_T"]).ConvertStringDateTimeToDateTime();
                    user.Modify_T = dr["Modify_T"] == DBNull.Value || Convert.ToString(dr["Modify_T"]).Trim() == String.Empty
                        ? default(DateTime) : Convert.ToString(dr["Modify_T"]).ConvertStringDateTimeToDateTime();
                    user.Delete_T = dr["Delete_T"] == DBNull.Value || Convert.ToString(dr["Delete_T"]).Trim() == ""
                        ? default(DateTime) : Convert.ToString(dr["Delete_T"]).ConvertStringDateTimeToDateTime();
                    user.UserPhone = Convert.ToString(dr["UserPhone"]);
                    user.UserFax = Convert.ToString(dr["UserFax"]);
                    user.IdentityNumber = Convert.ToString(dr["IdentityNumber"]);
                    user.SearchRest = Convert.ToInt32(dr["SearchRest"]);
                    user.Pause = Convert.ToInt32(dr["Pause"]);
                    user.EOD = Convert.ToInt32(dr["EOD"]);
                    user.POD = Convert.ToInt32(dr["POD"]);
                    user.ExtName = Convert.ToString(dr["ExtName"]);
                    user.SelectType = Convert.ToInt32(dr["SelectType"]);
                    user.DNISCheck = Convert.ToInt32(dr["DNISCheck"]);
                    user.DNIS = Convert.ToString(dr["DNIS"]);
                    user.UserPic = Convert.ToString(dr["UserPic"]);
                    user.IsGroupBased = Convert.ToBoolean(dr["IsGroupBased"]);
                    user.IsDeviceUser = Convert.ToInt32(dr["IsDeviceUser"]); //Arivu : For Inquire Custom Marker Filtering
                    user.EnableDisableInquireUser = Convert.ToInt32(dr["Enable_User"]); //KM : For Inquire Custom Marker Filtering
                    user.IsAvrisView = !DBRecordExtensions.HasColumn(dr, "IsAvrisView") ? false : (dr["IsAvrisView"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsAvrisView"]));
                    user.IsIQ3View = !DBRecordExtensions.HasColumn(dr, "IsIQ3View") ? false : (dr["IsIQ3View"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsIQ3View"]));


                    user.CreatedBy = !DBRecordExtensions.HasColumn(dr, "CreatedBy") ? 0 : dr["CreatedBy"] == DBNull.Value ? 0 : Convert.ToInt32(dr["CreatedBy"]);
                    user.IsIwbUser = dr["IsIwbUser"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsIwbUser"]);
                    user.OrganizationId = dr["OrganizationId"] == DBNull.Value ? 0 : Convert.ToInt32(dr["OrganizationId"]);
                    user.DOB = dr["DOB"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["DOB"]);
                    user.City = Convert.ToString(dr["City"]);
                    user.State = Convert.ToString(dr["State"]);
                    user.SocialSecurityNumber = Convert.ToString(dr["SSN"]);
                    user.WelderStencilNumber = Convert.ToString(dr["StencilNumber"]);
                    user.CustomerIdZoho = !DBRecordExtensions.HasColumn(dr, "CustomerIdZoho") ? string.Empty : Convert.ToString(dr["CustomerIdZoho"]);

                    user.RoleId = !DBRecordExtensions.HasColumn(dr, "RoleId") ? 0 : (dr["RoleId"] == DBNull.Value ? 0 : Convert.ToInt32(dr["RoleId"]));
                    user.RoleName = !DBRecordExtensions.HasColumn(dr, "RoleName") ? "" : (dr["RoleName"] == DBNull.Value ? "" : Convert.ToString(dr["RoleName"]));
                    user.IsRevCell = Convert.ToBoolean(dr["IsRevCell"]);
                    //user.IsLockedOut = dr["IsLockedOut"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsLockedOut"]);
                    user.IsLockedOut = !DBRecordExtensions.HasColumn(dr, "IsLockedOut") ? false : dr["IsLockedOut"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsLockedOut"]);
                    user.Is2FAEnabled = !DBRecordExtensions.HasColumn(dr, "Is2FAEnabled") ? false : dr["Is2FAEnabled"] == DBNull.Value ? false : Convert.ToBoolean(dr["Is2FAEnabled"]);
                    user.AutoUpload = !DBRecordExtensions.HasColumn(dr, "AutoUpload") ? false : dr["AutoUpload"] == DBNull.Value ? false : Convert.ToBoolean(dr["AutoUpload"]);
                    user.DisableCustomAssetId = !DBRecordExtensions.HasColumn(dr, "DisableCustomAssetId") ? false : dr["DisableCustomAssetId"] == DBNull.Value ? false : Convert.ToBoolean(dr["DisableCustomAssetId"]);
                    if (user.UserType == 1)
                    {
                        user.UMUserType = "Admin";
                    }
                    else if (user.UserType == 0 && user.Ext == "0")
                    {
                        // user.UMUserType = "Viewer / User";
                        user.UMUserType = "User";
                    }
                    else if (user.UserType == 0 && user.IsDeviceUser == 0)
                    {
                        user.UMUserType = "Channel";
                    }
                    else if (user.UserType == 0 && user.IsDeviceUser == 1)
                    {
                        // user.UMUserType = "Inquire User";
                        user.UMUserType = "User";
                    }
                    else if (user.IsRevCell)
                        user.UMUserType = "User";
                    else if (user.UserType == 6)
                        user.UMUserType = "Owner";
                    else if (user.UserType == 7)
                        user.UMUserType = "Contractor";
                    else if (user.UserType == 8)
                        user.UMUserType = "Welder";

                    user.UserName = user.UserName.Replace("Agent", "Channel");
                    user.UserID = Convert.ToString(dr["UserID"]).Replace("Agent", "Channel");
                    if (!(user.UserType == 0 && user.IsDeviceUser == 0 && user.Ext.Trim() != "0"))
                        users.Add(user);
                    //user.QBUserId = dr["QBUserId"] == DBNull.Value ? 0 : Convert.ToInt32(dr["QBUserId"]);
                    //user.ExistsOnQB = dr["ExistsOnQB"] == DBNull.Value ? false : Convert.ToBoolean(dr["ExistsOnQB"]);
                    //user.IsEventSpecific = dr["IsEventSpecific"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsEventSpecific"]);
                    user.CanInvite = dr["CanInvite"] == DBNull.Value ? false : Convert.ToBoolean(dr["CanInvite"]);
                    user.IsEnterpriseUser = dr["IsEnterpriseUser"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsEnterpriseUser"]);
                    user.RevSyncServerUserNum = Convert.ToInt32(dr["RevSyncServerUserNum"]);
                    user.IsSyncedFromClient = Convert.ToBoolean(dr["IsSyncedFromClient"]);
                }
            }
            catch (Exception _exception)
            {
                throw _exception;
            }
            return users;
        }

        public static List<User> MapUsersAsAgents(SqlDataReader dr)
        {
            List<User> users = new List<User>();
            User user = null;
            try
            {
                while (dr.Read())
                {
                    user = new User();
                    user.UserNum = (int)dr["UserNum"];
                    user.UserType = (int)dr["UserType"];
                    user.UserName = Convert.ToString(dr["UserName"]);
                    user.UserID = Convert.ToString(dr["UserID"]);
                    user.UserPW = Convert.ToString(dr["UserPW"]);
                    user.UserName = Convert.ToString(dr["UserName"]);
                    user.ViewID = Convert.ToInt32(dr["ViewID"]);
                    user.Ext = Convert.ToString(dr["Ext"]);
                    user.UserEmail = Convert.ToString(dr["UserEmail"]);
                    user.Status = Convert.ToInt32(dr["Status"]);
                    user.SelectType = Convert.ToInt32(dr["SelectType"]);
                    users.Add(user);
                }
            }
            catch (Exception _exception)
            {
                throw _exception;
            }
            return users;
        }

        public static List<User> MapAllActiveUsers(SqlDataReader dr)
        {
            List<User> users = new List<User>();
            User user = null;
            try
            {
                while (dr.Read())
                {
                    user = new User();
                    user.UserNum = (int)dr["UserNum"];
                    user.UserID = Convert.ToString(dr["UserID"]);
                    user.UserName = Convert.ToString(dr["UserName"]);
                    user.UserEmail = Convert.ToString(dr["UserEmail"]);
                    users.Add(user);
                }
            }
            catch (Exception _exception)
            {
                throw _exception;
            }
            return users;
        }

        public static List<User> MapAllUsersWithPermission(SqlDataReader dr)
        {
            List<User> users = new List<User>();
            User user = null;
            try
            {
                while (dr.Read())
                {
                    user = new User();
                    user.UserNum = (int)dr["UserNum"];
                    user.UserType = (int)dr["UserType"];
                    user.UserName = Convert.ToString(dr["UserName"]);
                    user.UserID = Convert.ToString(dr["UserID"]);
                    user.UserPW = Convert.ToString(dr["UserPW"]);
                    user.UserName = Convert.ToString(dr["UserName"]);
                    user.ViewID = Convert.ToInt32(dr["ViewID"]);
                    user.Ext = Convert.ToString(dr["Ext"]);
                    user.UserEmail = Convert.ToString(dr["UserEmail"]);
                    user.Status = Convert.ToInt32(dr["Status"]);
                    user.SelectType = Convert.ToInt32(dr["SelectType"]);
                    user.PermissionStr = Convert.ToString(dr["Permission"]);
                    users.Add(user);
                }
            }
            catch (Exception _exception)
            {
                throw _exception;
            }
            return users;
        }

        public static User MapUserAsAgent(SqlDataReader dr)
        {
            User user = null;
            try
            {
                while (dr.Read())
                {
                    user = new User();
                    user.UserNum = (int)dr["UserNum"];
                    user.UserType = (int)dr["UserType"];
                    user.UserName = Convert.ToString(dr["UserName"]);
                    user.UserID = Convert.ToString(dr["UserID"]);
                    user.UserPW = Convert.ToString(dr["UserPW"]);
                    user.UserName = Convert.ToString(dr["UserName"]);
                    user.ViewID = Convert.ToInt32(dr["ViewID"]);
                    user.Ext = Convert.ToString(dr["Ext"]);
                    user.UserEmail = Convert.ToString(dr["UserEmail"]);
                    user.Status = Convert.ToInt32(dr["Status"]);
                    user.SelectType = Convert.ToInt32(dr["SelectType"]);
                }
            }
            catch (Exception _exception)
            {
                throw _exception;
            }
            return user;
        }

        public static User MapUser(SqlDataReader dr)
        {
            User user = null;
            try
            {
                while (dr.Read())
                {
                    user = new User();
                    user.UserNum = (int)dr["UserNum"];
                    user.GroupNum = dr["GroupNum"] == DBNull.Value ? 0 : (int)dr["GroupNum"];
                    user.UserType = (int)dr["UserType"];
                    user.UserName = Convert.ToString(dr["UserName"]);
                    user.UserID = Convert.ToString(dr["UserID"]);
                    user.UserPW = Convert.ToString(dr["UserPW"]);
                    user.UserName = Convert.ToString(dr["UserName"]);
                    user.ViewID = Convert.ToInt32(dr["ViewID"]);
                    user.Ext = Convert.ToString(dr["Ext"]);
                    user.UserEmail = Convert.ToString(dr["UserEmail"]);
                    user.Status = Convert.ToInt32(dr["Status"]);
                    user.JoinBeginDate = dr["JoinBeginDate"] == DBNull.Value || Convert.ToString(dr["JoinBeginDate"]).Trim() == ""
                        ? default(DateTime) : (Convert.ToString(dr["JoinBeginDate"]) + "000000").ConvertStringDateTimeToDateTime();
                    user.JoinEndDate = dr["JoinEndDate"] == DBNull.Value || Convert.ToString(dr["JoinBeginDate"]).Trim() == ""
                        ? default(DateTime) : (Convert.ToString(dr["JoinEndDate"]) + "000000").ConvertStringDateTimeToDateTime();
                    user.Create_T = Convert.ToString(dr["Create_T"]).ConvertStringDateTimeToDateTime();
                    user.Modify_T = dr["Modify_T"] == DBNull.Value || Convert.ToString(dr["Modify_T"]).Trim() == String.Empty
                        ? default(DateTime) : Convert.ToString(dr["Modify_T"]).ConvertStringDateTimeToDateTime();
                    user.Delete_T = dr["Delete_T"] == DBNull.Value || Convert.ToString(dr["Delete_T"]).Trim() == ""
                        ? default(DateTime) : Convert.ToString(dr["Delete_T"]).ConvertStringDateTimeToDateTime();
                    user.UserPhone = Convert.ToString(dr["UserPhone"]);
                    user.UserFax = Convert.ToString(dr["UserFax"]);
                    user.IdentityNumber = Convert.ToString(dr["IdentityNumber"]);
                    user.SearchRest = Convert.ToInt32(dr["SearchRest"]);
                    user.Pause = Convert.ToInt32(dr["Pause"]);
                    user.EOD = Convert.ToInt32(dr["EOD"]);
                    user.POD = Convert.ToInt32(dr["POD"]);
                    user.ExtName = Convert.ToString(dr["ExtName"]);
                    user.SelectType = Convert.ToInt32(dr["SelectType"]);
                    user.DNISCheck = Convert.ToInt32(dr["DNISCheck"]);
                    user.DNIS = Convert.ToString(dr["DNIS"]);
                    user.UserPic = Convert.ToString(dr["UserPic"]);
                    user.IsGroupBased = Convert.ToBoolean(dr["IsGroupBased"]);
                    user.IsDeviceUser = Convert.ToInt32(dr["IsDeviceUser"]); //Arivu : For Inquire Custom Marker Filtering
                    user.EnableDisableInquireUser = Convert.ToInt32(dr["Enable_User"]); //KM : For Inquire Custom Marker Filtering
                    user.IsAvrisView = !DBRecordExtensions.HasColumn(dr, "IsAvrisView") ? false : (dr["IsAvrisView"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsAvrisView"]));
                    user.IsIQ3View = !DBRecordExtensions.HasColumn(dr, "IsIQ3View") ? false : (dr["IsIQ3View"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsIQ3View"]));
                    user.IsRevCell = Convert.ToBoolean(dr["IsRevCell"]);
                    if (user.UserType == 1)
                    {
                        user.UMUserType = "Admin";
                    }
                    else if (user.UserType == 0 && user.Ext == "0")
                    {
                        // user.UMUserType = "Viewer / User";
                        user.UMUserType = "User";
                    }
                    else if (user.UserType == 0 && user.IsDeviceUser == 0)
                    {
                        user.UMUserType = "Channel";
                    }
                    else if (user.UserType == 0 && user.IsDeviceUser == 1)
                    {
                        // user.UMUserType = "Inquire User";
                        user.UMUserType = "User";
                    }
                    else if (user.IsRevCell)
                        user.UMUserType = "User";

                    user.UserName = user.UserName.Replace("Agent", "Channel");
                    user.UserID = Convert.ToString(dr["UserID"]).Replace("Agent", "Channel");
                    user.CanInvite = dr["CanInvite"] == DBNull.Value ? false : Convert.ToBoolean(dr["CanInvite"]);
                    user.IsEnterpriseUser = dr["IsEnterpriseUser"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsEnterpriseUser"]);
                    user.RevSyncServerUserNum = Convert.ToInt32(dr["RevSyncServerUserNum"]);
                    user.IsSyncedFromClient = Convert.ToBoolean(dr["IsSyncedFromClient"]);

                }
            }
            catch (Exception _exception)
            {
                throw _exception;
            }
            return user;
        }

        public static List<User> MapUsers(SqlDataReader dr, int recId, string recName)
        {
            List<User> users = new List<User>();
            User user = null;
            while (dr.Read())
            {
                user = new User();

                user.UserNum = (int)dr["UserNum"];
                user.RevSyncServerUserNum = (int)dr["RevSyncServerUserNum"];
                user.GroupNum = dr["GroupNum"] == DBNull.Value ? 0 : (int)dr["GroupNum"];
                user.UserType = (int)dr["UserType"];
                user.UserName = Convert.ToString(dr["UserName"]);
                user.UserID = Convert.ToString(dr["UserID"]);
                user.UserPW = Convert.ToString(dr["UserPW"]);
                user.UserName = Convert.ToString(dr["UserName"]);
                user.ViewID = Convert.ToInt32(dr["ViewID"]);
                user.Ext = Convert.ToString(dr["Ext"]);
                user.UserEmail = Convert.ToString(dr["UserEmail"]);
                user.Status = Convert.ToInt32(dr["Status"]);
                user.JoinBeginDate = dr["JoinBeginDate"] == DBNull.Value || Convert.ToString(dr["JoinBeginDate"]).Trim() == ""
                    ? default(DateTime) : (Convert.ToString(dr["JoinBeginDate"]) + "000000").ConvertStringDateTimeToDateTime();
                user.JoinEndDate = dr["JoinEndDate"] == DBNull.Value || Convert.ToString(dr["JoinBeginDate"]).Trim() == ""
                    ? default(DateTime) : (Convert.ToString(dr["JoinEndDate"]) + "000000").ConvertStringDateTimeToDateTime();
                user.Create_T = Convert.ToString(dr["Create_T"]).ConvertStringDateTimeToDateTime();
                user.Modify_T = dr["Modify_T"] == DBNull.Value || Convert.ToString(dr["Modify_T"]).Trim() == String.Empty
                    ? default(DateTime) : Convert.ToString(dr["Modify_T"]).ConvertStringDateTimeToDateTime();
                user.Delete_T = dr["Delete_T"] == DBNull.Value || Convert.ToString(dr["Delete_T"]).Trim() == ""
                    ? default(DateTime) : Convert.ToString(dr["Delete_T"]).ConvertStringDateTimeToDateTime();
                user.UserPhone = Convert.ToString(dr["UserPhone"]);
                user.UserFax = Convert.ToString(dr["UserFax"]);
                user.IdentityNumber = Convert.ToString(dr["IdentityNumber"]);
                user.SearchRest = Convert.ToInt32(dr["SearchRest"]);
                user.Pause = Convert.ToInt32(dr["Pause"]);
                user.EOD = Convert.ToInt32(dr["EOD"]);
                user.POD = Convert.ToInt32(dr["POD"]);
                user.ExtName = Convert.ToString(dr["ExtName"]);
                user.SelectType = Convert.ToInt32(dr["SelectType"]);
                user.DNISCheck = Convert.ToInt32(dr["DNISCheck"]);
                user.DNIS = Convert.ToString(dr["DNIS"]);
                user.UserPic = Convert.ToString(dr["UserPic"]);
                user.HasShiftRest = Convert.ToBoolean(dr["HasShiftRest"]);

                user.IsDeviceUser = Convert.ToInt32(dr["IsDeviceUser"]); //Arivu : For Inquire Custom Marker Filtering
                user.EnableDisableInquireUser = Convert.ToInt32(dr["Enable_User"]); //KM : For Inquire Custom Marker Filtering
                user.IsAvrisView = !DBRecordExtensions.HasColumn(dr, "IsAvrisView") ? false : (dr["IsAvrisView"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsAvrisView"]));
                user.IsIQ3View = !DBRecordExtensions.HasColumn(dr, "IsIQ3View") ? false : (dr["IsIQ3View"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsIQ3View"]));

                user.RoleId = !DBRecordExtensions.HasColumn(dr, "RoleId") ? 0 : (dr["RoleId"] == DBNull.Value ? 0 : Convert.ToInt32(dr["RoleId"]));
                user.RoleName = !DBRecordExtensions.HasColumn(dr, "RoleName") ? "" : (dr["RoleName"] == DBNull.Value ? "" : Convert.ToString(dr["RoleName"]));

                user.RecId = recId;
                user.RecName = recName;
                user.IsEnterpriseUser = Convert.ToBoolean(dr["IsEnterpriseUser"]);
                user.IsRevCell = Convert.ToBoolean(dr["IsRevCell"]);
                user.IsLockedOut = !DBRecordExtensions.HasColumn(dr, "IsLockedOut") ? false : dr["IsLockedOut"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsLockedOut"]);
                user.Is2FAEnabled = !DBRecordExtensions.HasColumn(dr, "Is2FAEnabled") ? false : dr["Is2FAEnabled"] == DBNull.Value ? false : Convert.ToBoolean(dr["Is2FAEnabled"]);

                user.CreatedBy = !DBRecordExtensions.HasColumn(dr, "CreatedBy") ? 0 : dr["CreatedBy"] == DBNull.Value ? 0 : Convert.ToInt32(dr["CreatedBy"]);
                user.IsIwbUser = dr["IsIwbUser"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsIwbUser"]);
                user.OrganizationId = dr["OrganizationId"] == DBNull.Value ? 0 : Convert.ToInt32(dr["OrganizationId"]);
                user.DOB = dr["DOB"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["DOB"]);
                user.City = Convert.ToString(dr["City"]);
                user.State = Convert.ToString(dr["State"]);
                user.SocialSecurityNumber = Convert.ToString(dr["SSN"]);
                user.WelderStencilNumber = Convert.ToString(dr["StencilNumber"]);
                user.CustomerIdZoho = !DBRecordExtensions.HasColumn(dr, "CustomerIdZoho") ? string.Empty : Convert.ToString(dr["CustomerIdZoho"]);

                //user.QBUserId = dr["QBUserId"] == DBNull.Value ? 0 : Convert.ToInt32(dr["QBUserId"]);
                //user.ExistsOnQB = dr["ExistsOnQB"] == DBNull.Value ? false : Convert.ToBoolean(dr["ExistsOnQB"]);
                //user.IsEventSpecific = dr["IsEventSpecific"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsEventSpecific"]);
                user.CanInvite = dr["CanInvite"] == DBNull.Value ? false : Convert.ToBoolean(dr["CanInvite"]);
                user.IsEnterpriseUser = dr["IsEnterpriseUser"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsEnterpriseUser"]);
                user.AutoUpload = !DBRecordExtensions.HasColumn(dr, "AutoUpload") ? false : dr["AutoUpload"] == DBNull.Value ? false : Convert.ToBoolean(dr["AutoUpload"]);
                user.DisableCustomAssetId = !DBRecordExtensions.HasColumn(dr, "DisableCustomAssetId") ? false : dr["DisableCustomAssetId"] == DBNull.Value ? false : Convert.ToBoolean(dr["DisableCustomAssetId"]);
                if (user.UserType == 1)
                {
                    user.UMUserType = "Admin";
                }
                else if (user.IsEnterpriseUser)
                    user.UMUserType = "Enterprise";
                else if (user.UserType == 0 && user.Ext == "0")
                {
                    // user.UMUserType = "Viewer / User";
                    user.UMUserType = "User";
                }
                else if (user.UserType == 0 && user.IsDeviceUser == 0)
                {
                    user.UMUserType = "Channel";
                }
                else if (user.UserType == 0 && user.IsDeviceUser == 1)
                {
                    // user.UMUserType = "Inquire User";
                    user.UMUserType = "User";
                }
                else if (user.UserType == 6)
                    user.UMUserType = "Owner";
                else if (user.UserType == 7)
                    user.UMUserType = "Contractor";
                else if (user.UserType == 8)
                    user.UMUserType = "Welder";
                if (!(user.UserType == 0 && user.IsDeviceUser == 0 && user.Ext.Trim() != "0"))
                    users.Add(user);
            }
            return users;
        }

        public static List<User> MapUsersWithoutChannel(SqlDataReader dr)
        {
            List<User> users = new List<User>();
            User user = null;
            while (dr.Read())
            {
                user = new User();

                user.UserNum = (int)dr["UserNum"];
                user.UserType = (int)dr["UserType"];
                user.UserName = Convert.ToString(dr["UserName"]);
                user.UserID = Convert.ToString(dr["UserID"]);
                user.UserPW = Convert.ToString(dr["UserPW"]);
                user.UserName = Convert.ToString(dr["UserName"]);
                user.ViewID = Convert.ToInt32(dr["ViewID"]);
                user.Ext = Convert.ToString(dr["Ext"]);
                user.UserEmail = Convert.ToString(dr["UserEmail"]);
                user.Status = Convert.ToInt32(dr["Status"]);
                user.JoinBeginDate = dr["JoinBeginDate"] == DBNull.Value || Convert.ToString(dr["JoinBeginDate"]).Trim() == ""
                    ? default(DateTime) : (Convert.ToString(dr["JoinBeginDate"]) + "000000").ConvertStringDateTimeToDateTime();
                user.JoinEndDate = dr["JoinEndDate"] == DBNull.Value || Convert.ToString(dr["JoinBeginDate"]).Trim() == ""
                    ? default(DateTime) : (Convert.ToString(dr["JoinEndDate"]) + "000000").ConvertStringDateTimeToDateTime();
                user.Create_T = Convert.ToString(dr["Create_T"]).ConvertStringDateTimeToDateTime();
                user.Modify_T = dr["Modify_T"] == DBNull.Value || Convert.ToString(dr["Modify_T"]).Trim() == String.Empty
                    ? default(DateTime) : Convert.ToString(dr["Modify_T"]).ConvertStringDateTimeToDateTime();
                user.Delete_T = dr["Delete_T"] == DBNull.Value || Convert.ToString(dr["Delete_T"]).Trim() == ""
                    ? default(DateTime) : Convert.ToString(dr["Delete_T"]).ConvertStringDateTimeToDateTime();
                user.UserPhone = Convert.ToString(dr["UserPhone"]);
                user.UserFax = Convert.ToString(dr["UserFax"]);
                user.IdentityNumber = Convert.ToString(dr["IdentityNumber"]);
                user.SearchRest = Convert.ToInt32(dr["SearchRest"]);
                user.Pause = Convert.ToInt32(dr["Pause"]);
                user.EOD = Convert.ToInt32(dr["EOD"]);
                user.POD = Convert.ToInt32(dr["POD"]);
                user.ExtName = Convert.ToString(dr["ExtName"]);
                user.SelectType = Convert.ToInt32(dr["SelectType"]);
                user.DNISCheck = Convert.ToInt32(dr["DNISCheck"]);
                user.DNIS = Convert.ToString(dr["DNIS"]);
                user.UserPic = Convert.ToString(dr["UserPic"]);
                user.NoOfEvaluations = Convert.ToInt32(dr["NoOfEvals"]);

                users.Add(user);
            }
            return users;
        }

        /*public static List<Agent> MapAgents(SqlDataReader dr) {
            List<Agent> agents = new List<Agent>();
            Agent agent = null;
            while (dr.Read())
            {
                agent = new Agent();

                agent.Id = (int)dr["Id"];
                agent.Code = Convert.ToInt32(dr["Code"]);
                agent.Name = Convert.ToString(dr["Name"]);
                agent.EmailId = Convert.ToString(dr["EmailId"]);
                agent.Password = Convert.ToString(dr["Password"]);
                //agent.Email = Convert.ToString(dr["Email"]);
                agent.SaveAndEmail = Convert.ToBoolean(dr["SaveAndEmail"]);
                agent.Comments = Convert.ToString(dr["Comments"]);
                agent.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                agent.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                agent.NoOfEvaluations = (int)dr["NoOfEvals"];

                agents.Add(agent);
            }

            return agents;
        }
        public static List<Agent> MapAgents(SqlDataReader dr, Recorder recorder)
        {
            List<Agent> agents = new List<Agent>();
            Agent agent = null;
            while (dr.Read())
            {
                agent = new Agent();

                agent.Id = (int)dr["Id"];
                agent.Code = Convert.ToInt32(dr["Code"]);
                agent.Name = Convert.ToString(dr["Name"]);
                agent.EmailId = Convert.ToString(dr["EmailId"]);
                agent.Password = Convert.ToString(dr["Password"]);
                //agent.Email = Convert.ToString(dr["Email"]);
                agent.SaveAndEmail = Convert.ToBoolean(dr["SaveAndEmail"]);
                agent.Comments = Convert.ToString(dr["Comments"]);
                agent.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                agent.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                agent.NoOfEvaluations = (int)dr["NoOfEvals"];
                agent.RecId = recorder.Id;
                agent.RecName = recorder.Name;

                agents.Add(agent);
            }

            return agents;
        }
        public static Agent MapAgent(SqlDataReader dr)
        {
            Agent agent = null;
            while (dr.Read())
            {
                agent = new Agent();

                agent.Id = (int)dr["Id"];
                agent.Code = Convert.ToInt32(dr["Code"]);
                agent.Name = Convert.ToString(dr["Name"]);
                agent.EmailId = Convert.ToString(dr["EmailId"]);
                agent.SaveAndEmail = Convert.ToBoolean(dr["SaveAndEmail"]);
                agent.Comments = Convert.ToString(dr["Comments"]);
                agent.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                agent.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
            }

            return agent;
        }*/

        public static User MapUserWithoutChannel(SqlDataReader dr)
        {
            User user = null;
            while (dr.Read())
            {
                user = new User();

                user.UserNum = (int)dr["UserNum"];
                user.UserType = (int)dr["UserType"];
                user.UserName = Convert.ToString(dr["UserName"]);
                user.UserID = Convert.ToString(dr["UserID"]);
                user.UserPW = Convert.ToString(dr["UserPW"]);
                user.UserName = Convert.ToString(dr["UserName"]);
                user.ViewID = Convert.ToInt32(dr["ViewID"]);
                user.Ext = Convert.ToString(dr["Ext"]);
                user.UserEmail = Convert.ToString(dr["UserEmail"]);
                user.Status = Convert.ToInt32(dr["Status"]);
                user.JoinBeginDate = dr["JoinBeginDate"] == DBNull.Value || Convert.ToString(dr["JoinBeginDate"]).Trim() == ""
                    ? default(DateTime) : (Convert.ToString(dr["JoinBeginDate"]) + "000000").ConvertStringDateTimeToDateTime();
                user.JoinEndDate = dr["JoinEndDate"] == DBNull.Value || Convert.ToString(dr["JoinBeginDate"]).Trim() == ""
                    ? default(DateTime) : (Convert.ToString(dr["JoinEndDate"]) + "000000").ConvertStringDateTimeToDateTime();
                user.Create_T = Convert.ToString(dr["Create_T"]).ConvertStringDateTimeToDateTime();
                user.Modify_T = dr["Modify_T"] == DBNull.Value || Convert.ToString(dr["Modify_T"]).Trim() == String.Empty
                    ? default(DateTime) : Convert.ToString(dr["Modify_T"]).ConvertStringDateTimeToDateTime();
                user.Delete_T = dr["Delete_T"] == DBNull.Value || Convert.ToString(dr["Delete_T"]).Trim() == ""
                    ? default(DateTime) : Convert.ToString(dr["Delete_T"]).ConvertStringDateTimeToDateTime();
                user.UserPhone = Convert.ToString(dr["UserPhone"]);
                user.UserFax = Convert.ToString(dr["UserFax"]);
                user.IdentityNumber = Convert.ToString(dr["IdentityNumber"]);
                user.SearchRest = Convert.ToInt32(dr["SearchRest"]);
                user.Pause = Convert.ToInt32(dr["Pause"]);
                user.EOD = Convert.ToInt32(dr["EOD"]);
                user.POD = Convert.ToInt32(dr["POD"]);
                user.ExtName = Convert.ToString(dr["ExtName"]);
                user.SelectType = Convert.ToInt32(dr["SelectType"]);
                user.DNISCheck = Convert.ToInt32(dr["DNISCheck"]);
                user.DNIS = Convert.ToString(dr["DNIS"]);
                user.UserPic = Convert.ToString(dr["UserPic"]);

            }
            return user;
        }

        public static List<UserGroup> MapUserGroups(SqlDataReader dr)
        {
            List<UserGroup> userGroups = new List<UserGroup>();
            UserGroup uGroup = null;
            try
            {
                while (dr.Read())
                {
                    uGroup = new UserGroup();

                    uGroup.UserNum = (int)dr["UserNum"];
                    uGroup.GroupNum = dr["GroupNum"] == DBNull.Value ? 0 : (int)dr["GroupNum"];
                    uGroup.CreateID = Convert.ToInt32(dr["CreateID"]);
                    uGroup.AssignAuth = Convert.ToString(dr["AssignAuth"]);
                    uGroup.EnterpriseAssignAuth = DBRecordExtensions.HasColumn(dr, "EnterpriseAssignAuth") ? Convert.ToString(dr["EnterpriseAssignAuth"]) : "0000000000";  //dr["EnterpriseAssignAuth"] == DBNull.Value ? string.Empty : Convert.ToString(dr["EnterpriseAssignAuth"]);
                    uGroup.GroupName = Convert.ToString(dr["GroupName"]);
                    uGroup.Descr = Convert.ToString(dr["Descr"]);
                    uGroup.Ext = Convert.ToString(dr["Ext"]);
                    uGroup.Create_T = Convert.ToString(dr["Create_T"]).ConvertStringDateTimeToDateTime();
                    uGroup.Modify_T = dr["Modify_T"] == DBNull.Value || Convert.ToString(dr["Modify_T"]).Trim() == ""
                        ? default(DateTime) : Convert.ToString(dr["Modify_T"]).ConvertStringDateTimeToDateTime();
                    if (Convert.ToString(dr["Delete_T"]).Trim() != "NULL" && Convert.ToString(dr["Delete_T"]).Trim().Length > 5)
                    {
                        uGroup.Delete_T = dr["Delete_T"] == DBNull.Value || Convert.ToString(dr["Delete_T"]).Trim() == ""
                        ? default(DateTime) : Convert.ToString(dr["Delete_T"]).ConvertStringDateTimeToDateTime();
                    }


                    userGroups.Add(uGroup);
                }

            }
            catch (Exception _exception)
            {
                throw _exception;
            }
            return userGroups;
        }

        public static List<UserGroup> MapUserGroups(SqlDataReader dr, int recId)
        {
            List<UserGroup> userGroups = new List<UserGroup>();
            UserGroup uGroup = null;
            while (dr.Read())
            {
                uGroup = new UserGroup();

                uGroup.UserNum = (int)dr["UserNum"];
                uGroup.GroupNum = dr["GroupNum"] == DBNull.Value ? 0 : (int)dr["GroupNum"];
                uGroup.CreateID = Convert.ToInt32(dr["CreateID"]);
                uGroup.AssignAuth = Convert.ToString(dr["AssignAuth"]);
                uGroup.EnterpriseAssignAuth = Convert.ToString(dr["EnterpriseAssignAuth"]);
                uGroup.GroupName = Convert.ToString(dr["GroupName"]);
                uGroup.Descr = Convert.ToString(dr["Descr"]);
                uGroup.Ext = Convert.ToString(dr["Ext"]);
                uGroup.Create_T = Convert.ToString(dr["Create_T"]).ConvertStringDateTimeToDateTime();
                uGroup.Modify_T = dr["Modify_T"] == DBNull.Value || Convert.ToString(dr["Modify_T"]).Trim() == ""
                    ? default(DateTime) : Convert.ToString(dr["Modify_T"]).ConvertStringDateTimeToDateTime();
                uGroup.Delete_T = dr["Delete_T"] == DBNull.Value || Convert.ToString(dr["Delete_T"]).Trim() == "" || Convert.ToString(dr["Delete_T"]).Trim() == "NULL"
                    ? default(DateTime) : Convert.ToString(dr["Delete_T"]).ConvertStringDateTimeToDateTime();

                uGroup.RecId = recId;

                userGroups.Add(uGroup);
            }
            return userGroups;
        }

        public static User MapLoginUser(SqlDataReader dr)
        {
            List<User> users = new List<User>();
            User user = null;
            while (dr.Read())
            {
                user = new User();

                user.UserNum = (int)dr["UserNum"];
                user.UserType = (int)dr["UserType"];
                user.UserName = Convert.ToString(dr["UserName"]);
                user.UserPic = Convert.ToString(dr["UserPic"]);
                user.Ext = Convert.ToString(dr["Ext"]);
                user.ViewID = Convert.ToInt32(dr["ViewID"]);
                user.Status = Convert.ToInt32(dr["Status"]);
                user.SearchRest = Convert.ToInt32(dr["SearchRest"]);
                //user.Pause = Convert.ToInt32(dr["Pause"]);
                //user.EOD = Convert.ToInt32(dr["EOD"]);
                //user.POD = Convert.ToInt32(dr["POD"]);
                user.SelectType = Convert.ToInt32(dr["SelectType"]);
                //user.DNISCheck = Convert.ToInt32(dr["DNISCheck"]);
                //user.DNIS = Convert.ToString(dr["DNIS"]);
                user.HasShiftRest = Convert.ToBoolean(dr["HasShiftRest"]);
                user.IsDeviceUser = Convert.ToInt32(dr["IsDeviceUser"]);
                user.EnableDisableInquireUser = Convert.ToInt32(dr["Enable_User"]);
                user.IsAvrisView = !DBRecordExtensions.HasColumn(dr, "IsAvrisView") ? false : (dr["IsAvrisView"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsAvrisView"]));
                user.IsIQ3View = !DBRecordExtensions.HasColumn(dr, "IsIQ3View") ? false : (dr["IsIQ3View"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsIQ3View"]));
                user.RoleId = !DBRecordExtensions.HasColumn(dr, "RoleId") ? 0 : (dr["RoleId"] == DBNull.Value ? 0 : Convert.ToInt32(dr["RoleId"]));
                user.IsEnterpriseUser = Convert.ToBoolean(dr["IsEnterpriseUser"]);
                //user.IsEventSpecific = Convert.ToBoolean(dr["IsEventSpecific"]);
                user.CanInvite = Convert.ToBoolean(dr["CanInvite"]);
                //user.QBUserId = Convert.ToInt32(dr["QBUserId"]);
                //user.ExistsOnQB = Convert.ToBoolean(dr["ExistsOnQB"]);
                user.IsTempLogin = Convert.ToBoolean(dr["IsTempLogin"]);
                user.IsLockedOut = dr["IsLockedOut"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsLockedOut"]);
                user.Is2FAEnabled = !DBRecordExtensions.HasColumn(dr, "Is2FAEnabled") ? false : (dr["Is2FAEnabled"] == DBNull.Value ? false : Convert.ToBoolean(dr["Is2FAEnabled"]));
                user.IsCompactView = dr["IsCompactView"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsCompactView"]);
                user.IsAgreedToLicense = dr["IsAgreedToLicense"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsAgreedToLicense"]);
                user.UserPhone = !DBRecordExtensions.HasColumn(dr, "UserPhone") ? string.Empty : (dr["UserPhone"] == DBNull.Value ? string.Empty : Convert.ToString(dr["UserPhone"]));
                user.IsRevCell = dr["IsRevcell"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsRevcell"]);
                user.CreatedBy = dr["CreatedBy"] == DBNull.Value ? 0 : Convert.ToInt32(dr["CreatedBy"]);
                user.IsIwbUser = dr["IsIwbUser"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsIwbUser"]);
                user.OrganizationId = dr["OrganizationId"] == DBNull.Value ? 0 : Convert.ToInt32(dr["OrganizationId"]);
                //user.DOB = dr["DOB"] == DBNull.Value ? DBNull.Value : Convert.ToDateTime(dr["DOB"]);
                user.DOB = dr["DOB"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["DOB"]);
                user.City = Convert.ToString(dr["City"]);
                user.State = Convert.ToString(dr["State"]);
                user.SocialSecurityNumber = Convert.ToString(dr["SSN"]);
                user.WelderStencilNumber = Convert.ToString(dr["StencilNumber"]);
                user.CustomerIdZoho = !DBRecordExtensions.HasColumn(dr, "CustomerIdZoho") ? string.Empty : Convert.ToString(dr["CustomerIdZoho"]);

                users.Add(user);
            }
            return users.FirstOrDefault();
        }

        public static UserGroup MapLoginUserGroup(SqlDataReader dr)
        {
            List<UserGroup> userGroups = new List<UserGroup>();
            UserGroup uGroup = null;
            while (dr.Read())
            {
                uGroup = new UserGroup();

                uGroup.GroupNum = dr["GroupNum"] == DBNull.Value ? 0 : (int)dr["GroupNum"];
                uGroup.ParentGroup = dr["ParentGroup"] == DBNull.Value ? 0 : (int)dr["ParentGroup"];
                uGroup.Depth = Convert.ToInt32(dr["Depth"]);
                uGroup.AssignAuth = Convert.ToString(dr["AssignAuth"]);
                uGroup.IndexNum = Convert.ToInt32(dr["IndexNum"]);

                userGroups.Add(uGroup);
            }
            return userGroups.FirstOrDefault();
        }

        public static string MapSimpleAccessRights(SqlDataReader dr, out string originalSimpleAccessRight)
        {
            #region Previous Code with single accees right
            //string simpleAccessRights = null;
            //if (dr.HasRows)
            //{
            //    dr.Read();

            //    simpleAccessRights = Convert.ToString(dr["AccessRight"]);
            //}
            //return simpleAccessRights;
            #endregion
            List<string> simpleAccessRights = new List<string>();
            originalSimpleAccessRight = "";
            int count = 0;
            while (dr.Read())
            {
                if (count == 0)
                    originalSimpleAccessRight = Convert.ToString(dr[0]);
                simpleAccessRights.Add(Convert.ToString(dr["AccessRight"]));
                count++;
            }
            string accessRights = StringHelper.ProcesSimpleAccessRights(simpleAccessRights);
            return accessRights;
        }


        public static string MapAssignedNodes(SqlDataReader dr)
        {
            string assignedNodes = null;

            if (dr.HasRows)
            {
                dr.Read();

                assignedNodes = Convert.ToString(dr["AssignedNodes"]);
            }
            return assignedNodes;
        }

        public static List<Permission> MapPermissions(SqlDataReader dr)
        {
            List<Permission> permissions = new List<Permission>();
            Permission permission = null;
            while (dr.Read())
            {
                permission = new Permission();

                permission.Id = !DBRecordExtensions.HasColumn(dr, "PermissionId") ? (int)dr["Id"] : (int)dr["PermissionId"];
                permission.Title = Convert.ToString(dr["Title"]);
                permission.UserNum = !DBRecordExtensions.HasColumn(dr, "AppUserId") ? (long?)null : Convert.ToInt32(dr["AppUserId"]);

                permissions.Add(permission);
            }
            return permissions;
        }

        public static List<UserProfile> MapUserProfiles(SqlDataReader dr)
        {
            List<UserProfile> userProfiles = new List<UserProfile>();
            UserProfile userProfile = null;
            while (dr.Read())
            {
                userProfile = new UserProfile();

                userProfile.Id = (int)dr["Id"];
                userProfile.AppUserId = dr["AppUserId"] == DBNull.Value ? 0 : (int)dr["AppUserId"];
                userProfile.SocialSecurityNumber = Convert.ToString(dr["SocialSecurityNumber"]);
                userProfile.IdentityNumber = Convert.ToString(dr["IdentityNumber"]);
                userProfile.FirstName = Convert.ToString(dr["FirstName"]);
                userProfile.LastName = Convert.ToString(dr["LastName"]);
                userProfile.Description = Convert.ToString(dr["Description"]);
                userProfile.EmailDefault = Convert.ToString(dr["EmailDefault"]);
                userProfile.EmailsOther = Convert.ToString(dr["EmailsOther"]);
                userProfile.UserMobile = Convert.ToString(dr["UserMobile"]);
                userProfile.UserFax = Convert.ToString(dr["UserFax"]);
                userProfile.UserPhone = Convert.ToString(dr["UserPhone"]);
                userProfile.IsCompleted = Convert.ToBoolean(dr["IsCompleted"]);
                userProfile.Gender = Convert.ToString(dr["Gender"]);
                userProfile.UserPicture = Convert.ToString(dr["UserPicture"]);
                userProfile.DateOfBirth = dr["DateOfBirth"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["DateOfBirth"]);
                userProfile.JoinBeginDate = dr["JoinBeginDate"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["JoinBeginDate"]);
                userProfile.JoinEndDate = dr["JoinEndDate"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["JoinEndDate"]);
                userProfile.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                userProfile.ModifiedDate = dr["ModifiedDate"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["ModifiedDate"]);
                userProfile.DeletedDate = dr["DeletedDate"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["DeletedDate"]);
                //userProfile.CreatedBy = Convert.ToInt64(dr["CreatedBy"]);
                //userProfile.ModifiedBy = Convert.ToInt64(dr["ModifiedBy"]);
                userProfile.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);

                userProfiles.Add(userProfile);
            }
            return userProfiles;
        }

        #endregion

        #region MyRegion

        public static List<TreeViewDataDTO> MapTreeViewDataDTOs(SqlDataReader dr)
        {
            List<TreeViewDataDTO> treeViewDataDTOs = new List<TreeViewDataDTO>();
            TreeViewDataDTO treeViewDataDTO = null;
            while (dr.Read())
            {
                treeViewDataDTO = new TreeViewDataDTO();

                treeViewDataDTO.NodeId = Convert.ToString(dr["NodeId"]);
                treeViewDataDTO.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                treeViewDataDTO.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                treeViewDataDTO.Depth = Convert.ToInt32(dr["Depth"]);
                treeViewDataDTO.MenuType = Convert.ToInt32(dr["MenuType"]);
                treeViewDataDTO.ViewType = Convert.ToInt32(dr["ViewType"]);
                treeViewDataDTO.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                treeViewDataDTO.Param1 = Convert.ToString(dr["Param1"]);
                treeViewDataDTO.Param2 = Convert.ToString(dr["Param2"]);
                treeViewDataDTO.Param3 = Convert.ToString(dr["Param3"]);

                treeViewDataDTOs.Add(treeViewDataDTO);
            }
            return treeViewDataDTOs;
        }

        public static List<Recorder> MapRecorders(SqlDataReader dr)
        {
            List<Recorder> recorders = new List<Recorder>();
            Recorder recorder = null;
            while (dr.Read())
            {
                recorder = new Recorder();

                recorder.Id = Convert.ToInt32(dr["RecID"]);
                recorder.Name = Convert.ToString(dr["RecName"]);
                recorder.IP = Convert.ToString(dr["RecIP"]);
                recorder.Port = Convert.ToInt32(dr["RecPort"]);
                recorder.IsPrimary = Convert.ToBoolean(dr["IsPrimary"]);
                recorder.ConnectionString = Convert.ToString(dr["DBConString"]);

                //recorder.RemoteLogin = Convert.ToString(dr["RemoteLogin"]);
                //recorder.RemotePwd = Convert.ToString(dr["RemotePwd"]);
                //recorder.DataSource = Convert.ToString(dr["DataSource"]);
                //recorder.Status = Convert.ToInt32(dr["Status"]);
                //recorder.Create_T = Convert.ToString(dr["Create_T"]);
                //recorder.Modify_T = Convert.ToString(dr["Modify_T"]);
                //recorder.Delete_T = Convert.ToString(dr["Delete_T"]);
                //recorder.Descr = Convert.ToString(dr["Descr"]);

                recorders.Add(recorder);

            }
            return recorders;
        }


        #endregion

        #region Report Objects

        public static List<RPTEvaluation> MapRPTEvaluations(SqlDataReader dr)
        {
            List<RPTEvaluation> rptEvaluations = new List<RPTEvaluation>();
            RPTEvaluation rptEvaluation = null;

            while (dr.Read())
            {

                rptEvaluation = new RPTEvaluation();
                rptEvaluation.Id = Convert.ToInt64(dr["Id"]);
                rptEvaluation.SurveyId = Convert.ToInt32(dr["SurveyId"]);
                rptEvaluation.SurveyName = Convert.ToString(dr["SurveyName"]);
                rptEvaluation.CallId = Convert.ToString(dr["CallId"]);
                rptEvaluation.StatusId = Convert.ToInt16(dr["StatusId"]);

                rptEvaluation.GroupNum = Convert.ToInt32(dr["GroupNum"]);
                rptEvaluation.GroupName = Convert.ToString(dr["GroupName"]);
                rptEvaluation.AgentId = Convert.ToInt32(dr["AgentId"]);
                rptEvaluation.AgentName = Convert.ToString(dr["AgentName"]);
                rptEvaluation.SupervisorId = Convert.ToInt32(dr["SupervisorId"]);
                rptEvaluation.SupervisorName = Convert.ToString(dr["SupervisorName"]);
                rptEvaluation.StatusId = Convert.ToInt16(dr["StatusId"]);
                rptEvaluation.IsShared = Convert.ToBoolean(dr["IsShared"]);
                rptEvaluation.EvaluatedScore = Convert.ToSingle(dr["EvaluatedScore"]);

                rptEvaluation.CompletedDate = dr["CompletedDate"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["CompletedDate"]);
                rptEvaluation.SupervisorComments = Convert.ToString(dr["ScorerComments"]);
                rptEvaluation.MaxScore = Convert.ToSingle(dr["MaxScore"]);

                rptEvaluation.QId = Convert.ToInt32(dr["QId"]);
                rptEvaluation.QTitle = Convert.ToString(dr["QTitle"]);
                rptEvaluation.QScore = Convert.ToSingle(dr["QScore"]);
                rptEvaluation.SectionId = Convert.ToInt32(dr["SectionId"]);
                rptEvaluation.SectionTitle = Convert.ToString(dr["SectionTitle"]);
                rptEvaluation.TotalScore = Convert.ToInt32(dr["TotalScore"]);

                rptEvaluations.Add(rptEvaluation);
            }
            return rptEvaluations;
        }
        public static List<RPTEvaluationMain> MapRPTEvaluationsMain(SqlDataReader dr)
        {
            List<RPTEvaluationMain> rptEvaluations = new List<RPTEvaluationMain>();
            RPTEvaluationMain rptEvaluation = null;

            while (dr.Read())
            {

                rptEvaluation = new RPTEvaluationMain();
                rptEvaluation.Id = Convert.ToInt64(dr["Id"]);
                rptEvaluation.SurveyId = Convert.ToInt32(dr["SurveyId"]);
                rptEvaluation.SurveyName = Convert.ToString(dr["SurveyName"]);
                rptEvaluation.CallId = Convert.ToString(dr["CallId"]);
                rptEvaluation.StatusId = Convert.ToInt16(dr["StatusId"]);

                rptEvaluation.GroupNum = Convert.ToInt32(dr["GroupNum"]);
                rptEvaluation.GroupName = Convert.ToString(dr["GroupName"]);
                rptEvaluation.AgentId = Convert.ToInt32(dr["AgentId"]);
                rptEvaluation.AgentName = Convert.ToString(dr["AgentName"]);
                rptEvaluation.SupervisorId = Convert.ToInt32(dr["SupervisorId"]);
                rptEvaluation.SupervisorName = Convert.ToString(dr["SupervisorName"]);
                rptEvaluation.StatusId = Convert.ToInt16(dr["StatusId"]);
                rptEvaluation.IsShared = Convert.ToBoolean(dr["IsShared"]);
                rptEvaluation.EvaluatedScore = Convert.ToSingle(dr["EvaluatedScore"]);

                rptEvaluation.CompletedDate = dr["CompletedDate"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["CompletedDate"]);
                rptEvaluation.SupervisorComments = Convert.ToString(dr["ScorerComments"]);
                rptEvaluation.MaxScore = Convert.ToSingle(dr["MaxScore"]);

                rptEvaluation.TotalScore = Convert.ToInt32(dr["TotalScore"]);
                //rptEvaluation.TotalQuestions = Convert.ToInt32(dr["TotalQuestions"]);

                rptEvaluations.Add(rptEvaluation);
            }
            return rptEvaluations;
        }
        #endregion

        public static List<UserEvaluation> MapUserEvaluationsForSearchPage(SqlDataReader dr)
        {
            List<UserEvaluation> userEvals = new List<UserEvaluation>();
            UserEvaluation userEval = null;
            //if (callEvaluationDTOs == null) callEvaluationDTOs = new List<CallEvaluationDTO>();

            while (dr.Read())
            {

                userEval = new UserEvaluation();

                userEval.Id = Convert.ToInt64(dr["Id"]);
                userEvals.Add(userEval);
            }
            return userEvals;
        }

        public static List<ScheduleEventInfo> MapAllScheduledEvents(SqlDataReader dr)
        {
            List<ScheduleEventInfo> scheduledEvents = new List<ScheduleEventInfo>();
            ScheduleEventInfo scheduleEvent = null;
            while (dr.Read())
            {
                scheduleEvent = new ScheduleEventInfo();
                scheduleEvent.Id = Convert.ToInt32(dr["Id"]);
                scheduleEvent.EventId = Convert.ToString(dr["EventId"]);
                scheduleEvent.EventName = Convert.ToString(dr["EventName"]);
                scheduleEvent.Duration = Convert.ToString(dr["Duration"]);
                scheduleEvent.TenantId = Convert.ToInt32(dr["TenantId"]);
                scheduleEvent.ScheduledDateTime = Convert.ToDateTime(dr["ScheduledDateTime"]);
                scheduleEvent.ScheduledBy = Convert.ToInt32(dr["ScheduledBy"]);
                scheduleEvent.ScheduledByEmail = Convert.ToString(dr["ScheduledByEmail"]);
                scheduleEvent.ScheduledByName = Convert.ToString(dr["ScheduledByName"]);
                scheduleEvent.IsActive = Convert.ToBoolean(dr["IsActive"]);
                scheduleEvent.IsVirtualInspection = Convert.ToBoolean(dr["IsVirtualInspection"]);
                scheduleEvent.EndUserPhoneNumber = Convert.ToString(dr["EndUserPhoneNumber"]);
                scheduleEvent.EndUserEmail = Convert.ToString(dr["EndUserEmail"]);
                scheduleEvent.RemoteInvitationLink = Convert.ToString(dr["RemoteInvitationLink"]);
                scheduleEvent.CalendarEventId = dr["CalendarEventId"] == DBNull.Value ? "" : Convert.ToString(dr["CalendarEventId"]);
                scheduleEvent.ReminderSentOn = dr["ReminderSentOn"] == DBNull.Value ? new DateTime(2020, 1, 1) : Convert.ToDateTime(dr["ReminderSentOn"]);
                scheduleEvent.IsCompleted = dr["IsCompleted"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsCompleted"]);
                scheduleEvent.MarkerTypeId = Convert.ToInt32(dr["MarkerTypeId"]);
                scheduleEvent.InspectionTemplateId = dr["InspectionTemplateId"] == DBNull.Value ? 0 : Convert.ToInt32(dr["InspectionTemplateId"]);
                scheduleEvent.InspectionTemplateType = dr["InspectionTemplateType"] == DBNull.Value ? "" : Convert.ToString(dr["InspectionTemplateType"]);
                scheduleEvent.PreInspectionDetails = dr["PreInspectionDetails"] == DBNull.Value ? "" : Convert.ToString(dr["PreInspectionDetails"]);
                scheduleEvent.SentOn = Convert.ToDateTime(dr["SentOn"]);
                scheduleEvent.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                scheduleEvent.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                scheduledEvents.Add(scheduleEvent);
            }
            return scheduledEvents;
        }

        public static List<ScheduleEventParticipant> MapParticipants(SqlDataReader dr)
        {
            List<ScheduleEventParticipant> participants = new List<ScheduleEventParticipant>();
            ScheduleEventParticipant participant = null;
            while (dr.Read())
            {
                participant = new ScheduleEventParticipant();
                participant.Id = Convert.ToInt32(dr["Id"]);
                participant.EventId = Convert.ToString(dr["EventId"]);
                participant.ScheduledDateTime = Convert.ToDateTime(dr["ScheduledDateTime"]);
                participant.ParticipantId = Convert.ToInt32(dr["ParticipantId"]);
                participant.ParticipantEmail = Convert.ToString(dr["ParticipantEmail"]);
                participant.ParticipantPhoneNumber = Convert.ToString(dr["ParticipantPhoneNumber"]);
                participant.InvitationLink = Convert.ToString(dr["InvitationLink"]);
                participant.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                participant.ModifiedDate = Convert.ToDateTime(dr["ModifiedDate"]);
                participant.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                participant.HasLiveMonitorPermission = Convert.ToBoolean(dr["HasLiveMonitorPermission"]);
                participant.ReminderSentOn = dr["ReminderSentOn"] == DBNull.Value ? new DateTime(2020, 1, 1) : Convert.ToDateTime(dr["ReminderSentOn"]);
                participant.Ext = Convert.ToString(dr["Ext"]);
                participant.ExtName = Convert.ToString(dr["ExtName"]);
                participant.InspectorEmail = Convert.ToString(dr["InspectorEmail"]);
                participant.ParticipantIsEndUser = Convert.ToBoolean(dr["ParticipantIsEndUser"]);

                participants.Add(participant);
            }
            return participants;
        }

        public static List<SavedReport> MapAllSavedReports(SqlDataReader dr)
        {
            List<SavedReport> savedReports = new List<SavedReport>();
            SavedReport savedReport = null;
            try
            {
                while (dr.Read())
                {
                    savedReport = new SavedReport();
                    savedReport.Id = Convert.ToInt32(dr["Id"]);
                    savedReport.UserId = (int)dr["UserId"];
                    savedReport.ReportName = Convert.ToString(dr["ReportName"]);
                    savedReport.ReportTypeId = Convert.ToInt32(dr["ReportTypeId"]);
                    savedReport.Criteria = Convert.ToString(dr["Criteria"]);
                    savedReport.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                    savedReport.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                    savedReport.LastExecutedOn = dr["LastExecutedOn"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["LastExecutedOn"]);
                    savedReport.Description = Convert.ToString(dr["Comments"]);
                    savedReport.IsFavorite = Convert.ToBoolean(dr["IsFavorite"]);
                    savedReport.FavoriteOf = ReportDAL.GetAllUserNumReportSharedWith(Convert.ToString(dr["FavoriteOf"]));
                    savedReport.IsShared = Convert.ToBoolean(dr["IsShared"]);
                    savedReport.IsScheduled = Convert.ToBoolean(dr["IsScheduled"]);
                    savedReport.TypeOfReport = (TypeOfReport)Enum.Parse(typeof(TypeOfReport), Convert.ToString(dr["ReportTypeId"]));

                    if (dr.FieldExists("UserName"))
                    {
                        savedReport.UserName = Convert.ToString(dr["UserName"]);
                    }

                    savedReports.Add(savedReport);
                }
            }
            catch (Exception _exception)
            {
                throw _exception;
            }
            return savedReports;
        }

        public static SavedReport MapSavedReport(SqlDataReader dr)
        {
            SavedReport savedReport = null;
            try
            {
                while (dr.Read())
                {
                    savedReport = new SavedReport();
                    savedReport.Id = Convert.ToInt32(dr["Id"]);
                    savedReport.UserId = (int)dr["UserId"];
                    savedReport.ReportName = Convert.ToString(dr["ReportName"]);
                    savedReport.ReportTypeId = Convert.ToInt32(dr["ReportTypeId"]);
                    savedReport.Criteria = Convert.ToString(dr["Criteria"]);
                    savedReport.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                    savedReport.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                    savedReport.LastExecutedOn = dr["LastExecutedOn"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["LastExecutedOn"]);
                    savedReport.Description = Convert.ToString(dr["Comments"]);
                    savedReport.IsFavorite = Convert.ToBoolean(dr["IsFavorite"]);
                    savedReport.FavoriteOf = ReportDAL.GetAllUserNumReportSharedWith(Convert.ToString(dr["FavoriteOf"]));
                    savedReport.IsShared = Convert.ToBoolean(dr["IsShared"]);
                    savedReport.IsScheduled = Convert.ToBoolean(dr["IsScheduled"]);
                    savedReport.TypeOfReport = (TypeOfReport)Enum.Parse(typeof(TypeOfReport), Convert.ToString(dr["ReportTypeId"]));

                    if (dr.FieldExists("UserName"))
                    {
                        savedReport.UserName = Convert.ToString(dr["UserName"]);
                    }
                }
            }
            catch (Exception _exception)
            {
                throw _exception;
            }
            return savedReport;
        }

        #region Schedule Report

        public static List<ScheduleReport> MapAllScheduleReport(SqlDataReader dr)
        {
            List<ScheduleReport> objScheduleReportList = new List<ScheduleReport>(); ;
            ScheduleReport objScheduleReport = null;

            while (dr.Read())
            {
                objScheduleReport = MapScheduleReport(dr);

                if (objScheduleReport != null)
                {
                    objScheduleReportList.Add(objScheduleReport);
                }
            }

            return objScheduleReportList;
        }

        public static ScheduleReport MapScheduleReport(SqlDataReader dr)
        {
            ScheduleReport objScheduleReport = null;

            if (dr.HasRows)
            {
                objScheduleReport = new ScheduleReport();

                objScheduleReport.Id = (int)dr["Id"];
                objScheduleReport.ReportName = Convert.ToString(dr["ReportName"]);
                objScheduleReport.UserId = Convert.ToInt32(dr["UserId"]);
                objScheduleReport.SaveReportId = Convert.ToInt32(dr["SaveReportId"]);
                objScheduleReport.ExportType = (ReportExportType)Convert.ToInt32(dr["ExportType"]);
                objScheduleReport.CreatedOn = Convert.ToDateTime(dr["CreatedOn"]);


                if (!(dr["LastReportOn"] is DBNull))
                {
                    objScheduleReport.SetLastReportOn(Convert.ToDateTime(dr["LastReportOn"]));
                }

                if (!(dr["NextReportOn"] is DBNull))
                {
                    objScheduleReport.SetNextReportOn(Convert.ToDateTime(dr["NextReportOn"]));
                }

                if (!(dr["UpdatedOn"] is DBNull))
                {
                    objScheduleReport.UpdatedOn = Convert.ToDateTime(dr["UpdatedOn"]);
                }

                Recurrence objRecurrence = new Recurrence();
                objRecurrence.objRecurrenceType = (RecurrenceType)Convert.ToInt32(dr["RecurrenceType"]);

                if (!(dr["RecurrenceData"] is DBNull))
                {
                    objRecurrence.objRecurrenceData = new RecurrenceData(Convert.ToString(dr["RecurrenceData"]));
                }

                objScheduleReport.objRecurrence = objRecurrence;

                objScheduleReport.Recipients = ScheduleReportDAL.GetAllReportRecipient(Convert.ToString(dr["Recipients"]));
            }

            return objScheduleReport;
        }

        #endregion

        #region Revcell
        public static RevcellInfo MapRevcellInfo(SqlDataReader dr)
        {
            RevcellInfo revcellInfo = new RevcellInfo();
            while (dr.Read())
            {
                revcellInfo.Id = (int)dr["Id"];

                revcellInfo.Ext = Convert.ToInt32(dr["Ext"]);
                revcellInfo.UserNum = Convert.ToInt32(dr["UserNum"]);
                revcellInfo.PhoneNumberId = Convert.ToString(dr["PhoneNumberId"]);
                revcellInfo.PhoneNumber = Convert.ToString(dr["PhoneNumber"]);
                revcellInfo.PhoneName = Convert.ToString(dr["PhoneName"]);
                revcellInfo.SIPId = Convert.ToString(dr["SIPId"]);
                revcellInfo.SIPName = Convert.ToString(dr["SIPName"]);
                revcellInfo.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                revcellInfo.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
            }
            return revcellInfo;
        }
        public static PlivoInfo MapPlivoInfo(SqlDataReader dr)
        {
            PlivoInfo plivoInfo = new PlivoInfo();
            while (dr.Read())
            {
                plivoInfo.Id = (int)dr["Id"];

                plivoInfo.Ext = Convert.ToInt32(dr["Ext"]);
                plivoInfo.UserNum = Convert.ToInt32(dr["UserNum"]);
                plivoInfo.PhoneNumberId = Convert.ToString(dr["PhoneNumberId"]);
                plivoInfo.PhoneNumber = Convert.ToString(dr["PhoneNumber"]);
                plivoInfo.PhoneAlias = Convert.ToString(dr["PhoneAlias"]);
                plivoInfo.EndpointId = Convert.ToString(dr["EndpointId"]);
                plivoInfo.EndpointUserName = Convert.ToString(dr["EndpointUserName"]);
                plivoInfo.EndpointAlias = Convert.ToString(dr["EndpointAlias"]);
                plivoInfo.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                plivoInfo.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
            }
            return plivoInfo;
        }
        #endregion


        #region Shared Report Info

        public static List<SharedReportInfo> MapAllSharedReportInfo(SqlDataReader dr)
        {
            List<SharedReportInfo> objSharedReportInfoList = new List<SharedReportInfo>(); ;
            SharedReportInfo objSharedReportInfo = null;

            while (dr.Read())
            {
                objSharedReportInfo = MapSharedReportInfo(dr);

                if (objSharedReportInfo != null)
                {
                    objSharedReportInfoList.Add(objSharedReportInfo);
                }
            }

            return objSharedReportInfoList;
        }

        public static SharedReportInfo MapSharedReportInfo(SqlDataReader dr)
        {
            SharedReportInfo objSharedReportInfo = null;

            if (dr.HasRows)
            {
                objSharedReportInfo = new SharedReportInfo();
                if (dr.Read())
                {
                    objSharedReportInfo.Id = (int)dr["Id"];
                    objSharedReportInfo.ReportId = Convert.ToInt32(dr["ReportId"]);
                    objSharedReportInfo.SharedBy = Convert.ToInt32(dr["SharedBy"]);
                    objSharedReportInfo.SharedWith = ReportDAL.GetAllUserNumReportSharedWith(Convert.ToString(dr["SharedWith"]));
                    objSharedReportInfo.CreatedOn = Convert.ToDateTime(dr["CreatedOn"]);

                    if (!(dr["UpdatedOn"] is DBNull))
                    {
                        objSharedReportInfo.UpdatedOn = Convert.ToDateTime(dr["UpdatedOn"]);
                    }
                }
            }

            return objSharedReportInfo;
        }

        #endregion

        #region Roles
        public static List<Role> MapRoles(SqlDataReader dr)
        {
            List<Role> roles = new List<Role>();
            Role role = null;
            try
            {
                while (dr.Read())
                {
                    role = new Role();

                    role.Id = (int)dr["Id"];
                    role.Name = Convert.ToString(dr["Name"]);
                    role.Description = Convert.ToString(dr["Description"]);
                    role.IsSystemRole = Convert.ToBoolean(dr["IsSystemRole"]);
                    role.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                    role.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);

                    roles.Add(role);
                }

            }
            catch (Exception _exception)
            {
                throw _exception;
            }
            return roles;
        }

        public static List<RolePermission> MapRolePermissions(SqlDataReader dr)
        {
            List<RolePermission> rolePermissions = new List<RolePermission>();
            RolePermission rolePermission = null;
            try
            {
                while (dr.Read())
                {
                    rolePermission = new RolePermission();

                    rolePermission.Id = (int)dr["Id"];
                    rolePermission.RoleId = Convert.ToInt32(dr["RoleId"]);
                    rolePermission.PermissionId = Convert.ToInt32(dr["PermissionId"]);
                    rolePermission.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                    rolePermission.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);

                    rolePermissions.Add(rolePermission);
                }

            }
            catch (Exception _exception)
            {
                throw _exception;
            }
            return rolePermissions;
        }
        #endregion

        #region Inspections

        public static List<InspectionTemplate> GetAllInspectionTemplates(SqlDataReader dr)
        {
            List<InspectionTemplate> inspectionTemplates = new List<InspectionTemplate>();
            InspectionTemplate inspectionTemplate = null;
            while (dr.Read())
            {
                inspectionTemplate = new InspectionTemplate();
                inspectionTemplate.Id = Convert.ToInt32(dr["Id"]);
                inspectionTemplate.Title = Convert.ToString(dr["Title"]);
                inspectionTemplate.TemplateType = Convert.ToInt16(dr["TemplateType"]);
                inspectionTemplate.Description = Convert.ToString(dr["Description"]);
                inspectionTemplate.DisableCopyMarker = Convert.ToBoolean(dr["DisableCopyMarker"]);
                inspectionTemplate.IsPassFailRequired = DBRecordExtensions.HasColumn(dr, "IsPassFailRequired") ? Convert.ToBoolean(dr["IsPassFailRequired"]) : false;
                inspectionTemplate.HasSections = Convert.ToBoolean(dr["HasSections"]);
                inspectionTemplate.IsShared = Convert.ToBoolean(dr["IsShared"]);
                inspectionTemplate.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                inspectionTemplate.CreatedBy = Convert.ToInt32(dr["CreatedBy"]);
                inspectionTemplate.ModifiedDate = Convert.ToDateTime(dr["ModifiedDate"]);
                inspectionTemplate.ModifiedBy = Convert.ToInt32(dr["ModifiedBy"]);
                inspectionTemplate.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                inspectionTemplate.Owner = DBRecordExtensions.HasColumn(dr, "Owner") ? Convert.ToString(dr["Owner"]) : "";
                inspectionTemplate.InspectionSharedWith = DBRecordExtensions.HasColumn(dr, "InspectionSharedWith") ? Convert.ToString(dr["InspectionSharedWith"]) : "";
                inspectionTemplate.NoOfSections = Convert.ToInt32(dr["NoOfSections"]);
                inspectionTemplate.NoOfMarkers = Convert.ToInt32(dr["NoOfMarkers"]);
                inspectionTemplate.IsCurrentUserTemplate = Convert.ToBoolean(dr["IsCurrentUserTemplate"]);

                inspectionTemplate.InspectionTypeID = DBRecordExtensions.HasColumn(dr, "InspectionTypeID") ? (dr["InspectionTypeID"] == DBNull.Value ? 0 : Convert.ToInt32(dr["InspectionTypeID"])) : 0;
                inspectionTemplate.InspectionType = DBRecordExtensions.HasColumn(dr, "InspectionType") ? (dr["InspectionType"] == DBNull.Value ? string.Empty : Convert.ToString(dr["InspectionType"])) : string.Empty;

                inspectionTemplates.Add(inspectionTemplate);
            }
            return inspectionTemplates;
        }

        public static InspectionTemplate GetInspectionTemplateById(SqlDataReader dr)
        {
            InspectionTemplate inspectionTemplate = null;

            if (dr.Read())
            {
                inspectionTemplate = new InspectionTemplate();
                inspectionTemplate.Id = Convert.ToInt32(dr["Id"]);
                inspectionTemplate.Title = Convert.ToString(dr["Title"]);
                inspectionTemplate.TemplateType = Convert.ToInt32(dr["TemplateType"]);
                inspectionTemplate.InspectionTypeID = Convert.ToInt32(dr["InspectionTypeID"]);
                inspectionTemplate.Description = Convert.ToString(dr["Description"]);
                inspectionTemplate.DisableCopyMarker = Convert.ToBoolean(dr["DisableCopyMarker"]);
                inspectionTemplate.IsPassFailRequired = DBRecordExtensions.HasColumn(dr, "IsPassFailRequired") ? Convert.ToBoolean(dr["IsPassFailRequired"]) : false;
                inspectionTemplate.HasSections = Convert.ToBoolean(dr["HasSections"]);
                inspectionTemplate.IsShared = Convert.ToBoolean(dr["IsShared"]);
                inspectionTemplate.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                inspectionTemplate.CreatedBy = Convert.ToInt32(dr["CreatedBy"]);
                inspectionTemplate.ModifiedDate = Convert.ToDateTime(dr["ModifiedDate"]);
                inspectionTemplate.ModifiedBy = Convert.ToInt32(dr["ModifiedBy"]);
                inspectionTemplate.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                inspectionTemplate.Owner = Convert.ToString(dr["Owner"]);
                inspectionTemplate.InspectionSharedWith = Convert.ToString(dr["InspectionSharedWith"]);
                inspectionTemplate.NoOfSections = Convert.ToInt32(dr["NoOfSections"]);
                inspectionTemplate.NoOfMarkers = Convert.ToInt32(dr["NoOfMarkers"]);
                inspectionTemplate.IsCurrentUserTemplate = Convert.ToBoolean(dr["IsCurrentUserTemplate"]);
            }

            return inspectionTemplate;
        }

        public static List<Section> GetAllSections(SqlDataReader dr)
        {
            List<Section> sections = new List<Section>();
            Section section = null;

            if (dr.HasRows)
            {
                while (dr.Read())
                {
                    section = new Section();
                    section.Id = Convert.ToInt32(dr["Id"]);

                    section.InspectionTemplateId = Convert.ToInt32(dr["InspectionTemplateId"]);
                    section.Title = Convert.ToString(dr["Title"]);
                    section.SectionType = (SectionType)Convert.ToInt32(dr["SectionType"]);
                    section.IsDefaultSection = Convert.ToBoolean(dr["IsDefaultSection"]);
                    section.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                    section.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                    section.Markers = new List<Marker>();

                    sections.Add(section);
                }
            }
            return sections;
        }

        public static List<Marker> GetAllMarkers(SqlDataReader dr)
        {
            List<Marker> markers = new List<Marker>();
            Marker marker = null;

            if (dr.HasRows)
            {
                while (dr.Read())
                {
                    marker = new Marker();
                    marker.Id = Convert.ToInt32(dr["Id"]);
                    marker.InspectionTemplateId = Convert.ToInt32(dr["InspectionTemplateId"]);
                    marker.SectionId = Convert.ToInt32(dr["SectionId"]);
                    marker.MarkerTypeId = Convert.ToInt32(dr["MarkerTypeId"]);
                    marker.Title = Convert.ToString(dr["Title"]);
                    marker.Note = Convert.ToString(dr["Note"]);
                    marker.Description = Convert.ToString(dr["Description"]);
                    marker.Ordering = Convert.ToInt32(dr["Ordering"]);
                    marker.IsRepeating = Convert.ToBoolean(dr["IsRepeating"]);
                    marker.IsPhotoAllowed = Convert.ToBoolean(dr["IsPhotoAllowed"]);
                    marker.IsRequired = Convert.ToBoolean(dr["IsRequired"]);
                    marker.IsMultiSection = Convert.ToBoolean(dr["IsMultiSection"]);
                    marker.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                    marker.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);

                    marker.ParentId = Convert.ToInt32(dr["ParentId"]);

                    marker.MarkerOptions = new List<MarkerOption>();
                    markers.Add(marker);
                }
            }
            return markers;
        }

        public static List<MarkerOption> GetAllMarkerOptions(SqlDataReader dr)
        {
            List<MarkerOption> markerOptions = new List<MarkerOption>();
            MarkerOption markerOption = null;

            if (dr.HasRows)
            {
                while (dr.Read())
                {
                    //Id MarkerId    Title Ordering    CreatedDate IsDeleted
                    markerOption = new MarkerOption();
                    markerOption.Id = Convert.ToInt32(dr["Id"]);
                    markerOption.MarkerId = Convert.ToInt32(dr["MarkerId"]);
                    markerOption.Title = Convert.ToString(dr["Title"]);
                    markerOption.Ordering = Convert.ToInt32(dr["Ordering"]);
                    markerOption.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                    markerOption.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                    markerOptions.Add(markerOption);
                }
            }
            return markerOptions;
        }

        public static List<PreInspection> GetAllPreInspections(SqlDataReader dr)
        {
            List<PreInspection> preInspections = new List<PreInspection>();
            PreInspection preInspection = null;
            if (dr.HasRows)
            {
                while (dr.Read())
                {
                    preInspection = new PreInspection();
                    preInspection.Id = Convert.ToInt32(dr["Id"]);
                    preInspection.InspectionTemplateId = Convert.ToInt32(dr["InspectionTemplateId"]);
                    preInspection.Title = Convert.ToString(dr["Title"]);
                    preInspection.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                    preInspection.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                    preInspections.Add(preInspection);
                }
            }

            // User PreInspections
            //dr.NextResult();
            //if (dr.HasRows)
            //{
            //    while (dr.Read())
            //    {
            //        preInspection = new PreInspection();
            //        preInspection.Id = Convert.ToInt32(dr["Id"]);
            //        preInspection.Title = Convert.ToString(dr["Title"]);
            //        preInspection.Description = Convert.ToString(dr["Description"]);
            //        preInspection.UserNum = Convert.ToInt32(dr["UserNum"]);
            //        preInspection.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
            //        preInspection.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);

            //        // Default Values
            //        preInspection.InspectionTemplateId = 0;
            //        preInspection.IsUserPreInspection = true;

            //        preInspections.Add(preInspection);
            //    }
            //}

            return preInspections;
        }

        public static List<PreInspection> GetAllUserPreInspections(SqlDataReader dr)
        {
            List<PreInspection> preInspections = new List<PreInspection>();
            PreInspection preInspection = null;
            if (dr.HasRows)
            {
                while (dr.Read())
                {
                    preInspection = new PreInspection();
                    preInspection.Id = Convert.ToInt32(dr["Id"]);
                    preInspection.Title = Convert.ToString(dr["Title"]);
                    preInspection.Description = Convert.ToString(dr["Description"]);
                    preInspection.UserNum = Convert.ToInt32(dr["UserNum"]);
                    preInspection.GroupId = Convert.ToInt32(dr["GroupId"]);
                    preInspection.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                    preInspection.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);

                    // Default Value
                    preInspection.IsUserPreInspection = true;

                    preInspections.Add(preInspection);
                }
            }

            return preInspections;
        }

        public static List<PreInspectionGroup> GetAllUserPreInspectionGroup(SqlDataReader dr)
        {
            List<PreInspectionGroup> groupList = new List<PreInspectionGroup>();
            PreInspectionGroup group = null;
            if (dr.HasRows)
            {
                while (dr.Read())
                {
                    group = new PreInspectionGroup();
                    group.Id = Convert.ToInt32(dr["Id"]);
                    group.Name = Convert.ToString(dr["Name"]);
                    group.Description = Convert.ToString(dr["Description"]);
                    group.UserNum = Convert.ToInt32(dr["UserNum"]);
                    group.CreatedOn = Convert.ToDateTime(dr["CreatedOn"]);
                    groupList.Add(group);
                }
            }

            return groupList;
        }
        public static List<PreInspection> GetAllUserPreInspectionGroupField(SqlDataReader dr)
        {
            List<PreInspection> groupList = new List<PreInspection>();
            PreInspection group = null;
            if (dr.HasRows)
            {
                while (dr.Read())
                {
                    group = new PreInspection();
                    group.Id = Convert.ToInt32(dr["Id"]);
                    group.Title = Convert.ToString(dr["Name"]);
                    group.Description = Convert.ToString(dr["Description"]);
                    group.UserNum = Convert.ToInt32(dr["UserNum"]);
                    group.CreatedDate = Convert.ToDateTime(dr["CreatedOn"]);
                    group.GroupId = Convert.ToInt32(dr["Id"]);
                    groupList.Add(group);
                }
            }

            return groupList;
        }
        #endregion

        public static List<Section> MapInspectionSections(SqlDataReader dr)
        {
            List<Section> inspectionSections = new List<Section>();
            Section inspectionSection = null;
            while (dr.Read())
            {
                inspectionSection = new Section();
                inspectionSection.Id = (int)dr["Id"];
                inspectionSection.Title = Convert.ToString(dr["Title"]);
                inspectionSection.InspectionTemplateId = (int)dr["InspectionTemplateId"];
                inspectionSection.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                inspectionSection.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                inspectionSection.SectionType = (SectionType)Convert.ToInt32(dr["SectionType"]);
                inspectionSection.SectionCategory = (SectionCategory)Convert.ToInt32(dr["SectionCategory"]);
                inspectionSection.IsDefaultSection = Convert.ToBoolean(dr["IsDefaultSection"]);
                inspectionSection.NoOfMarkers = Convert.ToInt32(dr["NoOfMarkers"]);
                inspectionSection.AllowCopySection = Convert.ToBoolean(dr["AllowCopySection"]);

                inspectionSections.Add(inspectionSection);
            }
            return inspectionSections;
        }
        public static List<Marker> MapMarkers(SqlDataReader dr)
        {
            try
            {
                List<Marker> markers = new List<Marker>();
                Marker marker = null;
                while (dr.Read())
                {
                    //if (questions == null) questions = new List<Question>();
                    marker = new Marker();
                    marker.Id = Convert.ToInt32(dr["Id"]);
                    marker.InspectionTemplateId = Convert.ToInt32(dr["InspectionTemplateId"]);
                    marker.SectionId = Convert.ToInt32(dr["SectionId"]);
                    marker.MarkerTypeId = Convert.ToInt32(dr["MarkerTypeId"]);
                    marker.Title = Convert.ToString(dr["Title"]);
                    marker.Note = Convert.ToString(dr["Note"]);
                    marker.Description = Convert.ToString(dr["Description"]);
                    marker.Ordering = Convert.ToInt32(dr["Ordering"]);
                    marker.IsRepeating = Convert.ToBoolean(dr["IsRepeating"]);
                    marker.IsPhotoAllowed = Convert.ToBoolean(dr["IsPhotoAllowed"]);
                    marker.IsRequired = Convert.ToBoolean(dr["IsRequired"]);
                    marker.IsMultiSection = Convert.ToBoolean(dr["IsMultiSection"]);
                    marker.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                    marker.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                    marker.MarkerOptions = new List<MarkerOption>();
                    markers.Add(marker);
                }
                return markers;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public static Inspection MapInspection(SqlDataReader dr)
        {
            Inspection inspection = null;
            while (dr.Read())
            {
                //Id InspectionTemplateId    EventId Title   Description InspectorId InspectionDate Comments    Remarks CreatedDate IsDeleted InspectorName
                inspection = new Inspection();
                inspection.Id = Convert.ToInt32(dr["Id"]);
                inspection.InspectionTemplateId = Convert.ToInt32(dr["InspectionTemplateId"]);
                inspection.EventId = Convert.ToString(dr["EventId"]);
                inspection.Title = Convert.ToString(dr["Title"]);
                inspection.Description = Convert.ToString(dr["Description"]);
                inspection.InspectorId = Convert.ToInt32(dr["InspectorId"]);
                inspection.InspectionDate = Convert.ToDateTime(dr["InspectionDate"]);
                inspection.Comments = Convert.ToString(dr["Comments"]);
                inspection.Remarks = Convert.ToString(dr["Remarks"]);
                inspection.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                inspection.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                inspection.InspectorName = Convert.ToString(dr["InspectorName"]);

                inspection.StartTime = Convert.ToString(dr["StartTime"]);
                inspection.Duration = Convert.ToString(dr["Duration"]);
                inspection.NoOfMarkers = Convert.ToInt32(dr["NoOfMarkers"]);
                inspection.NoOfSections = Convert.ToInt32(dr["NoOfSections"]);
                inspection.EventNotes = Convert.ToString(dr["EventNotes"]);
                inspection.GPS = Convert.ToString(dr["GPS"]);
                inspection.HasPhotoMarker = DBRecordExtensions.HasColumn(dr, "HasPhotoMarker") ? Convert.ToBoolean(dr["HasPhotoMarker"]) : false;
                inspection.PhotoFileNameCSV = DBRecordExtensions.HasColumn(dr, "PhotoFileNameCSV") ? Convert.ToString(dr["PhotoFileNameCSV"]) : "";
            }
            return inspection;
        }

        public static List<MarkerSection> GetMarkerSections(SqlDataReader dr)
        {
            List<MarkerSection> markerSections = new List<MarkerSection>();
            MarkerSection markerSection = null;

            if (dr.HasRows)
            {
                while (dr.Read())
                {
                    markerSection = new MarkerSection();
                    markerSection.Id = Convert.ToInt32(dr["Id"]);
                    markerSection.InspectionTemplateId = Convert.ToInt32(dr["InspectionTemplateId"]);
                    markerSection.MarkerId = Convert.ToInt32(dr["MarkerId"]);
                    markerSection.SectionId = Convert.ToInt32(dr["SectionId"]);
                    markerSection.SectionTitle = Convert.ToString(dr["SectionTitle"]);
                    markerSection.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                    markerSection.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                    markerSections.Add(markerSection);
                }
            }
            return markerSections;
        }

        public static GraphicMarkerDetail GetGraphicMarker(SqlDataReader dr)
        {
            GraphicMarkerDetail graphicMarker = null;

            if (dr.HasRows)
            {
                while (dr.Read())
                {
                    graphicMarker = new GraphicMarkerDetail();
                    graphicMarker.Id = Convert.ToInt32(dr["Id"]);
                    graphicMarker.InspectionTemplateId = Convert.ToInt32(dr["InspectionTemplateId"]);
                    graphicMarker.MarkerTypeId = Convert.ToInt32(dr["MarkerTypeId"]);
                    graphicMarker.Title = Convert.ToString(dr["Title"]);
                    graphicMarker.Note = Convert.ToString(dr["Note"]);
                    graphicMarker.Description = Convert.ToString(dr["Description"]);
                    graphicMarker.Ordering = Convert.ToInt32(dr["Ordering"]);
                    graphicMarker.IsRepeating = Convert.ToBoolean(dr["IsRepeating"]);
                    graphicMarker.IsPhotoAllowed = Convert.ToBoolean(dr["IsPhotoAllowed"]);
                    graphicMarker.IsRequired = Convert.ToBoolean(dr["IsRequired"]);
                    graphicMarker.IsMultiSection = Convert.ToBoolean(dr["IsMultiSection"]);
                    graphicMarker.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                    graphicMarker.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                }
            }
            return graphicMarker;
        }

        public static List<IQ3InspectionParameter> GetAllInspectionParameters(SqlDataReader dr)
        {
            List<IQ3InspectionParameter> iq3InspectionParameters = new List<IQ3InspectionParameter>();
            IQ3InspectionParameter iq3InspectionParameter = null;

            if (dr.HasRows)
            {
                while (dr.Read())
                {
                    iq3InspectionParameter = new IQ3InspectionParameter();

                    iq3InspectionParameter.Id = Convert.ToInt32(dr["Id"]);
                    iq3InspectionParameter.Data = Convert.ToString(dr["Data"]);

                    iq3InspectionParameters.Add(iq3InspectionParameter);
                }
            }
            return iq3InspectionParameters;
        }

        #region IQ3 Asset

        public static Object GetIQ3Asset(SqlDataReader dr, int NumFields)
        {

            if (dr.HasRows)
            {
                dynamic iq3Asset = new System.Dynamic.ExpandoObject();

                if (dr.Read())
                {
                    iq3Asset.Id = Convert.ToInt32(dr["Id"]);
                    iq3Asset.Status = Convert.ToInt32(dr["Status"]);
                    iq3Asset.CreatedOn = Convert.ToDateTime(dr["CreatedOn"]);
                    iq3Asset.AssetId = Convert.ToString(dr["AssetId"]);
                    iq3Asset.AssetPhoto = Convert.ToString(dr["AssetPhoto"]);

                    // Skipping first 7 fields
                    for (int i = 8; i < dr.FieldCount; i++)
                    {
                        //string FieldName = string.Format("Field{0}", i);
                        ((IDictionary<String, Object>)iq3Asset).Add(dr.GetName(i), Convert.ToString(dr.GetValue(i)));
                    }

                    //return iq3Asset;
                }

                dr.NextResult();
                List<IQ3AssetDetail> iq3AssetDetails = new List<IQ3AssetDetail>();
                while (dr.Read())
                {
                    IQ3AssetDetail iq3AssetDetail = new IQ3AssetDetail();
                    iq3AssetDetail.Id = Convert.ToInt32(dr["Id"]);
                    iq3AssetDetail.Name = Convert.ToString(dr["Name"]);
                    iq3AssetDetail.Value = Convert.ToString(dr["Value"]);

                    iq3AssetDetails.Add(iq3AssetDetail);
                }

                iq3Asset.IQ3AssetDetails = iq3AssetDetails;

                return iq3Asset;
            }
            return null;
        }

        public static List<IQ3AssetModel> GetIQ3AssetsModel(SqlDataReader dr)
        {
            List<IQ3AssetModel> iq3AssetsModel = iq3AssetsModel = new List<IQ3AssetModel>();
            if (dr.HasRows)
            {
                IQ3AssetModel iq3Model = null;
                while (dr.Read())
                {
                    iq3Model = new IQ3AssetModel();
                    iq3Model.IQ3AssetModelId = Convert.ToInt32(dr["Id"]);
                    iq3Model.Name = Convert.ToString(dr["Name"]);
                    iq3Model.Serial = (dr["Serial"] != DBNull.Value) ? Convert.ToInt32(dr["Serial"]) : 0;
                    iq3Model.Alignment = Convert.ToString(dr["Alignment"]);
                    iq3Model.Caption = (dr["Caption"] != DBNull.Value) ? Convert.ToString(dr["Caption"]) : string.Empty;
                    iq3Model.IsVisible = Convert.ToBoolean(dr["IsVisible"]);

                    iq3AssetsModel.Add(iq3Model);
                }
            }

            return iq3AssetsModel;
        }

        public static List<Object> GetIQ3Assets(SqlDataReader dr, int NumFields)
        {
            List<Object> iq3AssetList = new List<Object>();

            if (dr.HasRows)
            {
                iq3AssetList = new List<Object>();

                while (dr.Read())
                {
                    dynamic iq3Asset = new System.Dynamic.ExpandoObject();
                    iq3Asset.Id = Convert.ToInt32(dr["Id"]);
                    iq3Asset.Status = Convert.ToInt32(dr["Status"]);
                    iq3Asset.CreatedOn = Convert.ToDateTime(dr["CreatedOn"]);
                    iq3Asset.AssetId = Convert.ToString(dr["AssetId"]);
                    iq3Asset.AssetPhoto = Convert.ToString(dr["AssetPhoto"]);

                    // Skipping first 7 fields
                    for (int i = 8; i < dr.FieldCount; i++)
                    {
                        //string FieldName = string.Format("Field{0}", i);
                        ((IDictionary<String, Object>)iq3Asset).Add(dr.GetName(i), Convert.ToString(dr.GetValue(i)));
                    }

                    iq3AssetList.Add(iq3Asset);
                }
            }
            return iq3AssetList;
        }

        public static List<IQ3AssetHistory> GetIQ3AssetHistory(SqlDataReader dr)
        {
            List<IQ3AssetHistory> iq3AssetHistoryList = null;

            if (dr.HasRows)
            {
                iq3AssetHistoryList = new List<IQ3AssetHistory>();

                while (dr.Read())
                {
                    IQ3AssetHistory iq3AssetHistory = new IQ3AssetHistory();
                    iq3AssetHistory.Id = Convert.ToInt32(dr["Id"]);
                    iq3AssetHistory.UserNum = Convert.ToInt32(dr["UserNum"]);
                    iq3AssetHistory.AssetId = Convert.ToInt32(dr["AssetId"]);
                    iq3AssetHistory.UserName = Convert.ToString(dr["UserName"]);
                    iq3AssetHistory.Field = Convert.ToString(dr["Field"]);
                    iq3AssetHistory.FieldTitle = Convert.ToString(dr["FieldTitle"]);
                    iq3AssetHistory.ChangedFrom = Convert.ToString(dr["ChangedFrom"]);
                    iq3AssetHistory.ChangedTo = Convert.ToString(dr["ChangedTo"]);
                    iq3AssetHistory.CreatedOn = Convert.ToDateTime(dr["CreatedOn"]);
                    iq3AssetHistory.HistoryEvent = (IQ3AssetHistoryEvent)Convert.ToInt32(dr["HistoryEvent"]);

                    iq3AssetHistoryList.Add(iq3AssetHistory);
                }
            }
            return iq3AssetHistoryList;
        }

        public static List<Asset> FetchAllAssets(SqlDataReader dr)
        {
            List<Asset> iq3AssetList = null;

            if (dr.HasRows)
            {
                iq3AssetList = new List<Asset>();

                while (dr.Read())
                {
                    Asset iq3Asset = new Asset();
                    iq3Asset.Id = Convert.ToInt32(dr["Id"]);
                    iq3Asset.Status = Convert.ToInt32(dr["Status"]);
                    iq3Asset.CreatedOn = Convert.ToDateTime(dr["CreatedOn"]);
                    iq3Asset.AssetId = Convert.ToString(dr["AssetId"]);
                    iq3Asset.AssetPhoto = Convert.ToString(dr["AssetPhoto"]);
                    iq3Asset.Field3 = Convert.ToString(dr["Field3"]);
                    //iq3Asset.UserNum = Convert.ToString(dr["UserNum"]);
                    iq3AssetList.Add(iq3Asset);
                }
            }
            return iq3AssetList;
        }

        public static List<AssetDetail> FetchAssetDetailsById(SqlDataReader dr)
        {
            List<AssetDetail> assetDetails = null;

            if (dr.HasRows)
            {
                assetDetails = new List<AssetDetail>();
                //Id AssetId Name Value   CreatedOn UpdatedOn   IsDeleted IsVisible
                while (dr.Read())
                {
                    AssetDetail assetDetail = new AssetDetail();
                    assetDetail.Id = Convert.ToInt32(dr["Id"]);
                    assetDetail.AssetId = Convert.ToInt32(dr["AssetId"]);
                    assetDetail.CreatedOn = Convert.ToDateTime(dr["CreatedOn"]);
                    assetDetail.Name = Convert.ToString(dr["Name"]);
                    assetDetail.Value = Convert.ToString(dr["Value"]);
                    assetDetail.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                    assetDetail.IsVisible = Convert.ToBoolean(dr["IsVisible"]);
                    assetDetails.Add(assetDetail);
                }
            }
            return assetDetails;
        }

        #endregion IQ3 Asset

        #region MGO Data

        public static MGOTempData GetMGOTempData(SqlDataReader dr)
        {
            MGOTempData tempData = null;

            if (dr.HasRows)
            {
                if (dr.Read())
                {
                    tempData = new MGOTempData();
                    tempData.Id = Convert.ToInt32(dr["Id"]);
                    tempData.Notes = Convert.ToString(dr["Notes"]);
                    tempData.ContractorName = Convert.ToString(dr["ContractorName"]);
                    tempData.ContractorEmail = Convert.ToString(dr["ContractorEmail"]);
                    tempData.ContractorPhoneNumber = Convert.ToString(dr["ContractorPhoneNumber"]);
                    tempData.JurisdictionID = Convert.ToString(dr["JurisdictionID"]);
                    tempData.WorkOrderID = Convert.ToString(dr["WorkOrderID"]);
                    tempData.ProjectTypeID = Convert.ToInt32(dr["ProjectTypeID"]);
                    tempData.PermitID = Convert.ToString(dr["PermitID"]);
                    tempData.Address = Convert.ToString(dr["Address"]);
                    tempData.ScheduledDate = Convert.ToDateTime(dr["ScheduledDate"]);
                    tempData.Latitude = Convert.ToString(dr["Latitude"]);
                    tempData.Longitude = Convert.ToString(dr["Longitude"]);
                    tempData.InspectionTypeID = dr["InspectionTypeID"] != DBNull.Value ? Convert.ToInt32(Convert.ToString(dr["InspectionTypeID"])) : 0;
                }
            }

            return tempData;
        }

        public static MGOReportData GetMGOReportData(SqlDataReader dr)
        {
            MGOReportData reportData = null;

            if (dr.HasRows)
            {
                if (dr.Read())
                {
                    reportData = new MGOReportData();
                    reportData.Id = Convert.ToInt32(dr["Id"]);
                    reportData.EventId = Convert.ToString(dr["EventId"]);
                    reportData.ContractorName = Convert.ToString(dr["ContractorName"]);
                    reportData.ContractorEmail = Convert.ToString(dr["ContractorEmail"]);
                    reportData.ContractorPhoneNumber = Convert.ToString(dr["ContractorPhoneNumber"]);
                    reportData.WorkOrderID = Convert.ToString(dr["WorkOrderID"]);
                    reportData.PermitID = Convert.ToString(dr["PermitID"]);
                    reportData.Address = Convert.ToString(dr["Address"]);
                    reportData.Latitude = Convert.ToString(dr["Latitude"]);
                    reportData.Longitude = Convert.ToString(dr["Longitude"]);

                    reportData.ProjectTypeID = Convert.ToInt32(dr["ProjectTypeID"]);
                    reportData.InspectionTypeID = Convert.ToInt32(dr["InspectionTypeID"]);
                    reportData.InspectionType = Convert.ToString(dr["InspectionType"]);
                    reportData.CategoryID = Convert.ToInt32(dr["CategoryID"]);
                    reportData.Category = Convert.ToString(dr["Category"]);

                    reportData.InspectionOptionID = dr["InspectionTypeID"] != DBNull.Value ? Convert.ToInt32(Convert.ToString(dr["InspectionTypeID"])) : 0;
                    reportData.InspectionOption = Convert.ToString(dr["InspectionOption"]);
                    reportData.InspectionOptionNotes = Convert.ToString(dr["InspectionOptionNotes"]);
                }
            }

            return reportData;
        }

        #endregion MGO Data

        #region Conditional Logics
        public static List<MarkerLogic> GetAllMarkerLogics(SqlDataReader dr)
        {
            List<MarkerLogic> markerLogics = new List<MarkerLogic>();
            MarkerLogic markerLogic = null;

            if (dr.HasRows)
            {
                while (dr.Read())
                {
                    markerLogic = new MarkerLogic();
                    markerLogic.Id = Convert.ToInt32(dr["Id"]);
                    markerLogic.MarkerId = Convert.ToInt32(dr["MarkerId"]);
                    markerLogic.MarkerOptionId = Convert.ToInt32(dr["MarkerOptionId"]);
                    markerLogic.MatchText = Convert.ToString(dr["MatchText"]);
                    markerLogic.LogicType = (MarkerLogicType)Enum.Parse(typeof(MarkerLogicType), (Convert.ToInt32(dr["LogicTypeId"])).ToString());
                    markerLogics.Add(markerLogic);
                }
            }
            return markerLogics;
        }

        public static List<LogicTrigger> GetLogicTriggers(SqlDataReader dr)
        {
            List<LogicTrigger> logicTriggers = null;
            LogicTrigger logicTrigger = null;

            if (dr.HasRows)
            {
                logicTriggers = new List<LogicTrigger>();
                while (dr.Read())
                {
                    logicTrigger = new LogicTrigger();
                    logicTrigger.Id = Convert.ToInt32(dr["Id"]);
                    logicTrigger.LogicId = Convert.ToInt32(dr["LogicId"]);
                    logicTrigger.TriggerType = (TriggerType)Enum.Parse(typeof(TriggerType), (Convert.ToInt32(dr["TriggerTypeId"])).ToString());
                    logicTriggers.Add(logicTrigger);
                }
            }
            return logicTriggers;
        }

        public static TriggerAction GetTriggerAction(SqlDataReader dr)
        {
            TriggerAction triggerAction = null;

            if (dr.HasRows)
            {
                while (dr.Read())
                {
                    triggerAction = new TriggerAction();
                    triggerAction.Id = Convert.ToInt32(dr["Id"]);
                    triggerAction.MarkerId = Convert.ToInt32(dr["MarkerId"]);
                    triggerAction.MarkerLogicId = Convert.ToInt32(dr["MarkerLogicId"]);
                    triggerAction.TriggerId = Convert.ToInt32(dr["TriggerId"]);
                    triggerAction.TriggerType = (TriggerType)Enum.Parse(typeof(TriggerType), (Convert.ToInt32(dr["TriggerTypeId"])).ToString());
                    triggerAction.CSVEmail = Convert.ToString(dr["CSVEmail"]);
                    triggerAction.CSVPhoneNumber = Convert.ToString(dr["CSVPhoneNumber"]);
                    triggerAction.MarkerToFocusJSON = Convert.ToString(dr["MarkerToFocusJSON"]);
                    triggerAction.MarkersToHideJSON = Convert.ToString(dr["MarkersToHideJSON"]);
                    triggerAction.MarkersToUnHideJSON = Convert.ToString(dr["MarkersToUnHideJSON"]);
                }
            }
            return triggerAction;
        }
        #endregion

        #region MGO - Inspection Type

        public static List<MGOInspectionType> GetMGOInspectionTypes(SqlDataReader dr)
        {
            List<MGOInspectionType> inspectionTypes = new List<MGOInspectionType>();
            if (dr.HasRows)
            {
                while (dr.Read())
                {
                    MGOInspectionType inspectionType = null;
                    inspectionType = new MGOInspectionType();

                    inspectionType.InspectionTypeID = Convert.ToInt32(dr["InspectionTypeID"]);
                    inspectionType.InspectionType = Convert.ToString(dr["InspectionType"]);

                    inspectionType.CategoryID = Convert.ToInt32(dr["CategoryID"]);
                    inspectionType.Category = Convert.ToString(dr["Category"]);

                    inspectionType.ProjectTypeID = dr["ProjectTypeID"] == DBNull.Value ? 0 : Convert.ToInt32(dr["ProjectTypeID"]);

                    inspectionTypes.Add(inspectionType);
                }
            }
            return inspectionTypes;
        }

        public static List<MGOInspectionOption> GetMGOInspectionOptions(SqlDataReader dr)
        {
            List<MGOInspectionOption> inspectionOptions = new List<MGOInspectionOption>();

            if (dr.HasRows)
            {
                while (dr.Read())
                {
                    MGOInspectionOption inspectionOption = null;
                    inspectionOption = new MGOInspectionOption();

                    inspectionOption.ID = Convert.ToInt32(dr["ID"]);
                    inspectionOption.Description = Convert.ToString(dr["Description"]);
                    inspectionOption.ProjectTypeID = dr["ProjectTypeID"] == DBNull.Value ? 0 : Convert.ToInt32(dr["ProjectTypeID"]);

                    inspectionOptions.Add(inspectionOption);
                }
            }
            return inspectionOptions;
        }

        #endregion MGO - Inspection Type

    }
}