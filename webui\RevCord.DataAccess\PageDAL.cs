﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data.SqlClient;
using System.Data;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.DataContracts.SurveyEntities;
using RevCord.DataContracts.Response;
using RevCord.DataContracts.Request;
using RevCord.DataContracts.ViewModelEntities;
using RevCord.DataContracts.Messages;
using RevCord.DataContracts.TenantEntities;
using System.Threading.Tasks;
using RevCord.DataAccess.Util;
using RevCord.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.ReportEntities;

namespace RevCord.DataAccess
{
    public class PageDAL
    {
        private int _tenantId;
        public PageDAL(int tenantId)
        {
            this._tenantId = tenantId;
        }
        #region Get Search Page Data

        public VoiceRecResponse GetSearchPageData(VoiceRecRequest voiceRecRequest)
        {
            List<TreeviewData> treeNodes = null;
            List<Playlist> playlists = null;
            List<Survey> surveys = null;
            TreeviewData treeNode = null;
            Playlist playlist = null;
            Survey survey = null;
            //bool isChainDBsConfigured = false;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = "vr_GetData_SearchPage";
                    cmd.Parameters.AddWithValue("@UserId", voiceRecRequest.UserId);
                    cmd.Parameters.AddWithValue("@UserType", voiceRecRequest.UserType);
                    //cmd.Parameters.AddWithValue("@AuthNum", authNum);
                    //cmd.Parameters.AddWithValue("@AuthType", authType);
                    //cmd.Parameters.AddWithValue("@Type", type);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.PageDAL, "GetSearchPageData", _tenantId));

                    //Total 4 Tables return.
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        //1. Treeview ResultSet
                        if (dr.HasRows)
                        {
                            treeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);

                                treeNodes.Add(treeNode);
                            }
                        }
                        //2. Playlist ResultSet
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            playlists = new List<Playlist>();
                            while (dr.Read())
                            {
                                playlist = new Playlist();
                                playlist.Id = (int)dr["Id"];
                                playlist.Name = Convert.ToString(dr["Name"]);
                                playlist.Comments = Convert.ToString(dr["Description"]);
                                playlist.NoOfCalls = Convert.ToInt32(dr["NoOfCalls"]);

                                playlists.Add(playlist);
                            }
                        }
                        //3. Survey ResultSet
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            surveys = new List<Survey>();
                            while (dr.Read())
                            {
                                survey = new Survey();
                                survey.Id = (int)dr["Id"];
                                survey.Name = Convert.ToString(dr["Title"]);

                                surveys.Add(survey);
                            }
                        }
                        ////4. Chain DB's Existis ResultSet
                        //dr.NextResult();
                        //if (dr.Read())//(dr.HasRows)
                        //{
                        //    //dr.Read();
                        //    isChainDBsConfigured = Convert.ToInt32(dr["DBCount"]) > 1 ? true : false;
                        //}
                    }
                }
                return new VoiceRecResponse
                {
                    TreeviewData = treeNodes,
                    Playlists = playlists,
                    Surveys = surveys,
                    //IsChainDBsConfigured= isChainDBsConfigured
                };
            }
            catch (Exception ex) { throw ex; }
            //return VoiceRecResponse;
        }

        public VRResponse GetSearchPageData(VRRequest request)
        {
            List<TreeviewData> treeNodes = null;
            List<TreeviewData> teamsTreeNodes = null;
            List<TreeviewData> revcellTreeNodes = null;
            List<TreeviewData> RadioTalkGroupTreeNodes = null;
            List<TreeviewData> iwbTreeNodes = null;
            List<Playlist> playlists = null;
            List<Survey> surveys = null;
            TreeviewData treeNode = null;
            Playlist playlist = null;
            Survey survey = null;
            //bool isChainDBsConfigured = false;
            List<TenantColumnModel> columnsModel = null;
            List<CallTag> lCallTag = null; 
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = "vr_GetData_SearchPage";
                    cmd.Parameters.AddWithValue("@UserId", request.UserId);
                    cmd.Parameters.AddWithValue("@UserType", request.UserType);
                    cmd.Parameters.AddWithValue("@IsOnlyIQ3ModeEnabled", request.IsOnlyIQ3ModeEnabled);
                    cmd.Parameters.AddWithValue("@IsIwb", request.IsIwbModeEnabled);

                    //cmd.Parameters.AddWithValue("@AuthNum", authNum);
                    //cmd.Parameters.AddWithValue("@AuthType", authType);
                    //cmd.Parameters.AddWithValue("@Type", type);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.PageDAL, "GetSearchPageData", _tenantId));

                    //Total 7 Tables return.
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        //1. Treeview ResultSet
                        if (dr.HasRows)
                        {
                            treeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);
                                treeNode.IsRevCell = Convert.ToBoolean(dr["IsRevCell"]);
                                treeNode.IsRevAgentAssociated = Convert.ToBoolean(dr["IsRevAgentAssociated"]);
                                treeNode.IsText911 = DBRecordExtensions.HasColumn(dr, "IsText911") ? Convert.ToBoolean(dr["IsText911"]) : false;
                                treeNodes.Add(treeNode);
                            }
                        }

                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            RadioTalkGroupTreeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);

                                RadioTalkGroupTreeNodes.Add(treeNode);
                            }
                        }

                        // Revcell ResultSet
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            revcellTreeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);

                                revcellTreeNodes.Add(treeNode);
                            }
                        }

                        // Teams ResultSet
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            teamsTreeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);

                                teamsTreeNodes.Add(treeNode);
                            }
                        }

                        //Iwb Treeview ResultSet 
                        if (request.IsIwbModeEnabled)
                        {
                            dr.NextResult();
                            if (dr.HasRows)
                            {
                                iwbTreeNodes = new List<TreeviewData>();
                                while (dr.Read())
                                {
                                    treeNode = new TreeviewData();

                                    treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                    treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                    treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                    treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                    treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                    treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                    treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                    treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                    treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                    treeNode.Param3 = Convert.ToString(dr["Param3"]);

                                    iwbTreeNodes.Add(treeNode);
                                }
                            }
                        }

                        //2. Playlist ResultSet
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            playlists = new List<Playlist>();
                            while (dr.Read())
                            {
                                playlist = new Playlist();
                                playlist.Id = (int)dr["Id"];
                                playlist.Name = Convert.ToString(dr["Name"]);
                                playlist.Comments = Convert.ToString(dr["Description"]);
                                playlist.NoOfCalls = Convert.ToInt32(dr["NoOfCalls"]);
                                playlist.RevSyncServerID = Convert.ToInt32(dr["RevSyncServerID"]);
                                playlist.IsSyncedFromClient = Convert.ToInt32(dr["IsSyncedFromClient"]);

                                playlists.Add(playlist);
                            }
                        }
                        //3. Survey ResultSet
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            surveys = new List<Survey>();
                            while (dr.Read())
                            {
                                survey = new Survey();
                                survey.Id = (int)dr["Id"];
                                survey.Name = Convert.ToString(dr["Title"]);
                                survey.RevSyncSurveyId = (int)dr["RevSyncServerId"];
                                surveys.Add(survey);
                            }
                        }
                        ////4. Chain DB's Existis ResultSet
                        //dr.NextResult();
                        //if (dr.Read())//(dr.HasRows)
                        //{
                        //    //dr.Read();
                        //    isChainDBsConfigured = Convert.ToInt32(dr["DBCount"]) > 1 ? true : false;
                        //}
                        //5. Column Model ResultSet
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            columnsModel = new List<TenantColumnModel>();
                            TenantColumnModel colModel = null;
                            while (dr.Read())
                            {
                                colModel = new TenantColumnModel();
                                colModel.ColumnModelId = Convert.ToInt16(dr["Id"]);//(int)dr["ColumnModelId"];
                                colModel.Name = Convert.ToString(dr["Name"]);
                                colModel.SNo = Convert.ToInt16(dr["Serial"]);
                                colModel.Width = Convert.ToInt16(dr["Width"]);
                                colModel.Alignment = Convert.ToString(dr["Alignment"]);
                                colModel.Caption = Convert.ToString(dr["Caption"]);
                                colModel.IsHidden = Convert.ToBoolean(dr["IsHidden"]);
                                colModel.UserNum = Convert.ToInt32(dr["UserNum"]);
                                colModel.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                                colModel.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                                columnsModel.Add(colModel);
                            }
                        }

                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            lCallTag = new List<CallTag>();
                            CallTag _CallTag = null;
                            while (dr.Read())
                            {
                                _CallTag = new CallTag();
                                _CallTag.TagID = Convert.ToInt16(dr["Id"]);//(int)dr["ColumnModelId"];
                                _CallTag.TagColor = Convert.ToString(dr["TagColorID"]);
                                _CallTag.TagName = Convert.ToString(dr["TagName"]);

                                lCallTag.Add(_CallTag);
                            }
                        }
                    }
                }
                return new VRResponse
                {
                    TreeviewData = treeNodes,
                    TreeviewDataTeams = teamsTreeNodes,
                    TreeviewDataRevcell = revcellTreeNodes,
                    TreeviewDataRadioTalkGroup = RadioTalkGroupTreeNodes,
                    TreeviewDataIwb = iwbTreeNodes,
                    Playlists = playlists,
                    Surveys = surveys,
                    //IsChainDBsConfigured= isChainDBsConfigured
                    ColumnsModel = columnsModel,
                    CallTag = lCallTag,
                };
            }
            catch (Exception ex) { throw ex; }
            //return VoiceRecResponse;
        }

        public VRResponse GetSearchPageData_Inquire(VRRequest request)
        {
            List<TreeviewData> treeNodes = null;
            List<Playlist> playlists = null;
            List<Survey> surveys = null;
            TreeviewData treeNode = null;
            Playlist playlist = null;
            Survey survey = null;
            //bool isChainDBsConfigured = false;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = "vr_GetData_SearchPage";
                    cmd.Parameters.AddWithValue("@UserId", request.UserId);
                    cmd.Parameters.AddWithValue("@UserType", request.UserType);
                    //cmd.Parameters.AddWithValue("@AuthNum", authNum);
                    //cmd.Parameters.AddWithValue("@AuthType", authType);
                    cmd.Parameters.AddWithValue("@Type", 7);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.PageDAL, "GetSearchPageData_Inquire", _tenantId));

                    //Total 4 Tables return.
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        //1. Treeview ResultSet
                        if (dr.HasRows)
                        {
                            treeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);
                                treeNode.IsText911 = DBRecordExtensions.HasColumn(dr, "IsText911") ? Convert.ToBoolean(dr["IsText911"]) : false;

                                treeNodes.Add(treeNode);
                            }
                        }
                    }
                }
                return new VRResponse
                {
                    TreeviewData = treeNodes,
                    Playlists = playlists,
                    Surveys = surveys,
                    //IsChainDBsConfigured= isChainDBsConfigured
                };
            }
            catch (Exception ex) { throw ex; }
            //return VoiceRecResponse;
        }

        public VRResponse GetRevcordAndInquireSearchPageData(VRRequest request)
        {
            List<TreeviewData> treeNodes = null;
            List<TreeviewData> inquireTreeNodes = null;
            List<TreeviewData> mdTreeNodes = null;
            List<TreeviewData> RadioTalkGroupTreeNodes = null;
            List<TreeviewData> ArchiveTreeNodes = null;
            List<TreeviewData> teamsTreeNodes = new List<TreeviewData>();
            List<TreeviewData> revcellTreeNodes = null;
            List<TreeviewData> iwbTreeNodes = null;
            List<Playlist> playlists = null;
            List<Survey> surveys = null;
            TreeviewData treeNode = null;
            Playlist playlist = null;
            Survey survey = null;
            List<TenantColumnModel> columnsModel = null;
            List<CallTag> lCallTag = null;
            List<BmFlag> lBmFlag = null;
            var isArchive = request.IsRoleBasedAccessEnabled ? (request.Role.IsSystemRole ? 1 : 0) : request.UserType;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = request.Role.Id == 0 ? "vr_GetData_SearchPage" : "vr_GetData_SearchPage_Roles";
                    cmd.Parameters.AddWithValue("@UserId", request.UserId);
                    cmd.Parameters.AddWithValue("@UserType", request.UserType);
                    cmd.Parameters.AddWithValue("@IsInquire", 1);
                    cmd.Parameters.AddWithValue("@IsMD", 1);
                    cmd.Parameters.AddWithValue("@IsArchiveGroup", isArchive);
                    cmd.Parameters.AddWithValue("@IsOnlyIQ3ModeEnabled", request.IsOnlyIQ3ModeEnabled);
                    //cmd.Parameters.AddWithValue("@IsIWBModeEnabled", request.IsIWBModeEnabled);
                    cmd.Parameters.AddWithValue("@IsIwb", request.IsIwbModeEnabled);

                    if (request.Role.Id > 0)
                    cmd.Parameters.AddWithValue("@RoleId", request.Role.Id);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.PageDAL, "GetRevcordAndInquireSearchPageData", _tenantId));

                    //Total 4 Tables return.
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        //1. Treeview ResultSet
                        if (dr.HasRows)
                        {
                            treeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);
                                treeNode.IsRevCell = Convert.ToBoolean(dr["IsRevCell"]);
                                treeNode.IsIQ3 = Convert.ToBoolean(dr["IsIQ3"]);
                                treeNode.IsRevAgentAssociated = Convert.ToBoolean(dr["IsRevAgentAssociated"]);
                                treeNode.IsText911 = DBRecordExtensions.HasColumn(dr, "IsText911") ? Convert.ToBoolean(dr["IsText911"]) : false;
                                treeNodes.Add(treeNode);
                            }
                        }
                        //2. Inquire Treeview ResultSet 
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            inquireTreeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);
                                treeNode.IsRevCell = Convert.ToBoolean(dr["IsRevCell"]);
                                treeNode.IsIQ3 = Convert.ToBoolean(dr["IsIQ3"]);

                                inquireTreeNodes.Add(treeNode);
                            }
                        }

                        //3. MD Treeview ResultSet 
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            mdTreeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);

                                mdTreeNodes.Add(treeNode);
                            }
                        }

                        //4. Radio Talk Group Treeview ResultSet 
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            RadioTalkGroupTreeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);

                                RadioTalkGroupTreeNodes.Add(treeNode);
                            }
                        }
                        ArchiveTreeNodes = new List<TreeviewData>();
                        if (isArchive == 1)
                        {
                            //4. Archive Group Treeview ResultSet 
                            dr.NextResult();
                            if (dr.HasRows)
                            {
                                while (dr.Read())
                                {
                                    treeNode = new TreeviewData();

                                    treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                    treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                    treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                    treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                    treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                    treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                    treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                    treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                    treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                    treeNode.Param3 = Convert.ToString(dr["Param3"]);

                                    ArchiveTreeNodes.Add(treeNode);
                                }
                            }
                        }

                        //5. Revcell Treeview ResultSet 
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            revcellTreeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);

                                revcellTreeNodes.Add(treeNode);
                            }
                        }

                        //6. Teams Treeview ResultSet 
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            teamsTreeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);

                                teamsTreeNodes.Add(treeNode);
                            }
                        }
                        //7. Iwb Treeview ResultSet 
                        if (request.IsIwbModeEnabled)
                        {
                            dr.NextResult();
                            if (dr.HasRows)
                            {
                                iwbTreeNodes = new List<TreeviewData>();
                                while (dr.Read())
                                {
                                    treeNode = new TreeviewData();

                                    treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                    treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                    treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                    treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                    treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                    treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                    treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                    treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                    treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                    treeNode.Param3 = Convert.ToString(dr["Param3"]);

                                    iwbTreeNodes.Add(treeNode);
                                }
                            }
                        }
                        //8. Playlist ResultSet
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            playlists = new List<Playlist>();
                            while (dr.Read())
                            {
                                playlist = new Playlist();
                                playlist.Id = (int)dr["Id"];
                                playlist.Name = Convert.ToString(dr["Name"]);
                                playlist.Comments = Convert.ToString(dr["Description"]);
                                playlist.NoOfCalls = Convert.ToInt32(dr["NoOfCalls"]);
                                playlist.RevSyncServerID = Convert.ToInt32(dr["RevSyncServerID"]);
                                playlists.Add(playlist);
                            }
                        }

                        //9. Survey ResultSet
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            surveys = new List<Survey>();
                            while (dr.Read())
                            {
                                survey = new Survey();
                                survey.Id = (int)dr["Id"];
                                survey.Name = Convert.ToString(dr["Title"]);
                                survey.RevSyncSurveyId = (int)dr["RevSyncServerID"];
                                surveys.Add(survey);
                            }
                        }

                        //10. Column Model ResultSet
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            columnsModel = new List<TenantColumnModel>();
                            TenantColumnModel colModel = null;
                            while (dr.Read())
                            {
                                colModel = new TenantColumnModel();
                                colModel.ColumnModelId = Convert.ToInt16(dr["Id"]);//(int)dr["ColumnModelId"];
                                colModel.Name = Convert.ToString(dr["Name"]);
                                colModel.SNo = Convert.ToInt16(dr["Serial"]);
                                colModel.Width = Convert.ToInt16(dr["Width"]);
                                colModel.Alignment = Convert.ToString(dr["Alignment"]);
                                colModel.Caption = Convert.ToString(dr["Caption"]);
                                colModel.IsHidden = Convert.ToBoolean(dr["IsHidden"]);
                                //colModel.IsSortable = Convert.ToBoolean(dr["IsSortable"]);
                                colModel.UserNum = Convert.ToInt16(dr["UserNum"]);
                                colModel.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                                colModel.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);

                                columnsModel.Add(colModel);
                            }
                        }

                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            lCallTag = new List<CallTag>();
                            CallTag _CallTag = null;
                            while (dr.Read())
                            {
                                _CallTag = new CallTag();
                                _CallTag.TagID = Convert.ToInt16(dr["Id"]);//(int)dr["ColumnModelId"];
                                _CallTag.TagColor = Convert.ToString(dr["TagColorID"]);
                                _CallTag.TagName = Convert.ToString(dr["TagName"]);

                                lCallTag.Add(_CallTag);
                            }
                        }

                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            lBmFlag = new List<BmFlag>();
                            BmFlag _BmFlag = null;
                            while (dr.Read())
                            {
                                _BmFlag = new BmFlag();
                                _BmFlag.FlagID = Convert.ToInt16(dr["id"]);
                                _BmFlag.FlagColor = Convert.ToString(dr["FlagColorID"]);
                                _BmFlag.FlagName = Convert.ToString(dr["FlagName"]);

                                lBmFlag.Add(_BmFlag);
                            }
                        }
                    }
                }
                return new VRResponse
                {
                    TreeviewData = treeNodes,
                    TreeviewDataTeams = teamsTreeNodes,
                    TreeviewDataRevcell = revcellTreeNodes,
                    TreeviewDataInquire = inquireTreeNodes,
                    TreeviewDataMD = mdTreeNodes,
                    TreeviewDataRadioTalkGroup = RadioTalkGroupTreeNodes,
                    TreeviewDataArchive = ArchiveTreeNodes,
                    TreeviewDataIwb = iwbTreeNodes,
                    Playlists = playlists,
                    Surveys = surveys,
                    ColumnsModel = columnsModel,
                    CallTag = lCallTag,
                    BookmarkFlag = lBmFlag,
                    //IsChainDBsConfigured= isChainDBsConfigured
                };
            }
            catch (Exception ex) { throw ex; }
            //return VoiceRecResponse;
        }

        public VRResponse GetRevcordAndInquireTreeView(VRRequest request)
        {
            List<TreeviewData> treeNodes = null;
            List<TreeviewData> teamsTreeNodes = null;
            List<TreeviewData> revcellTreeNodes = null;
            List<TreeviewData> inquireTreeNodes = null;
            List<TreeviewData> mdTreeNodes = null;
            List<TreeviewData> RadioTalkGroupTreeNodes = null;
            List<TreeviewData> ArchiveTreeNodes = null;
            TreeviewData treeNode = null;
            var isArchive = request.IsRoleBasedAccessEnabled ? (request.Role.IsSystemRole ? 1 : 0) : request.UserType;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = request.Role.Id == 0? "vr_GetTreeViewData" : "vr_GetTreeViewData_Roles";
                    cmd.Parameters.AddWithValue("@UserId", request.UserId);
                    cmd.Parameters.AddWithValue("@UserType", request.UserType);
                    cmd.Parameters.AddWithValue("@IsInquire", 1);
                    cmd.Parameters.AddWithValue("@IsMD", 1);
                    cmd.Parameters.AddWithValue("@IsArchiveGroup", isArchive);
                    cmd.Parameters.AddWithValue("@IsTeams", request.IsTeamsEnabled);
                    cmd.Parameters.AddWithValue("@IsRevcell", request.IsRevcellEnabled);

                    if(request.Role.Id > 0)
                        cmd.Parameters.AddWithValue("@RoleId", request.Role.Id);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.PageDAL, "GetRevcordAndInquireTreeView", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        //1. Treeview ResultSet
                        if (dr.HasRows)
                        {
                            treeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);
                                treeNode.IsRevCell = Convert.ToBoolean(dr["IsRevCell"]);
                                treeNode.IsIQ3 = Convert.ToBoolean(dr["IsIQ3"]);
                                treeNode.IsRevAgentAssociated = Convert.ToBoolean(dr["IsRevAgentAssociated"]);
                                treeNode.IsText911 = !DBRecordExtensions.HasColumn(dr, "IsText911") ? false : (dr["IsText911"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsText911"])); //DBRecordExtensions.HasColumn(dr, "IsText911") ? Convert.ToBoolean(dr["IsText911"]) : false;

                                
                                treeNodes.Add(treeNode);
                            }
                        }

                        //2. Revcell Treeview ResultSet 
                        if (request.IsRevcellEnabled)
                        {
                            dr.NextResult();
                            if (dr.HasRows)
                            {
                                revcellTreeNodes = new List<TreeviewData>();
                                while (dr.Read())
                                {
                                    treeNode = new TreeviewData();

                                    treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                    treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                    treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                    treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                    treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                    treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                    treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                    treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                    treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                    treeNode.Param3 = Convert.ToString(dr["Param3"]);

                                    revcellTreeNodes.Add(treeNode);
                                }
                            }
                        }

                        //3. Teams Treeview ResultSet 
                        if (request.IsTeamsEnabled)
                        {
                            dr.NextResult();
                            if (dr.HasRows)
                            {
                                teamsTreeNodes = new List<TreeviewData>();
                                while (dr.Read())
                                {
                                    treeNode = new TreeviewData();

                                    treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                    treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                    treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                    treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                    treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                    treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                    treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                    treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                    treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                    treeNode.Param3 = Convert.ToString(dr["Param3"]);

                                    teamsTreeNodes.Add(treeNode);
                                }
                            }
                        }

                        //4. Inquire Treeview ResultSet 
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            inquireTreeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);
                                treeNode.IsRevCell = Convert.ToBoolean(dr["IsRevCell"]);
                                treeNode.IsIQ3 = Convert.ToBoolean(dr["IsIQ3"]);
                                inquireTreeNodes.Add(treeNode);
                            }
                        }

                        //5. MD Treeview ResultSet 
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            mdTreeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);

                                mdTreeNodes.Add(treeNode);
                            }
                        }

                        //6. Radio Talk Group Treeview ResultSet 
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            RadioTalkGroupTreeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);

                                RadioTalkGroupTreeNodes.Add(treeNode);
                            }
                        }

                        //7. Archive Group Treeview ResultSet 
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            ArchiveTreeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);

                                ArchiveTreeNodes.Add(treeNode);
                            }
                        }
                    }
                }
                return new VRResponse
                {
                    TreeviewData = treeNodes,
                    TreeviewDataTeams = teamsTreeNodes,
                    TreeviewDataRevcell = revcellTreeNodes,
                    TreeviewDataInquire = inquireTreeNodes,
                    TreeviewDataMD = mdTreeNodes,
                    TreeviewDataArchive = ArchiveTreeNodes,
                    TreeviewDataRadioTalkGroup = RadioTalkGroupTreeNodes,
                    //TreeviewDataArchive = ArchiveTreeNodes,
                };
            }
            catch (Exception ex) { throw ex; }
        }

        #endregion

        #region InqFileVideoBookmarks

        public VRResponse InqFileVideoBookmarks(string callId)
        {
            List<InqFileVideoBookmark> lBookmark = null;
            InqFileVideoBookmark oBookmark = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.InqFileVideos.GET_INQ_FILEVIDEO_BOOKMARK;
                    cmd.Parameters.AddWithValue("@CallId", callId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.PageDAL, "InqFileVideoBookmarks", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            lBookmark = new List<InqFileVideoBookmark>();
                            while (dr.Read())
                            {
                                oBookmark = new InqFileVideoBookmark();
                                oBookmark.Id = Convert.ToInt32(dr["ID"]);
                                oBookmark.CallId = Convert.ToString(dr["CallId"]);
                                oBookmark.BookMarkPos = Convert.ToInt32(dr["BookMarkPos"]);
                                oBookmark.BookMarkText = Convert.ToString(dr["BookMarkText"]);
                                oBookmark.BookMarkNotes = Convert.ToString(dr["BookMarkNotes"]);
                                oBookmark.CannedNote = !DBRecordExtensions.HasColumn(dr, "CannedNote") ? null : Convert.ToString(dr["CannedNote"]);
                                oBookmark.Markermeasurement = !DBRecordExtensions.HasColumn(dr, "Markermeasurement") ? null : Convert.ToString(dr["Markermeasurement"]);
                                oBookmark.MarkerId = Convert.ToInt32(dr["MarkerId"]);
                                oBookmark.ParentId = Convert.ToInt32(dr["ParentId"]);
                                oBookmark.FullPath = Convert.ToString(dr["FullPath"]);
                                oBookmark.IsPicture = Convert.ToBoolean(dr["IsPicture"]);
                                oBookmark.PictureFileName = Convert.ToString(dr["PictureFileName"]);
                                oBookmark.EventDateTime = Convert.ToString(dr["EventDateTime"]);
                                oBookmark.VideoFileName = Convert.ToString(dr["VideoFileName"]);
                                lBookmark.Add(oBookmark);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }

            return new VRResponse
            {
                lInqFileVideoBookmark = lBookmark
            };
        }

        public int AddInqFileVideoBookmark(string callid, int position, string bookmark, string notes, int parentId, string fullPath)
        {
            int id = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.InqFileVideos.SAVE_INQ_FILEVIDEO_ADDBOOKMARK;
                    cmd.Parameters.AddWithValue("@CallId", callid);

                    cmd.Parameters.AddWithValue("@Bookmark", bookmark);
                    cmd.Parameters.AddWithValue("@NOTES", notes);
                    cmd.Parameters.AddWithValue("@POSITION", position);
                    cmd.Parameters.AddWithValue("@ParentId", parentId);
                    cmd.Parameters.AddWithValue("@FullPath", fullPath);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.PageDAL, "AddInqFileVideoBookmark", _tenantId));

                    id = (int)cmd.ExecuteScalar();
                }

            }
            catch (Exception ex)
            {
                //return false;
                throw ex;
            }

            return id;
        }

        public int AddInqInspectionBookmark(string callid, int position, string bookmark, string MarkerAnswer, string PictureName, string notes, int sectionid, int InspectionTemplateId)
        {
            int id = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.InqFileVideos.IQ3INSPECTIONADDBOOKMARK;
                    cmd.Parameters.AddWithValue("@CallId", callid);
                    cmd.Parameters.AddWithValue("@Bookmark", bookmark);
                    cmd.Parameters.AddWithValue("@NOTES", notes);
                    cmd.Parameters.AddWithValue("@POSITION", position);
                    cmd.Parameters.AddWithValue("@SectionId", sectionid);
                    cmd.Parameters.AddWithValue("@InspectionTemplateId", InspectionTemplateId);
                    cmd.Parameters.AddWithValue("@MarkerAnswer", MarkerAnswer);
                    cmd.Parameters.AddWithValue("@PictureName", PictureName);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.PageDAL, "AddInqInspectionBookmark", _tenantId));
                    id = (int)cmd.ExecuteScalar();
                }
            }
            catch (Exception ex) { throw ex; }

            return id;
        }
        

        public int AddIQ3VirtualBookmark(MarkerIQ3Virtual marker)
        {
            int id = 0;

            try
            {
                //using (var conn = DALHelper.GetConnection(_tenantId))
                //using (var cmd = conn.CreateCommand())
                //{
                //    cmd.CommandType = CommandType.Text;
                //    cmd.CommandText = "IF NOT EXISTS(SELECT * FROM t_Bookmark WHERE CallID = '" + marker.EventId.Trim() + "' AND BookMarkPos = '" + marker.MarkerTime +  "' AND BookmarkText = '" + marker.MarkerText + "') BEGIN " +
                //        "INSERT INTO [t_BookMark] ([CallID] ,[BookMarkPos] ,[BookMarkText] ,[BookMarkNotes] ,[MarkerId] ,[IsPicture], [ParentId], [FullPath], [CannedNote]) VALUES (" + "'" + marker.EventId.Trim() + "'," + "'" + marker.MarkerTime + "'," + "'" + marker.MarkerText.Replace("'", "").Trim() + "'," + "'" + marker.MarkerNote.Replace("'", "").Trim() + "','" + marker.MarkerId + "','" + marker.IsPicture + "','" + marker.ParentId + "','" + marker.FullPath + "','" + marker.CannedNote + "'); SELECT SCOPE_IDENTITY();"
                //        + "END ELSE BEGIN SELECT Id FROM t_Bookmark WHERE CallID = '" + marker.EventId.Trim() + "' AND BookMarkPos = '" + marker.MarkerTime + "' AND BookmarkText = '" + marker.MarkerText + "' END";
                //    conn.Open();

                //    marker.BookmarkId = Convert.ToInt32(cmd.ExecuteScalar());
                //    id = (int)marker.BookmarkId;

                //    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.PageDAL, "AddIQ3VirtualBookmark", _tenantId));
                //}
                //if(id > 0 && marker.FileName.Trim().Length > 0)
                //{
                //    using (var conn = DALHelper.GetConnection(_tenantId))
                //    using (var cmd = conn.CreateCommand())
                //    {
                //        cmd.CommandType = CommandType.StoredProcedure;
                //        cmd.CommandText = "vrAddBookmarkPictureDetails";
                //        cmd.Parameters.AddWithValue("@BookmarkId", id);
                //        cmd.Parameters.AddWithValue("@EventId", marker.EventId.Trim());
                //        cmd.Parameters.AddWithValue("@FileName", marker.FileName.Trim());
                //        conn.Open();
                //        Convert.ToInt32(cmd.ExecuteScalar());

                //        conn.Close();
                //    }
                //}
                
            }
            catch (Exception ex)
            {
                throw ex;
            }

            return id;
        }

        public bool InqFileVideoSaveEditedBookmark(string editedBookmark, int markerId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.InqFileVideos.SAVE_INQ_FILEVIDEO_EDITEDBOOKMARK;
                    cmd.Parameters.AddWithValue("@MarkerId", markerId);

                    cmd.Parameters.AddWithValue("@EditedBookmark", editedBookmark);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.PageDAL, "InqFileVideoSaveEditedBookmark", _tenantId));

                    cmd.ExecuteNonQuery();


                }

            }
            catch (Exception ex)
            {
                //return false;
                throw ex;
            }

            return true;
        }
        public bool InqFileVideoSaveEditedBookmarkNotes(string editedBookmarkNotes, int markerId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.InqFileVideos.SAVE_INQ_FILEVIDEO_EDITEDBOOKMARKNOTES;

                    cmd.Parameters.AddWithValue("@MarkerId", markerId);
                    cmd.Parameters.AddWithValue("@EditedBookmarkNotes", editedBookmarkNotes);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.PageDAL, "InqFileVideoSaveEditedBookmarkNotes", _tenantId));

                    cmd.ExecuteNonQuery();

                }

            }
            catch (Exception ex)
            {
                //return false;
                throw ex;
            }

            return true;
        }
        public bool DeleteInqFileVideoBookmark(int markerId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.InqFileVideos.DELETE_INQ_FILEVIDEO_BOOKMARK;

                    cmd.Parameters.AddWithValue("@MarkerId", markerId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.PageDAL, "DeleteInqFileVideoBookmark", _tenantId));

                    cmd.ExecuteNonQuery();

                }

            }
            catch (Exception ex)
            {
                //return false;
                throw ex;
            }

            return true;
        }
        public bool saveeventnotes(string saveeventnotes, string CallID)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.InqFileVideos.SAVE_EVENT_NOTES;

                    cmd.Parameters.AddWithValue("@saveeventnotes", saveeventnotes);
                    cmd.Parameters.AddWithValue("@CallID", CallID);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.PageDAL, "saveeventnotes", _tenantId));

                    cmd.ExecuteNonQuery();

                }

            }
            catch (Exception ex)
            {
                //return false;
                throw ex;
            }

            return true;
        }

        #endregion

        public bool UpdateBookmarkNotes(int bookmarkId, string modifiedBookMarkNotes)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "UPDATE t_Bookmark SET BookMarkNotes = @BookmarkNotes WHERE ID = @BookmarkId;";
                    cmd.Parameters.AddWithValue("@BookmarkId", bookmarkId);
                    cmd.Parameters.AddWithValue("@BookmarkNotes", modifiedBookMarkNotes);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.PageDAL, "UpdateBookmarkNotes", _tenantId));

                    cmd.ExecuteNonQuery();

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return true;
        }
        public bool UpdateInspectionBookmarkNotes(int bookmarkId, string modifiedBookMarkNotes)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "UPDATE [iq3InspectionMarker] SET [Note] = @BookmarkNotes WHERE ID = @BookmarkId;";
                    cmd.Parameters.AddWithValue("@BookmarkId", bookmarkId);
                    cmd.Parameters.AddWithValue("@BookmarkNotes", modifiedBookMarkNotes);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.PageDAL, "UpdateBookmarkNotes", _tenantId));

                    cmd.ExecuteNonQuery();

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return true;
        }
        
        public bool UpdateBookmark(int bookmarkId, string modifiedBookMark)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "UPDATE t_Bookmark SET BookMarkText = @BookmarkText WHERE ID = @BookmarkId;";
                    cmd.Parameters.AddWithValue("@BookmarkId", bookmarkId);
                    cmd.Parameters.AddWithValue("@BookmarkText", modifiedBookMark);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.PageDAL, "UpdateBookmark", _tenantId));

                    cmd.ExecuteNonQuery();

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return true;
        }

        public bool UpdateBookmarkMarkerAnswer(int bookmarkId, string modifiedMarkerAnswer)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "UPDATE iq3InspectionMarker SET MarkerAnswer = @modifiedMarkerAnswer WHERE ID = @BookmarkId;";
                    cmd.Parameters.AddWithValue("@BookmarkId", bookmarkId);
                    cmd.Parameters.AddWithValue("@modifiedMarkerAnswer", modifiedMarkerAnswer);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.PageDAL, "UpdateBookmark", _tenantId));

                    cmd.ExecuteNonQuery();

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return true;
        }

        public bool DeleteBookmark(int bookmarkId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "DELETE FROM t_Bookmark WHERE ID = @BookmarkId;";
                    cmd.Parameters.AddWithValue("@BookmarkId", bookmarkId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.PageDAL, "DeleteBookmark", _tenantId));

                    cmd.ExecuteNonQuery();

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return true;
        }

        public bool DeleteInspectionBookmark(int bookmarkId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "UPDATE [iq3InspectionMarker] SET [IsDeleted] = 1 WHERE ID = @BookmarkId;";
                    cmd.Parameters.AddWithValue("@BookmarkId", bookmarkId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.PageDAL, "DeleteInspectionBookmark", _tenantId));

                    cmd.ExecuteNonQuery();

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return true;
        }

        public bool UpdateEventName(string eventId, string eventName)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "UPDATE t_RevRec_Interviews SET Interviewee = @Interviewee WHERE InterviewId = @InterviewId";
                    cmd.Parameters.AddWithValue("@InterviewId", eventId);
                    cmd.Parameters.AddWithValue("@Interviewee", eventName);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.PageDAL, "UpdateEventName", _tenantId));

                    cmd.ExecuteNonQuery();

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return true;
        }

        public string GetEventCurrentStatus(string eventId)
        {
            string EventStatus = string.Empty;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT InterviewId, CurrentState FROM t_RevRec_Interviews WHERE InterviewId = @InterviewId";
                    cmd.Parameters.AddWithValue("@InterviewId", eventId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.PageDAL, "GetEventCurrentStatus", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            if (dr.Read())
                            {
                                EventStatus = Convert.ToString(dr["CurrentState"]);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return EventStatus;
        }
    }
}
