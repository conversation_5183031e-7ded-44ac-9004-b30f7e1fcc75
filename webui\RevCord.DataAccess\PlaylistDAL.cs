﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using RevCord.DataContracts.VoiceRecEntities;
using System.Data.SqlClient;
using RevCord.Util;
using RevCord.DataContracts;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using RevCord.DataAccess.Util;
using Newtonsoft.Json;
using RevCord.DataContracts.IQ3InspectionEntities;

namespace RevCord.DataAccess
{
    public class PlaylistDAL
    {
        private int _tenantId;
        private static int CMD_TIMEOUT = AppSettingsUtil.GetInt("dashboardCommandTimeout", 300);
        public PlaylistDAL(int tenantId)
        {
            _tenantId = tenantId;
        }
        #region Playlist

        public List<Playlist> GetPlaylists(int userId)
        {
            List<Playlist> playlists = null;
            Playlist playlist = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.PLAYLIST_GETLIST_BY_USERID;
                    cmd.Parameters.AddWithValue("@UserId", userId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Playlist, "GetPlaylists", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        playlists = new List<Playlist>();
                        while (dr.Read())
                        {
                            playlist = new Playlist();
                            playlist.Id = (int)dr["Id"];
                            playlist.Name = Convert.ToString(dr["Name"]);
                            playlist.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                            playlist.Comments = Convert.ToString(dr["Description"]);
                            playlist.NoOfCalls = Convert.ToInt32(dr["NoOfCalls"]);
                            playlist.VisibilityType = Convert.ToInt32(dr["VisibilityType"]);
                            /*playlist.UserId = (int)dr["UserId"];
                            playlist.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                            playlist.ModifiedDate = Convert.ToDateTime(dr["ModifiedDate"]);*/

                            playlist.RevSyncServerID = Convert.ToInt32(dr["RevSyncServerID"]);
                            playlist.IsSyncedFromClient = Convert.ToInt32(dr["IsSyncedFromClient"]);
                            playlist.IsOwnerPlaylist = Convert.ToBoolean(dr["IsOwnerPlaylist"]);
                            playlist.PlaylistType = Convert.ToInt32(dr["PlaylistType"]);
                            playlists.Add(playlist);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return playlists;
        }

        public List<Playlist> GetRepoList(int userId)
        {
            List<Playlist> repoList = new List<Playlist>();

            using (var conn = DALHelper.GetConnection(_tenantId))
            using (var cmd = conn.CreateCommand())
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandText = DBConstants.VoiceRec.REPO_LIST_BY_USERID;
                cmd.Parameters.AddWithValue("@UserId", userId);

                conn.Open();
                using (var reader = cmd.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        Playlist pl = new Playlist
                        {
                            Id = Convert.ToInt32(reader["Id"]),
                            Name = reader["Name"].ToString()
                        };
                        repoList.Add(pl);
                    }
                }
            }

            return repoList;
        }


        //public static Playlist GetPlaylist(int playlistId)
        //{
        //    //throw new NotImplementedException();
        //    PlaylistDetail pld = null;
        //    List<PlaylistDetail> plds = null;
        //    try
        //    {
        //        using (var conn = DALHelper.GetConnection())
        //        using (var cmd = conn.CreateCommand())
        //        {
        //            cmd.CommandType = CommandType.StoredProcedure;
        //            cmd.CommandText = DBConstants.VoiceRec.PLAYLIST_GETBYID;
        //            cmd.Parameters.AddWithValue("@PlaylistId", playlistId);

        //            conn.Open();
        //            using (SqlDataReader dr = cmd.ExecuteReader())
        //            {
        //                plds = new List<PlaylistDetail>();
        //                while (dr.Read())
        //                {
        //                    pld = new PlaylistDetail();

        //                    pld.Id = Convert.ToInt32(dr["plDetailId"]);
        //                    pld.CallId = Convert.ToString(dr["PlCallId"]);
        //                    pld.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);

        //                    pld.CallInfo = new CallInfo();
        //                    if (!dr.IsDBNull(dr.GetOrdinal("CallID")))
        //                    {
        //                        pld.CallInfo.CallId = Convert.ToString(dr["CallID"]);
        //                        pld.CallInfo.RecorderId = Convert.ToInt32(dr["RecID"]);
        //                        pld.CallInfo.GroupId = Convert.ToInt32(dr["GroupNum"]);
        //                        pld.CallInfo.GroupName = Convert.ToString(dr["GroupName"]);
        //                        pld.CallInfo.UserId = Convert.ToInt32(dr["UserNum"]);
        //                        pld.CallInfo.ChannelId = Convert.ToInt32(dr["Ext"]);
        //                        pld.CallInfo.ChannelName = dr.IsDBNull(dr.GetOrdinal("ExtName")) ? "" : Convert.ToString(dr["ExtName"]);
        //                        pld.CallInfo.CallType = Convert.ToInt32(dr["CallType"]);
        //                        //pld.CallInfo.StartTime = Convert.ToDateTime(dr["StartTime"]);
        //                        pld.CallInfo.StartTime = dr["StartTime"].ToString().ConvertStringDateTimeToDateTime();
        //                        pld.CallInfo.StartTimeString = Convert.ToString(dr["StartTime"]);
        //                        pld.CallInfo.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);
        //                        pld.CallInfo.FileName = Convert.ToString(dr["FileName"]);
        //                        pld.CallInfo.CustName = Convert.ToString(dr["CustName"]);
        //                        pld.CallInfo.ANI = Convert.ToString(dr["ANI"]);
        //                        pld.CallInfo.CallerID = Convert.ToString(dr["CallerID"]);
        //                        pld.CallInfo.CalledID = Convert.ToString(dr["CalledID"]);
        //                        //pld.CallInfo.SGroupID = dr.IsDBNull(dr.GetOrdinal("SGroupID")) ? 0 : Convert.ToInt32(dr["SGroupID"]);
        //                        pld.CallInfo.Tag1 = Convert.ToString(dr["Tag1"]);
        //                        pld.CallInfo.Tag2 = Convert.ToString(dr["Tag2"]);
        //                        pld.CallInfo.Tag3 = Convert.ToString(dr["Tag3"]);
        //                        pld.CallInfo.Tag4 = Convert.ToString(dr["Tag4"]);
        //                        pld.CallInfo.ANIPhone = Convert.ToString(dr["ANI_PH"]);
        //                        pld.CallInfo.ANIName = Convert.ToString(dr["ANI_NAME"]);
        //                        pld.CallInfo.ANIDetails = Convert.ToString(dr["ANI_DETAILS"]);
        //                        pld.CallInfo.CallComments = Convert.ToString(dr["CALL_COMMENT"]);

        //                        if (pld.CallInfo.CallType > 1)
        //                        {
        //                            pld.CallInfo.CallComments = string.Empty;
        //                            pld.CallInfo.MessageBody = Convert.ToString(dr["CALL_COMMENT"]);
        //                        }
        //                        pld.CallInfo.ScreenRecFile = Convert.ToString(dr["Screen_Rec_File"]);
        //                        if (!string.IsNullOrEmpty(pld.CallInfo.ScreenRecFile) && pld.CallInfo.CallType == 1)
        //                            pld.CallInfo.CallType = 6;

        //                        pld.CallInfo.RetainValue = Convert.ToInt32(dr["RetainValue"]);
        //                    }

        //                    plds.Add(pld);
        //                }
        //            }
        //        }
        //    }
        //    catch (Exception ex) { throw ex; }
        //    return new Playlist { Id = playlistId, NoOfCalls = plds.Count, PlaylistDetails = plds };
        //}

        /* ========= Get Playlist Item For All Types without Merge ========= */
        public Playlist GetPlaylistById(int playlistId)
        {
            //throw new NotImplementedException();
            PlaylistDetail pld = null;
            List<PlaylistDetail> plds = null;
            List<CallInfo> dsfCalls = null;
            List<PlaylistNote> playlistNotes = null;
            List<InspectionTemplate> inspectionTemplates = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.PLAYLIST_DETAILS_BY_PLAYLISTID;
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.Parameters.AddWithValue("@PlaylistId", playlistId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Playlist, "GetPlaylistById", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        plds = new List<PlaylistDetail>();
                        while (dr.Read())
                        {
                            //Id	PlaylistId	CallId	StartTime	FileName	MessageBody	FileType	NoOfPages	Duration	Ordering	IsUserAddedItem	IsDemoItem	RecorderId	IsDeleted	CreatedDate	ModifiedDate
                            pld = new PlaylistDetail();

                            pld.Id = Convert.ToInt32(dr["Id"]);
                            //pld.PlaylistId = Convert.ToInt32(dr["PlaylistId"]);
                            pld.CallId = Convert.ToString(dr["CallId"]);

                            pld.StartTime = Convert.ToDateTime(dr["StartTime"]);
                            pld.StartTimeString = pld.StartTime.Value.ToString("yyyyMMddHHmmss");
                            pld.FileName = Convert.ToString(dr["FileName"]);
                            pld.DurationInSec = Convert.ToDouble(dr["Duration"]) / 1000;
                            // pld.DurationInSec = pld.DurationInSec / 1000; //Added by Arivu
                            pld.DurationInSecInt = Convert.ToInt32(pld.DurationInSec);
                            pld.MessageBody = Convert.ToString(dr["MessageBody"]);
                            pld.FileType = (FileType)Enum.Parse(typeof(FileType), Convert.ToString(dr["FileType"]));
                            pld.NoOfPages = dr.IsDBNull(dr.GetOrdinal("NoOfPages")) ? 0 : Convert.ToInt32(dr["NoOfPages"]);
                            pld.IsUserAddedItem = Convert.ToBoolean(dr["IsUserAddedItem"]);
                            pld.IsDemoItem = Convert.ToBoolean(dr["IsDemoItem"]);
                            pld.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                            pld.ModifiedDate = Convert.ToDateTime(dr["ModifiedDate"]);
                            pld.RecorderId = Convert.ToInt32(dr["RecorderId"]);

                            if(pld.FileType == FileType.Document)
                            {
                                pld.DocumentInfo = new DataContracts.IWBEntities.IwbDocument();
                                pld.DocumentInfo.SignDocumentId = Convert.ToInt32(dr["SignDocumentId"]);
                                pld.DocumentInfo.OwnerEmail = Convert.ToString(dr["OwnerEmail"]);
                            }

                            plds.Add(pld);
                        }
                        //2. CallInfo ResultSet, used for Dsf Calls
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            //if (dr.Read())
                            //dsfCalls = ORMapper.MapCallInfos(dr);
                            dsfCalls = new List<CallInfo>();
                            while (dr.Read())
                            {
                                var callInfo = new CallInfo();

                                callInfo.CallId = Convert.ToString(dr["CallID"]);
                                callInfo.RecorderId = Convert.ToInt32(dr["RecID"]);
                                callInfo.GroupId = Convert.ToInt32(dr["GroupNum"]);
                                callInfo.GroupName = Convert.ToString(dr["GroupName"]);
                                callInfo.AgentId = Convert.ToInt32(dr["UserNum"]);
                                callInfo.ChannelId = Convert.ToInt32(dr["Ext"]);
                                callInfo.ChannelName = Convert.ToString(dr["ExtName"]);
                                callInfo.CallType = Convert.ToInt32(dr["CallType"]);
                                callInfo.StartTime = dr["StartTime"].ToString().ConvertStringDateTimeToDateTime();
                                callInfo.StartTimeString = Convert.ToString(dr["StartTime"]);
                                callInfo.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);
                                callInfo.FileName = Convert.ToString(dr["FileName"]);

                                if (callInfo.CallType != 7)
                                    callInfo.CallType_inq = callInfo.CallType;
                                else
                                {
                                    if (Regex.IsMatch(callInfo.FileName, "mp4", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "mov", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "m4v", RegexOptions.IgnoreCase))
                                    {
                                        callInfo.CallType_inq = 8;
                                    }
                                    else if (Regex.IsMatch(callInfo.FileName, "zip", RegexOptions.IgnoreCase))
                                    {
                                        callInfo.CallType_inq = 12;
                                    }
                                    else
                                    {
                                        callInfo.CallType_inq = 7;
                                    }
                                }

                                callInfo.Tag4 = Convert.ToString(dr["Tag4"]);
                                callInfo.IsRevCell = (dr["IsRevCell"] is DBNull) ? false : Convert.ToBoolean(dr["IsRevCell"]);
                                switch (callInfo.CallType)
                                {
                                    case 7:
                                    case 11:
                                        callInfo.Tag4 = Convert.ToString(dr["Tag4"]);
                                        break;

                                    default:
                                        if (callInfo.Tag4.Split(',').Length == 2 && !callInfo.IsRevCell)
                                            callInfo.Tag4 = callInfo.Tag4.Split(',')[1] + "," + callInfo.Tag4.Split(',')[0]; //Because for audio and Text911 GPS comes in reverse format [Longitude,Latitude]
                                        break;
                                }

                                callInfo.Tag3 = Convert.ToString(dr["Tag3"]);
                                callInfo.ANIPhone = Convert.ToString(dr["ANI_PH"]); //Ani Phone Number
                                callInfo.IsPictureEvent = !DBRecordExtensions.HasColumn(dr, "IsPictureEvent") ? false : Convert.ToBoolean(dr["IsPictureEvent"]);
                                callInfo.IsVirtualInspection = !DBRecordExtensions.HasColumn(dr, "IsVirtualInspection") ? false : Convert.ToBoolean(dr["IsVirtualInspection"]);
                                callInfo.IsRevView = !DBRecordExtensions.HasColumn(dr, "IsRevView") ? false : Convert.ToBoolean(dr["IsRevView"]);
                                callInfo.RevViewFileName = DBRecordExtensions.HasColumn(dr, "RevViewFileName") ? Convert.ToString(dr["RevViewFileName"]) : string.Empty;
                                callInfo.RevViewStartTime = DBRecordExtensions.HasColumn(dr, "RevViewStartTime") ? Convert.ToString(dr["RevViewStartTime"]) : string.Empty;
                                callInfo.RevViewPhoneNumber = DBRecordExtensions.HasColumn(dr, "RevViewPhoneNumber") ? Convert.ToString(dr["RevViewPhoneNumber"]) : string.Empty;
                                callInfo.RevViewAgentName = DBRecordExtensions.HasColumn(dr, "RevViewAgentName") ? Convert.ToString(dr["RevViewAgentName"]) : string.Empty;
                                callInfo.TagName = DBRecordExtensions.HasColumn(dr, "TagName") ? Convert.ToString(dr["TagName"]) : string.Empty;
                                callInfo.TagColorID = DBRecordExtensions.HasColumn(dr, "TagColorID") ? Convert.ToString(dr["TagColorID"]) : string.Empty;
                                callInfo.CallComments = Convert.ToString(dr["CALL_COMMENT"]);
                                //callInfo.ScreenRecFile = Convert.ToString(dr["Screen_Rec_File"]);
                                callInfo.ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]);//Convert.ToString(dr["ScreenFileNames"]).Split(',').ToList();
                                callInfo.RetainValue = Convert.ToInt32(dr["RetainValue"]);
                                //if (!string.IsNullOrEmpty(callInfo.ScreenFileNames))//TODO: delete this in version 10
                                //    callInfo.CallType = 6;

                                if (dr.FieldExists("BookMarkXML") && callInfo.CallType != 7)
                                {
                                    callInfo.BookmarkXML = Convert.ToString(dr["BookMarkXML"]);
                                    if (!String.IsNullOrEmpty(callInfo.BookmarkXML))
                                    {
                                        callInfo.BookmarkCSV = callInfo.BookmarkXML.ConvertXmlStringToCsvString();
                                    }
                                }

                                if (dr.FieldExists("InspectionBookMarkXML") && callInfo.CallType == 7)
                                {
                                    callInfo.BookmarkXML = Convert.ToString(dr["InspectionBookMarkXML"]);
                                    if (!String.IsNullOrEmpty(callInfo.BookmarkXML))
                                    {
                                        callInfo.BookmarkCSV = callInfo.BookmarkXML.ConvertXmlStringToCsvString();
                                    }
                                }
                                callInfo.VesselId = Convert.ToString(dr["VesselId"]);
                                callInfo.InspectionId = Convert.ToInt32(dr["InspectionId"]);
                                callInfo.InspectionTemplateId = Convert.ToInt32(dr["InspectionTemplateId"]);
                                callInfo.InspectionTemplateTitle = Convert.ToString(dr["InspectionTemplateTitle"]);
                                callInfo.AssetId = DBRecordExtensions.HasColumn(dr, "AssetId") ? Convert.ToString(dr["AssetId"]) : string.Empty;
                                dsfCalls.Add(callInfo);
                            }
                        }

                        #region DSF Files Mapping

                        if (dsfCalls != null)
                        {
                            //foreach (var dsfcall in dsfCalls)
                            dsfCalls.ForEach(dsfcall =>
                            {
                                var dsfPld = plds.FirstOrDefault(pd => pd.CallId == dsfcall.CallId);
                                if (dsfPld != null)
                                {
                                    //dsfPld.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);
                                    dsfPld.ChannelId = dsfcall.ChannelId;
                                    dsfPld.ChannelName = dsfcall.ChannelName;
                                    dsfPld.GroupId = dsfcall.GroupId;
                                    dsfPld.GroupName = dsfcall.GroupName;
                                    dsfPld.Comments = dsfcall.CallComments;
                                    dsfPld.Tag4 = dsfcall.Tag4;
                                    dsfPld.Tag3 = dsfcall.Tag3;//Address
                                    dsfPld.ANI_PH = dsfcall.ANIPhone; //Ani Phone Number
                                    dsfPld.IsPictureEvent = dsfcall.IsPictureEvent;
                                    dsfPld.IsVirtualInspection = dsfcall.IsVirtualInspection;
                                    dsfPld.IsRevView = dsfcall.IsRevView;
                                    dsfPld.RevViewFileName = dsfcall.RevViewFileName;
                                    dsfPld.RevViewStartTime = dsfcall.RevViewStartTime;
                                    dsfPld.RevViewPhoneNumber = dsfcall.RevViewPhoneNumber;
                                    dsfPld.RevViewAgentName = dsfcall.RevViewAgentName;
                                    dsfPld.RetainValue = Convert.ToBoolean(dsfcall.RetainValue);
                                    dsfPld.BookmarkXML = dsfcall.BookmarkXML;
                                    dsfPld.BookmarkCSV = dsfcall.BookmarkCSV;
                                    dsfPld.interview_DateTime = dsfcall.interview_DateTime;
                                    dsfPld.interview_Interviewer = dsfcall.interview_Interviewer;
                                    dsfPld.interview_Interviewee = dsfcall.interview_Interviewee;
                                    dsfPld.interview_InterviewId = dsfcall.interview_InterviewId;
                                    dsfPld.interview_GPS = dsfcall.interview_GPS;
                                    dsfPld.IsRevcell = dsfcall.IsRevCell;
                                    dsfPld.TagName = dsfcall.TagName;
                                    dsfPld.TagColorID = dsfcall.TagColorID;

                                    dsfPld.ScreenRecFile = dsfcall.ScreenFileNames;
                                    if (!string.IsNullOrEmpty(dsfcall.ScreenFileNames) && dsfcall.CallType == 6)
                                        dsfPld.FileType = FileType.Screens;
                                    dsfPld.VesselId = dsfcall.VesselId;
                                    dsfPld.InspectionId = dsfcall.InspectionId;
                                    dsfPld.InspectionTemplateId = dsfcall.InspectionTemplateId;
                                    dsfPld.InspectionTemplateTitle = dsfcall.InspectionTemplateTitle;
                                    dsfPld.AssetId = DBRecordExtensions.HasColumn(dr, "AssetId") ? dsfcall.AssetId : string.Empty;
                                }
                            });
                        }

                        #endregion

                        //3. Playlist Notes
                        playlistNotes = new List<PlaylistNote>();
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                var playlistNote = new PlaylistNote();
                                playlistNote.Id = Convert.ToInt32(dr["Id"]);
                                playlistNote.PlaylistId = Convert.ToInt32(dr["PlaylistId"]);
                                playlistNote.Note = Convert.ToString(dr["Note"]);
                                playlistNote.NoteType = Convert.ToInt32(dr["NoteType"]);
                                playlistNote.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                                playlistNote.ModifiedDate = Convert.ToDateTime(dr["ModifiedDate"]);
                                playlistNote.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                                playlistNotes.Add(playlistNote);
                            }
                        }

                        //4. Inspection
                        inspectionTemplates = new List<InspectionTemplate>();
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                var inspectionTemplate = new InspectionTemplate();
                                inspectionTemplate.Id = Convert.ToInt32(dr["InspectionTemplateId"]);
                                inspectionTemplate.Title = Convert.ToString(dr["InspectionTemplateTitle"]);
                                inspectionTemplates.Add(inspectionTemplate);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return new Playlist { Id = playlistId, NoOfCalls = plds.Count, PlaylistDetails = plds, PlaylistNotes = playlistNotes, InspectionTemplates = inspectionTemplates };
        }


        //public static Playlist GetPlaylistWithCallIds(int playlistId, out string callIds)
        //{
        //    callIds = "";
        //    PlaylistDetail pld = null;
        //    List<PlaylistDetail> plds = null;
        //    try
        //    {
        //        using (var conn = DALHelper.GetConnection())
        //        using (var cmd = conn.CreateCommand())
        //        {
        //            cmd.CommandType = CommandType.StoredProcedure;
        //            cmd.CommandText = DBConstants.VoiceRec.PLAYLIST_GETBYID;
        //            cmd.Parameters.AddWithValue("@PlaylistId", playlistId);

        //            //cmd.Parameters.AddWithValue("@CallIds", "");
        //            //cmd.Parameters["@CallIds"].Direction = ParameterDirection.Output;
        //            //callIds = cmd.Parameters["@CallIds"].Value.ToString();

        //            conn.Open();
        //            using (SqlDataReader dr = cmd.ExecuteReader())
        //            {
        //                plds = new List<PlaylistDetail>();
        //                while (dr.Read())
        //                {
        //                    pld = new PlaylistDetail();

        //                    pld.Id = Convert.ToInt32(dr["plDetailId"]);
        //                    pld.CallId = Convert.ToString(dr["PlCallId"]);
        //                    pld.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
        //                    //pld.FileType = Convert.ToDateTime(dr["FileType"]);
        //                    pld.NoOfPages = Convert.ToInt32(dr["NoOfPages"]);
        //                    pld.DurationInSec = Convert.ToInt32(dr["Duration"]);
        //                    pld.IsUserAddedItem = Convert.ToBoolean(dr["IsUserAddedItem"]);



        //                    #region Call Mapping

        //                    pld.CallInfo = new CallInfo();
        //                    if (!dr.IsDBNull(dr.GetOrdinal("CallID")))
        //                    {
        //                        pld.CallInfo.CallId = Convert.ToString(dr["CallID"]);
        //                        pld.CallInfo.RecorderId = Convert.ToInt32(dr["RecID"]);
        //                        pld.CallInfo.GroupId = Convert.ToInt32(dr["GroupNum"]);
        //                        pld.CallInfo.GroupName = Convert.ToString(dr["GroupName"]);
        //                        pld.CallInfo.UserId = Convert.ToInt32(dr["UserNum"]);
        //                        pld.CallInfo.ChannelId = Convert.ToInt32(dr["Ext"]);
        //                        pld.CallInfo.ChannelName = dr.IsDBNull(dr.GetOrdinal("ExtName")) ? "" : Convert.ToString(dr["ExtName"]);
        //                        pld.CallInfo.CallType = Convert.ToInt32(dr["CallType"]);
        //                        //pld.CallInfo.StartTime = Convert.ToDateTime(dr["StartTime"]);
        //                        pld.CallInfo.StartTime = dr["StartTime"].ToString().ConvertStringDateTimeToDateTime();
        //                        pld.CallInfo.StartTimeString = Convert.ToString(dr["StartTime"]);
        //                        pld.CallInfo.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);
        //                        pld.CallInfo.FileName = Convert.ToString(dr["FileName"]);
        //                        pld.CallInfo.CustName = Convert.ToString(dr["CustName"]);
        //                        pld.CallInfo.ANI = Convert.ToString(dr["ANI"]);
        //                        pld.CallInfo.CallerID = Convert.ToString(dr["CallerID"]);
        //                        pld.CallInfo.CalledID = Convert.ToString(dr["CalledID"]);
        //                        //pld.CallInfo.SGroupID = dr.IsDBNull(dr.GetOrdinal("SGroupID")) ? 0 : Convert.ToInt32(dr["SGroupID"]);
        //                        pld.CallInfo.Tag1 = Convert.ToString(dr["Tag1"]);
        //                        pld.CallInfo.Tag2 = Convert.ToString(dr["Tag2"]);
        //                        pld.CallInfo.Tag3 = Convert.ToString(dr["Tag3"]);
        //                        pld.CallInfo.Tag4 = Convert.ToString(dr["Tag4"]);
        //                        pld.CallInfo.ANIPhone = Convert.ToString(dr["ANI_PH"]);
        //                        pld.CallInfo.ANIName = Convert.ToString(dr["ANI_NAME"]);
        //                        pld.CallInfo.ANIDetails = Convert.ToString(dr["ANI_DETAILS"]);
        //                        pld.CallInfo.CallComments = Convert.ToString(dr["CALL_COMMENT"]);

        //                        if (pld.CallInfo.CallType > 1)
        //                        {
        //                            pld.CallInfo.CallComments = string.Empty;
        //                            pld.CallInfo.MessageBody = Convert.ToString(dr["CALL_COMMENT"]);
        //                        }
        //                        pld.CallInfo.ScreenRecFile = Convert.ToString(dr["Screen_Rec_File"]);
        //                        if (!string.IsNullOrEmpty(pld.CallInfo.ScreenRecFile) && pld.CallInfo.CallType == 1)
        //                            pld.CallInfo.CallType = 6;

        //                        pld.CallInfo.RetainValue = Convert.ToInt32(dr["RetainValue"]);
        //                    }
        //                    #endregion

        //                    plds.Add(pld);
        //                }
        //                //2. CallId Scaler ResultSet, used for EC Calls
        //                dr.NextResult();
        //                if (dr.Read())//(dr.HasRows)
        //                {
        //                    //dr.Read();
        //                    callIds = Convert.ToString(dr["CallIds"]);
        //                }
        //            }

        //        }
        //    }
        //    catch (Exception ex) { throw ex; }
        //    return new Playlist { Id = playlistId, NoOfCalls = plds.Count, PlaylistDetails = plds };
        //}


        public string InsertPlaylist(string name, string description, DateTime createdDate, int userId, bool isMTRplaylist)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();

                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;

                    //cmd.CommandType = CommandType.Text;
                    //cmd.CommandText = "INSERT INTO vrPlaylist (Name, UserId, Description,IsDeleted,CreatedDate) VALUES (@Name, @UserId, @Description, @IsDeleted, @CreatedDate);SELECT SCOPE_IDENTITY();";
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.PLAYLIST_INSERT;
                    if (isMTRplaylist)
                    {
                        cmd.Parameters.AddWithValue("@Name", "MTR-Playlist " + createdDate);
                        cmd.Parameters.AddWithValue("@PlaylistType", 2);
                    }
                    else
                    {
                        cmd.Parameters.AddWithValue("@Name", name);
                        cmd.Parameters.AddWithValue("@PlaylistType", 1);
                    }
                    cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@Description", description);
                    cmd.Parameters.AddWithValue("@IsDeleted", 0);
                    cmd.Parameters.AddWithValue("@CreatedDate", createdDate);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Playlist, "InsertPlaylist", _tenantId));

                    //cmd.ExecuteNonQuery();
                    //return Convert.ToBoolean(cmd.ExecuteScalar());//return true;
                    //return (int)cmd.ExecuteScalar();
                    int lastId = Convert.ToInt32(cmd.ExecuteScalar());
                    int RevSyncPlaylistID = 0;
                    if (SiteConfig.RevSyncEnabled)
                    {
                        Dictionary<string, string> _dic = new Dictionary<string, string>();
                        _dic.Add("Name", Convert.ToString(name));
                        _dic.Add("Description", Convert.ToString(description));
                        _dic.Add("CreatedDate", Convert.ToString(createdDate));
                        _dic.Add("UserId", Convert.ToString(userId));

                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Playlist, "InsertPlaylist : RevSync", _tenantId));

                        RevSyncPlaylistID = Convert.ToInt32(DALHelper.RevsyncAPICall(_tenantId, "PlaylistDAL", "InsertPlaylist", JsonConvert.SerializeObject(_dic)));
                        if (RevSyncPlaylistID > 0)
                        {
                            tran.Commit();
                            using (var Updatecmd = conn.CreateCommand())
                            {
                                Updatecmd.CommandType = CommandType.Text;
                                Updatecmd.CommandText = "UPDATE vrPlaylist SET [RevSyncServerID] = " + RevSyncPlaylistID + " Where Id = " + lastId;

                                Updatecmd.ExecuteNonQuery();
                            }
                        }
                        else tran.Rollback();
                    }
					else if (SiteConfig.IsMTEnable)
                    {
                        Dictionary<string, string> _dic = new Dictionary<string, string>();
                        _dic.Add("Name", Convert.ToString(name));
                        _dic.Add("Description", Convert.ToString(description));
                        _dic.Add("CreatedDate", Convert.ToString(createdDate));
                        _dic.Add("UserId", Convert.ToString(userId));

                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "PlaylistDAL", "InsertPlaylist", JsonConvert.SerializeObject(_dic));
                        if (isSent) tran.Commit(); else tran.Rollback();
                    }
                    else tran.Commit();
                    return lastId + "#" + RevSyncPlaylistID;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public int UpdatePlaylist(int id, string name, string description, DateTime modifiedDate, int userId, int RevSyncServerID)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();

                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;

                    //cmd.CommandType = CommandType.Text;
                    //cmd.CommandText = "UPDATE vrPlaylist SET Name = @Name, Description = @Description, ModifiedDate = @ModifiedDate Where Id = @Id";
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.PLAYLIST_UPDATE;
                    cmd.Parameters.AddWithValue("@Id", id);
                    cmd.Parameters.AddWithValue("@Name", name);
                    cmd.Parameters.AddWithValue("@Description", description);
                    cmd.Parameters.AddWithValue("@ModifiedDate", modifiedDate);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Playlist, "UpdatePlaylist", _tenantId));

                    //Int32 rowsAffected = cmd.ExecuteNonQuery();return rowsAffected;
                    //return Convert.ToBoolean(cmd.ExecuteScalar());//return true;
                    int rowsaffected = cmd.ExecuteNonQuery();

                    if (SiteConfig.RevSyncEnabled && RevSyncServerID > 0)
                    {
                        Dictionary<string, string> _dic = new Dictionary<string, string>();
                        _dic.Add("Id", Convert.ToString(id));
                        _dic.Add("Name", Convert.ToString(name));
                        _dic.Add("Description", Convert.ToString(description));
                        _dic.Add("ModifiedDate", Convert.ToString(modifiedDate));
                        _dic.Add("UserId", Convert.ToString(userId));

                        bool bReturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "PlaylistDAL", "UpdatePlaylist", JsonConvert.SerializeObject(_dic)));
                        if (bReturn) tran.Commit(); else tran.Rollback();
                    }
                    else if (SiteConfig.IsMTEnable)
                    {
                        Dictionary<string, string> _dic = new Dictionary<string, string>();
                        _dic.Add("Id", Convert.ToString(id));
                        _dic.Add("Name", Convert.ToString(name));
                        _dic.Add("Description", Convert.ToString(description));
                        _dic.Add("ModifiedDate", Convert.ToString(modifiedDate));
                        _dic.Add("UserId", Convert.ToString(userId));

                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "PlaylistDAL", "UpdatePlaylist", JsonConvert.SerializeObject(_dic));
                        if (isSent) tran.Commit(); else tran.Rollback();
                    }
                    else tran.Commit();

                    return rowsaffected;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public int DeletePlaylist(int id, DateTime modifiedDate, int RevSyncServerID)
        {
            using (var conn = DALHelper.GetConnection(_tenantId))
            using (var cmd = conn.CreateCommand())
            {
                conn.Open();

                SqlTransaction tran = conn.BeginTransaction();
                cmd.Transaction = tran;

                //cmd.CommandType = CommandType.Text;
                //cmd.CommandText = "UPDATE vrPlaylist SET IsDeleted = @IsDeleted, ModifiedDate = @ModifiedDate Where Id = @Id";
                //"UPDATE vrPlaylistDetail SET IsDeleted = 1 , ModifiedDate = @ModifiedDate WHERE PlayListId = @Id";
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandText = DBConstants.VoiceRec.PLAYLIST_DELETE;
                cmd.Parameters.AddWithValue("@PlayListId", id);
                cmd.Parameters.AddWithValue("@IsDeleted", 1);
                cmd.Parameters.AddWithValue("@ModifiedDate", modifiedDate);
                //int status = (int)cmd.ExecuteScalar();//statement:- 1 for Success, -1 for Failure
                //return status == 1 ? true : false;
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Playlist, "DeletePlaylist", _tenantId));

                int rowsaffected = cmd.ExecuteNonQuery();

                if (SiteConfig.RevSyncEnabled && RevSyncServerID > 0)
                {
                    bool bReturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "PlaylistDAL", "DeletePlaylist", JsonConvert.SerializeObject(RevSyncServerID)));
                    if (bReturn) tran.Commit(); else tran.Rollback();
                }
                else if (SiteConfig.IsMTEnable)
                {
                    bool isSent = DALHelper.SendMessageToHub(_tenantId, "PlaylistDAL", "DeletePlaylist", JsonConvert.SerializeObject(id));
                    if (isSent) tran.Commit(); else tran.Rollback();
                }
                else tran.Commit();

                return rowsaffected;
            }
        }


        #endregion


        #region Playlist Details

        //TODO: Create Seperate SP for Single Playlist Add Item
        public bool InsertPlaylistItemAudio(int playlistId, string callIds, short maxItems)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.PLAYLIST_DETAILS_INSERT;
                    //cmd.Parameters.AddWithValue("@RecId", playlistId);
                    cmd.Parameters.AddWithValue("@PlaylistId", playlistId);
                    cmd.Parameters.AddWithValue("@CallIds", callIds);
                    //cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@MAXItems", maxItems);
                    //cmd.Parameters.AddWithValue("@IsDeleted", isDeleted);
                    //cmd.Parameters.AddWithValue("@CreatedDate", createdDate);
                    //cmd.Parameters.AddWithValue("@ModifiedDate", modifiedDate);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Playlist, "InsertPlaylistItemAudio", _tenantId));

                    ////cmd.Parameters.Add("@Id", SqlDbType.Int).Direction = ParameterDirection.Output;  
                    //return Convert.ToInt16(cmd.ExecuteScalar());
                    int count = (int)cmd.ExecuteScalar(); //statement:- 1 for Success, -1 for Failure

                    return count == 1 ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }

        }

        public int InsertPlaylistItemsAudio(int playlistId, string callIds, int maxItems)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.PLAYLIST_DETAILS_INSERT;
                    cmd.Parameters.AddWithValue("@PlaylistId", playlistId);
                    cmd.Parameters.AddWithValue("@CallIds", callIds);
                    //cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@MAXItems", maxItems);
                    //cmd.Parameters.AddWithValue("@IsDeleted", isDeleted);
                    //cmd.Parameters.AddWithValue("@CreatedDate", createdDate);
                    //cmd.Parameters.AddWithValue("@ModifiedDate", modifiedDate);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Playlist, "InsertPlaylistItemsAudio", _tenantId));

                    ////cmd.Parameters.Add("@Id", SqlDbType.Int).Direction = ParameterDirection.Output;  
                    ////return Convert.ToInt16(cmd.ExecuteScalar());
                    int count = Convert.ToInt32(cmd.ExecuteScalar()); //(int)cmd.ExecuteScalar(); //statement:- 1 for Success, -1 for Failure
                    return count;
                    //return cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public List<PlaylistDetail> InsertPlaylistItemsNonAudio(int playlistId, List<PlaylistDetail> playlistItems)
        {
            //string commandText = @" IF Exists (SELECT * FROM Product_Location PL WHERE                  ProductID = @ProductID AND LocationID =  @LocationID)
            //   UPDATE Product_Location SET Quantity = Quantity + @Quantity WHERE ProductID = @ProductID AND LocationID = @LocationID
            //                            ELSE
            //                             INSERT INTO Product_Location (ProductID,LocationID,Quantity) VALUES(@ProductID,@LocationID,@quantity)";
            using (var conn = DALHelper.GetConnection(_tenantId))
            {
                conn.Open();
                SqlTransaction transaction = conn.BeginTransaction();
                SqlCommand command = conn.CreateCommand();
                command.Transaction = transaction;
                try
                {
                    //Ordering, @Ordering
                    command.CommandText = "INSERT INTO vrPlaylistDetail (CallId,PlayListId,CreatedDate,ModifiedDate,IsDeleted,StartTime,FileName,MessageBody) VALUES (@CallId, @PlayListId, @CreatedDate, @ModifiedDate, @IsDeleted, @StartTime, @FileName, @MessageBody);SELECT SCOPE_IDENTITY();";
                    command.CommandType = CommandType.Text;
                    //command.Parameters.AddWithValue("@CallId", "");
                    //command.Parameters.AddWithValue("@PlaylistId", 0);
                    //command.Parameters.AddWithValue("@Ordering", 0);
                    ////command.Parameters.AddWithValue("@MAXItems", maxItems);
                    //command.Parameters.AddWithValue("@IsDeleted", false);
                    //command.Parameters.AddWithValue("@FileName", "");
                    //command.Parameters.AddWithValue("@MessageBody", "");
                    //command.Parameters.AddWithValue("@StartTime", DateTime.Now);
                    //command.Parameters.AddWithValue("@CreatedDate", DateTime.Now);
                    //command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                    foreach (var pld in playlistItems)
                    {
                        command.Parameters.AddWithValue("@CallId", pld.CallId);
                        command.Parameters.AddWithValue("@PlaylistId", playlistId);
                        //command.Parameters.AddWithValue("@Ordering", 0);
                        //command.Parameters.AddWithValue("@MAXItems", maxItems);
                        command.Parameters.AddWithValue("@CreatedDate", DateTime.Now/*pld.CreatedDate*/);
                        command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now/*pld.ModifiedDate*/);
                        command.Parameters.AddWithValue("@IsDeleted", false);
                        command.Parameters.AddWithValue("@StartTime", pld.StartTime.HasValue ? pld.StartTime : DateTime.Now);
                        command.Parameters.AddWithValue("@FileName", pld.FileName);
                        command.Parameters.AddWithValue("@MessageBody", !string.IsNullOrEmpty(pld.MessageBody) ? pld.MessageBody : (object)DBNull.Value);
                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(command), Originator.Playlist, "InsertPlaylistItemsNonAudio", _tenantId));

                        //command.ExecuteNonQuery();
                        pld.Id = Convert.ToInt32(command.ExecuteScalar());
                        command.Parameters.Clear();
                    }
                    transaction.Commit();
                    return playlistItems;
                }
                catch (Exception ex)
                {
                    throw ex;
                    //try
                    //{
                    //    transaction.Rollback();
                    //}
                    //catch (Exception exRollback)
                    //{
                    //    if (!(exRollback is InvalidOperationException)) // connection closed or transaction already rolled back on the server.
                    //        throw exRollback;// ("Failed to roll back. " + exRollback.Message);
                    //}
                }
            }
        }

        public bool InsertPlaylistCustomItem(UserItemInfo userItem, int maxItems = 500)
        {
            try
            {
                int durInMilliSec = userItem.nDurationSec * 1000;
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    SqlTransaction transaction = conn.BeginTransaction();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Transaction = transaction;
                    cmd.CommandText = DBConstants.VoiceRec.PLAYLIST_DETAILS_INSERT_SINGLE_ITEM;

                    cmd.Parameters.AddWithValue("@CallId", userItem.sCallId);
                    cmd.Parameters.AddWithValue("@PlayListId", userItem.nPlaylistId);
                    //cmd.Parameters.AddWithValue("@Ordering",
                    cmd.Parameters.AddWithValue("@CreatedDate", DateTime.Now);
                    cmd.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);
                    cmd.Parameters.AddWithValue("@IsDeleted", false);
                    cmd.Parameters.AddWithValue("@StartTime", userItem.dtStartTime);
                    //DateTime other = DateTime.SpecifyKind(userItem.dtStartTime, DateTimeKind.Utc);
                    //DateTime other = new DateTime(userItem.dtStartTime.Ticks, DateTimeKind.Utc);

                    cmd.Parameters.AddWithValue("@FileName", userItem.sFileName);
                    cmd.Parameters.AddWithValue("@MessageBody", userItem.sText);
                    //cmd.Parameters.AddWithValue("@FileType", (int)userItem.nFileType);
                    cmd.Parameters.AddWithValue("@FileType", (int)getFileTypeFromPlayerFormat(userItem.nFileType));
                    cmd.Parameters.AddWithValue("@NoOfPages", userItem.nPageCount);
                    // cmd.Parameters.AddWithValue("@Duration", userItem.nDurationSec);
                    cmd.Parameters.AddWithValue("@Duration", durInMilliSec);
                    cmd.Parameters.AddWithValue("@IsUserAdded", true);
                    cmd.Parameters.AddWithValue("@RecorderId", userItem.nRecorderId);

                    cmd.Parameters.AddWithValue("@MAXItems", maxItems);
                    //cmd.Parameters.AddWithValue("@CreatedDate", createdDate);
                    //cmd.Parameters.AddWithValue("@ModifiedDate", modifiedDate);

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Playlist, "InsertPlaylistCustomItem", _tenantId));

                    bool breturn = cmd.ExecuteNonQuery() > 0 ? true : false;

                    if (SiteConfig.RevSyncEnabled && userItem.RevSyncServerPlaylistID > 0 && breturn)
                    {
                        breturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "PlaylistDAL", "InsertPlaylistCustomItem", JsonConvert.SerializeObject(userItem)));
                        if (breturn) transaction.Commit(); else transaction.Rollback();
                    }
                    else if (SiteConfig.IsMTEnable)
                    {
                        breturn = DALHelper.SendMessageToHub(_tenantId, "PlaylistDAL", "InsertPlaylistCustomItem", JsonConvert.SerializeObject(userItem));
                        if (breturn) transaction.Commit(); else transaction.Rollback();
                    }
                    else transaction.Commit();
                    return breturn;
                }
            }
            catch (Exception ex) { throw ex; }
        }
        private static FileType getFileTypeFromPlayerFormat(PlayerFileType playerFormat)
        {
            switch (playerFormat)
            {
                case PlayerFileType.NotSupported:
                    return FileType.None;
                //case PlayerFileType.DSF:
                //    break;
                case PlayerFileType.WAV:
                    return FileType.Audio;
                case PlayerFileType.WMA:
                    return FileType.Audio;
                case PlayerFileType.WMV:
                    return FileType.Video;
                case PlayerFileType.ASF://Wmv + Wma
                    return FileType.Video;
                case PlayerFileType.MP3:
                    return FileType.Audio;
                case PlayerFileType.SMS:
                    return FileType.Text;
                case PlayerFileType.Email:
                    return FileType.Email;
                case PlayerFileType.Social:
                    return FileType.Social;
                case PlayerFileType.Image:
                    return FileType.Image;
                case PlayerFileType.Document:
                    return FileType.Document;
                default:
                    return FileType.None;
            }
        }

        /* ========= Insert Playlist Item For All Types without Merge ========= */
        public int InsertPlaylistItem(IEnumerable<PlaylistDetail> plds, int playlistId, int RevSyncServerPlaylistID, int maxItems = 500)
        {
            int rowsAffected = 0;
            double nDurationInMS = 0; //arivu
            using (SqlConnection conn = DALHelper.GetConnection(_tenantId))
            {
                conn.Open();
                SqlTransaction transaction = conn.BeginTransaction();
                SqlCommand cmd = new SqlCommand(DBConstants.VoiceRec.PLAYLIST_DETAILS_INSERT_SINGLE_ITEM, conn);
                cmd.Transaction = transaction;
                cmd.CommandType = System.Data.CommandType.StoredProcedure;
                //SqlParameter param = cmd.Parameters.Add("@UserCode", SqlDbType.VarChar);

                foreach (var pld in plds)
                {
                    nDurationInMS = pld.DurationInSec * 1000;//arivu
                    //param.Value = code;
                    cmd.Parameters.AddWithValue("@CallId", pld.CallId);
                    cmd.Parameters.AddWithValue("@PlayListId", playlistId);
                    //cmd.Parameters.AddWithValue("@Ordering",
                    cmd.Parameters.AddWithValue("@CreatedDate", DateTime.Now);
                    cmd.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);
                    cmd.Parameters.AddWithValue("@IsDeleted", false);
                    cmd.Parameters.AddWithValue("@StartTime", pld.StartTimeString.ConvertStringDateTimeToDateTime());
                    cmd.Parameters.AddWithValue("@FileName", pld.FileName);
                    cmd.Parameters.AddWithValue("@MessageBody", pld.MessageBody);
                    cmd.Parameters.AddWithValue("@FileType", pld.FileType);
                    cmd.Parameters.AddWithValue("@NoOfPages", pld.NoOfPages);

                    // cmd.Parameters.AddWithValue("@Duration", pld.DurationInSec); arivu
                    cmd.Parameters.AddWithValue("@Duration", pld.DurationInSec);


                    cmd.Parameters.AddWithValue("@IsUserAdded", pld.IsUserAddedItem);
                    cmd.Parameters.AddWithValue("@IsDemoItem", pld.IsDemoItem);
                    cmd.Parameters.AddWithValue("@RecorderId", pld.RecorderId);

                    cmd.Parameters.AddWithValue("@MAXItems", maxItems);
                    //rowsAffected += cmd.ExecuteNonQuery();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Playlist, "InsertPlaylistItem", _tenantId));

                    int currentRowAffected = cmd.ExecuteNonQuery();

                    if (currentRowAffected >= 0)
                        rowsAffected++;

                    cmd.Parameters.Clear();
                }

                try
                {
                    if (SiteConfig.RevSyncEnabled && RevSyncServerPlaylistID > 0) {
                        bool bReturn = false;
                        int revsync_rowsAffected = 0;
                        foreach (var pld in plds)
                        {
                            List<PlaylistDetail> playlistDetails = new List<PlaylistDetail>();
                            playlistDetails.Add(pld);

                            Dictionary<string, string> _dic = new Dictionary<string, string>();
                            _dic.Add("PlaylistDetail", JsonConvert.SerializeObject(playlistDetails));
                            _dic.Add("PlaylistId", Convert.ToString(RevSyncServerPlaylistID));

                            bReturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "PlaylistDAL", "InsertPlaylistItem", JsonConvert.SerializeObject(_dic)));
                            if (bReturn) revsync_rowsAffected++;
                        }
                        
                        if (revsync_rowsAffected > 0) transaction.Commit(); else transaction.Rollback();
                    } else if (SiteConfig.IsMTEnable) {
                        Dictionary<string, string> _dic = new Dictionary<string, string>();
                        _dic.Add("PlaylistDetail", JsonConvert.SerializeObject(plds));
                        _dic.Add("PlaylistId", Convert.ToString(playlistId));

                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "PlaylistDAL", "InsertPlaylistItem", JsonConvert.SerializeObject(_dic));
                        if (isSent) transaction.Commit(); else transaction.Rollback();
                    } else
                        transaction.Commit();
                }
                catch (Exception _exception)
                {
                    transaction.Rollback();
                    throw _exception;
                }
            }
            return rowsAffected;
        }

        public bool DeletePlaylistItem(int playlistDetailId, int RevSyncServerPlaylistID)
        {
            int count = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    SqlTransaction transaction = conn.BeginTransaction();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Transaction = transaction;
                    cmd.CommandText = DBConstants.VoiceRec.PLAYLIST_DETAILS_DELETE_BY_ID;
                    cmd.Parameters.AddWithValue("@PlayListDetailId", playlistDetailId);
                    //cmd.Parameters.AddWithValue("@ModifiedDate", modifiedDate);
                    count = (int)cmd.ExecuteScalar(); //statement:- 1 for Success, -1 for Failure

                    if (SiteConfig.RevSyncEnabled && RevSyncServerPlaylistID > 0)
                    {
                        bool bReturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "PlaylistDAL", "DeletePlaylistItem", JsonConvert.SerializeObject(RevSyncServerPlaylistID)));
                        if (bReturn) transaction.Commit(); else transaction.Rollback();
                    }
                    else if (SiteConfig.IsMTEnable)
                    {
                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "PlaylistDAL", "DeletePlaylistItem", JsonConvert.SerializeObject(playlistDetailId));
                        if (isSent) transaction.Commit(); else transaction.Rollback();
                    }
                    else
                        transaction.Commit();
                }
            }
            catch (Exception ex) { throw ex; }
            return count == 1 ? true : false;
        }

        public int DeletePlaylistItem(int playlistId, string callId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.PLAYLIST_DETAILS_DELETE_BY_PLID_N_CALLID;
                    cmd.Parameters.AddWithValue("@PlayListId", playlistId);
                    cmd.Parameters.AddWithValue("@CallId", callId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Playlist, "DeletePlaylistItem", _tenantId));

                    return cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public bool DeletePlaylistItemByCallId(string callId)
        {
            int rowsaffected = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    SqlTransaction transaction = conn.BeginTransaction();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Transaction = transaction;
                    cmd.CommandText = DBConstants.VoiceRec.PLAYLIST_DETAILS_DELETE_BY_CALLID;
                    cmd.Parameters.AddWithValue("@CallId", callId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Playlist, "DeletePlaylistItemByCallId", _tenantId));

                    rowsaffected = cmd.ExecuteNonQuery();

                    if (SiteConfig.RevSyncEnabled) {
                        bool bReturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "PlaylistDAL", "DeletePlaylistItemByCallId", JsonConvert.SerializeObject(callId)));
                        if (bReturn) transaction.Commit(); else transaction.Rollback();
                    } else if (SiteConfig.IsMTEnable) {
                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "PlaylistDAL", "DeletePlaylistItemByCallId", JsonConvert.SerializeObject(callId));
                        if (isSent) transaction.Commit(); else transaction.Rollback();
                    } else transaction.Commit();
                }
            }
            catch (Exception ex) { throw ex; }
            return rowsaffected > 0 ? true : false;
        }

        public int DeletePlaylistItems(int playlistId, int RevSyncServerPlaylistID)
        {
            int rowsaffected = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    SqlTransaction transaction = conn.BeginTransaction();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Transaction = transaction;
                    cmd.CommandText = DBConstants.VoiceRec.PLAYLIST_DETAILS_DELETE_BY_PLAYLIST_ID;
                    cmd.Parameters.AddWithValue("@PlayListId", playlistId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Playlist, "DeletePlaylistItems", _tenantId));
                    rowsaffected = cmd.ExecuteNonQuery();

                    if (SiteConfig.RevSyncEnabled && RevSyncServerPlaylistID > 0) {
                        bool isSent = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "PlaylistDAL", "DeletePlaylistItems", JsonConvert.SerializeObject(RevSyncServerPlaylistID)));
                        if (isSent) transaction.Commit(); else transaction.Rollback();
                    } else if (SiteConfig.IsMTEnable) {
                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "PlaylistDAL", "DeletePlaylistItems", JsonConvert.SerializeObject(playlistId));
                        if (isSent) transaction.Commit(); else transaction.Rollback();
                    } else transaction.Commit();

                }
            }
            catch (Exception ex) { throw ex; }
            return rowsaffected;
        }

        #endregion


        #region Playlist Note
        public List<PlaylistNote> GetPlaylistNotes(int playlistId)
        {
            List<PlaylistNote> playlistNotes = null;
            PlaylistNote playlistNote = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.PLAYLIST_NOTE_GETALL;
                    cmd.Parameters.AddWithValue("@PlaylistId", playlistId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Playlist, "GetPlaylistNotes", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        playlistNotes = new List<PlaylistNote>();
                        while (dr.Read())
                        {
                            playlistNote = new PlaylistNote();
                            playlistNote.Id = (int)dr["Id"];
                            playlistNote.PlaylistId = Convert.ToInt32(dr["PlaylistId"]);
                            playlistNote.NoteType = Convert.ToInt32(dr["NoteType"]);
                            playlistNote.Note = Convert.ToString(dr["Note"]);
                            playlistNote.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                            playlistNote.ModifiedDate = Convert.ToDateTime(dr["ModifiedDate"]);
                            playlistNote.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                            playlistNotes.Add(playlistNote);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return playlistNotes;
        }
        public int InsertPlaylistNote(int playlistId, int noteType, string note)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.PLAYLIST_NOTE_INSERT;
                    cmd.Parameters.AddWithValue("@PlaylistId", playlistId);
                    cmd.Parameters.AddWithValue("@NoteType", noteType);
                    cmd.Parameters.AddWithValue("@Note", note);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Playlist, "InsertPlaylistNote", _tenantId));

                    int count = (int)cmd.ExecuteScalar();
                    return count;
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public bool UpdatePlaylistNote(int noteId, int playlistId, string note)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.PLAYLIST_NOTE_UPDATE;
                    cmd.Parameters.AddWithValue("@NoteId", noteId);
                    cmd.Parameters.AddWithValue("@PlaylistId", playlistId);
                    cmd.Parameters.AddWithValue("@Note", note);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Playlist, "UpdatePlaylistNote", _tenantId));

                    int count = (int)cmd.ExecuteScalar();
                    return count >= 1 ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public bool DeletePlaylistNote(int noteId, int playlistId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.PLAYLIST_NOTE_DELETE;
                    cmd.Parameters.AddWithValue("@NoteId", noteId);
                    cmd.Parameters.AddWithValue("@PlaylistId", playlistId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Playlist, "DeletePlaylistNote", _tenantId));
                    int count = (int)cmd.ExecuteScalar();
                    return count >= 1 ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
        }
        #endregion
        public bool UpdatePlaylistAttributes(int playlistId, string playlistName, string visibilityType, string playlistComments)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    SqlTransaction transaction = conn.BeginTransaction();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Transaction = transaction;
                    cmd.CommandText = DBConstants.VoiceRec.PLAYLIST_UPDATE_ATTRIBUTES;
                    cmd.Parameters.AddWithValue("@PlaylistId", playlistId);
                    cmd.Parameters.AddWithValue("@PlaylistName", playlistName);
                    cmd.Parameters.AddWithValue("@VisibilityType", visibilityType);
                    cmd.Parameters.AddWithValue("@Comments", playlistComments);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Playlist, "UpdatePlaylistAttributes", _tenantId));

                    int count = (int)cmd.ExecuteScalar();

                    try
                    {
                        if (SiteConfig.RevSyncEnabled)
                        {
                            Dictionary<string, string> _dic = new Dictionary<string, string>();
                            _dic.Add("playlistName", JsonConvert.SerializeObject(playlistName));
                            _dic.Add("PlaylistId", Convert.ToString(playlistId));

                            bool bReturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "PlaylistDAL", "UpdatePlaylistName", JsonConvert.SerializeObject(_dic)));
                            if (bReturn) transaction.Commit(); else transaction.Rollback();
                        } else if (SiteConfig.IsMTEnable) {
                            Dictionary<string, string> _dic = new Dictionary<string, string>();
                            _dic.Add("playlistName", JsonConvert.SerializeObject(playlistName));
                            _dic.Add("PlaylistId", Convert.ToString(playlistId));

                            bool isSent = DALHelper.SendMessageToHub(_tenantId, "PlaylistDAL", "UpdatePlaylistName", JsonConvert.SerializeObject(_dic));
                            if (isSent) transaction.Commit(); else transaction.Rollback();
                        }
                        else transaction.Commit();
                    }
                    catch (Exception _exception)
                    {
                        transaction.Rollback();
                        throw _exception;
                    }
                    return count > 0 ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public bool AddAlternateFileName(int playlistId, string callId, string alternateFileName)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.PLAYLIST_ALTERNATE_FILENAME_ADD;
                    cmd.Parameters.AddWithValue("@PlaylistId", playlistId);
                    cmd.Parameters.AddWithValue("@CallId", callId);
                    cmd.Parameters.AddWithValue("@AlternateFileName", alternateFileName);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Playlist, "AddAlternateFileName", _tenantId));

                    int count = (int)cmd.ExecuteScalar();
                    return count > 0 ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public int CountPlaylistItems(int playlistId)
        {
            int noOfItems = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT COUNT(*) AS NoOfItems FROM vrPlaylistDetail WHERE PlaylistId = @PlaylistId and IsDeleted = 0;";
                    cmd.Parameters.AddWithValue("@PlaylistId", playlistId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Playlist, "CountPlaylistItems", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            noOfItems = Convert.ToInt32(dr["NoOfItems"]);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return noOfItems;
        }

        public int PlaylistShareInsert(int playlistId, int sharedBy, int sharedWith,string sharedWithEmail, string oneTimeLink)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.PLAYLIST_SHARE_INSERT;
                    cmd.Parameters.AddWithValue("@PlaylistId", playlistId);
                    cmd.Parameters.AddWithValue("@SharedBy", sharedBy);
                    cmd.Parameters.AddWithValue("@SharedWith", sharedWith);
                    cmd.Parameters.AddWithValue("@SharedWithEmail", sharedWithEmail);
                    cmd.Parameters.AddWithValue("@IsViewed", false);
                    cmd.Parameters.AddWithValue("@OneTimeLink", oneTimeLink);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Playlist, "PlaylistShareInsert", _tenantId));

                    int count = (int)cmd.ExecuteScalar();
                    return count;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public PlaylistShare PlaylistShareGet(int plShareId)
        {
            PlaylistShare objPl = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT * FROM vrPlaylistShare WHERE Id = @Id;";
                    cmd.Parameters.AddWithValue("@Id", plShareId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Playlist, "PlaylistShareGet", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        objPl = new PlaylistShare();

                        if (dr.Read())
                        {
                            objPl.Id = Convert.ToInt32(dr["Id"]);
                            objPl.PlaylistId = Convert.ToInt32(dr["PlaylistId"]);
                            objPl.SharedBy = Convert.ToInt32(dr["SharedBy"]);
                            objPl.SharedWith = Convert.ToInt32(dr["SharedWith"]);
                            objPl.SharedWithEmail = Convert.ToString(dr["SharedWithEmail"]);
                            objPl.IsViewed = Convert.ToBoolean(dr["IsViewed"]);
                            objPl.OneTimeLink = Convert.ToString(dr["OneTimeLink"]);
                            objPl.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return objPl;
        }

        public bool PlaylistShareUpdate(int plShareId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "UPDATE vrPlaylistShare SET IsViewed = 1 WHERE Id = @Id";
                    cmd.Parameters.AddWithValue("@Id", plShareId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Playlist, "PlaylistShareUpdate", _tenantId));

                    int count = (int)cmd.ExecuteNonQuery();
                    return count > 0 ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public bool AddDocumentToPlayList(int documentId, int playlistId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.PLAYLIST_DOCUMENT_ADD;
                    cmd.Parameters.AddWithValue("@PlayListId", playlistId);
                    cmd.Parameters.AddWithValue("@DocumentId", documentId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Playlist, "AddDocumentToPlayList", _tenantId));

                    int count = (int)cmd.ExecuteNonQuery();
                    return count > 0 ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
        }
    }
}