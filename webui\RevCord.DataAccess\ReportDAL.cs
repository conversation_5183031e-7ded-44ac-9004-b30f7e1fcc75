﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.ReportEntities;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using RevCord.DataAccess.Util;
using RevCord.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.UserManagement;
using RevCord.DataContracts.IQ3;
using RevCord.DataContracts.Criteria;
using RevCord.DataContracts.VoiceRecEntities;

namespace RevCord.DataAccess
{
    public class ReportDAL
    {
        private int _tenantId;

        public ReportDAL(int tenantId)
        {
            this._tenantId = tenantId;
        }


        #region Advance Report's



        #endregion

        #region Evaluation Reports

        public List<RPTEvaluation> GetRPTEvaluationReport(string criteria, int userId)
        {
            List<RPTEvaluation> callEvaluations = new List<RPTEvaluation>();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.RPT_CALLEVALUATION_GET_BY_SURVEYID;
                    cmd.Parameters.AddWithValue("@Criteria", criteria);
                    cmd.Parameters.AddWithValue("@UserId", userId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "GetRPTEvaluationReport", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {

                        callEvaluations = ORMapper.MapRPTEvaluations(dr);

                        return callEvaluations;
                    }
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public List<RPTEvaluationMain> GetRPTEvaluationMainReport(string criteria, int userId)
        {
            List<RPTEvaluationMain> callEvaluations = new List<RPTEvaluationMain>();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.RPT_CALLEVALUATION_MAIN_GET_BY_SURVEYID;
                    cmd.Parameters.AddWithValue("@Criteria", criteria);
                    cmd.Parameters.AddWithValue("@UserId", userId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "GetRPTEvaluationMainReport", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {

                        callEvaluations = ORMapper.MapRPTEvaluationsMain(dr);

                        return callEvaluations;
                    }
                }
            }
            catch (Exception ex) { throw ex; }
        }

        #endregion

        #region Call Audit Report
        public List<CallAuditReport> GetCallAuditReport(int userId, DateTime startDate, DateTime endDate, int pageSize, int pageIndex, out int totalPages, out int totalRecords)
        {
            List<CallAuditReport> callAuditReports = new List<CallAuditReport>();
            CallAuditReport callAuditReport = new CallAuditReport();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Reports.CallAuditReport.CALL_AUDIT_RESULT_EXCEL_GETALL;
                    cmd.Parameters.AddWithValue("@startDate", startDate);
                    cmd.Parameters.AddWithValue("@endDate", endDate);
                    cmd.Parameters.AddWithValue("@userId", userId);

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "GetCallAuditReport", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            callAuditReports = new List<CallAuditReport>();
                            while (dr.Read())
                            {
                                callAuditReport = new CallAuditReport();
                                callAuditReport.Id = Convert.ToInt32(dr["Id"]);
                                callAuditReport.UserNum = (int)dr["UserNum"];
                                callAuditReport.AuditedBy = Convert.ToString(dr["AuditedBy"]);
                                callAuditReport.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                                callAuditReport.IPAddress = Convert.ToString(dr["IPAddress"]);

                                callAuditReport.CallId = Convert.ToString(dr["CallId"]);
                                callAuditReport.CallType = Convert.ToInt32(dr["CallType"]);
                                callAuditReport.StartTime = DateTime.ParseExact(Convert.ToString(dr["StartTime"]), "yyyyMMddHHmmss", null).ToString();
                                callAuditReport.DurationInMilliSeconds = Convert.ToInt32(dr["DurationInMilliSeconds"]);
                                callAuditReport.Ext = Convert.ToInt32(dr["Ext"]);
                                callAuditReport.ExtName = Convert.ToString(dr["ExtName"]);
                                callAuditReport.AuditDateTime = Convert.ToDateTime(dr["AuditDateTime"]);

                                callAuditReports.Add(callAuditReport);
                            }
                        }
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt32(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }
            return callAuditReports;
        }
        public List<CallAuditReport> GetAuditedCallsByExtension(DateTime startDate, DateTime endDate, int ext)
        {
            List<CallAuditReport> callAuditReports = new List<CallAuditReport>();
            CallAuditReport callAuditReport = new CallAuditReport();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Reports.CallAuditReport.CALL_AUDIT_GET_BY_EXTENSION;
                    cmd.Parameters.AddWithValue("@startDate", startDate);
                    cmd.Parameters.AddWithValue("@endDate", endDate);
                    cmd.Parameters.AddWithValue("@ext", ext);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "GetAuditedCallsByExtension", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            callAuditReports = new List<CallAuditReport>();
                            while (dr.Read())
                            {
                                callAuditReport = new CallAuditReport();
                                callAuditReport.RowNo = Convert.ToInt32(dr["RowNo"]);
                                callAuditReport.CallId = Convert.ToString(dr["CallId"]);
                                callAuditReport.UserNum = (int)dr["UserNum"];
                                callAuditReport.CallType = Convert.ToInt32(dr["CallType"]);
                                callAuditReport.StartTime = DateTime.ParseExact(Convert.ToString(dr["StartTime"]), "yyyyMMddHHmmss", null).ToString();
                                callAuditReport.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);
                                callAuditReport.Ext = Convert.ToInt32(dr["Ext"]);
                                callAuditReport.ExtName = Convert.ToString(dr["ExtName"]);
                                callAuditReport.FileName = Convert.ToString(dr["FileName"]);
                                callAuditReport.NoOfAudits = Convert.ToInt32(dr["NoOfAudits"]);

                                callAuditReports.Add(callAuditReport);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return callAuditReports;
        }
        public List<CallAuditReport> GetCallAuditTrail(DateTime startDate, DateTime endDate, string callId)
        {
            List<CallAuditReport> callAuditReports = new List<CallAuditReport>();
            CallAuditReport callAuditReport = new CallAuditReport();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Reports.CallAuditReport.CALL_AUDIT_TRAIL;
                    cmd.Parameters.AddWithValue("@startDate", startDate);
                    cmd.Parameters.AddWithValue("@endDate", endDate);
                    cmd.Parameters.AddWithValue("@callId", callId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "GetCallAuditTrail", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            callAuditReports = new List<CallAuditReport>();
                            while (dr.Read())
                            {
                                callAuditReport = new CallAuditReport();
                                callAuditReport.Id = Convert.ToInt32(dr["Id"]);
                                callAuditReport.UserNum = (int)dr["UserNum"];
                                callAuditReport.AuditedBy = Convert.ToString(dr["AuditedBy"]);
                                callAuditReport.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                                callAuditReport.IPAddress = Convert.ToString(dr["IPAddress"]);

                                callAuditReport.CallId = Convert.ToString(dr["CallId"]);
                                callAuditReport.CallType = Convert.ToInt32(dr["CallType"]);
                                callAuditReport.StartTime = DateTime.ParseExact(Convert.ToString(dr["StartTime"]), "yyyyMMddHHmmss", null).ToString();
                                callAuditReport.DurationInMilliSeconds = Convert.ToInt32(dr["DurationInMilliSeconds"]);
                                callAuditReport.Ext = Convert.ToInt32(dr["Ext"]);
                                callAuditReport.ExtName = Convert.ToString(dr["ExtName"]);
                                callAuditReport.FileName = Convert.ToString(dr["FileName"]);
                                callAuditReport.AuditDateTime = Convert.ToDateTime(dr["AuditDateTime"]);
                                callAuditReport.IsSaved = Convert.ToBoolean(dr["IsSaved"]);
                                callAuditReport.IsTagged = Convert.ToBoolean(dr["IsTagged"]);

                                callAuditReports.Add(callAuditReport);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return callAuditReports;
        }

        #endregion

        #region Saved Reports
        public int AddSavedReport(SavedReport savedReport)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Reports.SavedReport.SAVEDREPORT_ADD;
                    cmd.Parameters.AddWithValue("@UserId", savedReport.UserId);
                    cmd.Parameters.AddWithValue("@ReportType", savedReport.ReportTypeId);
                    cmd.Parameters.AddWithValue("@ReportName", savedReport.ReportName);
                    cmd.Parameters.AddWithValue("@Criteria", savedReport.Criteria);
                    cmd.Parameters.AddWithValue("@Comments", savedReport.Description);
                    cmd.Parameters.AddWithValue("@ExecutionDate", savedReport.LastExecutedOn);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "AddSavedReport", _tenantId));

                    int lastSavedId = (int)cmd.ExecuteScalar();
                    return lastSavedId;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public SavedReport CreateDuplicateReport(int UserId, int ReportId, string ReportName, string ReportDescription)
        {
            SavedReport objSavedReport = new SavedReport();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Reports.SavedReport.SAVEDREPORT_DUPLICATE;
                    cmd.Parameters.AddWithValue("@UserId", UserId);
                    cmd.Parameters.AddWithValue("@ReportId", ReportId);
                    cmd.Parameters.AddWithValue("@ReportName", ReportName);
                    cmd.Parameters.AddWithValue("@Comments", ReportDescription);
                    conn.Open();

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "CreateDuplicateReport", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        objSavedReport = ORMapper.MapSavedReport(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return objSavedReport;
        }

        public int UpdateSavedReport(int savedReportId, string criteria)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Reports.SavedReport.SAVEDREPORT_UPDATE;
                    cmd.Parameters.AddWithValue("@ReportId", savedReportId);
                    cmd.Parameters.AddWithValue("@Criteria", criteria);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "UpdateSavedReport", _tenantId));
                    int count = (int)cmd.ExecuteScalar();
                    return count;
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public List<SavedReport> GetAllSavedReports(int userId)
        {
            List<SavedReport> savedReports = new List<SavedReport>();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Reports.SavedReport.SAVEDREPORT_GETALL;
                    cmd.Parameters.AddWithValue("@UserId", userId);
                    conn.Open();
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        savedReports = ORMapper.MapAllSavedReports(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return savedReports;
        }

        public List<SavedReport> GetSavedReportsPaged(int userId, int pageSize, int pageIndex, string whereClause, bool loadGroupHeriracy, out int totalPages, out long totalRecords)
        {
            List<SavedReport> savedReports = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Reports.SavedReport.SAVEDREPORT_GETALL_PAGED;

                    cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@WhereClause", whereClause);
                    //cmd.Parameters.AddWithValue("@GroupsRequired", loadGroupHeriracy);
                    cmd.Parameters.Add("@GroupsRequired", SqlDbType.Bit).Value = loadGroupHeriracy;

                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        savedReports = ORMapper.MapAllSavedReports(dr);
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }
            return savedReports;
        }

        public List<SavedReport> GetSharedSavedReportsPaged(int userId, int pageSize, int pageIndex, string whereClause, out int totalPages, out long totalRecords)
        {
            List<SavedReport> savedReports = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Reports.SavedReport.SHAREDSAVEDREPORT_GETALL_PAGED;

                    cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@WhereClause", whereClause);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        savedReports = ORMapper.MapAllSavedReports(dr);
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }
            return savedReports;
        }


        public SavedReport GetSavedReportById(int savedReportId)
        {
            SavedReport savedReport = new SavedReport();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Reports.SavedReport.SAVEDREPORT_GET_BY_ID;
                    cmd.Parameters.AddWithValue("@Id", savedReportId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "GetSavedReportById", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        savedReport = ORMapper.MapSavedReport(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return savedReport;
        }
        public string GetSavedReportCriteriaById(int savedReportId)
        {
            string criteria = string.Empty;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Reports.SavedReport.SAVEDREPORT_GET_CRITERIA_BY_ID;
                    cmd.Parameters.AddWithValue("@Id", savedReportId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "GetSavedReportCriteriaById", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        dr.Read();
                        criteria = Convert.ToString(dr["Criteria"]);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return criteria;
        }
        public bool DeleteSavedReport(int savedReportId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Reports.SavedReport.SAVEDREPORT_DELETE;
                    cmd.Parameters.AddWithValue("@SavedReportId", savedReportId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "DeleteSavedReport", _tenantId));

                    int count = (int)cmd.ExecuteNonQuery();
                    return count >= 1 ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public int DeleteSavedReports(List<int> reportIds)
        {
            //var reportIds = new List<int>() { 118, 119, 120 };
            int rowsAffected = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "UPDATE vrSavedReport SET IsDeleted = 1 WHERE Id IN (" + String.Join(",", reportIds) + ")";
                    conn.Open();
                    rowsAffected = cmd.ExecuteNonQuery();
                    return rowsAffected;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public int MarkUnmarkFavorite(int userNum, int reportId, bool isFavorite)
        {
            int rowsAffected = 0;
            SavedReport objSavedReport = null;

            try
            {
                objSavedReport = GetSavedReportById(reportId);

                if (objSavedReport != null)
                {
                    if (isFavorite)
                    {
                        if (!objSavedReport.FavoriteOf.Contains(userNum))
                        {
                            objSavedReport.FavoriteOf.Add(userNum);
                        }
                    }
                    else
                    {
                        if (objSavedReport.FavoriteOf.Contains(userNum))
                        {
                            objSavedReport.FavoriteOf.Remove(userNum);
                        }
                    }

                    using (var conn = DALHelper.GetConnection(_tenantId))
                    using (var cmd = conn.CreateCommand())
                    {
                        cmd.CommandType = CommandType.Text;
                        cmd.CommandText = "UPDATE vrSavedReport SET FavoriteOf = @FavoriteOf WHERE Id = @reportId";
                        cmd.Parameters.AddWithValue("@reportId", reportId);
                        cmd.Parameters.AddWithValue("@FavoriteOf", string.Join(",", objSavedReport.FavoriteOf));

                        conn.Open();
                        rowsAffected = cmd.ExecuteNonQuery();
                    }
                }
                return rowsAffected;
            }
            catch (Exception ex) { throw ex; }
        }

        public int UpdateReportExecutionTime(int reportId, DateTime executionTime)
        {
            int rowsAffected = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "UPDATE vrSavedReport SET LastExecutedOn = @executionDate WHERE Id = @reportId";
                    cmd.Parameters.AddWithValue("@executionDate", executionTime);
                    cmd.Parameters.AddWithValue("@reportId", reportId);

                    conn.Open();
                    rowsAffected = cmd.ExecuteNonQuery();
                    return rowsAffected;
                }
            }
            catch (Exception ex) { throw ex; }
        }




        #endregion

        #region Activity Report
        public List<UserActivity> GetUserActivities(int userId, DateTime startDate, DateTime endDate, int pageSize, int pageIndex, out int totalPages, out long totalRecords)
        {
            List<UserActivity> userActivities = null;
            UserActivity userActivity = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.USER_ACTIVITY_SEARCH;
                    cmd.Parameters.AddWithValue("@startDate", startDate);
                    cmd.Parameters.AddWithValue("@endDate", endDate);
                    cmd.Parameters.AddWithValue("@userId", userId);

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "GetUserActivities", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            userActivities = new List<UserActivity>();
                            while (dr.Read())
                            {
                                userActivity = new UserActivity();
                                userActivity.Id = Convert.ToInt64(dr["Id"]);
                                userActivity.UserId = (int)dr["UserId"];
                                userActivity.ActivityId = Convert.ToInt32(dr["ActivityId"]);
                                userActivity.ActivityPerformed = Convert.ToString(dr["ActivityPerformed"]);
                                userActivity.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                                userActivity.MessageKey = Convert.ToString(dr["MessageKey"]);
                                userActivity.MessageData = Convert.ToString(dr["MessageData"]);
                                userActivity.Comments = Convert.ToString(dr["Comments"]);
                                userActivity.ClientIP = Convert.ToString(dr["ClientIPAddress"]);

                                userActivities.Add(userActivity);
                            }
                        }
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }
            return userActivities;
        }
        #endregion

        #region Shared Report Info

        public SharedReportInfo GetSharedReportInfo(int SharedReportInfoId)
        {
            SharedReportInfo objSharedReportInfo = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.SharedReportInfo.SHARED_REPORT_GET_BY_ID;
                    cmd.Parameters.AddWithValue("@Id", SharedReportInfoId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "GetSharedReportInfo", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        objSharedReportInfo = ORMapper.MapSharedReportInfo(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return objSharedReportInfo;
        }

        public SharedReportInfo GetSharedReportInfoByReport(int SavedReportId)
        {
            SharedReportInfo objSharedReportInfo = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.SharedReportInfo.SHARED_REPORT_GET_BY_SAVED_REPORT_ID;
                    cmd.Parameters.AddWithValue("@SavedReportId", SavedReportId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "GetSharedReportInfoByReport", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        objSharedReportInfo = ORMapper.MapSharedReportInfo(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return objSharedReportInfo;
        }

        public int SaveSharedReportInfo(SharedReportInfo objSharedReportInfo)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();

                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.SharedReportInfo.SHARED_REPORT_INSERT;
                    cmd.Parameters.AddWithValue("@SharedBy", objSharedReportInfo.SharedBy);
                    cmd.Parameters.AddWithValue("@ReportId", objSharedReportInfo.ReportId);

                    if (objSharedReportInfo.SharedWith != null)
                    {
                        cmd.Parameters.AddWithValue("@SharedWith", string.Join(",", objSharedReportInfo.SharedWith));
                    }

                    cmd.Parameters.AddWithValue("@CreatedOn", DateTime.Now);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "SaveSharedReportInfo", _tenantId));
                    return Convert.ToInt32(cmd.ExecuteScalar());
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public int UpdateSharedReportInfo(SharedReportInfo objSharedReportInfo)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();

                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.SharedReportInfo.SHARED_REPORT_UPDATE;
                    cmd.Parameters.AddWithValue("@Id", objSharedReportInfo.Id);

                    if (objSharedReportInfo.SharedWith != null)
                    {
                        cmd.Parameters.AddWithValue("@SharedWith", string.Join(",", objSharedReportInfo.SharedWith));
                    }

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "UpdateSharedReportInfo", _tenantId));

                    return Convert.ToInt32(cmd.ExecuteScalar());
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public int RemoveSharedReportInfo(int SharedReportInfoId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();

                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.SharedReportInfo.SHARED_REPORT_REMOVE;
                    cmd.Parameters.AddWithValue("@Id", SharedReportInfoId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "RemoveSharedReportInfo", _tenantId));

                    return Convert.ToInt32(cmd.ExecuteScalar());
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public static List<int> GetAllUserNumReportSharedWith(string ShareWith)
        {
            List<int> UserNumList = new List<int>();

            if (!string.IsNullOrEmpty(ShareWith))
            {
                foreach (var UserNum in ShareWith.Split(','))
                {
                    try
                    {
                        UserNumList.Add(Convert.ToInt32(UserNum));
                    }
                    catch (Exception ex)
                    {
                        //Not an Int
                    }
                }
            }

            return UserNumList;
        }

        #endregion

        public string GetCompanyNameForVesselReport()
        {
            try
            {
                using (var conn = DALHelper.GetConnectionMasterDB())
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();

                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT Name from mtTenant where Id = @TenantId";
                    cmd.Parameters.AddWithValue("@TenantId", _tenantId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "GetCompanyNameForVesselReport", _tenantId));

                    return Convert.ToString(cmd.ExecuteScalar());
                }
            }
            catch (Exception ex) { throw ex; }
        }

        #region Tenant IQ3 Usage Report
        public List<TenantIQ3Usage> FetchTenantIQ3UsageReport(ReportCriteria reportCriteria)
        {
            List<TenantIQ3Usage> tenantIQ3UsageList = new List<TenantIQ3Usage>();
            TenantIQ3Usage tenantIQ3Usage = null;

            List<MediaInfo> mediaInfos = null;
            MediaInfo mediaInfo = null;
            long index = 0;
            int counter = 0;
            try
            {
                using (var conn = DALHelper.GetConnectionMasterDB())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Reports.TenantIQ3UsageReport.TENANT_IQ3_USAGE_REPORT_FETCH_BY_USER;
                    cmd.Parameters.AddWithValue("@StartDate", reportCriteria.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", reportCriteria.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@IsAdmin", reportCriteria.IsAdmin);
                    cmd.Parameters.AddWithValue("@UserEmail", reportCriteria.UserEmail);
                    

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Tenant, "FetchTenantIQ3Usage", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                            tenantIQ3UsageList = new List<TenantIQ3Usage>();

                        while (dr.Read())
                        {
                            tenantIQ3Usage = new TenantIQ3Usage();
                            tenantIQ3Usage.RowNo = ++counter;
                            tenantIQ3Usage.TenantId = Convert.ToInt32(dr["Id"]);
                            tenantIQ3Usage.Name = Convert.ToString(dr["Name"]);
                            tenantIQ3Usage.CompanyName = Convert.ToString(dr["CompanyName"]);
                            tenantIQ3Usage.SoftwareVersion = Convert.ToString(dr["SoftwareVersion"]);
                            tenantIQ3Usage.CreatedDate = Convert.ToDateTime(dr["DateCreated"]);
                            tenantIQ3Usage.TenantAppType = TenantAppType.MT;

                            tenantIQ3UsageList.Add(tenantIQ3Usage);
                        }

                        dr.NextResult();
                        if (dr.HasRows)
                            mediaInfos = new List<MediaInfo>();
                        while (dr.Read())
                        {
                            mediaInfo = new MediaInfo();
                            mediaInfo.RowNo = ++index;
                            mediaInfo.TenantId = Convert.ToInt32(dr["TenantId"]);
                            mediaInfo.CallId = Convert.ToString(dr["CallId"]);
                            mediaInfo.StartTime = Convert.ToString(dr["StartTime"]);
                            mediaInfo.Duration = Convert.ToInt32(dr["Duration"]);
                            mediaInfo.ChannelNo = Convert.ToInt32(dr["ChannelNo"]);
                            mediaInfo.ChannelName = Convert.ToString(dr["ChannelName"]);
                            mediaInfo.FileName = Convert.ToString(dr["FileName"]);

                            mediaInfos.Add(mediaInfo);
                        }

                        foreach (var tenantIQ3UsageItem in tenantIQ3UsageList)
                        {
                            if (mediaInfos != null)
                            {
                                tenantIQ3UsageItem.NumberOfRecordings = Convert.ToInt32(mediaInfos.Where(s => s.TenantId == tenantIQ3UsageItem.TenantId).Count());
                                tenantIQ3UsageItem.TenantRecDurationInMS = Convert.ToInt64(mediaInfos.Where(s => s.TenantId == tenantIQ3UsageItem.TenantId).Sum(item => item.Duration));
                                tenantIQ3UsageItem.TenantRecDurationHHMMSS = convertMSToHHMMSS(tenantIQ3UsageItem.TenantRecDurationInMS);
                                tenantIQ3UsageItem.TenantMediaInfos = mediaInfos.Where(s => s.TenantId == tenantIQ3UsageItem.TenantId).ToList();
                            }
                            else {
                                tenantIQ3UsageItem.TenantRecDurationInMS = 0;
                                tenantIQ3UsageItem.TenantRecDurationHHMMSS = "0";
                                tenantIQ3UsageItem.TenantMediaInfos = new List<MediaInfo>();
                            }
                        }
                    }
                }
                return tenantIQ3UsageList;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public List<MediaInfo> FetchIQ3UsageDetailsByTenant(ReportCriteria reportCriteria)
        {
            List<MediaInfo> mediaInfos = null;
            MediaInfo mediaInfo = null;
            long index = 0;
            try
            {
                SqlConnection connection = null;
                switch (reportCriteria.TenantAppType)
                {
                    case TenantAppType.All:
                        connection = DALHelper.GetConnectionMasterDB();
                        break;
                    case TenantAppType.MT:
                        connection = DALHelper.GetConnectionMasterDB();
                        break;
                    case TenantAppType.MGO:
                        connection = DALHelper.GetConnectionMGOMasterDB();
                        break;
                    default:
                        break;
                }
                using (var conn = connection)
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Reports.TenantIQ3UsageReport.TENANT_IQ3_USAGE_DETAILS_FETCH_BY_TENANT;
                    cmd.Parameters.AddWithValue("@StartDate", reportCriteria.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", reportCriteria.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@TenantId", reportCriteria.TenantId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Tenant, "FetchIQ3UsageDetailsByTenant", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                            mediaInfos = new List<MediaInfo>();
                        while (dr.Read())
                        {
                            mediaInfo = new MediaInfo();
                            mediaInfo.RowNo = ++index;
                            mediaInfo.TenantId = Convert.ToInt32(dr["TenantId"]);
                            mediaInfo.CallId = Convert.ToString(dr["CallId"]).Trim();
                            mediaInfo.CallType = Convert.ToInt32(dr["CallType"]);
                            mediaInfo.StartTime = Convert.ToString(dr["StartTime"]);
                            mediaInfo.Duration = Convert.ToInt32(dr["Duration"]);
                            mediaInfo.ChannelNo = Convert.ToInt32(dr["Ext"]);
                            mediaInfo.ChannelName = Convert.ToString(dr["ExtName"]);
                            mediaInfo.FileName = Convert.ToString(dr["FileName"]);

                            mediaInfos.Add(mediaInfo);
                        }
                    }
                }
                return mediaInfos;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public int GetActiveTenantsCount()
        {
            int totalRecords = 0;
            try
            {
                using (var conn = DALHelper.GetConnectionMasterDB())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = 300;
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT COUNT(Id) AS TotalRecords FROM mtTenant WHERE IsDeleted = 0;";

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "GetActiveTenantsCount", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            totalRecords = Convert.ToInt32(dr["TotalRecords"]);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return totalRecords;
        }
        #endregion

        #region Tenant IQ3 Usage Report
        public List<TenantIQ3Usage> FetchMGOTenantIQ3UsageReport(ReportCriteria reportCriteria)
        {
            List<TenantIQ3Usage> tenantIQ3UsageList = new List<TenantIQ3Usage>();
            TenantIQ3Usage tenantIQ3Usage = null;

            List<MediaInfo> mediaInfos = null;
            MediaInfo mediaInfo = null;
            long index = 0;
            int counter = 0;
            try
            {
                using (var conn = DALHelper.GetConnectionMGOMasterDB())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Reports.TenantIQ3UsageReport.TENANT_IQ3_USAGE_REPORT_FETCH_BY_USER;
                    cmd.Parameters.AddWithValue("@StartDate", reportCriteria.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", reportCriteria.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@IsAdmin", reportCriteria.IsAdmin);
                    cmd.Parameters.AddWithValue("@UserEmail", reportCriteria.UserEmail);


                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Tenant, "FetchTenantIQ3Usage", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                            tenantIQ3UsageList = new List<TenantIQ3Usage>();

                        while (dr.Read())
                        {
                            tenantIQ3Usage = new TenantIQ3Usage();
                            tenantIQ3Usage.RowNo = ++counter;
                            tenantIQ3Usage.TenantId = Convert.ToInt32(dr["Id"]);
                            tenantIQ3Usage.Name = Convert.ToString(dr["Name"]);
                            tenantIQ3Usage.CompanyName = Convert.ToString(dr["CompanyName"]);
                            tenantIQ3Usage.SoftwareVersion = Convert.ToString(dr["SoftwareVersion"]);
                            tenantIQ3Usage.CreatedDate = Convert.ToDateTime(dr["DateCreated"]);
                            tenantIQ3Usage.TenantAppType = TenantAppType.MGO;

                            tenantIQ3UsageList.Add(tenantIQ3Usage);
                        }

                        dr.NextResult();
                        if (dr.HasRows)
                            mediaInfos = new List<MediaInfo>();
                        while (dr.Read())
                        {
                            mediaInfo = new MediaInfo();
                            mediaInfo.RowNo = ++index;
                            mediaInfo.TenantId = Convert.ToInt32(dr["TenantId"]);
                            mediaInfo.CallId = Convert.ToString(dr["CallId"]);
                            mediaInfo.StartTime = Convert.ToString(dr["StartTime"]);
                            mediaInfo.Duration = Convert.ToInt32(dr["Duration"]);
                            mediaInfo.ChannelNo = Convert.ToInt32(dr["ChannelNo"]);
                            mediaInfo.ChannelName = Convert.ToString(dr["ChannelName"]);
                            mediaInfo.FileName = Convert.ToString(dr["FileName"]);

                            mediaInfos.Add(mediaInfo);
                        }

                        foreach (var tenantIQ3UsageItem in tenantIQ3UsageList)
                        {
                            if (mediaInfos != null)
                            {
                                tenantIQ3UsageItem.NumberOfRecordings = Convert.ToInt32(mediaInfos.Where(s => s.TenantId == tenantIQ3UsageItem.TenantId).Count());
                                tenantIQ3UsageItem.TenantRecDurationInMS = Convert.ToInt64(mediaInfos.Where(s => s.TenantId == tenantIQ3UsageItem.TenantId).Sum(item => item.Duration));
                                tenantIQ3UsageItem.TenantRecDurationHHMMSS = convertMSToHHMMSS(tenantIQ3UsageItem.TenantRecDurationInMS);
                                tenantIQ3UsageItem.TenantMediaInfos = mediaInfos.Where(s => s.TenantId == tenantIQ3UsageItem.TenantId).ToList();
                            }
                            else
                            {
                                tenantIQ3UsageItem.TenantRecDurationInMS = 0;
                                tenantIQ3UsageItem.TenantRecDurationHHMMSS = "0";
                                tenantIQ3UsageItem.TenantMediaInfos = new List<MediaInfo>();
                            }
                        }
                    }
                }
                return tenantIQ3UsageList;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        #endregion

        private string convertMSToHHMMSS(long milliseconds)
        {
            TimeSpan ts = TimeSpan.FromMilliseconds(milliseconds);
            return ts.ToString(@"hh\:mm\:ss");
        }
    }
}