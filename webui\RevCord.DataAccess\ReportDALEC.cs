﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RevCord.DataContracts.ReportEntities;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.DataAccess.Util;
using RevCord.Util;
using RevCord.DataContracts;

namespace RevCord.DataAccess
{
    public class ReportDALEC
    {
        #region Evaluation Reports

        public List<RPTEvaluation> GetRPTEvaluationReportFromRecorder(Recorder recorder, string criteria, int userId)
        {
            List<RPTEvaluation> callEvaluations = new List<RPTEvaluation>();
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.RPT_CALLEVALUATION_GET_BY_SURVEYID;
                    cmd.Parameters.AddWithValue("@Criteria", criteria);
                    cmd.Parameters.AddWithValue("@UserId", userId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "GetRPTEvaluationReportFromRecorder", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {

                        callEvaluations = ORMapper.MapRPTEvaluations(dr);

                        return callEvaluations;
                    }
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public List<RPTEvaluationMain> GetRPTEvaluationMainReportFromRecorder(Recorder recorder, string criteria, int userId)
        {
            List<RPTEvaluationMain> callEvaluations = new List<RPTEvaluationMain>();
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.RPT_CALLEVALUATION_MAIN_GET_BY_SURVEYID;
                    cmd.Parameters.AddWithValue("@Criteria", criteria);
                    cmd.Parameters.AddWithValue("@UserId", userId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "GetRPTEvaluationMainReportFromRecorder", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {

                        callEvaluations = ORMapper.MapRPTEvaluationsMain(dr);

                        return callEvaluations;
                    }
                }
            }
            catch (Exception ex) { throw ex; }
        }

        #endregion
    }
}
