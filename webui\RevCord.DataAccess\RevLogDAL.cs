﻿using RevCord.DataContracts;
using RevCord.DataContracts.LogEntities;
using RevCord.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataAccess
{
    public class RevLogDAL
    {
        //private static int CMD_TIMEOUT = AppSettingsUtil.GetInt("commandTimeout", 60);
        public static int AddSuccessAuditLog(AuditAppType auditAppType, AuditLogCategory auditLogCategory, string originator, string functionName, int tenantId, int userId, string message, string query = "")
        {
            int appLogId = 0;
            try
            {
                using (var conn = DALHelper.GetRevLogDBConnection(tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.RevLog.REV_LOG_AddAuditLog;
                    cmd.Parameters.AddWithValue("@AppId", auditAppType);
                    cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@LogCategoryId", auditLogCategory);
                    cmd.Parameters.AddWithValue("@Originator", originator);
                    cmd.Parameters.AddWithValue("@FunctionName", functionName);
                    cmd.Parameters.AddWithValue("@Message", message);
                    cmd.Parameters.AddWithValue("@ExceptionCode", 0);
                    cmd.Parameters.AddWithValue("@StackTrace", string.Empty);
                    cmd.Parameters.AddWithValue("@Query", query);
                    conn.Open();
                    appLogId = Convert.ToInt32(cmd.ExecuteScalar());
                }
                return appLogId;
            }
            catch (Exception ex) { throw ex; }
        }

        public static int AddErrorAuditLog(AuditAppType auditAppType, AuditLogCategory auditLogCategory, string originator, string functionName, int tenantId, string message, string query = "", int userId=0)
        {
            int appLogId = 0;
            try
            {
                using (var conn = DALHelper.GetRevLogDBConnection(tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.RevLog.REV_LOG_AddAuditLog;
                    cmd.Parameters.AddWithValue("@AppId", auditAppType);
                    cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@LogCategoryId", auditLogCategory);
                    cmd.Parameters.AddWithValue("@Originator", originator);
                    cmd.Parameters.AddWithValue("@FunctionName", functionName);
                    cmd.Parameters.AddWithValue("@Message", message);
                    cmd.Parameters.AddWithValue("@ExceptionCode", 0);
                    cmd.Parameters.AddWithValue("@StackTrace", string.Empty);
                    cmd.Parameters.AddWithValue("@Query", query);
                    conn.Open();
                    appLogId = Convert.ToInt32(cmd.ExecuteScalar());
                }
                return appLogId;
            }
            catch (Exception ex) { throw ex; }
        }

        public static int AddExceptionAuditLog(AuditAppType auditAppType, AuditLogCategory auditLogCategory, string originator, string functionName, int tenantId, int userId, string message, string stackTrace, int exceptionCode)
        {
            int appLogId = 0;
            try
            {
                using (var conn = DALHelper.GetRevLogDBConnection(tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.RevLog.REV_LOG_AddAuditLog;
                    cmd.Parameters.AddWithValue("@AppId", auditAppType);
                    cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@LogCategoryId", auditLogCategory);
                    cmd.Parameters.AddWithValue("@Originator", originator);
                    cmd.Parameters.AddWithValue("@FunctionName", functionName);
                    cmd.Parameters.AddWithValue("@Message", message);
                    cmd.Parameters.AddWithValue("@ExceptionCode", exceptionCode);
                    cmd.Parameters.AddWithValue("@StackTrace", stackTrace);
                    cmd.Parameters.AddWithValue("@Query", string.Empty);
                    conn.Open();
                    appLogId = Convert.ToInt32(cmd.ExecuteScalar());
                }
                return appLogId;
            }
            catch (Exception ex) { throw ex; }
        }
        public static int AddSQLExceptionAuditLog(AuditAppType auditAppType, AuditLogCategory auditLogCategory, string originator, string functionName, int tenantId, int userId, string message, string stackTrace, int exceptionCode, string sql = "")
        {
            int appLogId = 0;
            try
            {
                using (var conn = DALHelper.GetRevLogDBConnection(tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.RevLog.REV_LOG_AddAuditLog;
                    cmd.Parameters.AddWithValue("@AppId", auditAppType);
                    cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@LogCategoryId", auditLogCategory);
                    cmd.Parameters.AddWithValue("@Originator", originator);
                    cmd.Parameters.AddWithValue("@FunctionName", functionName);
                    cmd.Parameters.AddWithValue("@Message", message);
                    cmd.Parameters.AddWithValue("@ExceptionCode", exceptionCode);
                    cmd.Parameters.AddWithValue("@StackTrace", stackTrace);
                    cmd.Parameters.AddWithValue("@Query", sql);
                    conn.Open();
                    appLogId = Convert.ToInt32(cmd.ExecuteScalar());
                }
                return appLogId;
            }
            catch (Exception ex) { throw ex; }
        }
    }
}
