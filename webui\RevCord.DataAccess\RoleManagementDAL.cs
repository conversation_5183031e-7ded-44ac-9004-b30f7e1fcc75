﻿using Microsoft.AspNet.SignalR.Messaging;
using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.RoleManagement;
using RevCord.DataContracts.UserManagement;
using RevCord.Util;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataAccess
{
    public class RoleManagementDAL
    {
        private int _tenantId;
        public RoleManagementDAL(int tenantId)
        {
            this._tenantId = tenantId;
        }
        public int AddRole(int roleType, string roleName, string Description, bool IsSystemRole)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = System.Data.CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.RoleManagement.ROLE_Add;
                    cmd.Parameters.AddWithValue("@RoleType", roleType);
                    cmd.Parameters.AddWithValue("@Name", roleName);
                    cmd.Parameters.AddWithValue("@Description", Description);
                    cmd.Parameters.AddWithValue("@IsSystemRole", IsSystemRole);
                    cmd.Parameters.AddWithValue("@CreatedDate", DateTime.Now);

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.RoleManagement, "AddRole", _tenantId));

                    int returnValue = Convert.ToInt32(cmd.ExecuteScalar());
                    conn.Close();
                    return returnValue;
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public List<Role> GetAllRoles()
        {
            List<Role> roles = null;
            Role role = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = System.Data.CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.RoleManagement.ROLE_GETALL;
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.RoleManagement, "GetAllRoles", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        roles = new List<Role>();
                        while (dr.Read())
                        {
                            role = new Role();
                            role.Id = (int)dr["Id"];
                            role.RoleType = Convert.ToInt32(dr["RoleType"]);
                            role.Name = Convert.ToString(dr["Name"]);
                            role.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                            role.Description = Convert.ToString(dr["Description"]);
                            role.IsSystemRole = Convert.ToBoolean(dr["IsSystemRole"]);
                            role.PermissionsCount = Convert.ToInt32(dr["PermissionsCount"]);
                            role.AllocatedCount = Convert.ToInt32(dr["AllocatedCount"]);
                            
                            roles.Add(role);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return roles;
        }
        public Role GetRoleById(int rID)
        {
            Role role = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = System.Data.CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.RoleManagement.ROLE_GETROLE_BYID;
                    cmd.Parameters.AddWithValue("@Id", rID);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.RoleManagement, "GetRoleById", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {

                        while (dr.Read())
                        {
                            role = new Role();
                            role.Id = (int)dr["Id"];
                            role.Name = Convert.ToString(dr["Name"]);
                            role.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                            role.Description = Convert.ToString(dr["Description"]);
                            role.IsSystemRole = Convert.ToBoolean(dr["IsSystemRole"]);

                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return role;
        }
        public bool UpdateRole(Role role)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = System.Data.CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.RoleManagement.ROLE_EDITROLE_BYID;
                    cmd.Parameters.AddWithValue("@Id", role.Id);
                    cmd.Parameters.AddWithValue("@Name", role.Name);
                    cmd.Parameters.AddWithValue("@Description", role.Description);

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.RoleManagement, "EditRoleById", _tenantId));

                    int returnValue = Convert.ToInt32(cmd.ExecuteScalar());
                    conn.Close();
                    return returnValue > 0 ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }

        }
        public bool DeleteRole(int rID)
        {

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = System.Data.CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.RoleManagement.ROLE_DELETEROLE_BYID;
                    cmd.Parameters.AddWithValue("@Id", rID);

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.RoleManagement, "DeleteRole", _tenantId));

                    int returnValue = Convert.ToInt32(cmd.ExecuteScalar());
                    conn.Close();
                    return returnValue > 0 ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }

        }

        public List<MMSPermission> GetRolePermissions(int roleId)
        {
            List<MMSPermission> permissions = null;
            MMSPermission permission = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = System.Data.CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.RoleManagement.RolePERMISSION_GETPermissionListByID;
                    cmd.Parameters.AddWithValue("@RoleId", roleId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.RoleManagement, "GetPermissionsByRoleId", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        permissions = new List<MMSPermission>();
                        while (dr.Read())
                        {
                            permission = new MMSPermission();
                            permission.Id = (int)dr["Id"];
                            permission.Title = Convert.ToString(dr["Title"]);
                            permission.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                            permissions.Add(permission);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return permissions;
        }

        public bool UpdateRolePermissions(int rId, int pId, bool isDeleted)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = System.Data.CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.RoleManagement.UPDATE_ROLE_PERMISSIONS;
                    
                    cmd.Parameters.AddWithValue("@RoleId", rId);
                    cmd.Parameters.AddWithValue("@PermissionId", pId);
                    cmd.Parameters.AddWithValue("@CreatedDate", DateTime.Now);
                    cmd.Parameters.AddWithValue("@IsDeleted", isDeleted);

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.RoleManagement, "UpdateRolePermissions", _tenantId));

                    int returnValue = Convert.ToInt32(cmd.ExecuteNonQuery());
                    conn.Close();
                    return returnValue > 0 ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }

        }

        public List<RoleChannelPermission> GetChannelBasedRolePermissions(int roleId)
        {
            List<RoleChannelPermission> roleChannelPermissions = null;
            RoleChannelPermission roleChannelPermission = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = System.Data.CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.RoleManagement.RolePERMISSION_GETCHANNELPermissionListByID;
                    cmd.Parameters.AddWithValue("@RoleId", roleId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.RoleManagement, "GetPermissionsByRoleId", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        roleChannelPermissions = new List<RoleChannelPermission>();
                        while (dr.Read()) {
                            roleChannelPermission = new RoleChannelPermission();
                            roleChannelPermission.Id = (int)dr["Id"];
                            roleChannelPermission.RoleId = Convert.ToInt32(dr["RoleId"]);
                            roleChannelPermission.PermissionId = Convert.ToInt32(dr["PermissionId"]);
                            roleChannelPermission.ChannelNum = Convert.ToInt32(dr["ChannelNum"]);
                            
                            roleChannelPermission.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                            roleChannelPermission.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                            roleChannelPermissions.Add(roleChannelPermission);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return roleChannelPermissions;
        }

        public bool UpdateChannelBasedRolePermissions(int roleId, int permissionId, string channelsToAllocate, string channelsToDeAllocate)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = System.Data.CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.RoleManagement.RolePERMISSION_UPDATE_CHANNEL_BASED;

                    cmd.Parameters.AddWithValue("@RoleId", roleId);
                    cmd.Parameters.AddWithValue("@PermissionId", permissionId);
                    cmd.Parameters.AddWithValue("@ChannelsToAllocate", channelsToAllocate);
                    cmd.Parameters.AddWithValue("@ChannelsToDeAllocate", channelsToDeAllocate);

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.RoleManagement, "UpdateChannelBasedRolePermissions", _tenantId));

                    int returnValue = Convert.ToInt32(cmd.ExecuteNonQuery());
                    conn.Close();
                    return returnValue > 0 ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }

        }

        public List<User> GetUsersByRoleId(int roleId)
        {
            List<User> users = new List<User>();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = System.Data.CommandType.Text;
                    cmd.CommandText = "SELECT * FROM t_Account WHERE RoleId = @RoleId";
                    cmd.Parameters.AddWithValue("@RoleId", roleId);

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.RoleManagement, "GetUserCountByRoleId", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        
                        while (dr.Read())
                        {
                            var user = new User();
                            user.UserID = Convert.ToString(dr["UserID"]); ;
                            user.UserName = Convert.ToString(dr["UserName"]);
                            users.Add(user);
                        }
                    }
                    conn.Close();
                    return users;
                }
            }
            catch (Exception ex) { throw ex; }
        }
    }
}