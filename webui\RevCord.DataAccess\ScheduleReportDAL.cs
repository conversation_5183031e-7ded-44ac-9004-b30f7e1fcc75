﻿using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.ScheduleReportEntities;
using RevCord.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataAccess
{
    public class ScheduleReportDAL
    {
        private int _tenantId;

        public ScheduleReportDAL(int tenantId)
        {
            _tenantId = tenantId;
        }

        public List<ScheduleReport> GetAllScheduleReport(long UserId)
        {
            List<ScheduleReport> objScheduleReportList = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.ScheduleReport.SCHEDULE_REPORT_GET_ByUserId;
                    cmd.Parameters.AddWithValue("@UserId", UserId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.ScheduleReport, "GetAllScheduleReport", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        objScheduleReportList = ORMapper.MapAllScheduleReport(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return objScheduleReportList;
        }

        public List<string> GetAllReportRecipient(int ScheduleReportId)
        {
            List<string> objReportRecipientList = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.ScheduleReport.SCHEDULE_REPORT_GETBYID;
                    cmd.Parameters.AddWithValue("@ScheduleReportId", ScheduleReportId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.ScheduleReport, "GetAllReportRecipient", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.Read())
                        {
                            objReportRecipientList = GetAllReportRecipient(Convert.ToString(dr["Recipients"]));
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return objReportRecipientList;
        }

        public ScheduleReport GetScheduleReport(int ScheduleReportId)
        {
            ScheduleReport objScheduleReport = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.ScheduleReport.SCHEDULE_REPORT_GETBYID;
                    cmd.Parameters.AddWithValue("@ScheduleReportId", ScheduleReportId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.ScheduleReport, "GetScheduleReport", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        objScheduleReport = ORMapper.MapScheduleReport(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return objScheduleReport;
        }

        public List<ScheduleReport> GetScheduleReportToBeSend(long UserId)
        {
            List<ScheduleReport> objScheduleReportList = null;
            ScheduleReport objScheduleReport = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.ScheduleReport.SCHEDULE_REPORT_GET_ByUserId_ReportToBeSend;
                    cmd.Parameters.AddWithValue("@UserId", UserId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.ScheduleReport, "GetScheduleReportToBeSend", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        objScheduleReportList = ORMapper.MapAllScheduleReport(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return objScheduleReportList;
        }

        public int SaveScheduleReport(ScheduleReport objScheduleReport)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();

                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.ScheduleReport.SCHEDULE_REPORT_INSERT;
                    cmd.Parameters.AddWithValue("@ReportName", objScheduleReport.ReportName);
                    cmd.Parameters.AddWithValue("@UserId", objScheduleReport.UserId);
                    cmd.Parameters.AddWithValue("@SaveReportId", objScheduleReport.SaveReportId);
                    cmd.Parameters.AddWithValue("@ExportType", objScheduleReport.ExportType);
                    cmd.Parameters.AddWithValue("@RecurrenceType", objScheduleReport.objRecurrence.objRecurrenceType);
                    if (objScheduleReport.objRecurrence.objRecurrenceData != null)
                    {
                        cmd.Parameters.AddWithValue("@RecurrenceData", objScheduleReport.objRecurrence.objRecurrenceData.ToString());
                    }
                    cmd.Parameters.AddWithValue("@NextReportOn", objScheduleReport.GetNextReportOn());
                    if (objScheduleReport.Recipients != null)
                    {
                        cmd.Parameters.AddWithValue("@Recipients", string.Join(",", objScheduleReport.Recipients));
                    }
                    cmd.Parameters.AddWithValue("@CreatedOn", DateTime.Now);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.ScheduleReport, "SaveScheduleReport", _tenantId));
                    return Convert.ToInt32(cmd.ExecuteScalar());
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public int UpdateScheduleReport(ScheduleReport objScheduleReport)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();

                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.ScheduleReport.SCHEDULE_REPORT_UPDATE;
                    cmd.Parameters.AddWithValue("@Id", objScheduleReport.Id);
                    cmd.Parameters.AddWithValue("@ReportName", objScheduleReport.ReportName);
                    cmd.Parameters.AddWithValue("@ExportType", objScheduleReport.ExportType);
                    cmd.Parameters.AddWithValue("@RecurrenceType", objScheduleReport.objRecurrence.objRecurrenceType);
                    if (objScheduleReport.objRecurrence.objRecurrenceData != null)
                    {
                        cmd.Parameters.AddWithValue("@RecurrenceData", objScheduleReport.objRecurrence.objRecurrenceData.ToString());
                    }
                    cmd.Parameters.AddWithValue("@NextReportOn", objScheduleReport.GetNextReportOn());
                    if (objScheduleReport.Recipients != null)
                    {
                        cmd.Parameters.AddWithValue("@Recipients", string.Join(",", objScheduleReport.Recipients));
                    }
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.ScheduleReport, "UpdateScheduleReport", _tenantId));

                    return Convert.ToInt32(cmd.ExecuteScalar());
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public int UpdateScheduleReportAfterReportSend(ScheduleReport objScheduleReport)
        {
            try
            {
                objScheduleReport.SendCounter += 1;
                objScheduleReport.SetLastReportOn(objScheduleReport.NextReportOn);

                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();

                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.ScheduleReport.SCHEDULE_REPORT_UPDATE_AFTER_ReportSend;
                    cmd.Parameters.AddWithValue("@Id", objScheduleReport.Id);
                    cmd.Parameters.AddWithValue("@SendCounter", objScheduleReport.SendCounter);
                    cmd.Parameters.AddWithValue("@LastReportOn", objScheduleReport.LastReportOn);
                    cmd.Parameters.AddWithValue("@NextReportOn", objScheduleReport.GetNextReportOn(objScheduleReport.LastReportOn, objScheduleReport.SendCounter));
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.ScheduleReport, "UpdateScheduleReportAfterReportSend", _tenantId));

                    return Convert.ToInt32(cmd.ExecuteScalar());
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public int RemoveScheduleReport(int ScheduleReportId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();

                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.ScheduleReport.SCHEDULE_REPORT_REMOVE;
                    cmd.Parameters.AddWithValue("@Id", ScheduleReportId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.ScheduleReport, "RemoveScheduleReport", _tenantId));

                    return Convert.ToInt32(cmd.ExecuteScalar());
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public static List<string> GetAllReportRecipient(string Recipients)
        {
            List<string> RecipientList = null;

            if (!string.IsNullOrEmpty(Recipients))
            {
                RecipientList = new List<string>();

                foreach (var Recipient in Recipients.Split(','))
                {
                    RecipientList.Add(Recipient);
                }
            }

            return RecipientList;
        }
    }
}
