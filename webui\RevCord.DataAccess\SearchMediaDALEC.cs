﻿using RevCord.DataContracts;
using RevCord.DataContracts.Criteria;
using RevCord.DataContracts.DTO;
using RevCord.DataContracts.MessageBase;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataAccess
{
    public class SearchMediaDALEC
    {
        private static int CMD_TIMEOUT = AppSettingsUtil.GetInt("commandTimeout", 60);
        private int _tenantId;
        public SearchMediaDALEC(int tenantId)
        {
            this._tenantId = tenantId;
        }

        public async Task<DALMediaResponse> GetDefaultSearchResultsDTOEC(int pageSize, int pageIndex, string duration, string criteria, CallCriteria callCriteria, string customSpParam, Recorder recorder)
        {
            List<MediaInfo> calls = new List<MediaInfo>();
            int totalPages = 0;
            long totalRecords = 0;
            var sw = new Stopwatch();

            using (var conn = DALHelper.GetConnection(_tenantId))
            {
                using (var cmd = new SqlCommand())
                {
                    cmd.Connection = conn;

                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_MEDIA_PRIMARY_DB;//"vr_Call_SearchPrimarySM";

                    cmd.Parameters.AddWithValue("@StartDate", callCriteria.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", callCriteria.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@StartTime", callCriteria.StartTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@EndTime", callCriteria.EndTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria);
                    cmd.Parameters.AddWithValue("@CustomText", customSpParam);

                    cmd.Parameters.AddWithValue("@IsGlobalSearch", callCriteria.SearchType == SearchType.Global ? true : false);

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;
                    try
                    {
                        long index = 0;
                        await conn.OpenAsync();
                        sw.Start();
                        using (var dr = await cmd.ExecuteReaderAsync())
                        {
                            while (await dr.ReadAsync())
                            {
                                var call = new MediaInfo
                                {
                                    RowNo = ++index, //(long)dr["RowNo"],
                                    CallId = Convert.ToString(dr["CallID"]),
                                    CallType = Convert.ToInt32(dr["CallType"]),
                                    ChannelName = Convert.ToString(dr["ExtName"]),
                                    StartTime = Convert.ToString(dr["StartTime"]),
                                    Duration = Convert.ToInt32(dr["Duration"]),
                                    UniqueId = Convert.ToInt64(dr["UniqueId"]),
                                    FileName = Convert.ToString(dr["FileName"]),
                                    GroupName = Convert.ToString(dr["GroupName"]),
                                    RecorderId = recorder.Id,
                                    ECRecName = recorder.Name,
                                    ChannelNo = Convert.ToInt32(dr["Ext"]),
                                    CustName = Convert.ToString(dr["CustName"]),
                                    ANI = Convert.ToString(dr["ANI"]),
                                    CallerId = Convert.ToString(dr["CallerID"]),
                                    CalledId = Convert.ToString(dr["CalledID"]),
                                    CustInfo1 = Convert.ToString(dr["Tag1"]),
                                    CustInfo2 = Convert.ToString(dr["Tag2"]),
                                    CustInfo3 = Convert.ToString(dr["Tag3"]),
                                    //CustInfo4 = Convert.ToString(dr["Tag4"]),
                                    CustInfo5 = Convert.ToString(dr["Tag4"]),
                                    Tag6 = Convert.ToString(dr["Tag6"]),
                                    Tag7 = Convert.ToString(dr["Tag7"]),
                                    Tag8 = Convert.ToString(dr["Tag8"]),
                                    Tag9 = Convert.ToString(dr["Tag9"]),
                                    Tag10 = Convert.ToString(dr["Tag10"]),
                                    Tag11 = Convert.ToString(dr["Tag11"]),
                                    Tag12 = Convert.ToString(dr["Tag12"]),
                                    Tag13 = Convert.ToString(dr["Tag13"]),
                                    Tag14 = Convert.ToString(dr["Tag14"]),
                                    Tag15 = Convert.ToString(dr["Tag15"]),
                                    Tag16 = Convert.ToString(dr["Tag16"]),
                                    ANINumber = Convert.ToString(dr["ANI_PH"]),
                                    ANIName = Convert.ToString(dr["ANI_NAME"]),
                                    ANIDetails = Convert.ToString(dr["ANI_DETAILS"]),
                                    Comment = Convert.ToString(dr["CALL_COMMENT"]),
                                    UserId = Convert.ToInt32(dr["UserNum"]),
                                    RetainCall = Convert.ToBoolean(dr["RetainValue"]),
                                    TranscriptId = Convert.ToString(dr["TranscriptionId"]),
                                    Transcription = Convert.ToString(dr["Transcription"]),
                                    BookmarkXML = Convert.ToString(dr["BookMarkXML"]),
                                    ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]),
                                    IsRevCell = Convert.ToBoolean(dr["IsRevCell"]),

                                    IsPictureEvent = Convert.ToBoolean(dr["IsPictureEvent"]),
                                    IsVirtualInspection = Convert.ToBoolean(dr["IsVirtualInspection"]),
                                    IsRevView = Convert.ToBoolean(dr["IsRevView"]),
                                    EventName = Convert.ToString(dr["InterviewId"]),
                                    EventNameIQ3 = Convert.ToString(dr["Interviewee"]),
                                    Interview_DateTime = Convert.ToString(dr["DateTime"]),
                                    Interview_Interviewer = Convert.ToString(dr["Interviewer"]),
                                    Interview_Interviewee = Convert.ToString(dr["Interviewee"]),
                                    Interview_MdInterviewee = Convert.ToString(dr["MDInterviewee"]),
                                    //Interview_InterviewId = Convert.ToString(dr["InterviewId"]),
                                    Interview_GPS = Convert.ToString(dr["GPS"]),
                                    Interview_Notes = Convert.ToString(dr["Notes"]),
                                    ErrorInMuxProcess = 0, //Convert.ToInt32(dr["ErrorInMuxProcess"]),
                                    
                                };

                                calls.Add(call);
                            }
                        }
                        sw.Stop();
                        Debug.WriteLine(sw.Elapsed.ToString(@"hh\:mm\:ss"));
                    }
                    catch (Exception ex)
                    {
                        throw ex;
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            return new DALMediaResponse
            {
                Acknowledge = AcknowledgeType.Success,
                ListOfMedias = calls,
                TotalPages = totalPages,
                TotalRecords = totalRecords,
                Message = criteria,
                ProcessingTime = sw.Elapsed.ToString(@"hh\:mm\:ss"),
            };
        }

        public async Task<DALMediaResponse> GetAdvanceSearchResultsDTO(int pageSize, int pageIndex, string duration, string criteria, CallCriteria callCriteria, string customSpParam, Recorder recorder)
        {
            List<MediaInfo> calls = new List<MediaInfo>();
            int totalPages = 0;
            long totalRecords = 0;
            var sw = new Stopwatch();

            using (var conn = DALHelper.GetConnection(_tenantId))
            {
                using (var cmd = new SqlCommand())
                {
                    cmd.Connection = conn;

                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_MEDIA_PRIMARY_DB;//"vr_Call_SearchPrimarySM";

                    cmd.Parameters.AddWithValue("@StartDate", callCriteria.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", callCriteria.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@StartTime", callCriteria.StartTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@EndTime", callCriteria.EndTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria);
                    cmd.Parameters.AddWithValue("@CustomText", customSpParam);

                    cmd.Parameters.AddWithValue("@IsGlobalSearch", callCriteria.SearchType == SearchType.Global ? true : false);

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    try
                    {
                        sw.Start();
                        long index = 0;
                        await conn.OpenAsync();
                        using (var dr = await cmd.ExecuteReaderAsync())
                        {
                            while (await dr.ReadAsync())
                            {
                                var call = new MediaInfo
                                {
                                    RowNo = ++index, //(long)dr["RowNo"],
                                    CallId = Convert.ToString(dr["CallID"]),
                                    CallType = Convert.ToInt32(dr["CallType"]),
                                    ChannelName = Convert.ToString(dr["ExtName"]),
                                    StartTime = Convert.ToString(dr["StartTime"]),
                                    Duration = Convert.ToInt32(dr["Duration"]),
                                    UniqueId = Convert.ToInt64(dr["UniqueId"]),
                                    FileName = Convert.ToString(dr["FileName"]),
                                    GroupName = Convert.ToString(dr["GroupName"]),
                                    RecorderId = recorder.Id,
                                    ECRecName = recorder.Name,
                                    ChannelNo = Convert.ToInt32(dr["Ext"]),
                                    CustName = Convert.ToString(dr["CustName"]),
                                    ANI = Convert.ToString(dr["ANI"]),
                                    CallerId = Convert.ToString(dr["CallerID"]),
                                    CalledId = Convert.ToString(dr["CalledID"]),
                                    CustInfo1 = Convert.ToString(dr["Tag1"]),
                                    CustInfo2 = Convert.ToString(dr["Tag2"]),
                                    CustInfo3 = Convert.ToString(dr["Tag3"]),
                                    //CustInfo4 = Convert.ToString(dr["Tag4"]),
                                    CustInfo5 = Convert.ToString(dr["Tag4"]),
                                    Tag6 = Convert.ToString(dr["Tag6"]),
                                    Tag7 = Convert.ToString(dr["Tag7"]),
                                    Tag8 = Convert.ToString(dr["Tag8"]),
                                    Tag9 = Convert.ToString(dr["Tag9"]),
                                    Tag10 = Convert.ToString(dr["Tag10"]),
                                    Tag11 = Convert.ToString(dr["Tag11"]),
                                    Tag12 = Convert.ToString(dr["Tag12"]),
                                    Tag13 = Convert.ToString(dr["Tag13"]),
                                    Tag14 = Convert.ToString(dr["Tag14"]),
                                    Tag15 = Convert.ToString(dr["Tag15"]),
                                    Tag16 = Convert.ToString(dr["Tag16"]),
                                    ANINumber = Convert.ToString(dr["ANI_PH"]),
                                    ANIName = Convert.ToString(dr["ANI_NAME"]),
                                    ANIDetails = Convert.ToString(dr["ANI_DETAILS"]),
                                    Comment = Convert.ToString(dr["CALL_COMMENT"]),
                                    UserId = Convert.ToInt32(dr["UserNum"]),
                                    RetainCall = Convert.ToBoolean(dr["RetainValue"]),
                                    TranscriptId = Convert.ToString(dr["TranscriptionId"]),
                                    Transcription = Convert.ToString(dr["Transcription"]),
                                    BookmarkXML = Convert.ToString(dr["BookMarkXML"]),
                                    ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]),
                                    IsRevCell = Convert.ToBoolean(dr["IsRevCell"]),

                                    IsPictureEvent = Convert.ToBoolean(dr["IsPictureEvent"]),
                                    IsVirtualInspection = Convert.ToBoolean(dr["IsVirtualInspection"]),
                                    IsRevView = Convert.ToBoolean(dr["IsRevView"]),
                                    EventName = Convert.ToString(dr["InterviewId"]),
                                    EventNameIQ3 = Convert.ToString(dr["Interviewee"]),
                                    Interview_DateTime = Convert.ToString(dr["DateTime"]),
                                    Interview_Interviewer = Convert.ToString(dr["Interviewer"]),
                                    Interview_Interviewee = Convert.ToString(dr["Interviewee"]),
                                    Interview_MdInterviewee = Convert.ToString(dr["MDInterviewee"]),
                                    //Interview_InterviewId = Convert.ToString(dr["InterviewId"]),
                                    Interview_GPS = Convert.ToString(dr["GPS"]),
                                    Interview_Notes = Convert.ToString(dr["Notes"]),
                                    ErrorInMuxProcess = 0,
                                };
                                calls.Add(call);
                            }
                        }
                        sw.Stop();
                        Debug.WriteLine(sw.Elapsed.ToString(@"hh\:mm\:ss"));
                    }
                    catch (Exception ex)
                    {
                        throw ex;
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            return new DALMediaResponse
            {
                Acknowledge = AcknowledgeType.Success,
                ListOfMedias = calls,
                TotalPages = totalPages,
                TotalRecords = totalRecords,
                Message = criteria,
                ProcessingTime = sw.Elapsed.ToString(@"hh\:mm\:ss"),
            };
        }

        public async Task<DALMediaResponse> PerformSearchChainedDBs(int pageSize, int pageIndex, string duration, string criteria, CallCriteria callCriteria, string customSpParam, Recorder recorder)
        {
            List<MediaInfo> calls = new List<MediaInfo>();
            int totalPages = 0;
            long totalRecords = 0;
            var sw = new Stopwatch();

            using (var conn = DALHelper.GetConnection(_tenantId))
            {
                using (var cmd = new SqlCommand())
                {
                    cmd.Connection = conn;

                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = pageIndex == 1 ? DBConstants.VoiceRec.CALLS_SEARCH_MEDIA_SECONDARY_DBS : DBConstants.VoiceRec.CALLS_SEARCH_MEDIA_SECONDARY_DBS_2N;

                    cmd.Parameters.AddWithValue("@StartDate", callCriteria.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", callCriteria.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@StartTime", callCriteria.StartTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@EndTime", callCriteria.EndTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria);
                    cmd.Parameters.AddWithValue("@CustomText", customSpParam);

                    cmd.Parameters.AddWithValue("@IsGlobalSearch", callCriteria.SearchType == SearchType.Global ? true : false);

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    try
                    {
                        sw.Start();
                        await conn.OpenAsync();
                        using (var dr = await cmd.ExecuteReaderAsync())
                        {
                            while (await dr.ReadAsync())
                            {
                                var call = new MediaInfo
                                {
                                    RowNo = (long)dr["RowNo"],
                                    CallId = Convert.ToString(dr["CallID"]),
                                    CallType = Convert.ToInt32(dr["CallType"]),
                                    ChannelName = Convert.ToString(dr["ExtName"]),
                                    StartTime = Convert.ToString(dr["StartTime"]),
                                    Duration = Convert.ToInt32(dr["Duration"]),
                                    UniqueId = Convert.ToInt64(dr["UniqueId"]),
                                    FileName = Convert.ToString(dr["FileName"]),
                                    GroupName = Convert.ToString(dr["GroupName"]),
                                    RecorderId = recorder.Id,
                                    ECRecName = recorder.Name,
                                    ChannelNo = Convert.ToInt32(dr["Ext"]),
                                    CustName = Convert.ToString(dr["CustName"]),
                                    ANI = Convert.ToString(dr["ANI"]),
                                    CallerId = Convert.ToString(dr["CallerID"]),
                                    CalledId = Convert.ToString(dr["CalledID"]),
                                    CustInfo1 = Convert.ToString(dr["Tag1"]),
                                    CustInfo2 = Convert.ToString(dr["Tag2"]),
                                    CustInfo3 = Convert.ToString(dr["Tag3"]),
                                    //CustInfo4 = Convert.ToString(dr["Tag4"]),
                                    CustInfo5 = Convert.ToString(dr["Tag4"]),
                                    Tag6 = Convert.ToString(dr["Tag6"]),
                                    Tag7 = Convert.ToString(dr["Tag7"]),
                                    Tag8 = Convert.ToString(dr["Tag8"]),
                                    Tag9 = Convert.ToString(dr["Tag9"]),
                                    Tag10 = Convert.ToString(dr["Tag10"]),
                                    Tag11 = Convert.ToString(dr["Tag11"]),
                                    Tag12 = Convert.ToString(dr["Tag12"]),
                                    Tag13 = Convert.ToString(dr["Tag13"]),
                                    Tag14 = Convert.ToString(dr["Tag14"]),
                                    Tag15 = Convert.ToString(dr["Tag15"]),
                                    Tag16 = Convert.ToString(dr["Tag16"]),
                                    ANINumber = Convert.ToString(dr["ANI_PH"]),
                                    ANIName = Convert.ToString(dr["ANI_NAME"]),
                                    ANIDetails = Convert.ToString(dr["ANI_DETAILS"]),
                                    Comment = Convert.ToString(dr["CALL_COMMENT"]),
                                    UserId = Convert.ToInt32(dr["UserNum"]),
                                    RetainCall = Convert.ToBoolean(dr["RetainValue"]),
                                    TranscriptId = Convert.ToString(dr["TranscriptionId"]),
                                    Transcription = Convert.ToString(dr["Transcription"]),
                                    BookmarkXML = Convert.ToString(dr["BookMarkXML"]),
                                    ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]),
                                    IsRevCell = Convert.ToBoolean(dr["IsRevCell"]),

                                    IsPictureEvent = Convert.ToBoolean(dr["IsPictureEvent"]),
                                    IsVirtualInspection = Convert.ToBoolean(dr["IsVirtualInspection"]),
                                    IsRevView = Convert.ToBoolean(dr["IsRevView"]),
                                    EventName = Convert.ToString(dr["InterviewId"]),
                                    EventNameIQ3 = Convert.ToString(dr["Interviewee"]),
                                    Interview_DateTime = Convert.ToString(dr["DateTime"]),
                                    Interview_Interviewer = Convert.ToString(dr["Interviewer"]),
                                    Interview_Interviewee = Convert.ToString(dr["Interviewee"]),
                                    Interview_MdInterviewee = Convert.ToString(dr["MDInterviewee"]),
                                    //Interview_InterviewId = Convert.ToString(dr["InterviewId"]),
                                    Interview_GPS = Convert.ToString(dr["GPS"]),
                                    Interview_Notes = Convert.ToString(dr["Notes"]),
                                    ErrorInMuxProcess = 0,
                                };
                                calls.Add(call);
                            }
                        }
                        sw.Stop();
                        Debug.WriteLine(sw.Elapsed.ToString(@"hh\:mm\:ss"));
                    }
                    catch (Exception ex)
                    {
                        throw ex;
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            return new DALMediaResponse
            {
                Acknowledge = AcknowledgeType.Success,
                ListOfMedias = calls,
                TotalPages = totalPages,
                TotalRecords = totalRecords,
                Message = criteria,
                ProcessingTime = sw.Elapsed.ToString(@"hh\:mm\:ss"),
            };
        }
    }
}
