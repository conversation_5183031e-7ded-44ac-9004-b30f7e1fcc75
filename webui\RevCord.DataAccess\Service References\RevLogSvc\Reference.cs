﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace RevCord.DataAccess.RevLogSvc {
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.ServiceContractAttribute(ConfigurationName="RevLogSvc.IRevLogSvc")]
    public interface IRevLogSvc {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevLogSvc/AddSuccessAuditLog", ReplyAction="http://tempuri.org/IRevLogSvc/AddSuccessAuditLogResponse")]
        int AddSuccessAuditLog(RevCord.DataContracts.AuditAppType auditAppType, RevCord.DataContracts.AuditLogCategory auditLogCategory, string originator, string functionName, int tenantId, int userId, string message, string query);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevLogSvc/AddSuccessAuditLog", ReplyAction="http://tempuri.org/IRevLogSvc/AddSuccessAuditLogResponse")]
        System.Threading.Tasks.Task<int> AddSuccessAuditLogAsync(RevCord.DataContracts.AuditAppType auditAppType, RevCord.DataContracts.AuditLogCategory auditLogCategory, string originator, string functionName, int tenantId, int userId, string message, string query);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevLogSvc/AddErrorAuditLog", ReplyAction="http://tempuri.org/IRevLogSvc/AddErrorAuditLogResponse")]
        int AddErrorAuditLog(RevCord.DataContracts.AuditAppType auditAppType, RevCord.DataContracts.AuditLogCategory auditLogCategory, string originator, string functionName, int tenantId, string message, string query, int userId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevLogSvc/AddErrorAuditLog", ReplyAction="http://tempuri.org/IRevLogSvc/AddErrorAuditLogResponse")]
        System.Threading.Tasks.Task<int> AddErrorAuditLogAsync(RevCord.DataContracts.AuditAppType auditAppType, RevCord.DataContracts.AuditLogCategory auditLogCategory, string originator, string functionName, int tenantId, string message, string query, int userId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevLogSvc/AddExceptionAuditLog", ReplyAction="http://tempuri.org/IRevLogSvc/AddExceptionAuditLogResponse")]
        int AddExceptionAuditLog(RevCord.DataContracts.AuditAppType auditAppType, RevCord.DataContracts.AuditLogCategory auditLogCategory, string originator, string functionName, int tenantId, int userId, string message, string stackTrace, int exceptionCode);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevLogSvc/AddExceptionAuditLog", ReplyAction="http://tempuri.org/IRevLogSvc/AddExceptionAuditLogResponse")]
        System.Threading.Tasks.Task<int> AddExceptionAuditLogAsync(RevCord.DataContracts.AuditAppType auditAppType, RevCord.DataContracts.AuditLogCategory auditLogCategory, string originator, string functionName, int tenantId, int userId, string message, string stackTrace, int exceptionCode);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevLogSvc/AddSQLExceptionAuditLog", ReplyAction="http://tempuri.org/IRevLogSvc/AddSQLExceptionAuditLogResponse")]
        int AddSQLExceptionAuditLog(RevCord.DataContracts.AuditAppType auditAppType, RevCord.DataContracts.AuditLogCategory auditLogCategory, string originator, string functionName, int tenantId, int userId, string message, string stackTrace, int exceptionCode, string query);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IRevLogSvc/AddSQLExceptionAuditLog", ReplyAction="http://tempuri.org/IRevLogSvc/AddSQLExceptionAuditLogResponse")]
        System.Threading.Tasks.Task<int> AddSQLExceptionAuditLogAsync(RevCord.DataContracts.AuditAppType auditAppType, RevCord.DataContracts.AuditLogCategory auditLogCategory, string originator, string functionName, int tenantId, int userId, string message, string stackTrace, int exceptionCode, string query);
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public interface IRevLogSvcChannel : RevCord.DataAccess.RevLogSvc.IRevLogSvc, System.ServiceModel.IClientChannel {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public partial class RevLogSvcClient : System.ServiceModel.ClientBase<RevCord.DataAccess.RevLogSvc.IRevLogSvc>, RevCord.DataAccess.RevLogSvc.IRevLogSvc {
        
        public RevLogSvcClient() {
        }
        
        public RevLogSvcClient(string endpointConfigurationName) : 
                base(endpointConfigurationName) {
        }
        
        public RevLogSvcClient(string endpointConfigurationName, string remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public RevLogSvcClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public RevLogSvcClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress) {
        }
        
        public int AddSuccessAuditLog(RevCord.DataContracts.AuditAppType auditAppType, RevCord.DataContracts.AuditLogCategory auditLogCategory, string originator, string functionName, int tenantId, int userId, string message, string query) {
            return base.Channel.AddSuccessAuditLog(auditAppType, auditLogCategory, originator, functionName, tenantId, userId, message, query);
        }
        
        public System.Threading.Tasks.Task<int> AddSuccessAuditLogAsync(RevCord.DataContracts.AuditAppType auditAppType, RevCord.DataContracts.AuditLogCategory auditLogCategory, string originator, string functionName, int tenantId, int userId, string message, string query) {
            return base.Channel.AddSuccessAuditLogAsync(auditAppType, auditLogCategory, originator, functionName, tenantId, userId, message, query);
        }
        
        public int AddErrorAuditLog(RevCord.DataContracts.AuditAppType auditAppType, RevCord.DataContracts.AuditLogCategory auditLogCategory, string originator, string functionName, int tenantId, string message, string query, int userId) {
            return base.Channel.AddErrorAuditLog(auditAppType, auditLogCategory, originator, functionName, tenantId, message, query, userId);
        }
        
        public System.Threading.Tasks.Task<int> AddErrorAuditLogAsync(RevCord.DataContracts.AuditAppType auditAppType, RevCord.DataContracts.AuditLogCategory auditLogCategory, string originator, string functionName, int tenantId, string message, string query, int userId) {
            return base.Channel.AddErrorAuditLogAsync(auditAppType, auditLogCategory, originator, functionName, tenantId, message, query, userId);
        }
        
        public int AddExceptionAuditLog(RevCord.DataContracts.AuditAppType auditAppType, RevCord.DataContracts.AuditLogCategory auditLogCategory, string originator, string functionName, int tenantId, int userId, string message, string stackTrace, int exceptionCode) {
            return base.Channel.AddExceptionAuditLog(auditAppType, auditLogCategory, originator, functionName, tenantId, userId, message, stackTrace, exceptionCode);
        }
        
        public System.Threading.Tasks.Task<int> AddExceptionAuditLogAsync(RevCord.DataContracts.AuditAppType auditAppType, RevCord.DataContracts.AuditLogCategory auditLogCategory, string originator, string functionName, int tenantId, int userId, string message, string stackTrace, int exceptionCode) {
            return base.Channel.AddExceptionAuditLogAsync(auditAppType, auditLogCategory, originator, functionName, tenantId, userId, message, stackTrace, exceptionCode);
        }
        
        public int AddSQLExceptionAuditLog(RevCord.DataContracts.AuditAppType auditAppType, RevCord.DataContracts.AuditLogCategory auditLogCategory, string originator, string functionName, int tenantId, int userId, string message, string stackTrace, int exceptionCode, string query) {
            return base.Channel.AddSQLExceptionAuditLog(auditAppType, auditLogCategory, originator, functionName, tenantId, userId, message, stackTrace, exceptionCode, query);
        }
        
        public System.Threading.Tasks.Task<int> AddSQLExceptionAuditLogAsync(RevCord.DataContracts.AuditAppType auditAppType, RevCord.DataContracts.AuditLogCategory auditLogCategory, string originator, string functionName, int tenantId, int userId, string message, string stackTrace, int exceptionCode, string query) {
            return base.Channel.AddSQLExceptionAuditLogAsync(auditAppType, auditLogCategory, originator, functionName, tenantId, userId, message, stackTrace, exceptionCode, query);
        }
    }
}
