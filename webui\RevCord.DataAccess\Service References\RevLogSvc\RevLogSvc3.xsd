<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://tempuri.org/" elementFormDefault="qualified" targetNamespace="http://tempuri.org/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://localhost:27336/RevLogSvc.svc?xsd=xsd2" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts" />
  <xs:element name="AddSuccessAuditLog">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q1="http://schemas.datacontract.org/2004/07/RevCord.DataContracts" minOccurs="0" name="auditAppType" type="q1:AuditAppType" />
        <xs:element xmlns:q2="http://schemas.datacontract.org/2004/07/RevCord.DataContracts" minOccurs="0" name="auditLogCategory" type="q2:AuditLogCategory" />
        <xs:element minOccurs="0" name="originator" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="functionName" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="tenantId" type="xs:int" />
        <xs:element minOccurs="0" name="userId" type="xs:int" />
        <xs:element minOccurs="0" name="message" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="query" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="AddSuccessAuditLogResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="AddSuccessAuditLogResult" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="AddErrorAuditLog">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q3="http://schemas.datacontract.org/2004/07/RevCord.DataContracts" minOccurs="0" name="auditAppType" type="q3:AuditAppType" />
        <xs:element xmlns:q4="http://schemas.datacontract.org/2004/07/RevCord.DataContracts" minOccurs="0" name="auditLogCategory" type="q4:AuditLogCategory" />
        <xs:element minOccurs="0" name="originator" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="functionName" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="tenantId" type="xs:int" />
        <xs:element minOccurs="0" name="message" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="query" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="userId" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="AddErrorAuditLogResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="AddErrorAuditLogResult" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="AddExceptionAuditLog">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q5="http://schemas.datacontract.org/2004/07/RevCord.DataContracts" minOccurs="0" name="auditAppType" type="q5:AuditAppType" />
        <xs:element xmlns:q6="http://schemas.datacontract.org/2004/07/RevCord.DataContracts" minOccurs="0" name="auditLogCategory" type="q6:AuditLogCategory" />
        <xs:element minOccurs="0" name="originator" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="functionName" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="tenantId" type="xs:int" />
        <xs:element minOccurs="0" name="userId" type="xs:int" />
        <xs:element minOccurs="0" name="message" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="stackTrace" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="exceptionCode" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="AddExceptionAuditLogResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="AddExceptionAuditLogResult" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="AddSQLExceptionAuditLog">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q7="http://schemas.datacontract.org/2004/07/RevCord.DataContracts" minOccurs="0" name="auditAppType" type="q7:AuditAppType" />
        <xs:element xmlns:q8="http://schemas.datacontract.org/2004/07/RevCord.DataContracts" minOccurs="0" name="auditLogCategory" type="q8:AuditLogCategory" />
        <xs:element minOccurs="0" name="originator" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="functionName" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="tenantId" type="xs:int" />
        <xs:element minOccurs="0" name="userId" type="xs:int" />
        <xs:element minOccurs="0" name="message" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="stackTrace" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="exceptionCode" type="xs:int" />
        <xs:element minOccurs="0" name="query" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="AddSQLExceptionAuditLogResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="AddSQLExceptionAuditLogResult" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>