﻿<?xml version="1.0" encoding="utf-8"?>
<configurationSnapshot xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="urn:schemas-microsoft-com:xml-wcfconfigurationsnapshot">
  <behaviors />
  <bindings>
    <binding digest="System.ServiceModel.Configuration.BasicHttpBindingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089:&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data name=&quot;BasicHttpBinding_IRevLogSvc&quot; /&gt;" bindingType="basicHttpBinding" name="BasicHttpBinding_IRevLogSvc" />
  </bindings>
  <endpoints>
    <endpoint normalizedDigest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;http://localhost:27336/RevLogSvc.svc&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;BasicHttpBinding_IRevLogSvc&quot; contract=&quot;RevLogSvc.IRevLogSvc&quot; name=&quot;BasicHttpBinding_IRevLogSvc&quot; /&gt;" digest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;http://localhost:27336/RevLogSvc.svc&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;BasicHttpBinding_IRevLogSvc&quot; contract=&quot;RevLogSvc.IRevLogSvc&quot; name=&quot;BasicHttpBinding_IRevLogSvc&quot; /&gt;" contractName="RevLogSvc.IRevLogSvc" name="BasicHttpBinding_IRevLogSvc" />
  </endpoints>
</configurationSnapshot>