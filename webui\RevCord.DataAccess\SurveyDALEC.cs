﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using RevCord.DataContracts.EvaluationEntities;
using RevCord.DataContracts.SurveyEntities;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.Util;
using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using Newtonsoft.Json;

namespace RevCord.DataAccess
{
    public class SurveyDALEC
    {
        #region Public Functions

        public List<Survey> GetSurveysFromRecorder(Recorder recorder)
        {
            List<Survey> surveys = null;
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SURVEY_GETLIST;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "GetSurveysFromRecorder", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        surveys = ORMapper.MapSurveys(dr, recorder);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return surveys;
        }
        public List<Survey> GetPublishedSurveysFromRecorder(Recorder recorder)
        {
            List<Survey> surveys = null;
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SURVEY_GETLIST;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "GetPublishedSurveysFromRecorder", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        surveys = ORMapper.MapSurveys(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return surveys.Where(s => s.IsPublished == true).ToList();
        }
        public List<Survey> DeleteAndGetSurveysFromRecorder(int surveyId, Recorder recorder)
        {
            List<Survey> surveys = null;
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SURVEY_DELETE_N_GET;
                    cmd.Parameters.AddWithValue("@SurveyId ", surveyId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "DeleteAndGetSurveysFromRecorder", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        surveys = ORMapper.MapSurveys(dr, recorder);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return surveys;
        }
        public List<Survey> PublishAndGetSurveysFromRecorders(int surveyId, Recorder recorder)
        {
            List<Survey> surveys = null;
            List<CallEvaluation> callEvaluations = new List<CallEvaluation>();

            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SURVEY_PUBLISH_N_GET;
                    cmd.Parameters.AddWithValue("@SurveyId ", surveyId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "PublishAndGetSurveysFromRecorders", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        surveys = ORMapper.MapSurveys(dr, recorder);
                    }

                    EvaluatedCallsDetailBySurveyId(surveyId, recorder); // For QA-Published Forms Edit/ Delete ARIVU
                }
            }
            catch (Exception ex) { throw ex; }
            return surveys;
        }
        public Survey GetSurveyDetailsFromRecorder(int surveyId, Recorder recorder)
        {
            Survey survey = null;
            List<Survey> surveys = null;
            List<SurveySection> sections = new List<SurveySection>();
            List<Question> questions = new List<Question>();
            List<Option> options = new List<Option>();

            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SURVEY_GETDETAIL;
                    cmd.Parameters.AddWithValue("@SurveyId", surveyId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "GetSurveyDetailsFromRecorder", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        surveys = ORMapper.MapSurveys(dr);
                        survey = surveys == null || surveys.Count == 0 ? new Survey() : surveys[0];
                        dr.NextResult();
                        sections = ORMapper.MapSections(dr);//Section Table
                        dr.NextResult();
                        questions = ORMapper.MapQuestions(dr); //Question Table
                        dr.NextResult();
                        options = ORMapper.MapOptions(dr); //Question Option Table

                        survey.Sections = sections;
                        survey.Questions = questions;
                        if (survey.Questions != null)
                            survey.Questions.ForEach(q => q.Options = options.FindAll(o => o.QuestionId == q.Id));
                    }

                }
            }
            catch (Exception ex) { throw ex; }
            return survey;

        }
        public bool IsSurveyExistOnRecorder(string surveyTitle, Recorder recorder)
        {
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = string.Format("select * from emSurvey where Title = '{0}' and IsDeleted = 0", surveyTitle);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "IsSurveyExistOnRecorder", 0));

                    return Convert.ToInt32(cmd.ExecuteScalar()) > 0 ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public void CreateSurveyOnRecorder(Survey survey, Recorder recorder, int _tenantid)
        {
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SURVEY_INSERT;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@Title", survey.Name);
                    cmd.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);
                    cmd.Parameters.AddWithValue("@StartDate", survey.StartDate);
                    cmd.Parameters.AddWithValue("@EndDate", survey.EndDate);
                    cmd.Parameters.AddWithValue("@Description", survey.Description);
                    cmd.Parameters.AddWithValue("@CreatedDate", survey.CreatedDate);
                    cmd.Parameters.AddWithValue("@ActiveAfterEndDate", survey.ActiveAfterEndDate);
                    cmd.Parameters.AddWithValue("@IsPublished", survey.IsPublished);
                    cmd.Parameters.AddWithValue("@CreatedBy", survey.CreatedBy);
                    cmd.Parameters.AddWithValue("@HasSections", survey.HasSections);
                    cmd.Parameters.AddWithValue("@IsDeleted", survey.IsDeleted);
                    cmd.Parameters.Add("@SectionId", SqlDbType.Int).Direction = ParameterDirection.Output;
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "CreateSurveyOnRecorder", 0));

                    survey.Id = Convert.ToInt32(cmd.ExecuteScalar());
                    survey.Sections = new List<SurveySection>();
                  
                    int RevSyncServerSurveyId = 0;
                    if (SiteConfig.RevSyncEnabled)
                    {
                        RevSyncServerSurveyId = Convert.ToInt32(DALHelper.RevsyncAPICall(_tenantid, "SurveyDALEC", "CreateSurveyOnRecorder", JsonConvert.SerializeObject(survey)));
                        survey.RevSyncSurveyId = RevSyncServerSurveyId;
                        SurveySection eQSection = new SurveySection { Id = (int)cmd.Parameters["@SectionId"].Value, RevSyncId = RevSyncServerSurveyId };
                        survey.Sections.Add(eQSection);
                        if (RevSyncServerSurveyId > 0)
                        {
                            tran.Commit();
                            using (var Updatecmd = conn.CreateCommand())
                            {
                                Updatecmd.CommandType = CommandType.Text;
                                Updatecmd.CommandText = "UPDATE emSurvey SET [RevSyncServerID] = " + RevSyncServerSurveyId + " Where Id = " + survey.Id;
                                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(Updatecmd), Originator.Survey, "CreateSurveyOnRecorder", 0));
                                Updatecmd.ExecuteNonQuery();
                            }
                        }
                        else tran.Rollback();
                    }
					else if (SiteConfig.IsMTEnable)
                    {
                        bool isSent = DALHelper.SendMessageToHub(_tenantid, "SurveyDALEC", "CreateSurveyOnRecorder", JsonConvert.SerializeObject(survey));
                        if (isSent) tran.Commit(); else tran.Rollback();
                    }
                    else
                    {
                        SurveySection eQSection = new SurveySection { Id = (int)cmd.Parameters["@SectionId"].Value };
                        survey.Sections.Add(eQSection);
                        tran.Commit();
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            //finally{}
        }
        public long CreateQuestionOnRecorder(Question sQuestion, Recorder recorder, int _tenantid)
        {
            try
            {
                XElement xOptions = this.CreateOptionsXML(sQuestion.Options);
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.QUESTION_INSERT;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@SurveyId", sQuestion.SurveyId);
                    cmd.Parameters.AddWithValue("@SectionId", sQuestion.SectionId);
                    cmd.Parameters.AddWithValue("@Title", sQuestion.Statement);
                    cmd.Parameters.AddWithValue("@QuestionTypeId", sQuestion.TypeId);
                    cmd.Parameters.AddWithValue("@Ordering", sQuestion.Ordering);
                    cmd.Parameters.AddWithValue("@Options", xOptions.ToString());
                    cmd.Parameters.AddWithValue("@CreatedDate", sQuestion.CreatedDate);
                    cmd.Parameters.AddWithValue("@IsDeleted", sQuestion.IsDeleted);
                    cmd.Parameters.AddWithValue("@QuestionId", 0);
                    cmd.Parameters["@QuestionId"].Direction = ParameterDirection.Output;
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "CreateQuestionOnRecorder", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (sQuestion.Options != null && sQuestion.Options.Count != 0)
                        {
                            while (dr.Read())
                            {
                                foreach (var item in sQuestion.Options)
                                {
                                    if (item.Ordering == (int)dr["Ordering"])
                                        item.Id = (long)dr["Id"];
                                }
                            }
                        }
                    }
                    sQuestion.Id = Convert.ToInt64(cmd.Parameters["@QuestionId"].Value.ToString());

                    int RevSyncServerQuestionId = 0;
                    if (SiteConfig.RevSyncEnabled)
                    {
                        RevSyncServerQuestionId = Convert.ToInt32(DALHelper.RevsyncAPICall(_tenantid, "SurveyDALEC", "CreateQuestionOnRecorder", JsonConvert.SerializeObject(sQuestion)));
                        sQuestion.RevSyncQuestionId = RevSyncServerQuestionId;
                        if (RevSyncServerQuestionId > 0)
                        {
                            tran.Commit();
                            using (var Updatecmd = conn.CreateCommand())
                            {
                                Updatecmd.CommandType = CommandType.Text;
                                Updatecmd.CommandText = "UPDATE emQuestion SET [RevSyncServerID] = " + RevSyncServerQuestionId + " Where Id = " + sQuestion.Id;
                                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(Updatecmd), Originator.Survey, "CreateQuestionOnRecorder", 0));
                                Updatecmd.ExecuteNonQuery();
                            }
                        }
                        else tran.Rollback();
                    }
					else if (SiteConfig.IsMTEnable)
                    {
                        bool isSent = DALHelper.SendMessageToHub(_tenantid, "SurveyDALEC", "CreateQuestionOnRecorder", JsonConvert.SerializeObject(sQuestion));
                        if (isSent) tran.Commit(); else tran.Rollback();
                    }
                    else
                    {
                        tran.Commit();
                    }
                    return sQuestion.Id;
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public List<SurveySection> CreateAndGetSectionsOnRecorder(SurveySection surveyGroup, Recorder recorder, int _tenantid)
        {
            List<SurveySection> sections = null;
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SECTION_INSERT_N_GET;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@Title", surveyGroup.Title);
                    cmd.Parameters.AddWithValue("@SurveyId", surveyGroup.SurveyId);
                    cmd.Parameters.AddWithValue("@CreatedDate", surveyGroup.CreatedDate);
                    cmd.Parameters.AddWithValue("@IsDeleted", surveyGroup.IsDeleted);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "CreateAndGetSectionsOnRecorder", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        sections = ORMapper.MapSections(dr);
                    }
                    int RevSyncServerSectionId = 0;
                    if (SiteConfig.RevSyncEnabled) {
                        RevSyncServerSectionId = Convert.ToInt32(DALHelper.RevsyncAPICall(_tenantid, "SurveyDALEC", "CreateAndGetSectionsOnRecorder", JsonConvert.SerializeObject(surveyGroup)));//Convert.ToInt32(RevSynccmd.ExecuteScalar());
                        sections[0].RevSyncId = RevSyncServerSectionId;
                        if (RevSyncServerSectionId > 0)
                        {
                            tran.Commit();
                            using (var Updatecmd = conn.CreateCommand())
                            {
                                Updatecmd.CommandType = CommandType.Text;
                                Updatecmd.CommandText = "UPDATE emSurveySection SET [RevSyncServerID] = " + RevSyncServerSectionId + " Where Id = " + sections[0].Id;
                                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "CreateAndGetSectionsOnRecorder", 0));
                                Updatecmd.ExecuteNonQuery();
                            }
                        }
                        else tran.Rollback();
                    } else if (SiteConfig.IsMTEnable) {
                        bool isSent = DALHelper.SendMessageToHub(_tenantid, "SurveyDALEC", "CreateAndGetSectionsOnRecorder", JsonConvert.SerializeObject(surveyGroup));
                        if (isSent) tran.Commit(); else tran.Rollback();
                    } else tran.Commit();
                }
            }
            catch (Exception ex) { throw ex; }
            return sections;
        }
        public List<Question> GetQuestionsBySurveyIdFromRecorder(int surveyId, Recorder recorder)
        {
            List<Question> questions = null;
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.QUESTION_GETBYSURVEYID;
                    cmd.Parameters.AddWithValue("@SurveyId", surveyId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "GetQuestionsBySurveyIdFromRecorder", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        questions = ORMapper.MapQuestions(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return questions;
        }
        public List<SurveySection> GetSectionsBySurveyIdFromRecorder(int surveyId, Recorder recorder)
        {
            List<SurveySection> sections = null;
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SECTION_GETBYSURVEYID;
                    cmd.Parameters.AddWithValue("@SurveyId", surveyId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "GetSectionsBySurveyIdFromRecorder", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        sections = ORMapper.MapSections(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return sections;
        }
        public bool UpdateQuestionSectionOnRecorder(int questionId, int sectionId, Recorder recorder)
        {
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = string.Format("update emQuestion Set SectionId = {0} where Id = {1}", sectionId, questionId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "UpdateQuestionSectionOnRecorder", 0));
                    return Convert.ToInt32(cmd.ExecuteScalar()) > 0 ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public List<SurveySection> UpdateAndGetSectionsFromRecorder(SurveySection surveyGroup, Recorder recorder)
        {
            List<SurveySection> sections = null;
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SECTION_UPDATE_N_GET;
                    cmd.Parameters.AddWithValue("@Id", surveyGroup.Id);
                    cmd.Parameters.AddWithValue("@Title", surveyGroup.Title);
                    cmd.Parameters.AddWithValue("@SurveyId", surveyGroup.SurveyId);
                    cmd.Parameters.AddWithValue("@CreatedDate", surveyGroup.CreatedDate);
                    cmd.Parameters.AddWithValue("@IsDeleted", surveyGroup.IsDeleted);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "UpdateAndGetSectionsFromRecorder", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        sections = ORMapper.MapSections(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return sections;
        }
        public List<SurveySection> DeleteAndGetSectionsFromRecorder(int sectionId, int surveyId, Recorder recorder)
        {
            List<SurveySection> sections = null;
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SECTION_DELETE_N_GET;
                    cmd.Parameters.AddWithValue("@Id ", sectionId);
                    cmd.Parameters.AddWithValue("@SurveyId ", surveyId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "DeleteAndGetSectionsFromRecorder", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        sections = ORMapper.MapSections(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return sections;
        }
        public List<SurveySection> AssignUnAssignQuestionsAndGetSectionsFromRecorder(int surveyId, int sectionId, string assignedQuestions, string unassignedQuestions, Recorder recorder)
        {
            List<SurveySection> sections = null;
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.QUESTION_UPDATESECTIONS;
                    cmd.Parameters.AddWithValue("SurveyId", surveyId);
                    cmd.Parameters.AddWithValue("SectionId", sectionId);
                    cmd.Parameters.AddWithValue("Selected", assignedQuestions);
                    cmd.Parameters.AddWithValue("DeSelected", unassignedQuestions);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "AssignUnAssignQuestionsAndGetSectionsFromRecorder", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        sections = ORMapper.MapSections(dr);
                    }
                    return sections;
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public Survey GetSurveyFromRecorder(int surveyId, Recorder recorder)
        {
            Survey survey = null;
            List<Survey> surveys = null;
            List<SurveySection> sections = new List<SurveySection>();
            List<Question> questions = new List<Question>();
            List<Option> options = new List<Option>();

            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SURVEY_GETDETAIL;
                    cmd.Parameters.AddWithValue("@SurveyId", surveyId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "GetSurveyFromRecorder", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        surveys = ORMapper.MapSurveys(dr);
                        survey = surveys == null || surveys.Count == 0 ? new Survey() : surveys[0];
                        dr.NextResult();
                        sections = ORMapper.MapSections(dr);//Section Table
                        dr.NextResult();
                        questions = ORMapper.MapQuestions(dr); //Question Table
                        dr.NextResult();
                        options = ORMapper.MapOptions(dr); //Question Option Table

                        survey.Sections = sections;
                        survey.Questions = questions;
                        if (survey.Questions != null)
                            survey.Questions.ForEach(q => q.Options = options.FindAll(o => o.QuestionId == q.Id));
                    }

                }
            }
            catch (Exception ex) { throw ex; }
            return survey;

        }
        public void UpdateSurveyOnRecorder(Survey survey, Recorder recorder, int _tenantid)
        {
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SURVEY_UPDATE;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@Id", survey.Id);
                    cmd.Parameters.AddWithValue("@Title", survey.Name);
                    cmd.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);
                    cmd.Parameters.AddWithValue("@StartDate", survey.StartDate);
                    cmd.Parameters.AddWithValue("@EndDate", survey.EndDate);
                    cmd.Parameters.AddWithValue("@Description", survey.Description);
                    cmd.Parameters.AddWithValue("@CreatedDate", survey.CreatedDate);
                    cmd.Parameters.AddWithValue("@ActiveAfterEndDate", survey.ActiveAfterEndDate);
                    cmd.Parameters.AddWithValue("@IsPublished", survey.IsPublished);
                    cmd.Parameters.AddWithValue("@CreatedBy", survey.CreatedBy);
                    cmd.Parameters.AddWithValue("@HasSections", survey.HasSections);
                    cmd.Parameters.AddWithValue("@IsDeleted", survey.IsDeleted);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "UpdateSurveyOnRecorder", 0));
                    survey.Id = Convert.ToInt32(cmd.ExecuteScalar());
                    int RevSyncServerSurveyId = 0;
                    if (SiteConfig.RevSyncEnabled)
                    {  
                        RevSyncServerSurveyId = Convert.ToInt32(DALHelper.RevsyncAPICall(_tenantid, "SurveyDALEC", "UpdateSurveyOnRecorder", JsonConvert.SerializeObject(survey)));
                        survey.RevSyncSurveyId = RevSyncServerSurveyId;
                        if (RevSyncServerSurveyId > 0) {
                            tran.Commit();
                            using (var Updatecmd = conn.CreateCommand()) {
                                Updatecmd.CommandType = CommandType.Text;
                                Updatecmd.CommandText = "UPDATE emSurvey SET [RevSyncServerID] = " + RevSyncServerSurveyId + " Where Id = " + survey.Id;
                                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(Updatecmd), Originator.Survey, "UpdateSurveyOnRecorder", 0));
                                Updatecmd.ExecuteNonQuery();
                            }
                        } else tran.Rollback();
                    } else if (SiteConfig.IsMTEnable) {
                        bool isSent = DALHelper.SendMessageToHub(_tenantid, "SurveyDALEC", "UpdateSurveyOnRecorder", JsonConvert.SerializeObject(survey));
                        if (isSent) tran.Commit(); else tran.Rollback();
                    } else tran.Commit();
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public long UpdateQuestionOnRecorder(Question eQuestion, Recorder recorder, int _tenantid)
        {
            try
            {
                XElement xOptions = this.CreateOptionsXML(eQuestion.Options);
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.QUESTION_UPDATE;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@QuestionId", eQuestion.Id);
                    cmd.Parameters.AddWithValue("@SurveyId", eQuestion.SurveyId);
                    cmd.Parameters.AddWithValue("@SectionId", eQuestion.SectionId);
                    cmd.Parameters.AddWithValue("@Title", eQuestion.Statement);
                    cmd.Parameters.AddWithValue("@QuestionTypeId", eQuestion.TypeId);
                    cmd.Parameters.AddWithValue("@Ordering", eQuestion.Ordering);
                    cmd.Parameters.AddWithValue("@Options", xOptions.ToString());
                    cmd.Parameters.AddWithValue("@CreatedDate", eQuestion.CreatedDate);
                    cmd.Parameters.AddWithValue("@IsDeleted", eQuestion.IsDeleted);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "UpdateQuestionOnRecorder", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (eQuestion.Options != null && eQuestion.Options.Count != 0)
                        {
                            while (dr.Read())
                            {
                                foreach (var item in eQuestion.Options)
                                {
                                    if (item.Ordering == (int)dr["Ordering"])
                                        item.Id = (long)dr["Id"];
                                }
                            }
                        }
                    }
                    int RevSyncServerQuestionId = 0;
                    if (SiteConfig.RevSyncEnabled)
                    {
                        RevSyncServerQuestionId = Convert.ToInt32(DALHelper.RevsyncAPICall(_tenantid, "SurveyDALEC", "UpdateQuestionOnRecorder", JsonConvert.SerializeObject(eQuestion)));
                        eQuestion.RevSyncQuestionId = RevSyncServerQuestionId;
                        if (RevSyncServerQuestionId > 0) {
                            tran.Commit();
                            using (var Updatecmd = conn.CreateCommand())
                            {
                                Updatecmd.CommandType = CommandType.Text;
                                Updatecmd.CommandText = "UPDATE emQuestion SET [RevSyncServerID] = " + RevSyncServerQuestionId + " Where Id = " + eQuestion.Id;
                                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(Updatecmd), Originator.Survey, "UpdateQuestionOnRecorder", 0));

                                Updatecmd.ExecuteNonQuery();
                            }
                        } else tran.Rollback();
                    } else if (SiteConfig.IsMTEnable) {
                        bool isSent = DALHelper.SendMessageToHub(_tenantid, "SurveyDALEC", "UpdateQuestionOnRecorder", JsonConvert.SerializeObject(eQuestion));
                        if (isSent) tran.Commit(); else tran.Rollback();
                    } else tran.Commit();
                    return eQuestion.Id;
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public void DeleteQuestionFromRecorder(long id, Recorder recorder)
        {
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.QUESTION_DELETE;
                    //cmd.Parameters.AddWithValue("@SurveyId ", eQuestion.SurveyId);
                    cmd.Parameters.AddWithValue("@QuestionId", id);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "DeleteQuestionFromRecorder", 0));

                    cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public void UpdateIsPublishedOnRecorder(int surveyId, Recorder recorder)
        {
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SURVEY_UPDATE_ISPUBLISH;
                    cmd.Parameters.AddWithValue("@Id", surveyId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "UpdateIsPublishedOnRecorder", 0));
                    cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex)
            { throw ex; }
        }
        public long UpdateQuestionsOrderOnRecorder(List<Question> eRequest, Recorder recorder)
        {
            try
            {
                XElement xQuestions = this.CreateQuestionsXML(eRequest);
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.QUESTION_UPDATEORDER;
                    cmd.Parameters.AddWithValue("Questions", xQuestions.ToString());
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "UpdateQuestionsOrderOnRecorder", 0));
                    return Convert.ToInt64(cmd.ExecuteScalar());
                }
            }
            catch (Exception ex) { throw ex; }
        }
        #endregion

        #region Private Functions : Added By Arivu previously in Non-EC Mode
        private void EvaluatedCallsDetailBySurveyId(int surveyId, Recorder recorder)
        {
            List<CallEvaluation> callEvaluations = new List<CallEvaluation>();
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATION_GETALL_BY_SURVEYID;
                    cmd.Parameters.AddWithValue("@SurveyId", surveyId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "EvaluatedCallsDetailBySurveyId", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        callEvaluations = ORMapper.MapCallEvaluationsForPublishedForm(dr);

                        foreach (var callEval in callEvaluations)
                        {
                            if (callEval.StatusId == 3 || callEval.StatusId == 4)
                            {
                                UpdateStatusIdInCallEvaluation(surveyId, recorder);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            { throw ex; }

        }
        private void UpdateStatusIdInCallEvaluation(int surveyId, Recorder recorder)
        {
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.STATUSID_UPDATE_BY_SURVEYID;
                    cmd.Parameters.AddWithValue("@SurveyId", surveyId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "UpdateStatusIdInCallEvaluation", 0));

                    cmd.ExecuteNonQuery();

                }
            }
            catch (Exception ex) { throw ex; }
        }
        #endregion

        #region Private Functions
        private XElement CreateOptionsXML(List<Option> aOptions)
        {
            try
            {
                XElement options = new XElement("Options");
                foreach (var option in aOptions)
                {
                    options.Add(new XElement("Option",
                                    new XAttribute("Id", option.Id),
                                    new XAttribute("QuestionId", option.QuestionId),
                                    new XAttribute("Title", option.Title),
                                    new XAttribute("Ordering", option.Ordering),
                                    new XAttribute("CreatedDate", DateTime.Now),
                                    new XAttribute("IsDeleted", option.IsDeleted),
                                    new XAttribute("Score", option.Score)
                                ));
                }
                return options;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        private XElement CreateQuestionsXML(List<Question> eQuestions)
        {
            try
            {
                XElement questions = new XElement("Questions");
                foreach (var qItem in eQuestions)
                {
                    questions.Add(new XElement("Question",
                                    new XAttribute("Id", qItem.Id),
                                    new XAttribute("SurveyId", qItem.SurveyId),
                                    new XAttribute("Ordering", qItem.Ordering),
                                    new XAttribute("IsDeleted", qItem.IsDeleted)
                                ));
                }
                return questions;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        #endregion
    }
}
