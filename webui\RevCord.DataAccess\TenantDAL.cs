﻿using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.TenantEntities;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataAccess
{
    public class TenantDAL
    {
        private int _tenantId;
        public TenantDAL(int tenantId)
        {
            this._tenantId = tenantId;
        }

        public int UpdateColumnModel(List<TenantColumnModel> columnModel)
        {
            /*try
            {
                using (var conn = DALHelper.GetConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Tenant.TENANT_COLUMN_MODEL_UPDATE;

                    
                    cmd.Parameters.AddWithValue("@Name", columnModel.Name);

                    conn.Open();
                    int noOfRowsAffected = Convert.ToInt32(cmd.ExecuteNonQuery());
                    return noOfRowsAffected;

                }
            }
            catch (Exception ex) { throw ex; }*/

            try
            {
                int rowAffected = 0;
                using (var conn = DALHelper.GetConnection(_tenantId))
                {
                    conn.Open();
                    using (var tran = conn.BeginTransaction("TransMT"))
                    {
                        using (var cmd = conn.CreateCommand())
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.CommandText = DBConstants.Tenant.COLUMN_MODEL_UPDATE;
                            cmd.Transaction = tran as SqlTransaction;
                            foreach (var col in columnModel)
                            {
                                cmd.Parameters.AddWithValue("@ColumnModelId", col.ColumnModelId);
                                cmd.Parameters.AddWithValue("@Serial", col.SNo);
                                cmd.Parameters.AddWithValue("@Width", col.Width);
                                cmd.Parameters.AddWithValue("@Alignment", col.Alignment);
                                cmd.Parameters.AddWithValue("@Caption", col.Caption);
                                cmd.Parameters.AddWithValue("@IsHidden", col.IsHidden);
                                cmd.Parameters.AddWithValue("@Module", col.Module);
                                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Tenant, "UpdateColumnModel", _tenantId));
                                cmd.ExecuteNonQuery();
                                cmd.Parameters.Clear();
                                rowAffected++;
                            }
                        }
                        tran.Commit();
                    }
                    return rowAffected;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public List<CallTag> GetCalTags()
        {
            List<CallTag> lCallTag = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT * from vrCallTag where tagname is not null and LEN(ltrim(rtrim(tagname))) > 0";

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Tenant, "GetConfigurations", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        lCallTag = new List<CallTag>();
                        CallTag _CallTag = null;
                        while (dr.Read())
                        {
                            _CallTag = new CallTag();
                            _CallTag.TagID = Convert.ToInt16(dr["Id"]);//(int)dr["ColumnModelId"];
                            _CallTag.TagColor = Convert.ToString(dr["TagColorID"]);
                            _CallTag.TagName = Convert.ToString(dr["TagName"]);

                            lCallTag.Add(_CallTag);
                        }
                    }
                }

                return lCallTag;
            }
            catch (Exception ex) { throw ex; }
        }
        public List<TenantColumnModel> GetColumnsModel(int userNum, bool isOnlyIQ3ModeEnabled)
        {
            List<TenantColumnModel> columnsModel = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Tenant.COLUMN_MODEL_GET;
                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@IsOnlyIQ3ModeEnabled", isOnlyIQ3ModeEnabled);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Tenant, "GetColumnsModel", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        columnsModel = new List<TenantColumnModel>();
                        TenantColumnModel colModel = null;
                        while (dr.Read())
                        {
                            colModel = new TenantColumnModel();
                            colModel.ColumnModelId = Convert.ToInt16(dr["Id"]);//(int)dr["ColumnModelId"];
                            colModel.Name = Convert.ToString(dr["Name"]);
                            colModel.SNo = Convert.ToInt16(dr["Serial"]);
                            colModel.Width = Convert.ToInt16(dr["Width"]);
                            colModel.Alignment = Convert.ToString(dr["Alignment"]);
                            colModel.Caption = Convert.ToString(dr["Caption"]);
                            colModel.IsHidden = Convert.ToBoolean(dr["IsHidden"]);
                            //colModel.IsSortable = Convert.ToBoolean(dr["IsSortable"]);
                            colModel.Module = (ColumnModelModule)Enum.Parse(typeof(ColumnModelModule), Convert.ToString(dr["Module"]));

                            columnsModel.Add(colModel);
                        }
                    }
                }

                return columnsModel;
            }
            catch (Exception ex) { throw ex; }
        }


        public List<TenantColumnModel> GetColumnsModel(string whereClause, int userNum, bool isOnlyIQ3ModeEnabled)
        {
            List<TenantColumnModel> columnsModel = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Tenant.COLUMN_MODEL_GET_BY_WHERE;
                    cmd.Parameters.AddWithValue("@WhereClause", whereClause);
                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@IsOnlyIQ3ModeEnabled", isOnlyIQ3ModeEnabled);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Tenant, "GetColumnsModel", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        columnsModel = new List<TenantColumnModel>();
                        TenantColumnModel colModel = null;
                        while (dr.Read())
                        {
                            colModel = new TenantColumnModel();
                            colModel.ColumnModelId = Convert.ToInt16(dr["Id"]);//(int)dr["ColumnModelId"];
                            colModel.Name = Convert.ToString(dr["Name"]);
                            colModel.SNo = Convert.ToInt16(dr["Serial"]);
                            colModel.Width = Convert.ToInt16(dr["Width"]);
                            colModel.Alignment = Convert.ToString(dr["Alignment"]);
                            colModel.Caption = Convert.ToString(dr["Caption"]);
                            colModel.IsHidden = Convert.ToBoolean(dr["IsHidden"]);
                            colModel.UserNum = Convert.ToInt32(dr["UserNum"]);
                            colModel.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                            colModel.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                            //colModel.IsSortable = Convert.ToBoolean(dr["IsSortable"]);
                            colModel.UserNum = Convert.ToInt32(dr["UserNum"]);
                            colModel.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                            colModel.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                            colModel.Module = (ColumnModelModule)Enum.Parse(typeof(ColumnModelModule), Convert.ToString(dr["Module"]));

                            columnsModel.Add(colModel);
                        }
                    }
                }

                return columnsModel;
            }
            catch (Exception ex) { throw ex; }
        }

        public bool ResetUserColumnModel(int userNum)
        {
            try
            {
                int rowAffected = 0;
                using (var conn = DALHelper.GetConnection(_tenantId))
                {
                    conn.Open();
                    using (var tran = conn.BeginTransaction("TransMT"))
                    {
                        using (var cmd = conn.CreateCommand())
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.CommandText = "UPDATE mtColumnModel SET IsDeleted = 1 WHERE UserNum = @UserNum";
                            cmd.Parameters.AddWithValue("@UserNum", userNum);
                            cmd.Transaction = tran as SqlTransaction;
                            Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Tenant, "ResetUserColumnModel", _tenantId));
                            rowAffected = cmd.ExecuteNonQuery();
                        }
                        tran.Commit();
                    }
                }
                return rowAffected > 0 ? true : false;
            }
            catch (Exception ex) { throw ex; }
        }

        public List<TenantCustomConfiguration> GetConfigurations()
        {
            List<TenantCustomConfiguration> customConfigurations = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT * from mtConfigurations WHERE IsDeleted = 0";

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Tenant, "GetConfigurations", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        customConfigurations = new List<TenantCustomConfiguration>();
                        TenantCustomConfiguration customConfig = null;
                        while (dr.Read())
                        {
                            customConfig = new TenantCustomConfiguration();
                            customConfig.Id = Convert.ToInt16(dr["Id"]);
                            customConfig.KeyName = Convert.ToString(dr["KeyName"]);
                            customConfig.KeyValue = Convert.ToString(dr["KeyValue"]);

                            customConfigurations.Add(customConfig);
                        }
                    }
                }

                return customConfigurations;
            }
            catch (Exception ex) { throw ex; }
        }

        public List<TenantCustomConfiguration> GetConfigurations(string whereClause)
        {
            List<TenantCustomConfiguration> customConfigurations = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Tenant.CONFIGURATION_GET_BY_WHERE;
                    cmd.Parameters.AddWithValue("@WhereClause", whereClause);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Tenant, "GetConfigurations", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        customConfigurations = new List<TenantCustomConfiguration>();
                        TenantCustomConfiguration customConfig = null;
                        while (dr.Read())
                        {
                            customConfig = new TenantCustomConfiguration();
                            customConfig.Id = Convert.ToInt16(dr["Id"]);
                            customConfig.KeyName = Convert.ToString(dr["Name"]);
                            customConfig.KeyValue = Convert.ToString(dr["ConfigurationValue"]);

                            customConfigurations.Add(customConfig);
                        }
                    }
                }

                return customConfigurations;
            }
            catch (Exception ex) { throw ex; }
        }

        public int UpdateConfigurations(List<TenantCustomConfiguration> tenantConfigurations)
        {
            try
            {
                int rowAffected = 0;
                using (var conn = DALHelper.GetConnection(_tenantId))
                {
                    conn.Open();
                    using (var tran = conn.BeginTransaction("TransMT"))
                    {
                        using (var cmd = conn.CreateCommand())
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.CommandText = DBConstants.Tenant.CONFIGURATION_UPDATE;
                            cmd.Transaction = tran as SqlTransaction;
                            foreach (var config in tenantConfigurations)
                            {
                                cmd.Parameters.AddWithValue("@ConfigurationId", config.Id);
                                cmd.Parameters.AddWithValue("@ConfigurationValue", config.KeyValue);
                                cmd.Parameters.AddWithValue("@LastModifiedDate", DateTime.Now);
                                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Tenant, "UpdateConfigurations", _tenantId));

                                cmd.ExecuteNonQuery();
                                cmd.Parameters.Clear();
                                rowAffected++;
                            }
                        }
                        tran.Commit();
                    }
                    return rowAffected;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public bool AuthenticateEmail(string email)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandText = "SELECT * FROM t_Account WHERE (UserID = @Email OR UserEmail = @Email)";
                    cmd.Parameters.AddWithValue("@Email", email);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Tenant, "AuthenticateEmail", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        return dr.HasRows;
                    }
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public bool AuthenticateEmail(string email, out int tenantId)
        {
            tenantId = 0;

            try
            {

                using (var conn = DALHelper.GetConnectionMasterDB())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandText = "SELECT * FROM mtTenantUser WHERE (Email = @Email)";
                    cmd.Parameters.AddWithValue("@Email", email);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Tenant, "AuthenticateEmail(string email, out int tenantId)", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            dr.Read();
                            tenantId = Convert.ToInt32(dr["TenantId"]);
                        }
                        return dr.HasRows;
                        //return false;
                    }
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public bool AuthenticateEmail(string email, out int tenantId, out string connection)
        {
            tenantId = 0;
            connection = "";
            try
            {

                using (var conn = DALHelper.GetConnectionMasterDB())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Tenant.VALIDATE_EMAIL_EXISTS;
                    cmd.Parameters.AddWithValue("@Email", email);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Tenant, "AuthenticateEmail(string email, out int tenantId, out string connection)", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            dr.Read();
                            tenantId = Convert.ToInt32(dr["TenantId"]);
                            connection = Convert.ToString(dr["DBConnection"]);
                        }
                        return dr.HasRows;
                        //return false;
                    }
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public string GetTenantDbConnection(int tenantId)
        {
            string tenantDbConnection = string.Empty;
            try
            {

                using (var conn = DALHelper.GetConnectionMasterDB())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Tenant.GET_DB_CONNECTION;
                    cmd.Parameters.AddWithValue("@TenantId", tenantId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Tenant, "GetTenantDbConnection", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            dr.Read();
                            tenantDbConnection = Convert.ToString(dr["DBConnection"]);
                        }
                        return tenantDbConnection;
                    }
                }
            }
            catch (Exception ex) { throw ex; }
        }


        public List<Tenant> AuthenticateEmailAndGetTenants(string email)
        {
            List<Tenant> userTenants = null;
            Tenant tenant = null;
            try
            {
                using (var conn = DALHelper.GetConnectionMasterDB())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Tenant.VALIDATE_EMAIL_AND_GET_TENANTS;
                    cmd.Parameters.AddWithValue("@Email", email);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Tenant, "AuthenticateEmailAndGetTenants", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                            userTenants = new List<Tenant>();

                        while (dr.Read())
                        {
                            tenant = new Tenant();
                            tenant.Id = Convert.ToInt32(dr["TenantId"]);
                            tenant.Name = Convert.ToString(dr["TenantName"]);
                            tenant.SoftwareVersion = Convert.ToString(dr["SoftwareVersion"]);

                            userTenants.Add(tenant);
                        }
                    }
                }
                return userTenants;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }


        public long InsertUserAccount(int tenantId, int userNum, string email, string loginName, string firstName, string lastName, string userName, string comments)
        {
            //InsertUserAccount(3, 1000, "<EMAIL>", "<EMAIL>", "", "", "sher", "DAL test");
            try
            {
                using (var conn = DALHelper.GetConnectionMasterDB())
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Tenant.TENANT_USER_INSERT;
                    cmd.Parameters.AddWithValue("@TenantId", tenantId);
                    cmd.Parameters.AddWithValue("@UserNum ", userNum);
                    cmd.Parameters.AddWithValue("@UserEmail", email);
                    cmd.Parameters.AddWithValue("@UserID", loginName);
                    cmd.Parameters.AddWithValue("@FirstName", firstName);
                    cmd.Parameters.AddWithValue("@LastName", lastName);
                    cmd.Parameters.AddWithValue("@UserName", userName);
                    cmd.Parameters.AddWithValue("@Comments", comments);
                    cmd.Parameters.AddWithValue("@CreatedDate", DateTime.Now);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Tenant, "InsertUserAccount", _tenantId));

                    long userId = (long)cmd.ExecuteScalar();
                    return userId;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public List<TenantUser> FetchIQ3TenantUsers()
        {
            List<TenantUser> tenantUsers = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    //cmd.CommandText = "SELECT * FROM t_Account WHERE STATUS = 1 AND ISDEVICEUSER = 1 AND ENABLE_USER = 1";
                    cmd.CommandText = "SELECT distinct acc.* FROM t_Account acc INNER JOIN umIQ3CustomMarker cm ON acc.UserNum = cm.UserNum WHERE acc.STATUS = 1 AND acc.ISDEVICEUSER = 1 AND acc.ENABLE_USER = 1";

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Tenant, "FetchIQ3TenantUsers", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        tenantUsers = new List<TenantUser>();
                        TenantUser tenantUser = null;
                        while (dr.Read())
                        {
                            tenantUser = new TenantUser();
                            tenantUser.UserId = Convert.ToString(dr["UserId"]);
                            tenantUser.UserName = Convert.ToString(dr["UserName"]);
                            tenantUser.UserNum = Convert.ToInt32(dr["UserNum"]);

                            tenantUsers.Add(tenantUser);
                        }
                    }
                }

                return tenantUsers;
            }
            catch (Exception ex) { throw ex; }
        }

        public List<Tenant> GetAllTenants()
        {
            List<Tenant> tenants = null;
            Tenant tenant = null;
            try
            {

                using (var conn = DALHelper.GetConnectionMasterDB())
                using (var cmd = conn.CreateCommand())
                {
                    //cmd.CommandType = CommandType.Text;
                    //cmd.CommandText = "select * from mtTenant where IsDeleted = 0";
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Tenant.TENANT_GET_IQ3ENABLED;
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Tenant, "GetAllTenants", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                            tenants = new List<Tenant>();

                        while (dr.Read())
                        {
                            tenant = new Tenant();
                            tenant.Id = Convert.ToInt32(dr["Id"]);
                            tenant.Name = Convert.ToString(dr["Name"]);
                            tenant.State = Convert.ToString(dr["State"]);
                            tenant.JurisdictionID = Convert.ToString(dr["JurisdictionID"]);

                            tenants.Add(tenant);
                        }
                    }
                    return tenants;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public int ReorderColumns(List<TenantColumnModel> columnModel)
        {
            try
            {
                int rowAffected = 0;
                using (var conn = DALHelper.GetConnection(_tenantId))
                {
                    conn.Open();
                    using (var tran = conn.BeginTransaction("TransMT"))
                    {
                        using (var cmd = conn.CreateCommand())
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.CommandText = DBConstants.Tenant.COLUMN_MODEL_REORDER;
                            cmd.Transaction = tran as SqlTransaction;
                            foreach (var col in columnModel)
                            {
                                cmd.Parameters.AddWithValue("@Serial", col.SNo);
                                cmd.Parameters.AddWithValue("@Module", col.Module);
                                cmd.Parameters.AddWithValue("@UserNum", col.UserNum);
                                cmd.Parameters.AddWithValue("@ColumnName", col.Name);

                                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Tenant, "ReorderColumn", _tenantId));
                                cmd.ExecuteNonQuery();
                                cmd.Parameters.Clear();
                                rowAffected++;
                            }
                        }
                        tran.Commit();
                    }
                    return rowAffected;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        #region Tenant License
        public TenantInformation GetTenantInformation(out int noOfDomainUsers)
        {
            noOfDomainUsers = 0;
            try
            {
                string strLicence = string.Empty;
                TenantInformation tenantInformation = new TenantInformation();
                tenantInformation.SerialKey = string.Empty;

                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Tenant.TENANT_GET_TENANTINFORMATION;
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Common, "GetTenantInformation", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            tenantInformation.Id = Convert.ToInt32(dr["Id"]);
                            tenantInformation.LicenseId = Convert.ToString(dr["LicenseId"]);
                            tenantInformation.ActivationPassword = Convert.ToString(dr["ActivationPassword"]);
                            tenantInformation.IsNextGenEnabled = Convert.ToBoolean(dr["NextGenRecorder"]);
                        }

                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                tenantInformation.NoOfADUsers = Convert.ToInt32(dr["NoOfDomainUser"]);
                                noOfDomainUsers = tenantInformation.NoOfADUsers;
                            }
                        }

                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                tenantInformation.SerialKey = Convert.ToString(dr["SerialKey"]);
                            }
                        }
                    }
                    return tenantInformation;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        #endregion
    }
}