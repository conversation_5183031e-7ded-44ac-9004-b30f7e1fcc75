﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data.SqlClient;
using System.Data;
using RevCord.DataContracts.Response;
using RevCord.DataContracts.UserManagement;
using RevCord.DataContracts.DTO;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.DataContracts.ViewModelEntities;
using RevCord.DataAccess.Util;
using RevCord.Util;
using RevCord.DataContracts;
using System.Threading.Tasks;

namespace RevCord.DataAccess
{
    public class UserManagerDALEC
    {
        private static int CMD_TIMEOUT = AppSettingsUtil.GetInt("commandTimeout", 300);
        private static string MTConnectionHost = AppSettingsHelper.GetValueAsString("MTConnectionHost");

        private int _tenantId;
        public UserManagerDALEC(int tenantId)
        {
            this._tenantId = tenantId;
        }

        #region ------- Treeview -------

        public List<TreeviewData> GetAdvanceReportTreeFromRecorderEC(int userNum, string userId, int authNum, string authType, int type, int userType, Recorder recorder)
        {
            //EXEC sp_Init_Tree @UserNum = 1000,  @AuthNum = 4,  @AuthType = '1', @Type = 4
            List<TreeviewData> treeNodes = null;
            TreeviewData treeNode = null;
            try
            {

                using (var conn = new SqlConnection(recorder.ConnectionString))
                {
                    System.Diagnostics.Debug.WriteLine(string.Format("{0}{1}", recorder.Id, recorder.Name));
                    using (var cmd = conn.CreateCommand())
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        //cmd.CommandText = DBConstants.UserManagement.TREEVIEW_GET_ADMIN;
                        cmd.CommandText = userType == 0 ? DBConstants.UserManagement.TREEVIEW_GET_ADDITIONAL : DBConstants.UserManagement.TREEVIEW_GET_SIMPLE; //DBConstants.UserManagement.TREEVIEW_GET_SIMPLE;
                        cmd.CommandTimeout = CMD_TIMEOUT;
                        cmd.Parameters.AddWithValue("@UserNum", userNum);
                        cmd.Parameters.AddWithValue("@UserID", userId);
                        cmd.Parameters.AddWithValue("@AuthNum", authNum);
                        cmd.Parameters.AddWithValue("@AuthType", authType);
                        cmd.Parameters.AddWithValue("@Type", type);

                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.TreeControl, "GetAdvanceReportTreeFromRecorderEC", _tenantId));
                        conn.Open();
                        using (SqlDataReader dr = cmd.ExecuteReader())
                        {
                            if (dr.HasRows)
                            {
                                treeNodes = new List<TreeviewData>();
                                while (dr.Read())
                                {
                                    treeNode = new TreeviewData();

                                    treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                    treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                    treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                    treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                    treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                    treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                    treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                    treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                    treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                    treeNode.Param3 = Convert.ToString(dr["Param3"]);
                                    treeNode.IsRevCell = Convert.ToBoolean(dr["IsRevCell"]);
                                    treeNode.IsIQ3 = Convert.ToBoolean(dr["IsIQ3"]);
                                    treeNode.IsRevAgentAssociated = Convert.ToBoolean(dr["IsRevAgentAssociated"]);
                                    treeNode.IsText911 = DBRecordExtensions.HasColumn(dr, "IsText911") ? Convert.ToBoolean(dr["IsText911"]) : false;

                                    treeNodes.Add(treeNode);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return treeNodes;//.Take(9).Where(n => n.NodeId.ContainsAny("1000", "1002","1003")).ToList();
        }

        public List<TreeviewData> GetGroupsTree(int userNum, string userId, int authNum, string authType, int type, int userType, Recorder recorder, int roleId, int roleType)
        {
            //EXEC sp_Init_Tree @UserNum = 1000,  @AuthNum = 4,  @AuthType = '1', @Type = 4
            List<TreeviewData> treeNodes = null;
            TreeviewData treeNode = null;
            try
            {

                using (var conn = new SqlConnection(recorder.ConnectionString))
                {
                    System.Diagnostics.Debug.WriteLine(string.Format("{0}{1}", recorder.Id, recorder.Name));
                    using (var cmd = conn.CreateCommand())
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        if (roleId > 0)
                        {
                            cmd.CommandText = roleType == 1 ? DBConstants.UserManagement.TREEVIEW_GET_GROUPBASED : DBConstants.UserManagement.TREEVIEW_GET_CHANNELBASED;
                            cmd.Parameters.AddWithValue("@RoleId", roleId);
                            cmd.Parameters.AddWithValue("@UserNum", userNum);
                            cmd.Parameters.AddWithValue("@AuthNum", authNum);
                            cmd.Parameters.AddWithValue("@AuthType", authType);
                            cmd.Parameters.AddWithValue("@Type", type);
                        }
                        else
                        {
                            cmd.CommandText = userType == 0 ? DBConstants.UserManagement.TREEVIEW_GET_ADDITIONAL : DBConstants.UserManagement.TREEVIEW_GET_SIMPLE; //DBConstants.UserManagement.TREEVIEW_GET_SIMPLE;
                            cmd.Parameters.AddWithValue("@UserNum", userNum);
                            cmd.Parameters.AddWithValue("@UserID", userId);
                            cmd.Parameters.AddWithValue("@AuthNum", authNum);
                            cmd.Parameters.AddWithValue("@AuthType", authType);
                            cmd.Parameters.AddWithValue("@Type", type);
                        }
                        cmd.CommandTimeout = CMD_TIMEOUT;
                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.TreeControl, "GetGroupsTree", _tenantId));
                        conn.Open();
                        using (SqlDataReader dr = cmd.ExecuteReader())
                        {
                            if (dr.HasRows)
                            {
                                treeNodes = new List<TreeviewData>();
                                while (dr.Read())
                                {
                                    treeNode = new TreeviewData();

                                    treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                    treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                    treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                    treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                    treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                    treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                    treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                    treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                    treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                    treeNode.Param3 = Convert.ToString(dr["Param3"]);
                                    treeNode.IsRevCell = Convert.ToBoolean(dr["IsRevCell"]);
                                    treeNode.IsIQ3 = Convert.ToBoolean(dr["IsIQ3"]);
                                    treeNode.IsRevAgentAssociated = Convert.ToBoolean(dr["IsRevAgentAssociated"]);
                                    treeNode.IsText911 = DBRecordExtensions.HasColumn(dr, "IsText911") ? Convert.ToBoolean(dr["IsText911"]) : false;

                                    treeNodes.Add(treeNode);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return treeNodes;//.Take(9).Where(n => n.NodeId.ContainsAny("1000", "1002","1003")).ToList();
        }
        public List<TreeviewData> GetGroupsTreeWithoutRevCell(int userNum, string userId, int authNum, string authType, int type, int userType, bool isRevCellRequired, Recorder recorder, int roleId, int roleType)
        {
            List<TreeviewData> treeNodes = null;
            TreeviewData treeNode = null;
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                {
                    System.Diagnostics.Debug.WriteLine(string.Format("{0}{1}", recorder.Id, recorder.Name));
                    using (var cmd = conn.CreateCommand())
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        if (roleId > 0)
                        {
                            cmd.CommandText = roleType == 1 ? DBConstants.UserManagement.TREEVIEW_GET_GROUPBASED : DBConstants.UserManagement.TREEVIEW_GET_CHANNELBASED;
                            cmd.Parameters.AddWithValue("@RoleId", roleId);
                        }
                        else
                            cmd.CommandText = userType == 0 ? DBConstants.UserManagement.TREEVIEW_GET_ADDITIONAL : DBConstants.UserManagement.TREEVIEW_GET_SIMPLE;
                        cmd.CommandTimeout = CMD_TIMEOUT;
                        cmd.Parameters.AddWithValue("@UserNum", userNum);
                        cmd.Parameters.AddWithValue("@UserID", userId);
                        cmd.Parameters.AddWithValue("@AuthNum", authNum);
                        cmd.Parameters.AddWithValue("@AuthType", authType);
                        cmd.Parameters.AddWithValue("@Type", type);
                        cmd.Parameters.AddWithValue("@IsRevCellRequired", isRevCellRequired);
                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.TreeControl, "GetGroupsTreeWithoutRevCell", _tenantId));
                        conn.Open();
                        using (SqlDataReader dr = cmd.ExecuteReader())
                        {
                            if (dr.HasRows)
                            {
                                treeNodes = new List<TreeviewData>();
                                while (dr.Read())
                                {
                                    treeNode = new TreeviewData();

                                    treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                    treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                    treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                    treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                    treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                    treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                    treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                    treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                    treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                    treeNode.Param3 = Convert.ToString(dr["Param3"]);
                                    treeNode.IsRevCell = Convert.ToBoolean(dr["IsRevCell"]);
                                    treeNode.IsIQ3 = Convert.ToBoolean(dr["IsIQ3"]);
                                    treeNodes.Add(treeNode);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return treeNodes;//.Take(9).Where(n => n.NodeId.ContainsAny("1000", "1002","1003")).ToList();
        }

        public List<TreeviewData> GetGroupsTreeNonAdmin(int userNum, string userId, int authNum, string authType, int type, int userType, Recorder recorder)
        {
            //EXEC sp_MyExt_Tree @UserNum = 1061, @AuthNum = 4, @AuthType = 1, @Type  = 1
            List<TreeviewData> treeNodes = null;
            TreeviewData treeNode = null;
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = userType == 0 ? DBConstants.UserManagement.TREEVIEW_GET_ADDITIONAL : DBConstants.UserManagement.TREEVIEW_GET_SIMPLE;
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@UserID", userId);
                    cmd.Parameters.AddWithValue("@AuthNum", authNum);
                    cmd.Parameters.AddWithValue("@AuthType", authType);
                    cmd.Parameters.AddWithValue("@Type", type);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.TreeControl, "GetGroupsTreeNonAdmin", _tenantId));
                    conn.Open();
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            treeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);

                                treeNodes.Add(treeNode);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return treeNodes;//.Take(9).Where(n => n.NodeId.ContainsAny("1000", "1002","1003")).ToList();
        }
        public List<EnterpriseNodeDTO> GetEnterpriseUserRightsData(int userNum, string userId, int authNum, string authType, int type, int userType, out List<Tuple<int, int>> userRecodersGroups)
        {
            //EXEC um_SimpleUserTree_Wrapper @UserNum = 1032, @AuthNum = 0, @AuthType = '1', @Type = 1
            /***        Tables Order        ***/
            //IRLite
            //Monitor
            //Search
            //QA Evaluation
            //QA Evaluation Reports
            //Advanced Reports
            //IRFull

            /*List<TreeviewData> treeNodes = null;
            TreeviewData treeNode = null;*/
            List<EnterpriseNodeDTO> entTreeNodes = null;
            EnterpriseNodeDTO entTreeNode = null;
            try
            {

                using (var conn = DALHelper.GetConnection(_tenantId))
                {
                    using (var cmd = conn.CreateCommand())
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandText = "um_EnterpriseUser_GetNodeData_Wrapper"; //"um_EnterpriseUserTree_Wrapper";
                        cmd.CommandTimeout = CMD_TIMEOUT;
                        cmd.Parameters.AddWithValue("@UserNum", userNum);
                        //cmd.Parameters.AddWithValue("@UserID", userId);
                        cmd.Parameters.AddWithValue("@AuthNum", authNum);
                        //cmd.Parameters.AddWithValue("@AuthType", authType);
                        //cmd.Parameters.AddWithValue("@Type", type);
                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.TreeControl, "GetEnterpriseUserRightsData", _tenantId));
                        conn.Open();
                        using (SqlDataReader dr = cmd.ExecuteReader())
                        {
                            //if (dr.HasRows)
                            //{
                            //treeNodes = new List<TreeviewData>();
                            entTreeNodes = new List<EnterpriseNodeDTO>();
                            //1. IRLite
                            while (dr.Read())
                            {
                                #region Mapping
                                /*treeNode = new TreeviewData();
                                    treeNode.RecId = Convert.ToInt32(dr["RecId"]);
                                    treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                    treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                    treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                    treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                    treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                    treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                    treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                    treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                    treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                    treeNode.Param3 = Convert.ToString(dr["Param3"]);
                                    treeNodes.Add(treeNode);*/
                                entTreeNode = new EnterpriseNodeDTO();
                                entTreeNode.RecId = Convert.ToInt32(dr["RecId"]);
                                entTreeNode.ModuleBit = ModuleBit.IRLite;
                                entTreeNode.NodeId = Convert.ToInt32(dr["SelectedExt"]);
                                //entTreeNode.NodeCaption = Convert.ToString(dr["SelectedExtName"]);
                                entTreeNode.DbStatus = true;
                                entTreeNodes.Add(entTreeNode);
                                #endregion
                            }
                            //2. Monitor
                            dr.NextResult();
                            while (dr.Read())
                            {
                                #region Mapping
                                entTreeNode = new EnterpriseNodeDTO();
                                entTreeNode.RecId = Convert.ToInt32(dr["RecId"]);
                                entTreeNode.ModuleBit = ModuleBit.Monitor;
                                entTreeNode.NodeId = Convert.ToInt32(dr["SelectedExt"]);
                                entTreeNode.DbStatus = true;
                                entTreeNodes.Add(entTreeNode);
                                #endregion
                            }
                            //3. Search
                            dr.NextResult();
                            while (dr.Read())
                            {
                                #region Mapping
                                entTreeNode = new EnterpriseNodeDTO();
                                entTreeNode.RecId = Convert.ToInt32(dr["RecId"]);
                                entTreeNode.ModuleBit = ModuleBit.Search;
                                entTreeNode.NodeId = Convert.ToInt32(dr["SelectedExt"]);
                                entTreeNode.DbStatus = true;
                                entTreeNodes.Add(entTreeNode);
                                #endregion
                            }
                            //4. QA Evaluation
                            dr.NextResult();
                            while (dr.Read())
                            {
                                #region Mapping
                                entTreeNode = new EnterpriseNodeDTO();
                                entTreeNode.RecId = Convert.ToInt32(dr["RecId"]);
                                entTreeNode.ModuleBit = ModuleBit.Evaluation;
                                entTreeNode.NodeId = Convert.ToInt32(dr["SelectedUserNum"]);
                                entTreeNode.DbStatus = true;
                                entTreeNodes.Add(entTreeNode);
                                #endregion
                            }
                            //5. QA Evaluation Reports
                            dr.NextResult();
                            while (dr.Read())
                            {
                                #region Mapping
                                entTreeNode = new EnterpriseNodeDTO();
                                entTreeNode.RecId = Convert.ToInt32(dr["RecId"]);
                                entTreeNode.ModuleBit = ModuleBit.QAEvaluationReports;
                                entTreeNode.NodeId = Convert.ToInt32(dr["SelectedUserNum"]);
                                entTreeNode.DbStatus = true;
                                entTreeNodes.Add(entTreeNode);
                                #endregion
                            }
                            //6. Advanced Reports
                            dr.NextResult();
                            while (dr.Read())
                            {
                                #region Mapping
                                entTreeNode = new EnterpriseNodeDTO();
                                entTreeNode.RecId = Convert.ToInt32(dr["RecId"]);
                                entTreeNode.ModuleBit = ModuleBit.AdvancedReports;
                                entTreeNode.NodeId = Convert.ToInt32(dr["SelectedExt"]);
                                entTreeNode.DbStatus = true;
                                entTreeNodes.Add(entTreeNode);
                                #endregion
                            }
                            //7. IRFull
                            dr.NextResult();
                            while (dr.Read())
                            {
                                #region Mapping
                                entTreeNode = new EnterpriseNodeDTO();
                                entTreeNode.RecId = Convert.ToInt32(dr["RecId"]);
                                entTreeNode.ModuleBit = ModuleBit.IRFull;
                                entTreeNode.NodeId = Convert.ToInt32(dr["SelectedExt"]);
                                entTreeNode.DbStatus = true;
                                entTreeNodes.Add(entTreeNode);
                                #endregion
                            }
                            //8. Recorder Groups
                            dr.NextResult();
                            var userRecGrp = new List<Tuple<int, int>>();
                            while (dr.Read())
                            {
                                if (Convert.ToInt32(dr["GroupNum"]) != 0)
                                    userRecGrp.Add(new Tuple<int, int>(Convert.ToInt32(dr["RecId"]), Convert.ToInt32(dr["GroupNum"])));
                            }
                            userRecodersGroups = userRecGrp;
                            //}
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return entTreeNodes;
        }
        public List<EnterpriseNodeDTO> GetEnterpriseUserRightsData(int userNum, int authNum)
        {
            List<EnterpriseNodeDTO> entTreeNodes = null;
            EnterpriseNodeDTO entTreeNode = null;
            try
            {

                using (var conn = DALHelper.GetConnection(_tenantId))
                {
                    using (var cmd = conn.CreateCommand())
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandText = "um_EnterpriseUser_GetNodeData_Wrapper";
                        cmd.CommandTimeout = CMD_TIMEOUT;
                        cmd.Parameters.AddWithValue("@UserNum", userNum);
                        cmd.Parameters.AddWithValue("@AuthNum", authNum);
                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.TreeControl, "GetEnterpriseUserRightsData", _tenantId));
                        conn.Open();
                        using (SqlDataReader dr = cmd.ExecuteReader())
                        {
                            entTreeNodes = new List<EnterpriseNodeDTO>();
                            //1. IRLite
                            while (dr.Read())
                            {
                                #region Mapping
                                entTreeNode = new EnterpriseNodeDTO();
                                entTreeNode.RecId = Convert.ToInt32(dr["RecId"]);
                                entTreeNode.ModuleBit = ModuleBit.IRLite;
                                entTreeNode.NodeId = Convert.ToInt32(dr["SelectedExt"]);
                                entTreeNode.DbStatus = true;
                                entTreeNodes.Add(entTreeNode);
                                #endregion
                            }
                            //2. Monitor
                            dr.NextResult();
                            while (dr.Read())
                            {
                                #region Mapping
                                entTreeNode = new EnterpriseNodeDTO();
                                entTreeNode.RecId = Convert.ToInt32(dr["RecId"]);
                                entTreeNode.ModuleBit = ModuleBit.Monitor;
                                entTreeNode.NodeId = Convert.ToInt32(dr["SelectedExt"]);
                                entTreeNode.DbStatus = true;
                                entTreeNodes.Add(entTreeNode);
                                #endregion
                            }
                            //3. Search
                            dr.NextResult();
                            while (dr.Read())
                            {
                                #region Mapping
                                entTreeNode = new EnterpriseNodeDTO();
                                entTreeNode.RecId = Convert.ToInt32(dr["RecId"]);
                                entTreeNode.ModuleBit = ModuleBit.Search;
                                entTreeNode.NodeId = Convert.ToInt32(dr["SelectedExt"]);
                                entTreeNode.DbStatus = true;
                                entTreeNodes.Add(entTreeNode);
                                #endregion
                            }
                            //4. QA Evaluation
                            dr.NextResult();
                            while (dr.Read())
                            {
                                #region Mapping
                                entTreeNode = new EnterpriseNodeDTO();
                                entTreeNode.RecId = Convert.ToInt32(dr["RecId"]);
                                entTreeNode.ModuleBit = ModuleBit.Evaluation;
                                entTreeNode.NodeId = Convert.ToInt32(dr["SelectedUserNum"]);
                                entTreeNode.DbStatus = true;
                                entTreeNodes.Add(entTreeNode);
                                #endregion
                            }
                            //5. QA Evaluation Reports
                            dr.NextResult();
                            while (dr.Read())
                            {
                                #region Mapping
                                entTreeNode = new EnterpriseNodeDTO();
                                entTreeNode.RecId = Convert.ToInt32(dr["RecId"]);
                                entTreeNode.ModuleBit = ModuleBit.QAEvaluationReports;
                                entTreeNode.NodeId = Convert.ToInt32(dr["SelectedUserNum"]);
                                entTreeNode.DbStatus = true;
                                entTreeNodes.Add(entTreeNode);
                                #endregion
                            }
                            //6. Advanced Reports
                            dr.NextResult();
                            while (dr.Read())
                            {
                                #region Mapping
                                entTreeNode = new EnterpriseNodeDTO();
                                entTreeNode.RecId = Convert.ToInt32(dr["RecId"]);
                                entTreeNode.ModuleBit = ModuleBit.AdvancedReports;
                                entTreeNode.NodeId = Convert.ToInt32(dr["SelectedExt"]);
                                entTreeNode.DbStatus = true;
                                entTreeNodes.Add(entTreeNode);
                                #endregion
                            }
                            //7. IRFull
                            dr.NextResult();
                            while (dr.Read())
                            {
                                #region Mapping
                                entTreeNode = new EnterpriseNodeDTO();
                                entTreeNode.RecId = Convert.ToInt32(dr["RecId"]);
                                entTreeNode.ModuleBit = ModuleBit.IRFull;
                                entTreeNode.NodeId = Convert.ToInt32(dr["SelectedExt"]);
                                entTreeNode.DbStatus = true;
                                entTreeNodes.Add(entTreeNode);
                                #endregion
                            }
                            //8. SaveAndEmail
                            dr.NextResult();
                            while (dr.Read())
                            {
                                #region Mapping
                                entTreeNode = new EnterpriseNodeDTO();
                                entTreeNode.RecId = Convert.ToInt32(dr["RecId"]);
                                entTreeNode.ModuleBit = ModuleBit.SaveNEmail;
                                entTreeNode.NodeId = Convert.ToInt32(dr["SelectedExt"]);
                                entTreeNode.DbStatus = true;
                                entTreeNodes.Add(entTreeNode);
                                #endregion
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return entTreeNodes;
        }

        //public static bool InsertEnterpriseUserRights(bool isExtensionBased, List<int> recorders )
        //{
        //    //@IsExtensionBased = 1,
        //    //@RecId = 1,
        //    //@MainExt = 0,
        //    //@MainUserNum = 1032,
        //    //@SelectedExts = N'1001,1002,1003,1004',
        //    //@SelectedExtNames = N'',
        //    //@SelectedExtStatus = N'0,0,0,0',
        //    //@SelectedUserNums = N'',
        //    //@SelectedUserNames = N'',
        //    //@SelectedUserStatus = '',
        //    //@AccessRight = N'0000000010'

        //}

        public List<string> GetEnterpriseUserNodes(int userNum, int authNum, Recorder recorder)
        {
            List<string> enterpriseUserNodes = new List<string>();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                {
                    using (var cmd = conn.CreateCommand())
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandText = DBConstants.UserManagement.ENTERPRISEUSER_GETNODEDATA;
                        cmd.CommandTimeout = CMD_TIMEOUT;
                        cmd.Parameters.AddWithValue("@RecId", recorder.Id);
                        cmd.Parameters.AddWithValue("@UserNum", userNum);
                        cmd.Parameters.AddWithValue("@AuthNum", authNum);
                        cmd.Parameters.AddWithValue("@IsRecIdRequired", true);
                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.TreeControl, "GetEnterpriseUserNodes", _tenantId));
                        conn.Open();
                        using (SqlDataReader dr = cmd.ExecuteReader())
                        {
                            if (dr.HasRows)
                            {
                                while (dr.Read())
                                {
                                    enterpriseUserNodes.Add((authNum == 5 || authNum == 7) ? Convert.ToString(dr["SelectedUserNum"]) : Convert.ToString(dr["SelectedExt"]));
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return enterpriseUserNodes;
        }

        public List<TreeviewData> GetEnterpriseUserTree(int userNum, string userId, int authNum, string authType, int type, int userType, Recorder recorder)
        {
            //EXEC um_EnterpriseUserTree @UserNum = 1000,  @AuthNum = 4,  @AuthType = '1', @Type = 3
            List<TreeviewData> treeNodes = null;
            TreeviewData treeNode = null;
            try
            {

                using (var conn = new SqlConnection(recorder.ConnectionString))
                {
                    //System.Diagnostics.Debug.WriteLine(string.Format("{0}{1}", recorder.Id, recorder.Name));
                    using (var cmd = conn.CreateCommand())
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandText = "um_EnterpriseUserTree";
                        cmd.CommandTimeout = CMD_TIMEOUT;
                        cmd.Parameters.AddWithValue("@UserNum", userNum);
                        cmd.Parameters.AddWithValue("@UserID", userId);
                        cmd.Parameters.AddWithValue("@AuthNum", authNum);
                        cmd.Parameters.AddWithValue("@AuthType", authType);
                        cmd.Parameters.AddWithValue("@Type", type);
                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.TreeControl, "GetEnterpriseUserTree", _tenantId));
                        conn.Open();
                        using (SqlDataReader dr = cmd.ExecuteReader())
                        {
                            if (dr.HasRows)
                            {
                                treeNodes = new List<TreeviewData>();
                                while (dr.Read())
                                {
                                    treeNode = new TreeviewData();

                                    treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                    treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                    treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                    treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                    treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                    treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                    treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                    treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                    treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                    treeNode.Param3 = Convert.ToString(dr["Param3"]);
                                    treeNode.ChannelType = Convert.ToInt32(dr["ChannelType"]);

                                    treeNodes.Add(treeNode);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return treeNodes;
        }

        #endregion



        public UserManagementResponse GetAppUsers(Recorder recorder, int uId = 1000)
        {
            List<User> users = null;
            List<UserGroup> uGroups = null;
            List<Permission> permissions = null;
            List<GlobalGroup> groups = null;
            List<TreeViewDataDTO> treeViewDataDTOs = null;
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.APPUSER_GETLIST;
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.Parameters.AddWithValue("@UserId", uId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.TreeControl, "GetAppUsers", _tenantId));
                    conn.Open();
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        users = ORMapper.MapUsers(dr, recorder.Id, recorder.Name);
                        dr.NextResult();
                        uGroups = ORMapper.MapUserGroups(dr, recorder.Id);//User Permission Table
                        //dr.NextResult();
                        //userPermissions = ORMapper.MapPermissions(dr);//Create User Permissions
                        dr.NextResult();
                        groups = ORMapper.MapGlobalGroups(dr); //Group Table
                        dr.NextResult();
                        permissions = ORMapper.MapPermissions(dr); //Permissions Table
                        dr.NextResult();
                        treeViewDataDTOs = ORMapper.MapTreeViewDataDTOs(dr); //treeViewData Table
                        uGroups.ForEach(g =>
                        {

                            List<TreeViewDataDTO> tvdDTOs = treeViewDataDTOs.FindAll(tvd => tvd.Param1 == Convert.ToString(g.GroupNum) && tvd.IsGroup == false);
                            if (tvdDTOs != null && tvdDTOs.Count != 0)
                                g.TreeViewDataDTOs = tvdDTOs.Select(i => (TreeViewDataDTO)i.Clone()).ToList();
                        });
                        if (users != null)
                        {
                            users.ForEach(u => u.UserGroup = uGroups.FirstOrDefault(ug => ug.UserNum == u.UserNum));
                            users.ForEach(u =>
                            {
                                u.Permissions = GetPermissions(u.UserNum, u.UserGroup, permissions);
                                u.EnterprisePermissions = GetEnterprisePermissions(u.UserNum, u.UserGroup, permissions);
                            });
                            users.ForEach(u =>
                            {
                                GlobalGroup gGroup = groups.FirstOrDefault(g => g.Id == u.GroupNum);
                                if (gGroup != null) u.GlobalGroup = (GlobalGroup)gGroup.Clone();
                            });
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return new UserManagementResponse { Users = users, GlobalGroups = groups, Permissions = permissions, TreeViewDataDTOs = treeViewDataDTOs };
        }
        public List<User> GetAllUsersFromRecorder(Recorder recorder)
        {
            List<User> users = null;
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text; ;
                    cmd.CommandText = "SELECT * FROM t_ACCOUNT WHERE STATUS = 1 AND (EXT = 0)";
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    conn.Open();
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        users = ORMapper.MapUsersAsAgents(dr);
                        dr.NextResult();
                    }

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return users;
        }
        public List<UserInfoLite> GetUserData(Recorder recorder)
        {
            List<UserInfoLite> userInfos = new List<UserInfoLite>();
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT * FROM t_Account WHERE Status = 1 AND Ext='0';";
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.TreeControl, "GetUserData", _tenantId));

                    conn.Open();
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        UserInfoLite liteUser = null;
                        while (dr.Read())
                        {
                            liteUser = new UserInfoLite();
                            liteUser.Id = (int)dr["UserNum"];
                            liteUser.FullName = Convert.ToString(dr["UserName"]);
                            liteUser.RecId = recorder.Id;
                            userInfos.Add(liteUser);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return userInfos;
        }


        //public static UserManagementResponse GetAppUsers(List<Recorder> recorders, int uId = 1000)
        //{
        //    List<User> users = null;
        //    List<UserGroup> uGroups = null;
        //    List<Permission> permissions = null;
        //    List<GlobalGroup> groups = null;
        //    List<TreeViewDataDTO> treeViewDataDTOs = null;
        //    try
        //    {
        //        foreach (var rec in recorders)
        //        {
        //            using (var conn = new SqlConnection(rec.ConnectionString))
        //            using (var cmd = conn.CreateCommand())
        //            {
        //                cmd.CommandType = CommandType.StoredProcedure;
        //                cmd.CommandText = DBConstants.UserManagement.APPUSER_GETLIST;
        //                cmd.Parameters.AddWithValue("@UserId", uId);

        //                conn.Open();
        //                using (SqlDataReader dr = cmd.ExecuteReader())
        //                {
        //                    users = ORMapper.MapUsers(dr);
        //                    dr.NextResult();
        //                    uGroups = ORMapper.MapUserGroups(dr);//User Permission Table
        //                    //dr.NextResult();
        //                    //userPermissions = ORMapper.MapPermissions(dr);//Create User Permissions
        //                    dr.NextResult();
        //                    groups = ORMapper.MapGlobalGroups(dr); //Group Table
        //                    dr.NextResult();
        //                    permissions = ORMapper.MapPermissions(dr); //Permissions Table
        //                    dr.NextResult();
        //                    treeViewDataDTOs = ORMapper.MapTreeViewDataDTOs(dr); //treeViewData Table
        //                    uGroups.ForEach(g =>
        //                    {

        //                        List<TreeViewDataDTO> tvdDTOs = treeViewDataDTOs.FindAll(tvd => tvd.Param1 == Convert.ToString(g.GroupNum) && tvd.IsGroup == false);
        //                        if (tvdDTOs != null && tvdDTOs.Count != 0)
        //                            g.TreeViewDataDTOs = tvdDTOs.Select(i => (TreeViewDataDTO)i.Clone()).ToList();
        //                    });
        //                    if (users != null)
        //                    {
        //                        users.ForEach(u => u.UserGroup = uGroups.FirstOrDefault(ug => ug.UserNum == u.UserNum));
        //                        users.ForEach(u =>
        //                        {
        //                            u.Permissions = GetPermissions(u.UserNum, u.UserGroup, permissions);

        //                        });
        //                        users.ForEach(u =>
        //                        {
        //                            GlobalGroup gGroup = groups.FirstOrDefault(g => g.Id == u.GroupNum);
        //                            if (gGroup != null) u.GlobalGroup = (GlobalGroup)gGroup.Clone();
        //                        });
        //                    }
        //                }
        //            }
        //        }
        //    }
        //    catch (Exception ex) { throw ex; }
        //    return new UserManagementResponse { Users = users, GlobalGroups = groups, Permissions = permissions, TreeViewDataDTOs = treeViewDataDTOs };
        //}


        //public UserManagementResponse GetAppUsers( int uId = 1000)
        //{
        //    List<User> users = null;
        //    List<UserGroup> uGroups = null;
        //    List<Permission> permissions = null;
        //    List<GlobalGroup> groups = null;
        //    List<TreeViewDataDTO> treeViewDataDTOs = null;
        //    try
        //    {
        //        using (var conn = DALHelper.GetConnection())
        //        using (var cmd = conn.CreateCommand())
        //        {
        //            cmd.CommandType = CommandType.StoredProcedure;
        //            cmd.CommandText = DBConstants.UserManagement.APPUSER_GETLIST;
        //            cmd.Parameters.AddWithValue("@UserId", uId);

        //            conn.Open();
        //            using (SqlDataReader dr = cmd.ExecuteReader())
        //            {
        //                users = ORMapper.MapUsers(dr);
        //                dr.NextResult();
        //                uGroups = ORMapper.MapUserGroups(dr);//User Permission Table
        //                //dr.NextResult();
        //                //userPermissions = ORMapper.MapPermissions(dr);//Create User Permissions
        //                dr.NextResult();
        //                groups = ORMapper.MapGlobalGroups(dr); //Group Table
        //                dr.NextResult();
        //                permissions = ORMapper.MapPermissions(dr); //Permissions Table
        //                dr.NextResult();
        //                treeViewDataDTOs = ORMapper.MapTreeViewDataDTOs(dr); //treeViewData Table
        //                uGroups.ForEach(g =>
        //                {

        //                    List<TreeViewDataDTO> tvdDTOs = treeViewDataDTOs.FindAll(tvd => tvd.Param1 == Convert.ToString(g.GroupNum) && tvd.IsGroup == false);
        //                    if (tvdDTOs != null && tvdDTOs.Count != 0)
        //                        g.TreeViewDataDTOs = tvdDTOs.Select(i => (TreeViewDataDTO)i.Clone()).ToList();
        //                });
        //                if (users != null)
        //                {
        //                    users.ForEach(u => u.UserGroup = uGroups.FirstOrDefault(ug => ug.UserNum == u.UserNum));
        //                    users.ForEach(u =>
        //                    {
        //                        u.Permissions = this.GetPermissions(u.UserNum, u.UserGroup, permissions);

        //                    });
        //                    users.ForEach(u =>
        //                    {
        //                        GlobalGroup gGroup = groups.FirstOrDefault(g => g.Id == u.GroupNum);
        //                        if (gGroup != null) u.GlobalGroup = (GlobalGroup)gGroup.Clone();
        //                    });
        //                }
        //            }
        //        }
        //    }
        //    catch (Exception ex) { throw ex; }
        //    return new UserManagementResponse { Users = users, GlobalGroups = groups, Permissions = permissions, TreeViewDataDTOs = treeViewDataDTOs };
        //}



        #region Utility Methods

        private static List<Permission> GetPermissions(int uNum, UserGroup uGroup, List<Permission> permissions)
        {
            List<Permission> uPermissions = new List<Permission>();
            Permission uPermission = null;
            if (uGroup == null)
                return new List<Permission>();
            foreach (var p in permissions)
            {
                if (Convert.ToInt32(uGroup.AssignAuth.Substring(p.Id, 1)) == 1)
                {
                    uPermission = (Permission)p.Clone();
                    uPermission.UserNum = uNum;
                    uPermissions.Add(uPermission);
                }

            }
            return uPermissions;

        }

        private static List<Permission> GetEnterprisePermissions(int uNum, UserGroup uGroup, List<Permission> permissions)
        {
            List<Permission> uPermissions = new List<Permission>();
            Permission uEntPermission = null;
            if (uGroup == null)
                return new List<Permission>();
            foreach (var p in permissions)
            {
                if (Convert.ToInt32(uGroup.EnterpriseAssignAuth.Substring(p.Id, 1)) == 1)
                {
                    uEntPermission = (Permission)p.Clone();
                    uEntPermission.UserNum = uNum;
                    uPermissions.Add(uEntPermission);
                }

            }
            return uPermissions;
        }

        #endregion

        public List<TreeviewData> GetGroupsTreeFromRecorder(Recorder recorder, int userNum, string userId, int authNum, string authType, int type, int userType)
        {
            List<TreeviewData> treeNodes = null;
            TreeviewData treeNode = null;
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.TREEVIEW_GET_ADMIN;
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@UserID", userId);
                    cmd.Parameters.AddWithValue("@AuthNum", authNum);
                    cmd.Parameters.AddWithValue("@AuthType", authType);
                    cmd.Parameters.AddWithValue("@Type", type);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.TreeControl, "GetGroupsTreeFromRecorder", _tenantId));

                    conn.Open();
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            treeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);

                                treeNodes.Add(treeNode);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return treeNodes;
        }

        public List<TreeviewData> GetGroupsTreeNonAdminFromRecorder(Recorder recorder, int userNum, string userId, int authNum, string authType, int type, int userType)
        {
            List<TreeviewData> treeNodes = null;
            TreeviewData treeNode = null;
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = userType == 0 ? DBConstants.UserManagement.TREEVIEW_GET_ADDITIONAL : DBConstants.UserManagement.TREEVIEW_GET_SIMPLE;
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@UserID", userId);
                    cmd.Parameters.AddWithValue("@AuthNum", authNum);
                    cmd.Parameters.AddWithValue("@AuthType", authType);
                    cmd.Parameters.AddWithValue("@Type", type);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.TreeControl, "GetGroupsTreeNonAdminFromRecorder", _tenantId));

                    conn.Open();
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            treeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);

                                treeNodes.Add(treeNode);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return treeNodes;
        }

        public List<TreeviewData> GetUsersTreeFromRecorder(Recorder recorder, int userNum)
        {
            List<TreeviewData> treeNodes = null;
            TreeviewData treeNode = null;
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = "sp_Init_User_Tree";
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.TreeControl, "GetUsersTreeFromRecorder", _tenantId));

                    conn.Open();
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            treeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);
                                treeNode.Childrens = new List<TreeviewData>();

                                treeNodes.Add(treeNode);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return treeNodes;
        }
        public List<TreeviewData> GetInquireGroupsTreeFromRecorder(Recorder recorder, int userNum, string userId, int authNum, string authType, int type, int userType)
        {
            List<TreeviewData> treeNodes = null;
            TreeviewData treeNode = null;
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.TREEVIEW_GET_ADMIN;
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@UserID", userId);
                    cmd.Parameters.AddWithValue("@AuthNum", authNum);
                    cmd.Parameters.AddWithValue("@AuthType", authType);
                    cmd.Parameters.AddWithValue("@Type", type);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.TreeControl, "GetInquireGroupsTreeFromRecorder", _tenantId));

                    conn.Open();
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            treeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);

                                treeNodes.Add(treeNode);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return treeNodes;
        }

        public Tuple<List<int>, List<int>> GetExtensionsNAgentsByGroupId(int groupId, Recorder recorder)
        {
            Tuple<List<int>, List<int>> tuple = null;
            List<int> extensions = new List<int>();
            List<int> agents = new List<int>();
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.ENTERPRISEUSER_GETEXTNAGENT_BYGROUPID;
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.Parameters.AddWithValue("@GroupId", groupId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.TreeControl, "GetExtensionsNAgentsByGroupId", _tenantId));

                    conn.Open();
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        //1. Extensions
                        while (dr.Read())
                        {
                            extensions.Add(Convert.ToInt32(dr["Ext"]));
                        }
                        //2. Agents
                        dr.NextResult();
                        while (dr.Read())
                        {
                            agents.Add(Convert.ToInt32(dr["UserNum"]));
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            tuple = new Tuple<List<int>, List<int>>(extensions, agents);
            return tuple;
        }
        public int SaveEnterpriseUserRights(string xmlEnterpriseUserRights, bool isExtensionBased = true)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = isExtensionBased == true ? DBConstants.UserManagement.ENTERPRISEUSER_SET_RIGHTS : "um_EnterpriseUser_GroupRight_Insert";
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.Parameters.AddWithValue("@PermissionXML", xmlEnterpriseUserRights);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.TreeControl, "SaveEnterpriseUserRights", _tenantId));

                    conn.Open();
                    return cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public int SaveEnterpriseGroupRights(string xmlEnterpriseUserRights, string xmlEnterpriseTabRights)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.ENTERPRISEUSER_SET_GROUP_RIGHTS;
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.Parameters.AddWithValue("@PermissionXML", xmlEnterpriseUserRights);
                    cmd.Parameters.AddWithValue("@TabPermissionXML", xmlEnterpriseTabRights);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.TreeControl, "SaveEnterpriseGroupRights", _tenantId));

                    conn.Open();
                    return cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public int SaveEnterpriseUserRecordersGroup(int recId, int userNum, int groupNum, bool isEmptyPermission)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.ENTERPRISEUSER_RECORDERS_Group;
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.Parameters.AddWithValue("@RecId", recId);
                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@GroupNum", groupNum);
                    cmd.Parameters.AddWithValue("@IsEmptyPermission", isEmptyPermission);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.TreeControl, "SaveEnterpriseUserRecordersGroup", _tenantId));

                    conn.Open();
                    return cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public List<TreeviewData> GetInquireGroupsTreeforEvaluationReportFromRecorder(Recorder recorder, int userNum, int selectType, int roleId = 0, int roleType = 0)
        {
            int userType = 0;
            List<TreeviewData> treeNodes = null;
            TreeviewData treeNode = null;
            try
            {
                string ConnString = recorder.ConnectionString;
                if (_tenantId > 0)
                {
                    if (!string.IsNullOrEmpty(MTConnectionHost))
                    {
                        ConnString = recorder.ConnectionString.Replace(@".\Revcord;", MTConnectionHost).Replace(@".\REVCORD;", MTConnectionHost);
                    }
                }

                using (var conn = new SqlConnection(ConnString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    if (roleId > 0)
                    {
                        cmd.CommandText = roleType == 1 ? DBConstants.UserManagement.TREEVIEW_GET_GROUPBASED : DBConstants.UserManagement.TREEVIEW_GET_CHANNELBASED;
                        cmd.Parameters.AddWithValue("@RoleId", roleId);
                        cmd.Parameters.AddWithValue("@UserNum", userNum);
                        cmd.Parameters.AddWithValue("@AuthNum", 7);
                        cmd.Parameters.AddWithValue("@AuthType", 1);
                        cmd.Parameters.AddWithValue("@Type", 11);
                    }
                    else
                    {
                        cmd.CommandText = selectType == 0 ? DBConstants.UserManagement.TREEVIEW_GET : DBConstants.UserManagement.TREEVIEW_SIMPLE_USER_RIGHTS_GET;
                        cmd.Parameters.AddWithValue("@UserNum", userNum);
                        cmd.Parameters.AddWithValue("@UserID", "");
                        if (selectType == 1)
                            cmd.Parameters.AddWithValue("@AuthNum", 7);
                        else
                            cmd.Parameters.AddWithValue("@AuthNum", 4);
                        cmd.Parameters.AddWithValue("@AuthType", 1);
                        cmd.Parameters.AddWithValue("@Type", 11);
                    }
                    cmd.CommandTimeout = CMD_TIMEOUT;

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.TreeControl, "GetInquireGroupsTreeforEvaluationReportFromRecorder", _tenantId));

                    conn.Open();
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            treeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);

                                treeNodes.Add(treeNode);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return treeNodes;
        }

        public List<TreeviewData> GetMDGroupsTreeForEvaluationReportFromRecorder(Recorder recorder, int userNum, int selectType)
        {
            int userType = 0;
            List<TreeviewData> treeNodes = null;
            TreeviewData treeNode = null;
            try
            {
                string ConnString = recorder.ConnectionString;
                if (_tenantId > 0)
                {
                    if (!string.IsNullOrEmpty(MTConnectionHost))
                    {
                        ConnString = recorder.ConnectionString.Replace(@".\Revcord;", MTConnectionHost).Replace(@".\REVCORD;", MTConnectionHost);
                    }
                }

                using (var conn = new SqlConnection(ConnString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = selectType == 0 ? DBConstants.UserManagement.TREEVIEW_GET : DBConstants.UserManagement.TREEVIEW_SIMPLE_USER_RIGHTS_GET;
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@UserID", "");
                    cmd.Parameters.AddWithValue("@AuthNum", 7);
                    cmd.Parameters.AddWithValue("@AuthType", 1);
                    cmd.Parameters.AddWithValue("@Type", 16);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.TreeControl, "GetMDGroupsTreeForEvaluationReportFromRecorder", _tenantId));

                    conn.Open();
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            treeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);

                                treeNodes.Add(treeNode);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return treeNodes;
        }

        public List<TreeviewData> GetRVITree(int userNum, Recorder recorder)
        {
            //EXEC [dbo].[rvi_Tree] @UserNum = 1340
            List<TreeviewData> treeNodes = null;
            TreeviewData treeNode = null;
            try
            {
                string ConnString = recorder.ConnectionString;
                if (_tenantId > 0)
                {
                    if (!string.IsNullOrEmpty(MTConnectionHost))
                    {
                        ConnString = recorder.ConnectionString.Replace(@".\Revcord;", MTConnectionHost).Replace(@".\REVCORD;", MTConnectionHost);
                    }
                }

                using (var conn = new SqlConnection(ConnString))
                {
                    using (var cmd = conn.CreateCommand())
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandText = DBConstants.UserManagement.TREEVIEW_GET_RVI_TREE;
                        cmd.CommandTimeout = CMD_TIMEOUT;
                        cmd.Parameters.AddWithValue("@UserNum", userNum);
                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.TreeControl, "GetRVITree", _tenantId));
                        conn.Open();
                        using (SqlDataReader dr = cmd.ExecuteReader())
                        {
                            if (dr.HasRows)
                            {
                                treeNodes = new List<TreeviewData>();
                                while (dr.Read())
                                {
                                    treeNode = new TreeviewData();

                                    treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                    treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                    treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                    treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                    treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                    treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                    treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                    treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                    treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                    treeNode.Param3 = Convert.ToString(dr["Param3"]);
                                    treeNode.IsRevCell = Convert.ToBoolean(dr["IsRevCell"]);
                                    treeNode.IsRevAgentAssociated = Convert.ToBoolean(dr["IsRevAgentAssociated"]);
                                    treeNodes.Add(treeNode);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return treeNodes;
        }
    }
}
