﻿using RevCord.DataAccess.RevLogSvc;
using RevCord.DataContracts;
using RevCord.Util;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataAccess.Util
{
    public static class RevAuditLogger
    {
        static string revLogServiceURL = SiteConfig.RevLogServiceURL;
        static string revLogSvcFromWebConfig = AppSettingsUtil.GetString("revLogServiceURL", "http://localhost/RevLogService/RevLogSvc.svc");

        public static void WriteSQL(string sql, Originator originator, string functionName, int tenantId, int userId = 0)
        {
            if (SiteConfig.IsRevLogEnabled)
            {
                RevLogSvcClient rlSvcClient = new RevLogSvcClient();
                rlSvcClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(revLogServiceURL));
                rlSvcClient.AddSuccessAuditLogAsync(AuditAppType.MMS, AuditLogCategory.Information, originator.GetDescription(), functionName, tenantId, userId, "SQL Query generated successfully.", sql);
            }
        }

        public static void WriteError(Originator originator, string functionName, int tenantId, string message, string sql ="", int userId = 0)
        {
            if (SiteConfig.IsRevLogEnabled)
            {
                RevLogSvcClient rlSvcClient = new RevLogSvcClient();
                rlSvcClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(revLogServiceURL));
                rlSvcClient.AddErrorAuditLogAsync(AuditAppType.MMS, AuditLogCategory.Error, originator.GetDescription(), functionName, tenantId, message, sql, userId);
            }
        }

        public static void WriteSQLException(Originator originator, string functionName, int tenantId,  string message, string stackTrace, int errorCode, string sql, int userId = 0)
        {
            if (SiteConfig.IsRevLogEnabled)
            {
                RevLogSvcClient rlSvcClient = new RevLogSvcClient();
                rlSvcClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(revLogServiceURL));
                rlSvcClient.AddSQLExceptionAuditLogAsync(AuditAppType.MMS, AuditLogCategory.SQLException, originator.GetDescription(), functionName, tenantId, userId, message, stackTrace, errorCode, sql);
            }
        }

        public static void WriteException(Originator originator, string functionName, int tenantId, string message, string stackTrace, int errorCode, string sql, int userId = 0)
        {
            if (SiteConfig.IsRevLogEnabled)
            {
                RevLogSvcClient rlSvcClient = new RevLogSvcClient();
                rlSvcClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(revLogServiceURL));
                rlSvcClient.AddExceptionAuditLogAsync(AuditAppType.MMS, AuditLogCategory.Exception, originator.GetDescription(), functionName, tenantId, userId, message, stackTrace, errorCode);
            }
        }

        public static void WriteSuccess(Originator originator, string functionName, int tenantId, string successMessage)
        {
            if (SiteConfig.IsRevLogEnabled)
            {
                RevLogSvcClient rlSvcClient = new RevLogSvcClient();
                rlSvcClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(revLogServiceURL));
                rlSvcClient.AddSuccessAuditLog(AuditAppType.MMS, AuditLogCategory.Success, originator.GetDescription(), functionName, tenantId, 0, successMessage, string.Empty);
            }
        }

        public static void WriteInformation(Originator originator, string functionName, int tenantId, string infoMessage)
        {
            if (SiteConfig.IsRevLogEnabled)
            {
                RevLogSvcClient rlSvcClient = new RevLogSvcClient();
                rlSvcClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(revLogServiceURL));
                rlSvcClient.AddSuccessAuditLog(AuditAppType.MMS, AuditLogCategory.Information, originator.GetDescription(), functionName, tenantId, 0, infoMessage, string.Empty);
            }
        }

        public static void PSQL(string sql, Originator originator, string functionName, int tenantId, int userId = 0)
        {
            RevLogSvcClient rlSvcClient = new RevLogSvcClient();
            rlSvcClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(revLogSvcFromWebConfig));
            rlSvcClient.AddSuccessAuditLogAsync(AuditAppType.EnterpriseService, AuditLogCategory.Information, originator.GetDescription(), functionName, tenantId, userId, "SQL Query generated successfully.", sql);
        }

        public static void PutError(Originator originator, string functionName, int tenantId, string message, string sql = "", int userId = 0)
        {
            RevLogSvcClient rlSvcClient = new RevLogSvcClient();
            rlSvcClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(revLogSvcFromWebConfig));
            rlSvcClient.AddErrorAuditLogAsync(AuditAppType.EnterpriseService, AuditLogCategory.Error, originator.GetDescription(), functionName, tenantId, message, sql, userId);
        }

        public static void PutSQLException(Originator originator, string functionName, int tenantId, string message, string stackTrace, int errorCode, string sql, int userId = 0)
        {
            RevLogSvcClient rlSvcClient = new RevLogSvcClient();
            rlSvcClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(revLogSvcFromWebConfig));
            rlSvcClient.AddSQLExceptionAuditLogAsync(AuditAppType.EnterpriseService, AuditLogCategory.SQLException, originator.GetDescription(), functionName, tenantId, userId, message, stackTrace, errorCode, sql);
        }

        public static void PutException(Originator originator, string functionName, int tenantId, string message, string stackTrace, int errorCode, string sql, int userId = 0)
        {
            RevLogSvcClient rlSvcClient = new RevLogSvcClient();
            rlSvcClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(revLogSvcFromWebConfig));
            rlSvcClient.AddExceptionAuditLogAsync(AuditAppType.EnterpriseService, AuditLogCategory.Exception, originator.GetDescription(), functionName, tenantId, userId, message, stackTrace, errorCode);
        }

        public static void PutSuccess(Originator originator, string functionName, int tenantId, string successMessage)
        {
            RevLogSvcClient rlSvcClient = new RevLogSvcClient();
            rlSvcClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(revLogSvcFromWebConfig));
            rlSvcClient.AddSuccessAuditLog(AuditAppType.EnterpriseService, AuditLogCategory.Success, originator.GetDescription(), functionName, tenantId, 0, successMessage, string.Empty);
        }

        public static void PutInformation(Originator originator, string functionName, int tenantId, string infoMessage)
        {
            RevLogSvcClient rlSvcClient = new RevLogSvcClient();
            rlSvcClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(revLogSvcFromWebConfig));
            rlSvcClient.AddSuccessAuditLog(AuditAppType.EnterpriseService, AuditLogCategory.Information, originator.GetDescription(), functionName, tenantId, 0, infoMessage, string.Empty);
        }
    }
}
