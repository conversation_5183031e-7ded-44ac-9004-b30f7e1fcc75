﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.VoiceRecEntities;
using System.Data.SqlClient;
using System.Data;
using System.Diagnostics;
using RevCord.DataContracts;
using RevCord.DataContracts.Criteria;
using RevCord.Util;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using RevCord.DataAccess.Util;

namespace RevCord.DataAccess
{
    public class VoiceLoggingDALEC
    {
        //private static List<Recorder> recorders = new List<Recorder> 
        //{ 
        //    new Recorder { Id = 1, Name = "LocalRec", IP = "***********", ConnectionString = @"Data Source=.;Initial Catalog=VoiceRec_R9.2;Integrated Security=SSPI" },
        //    new Recorder { Id = 2, Name = "SarfrazRec", IP = "***********", ConnectionString = @"Data Source=Dell\REVCORD,49172;Initial Catalog=VoiceRec;User ID=sa;Password=******;Persist Security Info=True;" } 
        //};
        private static int CMD_TIMEOUT = AppSettingsUtil.GetInt("commandTimeout", 60);
        private int _tenantId;
        public VoiceLoggingDALEC(int tenantId)
        {
            this._tenantId = tenantId;
        }


        public static List<CallInfo> SearchCallsPrimaryDB(int pageSize, int pageIndex, out int totalPages, out long totalRecords, string duration, string criteria, CallCriteria callCriteria, Recorder recorder)
        {
            CallInfo callInfo = null;
            List<CallInfo> calls = null;
            try
            {
                System.Diagnostics.Debug.WriteLine(string.Format("{0}-{1}-{2}", recorder.Id, recorder.Name, recorder.ConnectionString));
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_AND_GET_PRIMARY_DB;

                    cmd.Parameters.AddWithValue("@StartDate", callCriteria.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", callCriteria.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@StartTime", callCriteria.StartTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@EndTime", callCriteria.EndTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@IsGlobalSearch", callCriteria.SearchType == SearchType.Global ? true : false);
                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria);

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "SearchCallsPrimaryDB", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows) calls = new List<CallInfo>();
                        //////while (dr.Read())
                        //////{
                        //////    callInfo = new CallInfo();

                        //////    #region --- Mapping ---

                        //////    callInfo.RecorderId = recorder.Id;
                        //////    callInfo.RecorderName = recorder.Name;

                        //////    callInfo.RowNo = (long)dr["RowNo"];
                        //////    callInfo.CallId = Convert.ToString(dr["CallID"]);
                        //////    callInfo.UniqueId = Convert.ToInt64(dr["UniqueId"]);
                        //////    callInfo.RecorderIP = recorder.IP;
                        //////    //callInfo.RecorderName = Convert.ToString(dr["RecName"]);
                        //////    callInfo.GroupId = Convert.ToInt32(dr["GroupNum"]);
                        //////    callInfo.GroupName = Convert.ToString(dr["GroupName"]);
                        //////    callInfo.AgentId = Convert.ToInt32(dr["UserNum"]);
                        //////    callInfo.ChannelId = Convert.ToInt32(dr["Ext"]);
                        //////    callInfo.ChannelName = Convert.ToString(dr["ExtName"]);
                        //////    callInfo.CallType = Convert.ToInt32(dr["CallType"]);
                        //////    //callInfo.StartTime = Convert.ToDateTime(dr["StartTime"]);
                        //////    callInfo.StartTime = dr["StartTime"].ToString().ConvertStringDateTimeToDateTime();
                        //////    callInfo.StartTimeString = Convert.ToString(dr["StartTime"]);
                        //////    callInfo.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);
                        //////    callInfo.FileName = Convert.ToString(dr["FileName"]);
                        //////    callInfo.CustName = Convert.ToString(dr["CustName"]);
                        //////    callInfo.ANI = Convert.ToString(dr["ANI"]);
                        //////    callInfo.CallerID = Convert.ToString(dr["CallerID"]);
                        //////    callInfo.CalledID = Convert.ToString(dr["CalledID"]);
                        //////    callInfo.SGroupID = dr.IsDBNull(dr.GetOrdinal("SGroupID")) ? 0 : Convert.ToInt32(dr["SGroupID"]);
                        //////    callInfo.Tag1 = Convert.ToString(dr["Tag1"]);
                        //////    callInfo.Tag2 = Convert.ToString(dr["Tag2"]);
                        //////    callInfo.Tag3 = Convert.ToString(dr["Tag3"]);
                        //////    callInfo.Tag4 = Convert.ToString(dr["Tag4"]);
                        //////    callInfo.Tag5 = Convert.ToString(dr["Tag5"]);
                        //////    callInfo.Tag6 = Convert.ToString(dr["Tag6"]);
                        //////    callInfo.Tag7 = Convert.ToString(dr["Tag7"]);
                        //////    callInfo.Tag8 = Convert.ToString(dr["Tag8"]);
                        //////    callInfo.Tag9 = Convert.ToString(dr["Tag9"]);
                        //////    callInfo.Tag10 = Convert.ToString(dr["Tag10"]);
                        //////    callInfo.Tag11 = Convert.ToString(dr["Tag11"]);
                        //////    callInfo.Tag12 = Convert.ToString(dr["Tag12"]);
                        //////    callInfo.Tag13 = Convert.ToString(dr["Tag13"]);
                        //////    callInfo.Tag14 = Convert.ToString(dr["Tag14"]);
                        //////    callInfo.Tag15 = Convert.ToString(dr["Tag15"]);
                        //////    callInfo.Tag16 = Convert.ToString(dr["Tag16"]);
                        //////    callInfo.ANIPhone = Convert.ToString(dr["ANI_PH"]);
                        //////    callInfo.ANIName = Convert.ToString(dr["ANI_NAME"]);
                        //////    callInfo.ANIDetails = Convert.ToString(dr["ANI_DETAILS"]);
                        //////    callInfo.CallComments = Convert.ToString(dr["CALL_COMMENT"]);
                        //////    //callInfo.ScreenRecFile = Convert.ToString(dr["Screen_Rec_File"]);
                        //////    callInfo.ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]);// dr.IsDBNull(dr.GetOrdinal("ScreenFileNames")) ? null : Convert.ToString(dr["ScreenFileNames"]).Split(',').ToList();
                        //////    callInfo.RetainValue = Convert.ToInt32(dr["RetainValue"]);
                        //////    callInfo.interview_DateTime = Convert.ToString(dr["DateTime"]);
                        //////    callInfo.interview_Interviewee = Convert.ToString(dr["Interviewer"]);
                        //////    callInfo.interview_MdInterviewee = dr.IsDBNull(dr.GetOrdinal("MDInterviewee")) ? null : Convert.ToString(dr["MDInterviewee"]); //Convert.ToString(dr["MdInterviewee"]);
                        //////    callInfo.interview_Interviewee = callInfo.CallType == 7 ? callInfo.interview_Interviewee : callInfo.interview_MdInterviewee;
                        //////    callInfo.interview_Interviewer = Convert.ToString(dr["Interviewee"]);
                        //////    callInfo.interview_InterviewId = Convert.ToString(dr["InterviewId"]);
                        //////    callInfo.interview_GPS = Convert.ToString(dr["GPS"]);
                        //////    //if (callInfo.ScreenFileNames != null)//if (!string.IsNullOrEmpty(callInfo.ScreenRecFile))//TODO: delete this in version 10
                        //////    //    callInfo.CallType = 6;
                        //////    callInfo.interview_Notes = Convert.ToString(dr["Notes"]);

                        //////    if (callInfo.CallType != 7)
                        //////    {
                        //////        callInfo.CallType_inq = callInfo.CallType;

                        //////    }
                        //////    else
                        //////    {
                        //////        if (Regex.IsMatch(callInfo.FileName, "mp4", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "mov", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "m4v", RegexOptions.IgnoreCase))
                        //////        {
                        //////            callInfo.CallType_inq = 8;
                        //////        }
                        //////        else
                        //////        {
                        //////            callInfo.CallType_inq = 7;
                        //////        }
                        //////    }

                        //////    callInfo.BookmarkCSV = "";
                        //////    if (dr.FieldExists("BookMarkXML"))
                        //////    {
                        //////        callInfo.BookmarkXML = Convert.ToString(dr["BookMarkXML"]);
                        //////        if (!String.IsNullOrEmpty(callInfo.BookmarkXML))
                        //////        {
                        //////            callInfo.BookmarkCSV = callInfo.BookmarkXML.ConvertXmlStringToCsvString();
                        //////        }
                        //////    }
                        //////    #endregion

                        //////    calls.Add(callInfo);
                        //////}
                        while (dr.Read())
                        {
                            callInfo = new CallInfo();

                            #region --- Mapping ---

                            callInfo.RecorderId = recorder.Id;
                            callInfo.RecorderName = recorder.Name;

                            callInfo.RowNo = (long)dr["RowNo"];

                            callInfo.CallId = Convert.ToString(dr["CallID"]);
                            callInfo.UniqueId = Convert.ToInt64(dr["UniqueId"]);
                            callInfo.RecorderId = Convert.ToInt32(dr["RecID"]);
                            callInfo.RecorderIP = recorder.IP;
                            //if (!dr.IsDBNull(dr.GetOrdinal("RecName")))
                            //    callInfo.RecorderName = dr.GetString(dr.GetOrdinal("RecName"));
                            callInfo.RecorderName = Convert.ToString(dr["RecName"]);
                            callInfo.GroupId = Convert.ToInt32(dr["GroupNum"]);
                            callInfo.GroupName = Convert.ToString(dr["GroupName"]);
                            callInfo.AgentId = Convert.ToInt32(dr["UserNum"]);
                            callInfo.ChannelId = Convert.ToInt32(dr["Ext"]);
                            callInfo.ChannelName = Convert.ToString(dr["ExtName"]);
                            callInfo.CallType = Convert.ToInt32(dr["CallType"]);
                            //callInfo.StartTime = Convert.ToDateTime(dr["StartTime"]);
                            callInfo.StartTime = dr["StartTime"].ToString().ConvertStringDateTimeToDateTime();
                            callInfo.StartTimeString = Convert.ToString(dr["StartTime"]);
                            callInfo.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);
                            callInfo.FileName = Convert.ToString(dr["FileName"]);

                            if (callInfo.CallType != 7)
                            {
                                callInfo.CallType_inq = callInfo.CallType;

                            }
                            else
                            {
                                if (Regex.IsMatch(callInfo.FileName, "mp4", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "mov", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "m4v", RegexOptions.IgnoreCase))
                                {
                                    callInfo.CallType_inq = 8;
                                }
                                else if (Regex.IsMatch(callInfo.FileName, "zip", RegexOptions.IgnoreCase))
                                {
                                    callInfo.CallType_inq = 12;
                                }
                                else
                                {
                                    callInfo.CallType_inq = 7;
                                }
                            }

                            callInfo.CustName = Convert.ToString(dr["CustName"]);
                            callInfo.ANI = Convert.ToString(dr["ANI"]);
                            callInfo.CallerID = Convert.ToString(dr["CallerID"]);
                            callInfo.CalledID = Convert.ToString(dr["CalledID"]);
                            callInfo.SGroupID = dr.IsDBNull(dr.GetOrdinal("SGroupID")) ? 0 : Convert.ToInt32(dr["SGroupID"]);
                            callInfo.Tag1 = Convert.ToString(dr["Tag1"]);
                            callInfo.Tag2 = Convert.ToString(dr["Tag2"]);
                            callInfo.Tag3 = Convert.ToString(dr["Tag3"]);
                            callInfo.Tag4 = Convert.ToString(dr["Tag4"]);
                            callInfo.Tag5 = Convert.ToString(dr["Tag5"]);
                            callInfo.Tag6 = Convert.ToString(dr["Tag6"]);
                            callInfo.Tag7 = Convert.ToString(dr["Tag7"]);
                            callInfo.Tag8 = Convert.ToString(dr["Tag8"]);
                            callInfo.Tag9 = Convert.ToString(dr["Tag9"]);
                            callInfo.Tag10 = Convert.ToString(dr["Tag10"]);
                            callInfo.Tag11 = Convert.ToString(dr["Tag11"]);
                            callInfo.Tag12 = Convert.ToString(dr["Tag12"]);
                            callInfo.Tag13 = Convert.ToString(dr["Tag13"]);
                            callInfo.Tag14 = Convert.ToString(dr["Tag14"]);
                            callInfo.Tag15 = Convert.ToString(dr["Tag15"]);
                            callInfo.Tag16 = Convert.ToString(dr["Tag16"]);
                            callInfo.IsRevCell = Convert.ToBoolean(dr["IsRevCell"]);

                            callInfo.ANIPhone = Convert.ToString(dr["ANI_PH"]);
                            callInfo.ANIName = Convert.ToString(dr["ANI_NAME"]);
                            callInfo.ANIDetails = Convert.ToString(dr["ANI_DETAILS"]);
                            callInfo.CallComments = Convert.ToString(dr["CALL_COMMENT"]);
                            callInfo.MessageBody = Convert.ToString(dr["Tag2"]);
                            //callInfo.ScreenRecFile = Convert.ToString(dr["Screen_Rec_File"]);
                            callInfo.ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]);//dr.IsDBNull(dr.GetOrdinal("ScreenFileNames")) ? null : Convert.ToString(dr["ScreenFileNames"]).Split(',').ToList();

                            callInfo.RetainValue = Convert.ToInt32(dr["RetainValue"]);

                            callInfo.Transcription = Convert.ToString(dr["Transcription"]);
                            callInfo.TranscriptionId = dr["TranscriptionId"] == DBNull.Value ? 0 : Convert.ToInt32(dr["TranscriptionId"]);
                            callInfo.Confidence = dr["Confidence"] == DBNull.Value ? 0 : Convert.ToDecimal(dr["Confidence"]);

                            callInfo.IsSyncedFromClient = Convert.ToInt32(dr["IsSyncedFromClient"]);

                            callInfo.interview_DateTime = Convert.ToString(dr["DateTime"]);
                            callInfo.interview_Interviewee = Convert.ToString(dr["Interviewee"]);
                            callInfo.interview_MdInterviewee = dr.IsDBNull(dr.GetOrdinal("MDInterviewee")) ? null : Convert.ToString(dr["MDInterviewee"]); //Convert.ToString(dr["MdInterviewee"]);

                            callInfo.interview_Interviewee = callInfo.CallType == 7 ? callInfo.interview_Interviewee : callInfo.interview_MdInterviewee;

                            callInfo.interview_Interviewer = Convert.ToString(dr["Interviewer"]);
                            callInfo.interview_InterviewId = Convert.ToString(dr["InterviewId"]);
                            callInfo.interview_GPS = Convert.ToString(dr["GPS"]);
                            callInfo.interview_Notes = Convert.ToString(dr["Notes"]);

                            callInfo.ErrorInMuxProcess = 0;
                            if (dr["ErrorinMuxProcess"] != DBNull.Value)
                                callInfo.ErrorInMuxProcess = Convert.ToInt32(dr["ErrorinMuxProcess"]);

                            callInfo.BookmarkCSV = string.Empty;
                            if (dr.FieldExists("BookMarkXML"))
                            {
                                callInfo.BookmarkXML = Convert.ToString(dr["BookMarkXML"]);
                                if (!String.IsNullOrEmpty(callInfo.BookmarkXML))
                                {
                                    callInfo.BookmarkCSV = callInfo.BookmarkXML.Inquire_ConvertXmlStringToCsvString(callInfo.CallType);
                                }
                            }
                            callInfo.IsPictureEvent = !DBRecordExtensions.HasColumn(dr, "IsPictureEvent") ? false : Convert.ToBoolean(dr["IsPictureEvent"]);
                            callInfo.IsVirtualInspection = !DBRecordExtensions.HasColumn(dr, "IsVirtualInspection") ? false : Convert.ToBoolean(dr["IsVirtualInspection"]);
                            callInfo.IsRevView = !DBRecordExtensions.HasColumn(dr, "IsRevView") ? false : Convert.ToBoolean(dr["IsRevView"]);
                            callInfo.RevViewFileName = DBRecordExtensions.HasColumn(dr, "RevViewFileName") ? Convert.ToString(dr["RevViewFileName"]) : string.Empty;
                            callInfo.RevViewStartTime = DBRecordExtensions.HasColumn(dr, "RevViewStartTime") ? Convert.ToString(dr["RevViewStartTime"]) : string.Empty;
                            callInfo.RevViewPhoneNumber = DBRecordExtensions.HasColumn(dr, "RevViewPhoneNumber") ? Convert.ToString(dr["RevViewPhoneNumber"]) : string.Empty;
                            callInfo.RevViewAgentName = DBRecordExtensions.HasColumn(dr, "RevViewAgentName") ? Convert.ToString(dr["RevViewAgentName"]) : string.Empty;
                            callInfo.STTFileName = DBRecordExtensions.HasColumn(dr, "STTFilePath") ? Convert.ToString(dr["STTFilePath"]) : string.Empty;
                            callInfo.VesselId = Convert.ToString(dr["VesselId"]);
                            callInfo.AssetId = DBRecordExtensions.HasColumn(dr, "AssetId") ? Convert.ToString(dr["AssetId"]) : string.Empty;
                            #endregion
                            calls.Add(callInfo);
                        }

                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }

            return calls;
        }

        public static List<CallInfo> SearchCallsPrimaryDB(int pageSize, int pageIndex, out int totalPages, out long totalRecords, string duration, string criteria, CallCriteria callCriteria, int noOfCalls, Recorder recorder)
        {
            CallInfo callInfo = null;
            List<CallInfo> calls = null;

            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_AND_GET_RANDOM_PRIMARY_DB;

                    cmd.Parameters.AddWithValue("@StartDate", callCriteria.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", callCriteria.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@StartTime", callCriteria.StartTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@EndTime", callCriteria.EndTime.ConvertTimeSpanToString());

                    cmd.Parameters.AddWithValue("@IsRandom", callCriteria.IsRandom);
                    cmd.Parameters.AddWithValue("@IsPercentage", callCriteria.IsPercentage);
                    cmd.Parameters.AddWithValue("@NoOfCalls", callCriteria.NoOfCalls);

                    cmd.Parameters.AddWithValue("@IsGlobalSearch", callCriteria.SearchType == SearchType.Global ? true : false);

                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria);

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "SearchCallsPrimaryDB(QA)", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows) calls = new List<CallInfo>();
                        while (dr.Read())
                        {
                            callInfo = new CallInfo();

                            #region --- Old Mapping ---

                            //////callInfo.RecorderId = recorder.Id;
                            //////callInfo.RecorderName = recorder.Name;

                            //////callInfo.RowNo = (long)dr["RowNo"];
                            //////callInfo.CallId = Convert.ToString(dr["CallID"]);
                            //////callInfo.RecorderIP = recorder.IP;
                            //////callInfo.UniqueId = Convert.ToInt64(dr["UniqueId"]);
                            //////callInfo.GroupId = Convert.ToInt32(dr["GroupNum"]);
                            //////callInfo.GroupName = Convert.ToString(dr["GroupName"]);
                            //////callInfo.AgentId = Convert.ToInt32(dr["UserNum"]);
                            //////callInfo.ChannelId = Convert.ToInt32(dr["Ext"]);
                            //////callInfo.ChannelName = Convert.ToString(dr["ExtName"]);
                            //////callInfo.CallType = Convert.ToInt32(dr["CallType"]);
                            //////callInfo.StartTime = dr["StartTime"].ToString().ConvertStringDateTimeToDateTime();
                            //////callInfo.StartTimeString = Convert.ToString(dr["StartTime"]);
                            //////callInfo.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);
                            //////callInfo.FileName = Convert.ToString(dr["FileName"]);
                            //////callInfo.CustName = Convert.ToString(dr["CustName"]);
                            //////callInfo.ANI = Convert.ToString(dr["ANI"]);
                            //////callInfo.CallerID = Convert.ToString(dr["CallerID"]);
                            //////callInfo.CalledID = Convert.ToString(dr["CalledID"]);
                            //////callInfo.SGroupID = dr.IsDBNull(dr.GetOrdinal("SGroupID")) ? 0 : Convert.ToInt32(dr["SGroupID"]);
                            //////callInfo.Tag1 = Convert.ToString(dr["Tag1"]);
                            //////callInfo.Tag2 = Convert.ToString(dr["Tag2"]);
                            //////callInfo.Tag3 = Convert.ToString(dr["Tag3"]);
                            //////callInfo.Tag4 = Convert.ToString(dr["Tag4"]);
                            //////callInfo.ANIPhone = Convert.ToString(dr["ANI_PH"]);
                            //////callInfo.ANIName = Convert.ToString(dr["ANI_NAME"]);
                            //////callInfo.ANIDetails = Convert.ToString(dr["ANI_DETAILS"]);
                            //////callInfo.CallComments = Convert.ToString(dr["CALL_COMMENT"]);
                            //////callInfo.ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]);
                            //////callInfo.RetainValue = Convert.ToInt32(dr["RetainValue"]);
                            //////callInfo.interview_DateTime = Convert.ToString(dr["DateTime"]);
                            //////callInfo.interview_Interviewee = Convert.ToString(dr["Interviewer"]);
                            //////callInfo.interview_MdInterviewee = dr.IsDBNull(dr.GetOrdinal("MDInterviewee")) ? null : Convert.ToString(dr["MDInterviewee"]); //Convert.ToString(dr["MdInterviewee"]);
                            //////callInfo.interview_Interviewee = callInfo.CallType == 7 ? callInfo.interview_Interviewee : callInfo.interview_MdInterviewee;
                            //////callInfo.interview_Interviewer = Convert.ToString(dr["Interviewee"]);
                            //////callInfo.interview_InterviewId = Convert.ToString(dr["InterviewId"]);
                            //////callInfo.interview_GPS = Convert.ToString(dr["GPS"]);

                            //////if (callInfo.CallType != 7)
                            //////{
                            //////    callInfo.CallType_inq = callInfo.CallType;

                            //////}
                            //////else
                            //////{
                            //////    if (Regex.IsMatch(callInfo.FileName, "mp4", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "mov", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "m4v", RegexOptions.IgnoreCase))
                            //////    {
                            //////        callInfo.CallType_inq = 8;
                            //////    }
                            //////    else
                            //////    {
                            //////        callInfo.CallType_inq = 7;
                            //////    }
                            //////}

                            //////callInfo.BookmarkCSV = "";
                            //////if (dr.FieldExists("BookMarkXML"))
                            //////{
                            //////    callInfo.BookmarkXML = Convert.ToString(dr["BookMarkXML"]);
                            //////    if (!String.IsNullOrEmpty(callInfo.BookmarkXML))
                            //////    {
                            //////        callInfo.BookmarkCSV = callInfo.BookmarkXML.ConvertXmlStringToCsvString();
                            //////    }
                            //////}
                            #endregion

                            #region --- Mapping ---

                            callInfo.RecorderId = recorder.Id;
                            callInfo.RecorderName = recorder.Name;

                            callInfo.RowNo = (long)dr["RowNo"];

                            callInfo.CallId = Convert.ToString(dr["CallID"]);
                            callInfo.UniqueId = Convert.ToInt64(dr["UniqueId"]);
                            callInfo.RecorderId = Convert.ToInt32(dr["RecID"]);
                            callInfo.RecorderIP = recorder.IP;
                            //if (!dr.IsDBNull(dr.GetOrdinal("RecName")))
                            //    callInfo.RecorderName = dr.GetString(dr.GetOrdinal("RecName"));
                            callInfo.RecorderName = Convert.ToString(dr["RecName"]);
                            callInfo.GroupId = Convert.ToInt32(dr["GroupNum"]);
                            callInfo.GroupName = Convert.ToString(dr["GroupName"]);
                            callInfo.AgentId = Convert.ToInt32(dr["UserNum"]);
                            callInfo.ChannelId = Convert.ToInt32(dr["Ext"]);
                            callInfo.ChannelName = Convert.ToString(dr["ExtName"]);
                            callInfo.CallType = Convert.ToInt32(dr["CallType"]);
                            //callInfo.StartTime = Convert.ToDateTime(dr["StartTime"]);
                            callInfo.StartTime = dr["StartTime"].ToString().ConvertStringDateTimeToDateTime();
                            callInfo.StartTimeString = Convert.ToString(dr["StartTime"]);
                            callInfo.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);
                            callInfo.FileName = Convert.ToString(dr["FileName"]);

                            if (callInfo.CallType != 7)
                            {
                                callInfo.CallType_inq = callInfo.CallType;

                            }
                            else
                            {
                                if (Regex.IsMatch(callInfo.FileName, "mp4", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "mov", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "m4v", RegexOptions.IgnoreCase))
                                {
                                    callInfo.CallType_inq = 8;
                                }
                                else if (Regex.IsMatch(callInfo.FileName, "zip", RegexOptions.IgnoreCase))
                                {
                                    callInfo.CallType_inq = 12;
                                }
                                else
                                {
                                    callInfo.CallType_inq = 7;
                                }
                            }

                            callInfo.CustName = Convert.ToString(dr["CustName"]);
                            callInfo.ANI = Convert.ToString(dr["ANI"]);
                            callInfo.CallerID = Convert.ToString(dr["CallerID"]);
                            callInfo.CalledID = Convert.ToString(dr["CalledID"]);
                            callInfo.SGroupID = dr.IsDBNull(dr.GetOrdinal("SGroupID")) ? 0 : Convert.ToInt32(dr["SGroupID"]);
                            callInfo.Tag1 = Convert.ToString(dr["Tag1"]);
                            callInfo.Tag2 = Convert.ToString(dr["Tag2"]);
                            callInfo.Tag3 = Convert.ToString(dr["Tag3"]);
                            callInfo.Tag4 = Convert.ToString(dr["Tag4"]);
                            callInfo.Tag5 = Convert.ToString(dr["Tag5"]);
                            callInfo.Tag6 = Convert.ToString(dr["Tag6"]);
                            callInfo.Tag7 = Convert.ToString(dr["Tag7"]);
                            callInfo.Tag8 = Convert.ToString(dr["Tag8"]);
                            callInfo.Tag9 = Convert.ToString(dr["Tag9"]);
                            callInfo.Tag10 = Convert.ToString(dr["Tag10"]);
                            callInfo.Tag11 = Convert.ToString(dr["Tag11"]);
                            callInfo.Tag12 = Convert.ToString(dr["Tag12"]);
                            callInfo.Tag13 = Convert.ToString(dr["Tag13"]);
                            callInfo.Tag14 = Convert.ToString(dr["Tag14"]);
                            callInfo.Tag15 = Convert.ToString(dr["Tag15"]);
                            callInfo.Tag16 = Convert.ToString(dr["Tag16"]);
                            callInfo.IsRevCell = Convert.ToBoolean(dr["IsRevCell"]);

                            callInfo.ANIPhone = Convert.ToString(dr["ANI_PH"]);
                            callInfo.ANIName = Convert.ToString(dr["ANI_NAME"]);
                            callInfo.ANIDetails = Convert.ToString(dr["ANI_DETAILS"]);
                            callInfo.CallComments = Convert.ToString(dr["CALL_COMMENT"]);
                            callInfo.MessageBody = Convert.ToString(dr["Tag2"]);
                            //callInfo.ScreenRecFile = Convert.ToString(dr["Screen_Rec_File"]);
                            callInfo.ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]);//dr.IsDBNull(dr.GetOrdinal("ScreenFileNames")) ? null : Convert.ToString(dr["ScreenFileNames"]).Split(',').ToList();

                            callInfo.RetainValue = Convert.ToInt32(dr["RetainValue"]);

                            callInfo.Transcription = Convert.ToString(dr["Transcription"]);
                            callInfo.TranscriptionId = dr["TranscriptionId"] == DBNull.Value ? 0 : Convert.ToInt32(dr["TranscriptionId"]);
                            callInfo.Confidence = dr["Confidence"] == DBNull.Value ? 0 : Convert.ToDecimal(dr["Confidence"]);

                            callInfo.IsSyncedFromClient = Convert.ToInt32(dr["IsSyncedFromClient"]);

                            callInfo.interview_DateTime = Convert.ToString(dr["DateTime"]);
                            callInfo.interview_Interviewee = Convert.ToString(dr["Interviewee"]);
                            callInfo.interview_MdInterviewee = dr.IsDBNull(dr.GetOrdinal("MDInterviewee")) ? null : Convert.ToString(dr["MDInterviewee"]); //Convert.ToString(dr["MdInterviewee"]);

                            callInfo.interview_Interviewee = callInfo.CallType == 7 ? callInfo.interview_Interviewee : callInfo.interview_MdInterviewee;

                            callInfo.interview_Interviewer = Convert.ToString(dr["Interviewer"]);
                            callInfo.interview_InterviewId = Convert.ToString(dr["InterviewId"]);
                            callInfo.interview_GPS = Convert.ToString(dr["GPS"]);
                            callInfo.interview_Notes = Convert.ToString(dr["Notes"]);

                            callInfo.ErrorInMuxProcess = 0;
                            if (dr["ErrorinMuxProcess"] != DBNull.Value)
                                callInfo.ErrorInMuxProcess = Convert.ToInt32(dr["ErrorinMuxProcess"]);

                            callInfo.BookmarkCSV = string.Empty;
                            if (dr.FieldExists("BookMarkXML"))
                            {
                                callInfo.BookmarkXML = Convert.ToString(dr["BookMarkXML"]);
                                if (!String.IsNullOrEmpty(callInfo.BookmarkXML))
                                {
                                    callInfo.BookmarkCSV = callInfo.BookmarkXML.Inquire_ConvertXmlStringToCsvString(callInfo.CallType);
                                }
                            }
                            callInfo.IsPictureEvent = !DBRecordExtensions.HasColumn(dr, "IsPictureEvent") ? false : Convert.ToBoolean(dr["IsPictureEvent"]);
                            callInfo.IsVirtualInspection = !DBRecordExtensions.HasColumn(dr, "IsVirtualInspection") ? false : Convert.ToBoolean(dr["IsVirtualInspection"]);
                            callInfo.IsRevView = !DBRecordExtensions.HasColumn(dr, "IsRevView") ? false : Convert.ToBoolean(dr["IsRevView"]);
                            callInfo.RevViewFileName = DBRecordExtensions.HasColumn(dr, "RevViewFileName") ? Convert.ToString(dr["RevViewFileName"]) : string.Empty;
                            callInfo.RevViewStartTime = DBRecordExtensions.HasColumn(dr, "RevViewStartTime") ? Convert.ToString(dr["RevViewStartTime"]) : string.Empty;
                            callInfo.RevViewPhoneNumber = DBRecordExtensions.HasColumn(dr, "RevViewPhoneNumber") ? Convert.ToString(dr["RevViewPhoneNumber"]) : string.Empty;
                            callInfo.RevViewAgentName = DBRecordExtensions.HasColumn(dr, "RevViewAgentName") ? Convert.ToString(dr["RevViewAgentName"]) : string.Empty;
                            callInfo.STTFileName = DBRecordExtensions.HasColumn(dr, "STTFilePath") ? Convert.ToString(dr["STTFilePath"]) : string.Empty;
                            callInfo.VesselId = Convert.ToString(dr["VesselId"]);
                            callInfo.AssetId = DBRecordExtensions.HasColumn(dr, "AssetId") ? Convert.ToString(dr["AssetId"]) : string.Empty;
                            #endregion


                            calls.Add(callInfo);
                        }
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }

            return calls;
        }

        public static List<CallInfo> SearchCallsChainedDBs(int pageSize, int pageIndex, out int totalPages, out long totalRecords, string duration, string criteria, CallCriteria callCriteria, Recorder recorder)
        {
            List<CallInfo> calls = null;

            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_AND_GET_CHAIN_DB;

                    cmd.Parameters.AddWithValue("@StartDate", callCriteria.SearchType == SearchType.Global ? "" : callCriteria.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", callCriteria.SearchType == SearchType.Global ? "" : callCriteria.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@StartTime", callCriteria.StartTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@EndTime", callCriteria.EndTime.ConvertTimeSpanToString());

                    cmd.Parameters.AddWithValue("@IsGlobalSearch", callCriteria.SearchType == SearchType.Global ? true : false);

                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria.Replace("OR (  )  OR", " "));

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;



                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "SearchCallsChainedDBs", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        calls = ORMapper.MapCallInfos(dr);

                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());

                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());

                }
            }
            catch (Exception ex) { throw ex; }

            return calls;
        }

        public static List<CallInfo> GetCallsByIds(Recorder recorder, string callIds)
        {
            List<CallInfo> calls = null;
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_GET_BY_COMMA_SEPERATED_IDS;
                    cmd.Parameters.AddWithValue("@CommaSeparatedCallIds", new System.Data.SqlTypes.SqlChars(new System.Data.SqlTypes.SqlString(callIds)));

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "GetCallsByIds", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        calls = ORMapper.MapCallInfos(dr, recorder.Id, recorder.Name);
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return calls;
        }

        public static CallInfoLite GetCallDetailsFromRecorder(Recorder recorder, string callId)
        {
            CallInfoLite call = null;
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandText = DBConstants.VoiceRec.CALL_GET_BY_ID_PRIMARY_DB;
                    cmd.Parameters.AddWithValue("@CallId", callId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "GetCallDetailsFromRecorder", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.Read())
                        {
                            call = new CallInfoLite
                            {
                                RowNo = 1,
                                CallId = Convert.ToString(dr["CallID"]),
                                AgentId = Convert.ToInt32(dr["UserNum"]),
                                ChannelId = Convert.ToInt32(dr["Ext"]),
                                //ChannelName = Convert.ToString(dr["ExtName"]),
                                StartTimeString = Convert.ToString(dr["StartTime"]),
                                DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]),
                                FileName = Convert.ToString(dr["FileName"]),
                                CallComments = Convert.ToString(dr["CALL_COMMENT"]),
                                TagRule = Convert.ToString(dr["TagRule"]),
                                RecorderId = recorder.Id,
                                RecorderIP = recorder.IP
                            };
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return call;
        }

        public static CallInfo GetCallByIdFromRecorder(Recorder recorder, string callId)
        {
            CallInfo call = null;
            try
            {
                //using (var conn = DALHelper.GetConnection(_tenantId))
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALL_GET_BY_ID;
                    cmd.Parameters.AddWithValue("@CallId", callId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "GetCallByIdFromRecorder", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        //call = ORMapper.MapCallInfoTable(dr); // only to t_callInfo without any join
                        call = ORMapper.MapCallInfo(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return call;
        }


        #region Export Data
        public List<CallInfoExportResult> GetCallInfoExportResults(int pageSize, int pageIndex, string startDate, string endDate, string startTime, string endTime, bool isGlobalSearch, string duration, string criteria, Recorder recorder)
        {
            CallInfoExportResult callExportResult = null;
            List<CallInfoExportResult> callExportResults = new List<CallInfoExportResult>();
            try
            {
                //using (var conn = DALHelper.GetConnection(_tenantId))
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_AND_GET_EXPORT_RESULT;

                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);
                    cmd.Parameters.AddWithValue("@StartTime", startTime);
                    cmd.Parameters.AddWithValue("@EndTime", endTime);
                    cmd.Parameters.AddWithValue("@IsGlobalSearch", isGlobalSearch);
                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria);
                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "GetCallInfoExportResults", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            callExportResult = new CallInfoExportResult();

                            #region --- Mapping ---



                            callExportResult.RowNo = (long)dr["RowNo"];
                            callExportResult.GroupName = Convert.ToString(dr["GroupName"]);
                            callExportResult.AgentId = Convert.ToInt32(dr["UserNum"]);
                            callExportResult.ChannelName = Convert.ToString(dr["ExtName"]);
                            callExportResult.StartTime = dr["StartTime"].ToString().ConvertStringDateTimeToDateTime();
                            callExportResult.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);

                            callExportResult.BookmarkCSV = Convert.ToString(dr["BookmarkCSV"]);
                            callExportResult.CallComments = Convert.ToString(dr["CALL_COMMENT"]);
                            callExportResult.CalledID = Convert.ToString(dr["CalledID"]);

                            callExportResult.Tag4 = Convert.ToString(dr["Tag4"]);

                            callExportResult.ANIName = Convert.ToString(dr["ANI_NAME"]);
                            callExportResult.ANIPhone = Convert.ToString(dr["ANI_PH"]);
                            callExportResult.ANIDetails = Convert.ToString(dr["ANI_DETAILS"]);

                            callExportResult.Tag5 = Convert.ToString(dr["Tag5"]);
                            callExportResult.Tag6 = Convert.ToString(dr["Tag6"]);
                            callExportResult.Tag7 = Convert.ToString(dr["Tag7"]);
                            callExportResult.Tag8 = Convert.ToString(dr["Tag8"]);
                            callExportResult.Tag9 = Convert.ToString(dr["Tag9"]);
                            callExportResult.Tag10 = Convert.ToString(dr["Tag10"]);
                            callExportResult.Tag11 = Convert.ToString(dr["Tag11"]);
                            callExportResult.Tag12 = Convert.ToString(dr["Tag12"]);
                            callExportResult.Tag13 = Convert.ToString(dr["Tag13"]);
                            callExportResult.Tag14 = Convert.ToString(dr["Tag14"]);
                            callExportResult.Tag15 = Convert.ToString(dr["Tag15"]);
                            callExportResult.Tag16 = Convert.ToString(dr["Tag16"]);
                            callExportResult.RecorderName = recorder.Name;
                            #endregion
                            callExportResults.Add(callExportResult);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return callExportResults;
        }
        public static List<CallInfoExportResult> GetCallInfoExportResultsByIds(Recorder recorder, string callIds, DateTime startDate, DateTime endDate)
        {
            List<CallInfoExportResult> callInfoExportResults = null;
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandText = DBConstants.VoiceRec.EXPORT_RESULTS_GET_BY_COMMA_SEPERATED_IDS;
                    cmd.Parameters.AddWithValue("@CommaSeparatedCallIds", new System.Data.SqlTypes.SqlChars(new System.Data.SqlTypes.SqlString(callIds)));
                    cmd.Parameters.AddWithValue("@StartDateTime", startDate.ToString("yyyyMMddHHmmss"));
                    cmd.Parameters.AddWithValue("@EndDateTime", endDate.ToString("yyyyMMddHHmmss"));

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "GetCallInfoExportResultsByIds", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        callInfoExportResults = ORMapper.MapCallInfoExportResults(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return callInfoExportResults;
        }
        #endregion

        #region ------- Bookmark -------

        public static List<string> InsertBookmarkAndGetByCallId(Recorder recorder, Bookmark bookmark, out int rowsAffected)
        {
            List<string> bookmarks = null;
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.BOOKMARK_INSERT_N_GET;
                    cmd.Parameters.AddWithValue("@CallID", bookmark.CallId);
                    cmd.Parameters.AddWithValue("@BookMarkPos", bookmark.Position);
                    cmd.Parameters.AddWithValue("@BookMarkText", bookmark.Text);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "InsertBookmarkAndGetByCallId", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        rowsAffected = dr.RecordsAffected;
                        if (dr.HasRows)
                        {
                            dr.Read();
                            bookmarks = new List<string>();
                            //string BookmarkXML = Convert.ToString(dr[0]);
                            string BookmarkXML = Convert.ToString(dr["BookMarkXML"]);
                            string BookmarkCSV = "";
                            if (!String.IsNullOrEmpty(BookmarkXML))
                            {
                                BookmarkCSV = BookmarkXML.ConvertXmlStringToCsvString();
                            }
                            bookmarks.Add(BookmarkXML);
                            bookmarks.Add(BookmarkCSV);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return bookmarks;
        }

        public static List<string> UpdateBookmarkAndGetByCallId(Recorder recorder, Bookmark bookmark, out int rowsAffected)
        {
            List<string> bookmarks = null;
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.BOOKMARK_UPDATE_N_GET;
                    cmd.Parameters.AddWithValue("@CallID", bookmark.CallId);
                    cmd.Parameters.AddWithValue("@BookMarkPos", bookmark.Position);
                    cmd.Parameters.AddWithValue("@BookMarkText", bookmark.Text);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "UpdateBookmarkAndGetByCallId", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        rowsAffected = dr.RecordsAffected;
                        if (dr.HasRows)
                        {
                            dr.Read();
                            bookmarks = new List<string>();
                            //string BookmarkXML = Convert.ToString(dr[0]);
                            string BookmarkXML = Convert.ToString(dr["BookMarkXML"]);
                            string BookmarkCSV = "";
                            if (!String.IsNullOrEmpty(BookmarkXML))
                            {
                                BookmarkCSV = BookmarkXML.ConvertXmlStringToCsvString();
                            }
                            bookmarks.Add(BookmarkXML);
                            bookmarks.Add(BookmarkCSV);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return bookmarks;
        }


        #endregion


        #region ------- Custom Fields -------

        public static int UpdateCallsCustomFields(Recorder recorder, string callIds, int fieldType, string fieldText, int userId)
        {
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_UPDATE_CUSTOM_FIELDS_CHAIN_DB;
                    cmd.Parameters.AddWithValue("@Comment", fieldText);
                    //cmd.Parameters.AddWithValue("@CallID", callIds.Split(',')[0]);
                    //cmd.Parameters.AddWithValue("@CommaSeparatedCallIds", callIds);
                    cmd.Parameters.AddWithValue("@CommaSeparatedCallIds", new System.Data.SqlTypes.SqlChars(new System.Data.SqlTypes.SqlString(callIds)));
                    //cmd.Parameters.AddWithValue("@Ext", callInfo.CalledID);
                    cmd.Parameters.AddWithValue("@Type", fieldType);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "UpdateCallsCustomFields", 0));

                    return cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public static int UpdateCallCustomFields(Recorder recorder, CallInfo callInfo)
        {
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_CUSTOM_FIELDS_UPDATE;
                    cmd.Parameters.AddWithValue("@CallID", callInfo.CallId);
                    cmd.Parameters.AddWithValue("@Tag1", callInfo.Tag1);
                    cmd.Parameters.AddWithValue("@Tag2", callInfo.Tag2);
                    cmd.Parameters.AddWithValue("@Tag3", callInfo.Tag3);
                    cmd.Parameters.AddWithValue("@Tag4", callInfo.Tag4);
                    cmd.Parameters.AddWithValue("@CustName", callInfo.CustName);
                    cmd.Parameters.AddWithValue("@CallComments", callInfo.CallComments);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "UpdateCallCustomFields", 0));

                    //return Convert.ToBoolean(cmd.ExecuteScalar());
                    return cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public static int UpdateCallRetention(Recorder recorder, string callId, bool retainValue)
        {
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_RETAIN_VALUE_UPDATE;
                    cmd.Parameters.AddWithValue("@CallID", callId);
                    cmd.Parameters.AddWithValue("@RetainValue", retainValue);
                    //cmd.Parameters.AddWithValue("@Ext", callInfo.CalledID);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "UpdateCallRetention", 0));

                    //string s = Convert.ToString( cmd.ExecuteScalar());
                    return cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public static int Inquiremarkerupdate(Recorder recorder, string callId, int markerid, string markertext, string markernotes)
        {
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_INQUIRE_MARKER_UPDATE;
                    cmd.Parameters.AddWithValue("@CallID", callId);
                    cmd.Parameters.AddWithValue("@markerid", markerid);
                    cmd.Parameters.AddWithValue("@markertext", markertext);
                    cmd.Parameters.AddWithValue("@markernotes", markernotes);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "Inquiremarkerupdate", 0));

                    return cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex) { throw ex; }
        }

        #endregion

        #region Monitor Channels
        public static List<MonitorChannel> GetChannelsToMonitorFromAllRecorders(Recorder recorder, string whereClause)
        {
            List<MonitorChannel> channels = null;
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CHANNELS_MONITOR_GETBY_WHERECLAUSE;
                    cmd.Parameters.AddWithValue("@WhereClause", whereClause);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "GetChannelsToMonitorFromAllRecorders", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (channels == null) channels = new List<MonitorChannel>();
                        while (dr.Read())
                        {
                            var channel = new MonitorChannel
                            {
                                UserNum = Convert.ToInt32(dr["UserNum"]),
                                UserName = Convert.ToString(dr["UserName"]),
                                GroupNum = Convert.ToInt32(dr["GroupNum"]),
                                GroupName = Convert.ToString(dr["GroupName"]),
                                Ext = Convert.ToString(dr["Ext"]),
                                AgentIP = Convert.ToString(dr["AgentIP"]),
                                UserPW = Convert.ToString(dr["UserPW"]),
                                ROD = Convert.ToString(dr["ROD"]),
                                POD = Convert.ToString(dr["POD"]),
                                EOD = Convert.ToString(dr["EOD"]),
                                SOD = Convert.ToString(dr["SOD"]),
                                ExtName = Convert.ToString(dr["ExtName"]),
                                UserEmail = Convert.ToString(dr["UserEmail"]),
                                RecId = Convert.ToInt32(dr["RecId"]),
                                RecIP = Convert.ToString(dr["RecIP"]),
                                RecPort = Convert.ToInt16(dr["RecPort"]),
                                Channeltype = Convert.ToInt16(dr["Channeltype"]),
                                IsAvtecChannel = Convert.ToInt16(dr["IsAvtecChannel"]),
                                Status = "Inactive",
                                IsRevView = Convert.ToBoolean(dr["IsRevView"])
                            };

                            channels.Add(channel);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return channels;
        }
        #endregion

        #region Evaluation
        public bool InsertCallsOnRecorder(string calls, int userId, int surveyId, Recorder recorder)
        {
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = "em_CallEvaluation_Insert";
                    cmd.Parameters.AddWithValue("@CallInfos", calls.ToString());
                    cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@SurveyId", surveyId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "InsertCallsOnRecorder", 0));

                    cmd.ExecuteNonQuery();

                    return true;
                }
            }
            catch (Exception ex) { throw ex; }
            return false;
        }
        #endregion
    }
}
//public static List<MonitorChannel> GetChannels(string whereClause)
//{
//    List<MonitorChannel> channels = new List<MonitorChannel>();
//    MonitorChannel channel = null;
//    try
//    {
//        foreach (var rec in recorders)
//        {
//            using (var conn = new SqlConnection(rec.ConnectionString))
//            {
//                Debug.WriteLine(string.Format("{0}{1}", rec.Id, rec.Name));
//                using (var cmd = conn.CreateCommand())
//                {
//                    cmd.CommandType = CommandType.Text;
//                    cmd.CommandText = "SELECT * FROM t_ExtInfo WHERE Status = 1";

//                    conn.Open();
//                    using (SqlDataReader dr = cmd.ExecuteReader())
//                    {
//                        while (dr.Read())
//                        {
//                            channel = new MonitorChannel();
//                            channel.Ext = dr.GetString(dr.GetOrdinal("Ext"));
//                            if (!dr.IsDBNull(dr.GetOrdinal("ExtName")))
//                                channel.ExtName = dr.GetString(dr.GetOrdinal("ExtName"));

//                            channel.RecId = rec.Id;
//                            channel.RecIP = rec.IP;

//                            channels.Add(channel);
//                        }
//                    }
//                }
//            }
//        }
//        return channels;
//    }
//    catch (Exception ex) { throw ex; }
//    //return channels;
//}
