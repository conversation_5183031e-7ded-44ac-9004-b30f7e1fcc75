﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Data.SqlClient;
using System.Xml.Linq;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.DataContracts.DTO;
using RevCord.Util;
using System.IO;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using Newtonsoft.Json;

namespace RevCord.DataAccess
{
    public class VoiceRecDAL
    {
        private static int CMD_TIMEOUT = AppSettingsUtil.GetInt("commandTimeout", 60);
        private int _tenantId;
        public VoiceRecDAL(int tenantId)
        {
            this._tenantId = tenantId;
        }

        #region Deleted

        /// <summary>
        /// This method will receive List of calls and Insert in SurveyDB
        /// </summary>
        /// <param name="calls"></param>
        /// <returns></returns>
        public bool InsertCalls(string calls, int userId, int surveyId,int revsyncSurveyId)
        {
            try
            {
                //XElement xCalls = this.CreateCallsXML(calls);
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = "em_CallEvaluation_Insert";
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@CallInfos", calls.ToString());
                    //cmd.Parameters.AddWithValue("@SurveyId", surveyId); //DBNULL
                    cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@SurveyId", surveyId);
                    cmd.Parameters.AddWithValue("@EvaluationType", 1);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceRec, "InsertCalls", _tenantId));

                    int lastId =Convert.ToInt32( cmd.ExecuteScalar());
                    int revsyncserverid = 0;
                    if(SiteConfig.RevSyncEnabled && lastId > 0)
                    {
                        try
                        {
                            Dictionary<string, string> _dic = new Dictionary<string, string>();
                            _dic.Add("Calls", Convert.ToString(calls));
                            _dic.Add("UserId", Convert.ToString(userId));
                            _dic.Add("SurveyId", Convert.ToString(surveyId));

                            revsyncserverid = Convert.ToInt32(DALHelper.RevsyncAPICall(_tenantId, "VoiceRecDAL", "InsertCalls", JsonConvert.SerializeObject(_dic)));
                            
                            if (revsyncserverid > 0)
                            {
                                tran.Commit();
                                using (var Updatecmd = conn.CreateCommand())
                                {
                                    Updatecmd.CommandType = CommandType.Text;
                                    Updatecmd.CommandText = "UPDATE emCallEvaluation SET [RevSyncServerID] = " + revsyncserverid + " Where Id = " + lastId;
                                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(Updatecmd), Originator.VoiceRec, "InsertCalls RevSync", _tenantId));
                                    Updatecmd.ExecuteNonQuery();
                                }
                            }
                        }
                        catch(Exception e)
                        {
                            tran.Rollback();
                        }
                    }
                    else if (SiteConfig.IsMTEnable)
                    {
                        Dictionary<string, string> _dic = new Dictionary<string, string>();
                        _dic.Add("Calls", Convert.ToString(calls));
                        _dic.Add("UserId", Convert.ToString(userId));
                        _dic.Add("SurveyId", Convert.ToString(surveyId));

                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "VoiceRecDAL", "InsertCalls", JsonConvert.SerializeObject(_dic));

                        //if (isSent)
                            tran.Commit();
                        //else
                        //    tran.Rollback();
                    }
                    else
                        tran.Commit();

                    return true;
                }
            }
            catch (Exception ex) { throw ex; }
            //finally{}
            return false;
        }


        //public List<CallInfo> GetCallsByStatus(int pageSize, int pageIndex, out int totalPages, int statusId)

        #endregion


        #region CallInfo

        public List<CallInfo> GetCallInfosByCallIds(string callIds)
        {
            List<CallInfo> calls = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_GETBY_COMMA_SEPERATED_IDS;
                    cmd.Parameters.AddWithValue("@CommaSeparatedCallIds", callIds);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceRec, "GetCallInfosByCallIds", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        calls = ORMapper.MapCallInfos(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return calls;
        }

        public bool UpdateCallInfoCustomFields(CallInfo callInfo)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_CUSTOM_FIELDS_UPDATE;
                    cmd.Parameters.AddWithValue("@CallID", callInfo.CallId);
                    cmd.Parameters.AddWithValue("@Tag1", callInfo.Tag1);
                    cmd.Parameters.AddWithValue("@Tag2", callInfo.Tag2);
                    cmd.Parameters.AddWithValue("@Tag3", callInfo.Tag3);
                    cmd.Parameters.AddWithValue("@Tag4", callInfo.Tag4);
                    cmd.Parameters.AddWithValue("@CustName", callInfo.CustName);
                    cmd.Parameters.AddWithValue("@CallComments", callInfo.CallComments);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceRec, "UpdateCallInfoCustomFields", _tenantId));

                    return Convert.ToBoolean(cmd.ExecuteScalar());

                    //return true;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public short UpdateCallInfoFields(string callIds, int fieldType, string fieldText, int userId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_CUSTOM_FIELDS_MULTIPLE_UPDATE;
                    cmd.Parameters.AddWithValue("@Comment", fieldText);
                    cmd.Parameters.AddWithValue("@CallID", callIds.Split(',')[0]);
                    //cmd.Parameters.AddWithValue("@CommaSeparatedCallIds", callIds);
                    cmd.Parameters.AddWithValue("@CommaSeparatedCallIds", new System.Data.SqlTypes.SqlChars(new System.Data.SqlTypes.SqlString(callIds)));
                    //cmd.Parameters.AddWithValue("@Ext", callInfo.CalledID);
                    cmd.Parameters.AddWithValue("@Type", fieldType);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceRec, "UpdateCallInfoFields", _tenantId));
                    //string s = Convert.ToString( cmd.ExecuteScalar());
                    return Convert.ToInt16(cmd.ExecuteScalar());

                    //return true;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public int UpdateCallInfoRetainValue(string callId, bool retainValue, int userId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_RETAIN_VALUE_UPDATE;
                    cmd.Parameters.AddWithValue("@CallID", callId);
                    cmd.Parameters.AddWithValue("@RetainValue", retainValue);
                    //cmd.Parameters.AddWithValue("@Ext", callInfo.CalledID);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceRec, "UpdateCallInfoRetainValue", _tenantId));

                    //string s = Convert.ToString( cmd.ExecuteScalar());
                    return Convert.ToInt16(cmd.ExecuteNonQuery());
                    //return true;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        #endregion

        #region Search Calls to be Evaluated


        public List<CallInfoSearchResultDTO> CreateAndGetCallSearchResults(int pageSize, int pageIndex, out int totalPages, CallInfoSearchCriteriaDTO callInfoSearchCriteriaDTO)
        {
            CallInfoSearchResultDTO callInfo = null;
            List<CallInfoSearchResultDTO> calls = null;
            totalPages = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_CREATE_TABLE_AND_SEARCH;
                    cmd.Parameters.AddWithValue("@BUF_Name", "EvalSearchCalls");
                    cmd.Parameters.AddWithValue("@s_Date", callInfoSearchCriteriaDTO.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@e_Date", callInfoSearchCriteriaDTO.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@s_Time", callInfoSearchCriteriaDTO.StartTime.TimeSpanAsStringWithoutColon());
                    cmd.Parameters.AddWithValue("@e_Time", callInfoSearchCriteriaDTO.EndTime.TimeSpanAsStringWithoutColon());
                    cmd.Parameters.AddWithValue("@s_Dur", callInfoSearchCriteriaDTO.StartDuration.TotalMilliseconds.ToString());
                    cmd.Parameters.AddWithValue("@e_Dur", callInfoSearchCriteriaDTO.EndDuration.TotalMilliseconds.ToString());
                    cmd.Parameters.AddWithValue("@IsRandom", callInfoSearchCriteriaDTO.IsRandom);
                    cmd.Parameters.AddWithValue("@NoOfCalls", callInfoSearchCriteriaDTO.NoOfRandomCalls);
                    cmd.Parameters.AddWithValue("@IsPercentage", callInfoSearchCriteriaDTO.IsPercentage);
                    //cmd.Parameters.AddWithValue("@encodeType", 1);
                    //cmd.Parameters.AddWithValue("@ViewType", 1);
                    //cmd.Parameters.AddWithValue("@TopCount", -1);
                    cmd.Parameters.AddWithValue("@optionSTR", callInfoSearchCriteriaDTO.Criteria);
                    //cmd.Parameters.AddWithValue("@LiveSearch", 1);
                    //cmd.Parameters.AddWithValue("@searchType", 1);

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", totalPages);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceRec, "CreateAndGetCallSearchResults", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (calls == null) calls = new List<CallInfoSearchResultDTO>();
                        dr.NextResult();// in order to avoide "InsertCount" Table
                        while (dr.Read())
                        {
                            callInfo = new CallInfoSearchResultDTO();
                            ////callInfo.CreatedDate = dr.GetDateTime(3);
                            ////callInfo.IsDeleted = dr.GetBoolean(4);

                            if (dr.FieldExists("idx"))
                                callInfo.Index = Convert.ToInt32(dr["RowNo"]);  //(int)dr["idx"] + 1;
                            if (dr.FieldExists("CallIDIDX"))
                                callInfo.CallID = Convert.ToString(dr["CallIDIDX"]);
                            if (dr.FieldExists("FileNameIDX"))
                                callInfo.FileName = Convert.ToString(dr["FileNameIDX"]);
                            //callInfo.StartTime= 
                            //callInfo.Time=
                            //callInfo.RecorderIP=
                            //callInfo.InHDD=
                            //callInfo.CallIndex=Convert.ToString(dr["COLUMN_NAME"]);
                            //callInfo.BackupID=
                            if (dr.FieldExists("GroupNameIDX"))
                                callInfo.GroupName = Convert.ToString(dr["GroupNameIDX"]);
                            if (dr.FieldExists("UserNameIDX"))
                                callInfo.UserName = Convert.ToString(dr["UserNameIDX"]);
                            if (dr.FieldExists("TimeInfoIDX"))
                                callInfo.TimeInfo = Convert.ToString(dr["TimeInfoIDX"]);
                            
                            if (dr.FieldExists("DurationIDX"))
                                callInfo.Duration = Convert.ToString(dr["DurationIDX"]);
                            if (dr.FieldExists("ExtIDX"))
                                callInfo.ExtensionID = Convert.ToString(dr["ExtIDX"]);
                            if (dr.FieldExists("ExtNameIDX"))
                                callInfo.ExtName = Convert.ToString(dr["ExtNameIDX"]);
                            //callInfo.ExtName=
                            //callInfo.Called=
                            //callInfo.CallTag=
                            if (dr.FieldExists("CallCommentIDX"))
                                callInfo.CallComment = Convert.ToString(dr["CallCommentIDX"]);
                            //callInfo.RODCustName=
                            //callInfo.RODCustNum=
                            //callInfo.RODCustInfo2=
                            //callInfo.RODCustInfo3=
                            //callInfo.RODCustInfo4=
                            //callInfo.ANIName=
                            //callInfo.ANIPhone=
                            //callInfo.ANIDetails=
                            //callInfo.VideoFileName=
                            //callInfo.Bookmarks=
                            //callInfo.RetainValue=

                            calls.Add(callInfo);
                        }
                    }
                    totalPages = Convert.ToInt32(string.IsNullOrEmpty(cmd.Parameters["@TotalPages"].Value.ToString()) ? "0" :
                                    cmd.Parameters["@TotalPages"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }

            return calls;
        }

        public List<CallInfo> GetCallSearchResults(CallInfoSearchCriteriaDTO callInfoSearchCriteriaDTO)
        {
            List<CallInfo> calls = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_AND_GET;

                    cmd.Parameters.AddWithValue("@StartDate", callInfoSearchCriteriaDTO.StartDate);
                    cmd.Parameters.AddWithValue("@EndDate", callInfoSearchCriteriaDTO.EndDate);
                    cmd.Parameters.AddWithValue("@DurationSTR", callInfoSearchCriteriaDTO.DurationStr);
                    cmd.Parameters.AddWithValue("@OptionSTR", callInfoSearchCriteriaDTO.Criteria);
                    cmd.Parameters.AddWithValue("@UserId", 1000);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceRec, "GetCallSearchResults", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        calls = ORMapper.MapCallInfos(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return calls;
        }

        public List<CallInfo> GetCallSearchResultsPaged(int pageSize, int pageIndex, out int totalPages, out long totalRecords, CallInfoSearchCriteriaDTO callInfoSearchCriteriaDTO)
        {
            List<CallInfo> calls = null;
            try
            {
                //using (var conn = DALHelper.GetConnectionVoiceRec())
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_AND_GET_PAGED;

                    cmd.Parameters.AddWithValue("@StartDate", callInfoSearchCriteriaDTO.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", callInfoSearchCriteriaDTO.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@StartTime", callInfoSearchCriteriaDTO.StartTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@EndTime", callInfoSearchCriteriaDTO.EndTime.ConvertTimeSpanToString());

                    cmd.Parameters.AddWithValue("@DurationSTR", callInfoSearchCriteriaDTO.DurationStr);
                    cmd.Parameters.AddWithValue("@OptionSTR", callInfoSearchCriteriaDTO.Criteria);

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceRec, "GetCallSearchResultsPaged", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        calls = ORMapper.MapCallInfos(dr);
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }

            return calls;
        }

        public List<CallInfo> GetCallsByLocation(CallInfoSearchCriteriaDTO callInfoSearchCriteriaDTO)
        {
            List<CallInfo> calls = null;
            CallInfo call = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_AND_GET_BY_LOCATION;
                    cmd.CommandTimeout = CMD_TIMEOUT;

                    cmd.Parameters.AddWithValue("@StartDate", callInfoSearchCriteriaDTO.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", callInfoSearchCriteriaDTO.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@StartTime", callInfoSearchCriteriaDTO.StartTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@EndTime", callInfoSearchCriteriaDTO.EndTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@DurationSTR", callInfoSearchCriteriaDTO.DurationStr);
                    cmd.Parameters.AddWithValue("@OptionSTR", callInfoSearchCriteriaDTO.Criteria);
                    //cmd.Parameters.AddWithValue("@UserId", 1000);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceRec, "GetCallsByLocation", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        //calls = ORMapper.MapCallInfos(dr);
                        if (calls == null) calls = new List<CallInfo>();
                        while (dr.Read())
                        {
                            call = new CallInfo();
                            //call.RowNo = (long)dr["RowNo"];

                            call.CallId = Convert.ToString(dr["CallID"]);
                            call.UniqueId = Convert.ToInt64(dr["UniqueId"]);
                            call.RecorderId = Convert.ToInt32(dr["RecID"]);
                            call.ChannelId = Convert.ToInt32(dr["Ext"]);
                            call.AgentId = Convert.ToInt32(dr["UserNum"]);
                            call.CallType = Convert.ToInt32(dr["CallType"]);
                            
                            call.StartTime = dr["StartTime"].ToString().ConvertStringDateTimeToDateTime();
                            call.StartTimeString = Convert.ToString(dr["StartTime"]);
                            call.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);
                            call.FileName = Convert.ToString(dr["FileName"]);

                            if (call.CallType != 7)
                            {
                                call.CallType_inq = call.CallType;
                            }
                            else
                            {
                                if (Regex.IsMatch(call.FileName, "mp4", RegexOptions.IgnoreCase) || Regex.IsMatch(call.FileName, "mov", RegexOptions.IgnoreCase) || Regex.IsMatch(call.FileName, "m4v", RegexOptions.IgnoreCase))
                                {
                                    call.CallType_inq = 8;
                                }
                                else if (Regex.IsMatch(call.FileName, "zip", RegexOptions.IgnoreCase))
                                {
                                    call.CallType_inq = 12;
                                }
                                else
                                {
                                    call.CallType_inq = 7;
                                }
                            }
                            call.Tag3 = Convert.ToString(dr["Tag3"]);
                            call.Tag4 = Convert.ToString(dr["GPS"]);
                            call.IsRevCell = Convert.ToBoolean(dr["IsRevCell"]);
                            if (call.CallType != 7 && call.CallType != 11 && !call.IsRevCell) { 
                                call.Latitude = Convert.ToString(dr["Latitude"]);
                                call.Longitude = Convert.ToString(dr["Longitude"]);
                            }
                            else
                            {
                                call.Latitude = Convert.ToString(dr["Longitude"]);
                                call.Longitude = Convert.ToString(dr["Latitude"]);
                            }

                            call.GroupName = Convert.ToString(dr["GroupName"]);
                            call.ChannelName = Convert.ToString(dr["ExtName"]);
                            call.ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]);
                            if (dr.FieldExists("BookMarkXML"))
                            {
                                call.BookmarkXML = Convert.ToString(dr["BookMarkXML"]);
                                if (!String.IsNullOrEmpty(call.BookmarkXML))
                                {
                                    call.BookmarkCSV = call.BookmarkXML.ConvertXmlStringToCsvString();
                                }
                            }
                            call.IsPictureEvent = !DBRecordExtensions.HasColumn(dr, "IsPictureEvent") ? false : Convert.ToBoolean(dr["IsPictureEvent"]);
                            call.IsVirtualInspection = !DBRecordExtensions.HasColumn(dr, "IsVirtualInspection") ? false : Convert.ToBoolean(dr["IsVirtualInspection"]);
                            call.IsRevView = !DBRecordExtensions.HasColumn(dr, "IsRevView") ? false : Convert.ToBoolean(dr["IsRevView"]);
                            call.RevViewFileName = DBRecordExtensions.HasColumn(dr, "RevViewFileName") ? Convert.ToString(dr["RevViewFileName"]) : string.Empty;
                            call.RevViewStartTime = DBRecordExtensions.HasColumn(dr, "RevViewStartTime") ? Convert.ToString(dr["RevViewStartTime"]) : string.Empty;
                            call.RevViewPhoneNumber = DBRecordExtensions.HasColumn(dr, "RevViewPhoneNumber") ? Convert.ToString(dr["RevViewPhoneNumber"]) : string.Empty;
                            call.RevViewAgentName = DBRecordExtensions.HasColumn(dr, "RevViewAgentName") ? Convert.ToString(dr["RevViewAgentName"]) : string.Empty;
                            call.STTFileName = DBRecordExtensions.HasColumn(dr, "STTFilePath") ? Convert.ToString(dr["STTFilePath"]) : string.Empty;
                            call.RapidsosStatus = DBRecordExtensions.HasColumn(dr, "RapidsosStatus") ? Convert.ToInt32(dr["RapidsosStatus"]) : 0; 
                            calls.Add(call);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return calls;
        }

        public List<CallInfo> GetCallSearchResultsPagedDemo(int actualPageSize, int pageSize, int pageIndex, out int totalPages, out long totalRecords, out int totalPagesDemo, out long totalRecordsDemo, CallInfoSearchCriteriaDTO callInfoSearchCriteriaDTO)
        {
            List<CallInfo> calls = null;
            List<CallInfo> dummyCalls = null;
            int actualCallCount;
            try
            {
                //using (var conn = DALHelper.GetConnectionVoiceRec())
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = 120;
                    cmd.CommandType = CommandType.StoredProcedure;
                    //cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_AND_GET_PAGED;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_AND_GET_PAGED_DEMO;

                    cmd.Parameters.AddWithValue("@StartDate", callInfoSearchCriteriaDTO.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", callInfoSearchCriteriaDTO.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@StartTime", callInfoSearchCriteriaDTO.StartTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@EndTime", callInfoSearchCriteriaDTO.EndTime.ConvertTimeSpanToString());

                    cmd.Parameters.AddWithValue("@DurationSTR", callInfoSearchCriteriaDTO.DurationStr);
                    cmd.Parameters.AddWithValue("@OptionSTR", callInfoSearchCriteriaDTO.Criteria);

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@ActualPageSize", actualPageSize);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;


                    cmd.Parameters.AddWithValue("@TotalPagesDemo", 1);
                    cmd.Parameters.AddWithValue("@TotalRecordsDemo", 0);
                    cmd.Parameters["@TotalPagesDemo"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecordsDemo"].Direction = ParameterDirection.Output;


                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceRec, "GetCallSearchResultsPagedDemo", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        calls = ORMapper.MapCallInfos(dr);

                        dr.NextResult();
                        dummyCalls = ORMapper.MapDummyCallInfos(dr);
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                    actualCallCount = calls.Count;
                    
                    totalPagesDemo = Convert.ToInt32(cmd.Parameters["@TotalPagesDemo"].Value.ToString());
                    totalRecordsDemo = Convert.ToInt64(cmd.Parameters["@TotalRecordsDemo"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }

            calls.AddRange(dummyCalls.Take(pageSize - actualCallCount).ToList());
            return calls;
        }


        public List<CallInfo> GetCallsByIds(string callIds)
        {
            List<CallInfo> calls = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_GET_BY_COMMA_SEPERATED_IDS;
                    cmd.Parameters.AddWithValue("@CommaSeparatedCallIds", new System.Data.SqlTypes.SqlChars(new System.Data.SqlTypes.SqlString(callIds)));

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceRec, "GetCallsByIds", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        calls = ORMapper.MapCallInfos(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return calls;
        }

        public List<RapidSOSGPSData> getRapidSOSMapDetails(string callId)
        {
            List<RapidSOSGPSData> LRapidSOSGPSData = new List<RapidSOSGPSData>();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Reports.MapReports.RAPIDSOSMAP_GET;
                    cmd.Parameters.AddWithValue("@CallId", callId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceRec, "getRapidSOSMapDetails", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        //call = ORMapper.MapCallInfoTable(dr); // only to t_callInfo without any join
                        while (dr.Read())
                        {
                            RapidSOSGPSData _RapidSOSGPSData = new RapidSOSGPSData();
                            _RapidSOSGPSData.CallId = Convert.ToString(dr["CallID"]);
                            _RapidSOSGPSData.GPS = Convert.ToString(dr["GPS"]);
                            _RapidSOSGPSData.LocationTime = Convert.ToString(dr["GPSTimeStamp"]);
                            _RapidSOSGPSData.GPSRelativeTime = Convert.ToInt32(dr["GPSRelativeTime"]); 
                            DateTime dateTime = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
                            _RapidSOSGPSData.LocationDatetime = dateTime.AddSeconds(Convert.ToDouble(_RapidSOSGPSData.LocationTime)/ 1000d).ToLocalTime();
                            LRapidSOSGPSData.Add(_RapidSOSGPSData);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return LRapidSOSGPSData;
        }

        public CallInfo GetCallInfoById(string callId)
        {
            CallInfo call = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALL_GET_BY_ID_PRIMARY_DB;
                    cmd.Parameters.AddWithValue("@CallId", callId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceRec, "GetCallInfoById", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        //call = ORMapper.MapCallInfoTable(dr); // only to t_callInfo without any join
                        call = ORMapper.MapCallInfo(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return call;
        }

        public List<CallInfo> GetCallsByCommaSeperatedIds(string callIds)
        {
            List<CallInfo> calls = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_GET_BY_COMMA_SEPERATED_IDS;// CALLS_ONLY_GET_BY_COMMA_SEPERATED_IDS;
                    cmd.Parameters.AddWithValue("@CommaSeparatedCallIds", new System.Data.SqlTypes.SqlChars(new System.Data.SqlTypes.SqlString(callIds)));

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceRec, "GetCallsByCommaSeperatedIds", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        calls = ORMapper.MapCallInfos(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return calls;
        }

        #endregion


        #region Utility

        //private XElement CreateCallsXML(List<CallInfo> calls)
        //{
        //    XElement xeCalls = new XElement("Calls");
        //    foreach (var call in calls)
        //    {
        //        xeCalls.Add(new XElement("Call",
        //                        new XAttribute("CallID", call.CallId),
        //                        new XAttribute("RecID", call.RecorderId),
        //                        new XAttribute("GroupNum", call.GroupId),
        //                        new XAttribute("UserNum", call.AgentId),
        //                        new XAttribute("Ext", call.ChannelId),
        //                        new XAttribute("CallType", call.CallType),
        //                        new XAttribute("StartTime", call.StartTime),
        //                        new XAttribute("Duration", call.DurationInMilliSeconds),
        //                        new XAttribute("FileName", call.FileName),
        //                        new XAttribute("CustName", call.CustName),
        //                        new XAttribute("ANI", call.ANI),
        //                        new XAttribute("CallerID", call.CallerID),
        //                        new XAttribute("CalledID", call.CalledID),
        //                        new XAttribute("SGroupID", call.SGroupID),
        //                        new XAttribute("Tag1", call.Tag1),
        //                        new XAttribute("Tag2", call.Tag2),
        //                        new XAttribute("Tag3", call.Tag3),
        //                        new XAttribute("Tag4", call.Tag4),
        //                        new XAttribute("ANI_PH", call.ANIPhone),
        //                        new XAttribute("ANI_NAME", call.ANIName),
        //                        new XAttribute("ANI_DETAILS", call.ANIDetails),
        //                        new XAttribute("CALL_COMMENT", call.CallComments),
        //                        new XAttribute("Screen_Rec_File", call.ScreenRecFile),
        //                        new XAttribute("RetainValue", call.RetainValue)
        //                    ));

        //    }
        //    return xeCalls;
        //}

        #endregion


        #region BookMark

        public bool SaveBookMark(Bookmark bookmark)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.BOOKMARK_INSERT;
                    cmd.Parameters.AddWithValue("@CallID", bookmark.CallId);
                    cmd.Parameters.AddWithValue("@BookMarkPos", bookmark.Position);
                    cmd.Parameters.AddWithValue("@BookMarkText", bookmark.Text);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceRec, "SaveBookMark", _tenantId));
                    //cmd.Parameters.Add("@Id", SqlDbType.Int).Direction = ParameterDirection.Output;  
                    //bookmark.Id = Convert.ToInt32(cmd.ExecuteScalar());
                    cmd.ExecuteNonQuery();

                    return true;
                }
            }
            catch (Exception ex) { throw ex; }
            //finally{}
        }

        public bool UpdateBookMark(Bookmark bookmark)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.BOOKMARK_UPDATE;
                    cmd.Parameters.AddWithValue("@CallID", bookmark.CallId);
                    cmd.Parameters.AddWithValue("@BookMarkPos", bookmark.Position);
                    cmd.Parameters.AddWithValue("@BookMarkText", bookmark.Text);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceRec, "UpdateBookMark", _tenantId));

                    //cmd.Parameters.Add("@Id", SqlDbType.Int).Direction = ParameterDirection.Output;  
                    //bookmark.Id = Convert.ToInt32(cmd.ExecuteScalar());
                    cmd.ExecuteNonQuery();

                    return true;
                }
            }
            catch (Exception ex) { throw ex; }
            //finally{}
        }

        public List<string> SaveBookMarkAndGetByCallId(Bookmark bookmark)
        {
            List<string> bookmarks = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.BOOKMARK_INSERT_N_GET;
                    cmd.Parameters.AddWithValue("@CallID", bookmark.CallId);
                    cmd.Parameters.AddWithValue("@BookMarkPos", bookmark.Position);
                    cmd.Parameters.AddWithValue("@BookMarkText", bookmark.Text);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceRec, "SaveBookMarkAndGetByCallId", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            dr.Read();
                            bookmarks = new List<string>();
                            //string BookMarkXML = Convert.ToString(dr[0]);
                            string BookMarkXML = Convert.ToString(dr["BookMarkXML"]);
                            string BookMarkCSV = "";
                            if (!String.IsNullOrEmpty(BookMarkXML))
                            {
                                BookMarkCSV = BookMarkXML.ConvertXmlStringToCsvString();
                            }
                            bookmarks.Add(BookMarkXML);
                            bookmarks.Add(BookMarkCSV);
                        }
                    }
                }

                if (SiteConfig.RevSyncEnabled)
                    DALHelper.RevsyncAPICall(_tenantId, "VoiceRecDAL", "SaveBookMarkAndGetByCallId", JsonConvert.SerializeObject(bookmark));
                else if (SiteConfig.IsMTEnable)
                    DALHelper.SendMessageToHub(_tenantId, "VoiceRecDAL", "SaveBookMarkAndGetByCallId", JsonConvert.SerializeObject(bookmark));
            }
            catch (Exception ex) { throw ex; }
            return bookmarks;
        }

        public List<string> UpdateBookMarkAndGetByCallId(Bookmark bookmark)
        {
            List<string> bookmarks = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.BOOKMARK_UPDATE_N_GET;
                    cmd.Parameters.AddWithValue("@CallID", bookmark.CallId);
                    cmd.Parameters.AddWithValue("@BookMarkPos", bookmark.Position);
                    cmd.Parameters.AddWithValue("@BookMarkText", bookmark.Text);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceRec, "UpdateBookMarkAndGetByCallId", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            dr.Read();
                            bookmarks = new List<string>();
                            //string BookMarkXML = Convert.ToString(dr[0]);
                            string BookMarkXML = Convert.ToString(dr["BookMarkXML"]);
                            string BookMarkCSV = "";
                            if (!String.IsNullOrEmpty(BookMarkXML))
                            {
                                BookMarkCSV = BookMarkXML.ConvertXmlStringToCsvString();
                            }
                            bookmarks.Add(BookMarkXML);
                            bookmarks.Add(BookMarkCSV);
                        }
                    }
                }

                if (SiteConfig.RevSyncEnabled)
                    DALHelper.RevsyncAPICall(_tenantId, "VoiceRecDAL", "UpdateBookMarkAndGetByCallId", JsonConvert.SerializeObject(bookmark));
                else if (SiteConfig.IsMTEnable)
                    DALHelper.SendMessageToHub(_tenantId, "VoiceRecDAL", "UpdateBookMarkAndGetByCallId", JsonConvert.SerializeObject(bookmark));
            }
            catch (Exception ex) { throw ex; }
            return bookmarks;
        }


        #endregion

        #region Channels

        //public List<MonitorChannel> GetMonitorChannels()
        //{
        //    List<MonitorChannel> channels = null;
        //    MonitorChannel channel = null;
        //    try
        //    {
        //        using (var conn = DALHelper.GetConnection())
        //        using (var cmd = conn.CreateCommand())
        //        {
        //            cmd.CommandType = CommandType.StoredProcedure;
        //            cmd.CommandText = DBConstants.VoiceRec.CHANNELS_MONITOR_GET;

        //            conn.Open();
        //            using (SqlDataReader dr = cmd.ExecuteReader())
        //            {
        //                if (channels == null) channels = new List<MonitorChannel>();
        //                while (dr.Read())
        //                {
        //                    channel = new MonitorChannel();

        //                    channel.RowNo = Convert.ToInt32(dr["RowNo"]);
        //                    channel.CallID = Convert.ToString(dr["CallID"]);
        //                    channel.Ext = Convert.ToString(dr["Ext"]);
        //                    channel.ExtName = Convert.ToString(dr["ExtName"]);
        //                    channel.ExtStatus = Convert.ToInt32(dr["ExtStatus"]);
        //                    channel.StartTime = Convert.ToString(dr["StartTime"]);
        //                    //channel.Duration = Convert.ToInt32(dr["Duration"]);

        //                    channels.Add(channel);
        //                }
        //            }
        //        }
        //    }
        //    catch (Exception ex) { throw ex; }

        //    return channels;
        //}

        public List<MonitorChannel> GetMonitorChannels(string whereClause)
        {
            List<MonitorChannel> channels = null;
            //MonitorChannel channel = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CHANNELS_MONITOR_GET;
                    cmd.Parameters.AddWithValue("@WhereClause", whereClause);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceRec, "GetMonitorChannels", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (channels == null) channels = new List<MonitorChannel>();
                        while (dr.Read())
                        {
                            //channel = new MonitorChannel();
                            ////channel.RowNo = Convert.ToInt32(dr["RowNo"]);
                            //channel.UserNum = Convert.ToInt32(dr["UserNum"]);
                            //channel.UserName = Convert.ToString(dr["UserName"]);
                            //channel.GroupNum = Convert.ToInt32(dr["GroupNum"]);
                            //channel.GroupName = Convert.ToString(dr["GroupName"]);
                            //channel.Ext = Convert.ToString(dr["Ext"]);
                            //channel.AgentIP = Convert.ToString(dr["AgentIP"]);
                            //channel.UserPW = Convert.ToString(dr["UserPW"]);
                            //channel.ROD = Convert.ToString(dr["ROD"]);
                            //channel.POD = Convert.ToString(dr["POD"]);
                            //channel.EOD = Convert.ToString(dr["EOD"]);
                            //channel.SOD = Convert.ToString(dr["SOD"]);
                            //channel.ExtName = Convert.ToString(dr["ExtName"]);
                            //channel.UserEmail = Convert.ToString(dr["UserEmail"]);
                            //channel.RecId = Convert.ToInt32(dr["RecId"]);
                            //channel.RecIP = Convert.ToString(dr["RecIP"]);
                            //channel.RecPort = Convert.ToString(dr["RecPort"]);
                            //channel.Status = "Inactive";

                            var channel = new MonitorChannel
                            {
                                UserNum = Convert.ToInt32(dr["UserNum"]),
                                UserName = Convert.ToString(dr["UserName"]),
                                GroupNum = Convert.ToInt32(dr["GroupNum"]),
                                GroupName = Convert.ToString(dr["GroupName"]),
                                Ext = Convert.ToString(dr["Ext"]),
                                AgentIP = Convert.ToString(dr["AgentIP"]),
                                UserPW = Convert.ToString(dr["UserPW"]),
                                ROD = Convert.ToString(dr["ROD"]),
                                POD = Convert.ToString(dr["POD"]),
                                EOD = Convert.ToString(dr["EOD"]),
                                SOD = Convert.ToString(dr["SOD"]),
                                ExtName = Convert.ToString(dr["ExtName"]),
                                UserEmail = Convert.ToString(dr["UserEmail"]),
                                RecId = Convert.ToInt32(dr["RecId"]),
                                RecIP = Convert.ToString(dr["RecIP"]),
                                RecPort = Convert.ToInt16(dr["RecPort"]),
                                Status = "Inactive"
                            };  

                            channels.Add(channel);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return channels;
        }


        #endregion

        #region Simple User Rights

        public string GetSimpleUserSearchCriteria(SimpleUserSearchCriteria simpleUserSearchCriteria)
        {
            string usersExts = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.SIMPLE_USER_SEARCH_CRITERIA;
                    cmd.Parameters.AddWithValue("@UserNum", simpleUserSearchCriteria.UserNum);
                    cmd.Parameters.AddWithValue("@UserID", simpleUserSearchCriteria.UserId);
                    cmd.Parameters.AddWithValue("@AuthNum", simpleUserSearchCriteria.AuthNum);
                    cmd.Parameters.AddWithValue("@AuthType", simpleUserSearchCriteria.AuthType);
                    cmd.Parameters.AddWithValue("@Type", simpleUserSearchCriteria.Type);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceRec, "GetSimpleUserSearchCriteria", _tenantId));

                    usersExts = Convert.ToString(cmd.ExecuteScalar());
                    return usersExts;
                }
            }
            catch (Exception ex) { throw ex; }


        }


        #endregion


        #region Get Search Page Data

        public DataContracts.Response.VoiceRecResponse GetSearchPageData(DataContracts.Request.VoiceRecRequest voiceRecRequest)
        {
            return null;
            //List<PlayList> playlists = null;
            //PlayList playlist = null;
            //try
            //{
            //    using (var conn = DALHelper.GetConnection())
            //    using (var cmd = conn.CreateCommand())
            //    {
            //        cmd.CommandType = CommandType.StoredProcedure;
            //        cmd.CommandText = "vr_SearchPage_GetData";
            //        cmd.Parameters.AddWithValue("@UserId", voiceRecRequest.UserId);

            //        conn.Open();
            //        //Total 8 Tables return.
            //        using (SqlDataReader dr = cmd.ExecuteReader())
            //        {
            //            //1. Playlist ResultSet
                        
            //            dr.NextResult();
            //            //2. Survey ResultSet
                        
            //            dr.NextResult();
            //            //3. CallInfo ResultSet
            //        }
            //    }
            //}
            //catch (Exception ex) { throw ex; }
            //return playlists;
        }

        #endregion

        #region Playlist

        //public int SavePlaylist(Playlist playlist, long userId)
        //{
        //    try
        //    {
        //        using (var conn = DALHelper.GetConnection())
        //        using (var cmd = conn.CreateCommand())
        //        {
        //            cmd.CommandType = CommandType.StoredProcedure;
        //            cmd.CommandText = DBConstants.VoiceRec.PLAYLIST_INSERT;
        //            cmd.Parameters.AddWithValue("@Id", playlist.Id);
        //            cmd.Parameters.AddWithValue("@Name", playlist.Name);
        //            cmd.Parameters.AddWithValue("@UserId", userId);
        //            //cmd.Parameters.AddWithValue("@Description", description);
        //            //cmd.Parameters.AddWithValue("@IsDeleted", isDeleted);
        //            //cmd.Parameters.AddWithValue("@CreatedDate", createdDate);
        //            //cmd.Parameters.AddWithValue("@ModifiedDate", modifiedDate);

        //            conn.Open();
        //            return Convert.ToInt32(cmd.ExecuteScalar());
        //            //cmd.Parameters.Add("@Id", SqlDbType.Int).Direction = ParameterDirection.Output;
        //            ////playlist.Id = Convert.ToInt32(cmd.ExecuteScalar());
        //            //int statusValue= Convert.ToInt32(cmd.ExecuteScalar());//0:update, >=1: insert, -1: error
        //            //if (statusValue != -1)
        //            //    playlist.Id = statusValue > 0 ? statusValue : playlist.Id;
        //        }
        //    }
        //    catch (Exception ex) { throw ex; }
        //    //finally{}
        //}

        //public bool DeletePlaylist(int playlistId)
        //{
        //    using (var conn = DALHelper.GetConnection())
        //    using (var cmd = conn.CreateCommand())
        //    {
        //        conn.Open();
        //        cmd.CommandType = CommandType.StoredProcedure;
        //        cmd.CommandText = DBConstants.VoiceRec.PLAYLIST_DELETE;
        //        cmd.Parameters.AddWithValue("@PlayListId", playlistId);
        //        int count = (int)cmd.ExecuteScalar(); //statement:- 1 for Success, -1 for Failure

        //        return count == 1 ? true : false;
        //    }
        //}

        //public List<Playlist> GetPlaylist(int userId)
        //{
        //    List<Playlist> playlists = null;
        //    Playlist playlist = null;
        //    try
        //    {
        //        using (var conn = DALHelper.GetConnection())
        //        using (var cmd = conn.CreateCommand())
        //        {
        //            cmd.CommandType = CommandType.StoredProcedure;
        //            cmd.CommandText = DBConstants.VoiceRec.PLAYLIST_GETLIST_BY_USERID;
        //            cmd.Parameters.AddWithValue("@UserId", userId);

        //            conn.Open();
        //            using (SqlDataReader dr = cmd.ExecuteReader())
        //            {
        //                playlists = new List<Playlist>();
        //                while (dr.Read())
        //                {
        //                    playlist = new Playlist();
        //                    playlist.Id = (int)dr["Id"];
        //                    playlist.Name = Convert.ToString(dr["Name"]);
        //                    /*playlist.UserId = (int)dr["UserId"];
        //                    playlist.Description = Convert.ToString(dr["Description"]);
        //                    playlist.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
        //                    playlist.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
        //                    playlist.ModifiedDate = Convert.ToDateTime(dr["ModifiedDate"]);*/

        //                    playlists.Add(playlist);
        //                }
        //            }
        //        }
        //    }
        //    catch (Exception ex) { throw ex; }
        //    return playlists;
        //}

        //public short AddTracksInsidePlaylist(int playlistId, string callIds, int userId, int MaxItems)
        //{
        //    //callIds = "207,208,209";
        //    try
        //    {
        //        using (var conn = DALHelper.GetConnection())
        //        using (var cmd = conn.CreateCommand())
        //        {
        //            cmd.CommandType = CommandType.StoredProcedure;
        //            cmd.CommandText = DBConstants.VoiceRec.PLAYLIST_DETAILS_INSERT;
        //            cmd.Parameters.AddWithValue("@PlaylistId", playlistId);
        //            cmd.Parameters.AddWithValue("@CallIds", callIds);
        //            cmd.Parameters.AddWithValue("@UserId", userId);
        //            cmd.Parameters.AddWithValue("@MAXItems", MaxItems);
        //            //cmd.Parameters.AddWithValue("@IsDeleted", isDeleted);
        //            //cmd.Parameters.AddWithValue("@CreatedDate", createdDate);
        //            //cmd.Parameters.AddWithValue("@ModifiedDate", modifiedDate);

        //            conn.Open();
        //            //cmd.Parameters.Add("@Id", SqlDbType.Int).Direction = ParameterDirection.Output;  
        //            return Convert.ToInt16(cmd.ExecuteScalar());

        //        }
        //    }
        //    catch (Exception ex) { throw ex; }
        //}

        //public List<PlaylistDetail> GetTracksInsidePlaylist(int playlistId)
        //{
        //    List<PlaylistDetail> calls = null;
        //    try
        //    {
        //        using (var conn = DALHelper.GetConnection())
        //        using (var cmd = conn.CreateCommand())
        //        {
        //            cmd.CommandType = CommandType.StoredProcedure;
        //            cmd.CommandText = DBConstants.VoiceRec.PLAYLIST_DETAILS_BY_ID;
        //            cmd.Parameters.AddWithValue("@PlaylistId", playlistId);

        //            conn.Open();
        //            using (SqlDataReader dr = cmd.ExecuteReader())
        //            {
        //                //calls = ORMapper.MapPlayListDetails(dr);
        //                calls = ORMapper.MapPlayListDetailsForDemo(dr);
        //            }
        //        }
        //    }
        //    catch (Exception ex) { throw ex; }

        //    return calls;
        //}

        //public bool DeletePlaylistDetails(int playlistId)
        //{
        //    using (var conn = DALHelper.GetConnection())
        //    using (var cmd = conn.CreateCommand())
        //    {
        //        conn.Open();
        //        cmd.CommandType = CommandType.StoredProcedure;
        //        cmd.CommandText = DBConstants.VoiceRec.PLAYLIST_DETAILS_DELETE_BY_PLAYLIST_ID;
        //        cmd.Parameters.AddWithValue("@PlayListId", playlistId);
        //        int count = (int)cmd.ExecuteScalar(); //statement:- 1 for Success, -1 for Failure

        //        return count == 1 ? true : false;
        //    }
        //}

        //public bool DeletePlaylistDetail(int playlistDetailId)
        //{
        //    using (var conn = DALHelper.GetConnection())
        //    using (var cmd = conn.CreateCommand())
        //    {
        //        conn.Open();
        //        cmd.CommandType = CommandType.StoredProcedure;
        //        cmd.CommandText = DBConstants.VoiceRec.PLAYLIST_DETAILS_DELETE_BY_ID;
        //        cmd.Parameters.AddWithValue("@PlayListDetailId", playlistDetailId);
        //        int count = (int)cmd.ExecuteScalar(); //statement:- 1 for Success, -1 for Failure

        //        return count == 1 ? true : false;
        //    }
        //}


        #endregion

        #region Onsite Contact Information

        public int UpdateOnsiteContactInfoConfirmation(int UserNum, string Comments)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.ONSITE_CONTACT_INFO_CONFIRMATION_UPDATE;
                    cmd.Parameters.AddWithValue("@UserNum", UserNum);
                    cmd.Parameters.AddWithValue("@Comments", Comments);
                    cmd.Parameters.AddWithValue("@LastConfirmedOn", DateTime.Now);
                    //cmd.Parameters.AddWithValue("@Ext", callInfo.CalledID);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceRec, "UpdateOnsiteContactInfoConfirmation", _tenantId));

                    return Convert.ToInt16(cmd.ExecuteNonQuery());
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public OnsiteContactInfo GetOnsiteContactInfoConfirmation()
        {
            OnsiteContactInfo objContactInfo = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.ONSITE_CONTACT_INFO_CONFIRMATION_GET;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceRec, "GetOnsiteContactInfoConfirmation", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        objContactInfo = new OnsiteContactInfo();

                        if (dr.HasRows)
                        {
                            if (dr.Read())
                            {
                                objContactInfo.Comments = Convert.ToString(dr["Comments"]);
                                objContactInfo.LastConfirmedBy = Convert.ToInt32(dr["LastConfirmedBy"]);
                                objContactInfo.LastConfirmedOn = Convert.ToDateTime(dr["LastConfirmedOn"]);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return objContactInfo;
        }

        #endregion

    }

    public static class DataReaderExtensions
    {
        public static bool FieldExists(this IDataReader reader, string fieldName)
        {
            reader.GetSchemaTable().DefaultView.RowFilter = string.Format("ColumnName= '{0}'", fieldName);
            return (reader.GetSchemaTable().DefaultView.Count > 0);
        }
    }

}
