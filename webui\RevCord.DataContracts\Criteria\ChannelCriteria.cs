﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace RevCord.DataContracts.Criteria
{
    public class ChannelCriteria
    {
        /// <summary>
        /// Flag as to whether to Get all Channels and exclude where clause.
        /// </summary>
        public bool GetAllChannels { get; set; }

        /// <summary>
        /// Selected Channels
        /// </summary>
        //public List<string> Channels { get; set; }

        public CategoryGroupExtension CategoryGroupExtension { get; set; }
        public List<RecorderCategoryGroupExtension> RecorderCategoryGroupExtensions { get; set; }
        public int RecorderId { get; set; }

    }
}
