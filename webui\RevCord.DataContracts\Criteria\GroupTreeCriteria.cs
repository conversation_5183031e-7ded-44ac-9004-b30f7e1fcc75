﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace RevCord.DataContracts.Criteria
{
    public class GroupTreeCriteria
    {
        public int UserNum { get; set; }

        public string UserId { get; set; }

        /// <summary>
        /// Channels, Users
        /// </summary>
        public GroupNodeType TreeType { get; set; }

        public int AuthNum { get; set; }

        public string AuthType { get; set; }

        public int Type { get; set; }

        public int UserType { get; set; }

        public int SelectType { get; set; }

        public bool IsAudioOnly { get; set; }
        public bool HideTextAndScreensTree { get; set; }

        public bool IsRevCellRequired { get; set; }
        public bool IsAgentNodesRequired { get; set; }

        public bool IsEnterpriseTree { get; set; }

        public bool IsRadioTalkGroup { get; set; }
        public bool IsArchiveTalkGroup { get; set; }
    }
}
