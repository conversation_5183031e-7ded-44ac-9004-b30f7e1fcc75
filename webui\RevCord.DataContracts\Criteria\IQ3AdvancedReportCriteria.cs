﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.Criteria
{
    public class IQ3AdvancedReportCriteria
    {
        public string TempLogoFileName { get; set; }
        public int ThumbnailSize { get; set; }
        public bool ShowTitlePage { get; set; }
        public bool ShowTableOfContents { get; set; }
        public bool ShowFooter { get; set; }
        public bool ShowCustomFields { get; set; }
        public bool ShowFlaggedItemPriority { get; set; }
        public bool ShowFlaggedItems { get; set; }
        public bool ShowMarkerNotes { get; set; }
        public bool ShowTimeStamps { get; set; }
        public bool ShowMarkerDescription { get; set; }
        public bool ShowMarkerVideoLink { get; set; }
        public bool ShowEventData { get; set; }
        public bool ShowAssetData { get; set; }
    }
}