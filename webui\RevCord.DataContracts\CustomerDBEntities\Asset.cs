﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.CustomerDBEntities
{
    public class Asset
    {
        //Id Status  CreatedOn UpdatedOn   IsDeleted AssetId Field2 Field3
        public int Id { get; set; }
        public string AssetId { get; set; }
        public string AssetPhoto { get; set; }
        public string Field3 { get; set; }

        public int Status { get; set; }
        public DateTime CreatedOn { get; set; }
        public DateTime UpdatedOn { get; set; }
        public bool IsDeleted { get; set; }
        
        public List<AssetDetail> AssetDetails { get; set; }
    }
}
