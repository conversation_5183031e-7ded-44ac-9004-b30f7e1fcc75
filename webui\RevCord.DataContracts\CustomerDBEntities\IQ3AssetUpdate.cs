﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.CustomerDBEntities
{
    public class IQ3AssetUpdate
    {
        public int AssetId { get; set; }
        public List<IQ3AssetData> IQ3AssetData { get; set; }
        public List<IQ3AssetDetail> IQ3AssetDetails { get; set; }
    }

    public class IQ3AssetData
    {
        public int Id { get; set; }
        public string AssetId { get; set; }
        public string Field { get; set; } // Field Name
        public string FieldTitle { get; set; } // Field Caption
        public string Value { get; set; } // Field Value
        public string OldValue { get; set; } // Field Value
    }

    public enum IQ3AssetStatus
    {
        [DescriptionAttribute("Available")]
        Available = 1,
        [DescriptionAttribute("Unavailable")]
        Unavailable = 2,
    }
}
