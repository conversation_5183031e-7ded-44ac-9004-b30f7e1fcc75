﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace RevCord.DataContracts.DTO
{
    public class CallEvaluationDTO
    {
        public int RowNo { get; set; }
        public int CallEvaluationId { get; set; }
        public int CallEvaluationRevSyncId { get; set; }
        public int CallEvaluationCode { get; set; }
        public int? SurveyId { get; set; }
        public int? RevSyncSurveyId { get; set; }
        public string SurveyName { get; set; }
        public short StatusId { get; set; }
        public int AppUserId { get; set; }
        public bool IsAgentAssociated { get; set; }
        public string AssociatedAgent { get; set; }
        public int AssociatedAgentCode { get; set; }
        public string AssociatedAgentEmail { get; set; }
        public string Feedback { get; set; }
        public float EvaluatedScore { get; set; }
        public float TotalScore { get; set; }
        public int TotalQuestions { get; set; }
        public int AnsweredQuestions { get; set; }
        public bool IsShared { get; set; }
        public string SharedWith { get; set; }

        //public int LocalCallId { get; set; }//Local Call Id
        public string CallID { get; set; }
        public int GroupNum { get; set; }
        public int UserNum { get; set; }
        public string UserEmail { get; set; }
        public string GroupName { get; set; }
        public string UserName { get; set; }
        public string Evaluator { get; set; }
        public string EvaluatorEmail { get; set; }
        public string EvaluatorComments { get; set; }
        public string StartTime { get; set; }
        public int Duration { get; set; }
        public string Channel { get; set; }
        public string ExtName { get; set; }
        public string FileName { get; set; }
        public string ScreenFileNames { get; set; }
        public int CallType { get; set; }
        public bool IsRevCell { get; set; }

        public int CallType_inq { get; set; }
		public int RecId { get; set; }
        public string RecIP { get; set; }
        public string RecName { get; set; }
        public string BookmarkXML { get; set; }
        public bool IsPictureEvent { get; set; }
        public bool IsVirtualInspection { get; set; }
        public bool IsRevView { get; set; }
        public string RevViewFileName { get; set; }
        public string RevViewStartTime { get; set; }
        public string RevViewPhoneNumber { get; set; }
        public string RevViewAgentName { get; set; }
        public string EventName { get; set; }

        public EvaluationStatus Status
        {
            get { return (EvaluationStatus)StatusId; }
            set { StatusId = (byte)value; }
        }

        public DateTime CreatedDate { get; set; }
        public DateTime? CompletedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public int CreatedBy { get; set; }
        public int ModifiedBy { get; set; }
        public int EvaluationType { get; set; }


    }
}
