﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace RevCord.DataContracts.DTO
{
    public class CallInfoSearchResultDTO
    {
        #region Properties

        public int Index { get; set; }
        public string CallID { get; set; }
        public string FileName { get; set; }
        public string StartTime { get; set; }
        public string Time { get; set; }//public string CallTime { get; set; }
        public string RecorderIP { get; set; }
        public string InHDD { get; set; }
        public int CallIndex { get; set; }
        public int BackupID { get; set; }
        public string GroupName { get; set; }
        public string UserName { get; set; }
        public string TimeInfo { get; set; }
        public string Duration { get; set; }
        public string ExtensionID { get; set; }//Channel
        public string ExtName { get; set; }//public string ExtensionName { get; set; }
        public string Called { get; set; }//public string CallerCalleeNumber { get; set; }
        public string CallTag { get; set; }
        public string CallComment { get; set; }
        public string RODCustName { get; set; }
        public string RODCustNum { get; set; }
        public string RODCustInfo2 { get; set; }
        public string RODCustInfo3 { get; set; }
        public string RODCustInfo4 { get; set; }
        public string ANIName { get; set; }
        public string ANIPhone { get; set; }
        public string ANIDetails { get; set; }
        public string VideoFileName { get; set; }
        public string Bookmarks { get; set; }
        public string RetainValue { get; set; }

        #endregion

        #region Constructor's

        public CallInfoSearchResultDTO() { }

        public CallInfoSearchResultDTO(int index, string callID, string fileName
                                    , string startTime, string callTime, string recorderIP
                                    , string inHDD, int callIndex, int backupID
                                    , string groupName, string userName, string timeInfo
                                    , string duration, string extensionID, string extensionName
                                    , string callerCalleeNumber, string callTag, string callComment
                                    , string rODCustName, string rODCustNum, string rODCustInfo2
                                    , string rODCustInfo3, string rODCustInfo4, string aNIName
                                    , string aNIPhone, string aNIDetails, string VideoFileName
                                    , string Bookmarks, string RetainValue)
        {

            this.Index = index + 1;
            this.CallID = callID;
            this.FileName = fileName;
            this.StartTime = startTime;
            this.Time = callTime;
            this.RecorderIP = recorderIP;
            this.InHDD = inHDD;
            this.CallIndex = callIndex;
            this.BackupID = backupID;
            this.GroupName = groupName;
            this.UserName = userName;
            this.TimeInfo = timeInfo;
            this.Duration = duration;
            this.ExtensionID = extensionID;
            this.ExtName = extensionName;
            this.Called = callerCalleeNumber;
            this.CallTag = callTag;
            this.CallComment = callComment;
            this.RODCustName = rODCustName;
            this.RODCustNum = rODCustNum;
            this.RODCustInfo2 = rODCustInfo2;
            this.RODCustInfo3 = rODCustInfo3;
            this.RODCustInfo4 = rODCustInfo4;
            this.ANIName = aNIName;
            this.ANIPhone = aNIPhone;
            this.ANIDetails = aNIDetails;
            this.VideoFileName = VideoFileName;
            this.Bookmarks = Bookmarks;
            this.RetainValue = RetainValue;
        }

        #endregion

    }
}
