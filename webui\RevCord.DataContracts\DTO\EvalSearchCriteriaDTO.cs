﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace RevCord.DataContracts.DTO
{
    /// <summary>
    /// Used for Search Evaluations
    /// </summary>
    public class EvalSearchCriteriaDTO
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string CampaignName { get; set; }
        public string AgentName { get; set; }
        public int AgentNum { get; set; }
        public string EvaluatorName { get; set; }
        public bool IsEvaluatorSearch { get; set; }
        public int? MinScore { get; set; }
        public int? MaxScore { get; set; }
        public SharedValue SharedValue { get; set; }
        public bool IsSearchActive { get; set; }
        public bool IsEnterpriseAssociatedEvalSearch { get; set; }
        public int EvaluationType { get; set; }
    }
}
