﻿using RevCord.DataContracts.VoiceRecEntities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.DTO
{
    public class RecorderDALMediaResponse
    {
        public int RecorderId { get; set; }
        public string RecorderName { get; set; }
        public List<MediaInfo> ListOfMedias { get; set; }
        public int TotalPages { get; set; }
        public long TotalRecords { get; set; }
        public int PageSize { get; set; }
    }
}
