﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace RevCord.DataContracts.DTO
{
    [Serializable]
    public class TreeViewDataDTO
    {
        public string NodeId { get; set; }
        public string NodeCaption { get; set; }
        public int ParentNodeId { get; set; }
        public int Depth { get; set; }
        public int MenuType { get; set; }
        public int ViewType { get; set; }
        public bool IsGroup { get; set; }
        public string Param1 { get; set; }
        public string Param2 { get; set; }
        public string Param3 { get; set; }
        public object Clone()
        {
            return ObjectUtil.DeepClone(this);
        }
    }
}