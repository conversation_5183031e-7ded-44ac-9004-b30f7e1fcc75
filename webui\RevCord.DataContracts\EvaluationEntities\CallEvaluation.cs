﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.SurveyEntities;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.DataContracts.UserManagement;

namespace RevCord.DataContracts.EvaluationEntities
{
    public class CallEvaluation
    {
        //public int UserId { get; set; }
        //public int TotalQuestions { get; set; }
        //public int NoOfEvaluatedQuestions { get; set; }
        public float EvaluatedScore { get; set; }

        #region Properties

        public long Id { get; set; }
        public long RevSyncId { get; set; }
        public long Code { get; set; }
        public string SupervisorComments { get; set; }
        public bool IsShared { get; set; }
        public bool IsDeleted { get; set; }
        public bool IsAgentAssociated { get; set; }
        public string AssociatedAgent { get; set; }
        public int AssociatedAgentCode { get; set; }
        public string AssociatedAgentEmail { get; set; }
        public short StatusId { get; set; }
        public EvaluationStatus Status
        {
            get { return (EvaluationStatus)StatusId; }
            set { StatusId = (byte)value; }
        }
        public DateTime CreatedDate { get; set; }
        public DateTime? CompletedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public int CreatedBy { get; set; }
        public int ModifiedBy { get; set; }

        public int AppUserId { get; set; }
        public int UserId { get; set; }
        public int SurveyId { get; set; }
        /*********************** Custom Properties *******************/
        public int NoOfEvaluatedQuestions
        {
            get
            {
                return (from a in this.Answers
                        select a).GroupBy(a => a.QuestionId).Count();
            }
        }
        public int NoOfSurveyQuestions
        {
            get
            {
                //return this.Survey.Questions == null ? 0 : this.Survey.Questions.Count;
                return this.Survey.NoOfQuestions;
            }
        }
        public int RecId { get; set; }
        public string RecIP { get; set; }
        public string RecName { get; set; }
        //public float GetResult
        //{
        //    get
        //    {
        //        float totalScore = (from ans in this.Answers
        //                            from opt in ans.Options
        //                            select (float?)opt.Score).Sum() ?? 0;

        //        //float totalScore = this.Answers.SelectMany(ans => ans.Options)
        //        //                               .Sum(opt => (float?)opt.Score) ?? 0;

        //        return totalScore;
        //    }
        //}

        //public float TotalScoreSurvey
        //{
        //    get
        //    {
        //        float totalScore = (from ques in this.Survey.Questions
        //                            from opt in ques.Options
        //                            where ques.Type == ControlType.Checkbox || ques.Type == ControlType.Radio
        //                            select (float?)opt.Score).Sum() ?? 0;

        //        return totalScore;
        //    }
        //}

        /*********************** Custom Properties *******************/

        #endregion

        #region Methods

        public IEnumerable<Option> FindAnswerOptionsFor(int questionId)
        {
            //this.Answers.ToList().FindAll(a=>a.Options
            var r = this.Answers.Where(a => a.QuestionId == questionId)
                                .SelectMany(a => a.Options)
                                .Where(o => o.QuestionId == questionId);


            //return (from ans in this.Answers
            //        where ans.QuestionId == questionId
            //        from opt in ans.Options
            //        where opt.QuestionId == questionId
            //        select ans.Options.ToList()
            //        );
            return r != null ? r : null;
        }

        #endregion

        #region Associations
        public User Supervisor { get; set; }
        public User Agent { get; set; }
        public Survey Survey { get; set; }
        public CallInfo CallInfo { get; set; }
        public List<CallInfo> CallSegments { get; set; }
        public List<Answer> Answers { get; set; }

        public bool IsSegmented { get; set; }
        public int EvaluationType { get; set; }
        public int MultiCallEvaluationId { get; set; }

        #endregion

    }
}
