﻿using RevCord.DataContracts.VoiceRecEntities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.EvaluationEntities
{
    public class MultiCallEvaluationGroupDetail
    {
        public int Id { get; set; }
        public int GroupId { get; set; }
        public string CallId { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsDeleted { get; set; }
    }
}