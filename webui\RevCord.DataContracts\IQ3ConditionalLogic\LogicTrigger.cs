﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.IQ3ConditionalLogic
{
    public class LogicTrigger
    {
        public int Id { get; set; }
        public int LogicId { get; set; }
        public TriggerType TriggerType { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsDeleted { get; set; }

        #region Associations
        public TriggerAction TriggerAction { get; set; }
        //public List<TriggerAction> TriggerActions { get; set; }
        #endregion
    }
}