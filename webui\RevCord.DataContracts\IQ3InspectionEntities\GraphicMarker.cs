﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.IQ3InspectionEntities
{
    public class GraphicMarkerDetail
    {
        public int Id { get; set; }
        public int InspectionTemplateId { get; set; }
        public int MarkerTypeId { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public string Note { get; set; }
        public bool IsRepeating { get; set; }
        public bool IsPhotoAllowed { get; set; }
        public bool IsMultiSection { get; set; }
        public bool IsRequired { get; set; }
        public int Ordering { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsDeleted { get; set; }

        public List<Section> Sections { get; set; }
    }
}
