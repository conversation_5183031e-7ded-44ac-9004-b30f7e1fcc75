﻿using RevCord.DataContracts.MessageBase;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.IQ3InspectionEntities
{
    public class InspectionTemplate
    {
        public int Id { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public bool HasSections { get; set; }
        public bool IsShared { get; set; }
        public DateTime CreatedDate { get; set; }
        public int CreatedBy { get; set; }
        public DateTime ModifiedDate { get; set; }
        public int ModifiedBy { get; set; }
        public bool IsDeleted { get; set; }
        public int TemplateRevSyncServerID { get; set; }

        public string InspectionSharedWith { get; set; }
        public string Owner { get; set; }
        public int NoOfSections { get; set; }
        public int NoOfMarkers { get; set; }
        public bool IsCurrentUserTemplate { get; set; }
        public int TemplateType { get; set; }

        public bool DisableCopyMarker { get; set; }
        public bool IsPassFailRequired { get; set; }

        public int InspectionTypeID { get; set; }
        public string InspectionType { get; set; }
        
        public List<InspectionTemplateSharer> TemplateSharers { get; set; }
        public List<PreInspection> PreInspections { get; set; }
        public List<Section> TemplateSections { get; set; }

        public List<PreInspectionGroup> PreInspectionGroups { get; set; }
        public List<PreInspection> PreInspectionGroupFields { get; set; }
    }
}
