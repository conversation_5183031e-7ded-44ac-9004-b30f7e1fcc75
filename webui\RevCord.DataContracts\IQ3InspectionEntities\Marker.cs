﻿using RevCord.DataContracts.IQ3ConditionalLogic;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.IQ3InspectionEntities
{
    public class Marker
    {
        public int Id { get; set; }
        public string EventId { get; set; }
        public int InspectionTemplateId { get; set; }
        public int SectionId { get; set; }
        public int MarkerId { get; set; }
        public int MarkerTypeId { get; set; }
        public int MarkerPosition { get; set; }
        public short MarkerOptionId { get; set; }
        public string Title { get; set; }
        public string MarkerAnswer { get; set; }
        public string Description { get; set; }
        public string Note { get; set; }
        public string MarkerMeasurement { get; set; }
        public bool IsSelected { get; set; }
        public bool IsRepeating { get; set; }
        public bool IsPhotoAllowed { get; set; }
        public bool IsPhotoUploaded { get; set; }
        public bool IsMultiSection { get; set; }
        public bool IsRequired { get; set; }
        public bool IsSupplementaryPhoto { get { return (this.IsPhotoAllowed == false && this.IsPhotoUploaded && this.PhotoFileName.Length > 0); } }
        public string PhotoFileName { get; set; }
        public bool IsVideoUploaded { get; set; }
        public string MarkerVideoFileName { get; set; }
        public string EventDateTime { get; set; }
        public string VideoFileName { get; set; }
        public int Ordering { get; set; }
        public int BookmarkFlag { get; set; }
        public string BookmarkFlagColorID { get; set; }
        public string BookmarkFlagName { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsDeleted { get; set; }
        public int ParentId { get; set; }

        public List<MarkerOption> MarkerOptions { get; set; }
        public List<MarkerSection> MarkerSections { get; set; }

        public GraphicMarkerDetail GraphicMarkerDetail { get; set; }

        public List<MarkerLogic> MarkerLogics { get; set; }

    }
}