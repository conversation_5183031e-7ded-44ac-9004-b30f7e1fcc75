﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.IQ3InspectionEntities
{
    public class PreInspection
    {
        public long Id { get; set; }
        public int InspectionTemplateId { get; set; }
        public string Title { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsDeleted { get; set; }

        // Use Pre Inspection Fields
        public string Description { get; set; }
        public int UserNum { get; set; }
        public bool IsUserPreInspection { get; set; }
        public int GroupId { get; set; }
    }
}
