﻿using RevCord.DataContracts.TenantEntities;
using RevCord.DataContracts.VoiceRecEntities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.IQ3
{
    public class TenantIQ3Usage
    {
        public int RowNo { get; set; }
        public int TenantId { get; set; }
        public TenantAppType TenantAppType { get; set; }
        public string Name { get; set; }
        public string CompanyName { get; set; }
        public string SoftwareVersion { get; set; }
        public DateTime CreatedDate { get; set; }

        public int NumberOfRecordings { get; set; }
        public long TenantRecDurationInMS { get; set; }
        public string TenantRecDurationHHMMSS { get; set; }
        public List<MediaInfo> TenantMediaInfos { get; set; }
    }
}