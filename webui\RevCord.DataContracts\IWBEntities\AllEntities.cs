﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.IWBEntities
{
    public enum IwbDocumentType
    {
        [EnumMember(Value = "MTR")]
        [DescriptionAttribute("MTR")]
        MTR = 1,
        [EnumMember(Value = "WPQ")]
        [DescriptionAttribute("WPQ")]
        WPQ,
        [EnumMember(Value = "WPS")]
        [DescriptionAttribute("WPS")]
        WPS,
        [EnumMember(Value = "PQR")]
        [DescriptionAttribute("PQR")]
        PQR
    }

    public enum IwbFieldComplianceStatus
    {
        [EnumMember(Value = "Pass")]
        [DescriptionAttribute("Pass")]
        Pass = 1,
        [EnumMember(Value = "Fail")]
        [DescriptionAttribute("Fail")]
        Fail,
        [EnumMember(Value = "Minor Deviation")]
        [DescriptionAttribute("Minor Deviation")]
        MinorDeviation,
    }

    public class IwbDocument
    {
        public int Id { get; set; }
        public string Alias { get; set; }
        public string Name { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public IwbDocumentType Type { get; set; }
        public string FileName { get; set; }//OriginalFileName
        public string FilePath { get; set; }
        public string FileExtension { get; set; }// Always a pdf, other formats gets converted to pdf
        public long FileSize { get; set; }
        public string OutputFileName { get; set; }
        public string JsonFileName { get; set; }
        public DateTime? UploadDate { get; set; }
        public DateTime? ExpiryDate { get; set; } // for WPQ

        public string Description { get; set; }
        public IList<IwbDocumentSection> Sections { get; set; }
        public int UserId { get; set; }
        public int OrganizationId { get; set; }
        public int CreatedBy { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? LastModifiedDate { get; set; }
        public int? LastModifiedBy { get; set; }

        //public string GeneratedContents { get; set; }//Received from AI

        //public IList<Tag> Tags { get; } = [];
        //public IList<IwbDocumentVersion> Versions { get; set; } = new List<IwbDocumentVersion>();
        public IwbMTR MTR { get; set; }
        public IwbWPS WPS { get; set; }
        public IwbWPQ WPQ { get; set; }

        public int SignDocumentId { get; set; }
        public string OwnerEmail { get; set; }

        public string SignLink { get; set; }

        public string UploadedBy { get; set; }

        public string OCRResult { get; set; }
        public bool IsSaved { get; set; }
    }

    public class IwbDocumentSection
    {
        //public IwbDocumentType DocumentType { get; set; }
        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public IList<IwbDocumentSectionField> Fields { get; set; }
    }


    public class IwbDocumentSectionField
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string ReportValue { get; set; }
        public string StandardValue { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public IwbFieldComplianceStatus ComplianceStatus { get; set; }
        public string Description { get; set; }
    }

    /*public class IwbDocumentVersion
    {
        public int Id { get; set; }
        public int VersionNo { get; set; }
        public string FileName { get; set; }

        public int DocumentId { get; set; }
        public IwbDocument Document { get; set; }
    }


    public class IwbDocumentTypeSection
    {
        public IwbDocumentType DocumentType { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public IList<IwbDocumentTypeSectionField> Fields { get; set; }
    }
    public class IwbDocumentTypeSectionField
    {
        public int Id { get; set; }
        public string Name { get; set; }
        //public string StandardValue { get; set; }
        public string Description { get; set; }
    }*/
}