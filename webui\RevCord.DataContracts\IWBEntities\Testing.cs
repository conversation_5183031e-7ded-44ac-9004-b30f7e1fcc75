﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.IWBEntities
{
    public class IwbTestType
    {
        public int Id { get; set; }
        public string CostCode { get; set; }
        public string JointType { get; set; }
        public string ProductForm { get; set; }
        public string AVPNumber { get; set; }
        public string AVBMThickness { get; set; }
        public string AVOD { get; set; }
        public string AVBMODWTFillet { get; set; }
        public string AVWeldingProcess { get; set; }
        public string AVWeldingType { get; set; }
        public string AVBacking { get; set; }
        public string AVClassification { get; set; }
        public string AVSpecification { get; set; }
        public string AVFNumber { get; set; }
        public string AVProductForm { get; set; }
        public string AVInsert { get; set; }
        public string AVDepositThickness { get; set; }
        public string AVGreaterThan3 { get; set; }
        public string AVPosition { get; set; }
        public string AVProgression { get; set; }
        public string AVShielding { get; set; }
        public string AVTypeOfShielding { get; set; }
        public string AVBackingGas { get; set; }
        public string AVTransferMode { get; set; }
        public string AVCurrent { get; set; }
        public string AVPolarity { get; set; }
        public string RQPNumber { get; set; }
        public string RQBMThickness { get; set; }
        public string RQOD { get; set; }
        public string RQWeldingProcess { get; set; }
        public string RQWeldingType { get; set; }
        public string RQBacking { get; set; }
        public string RQClassification { get; set; }
        public string RQSpecification { get; set; }
        public string RQFNumber { get; set; }
        public string RQProductForm { get; set; }
        public string RQInsert { get; set; }
        public string RQDepositThickness { get; set; }
        public string RQGreaterThan3 { get; set; }
        public string RQPosition { get; set; }
        public string RQProgression { get; set; }
        public string RQShielding { get; set; }
        public string RQTypeOfShielding { get; set; }
        public string RQBackingGas { get; set; }
        public string RQTransferMode { get; set; }
        public string RQCurrent { get; set; }
        public string RQPolarity { get; set; }
        public string TestingCode { get; set; }
        public string NDE { get; set; }

    }

    public enum IwbTestStatus
    {
        [EnumMember(Value = "Scheduled")]
        [DescriptionAttribute("Scheduled")]
        Scheduled = 1,

        [EnumMember(Value = "In Progress")]
        [DescriptionAttribute("In Progress")]
        InProgress = 2,

        [EnumMember(Value = "Completed")]
        [DescriptionAttribute("Completed")]
        Completed = 3,
    }

    public enum IwbTestRegistrationStatus
    {
        [EnumMember(Value = "Pending")]
        [DescriptionAttribute("Pending")]
        Pending = 1,

        [EnumMember(Value = "Approved")]
        [DescriptionAttribute("Approved")]
        Approved = 2,
    }
    public enum IwbTestPassStatus
    {
        [EnumMember(Value = "Pending")]
        [DescriptionAttribute("Pending")]
        Pending = 1,

        [EnumMember(Value = "Pass")]
        [DescriptionAttribute("Pass")]
        Pass = 2,

        [EnumMember(Value = "Fail")]
        [DescriptionAttribute("Fail")]
        Fail = 3,
    }
    public class IwbTest
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime Deadline { get; set; }
        public int NoOfPositions { get; set; }
        public int NoOfApplicants { get; set; }
        public int TypeId { get; set; }
        public string TypeCode { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public IwbTestStatus CurrentStatus { get; set; }
        public int OrganizationId { get; set; }

        public string Description { get; set; }
        public int CreatedBy { get; set; }
        public DateTime? CreatedDate { get; set; }
    }

    public class IwbTestAttendee
    {
        public int Id { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public IwbTestRegistrationStatus RegistrationStatus { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public IwbTestPassStatus PassStatus { get; set; }

        public UserManagement.User Welder { get; set; }

        public int TestId { get; set; }

        public Wpq WPQData { get; set; }
    }

    public class IwbTestInvitation
    {
        public int Id { get; set; }
        public int TestId { get; set; }
        public int WelderId { get; set; }
        public int OrganizationId { get; set; }
        public string Description { get; set; }
        public int CreatedBy { get; set; }
        public DateTime? CreatedDate { get; set; }
    }

    public class SignOffRequestUser
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public string OwnerEmail { get; set; }
        public SignStatus SignStatus { get; set; }
    }

    public class WelderRatingModel
    {
        public int Id { get; set; }
        public int WelderId { get; set; }
        public int PerformanceRating { get; set; }
        public string PerformanceComment { get; set; }
        public int WeldCount { get; set; }
        public string WeldCountComment { get; set; }
        public int Repairs { get; set; }
        public string RepairsComment { get; set; }
        public int WorkEthicRating { get; set; }
        public string WorkEthicComment { get; set; }
        public int CreatedBy { get; set; }
    }

    public class JobTitleModel
    {
        public int Id { get; set; }
        public string Title { get; set; }
    }

    public class JobDetailsModel
    {
        public int Id { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string Location { get; set; }
        public string RecordNumber { get; set; }
    }



}