﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.IWBEntities
{
    public class IwbWPQ
    {
        public int Id { get; set; }
        public int UserId { get; set; }//WelderId
        public int JobId { get; set; }
        public int WpsId { get; set; }

        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public DateTime ExpiryDate { get; set; }
        public string CertifiedAuthority { get; set; }
        public DateTime CertificateDate { get; set; }
        public string InspectorName { get; set; }
        public string LabNo { get; set; }
        public string Notes { get; set; }

        public List<IwbWPQSection> Sections { get; set; }

    }

    public class IwbWPQSection
    {
        public int Id { get; set; }
        public int WpqId { get; set; }
        public string Name { get; set; }
        public List<IwbWPQSectionField> Fields { get; set; }
    }

    public class IwbWPQSectionField
    {
        public int Id { get; set; }
        public int WpqId { get; set; }
        public int WpqSectionId { get; set; }
        public string KDP { get; set; }
        public string InputValue { get; set; }
        public string StandardValue { get; set; }
        public string ComplianceStatus { get; set; }
        public string Reason { get; set; }
    }
}