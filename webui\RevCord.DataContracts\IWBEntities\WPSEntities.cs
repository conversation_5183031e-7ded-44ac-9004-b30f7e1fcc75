﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.IWBEntities
{
    public class IwbWPS
    {
        public int Id { get; set; }

        [JsonProperty("WPS record number")]
        public string RecordNumber { get; set; }

        [JsonProperty("Date Qualified")]
        public string DateQualified { get; set; }

        [JsonProperty("Company name")]
        public string CompanyName { get; set; }

        [JsonProperty("Supporting PQR(s)")]
        public string SupportingPQRs { get; set; }
        [JsonProperty("Reference docs")]
        public string ReferenceDocs { get; set; }
        public List<string> Scope { get; set; }
        public string Joint { get; set; }

        [JsonProperty("BASE METALS")]
        public BaseMetal BaseMetal { get; set; }

        [JsonProperty("THICKNESS RANGE QUALIFIED")]
        public ThickNessRangeQualified ThickNessRangeQualified { get; set; }

        [JsonProperty("DIAMETER RANGE QUALIFIED")]
        public DiaMeterRangeQualified DiaMeterRangeQualified { get; set; }

        [JsonProperty("FILLER METALS")]
        public FillerMetals FillerMetals { get; set; }

        [JsonProperty("WELDING PROCEDURE")]
        public WeldingProcedure WeldingProcedure { get; set; }

        [JsonProperty("PREHEAT TABLE")]
        public PreHeatTable PreHeatTable { get; set; }

        [JsonProperty("TECHNIQUE")]
        public Technique Technique { get; set; }
    }
    public class BaseMetal
    {
        public MetalType Type { get; set; }

        [JsonProperty("Welded to")]
        public WeldedTo WeldedTo { get; set; }

        [JsonProperty("Backing:")]
        public Backing Backing { get; set; }
        public string Retainers { get; set; }

    }
    public class MetalType
    {
        [JsonProperty("material")]
        public string Material { get; set; }
        [JsonProperty("p_no")]
        public List<string> PNo { get; set; }
        [JsonProperty("group_no")]
        public List<string> GroupNo { get; set; }
    }
    public class WeldedTo
    {
        [JsonProperty("material")]
        public string Material { get; set; }
        [JsonProperty("p_no")]
        public List<string> PNo { get; set; }
        [JsonProperty("group_no")]
        public List<string> GroupNo { get; set; }
    }
    public class Backing
    {
        [JsonProperty("material")]
        public string Material { get; set; }
        [JsonProperty("p_no")]
        public List<string> PNo { get; set; }
        [JsonProperty("group_no")]
        public List<string> GroupNo { get; set; }
    }

    public class ThickNessRangeQualified
    {
        [JsonProperty("Complete pen.")]
        public CompletePen CompletePen { get; set; }
        [JsonProperty("Impact tested")]
        public ImpactTested ImpactTested { get; set; }
        [JsonProperty("Partial pen.")]
        public PartialPen PartialPen { get; set; }
        [JsonProperty("Fillet welds")]
        public FilletWelds FilletWelds { get; set; }
    }
    public class CompletePen
    {
        [JsonProperty("As-welded")]
        public AsWelded AsWelded { get; set; }
        [JsonProperty("With PWHT")]
        public WithPWHT WithPWHT { get; set; }
    }
    public class AsWelded
    {
        [JsonProperty("min")]
        public string Min { get; set; }
        [JsonProperty("max")]
        public string Max { get; set; }
    }
    public class WithPWHT
    {
        [JsonProperty("min")]
        public string Min { get; set; }
        [JsonProperty("max")]
        public string Max { get; set; }
    }

    public class ImpactTested
    {
        [JsonProperty("As-welded")]
        public AsWelded AsWelded { get; set; }
        [JsonProperty("With PWHT")]
        public WithPWHT WithPWHT { get; set; }
    }
    public class PartialPen
    {
        [JsonProperty("As-welded")]
        public AsWelded AsWelded { get; set; }
        [JsonProperty("With PWHT")]
        public WithPWHT WithPWHT { get; set; }
    }
    public class FilletWelds
    {
        [JsonProperty("As-welded")]
        public AsWelded AsWelded { get; set; }
        [JsonProperty("With PWHT")]
        public WithPWHT WithPWHT { get; set; }
    }

    public class DiaMeterRangeQualified
    {
        [JsonProperty("Nominal pipe size")]
        public NominalPipeSize NominalPipeSize { get; set; }
    }
    public class NominalPipeSize
    {
        [JsonProperty("As-welded")]
        public AsWelded AsWelded { get; set; }
        [JsonProperty("With PWHT")]
        public WithPWHT WithPWHT { get; set; }
    }

    public class FillerMetals
    {
        public SMAW SMAW { get; set; }
    }
    public class SMAW
    {
        public string SFA { get; set; }
        public string Classification { get; set; }
        [JsonProperty("F-no.")]
        public string FNo { get; set; }
        [JsonProperty("A-no.")]
        public string ANo { get; set; }
        [JsonProperty("Chemical analysis or Trade name")]
        public string ChemicalAnalysisOrTradeName { get; set; }
        [JsonProperty("THICKNESS RANGE QUALIFIED")]
        public SnawThickNessRangeQualified ThickNessRangeQualified { get; set; }
    }
    public class SnawThickNessRangeQualified
    {
        [JsonProperty("As-welded")]
        public AsWelded AsWelded { get; set; }
        [JsonProperty("With PWHT")]
        public WithPWHT WithPWHT { get; set; }
    }

    public class WeldingProcedure
    {
        [JsonProperty("Welding process")]
        public string WeldingProcess { get; set; }
        public string Type { get; set; }
        [JsonProperty("Minimum preheat/interpass temperature (°F)")]
        public string MinimumPreheatInterpassTemperature { get; set; }
        [JsonProperty("Maximum interpass temperature (°F)")]
        public string MaximumInterPassTemperature { get; set; }
        [JsonProperty("Filler metal size (in.)")]
        public List<string> FillerMetalSize { get; set; }

        [JsonProperty("Layer number")]
        public string LayerNumber { get; set; }
        public string Position { get; set; }
        [JsonProperty("Weld progression")]
        public string WeldProgression { get; set; }
        [JsonProperty("Current/polarity")]
        public string CurrentPolarity { get; set; }
        [JsonProperty("Waveform control")]
        public string WaveFormControl { get; set; }
        [JsonProperty("Energy (J)")]
        public List<string> Energy { get; set; }
        [JsonProperty("Power (J/s)")]
        public List<string> Power { get; set; }
        public List<string> Amperes { get; set; }
        public List<string> Volts { get; set; }
        [JsonProperty("Travel speed (in./min)")]
        public List<string> TravelSpeed { get; set; }
        [JsonProperty("Maximum heat input (kJ/in.)")]
        public List<string> MaximumHeatInput { get; set; }
        [JsonProperty("String or weave")]
        public string StringOrWweave { get; set; }
        [JsonProperty("Multi/Single pass per side")]
        public string MultiSinglePassPerSide { get; set; }
        [JsonProperty("Maximum pass thickness (in.)")]
        public string MaximumPassThickness { get; set; }
        [JsonProperty("Weld deposit chemistry")]
        public string WeldDepositChemistry { get; set; }
    }

    public class PreHeatTable
    {
        [JsonProperty("ASME B31.1 and B31.3")]
        public string ASMEB311AndB313 { get; set; }
        [JsonProperty("ASME B31.4 and B31.8")]
        public string ASMEB314AndB318 { get; set; }
        [JsonProperty("ASME Section I")]
        public string ASMESectionI { get; set; }
        [JsonProperty("ASME Section VIII Div. 1")]
        public string ASMESectionVIIIDiv1 { get; set; }
    }
    public class Technique
    {
        public string Peening { get; set; }
        [JsonProperty("Surface preparation")]
        public string SurfacePreparation { get; set; }
        [JsonProperty("Initial/interpass cleaning")]
        public string InitialInterpassCleaning { get; set; }
        [JsonProperty("Back gouging method")]
        public string BackGougingMethod { get; set; }
    }

}
