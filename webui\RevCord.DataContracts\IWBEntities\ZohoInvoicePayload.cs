﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.IWBEntities
{
    public class ZohoInvoicePayload
    {
        public string customer_id { get; set; }
        public string reference_number { get; set; }
        public string date { get; set; }
        public string due_date { get; set; }
        public int payment_terms { get; set; }
        public double discount { get; set; }
        public bool is_discount_before_tax { get; set; }
        public string discount_type { get; set; }
        public bool is_inclusive_tax { get; set; }
        public List<ZohoInvoiceItem> invoice_items { get; set; }
    }

    public class ZohoInvoiceItem
    {
        //public string name { get; set; }
        public string product_id { get; set; }
        public string description { get; set; }
        public double quantity { get; set; }
        public double rate { get; set; }
    }
}