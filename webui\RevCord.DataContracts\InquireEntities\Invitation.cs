﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace RevCord.DataContracts.InquireEntities
{
    public class Invitation
    {
        public int Id { get; set; }
        public string InvitationURL { get; set; }
        public string InviteCode { get; set; }
        public int SentBy { get; set; }
        public int SentTo { get; set; }
        public string SentByEmail { get; set; }
        public string SentToEmail { get; set; }
        public DateTime SentOn { get; set; }
        public DateTime? AcceptedOn { get; set; }
        public string Comments { get; set; }
        public bool IsDeleted { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? LastModifiedDate { get; set; }
        public int CreatedBy { get; set; }
        public int LastModifiedBy { get; set; }

        public InvitationStatus Status { get; set; }
    }
}
