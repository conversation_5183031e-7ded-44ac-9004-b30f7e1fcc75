﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.InquireEntities
{
    public class PictureEventDetail
    {
        public long Id { get; set; }
        public string CallId { get; set; }

        public DateTime? StartTime { get; set; }

        public string StartTimeString { get; set; }

        public string FileName { get; set; }

        public FileType FileType { get; set; }

        public int FileDuration { get; set; }

        public string FileNotes { get; set; }

        public DateTime CreatedDate { get; set; }
        public DateTime ModifiedDate { get; set; }
    }
}
