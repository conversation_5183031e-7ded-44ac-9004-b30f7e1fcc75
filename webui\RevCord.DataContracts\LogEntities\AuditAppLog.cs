﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.LogEntities
{
    public class AuditAppLog
    {
        public int Id { get; set; }
        public AuditAppType AuditAppType { get; set; }
        public int UserId { get; set; }
        public AuditLogCategory AuditLogCategoryId { get; set; }
        public int ExceptionCode { get; set; }
        public string Message { get; set; }
        public string StackTrace { get; set; }
        public string Query { get; set; }
        public List<string> Parameters { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsDeleted { get; set; }
    }
}