﻿using RevCord.DataContracts.Criteria;
using RevCord.DataContracts.IWBEntities;
using RevCord.DataContracts.MessageBase;
using System.Collections.Generic;

namespace RevCord.DataContracts.Messages
{
    public class IwbRequest : RequestBase
    {
        public Dictionary<string, object> ContextData;

        public IwbCriteria Criteria { get; set; }

        public int UserId { get; set; }
        public int DocumentId { get; set; }
        public Wps Wps { get; set; }

        public Job Job { get; set; }
        public JobApplicant JobApplicant { get; set; }

        public UserWorkHistory WorkHistory { get; set; }

        public int OrganizationId { get; set; }
        public int JobId { get; set; }
        public int JobStatusId { get; set; }
        public int JobApplicationStatusId { get; set; }

        public Organization Organization { get; set; }
        public string OrganizationAccountPassword { get; set; }
        public OrganizationLocation Location { get; set; }

        public int LocationId { get; set; }

        public Wpq Wpq { get; set; }

        public IwbDocument Document { get; set; }
        public IwbTest Test { get; set; }
        public int TestId { get; set; }
        public int WelderId { get; set; }
        public IwbUser User { get; set; }
        public IwbTestInvitation TestInvitation { get; set; }
        public List<int> Ids { get; set; }
        public string Description { get; set; }
        public WelderRatingModel WelderRating { get; set; }
        public string CustomerId { get; set; }
        public string ItemId { get; set; }
        public string JsonData { get; set; }
        public object ZohoInvoice { get; set; }
        public ZohoInvoicePayload ObjectData { get; set; }
        public List<ZohoTransaction> TransactionHistory { get; set; }
        public string CustomerIdZoho { get; set; }
    }
}