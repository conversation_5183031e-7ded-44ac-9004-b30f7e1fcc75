﻿using System;
using System.Collections.Generic;
using System.Linq;
using RevCord.DataContracts.MessageBase;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.DataContracts.ViewModelEntities;
using RevCord.DataContracts.SurveyEntities;
using RevCord.DataContracts.TenantEntities;
using RevCord.DataContracts.IQ3;
using RevCord.DataContracts.IQ3InspectionEntities;

namespace RevCord.DataContracts.Messages
{
    public class VRResponse : ResponseBase
    {
        public int PlaylistId { get; set; }
        public int BookmarkId { get; set; }
        public int RevSyncPlaylistID { get; set; }
        

        public List<CallInfo> Calls { get; set; }

        public List<CallInfoExportResult> CallExportResults { get; set; }

        public CallInfoLite CallInfoLite { get; set; }

        public CallInfo CallInfo { get; set; }

        public List<RecorderCallInfo> RecorderCalls { get; set; }

        public Conversation Conversation { get; set; }

        /************* Purpose: used for Paging ****************/
        public int TotalPages { get; set; }
        public long TotalRecords { get; set; }
        /************* Purpose: used for Paging ****************/


        public List<MonitorChannel> MonitorChannels { get; set; }
        public List<RecorderMonitorChannel> RecorderMonitorChannels { get; set; }
        public List<Recorder> Sites { get; set; }

        public List<string> AvailableBookmarks { get; set; }

        public List<GroupTree> AllGroups { get; set; }
        
        public List<Survey> Surveys { get; set; }
        public List<TreeviewData> TreeviewData { get; set; }
        public List<TreeviewData> TreeviewDataTeams { get; set; }
        public List<TreeviewData> TreeviewDataRevcell { get; set; }
        public List<TreeviewData> TreeviewDataInquire { get; set; }
        public List<TreeviewData> TreeviewDataMD { get; set; }
        public List<TreeviewData> TreeviewDataRadioTalkGroup { get; set; }
        public List<TreeviewData> TreeviewDataArchive { get; set; }
        public List<TreeviewData> TreeviewDataIwb { get; set; }

        public List<Playlist> Playlists { get; set; }
        public PlaylistNote PlaylistNote { get; set; }
        public PlaylistShare PlaylistShare { get; set; }
        public List<PlaylistShare> PlaylistShareList { get; set; }
        public List<PlaylistNote> PlaylistNotes { get; set; }

        public int TranscriptionId { get; set; }

        public List<InqFileVideoBookmark> lInqFileVideoBookmark { get; set; }

        public List<ChatTranscript> ChatTranscript { get; set; }
        public List<TenantColumnModel> ColumnsModel { get; set; }
        public List<CallTag> CallTag { get; set; }
        public List<BmFlag> BookmarkFlag { get; set; }
        public List<ScheduleEventInfo> ScheduledEvents { get; set; }
        public ScheduleEventInfo ScheduledEvent { get; set; }
        public List<CustomField> CustomFields { get; set; }

        public Channel Channel { get; set; }
        public List<Channel> Channels { get; set; }

        public int LastSavedId { get; set; }

        public List<UserSearch> UserSearches { get; set; }
        public UserSearch UserSearch { get; set; }

        public List<MediaInfo> ListOfMedias { get; set; }
        public int GroupId { get; set; }
        public TenantGateway TenantGateway { get; set; }
        public List<TenantGateway> TenantGateways { get; set; }

        public Inspection Inspection { get; set; }

        public string EventStatus { get; set; }

        public List<PreInspectionData> PreInspectionDataList { get; set; }
    }
}
