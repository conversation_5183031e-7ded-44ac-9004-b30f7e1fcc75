﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.DTO;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.DataContracts.SurveyEntities;
using RevCord.DataContracts.ViewModelEntities;

namespace RevCord.DataContracts.Response
{
    public class VoiceRecResponse
    {
        /************* Purpose: used for Paging ****************/
        public int TotalPages { get; set; }
        public long TotalRecords { get; set; }
        /************* Purpose: used for Paging ****************/

        /************* Purpose: for Demo only- delete this in future ****************/
        public int TotalPagesDemo { get; set; }
        public long TotalRecordsDemo { get; set; }
        /************* Purpose: for Demo only- delete this in future ****************/

        //public bool IsChainDBsConfigured { get; set; }

        public bool FlagStatus { get; set; }

        public short StatusFromDB { get; set; }

        public int StatusReceivedFromDB { get; set; }

        public string SimpleUserSearchValues { get; set; }

        public List<string> AvailableData { get; set; } //used for bookmarks

        #region Association

        public List<CallInfoSearchResultDTO> CallInfoSearchResultsDTO { get; set; }
        public List<CallInfo> CallInfoSearchResults { get; set; }
        public CallInfo CallInfo { get; set; }

        public Playlist Playlist { get; set; }
        public List<Playlist> Playlists { get; set; }
        public List<RapidSOSGPSData> RapidSOSGPSDatas { get; set; }
        public Bookmark Bookmark { get; set; }
        public List<MonitorChannel> MonitorChannels { get; set; }

        public List<CustomTableFieldHead> TableFields { get; set; }

        public List<Survey> Surveys { get; set; }


        public List<TreeviewData> TreeviewData { get; set; }
        public List<GroupTree> AllGroups { get; set; }
        public OnsiteContactInfo OnsiteContactInfo { get; set; }
        #endregion


    }

}
