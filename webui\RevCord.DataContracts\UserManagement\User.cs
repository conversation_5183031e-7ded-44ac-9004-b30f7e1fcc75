﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.ViewModelEntities;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.DataContracts.RoleManagement;
using RevCord.DataContracts.IWBEntities;

namespace RevCord.DataContracts.UserManagement
{
    [Serializable]
    public class User
    {
        public int UserNum { get; set; }
        public int TenantId { get; set; }
        public int GroupNum { get; set; }
        public int UserType { get; set; }
        public string UserID { get; set; }
        public string UserPW { get; set; }
        public string UserName { get; set; }
        public int ViewID { get; set; }
        public string Ext { get; set; }
        public string UserEmail { get; set; }
        public int Status { get; set; }
        public DateTime Create_T { get; set; }
        public DateTime Modify_T { get; set; }
        public DateTime Delete_T { get; set; }
        public string IdentityNumber { get; set; }
        public string UserPhone { get; set; }
        public string UserFax { get; set; }
        public DateTime JoinBeginDate { get; set; }
        public DateTime JoinEndDate { get; set; }
        public string Descr { get; set; }
        public int SearchRest { get; set; }
        public int POD { get; set; }
        public int EOD { get; set; }

        public string CompanyName { get; set; }

        public string IsAssignedForChannel { get; set; } //For Simple User Rights : Arivu
        public int ChannelType { get; set; }
        public int Pause { get; set; }
        public string ExtName { get; set; }
        public int SelectType { get; set; }
        public int DNISCheck { get; set; }
        public string DNIS { get; set; }
        public string UserPic { get; set; }
        public bool HasShiftRest { get; set; }
        public bool IsADUser { get; set; }
        public string AccessRight { get; set; }// For Simple User Rights : Arivu
        public bool IsEnterpriseUser { get; set; }
        public bool IsGroupBased { get; set; }
             
        public int EnableDisableInquireUser { get; set; } // For Inquire Custom Marker Filtering :: KM
        public bool IsAvrisView { get; set; }
        public bool IsIQ3View { get; set; }
        public bool IsRevCell { get; set; }
        public int RevSyncServerUserNum { get; set; }
        public bool IsSyncedFromClient { get; set; }
        public int IsDeviceUser { get; set; } // For Inquire Custom Marker Filtering
        public string UMUserType { get; set; } // For Inquire Custom Marker Filtering
        public bool IsTagRule { get; set; } // T1 Extension filtering - Author : Arivu
        public string TagRule { get; set; } // T1 Extension filtering - Author : Arivu
        public string MinT1Ch { get; set; } // T1 Extension filtering - Author : Arivu
        public string MaxT1Ch { get; set; } // T1 Extension filtering - Author : Arivu
        //public int QBUserId { get; set; }
        //public bool ExistsOnQB { get; set; }
        //public bool IsEventSpecific { get; set; }
        public bool CanInvite { get; set; }

        public bool IsTempLogin { get; set; }
        public string TempUserPW { get; set; }
        public int RoleId { get; set; }
        public string RoleName { get; set; }
        public bool Is2FAEnabled { get; set; }
        public bool IsIwbUser { get; set; }
        public int OrganizationId { get; set; }
        public int CreatedBy { get; set; }
        public DateTime? DOB { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string SocialSecurityNumber { get; set; }
        public string WelderStencilNumber { get; set; }
        public string CustomerIdZoho { get; set; }


        #region Association
        public IwbRole IwbRole { get; set; }

        public Role Role { get; set; }
        public List<RolePermission> RolePermissions { get; set; }
        public UserGroup UserGroup { get; set; }
        public List<Permission> Permissions { get; set; }
        public string PermissionStr { get; set; }
        public List<Permission> EnterprisePermissions { get; set; }
        public GlobalGroup GlobalGroup { get; set; }
        public string SimpleAccessRight { get; set; }
        public string OriginalSimpleAccessRight { get; set; }
        public List<string> AssignedNodes { get; set; }
        public int NoOfEvaluations { get; set; }

        public int RecId { get; set; }
        public string RecName { get; set; }
        public List<UnAssignedUsers> SelectedUsers { get; set; } //For Inquire Group Management : Arivu
        public List<CustomMarkersData> cmDataUser { get; set; } //For Inquire Group Management : Arivu
        public List<AssignedUsers> SelectedUsersForSUR { get; set; } // For Simple User Rights : Arivu
        public List<AssignedUsers> DeSelectedUsers { get; set; }// For Simple User Rights : Arivu
        public List<User> simpleuserrightsforuser { get; set; }// For Simple User Rights : Arivu
        public List<RecorderAccessRight> RecorderAccessRights { get; set; }
        #endregion
        #region MonitorPlayer
        public bool IsContinuousPlay { get; set; }
        public bool IsMultiChannel { get; set; }
        #endregion
        public int isuser { get; set; }// For Simple User Rights : Arivu

        public DateTime? LastPasswordChanged { get; set; }
        public bool IsLockedOut { get; set; }
        public bool IsCompactView { get; set; }
        public bool IsAgreedToLicense { get; set; }
        public bool AutoUpload { get; set; }
        public bool DisableCustomAssetId { get; set; }

        public object Clone()
        {
            return ObjectUtil.DeepClone(this);
        }

    }
}
