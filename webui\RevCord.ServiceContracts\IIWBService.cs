﻿using RevCord.DataContracts.IWBEntities;
using RevCord.DataContracts.Messages;

namespace RevCord.ServiceContracts
{
    public interface IIwbService
    {
        #region ------- WPS -------

        IwbResponse SaveWPS(IwbRequest request);
        IwbResponse GetAllWPS(IwbRequest request);
        Wps GetWpsById(int wpsId, int tenantId);

        #endregion

        #region ------- Job -------
        IwbResponse SaveJob(IwbRequest request);
        IwbResponse AssignJob(IwbRequest request);
        IwbResponse GetJobsByWhereClause(IwbRequest request);


        #endregion

        #region Welders

        IwbResponse GetWeldersByWhereClause(IwbRequest request);

        #endregion

        #region ------- Work History -------

        IwbResponse SaveWorkHistory(IwbRequest request);
        IwbResponse GetWorkHistory(IwbRequest request);
        IwbResponse UpdateJobStatus(IwbRequest request);
        IwbResponse ApplyForJob(IwbRequest request);
        IwbResponse GetJobApplicants(IwbRequest request);
        #endregion

        #region ------- Organization -------

        IwbResponse SaveOrganization(IwbRequest request);
        IwbResponse GetAllOrganizations(IwbRequest request);
        IwbResponse SaveOrganizationLocation(IwbRequest request);
        IwbResponse GetLocationsByOrganizationId(int organizationId, int tenantId);
        IwbResponse DeleteLocation(IwbRequest request);

        #endregion


        #region ------- WPQ -------

        IwbResponse SaveWPQ(IwbRequest request);
        IwbResponse GetWPQsByUserId(IwbRequest request);

        #endregion


        #region ------- Document -------

        IwbResponse SaveScannedDocument(IwbRequest request);
        IwbResponse SaveDocument(IwbRequest request);
        IwbResponse GetDocumentsByWhereClause(IwbRequest request);

        #endregion

        #region Test Schedule

        IwbResponse GetTestingTypes(IwbRequest request);
        IwbResponse SaveTest(IwbRequest request);
        IwbResponse GetTestsByWhereClause(IwbRequest request);
        IwbResponse RegisterWelderForTest(IwbRequest request);
        IwbResponse GetTestAttendees(IwbRequest request);
        IwbResponse SaveTestInvitation(IwbRequest request);
        #endregion

        #region Users, Roles & Permissions

        IwbResponse RegisterUser(IwbRequest request);

        IwbResponse GetPermissionsByRole(IwbRequest request);

        IwbResponse SetCustomerIdForUser(IwbRequest request);

        #endregion

        #region Welder Dashboard

        IwbResponse GetWelderDashboardData(IwbRequest request);

        #endregion


    }
}