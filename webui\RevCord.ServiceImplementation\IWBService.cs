﻿using RevCord.BusinessLogic;
using RevCord.DataContracts.IWBEntities;
using RevCord.DataContracts.Messages;
using RevCord.ServiceContracts;
using System.Collections.Generic;

namespace RevCord.ServiceImplementation
{
    public class IwbService : IIwbService
    {

        #region ------- WPS -------

        public IwbResponse SaveWPS(IwbRequest request)
        {
            return new IwbManager().SaveWps(request);
        }

        public IwbResponse GetAllWPS(IwbRequest request)
        {
            return new IwbManager().GetWpsList(request);
        }

        public Wps GetWpsById(int wpsId, int tenantId)
        {
            return new IwbManager().GetWpsById(wpsId, tenantId);
        }




        #endregion


        #region ------- Job -------

        public IwbResponse SaveJob(IwbRequest request)
        {
            return new IwbManager().SaveJob(request);
        }
        public IwbResponse AssignJob(IwbRequest request)
        {
            return new IwbManager().AssignJob(request);
        }

        public IwbResponse GetJobsByWhereClause(IwbRequest request)
        {
            return new IwbManager().GetJobsByWhereClause(request);
        }

        public IwbResponse UpdateJobStatus(IwbRequest request)
        {
            return new IwbManager().UpdateJobStatus(request);
        }


        public IwbResponse UpdateJobApplicantStatus(IwbRequest request)
        {
            return new IwbManager().UpdateJobApplicantStatus(request);
        }

        public IwbResponse ApplyForJob(IwbRequest request)
        {
            return new IwbManager().ApplyForJob(request);
        }

        public IwbResponse GetJobApplicants(IwbRequest request)
        {
            return new IwbManager().GetJobApplicants(request);
        }
        #endregion

        #region Welders

        public IwbResponse GetWeldersByWhereClause(IwbRequest request)
        {
            return new IwbManager().GetWelders(request);
        }

        #endregion

        #region Work History

        public IwbResponse SaveWorkHistory(IwbRequest request)
        {
            return new IwbManager().SaveUserWorkHistory(request);
        }

        public IwbResponse GetWorkHistory(IwbRequest request)
        {
            return new IwbManager().GetWorkHistory(request);
        }


        #endregion


        #region ------- Organization -------

        public IwbResponse SaveOrganization(IwbRequest request)
        {
            return new IwbManager().SaveOrganization(request);
        }
        public IwbResponse GetAllOrganizations(IwbRequest request)
        {
            return new IwbManager().GetOrganizations(request);
        }


        public IwbResponse SaveOrganizationLocation(IwbRequest request)
        {
            return new IwbManager().SaveOrganizationLocation(request);
        }

        public IwbResponse GetLocationsByOrganizationId(int organizationId, int tenantId)
        {
            return new IwbManager().GetLocationsByOrganizationId(organizationId, tenantId);
        }

        public IwbResponse DeleteLocation(IwbRequest request)
        {
            return new IwbManager().DeleteLocation(request);
        }



        #endregion

        public IwbResponse GetJobsByWelder(IwbRequest request)
        {
            return new IwbManager().GetJobsByWelder(request);
        }

        public IwbResponse GetUserDataByUser(IwbRequest request)
        {
            return new IwbManager().GetUserDataByUser(request);
        }

        public IwbResponse UploadWPQ(IwbRequest request)
        {
            return new IwbManager().UploadWPQ(request);
        }

        #region ------- WPQ -------

        public IwbResponse SaveWPQ(IwbRequest request)
        {
            return new IwbManager().SaveWPQ(request);
        }

        public IwbResponse GetWPQsByUserId(IwbRequest request)
        {
            return new IwbManager().GetWPQsByUserId(request);
        }




        #endregion


        #region ------- Document -------

        public IwbResponse SaveScannedDocument(IwbRequest request)
        {
            return new IwbManager().SaveScannedDocument(request);
        }

        public IwbResponse SaveDocument(IwbRequest request)
        {
            return new IwbManager().SaveDocument(request);
        }

        public IwbResponse GetDocumentById(IwbRequest request)
        {
            return new IwbManager().GetDocumentById(request);
        }

        public IwbResponse VerifyDocument(IwbRequest request)
        {
            return new IwbManager().VerifyDocument(request);
        }

        public IwbResponse GetDocumentsByWhereClause(IwbRequest request)
        {
            return new IwbManager().GetDocumentsByWhereClause(request);
        }


        #endregion

        #region Test Schedule

        public IwbResponse GetTestingTypes(IwbRequest request)
        {
            return new IwbManager().GetTestTypes(request);
        }

        public IwbResponse SaveTest(IwbRequest request)
        {
            return new IwbManager().SaveTest(request);
        }

        public IwbResponse GetTestsByWhereClause(IwbRequest request)
        {
            return new IwbManager().GetTestsByWhereClause(request);
        }

        public IwbResponse RegisterWelderForTest(IwbRequest request)
        {
            return new IwbManager().RegisterWelderForTest(request);
        }
        public IwbResponse GetTestAttendees(IwbRequest request)
        {
            return new IwbManager().GetTestAttendees(request);
        }
        public IwbResponse GetSignOffRequestUsers(IwbRequest request)
        {
            return new IwbManager().GetSignOffRequestUsers(request);
        }

        public IwbResponse UpdateTestStatus(IwbRequest request)
        {
            return new IwbManager().UpdateTestStatus(request);
        }

        public IwbResponse UpdateWelderTestStatus(IwbRequest request)
        {
            return new IwbManager().UpdateWelderTestStatus(request);
        }

        public IwbResponse SaveTestInvitation(IwbRequest request)
        {
            return new IwbManager().SaveTestInvitation(request);
        }
        #endregion

        #region Welder Dashboard

        public IwbResponse GetWelderDashboardData(IwbRequest request)
        {
            return new IwbManager().GetWelderDashboardData(request);
        }

        #endregion

        #region MYPQ Related methods
        public IwbResponse GetMyWPQList(IwbRequest request)
        {
            return new IwbManager().GetMyWPQList(request);
        }

        public IwbResponse GetWPQDetail(IwbRequest request)
        {
            return new IwbManager().GetWPQDetail(request);
        }
        #endregion

        #region Reporting related methods
        public IwbResponse GetReportData(IwbRequest request)
        {
            return new IwbManager().GetReportData(request);
        }

        #endregion

        #region Users, Roles & Permissions

        public IwbResponse RegisterUser(IwbRequest request)
        {
            return new IwbManager().RegisterUser(request);
        }

        public IwbResponse GetPermissionsByRole(IwbRequest request)
        {
            var permissions = new List<IwbPermission>
            {
                new IwbPermission { Name = "Manage Documents", CssClassName = "lm-doc" },
                new IwbPermission { Name = "Search", CssClassName = "filter-row" },
                new IwbPermission { Name = "Add New", CssClassName = "add-doc" },
                new IwbPermission { Name = "All Listing", CssClassName = "" },
                new IwbPermission { Name = "Sign", CssClassName = "" },
                new IwbPermission { Name = "View Output", CssClassName = "view-doc" },
                new IwbPermission { Name = "View Signed", CssClassName = "view-signed-doc" },
                new IwbPermission { Name = "View Input", CssClassName = "" },
            };

            return new IwbResponse { Permissions = permissions };
        }

        public IwbResponse SetCustomerIdForUser(IwbRequest request)
        {
            return new IwbManager().SetCustomerIdForUser(request);
        }

        #endregion

        #region Worker history related methods
        public IwbResponse GetContractorWorkHistory(IwbRequest request)
        {
            return new IwbManager().GetContractorWorkHistory(request);
        }

        public IwbResponse GetWelderWorkHistory(IwbRequest request)
        {
            return new IwbManager().GetWelderWorkHistory(request);
        }

        public IwbResponse GetWelderInfo(IwbRequest request)
        {
            return new IwbManager().GetWelderInfo(request);
        }
        #endregion

        public IwbResponse SaveAIResponse(IwbRequest request)
        {
            return new IwbManager().SaveAIResponse(request);
        }

        public IwbResponse SaveWelderRatings(IwbRequest request)
        {
            return new IwbManager().SaveWelderRatings(request);
        }

        public IwbResponse GetWelderRatingsById(IwbRequest request)
        {
            return new IwbManager().GetWelderRatingsById(request);
        }

        public IwbResponse GetJobTitles(IwbRequest request)
        {
            return new IwbManager().GetJobTitles(request);
        }

        public IwbResponse GetJobDetailsById(IwbRequest request)
        {
                return new IwbManager().GetJobDetailsById(request);
        }

        public IwbResponse CreateZohoInvoice(IwbRequest request)
        {
            return new IwbManager().CreateZohoInvoice(request);
        }

        public IwbResponse GetZohoTransactionHistory(IwbRequest request)
        {
            return new IwbManager().GetZohoTransactionHistory(request);
        }
    }
}