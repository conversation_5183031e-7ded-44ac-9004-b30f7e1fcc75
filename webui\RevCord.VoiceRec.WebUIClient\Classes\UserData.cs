﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.DataContracts;
using RevCord.DataContracts.RoleManagement;
using RevCord.DataContracts.IWBEntities;

namespace RevCord.VoiceRec.WebUIClient.Classes
{
    public class UserData
    {
        private static readonly int _passwordExpiryInDays = RevCord.Util.AppSettingsUtil.GetInt("passwordExpiryInDays", 90);


        #region Properties

        public int UserNum { get; set; }
        public int UserNum_Shared { get; set; } //For Shared Pages
        public int TenantId { get; set; }
        public string TenantName { get; set; }
        public int TenantId_Shared { get; set; } //For Shared Pages
        public string UserName { get; set; }
        public int UserType { get; set; }
        public int Status { get; set; }
        public string AssignAuth { get; set; }
        public string SimpleAccessRight { get; set; }
        public int SearchRest { get; set; }
        public int SelectType { get; set; }
        public int ViewID { get; set; }
        public string ProfilePicture { get; set; }

        public int GroupNum { get; set; }
        public string GroupName { get; set; }
        public string UserID { get; set; }
        public string UserPW { get; set; }
        public string Ext { get; set; }
        public string UserEmail { get; set; }
        public string UserPhone { get; set; }
        public int POD { get; set; }
        public int EOD { get; set; }
        public int Pause { get; set; }
        public string ExtName { get; set; }
        public int DNISCheck { get; set; }
        public string DNIS { get; set; }
        public string IsDeviceUser { get; set; } // For Plugin User Type
        public bool IsIQ3Enabled { get; set; }
        public bool IsRevcellEnabled { get; set; }
        public bool IsAvrisViewEnabled { get; set; }
        public bool IsIQ3ViewEnabled { get; set; }
        public bool IsMDEnabled { get; set; }
        public int EnableUser { get; set; }
        public bool HasShiftRest { get; set; }            //HasShiftRest Added by Sarfraz
        public bool IsEnterpriseUser { get; set; }
        public bool IsIwbUser { get; set; }
        public int OrganizationId { get; set; }
        public IwbRole IwbUserRole { get; set; }
        public string CustomerId { get; set; }


        //public int QBUserId { get; set; }
        //public bool ExistsOnQB { get; set; }
        //public bool IsEventSpecific { get; set; }
        public bool CanInvite { get; set; }

        public bool IsTagRuleUser { get; set; }    // arivu - T1 Extension Filtering
        public string TagRuleUser { get; set; }    // Arivu - T1 Extenson Filtering

        public string MinT1ChData { get; set; }
        public string MaxT1ChData { get; set; }
        public bool AccessSetup { get; set; }
        public bool AccessSettings
        {
            get
            {
                //if (TypeOfUser == DataContracts.UserType.AdminSuper || TypeOfUser == DataContracts.UserType.AdminNormal)
                //    return true;

                //return false;
                return true;
            }
        }
        public bool AccessSchedule { get; set; }
        public bool AccessMonitor { get; set; }
        public bool AccessSearch { get; set; }
        public bool AccessEvaluation { get; set; }
        public bool AccessInstantRecall { get; set; }
        public bool AccessIRLite { get; set; }          //AccessIRLite Added by Sarfraz
        public bool AccessDashboard { get; set; }
        public bool AccessScore { get; set; }
        //public bool AccessEvalReports { get; set; }
        public bool AccessTotScore { get; set; }
        public bool AccessRMS { get; set; }
        public bool AccessSaveFile { get; set; }
        public bool AccessReport { get; set; }
        public bool AccessInvitation { get; set; }
        //public bool AccessRoleManagement { get; set; }

        public bool Login { get; set; }

        public bool IsRouteFromDefault { get; set; }

        public List<string> AssignedNodes { get; set; }
        public bool IsCompactView { get; set; }

        public static bool IsLogin
        {
            get { return SessionHandler.UserInformation != null; }
        }

        public bool Is2FAEnabled { get; set; }
        public bool IsTempLogin { get; set; }
        public int RoleId { get; set; }
        public Role Role { get; set; }
        public List<RolePermission> RolePermissions { get; set; }
        public List<ExtensionCallInfo> ExtensionCallInfos { get; set; }
        public List<RecorderAccessRight> RecorderAccessRights { get; set; }
        public UserType TypeOfUser
        {
            get
            {
                if (this.UserType == 1 && this.UserNum == 1000 && this.GroupNum == 1000)
                    return DataContracts.UserType.AdminSuper;
                if (this.UserType == 1 && this.UserNum != 1000 && this.GroupNum == 1000)
                    return DataContracts.UserType.AdminNormal;
                else if (this.UserType == 1 && this.GroupNum != 1000)
                    return DataContracts.UserType.AdminNormal;
                else if (this.UserType == 0 && this.SelectType == 0)
                    return DataContracts.UserType.Additional;
                else if (this.UserType == 0 && this.SelectType == 1)
                    return DataContracts.UserType.Simple;

                return DataContracts.UserType.None;
            }
        }

        public List<string> Last5Passwords { get; }

        public DateTime? LastPasswordChange { get; set; }

        public int DaysToExpirePassword
        {
            get
            {
                if (LastPasswordChange.HasValue)
                    return _passwordExpiryInDays - (DateTime.Now - LastPasswordChange.Value).Days;

                //return 0;
                return _passwordExpiryInDays;
            }
        }

        public bool IsViewPlaylist { get; set; }
        public int PlaylistIds { get; set; }


        //public DateTime PasswordExpiryDate
        //{
        //    get
        //    {
        //        if (LastPasswordChange.HasValue)
        //            return DateTime.Today - LastPasswordChange.Value;
        //        else
        //            return LastPasswordChange.Value + 90;
        //    }
        //}

        #endregion

        public static void Logout()
        {
            //SessionHandler.UserInformation = null;
            HttpContext.Current.Session.Remove("UserInfo");
        }
    }
}