﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace RevCord.VoiceRec.WebUIClient.Classes
{
    public class ZohoTokenResponse
    {
        [JsonProperty("access_token")]
        public string AccessToken { get; set; }

        [JsonProperty("refresh_token")]
        public string RefreshToken { get; set; }

        [JsonProperty("scope")]
        public string Scope { get; set; }

        [JsonProperty("api_domain")]
        public string ApiDomain { get; set; }

        [JsonProperty("token_type")]
        public string TokenType { get; set; }

        [JsonProperty("expires_in")]
        public int ExpiresIn { get; set; }
    }

    public class ZohoCustomerResponse
    {
        public string Code { get; set; }
        public string Message { get; set; }
        public ZohoCustomer Customer { get; set; }

    }

    public class ZohoCustomer
    {
        [JsonProperty("customer_id")]
        public string CustomerId { get; set; }
        [<PERSON>sonProperty("display_name")]
        public string DisplayName { get; set; }
        [JsonProperty("company_name")]
        public string CompanyName { get; set; }
        [JsonProperty("salutation")]
        public string Salutation { get; set; }
        [JsonProperty("first_name")]
        public string FirstName { get; set; }
        [JsonProperty("last_name")]
        public string LastName { get; set; }
        [JsonProperty("email")]
        public string Email { get; set; }
        [JsonProperty("can_add_card")]
        public bool CanAddCard { get; set; } = true;
        [JsonProperty("can_add_bank_account")]
        public bool CanAddBankAccount { get; set; } = true;
        [JsonProperty("phone")]
        public string Phone { get; set; }
        [JsonProperty("mobile")]
        public string Mobile { get; set; }
        [JsonProperty("notes")]
        public string Notes { get; set; }
        [JsonProperty("zcrm_account_id")]
        public string CrmAccountId { get; set; }
        [JsonProperty("zcrm_contact_id")]
        public string CrmContactId { get; set; }
        [JsonProperty("status")]
        public string Status { get; set; }
    }

}