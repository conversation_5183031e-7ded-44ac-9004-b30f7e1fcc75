﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using RevCord.VoiceRec.WebUIClient.Classes;
using RevCord.DataContracts.Response;
using RevCord.DataContracts.Request;
using RevCord.DataContracts.UserManagement;
using RevCord.ServiceImplementation;
using System.Collections;
using RevCord.VoiceRec.WebUIClient.Classes.Common;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.Util;
using System.Net.Mail;
using System.IO;
using System.DirectoryServices.AccountManagement;
using System.Threading.Tasks;
using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using RevCord.VoiceRec.WebUIClient.Classes.Util;
using RevCord.DataContracts.ReportEntities;
using RevCord.DataContracts.TenantEntities;
using RevLicenseService.SoloServer.Entities;

namespace RevCord.VoiceRec.WebUIClient
{
    public partial class Default : System.Web.UI.Page
    {
        #region Fields
        string sysSerialNo = string.Empty;
        UserManagementResponse _umResponse = null;
        UserManagementRequest _umRequest = null;
        User _loginUser = null;
        List<Recorder> _recorders = null;
        public bool _isMTEnable = RevCord.Util.SiteConfig.IsMTEnable;
        public bool _IsActiveDirectory = RevCord.Util.SiteConfig.IsActiveDirectory;
        bool _userExistsInMaster = false;
        int _tenantId = 0;
        string _softwareVersion = string.Empty;
        string _tenantName = string.Empty;
        string _tenantConnString = "";
        //string _tenantName = string.Empty;
        private static EncryptionUtil _encryption = new EncryptionUtil();
        private static readonly int _invalidLoginAttempts = AppSettingsUtil.GetInt("passwordLockoutInvalidAttempts", 5);
        private static readonly int _passwordExpiryInDays = RevCord.Util.AppSettingsUtil.GetInt("passwordExpiryInDays", 90);
        private static readonly bool _forcePasswordChange = RevCord.Util.AppSettingsUtil.GetBool("passwordForceChange", true);
        private static readonly bool _enableLockout = RevCord.Util.AppSettingsUtil.GetBool("passwordEnableLockout", true);
        private int _remainingAttempts = AppSettingsUtil.GetInt("passwordLockoutInvalidAttempts", 5);
        private string _previousUser = "";
        private EncryptDecryptQueryString encryptDecrypt = new EncryptDecryptQueryString();
        private static readonly string _encryptDecryptKey = "r0b1nr0y";

        private static string RevShieldStatus = "";
        private static OnsiteContactInfo objContactInfo = null;
        #endregion

        //[DllImport(@"C:\Disstech\bin\SoftkeyLic.dll", EntryPoint = @"?InitializeWebUI@CSoftkeyLic@@QAEHPAD00PAJ01@Z")]
        //static extern int InitializeWebUI(string strIniFile, string strFilePath, string strPasswrod, ref int error, StringBuilder output,ref int licHandle);

        //[DllImport(@"C:\Disstech\bin\SoftkeyLic.dll", EntryPoint = @"?CheckStatusWebUI@CSoftkeyLic@@QAEHPAJPAD0@Z")]
        //static extern int CheckStatusWebUI(ref int length, StringBuilder myString, ref int licHandle);

        ////[DllImport(@"C:\Disstech\bin\SoftkeyLic.dll", EntryPoint = @"?GetPaymentExpiration@CSoftkeyLic@@QAEHPA_NPAJ111PAD@Z")]
        ////static extern int GetPaymentExpiration(ref int isExpired, ref int month, ref int day, ref int year, ref int errorCode, StringBuilder errorDesc);
        //////BOOL CSoftkeyLic::GetPaymentExpiration(bool* bExpired, long* lMonth, long* lDay, long* lYear, long* lError, char* strErrorDesc)
        ////private bool getPaymentExpiration()
        ////{
        ////    int isExpired = 0;
        ////    int errorCode = 0;
        ////    StringBuilder errorDesc = new StringBuilder(200);
        ////    int expMonth = 0, expDay = 0, expYear = 0;
        ////    bool statusResult = Convert.ToBoolean(GetPaymentExpiration(ref isExpired, ref expMonth, ref expDay, ref expYear, ref errorCode, errorDesc));
        ////    this.lblLicenseError.InnerHtml = string.Format("{0}-{1}-{2}-{3}", "CheckStatusWebUI", errorCode, errorDesc.ToString());
        ////    return statusResult;
        ////}


        //private bool checkAppLicense()
        //{
        //    int errorCode = 0;
        //    StringBuilder errorDesc = new StringBuilder(100);
        //    int getLicHandle = 0;
        //    bool isInit = Convert.ToBoolean(InitializeWebUI("revcord.lf", "C:\\Disstech\\bin", "{70762dd3-8131-4a73-aab4-18db31643642}", ref errorCode, errorDesc, ref getLicHandle));

        //    this.lblLicenseError.InnerHtml = strin
        //    if (isInit)
        //    {g.Format("{0}-{1}-{2}-{3}", "InitializeWebUI", getLicHandle, errorCode, errorDesc.ToString());

        //        int errorCodeStatus = 0;
        //        StringBuilder errorDescStatus = new StringBuilder(200);
        //        bool statusResult = Convert.ToBoolean(CheckStatusWebUI(ref errorCodeStatus, errorDescStatus, ref getLicHandle));

        //        this.lblLicenseError.InnerHtml = string.Format("{0}-{1}-{2}-{3}", "CheckStatusWebUI", getLicHandle, errorCodeStatus, errorDescStatus.ToString());

        //        return statusResult;
        //    }
        //    return isInit;
        //    //return true;
        //}

        //protected void Page_Load(object sender, EventArgs e)
        //{
        //    try
        //    {
        //        ////#if (DEBUG == false)
        //        if (!checkAppLicense())
        //        {
        //            this.btnLogin.Visible = false;
        //            this.divLisenceMessage.Visible = false;
        //            this.lblLicenceInfo.Text = "Your License Expired. Contact Support";
        //            return;
        //        }
        //        ////#endif

        //        //this.lblLicenceInfo.Text = new CommonService().GetLicenceInformation();
        //        int noOfDomainUsers = 0;
        //        this.lblLicenceInfo.Text = new CommonService().GetLicenceInfoAndAdInfo(out noOfDomainUsers);
        //        ClientScript.RegisterClientScriptBlock(this.GetType(), "ActiveDirectory", string.Format("var noOfDomainUsers = {0}; var isAdConfigured = {1}", noOfDomainUsers, noOfDomainUsers > 0 ? true.ToString().ToLower() : false.ToString().ToLower()), true);
        //        //Response.Write(this.Request.Url.AbsolutePath.ToString());
        //        if (!Page.IsPostBack)
        //        {
        //            this.userID.Focus();
        //            Session.Abandon();
        //            Session.Clear();

        //            if (!UserData.IsLogin && (UserData)Cache["TimeOutUserObject"] != null)
        //            {
        //                String message = "Your Current Session has Expired.Please Re-login.";
        //                FailureText.Text = message;
        //            }
        //        }

        //        if (this.IsPostBack && Request.Form.Count != 0)
        //            this.GetUser(Request.Form["userID"], Request.Form["userPW"]);
        //    }
        //    catch (Exception ex) { throw ex; }
        //}


        private bool checkLicenseStatus()
        {
            try
            {
                RevLicenseService.LicenseService licenseService = new RevLicenseService.LicenseService();
                ApplicationLog.StampLog(LogCategory.Information, "License Refresh Start " + DateTime.Now.ToString());
                licenseService.RefreshLicenseFile();
                licenseService.ReloadLicenseFile();
                ApplicationLog.StampLog(LogCategory.Information, "License Refresh End " + DateTime.Now.ToString());

                licenseService.RefreshLicenseFile();
                licenseService.ReloadLicenseFile();

                string licenseStatus = licenseService.GetLicenseStatus();
                //string revshieldStatus = licenseService.GetRevShieldLevel();
                //DateTime revshieldExpiry = licenseService.RenewalDate();
                string siteName = licenseService.GetSiteName();
                bool isLicenseValid = licenseService.IsLicenseValid();
                SiteConfig.RevSyncTenantID = licenseService.GetTenantID();
                SessionHandler.LicensedChannelCount = licenseService.GetChannelCount();
                SessionHandler.LicenseStatus = licenseStatus;
                //SessionHandler.RevshieldStatus = revshieldStatus.ToUpper();
                //SessionHandler.RevshieldExpiryDate = revshieldExpiry.ToString();
                SessionHandler.SiteName = siteName;
                SessionHandler.LicenseId = licenseService.GetLicenseId();
                this.lblLicenseStatus.InnerHtml = licenseStatus;

                //this.lblRevshieldStatus.InnerHtml = revshieldStatus.ToUpper();
                //this.lblRevshieldExpiryDate.InnerHtml = revshieldExpiry.ToString();
                //this.lblSiteName.InnerHtml = siteName;  // Removed in Version 9.4.1 As Instruced By German

                //if (!string.Equals(revshieldStatus, "active", StringComparison.OrdinalIgnoreCase)) //if (revshieldStatus.ToLowerCase() == "active")
                //{
                //    //spanRevshieldActive.Attributes.Add("class", "hide");

                //    spanRevshieldInActive.Attributes.Add("class", spanRevshieldInActive.Attributes["class"].Replace("hide", ""));
                //}
                //this.lblLicenseAPIResponse.InnerHtml = string.Format("{0}-{1}-{2}-{3}-{4}", licenseStatus, revshieldStatus, revshieldExpiry, siteName, isLicenseValid);
                this.lblLicenseAPIResponse.InnerHtml = string.Format("{0}-{1}-{2}", licenseStatus, siteName, isLicenseValid);

                return isLicenseValid;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                form1.Attributes["style"] = "background:#eaeaeb url('" + Page.ResolveClientUrl("~/assetsNew/images/Login-BG.png") + "'); background-repeat:no-repeat; background-size: cover;";
                int noOfDomainUsers = 0;
                //sysSerialNo = new CommonService().GetLicenceInfoAndAdInfo(out noOfDomainUsers, 0); //this.lblSerialNo.Text = new CommonService().GetLicenceInfoAndAdInfo(out noOfDomainUsers);
                if (!_isMTEnable)
                {
                    //for MT moved serial here
                    sysSerialNo = new CommonService().GetLicenceInfoAndAdInfo(out noOfDomainUsers, 0); //this.lblSerialNo.Text = new CommonService().GetLicenceInfoAndAdInfo(out noOfDomainUsers);
                    this.lblSerialNo.InnerHtml = sysSerialNo;
                    SessionHandler.SystemSerialNumber = sysSerialNo.Trim();
                    ClientScript.RegisterClientScriptBlock(this.GetType(), "ActiveDirectory", string.Format("var noOfDomainUsers = {0}; var isAdConfigured = {1}; var isMtEnabled = {2}", noOfDomainUsers, noOfDomainUsers > 0 ? true.ToString().ToLower() : false.ToString().ToLower(), _isMTEnable.ToString().ToLower()), true);
                    //Response.Write(this.Request.Url.AbsolutePath.ToString());
                }
                else
                    ClientScript.RegisterClientScriptBlock(this.GetType(), "ActiveDirectory", string.Format("var noOfDomainUsers = {0}; var isAdConfigured = {1}; var isMtEnabled = {2}", noOfDomainUsers, noOfDomainUsers > 0 ? true.ToString().ToLower() : false.ToString().ToLower(), _isMTEnable.ToString().ToLower()), true);

                try
                {
                    try
                    {
                        RevShieldStatus = "Unknown";
                        SessionHandler.RevshieldStatus = RevShieldStatus;
                        objContactInfo = new OnsiteContactInfo();
                        objContactInfo.LastConfirmedOn = new DateTime(2000, 01, 01);
                        SessionHandler.OnsiteContactInformation = objContactInfo;
                    }
                    catch (Exception ex)
                    {

                    }

                    if (AppSettingsUtil.GetBool("RevShieldCheckEnabled", true))
                    {
                        RegisterAsyncTask(new PageAsyncTask(FetchRevshieldStatusAsync, EndAsyncTask, null, null));
                        RegisterAsyncTask(new PageAsyncTask(FetchOnsiteContactInformatiomAsync, EndAsyncTask, null, null));
                    }
                }
                catch (Exception ex)
                {
                    objContactInfo = new OnsiteContactInfo();
                    objContactInfo.LastConfirmedOn = new DateTime(2000, 01, 01);

                    SessionHandler.OnsiteContactInformation = objContactInfo;
                }

                //if (checkLicenseStatus() == false)
                //{
                //    this.btnLogin.Visible = false;
                //    SetCaptchaText(); // added by km ifuturz - captcha
                //    return;
                //}


                RevLicenseService.LicenseService licenseService = new RevLicenseService.LicenseService();
                SessionHandler.LicenseId = licenseService.GetLicenseId();

                string strReq = "";
                strReq = this.Request.RawUrl;
                if (strReq.Contains('?'))
                    strReq = strReq.Substring(strReq.IndexOf('?') + 1);

                if (!strReq.Equals("") && this.Request.RawUrl.Split('?').Length > 1 && !strReq.Contains("redirect=") && !Page.IsPostBack)
                {

                    var queryStrings = DecryptQueryString(strReq);

                    string[] arrMsgs = queryStrings.Split('=');
                    string qstringEmail = "", qstringPassword = "", qstringVersionNo = "", qsTenantName = string.Empty;
                    int tenatId = 0;
                    int remainingAttempt = 0;

                    if (arrMsgs.Length >= 2)
                    {
                        qstringEmail = arrMsgs[0].ToString().Trim();
                        qstringPassword = arrMsgs[1].ToString().Trim();
                    }

                    if (arrMsgs.Length >= 3)
                        tenatId = Convert.ToInt32(arrMsgs[2].ToString().Trim());

                    if (arrMsgs.Length >= 4)
                        qstringVersionNo = arrMsgs[3].ToString().Trim();

                    if (arrMsgs.Length >= 5)
                    {
                        _remainingAttempts = Convert.ToInt32(arrMsgs[4].ToString().Trim());
                        hdnRemainingLoginAttempts.Value = arrMsgs[4].ToString().Trim();
                    }

                    if (arrMsgs.Length >= 6)
                        hdnPreviousUser.Value = arrMsgs[5].ToString();

                    if (arrMsgs.Length >= 7)
                        qsTenantName = arrMsgs[6].ToString();

                    _tenantId = tenatId;
                    _softwareVersion = qstringVersionNo;
                    _tenantName = qsTenantName;

                    if (!string.IsNullOrEmpty(qstringEmail) && (!string.IsNullOrEmpty(qstringPassword)))
                    {
                        this.GetUser(qstringEmail, qstringPassword, true);
                    }
                }
                else
                {
                    if (!Page.IsPostBack)
                    {
                        this.userID.Focus();
                        Session.Abandon();
                        Session.Clear();

                        SetCaptchaText(); // ADDED BY KM IFUTURZ - CAPTCHA

                        if (!UserData.IsLogin && (UserData)Cache["TimeOutUserObject"] != null)
                        {
                            string message = "Your Current Session has Expired.Please Re-login.";
                            this.FailureText.Text = message;
                        }
                    }

                    if (this.IsPostBack && Request.Form.Count != 0)
                    {
                        if (_enableLockout)
                        {
                            /*SessionHandler.LoginAttempts++;
                            if (SessionHandler.LoginAttempts == _invalidLoginAttempts)
                            {
                                bool userLocked = new UserManagementService().LockUnlockUserAccount(Request.Form["userID"], true, _tenantId);
                                if (userLocked)
                                {
                                    string message = "Your account has been locked due to multiple invalid login attempts. Use Recovery link below";
                                    this.FailureText.Text = message;
                                    return;
                                }
                            }*/

                            _previousUser = Convert.ToString(this.hdnPreviousUser.Value);

                            if (_previousUser == Request.Form["userID"])
                            {
                                _remainingAttempts = Convert.ToInt32(this.hdnRemainingLoginAttempts.Value);
                            }
                            else
                            {
                                _remainingAttempts = AppSettingsUtil.GetInt("passwordLockoutInvalidAttempts", 5);
                                _previousUser = Request.Form["userID"];
                            }

                            _remainingAttempts--;

                            this.hdnRemainingLoginAttempts.Value = (_remainingAttempts).ToString();
                            this.hdnPreviousUser.Value = _previousUser;

                            //if (_remainingAttempts < 0)
                            //{
                            //    bool userLocked = new UserManagementService().LockUnlockUserAccount(Request.Form["userID"], true, _tenantId);
                            //    if (userLocked)
                            //    {
                            //        string message = "Your account has been locked due to multiple invalid login attempts. Use Recovery link below";
                            //        this.FailureText.Text = message;
                            //        return;
                            //    }
                            //}
                        }
                        if (SessionHandler.LoginAttempts > _invalidLoginAttempts && AppSettingsHelper.GetValueAsBool("isCaptchaEnabled", true))
                        {
                            string captchaInput = Request.Form["txtCaptchaText"];

                            if (string.IsNullOrEmpty(captchaInput))
                            {
                                string message = "Please enter captcha.";
                                this.FailureText.Text = message;
                            }
                            else if (captchaInput != SessionHandler.CAPTCHA)
                            {
                                string message = "Please enter valid captcha.";
                                this.FailureText.Text = message;
                            }
                            else
                                this.GetUser(Request.Form["userID"], Request.Form["userPW"]);

                            SetCaptchaText();
                        }
                        else
                            this.GetUser(Request.Form["userID"], Request.Form["userPW"]);
                    }
                }
                // MODIFIED BY KM IFUTURZ - CAPTCHA - END
            }
            catch (Exception ex) { throw ex; }
        }

        private IAsyncResult FetchRevshieldStatusAsync(object sender, EventArgs e, AsyncCallback cb, object state)
        {
            Action action = () =>
            {
                FetchRevshieldStatusAsync();
            };

            IAsyncResult asyncResult = action.BeginInvoke(cb, state);

            return asyncResult;

            //return FetchRevshieldStatusAsync();
        }

        private void FetchRevshieldStatusAsync()
        {
            RevLicenseService.LicenseService licenseService = new RevLicenseService.LicenseService();
            var result = licenseService.GetRevShieldStatus(sysSerialNo).Result;

            if (result == true)
                RevShieldStatus = "Active";
            else
                RevShieldStatus = "Not Active";
        }

        private IAsyncResult FetchOnsiteContactInformatiomAsync(object sender, EventArgs e, AsyncCallback cb, object state)
        {
            Action action = () =>
            {
                FetchOnsiteContactInformatiomAsync();
            };

            IAsyncResult asyncResult = action.BeginInvoke(cb, state);

            return asyncResult;

            //return FetchOnsiteContactInformatiomAsync();
        }

        private void FetchOnsiteContactInformatiomAsync()
        {
            if (!_isMTEnable)
            {
                RevLicenseService.Classes.OnsiteContactInformation result = null;

                try
                {
                    RevLicenseService.LicenseService licenseService = new RevLicenseService.LicenseService();
                    result = licenseService.GetOnsiteContactInformation(sysSerialNo).Result;
                }
                catch (Exception ex)
                {

                }

                var response = new VoiceRecService().GetOnsiteContactInfoConfirmation(0);
                objContactInfo = response.OnsiteContactInfo;

                if (objContactInfo == null)
                {
                    objContactInfo = new OnsiteContactInfo();
                }

                if (result != null)
                {
                    objContactInfo.OnsiteContactName = result.ContactName;
                    objContactInfo.OnsiteContactEmail = result.ContactEmail;
                    objContactInfo.OnsiteContactNumber = result.ContactNumber;
                    objContactInfo.Product_Name = result.Product_Name;
                }

            }
            else
            {
                if (objContactInfo == null)
                {
                    objContactInfo = new OnsiteContactInfo();
                    objContactInfo.LastConfirmedOn = new DateTime(2000, 01, 01);
                }
            }
        }

        private void EndAsyncTask(IAsyncResult asyncResult)
        {
            if (!string.Equals(RevShieldStatus, "active", StringComparison.OrdinalIgnoreCase))
            {
                spanRevshieldInActive.Attributes.Add("class", spanRevshieldInActive.Attributes["class"].Replace("hide", ""));
            }
            this.lblRevshieldStatus.InnerHtml = RevShieldStatus.ToUpper();

            SessionHandler.RevshieldStatus = RevShieldStatus;
            SessionHandler.OnsiteContactInformation = objContactInfo;
        }

        #region ADDED BY KM IFUTURZ - CAPTCHA
        private void SetCaptchaText()
        {
            Random oRandom = new Random();
            int iNumber = oRandom.Next(100000, 999999);
            //Session["Captcha"] = iNumber.ToString();
            SessionHandler.CAPTCHA = iNumber.ToString();
        }
        #endregion ADDED BY KM IFUTURZ - CAPTCHA

        public bool IsValidemail(string emailaddress)
        {
            try
            {
                MailAddress m = new MailAddress(emailaddress);

                return true;
            }
            catch (FormatException)
            {
                return false;
            }
        }

        protected void GetUser(string uId, string pwd, bool bEnc = false)
        {
            if (_isMTEnable)
            {
                string email = Request.Form["userID"];

                if (!bEnc)
                {
                    _tenantId = Convert.ToInt32(Request.Form["hdnTenantId"]);
                    _softwareVersion = Convert.ToString(Request.Form["hdnSoftwareVersion"]);
                    _tenantName = Convert.ToString(Request.Form["hdnTenantName"]);
                }


                CreateTenantIconDirectory(_tenantId);
                _tenantConnString = new TenantService().GetDatabaseConnection(_tenantId);
                //_tenantConnString = @"Data Source=mt.revcord.com\Revcord,1533;Initial Catalog=6247-VoiceRec;User ID=sa;Password=******;Persist Security Info=True;";
                if (string.IsNullOrEmpty(_tenantConnString))
                {
                    FailureText.Text = "Tenant Database does not exists";
                    this.hdnTenantId.Value = "0";
                    this.hdnSoftwareVersion.Value = string.Empty;
                    this.hdnTenantName.Value = string.Empty;
                    //ActivityLogger.LogAsync(0, 3, string.Format("Login failed for {0}. Invalid User Name or Password", _userName), "AL_LoginFailInValidCredentials", _userName, "", IPHelper.GetVisitorIPAddress(HttpContext.Current), _tenantId);//LogAsync();
                    return;
                }
                // Deal here with the Tenant Information and fetch related data. 
                //int noOfAdUser = 0;
                //var tenantResponse = new TenantService().GetTenantInformation(_tenantId, out noOfAdUser);
                //if (tenantResponse.TenantInformation != null) {
                //    this.lblSerialNo.InnerHtml = tenantResponse.TenantInformation.SerialKey;
                //    SessionHandler.SystemSerialNumber = tenantResponse.TenantInformation.SerialKey.Trim();
                //    SessionHandler.TenantInformation = tenantResponse.TenantInformation;
                //    var licenseId = tenantResponse.TenantInformation.LicenseId;
                //    var activationPassword = tenantResponse.TenantInformation.ActivationPassword;
                //    RevLicenseService.LicenseService licenseService = new RevLicenseService.LicenseService();
                //    LicenseInfoCheck licenseInfoCheck = licenseService.GetMTLicenseInfo(licenseId, activationPassword);
                //    var soloLicense = licenseService.ValidateAndGetMTLicenseStatus(licenseInfoCheck);
                //    string licenseStatus = soloLicense.Status;
                //    string siteName = licenseInfoCheck.UDefChar1;
                //    bool isLicenseValid = soloLicense.IsValid;
                //    SiteConfig.RevSyncTenantID = licenseInfoCheck.UDefNum4;
                //    SessionHandler.TenantInformation.MTChannelsPurchased = Convert.ToInt32(licenseInfoCheck.Quantity);
                //    SessionHandler.LicensedChannelCount = SessionHandler.TenantInformation.MTChannelsPurchased;
                //    SessionHandler.LicenseStatus = licenseStatus;
                //    SessionHandler.SiteName = siteName;
                //    SessionHandler.LicenseId = Convert.ToInt32(soloLicense.LicenseId);
                //    this.lblLicenseStatus.InnerHtml = licenseStatus;
                //    this.lblLicenseAPIResponse.InnerHtml = string.Format("{0}-{1}-{2}", licenseStatus, siteName, isLicenseValid);
                //    if(soloLicense.IsValid) {
                //        this.btnLogin.Visible = false;
                //        SetCaptchaText();
                //        return;
                //    }
                //}

            }

            bool _IsADUser = false;
            if (_IsActiveDirectory)
            {
                string DomainName = System.Net.NetworkInformation.IPGlobalProperties.GetIPGlobalProperties().DomainName;
                using (PrincipalContext pc = new PrincipalContext(ContextType.Domain, DomainName))
                {
                    if (pc.ValidateCredentials(uId, pwd))
                    {
                        uId = uId + "@" + DomainName;
                        _IsADUser = true;
                        //using (var searcher = new PrincipalSearcher(new UserPrincipal(pc, uId, pwd, true)))
                        //{
                        //    foreach (var result in searcher.FindAll())
                        //    {
                        //        DirectoryEntry de = result.GetUnderlyingObject() as DirectoryEntry;
                        //        string mailid = de.Properties["mail"].Value.ToString().Trim();
                        //        if (mailid != null && mailid.Length > 0 && IsValidemail(mailid))
                        //        {
                        //            //uId = mailid;
                        //            uId = uId + "@" + DomainName;
                        //            _IsADUser = true;
                        //        }
                        //    }
                        //}
                    }
                }
            }

            string _userName = uId;
            string _password = pwd;
            try
            {
                if (_userName != "" && _password != "")
                {

                    if (_isMTEnable && !bEnc)
                    {
                        var encCreds = "?" + EncryptQueryString(_userName + "=" + _password + "=" + _tenantId + "=" + this.hdnSoftwareVersion.Value + "=" + _remainingAttempts + "=" + this.hdnPreviousUser.Value + "=" + this.hdnTenantName.Value);
                        bool isValidAccount = new UserManagementService().AuthenticateUser(_userName, _password, _tenantId);
                        if (isValidAccount)
                        {
                            if (!string.IsNullOrEmpty(AppSettingsUtil.GetString("MTMMSRedirect" + this.hdnSoftwareVersion.Value, string.Empty)))
                            {
                                Response.Redirect(AppSettingsUtil.GetString("MTMMSRedirect" + this.hdnSoftwareVersion.Value, "~/Default.aspx") + encCreds, true);
                            }
                            else
                            {
                                string script = "<script type=\"text/javascript\"> $(document).ready(function() { showVersionErrorDialog(); }); </script>";
                                ClientScript.RegisterClientScriptBlock(this.GetType(), "VersionErrorScript", script);
                                //FailureText.Text = "Dear user, please note that to access RevCloud, upgrading your logger to the 12 series is necessary. For any issues or require assistance, please contact Revcord support. Thank you for your cooperation.";
                                return;
                            }
                        }
                        else
                        {
                            FailureText.Text = "Invalid Username / Password";
                            return;
                        }
                    }
                    else
                    {

                        this._umRequest = new UserManagementRequest();
                        this._umRequest.User = new User { UserID = _userName, UserPW = _password, IsADUser = _IsADUser };
                        this._umRequest.TenantId = _tenantId;
                        this._umRequest.ForcePasswordChange = _forcePasswordChange;
                        this._umRequest.IsRoleBasedAccessEnabled = RevCord.Util.SiteConfig.IsRoleBasedAccessEnabled;

                        this._umResponse = new UserManagementService().GetMyInformation(this._umRequest);
                        this._loginUser = this._umResponse.LoginUser;
                        this._recorders = this._umResponse.Recorders;
                        //this.setConfig(this._umResponse.IsChainDBsConfigured);
                        SessionHandler.IsInquire = this._umResponse.IsInquire;
                        SessionHandler.STTEnabledChannels = this._umResponse.STTEnabledChannels;

                        if (this._umResponse.IsUserLocked)
                        {
                            FailureText.Text = "Your account is locked. Click below link to restore or contact your administrator.";
                            return;
                        }

                        if (_loginUser != null)
                        {
                            //if (!_loginUser.IsAgreedToLicense && this.chkLicenseAgreement.Checked == false)
                            //{
                            //    FailureText.Text = "Please accept Revcord software license agreement.";
                            //    divLicenseAgreement.Visible = true;
                            //    return;
                            //}
                            //else
                            //    divLicenseAgreement.Visible = true;

                            //if (!_loginUser.IsAgreedToLicense && this.chkLicenseAgreement.Checked)
                            //{
                            //    // update bool type field. 
                            //    UserLicenseAgreement userLicenseAgreement = new UserLicenseAgreement { UserNum = _loginUser.UserNum, UserName = _loginUser.UserName, UserEmail = _loginUser.UserID, CreatedDate = DateTime.Now, IsLicenseAccepted = true, IsDeleted = false };
                            //    UserManagementRequest umRequest = new UserManagementRequest { UserLicenseAgreement = userLicenseAgreement, TenantId = _tenantId };
                            //    UserManagementResponse umResponse = new UserManagementService().AcceptLicenseAgreement(umRequest);
                            //}

                            if (_loginUser.IsLockedOut)
                            {
                                FailureText.Text = "Your account is locked. Click below link to restore or contact your administrator.";
                                return;
                            }

                            if (_loginUser.LastPasswordChanged.HasValue)
                            {
                                //string lastPasswordChange = "11.22.2020";
                                //DateTime startDate = DateTime.Parse(lastPasswordChange);
                                DateTime startDate = _loginUser.LastPasswordChanged.Value;
                                DateTime expiryDate = startDate.AddDays(_passwordExpiryInDays);
                                if (DateTime.Now > expiryDate)
                                {
                                    Console.WriteLine("password expired");
                                    FailureText.Text = "Your account password for Revcord MMS has expired. Please click the Recover Password link below.";
                                    return;
                                }
                            }

                            if (Cache["ReturnUrl"] == null/*Request.QueryString["ReturnUrl"] == null*/)//This check is for session expire
                            {
                                SessionRedirectUser(_loginUser, _password);
                            }
                            else
                            {
                                string ReturnUrl = SecureQuery.Decrypt(Cache["ReturnUrl"].ToString());
                                if (ReturnUrl.ToLower().Contains("evaluatecalls.aspx"))
                                {
                                    ReturnUrl = ReturnUrl.Split('/')[1];//"/Evaluation";
                                }
                                UserData CacheUserData = new UserData();
                                CacheUserData = (UserData)Cache["TimeOutUserObject"];

                                if (Cache["TimeOutUserObject"] != null)
                                {
                                    if ((_loginUser.UserNum == CacheUserData.UserNum))
                                    {
                                        SessionHandler.UserInformation = CacheUserData;
                                        SessionHandler.UserID = CacheUserData.UserNum;
                                        SessionHandler.UserName = CacheUserData.UserName;
                                        Cache["Relogin"] = true;
                                        Cache.Remove("TimeOutUserObject");
                                        Cache.Remove("ReturnUrl");
                                        Response.Redirect(ReturnUrl, false);
                                    }
                                    else
                                    {
                                        SessionRedirectUser(_loginUser, _password);
                                    }
                                }
                                else
                                {
                                    SessionRedirectUser(_loginUser, _password);

                                }
                            }
                        }
                        else
                        {
                            if (this._umResponse.IsUserExists && _enableLockout)
                            {
                                //int remainingAttempts = _invalidLoginAttempts - SessionHandler.LoginAttempts;
                                //int remainingAttempts = Convert.ToInt32(this.hdnRemainingLoginAttempts.Value);
                                //this.hdnRemainingLoginAttempts.Value = (--remainingAttempts).ToString();
                                FailureText.Text = _remainingAttempts <= 1 ? "WARNING: After 5 consecutive unsuccessful login attempts, your account will be locked." : string.Format("Invalid Username / Password. (Attempts remaining: {0})", _remainingAttempts);

                                if (_remainingAttempts <= 0)
                                {
                                    //bool userLocked = new UserManagementService().LockUnlockUserAccount(Request.Form["userID"], true, _tenantId);
                                    bool userLocked = new UserManagementService().LockUnlockUserAccount(_userName, true, _tenantId);

                                    if (userLocked)
                                    {
                                        string message = "Your account has been locked due to multiple invalid login attempts. Use Recovery link below";
                                        this.FailureText.Text = message;
                                    }
                                }

                                ActivityLogger.LogAsync(0, 3, string.Format("Login failed for {0}. Invalid User Name or Password", _userName), "AL_LoginFailInValidCredentials", _userName, "", IPHelper.GetVisitorIPAddress(HttpContext.Current), _tenantId);//LogAsync();
                            }
                            else
                                FailureText.Text = "Invalid Username / Password";
                        }
                    }
                }
                else
                {
                    FailureText.Text = "Username and Password are required fields.";
                    if (_password == "")
                        userPW.BorderColor = System.Drawing.Color.Red;
                }

            }
            catch (Exception ex)
            {
                this.hdnTenantId.Value = "0";
                this.hdnSoftwareVersion.Value = string.Empty;
                this.hdnTenantName.Value = string.Empty;
                if (ex.Message.Contains("Force password change required"))
                {
                    FailureText.Text = ex.Message;
                    if (_forcePasswordChange)
                    {
                        string email = _userName;
                        string script = "<script type=\"text/javascript\"> $(document).ready(function() { forcePwdChange(\"" + email + "\"); }); </script>";
                        ClientScript.RegisterClientScriptBlock(this.GetType(), "ForceResetPassword", script);
                    }
                    return;
                }
                throw ex;
            }
        }

        private void setConfig(bool isChainDbConfig)
        {
            if (_isMTEnable)
            {
                SessionHandler.TenantConfiguration = new TenantConfiguration();
                SessionHandler.TenantConfiguration.IsChainDBsConfigured = isChainDbConfig;
                SessionHandler.TenantConfiguration.DBConnectionString = _tenantConnString;
            }
            else
            {
                if (SiteConfig.IsChainDBsConfigured != isChainDbConfig)
                {
                    SiteConfig.IsChainDBsConfigured = isChainDbConfig;
                }
            }
        }

        public void SessionRedirectUser(User objApUser, string _password)
        {
            try
            {
                SessionHandler.IsRoleBasedAccessEnabled = RevCord.Util.SiteConfig.IsRoleBasedAccessEnabled;
                if (!SessionHandler.IsRoleBasedAccessEnabled || objApUser.RoleId == 0)
                {
                    if (objApUser.Role == null)
                    {
                        objApUser.Role = new DataContracts.RoleManagement.Role();
                    }

                    objApUser.Role.Id = 0;
                }
                // Commented by SA : 20180630
                //CryptoHelper cryptoHelper = new CryptoHelper(AppSettingsUtil.GetString("encryptionPassword", "******"));
                UserData obUserData = new UserData();

                obUserData.UserNum = objApUser.UserNum;
                obUserData.UserName = objApUser.UserName;
                obUserData.ProfilePicture = string.IsNullOrEmpty(objApUser.UserPic) ? ResolveUrl("~/assets/icons/um/loginUserIcon.png") : (!_isMTEnable ? ResolveUrl(string.Format(@"~/Uploads/UserImages/{0}", objApUser.UserPic)) : ResolveUrl(string.Format(@"~/Uploads/UserImages/Tenant_{0}/{1}", _tenantId, objApUser.UserPic)));
                obUserData.GroupNum = objApUser.GroupNum;
                obUserData.UserType = objApUser.UserType;
                obUserData.Status = objApUser.Status;
                obUserData.SearchRest = objApUser.SearchRest;
                obUserData.SelectType = objApUser.SelectType;
                obUserData.ViewID = objApUser.ViewID;
                obUserData.IsRouteFromDefault = true;
                obUserData.AssignedNodes = objApUser.AssignedNodes;
                obUserData.UserName = objApUser.UserName;
                obUserData.UserID = objApUser.UserID;
                obUserData.UserPW = _password;
                obUserData.IsTempLogin = objApUser.IsTempLogin; // For Resetting password
                obUserData.Is2FAEnabled = objApUser.Is2FAEnabled; // For Two Factor Authentication
                obUserData.HasShiftRest = objApUser.HasShiftRest;
                obUserData.IsDeviceUser = "" + objApUser.IsDeviceUser;
                obUserData.IsEnterpriseUser = objApUser.IsEnterpriseUser;
                obUserData.IsIwbUser = objApUser.IsIwbUser;
                obUserData.OrganizationId = objApUser.OrganizationId;
                obUserData.IwbUserRole = objApUser.IwbRole;
                obUserData.CustomerId = objApUser.CustomerIdZoho;
                //obUserData.IsEventSpecific = objApUser.IsEventSpecific;
                obUserData.CanInvite = objApUser.CanInvite;
                obUserData.RecorderAccessRights = objApUser.RecorderAccessRights;
                obUserData.EnableUser = objApUser.EnableDisableInquireUser;
                obUserData.IsMDEnabled = objApUser.IsDeviceUser == 1 && objApUser.EnableDisableInquireUser == 2 ? true : false;
                obUserData.IsIQ3Enabled = objApUser.IsDeviceUser == 1 && objApUser.EnableDisableInquireUser == 1 ? true : false;
                obUserData.IsAvrisViewEnabled = (obUserData.UserType == 0 && obUserData.IsIQ3Enabled) && objApUser.IsAvrisView;
                obUserData.IsIQ3ViewEnabled = (obUserData.UserType == 0 && obUserData.IsIQ3Enabled) && objApUser.IsIQ3View;
                obUserData.LastPasswordChange = objApUser.LastPasswordChanged;
                obUserData.IsCompactView = objApUser.IsCompactView;
                obUserData.Role = objApUser.Role;
                obUserData.RoleId = objApUser.RoleId;
                obUserData.IsCompactView = objApUser.IsCompactView;
                obUserData.Ext = objApUser.Ext;
                obUserData.UserPhone = objApUser.UserPhone;


                if (obUserData.RecorderAccessRights == null)
                {
                    obUserData.RecorderAccessRights = new List<RecorderAccessRight>();
                }

                if (obUserData.RecorderAccessRights != null && obUserData.SelectType == 0)
                    obUserData.RecorderAccessRights.Insert(0, new RecorderAccessRight { RecId = 1, AccessRight = objApUser.UserGroup.AssignAuth });

                //obUserData.QBUserId = objApUser.QBUserId;
                //obUserData.ExistsOnQB = objApUser.ExistsOnQB;

                // Commented by SA : 20180630
                //obUserData.AssignedNodes.ForEach(assNode =>
                //{ //Replaced with EC
                //    if (obUserData.ExtensionCallInfos == null) obUserData.ExtensionCallInfos = new List<ExtensionCallInfo>();
                //    obUserData.ExtensionCallInfos.Add(new ExtensionCallInfo { CallType = 0, GroupExtension = assNode });
                //});

                // Deliberately turn off the role based permission if RoleId = 0 and role based permissions are enabled. 20220419
                if (SessionHandler.IsRoleBasedAccessEnabled && obUserData.RoleId == 0)
                {
                    SessionHandler.IsRoleBasedAccessEnabled = false;
                }
                if (SessionHandler.IsRoleBasedAccessEnabled)
                {
                    if (SiteConfig.IsECEnabled && objApUser.IsEnterpriseUser)
                    {
                        obUserData.AccessSetup = objApUser.RecorderAccessRights.Exists(p => p.AccessRight.Substring(0, 1) == "1");
                        //if (objApUser.IsDeviceUser == 1)
                        if (obUserData.IsIQ3Enabled)
                        {
                            obUserData.AccessSetup = true;
                        }
                        obUserData.AccessIRLite = objApUser.RecorderAccessRights.Exists(p => p.AccessRight.Substring(1, 1) == "1");
                        obUserData.AccessMonitor = objApUser.RecorderAccessRights.Exists(p => p.AccessRight.Substring(2, 1) == "1");
                        obUserData.AccessSearch = objApUser.RecorderAccessRights.Exists(p => p.AccessRight.Substring(3, 1) == "1");
                        obUserData.AccessEvaluation = objApUser.RecorderAccessRights.Exists(p => p.AccessRight.Substring(4, 1) == "1");
                        obUserData.AccessDashboard = objApUser.RecorderAccessRights.Exists(p => p.AccessRight.Substring(5, 1) == "1");
                        obUserData.AccessReport = objApUser.RecorderAccessRights.Exists(p => p.AccessRight.Substring(7, 1) == "1");
                        obUserData.AccessInstantRecall = objApUser.RecorderAccessRights.Exists(p => p.AccessRight.Substring(8, 1) == "1");
                        obUserData.AccessSaveFile = objApUser.RecorderAccessRights.Exists(p => p.AccessRight.Substring(9, 1) == "1");

                        obUserData.AccessInvitation = objApUser.CanInvite;
                        obUserData.RolePermissions = objApUser.RolePermissions;
                        obUserData.RoleId = objApUser.RoleId;
                    }
                    else
                    {
                        obUserData.AccessSetup = objApUser.RolePermissions.Exists(p => p.PermissionId == (int)MMSPermission.Setup);
                        //if (objApUser.IsDeviceUser == 1)
                        if (obUserData.IsIQ3Enabled)
                        {
                            obUserData.AccessSetup = true;
                        }
                        if (objApUser.IsIwbUser == true && objApUser.UserType == 8)
                        {
                            obUserData.AccessSetup = true;
                        }

                        obUserData.AccessIRLite = objApUser.RolePermissions.Exists(p => p.PermissionId == 2);
                        obUserData.AccessMonitor = objApUser.RolePermissions.Exists(p => p.PermissionId == 3);
                        obUserData.AccessSearch = objApUser.RolePermissions.Exists(p => p.PermissionId == 4);
                        obUserData.AccessEvaluation = objApUser.RolePermissions.Exists(p => p.PermissionId == 5);
                        obUserData.AccessDashboard = objApUser.RolePermissions.Exists(p => p.PermissionId == 6);
                        //obUserData.AccessRoleManagement = objApUser.RolePermissions.Exists(p => p.PermissionId == 7);
                        obUserData.AccessReport = objApUser.RolePermissions.Exists(p => p.PermissionId == 8);
                        obUserData.AccessInstantRecall = objApUser.RolePermissions.Exists(p => p.PermissionId == 9);
                        obUserData.AccessSaveFile = objApUser.RolePermissions.Exists(p => p.PermissionId == 10);

                        obUserData.AccessInvitation = objApUser.RolePermissions.Exists(p => p.PermissionId == 11);
                        obUserData.RolePermissions = objApUser.RolePermissions;
                        obUserData.RoleId = objApUser.RoleId;
                    }
                }
                else if (objApUser.UserGroup != null || objApUser.SelectType == 1)
                {
                    obUserData.SimpleAccessRight = objApUser.SimpleAccessRight;

                    if (objApUser.UserGroup != null)
                    {
                        obUserData.AssignAuth = objApUser.UserGroup.AssignAuth;
                        obUserData.GroupName = objApUser.UserGroup.GroupName;

                    }

                    if (objApUser.SelectType == 1 && objApUser.SimpleAccessRight != null)
                    {
                        //obUserData.AccessSetup = objApUser.SimpleAccessRight.Substring(0, 1) == "1" ? true : false;
                        obUserData.AccessSetup = objApUser.UserType == 1 ? true : objApUser.SimpleAccessRight.Substring(0, 1) == "1" ? true : false;
                        if (objApUser.IsDeviceUser == 1)
                        {
                            obUserData.AccessSetup = true;
                        }
                        if (objApUser.IsIwbUser == true)
                        {
                            obUserData.AccessSetup = true;
                        }
                        obUserData.AccessIRLite = objApUser.SimpleAccessRight.Substring(1, 1) == "1" ? true : false;
                        obUserData.AccessMonitor = objApUser.SimpleAccessRight.Substring(2, 1) == "1" ? true : false;
                        obUserData.AccessSearch = objApUser.SimpleAccessRight.Substring(3, 1) == "1" ? true : false;
                        obUserData.AccessEvaluation = objApUser.SimpleAccessRight.Substring(4, 1) == "1" ? true : false;
                        obUserData.AccessDashboard = objApUser.SimpleAccessRight.Substring(5, 1) == "1" ? true : false;
                        //obUserData.AccessRoleManagement = objApUser.SimpleAccessRight.Substring(6, 1) == "1" ? true : false;
                        // RevConfig make 6th bit on while assigning Access Rights to simple user while 7th bit should
                        // be on for Evaluation Reports, So I've to use OR condition for Evaluation Search.
                        //obUserData.AccessEvalReports = objApUser.SimpleAccessRight.Substring(5, 1) == "1" 
                        //   || objApUser.SimpleAccessRight.Substring(6, 1) == "1" ? true : false;
                        //obUserData.AccessEvalReports = objApUser.SimpleAccessRight.Substring(6, 1) == "1" ? true : false;
                        obUserData.AccessReport = objApUser.SimpleAccessRight.Substring(7, 1) == "1" ? true : false;
                        obUserData.AccessInstantRecall = objApUser.SimpleAccessRight.Substring(8, 1) == "1" ? true : false;
                        obUserData.AccessSaveFile = objApUser.SimpleAccessRight.Substring(9, 1) == "1" ? true : false;
                        obUserData.AccessInvitation = objApUser.CanInvite;
                    }
                    else
                    {
                        if (objApUser.IsEnterpriseUser)
                        {
                            List<string> accessRights = new List<string>();
                            accessRights.Add(objApUser.UserGroup.AssignAuth);
                            accessRights.Add(objApUser.SimpleAccessRight);
                            objApUser.UserGroup.AssignAuth = StringHelper.ProcesSimpleAccessRights(accessRights);
                        }

                        obUserData.AccessSetup = objApUser.UserType == 1 ? true : objApUser.UserGroup.AssignAuth.Substring(0, 1) == "1" ? true : false;
                        if (objApUser.IsDeviceUser == 1)
                        {
                            obUserData.AccessSetup = true;
                        }
                        obUserData.AccessIRLite = objApUser.UserGroup.AssignAuth.Substring(1, 1) == "1" ? true : false;
                        obUserData.AccessMonitor = objApUser.UserGroup.AssignAuth.Substring(2, 1) == "1" ? true : false;
                        obUserData.AccessSearch = objApUser.UserGroup.AssignAuth.Substring(3, 1) == "1" ? true : false;
                        obUserData.AccessEvaluation = objApUser.UserGroup.AssignAuth.Substring(4, 1) == "1" ? true : false;
                        obUserData.AccessDashboard = objApUser.UserGroup.AssignAuth.Substring(5, 1) == "1" ? true : false;
                        //obUserData.AccessRoleManagement = objApUser.UserGroup.AssignAuth.Substring(6, 1) == "1" ? true : false;
                        obUserData.AccessReport = objApUser.UserGroup.AssignAuth.Substring(7, 1) == "1" ? true : false;
                        obUserData.AccessInstantRecall = objApUser.UserGroup.AssignAuth.Substring(8, 1) == "1" ? true : false;
                        obUserData.AccessSaveFile = objApUser.UserGroup.AssignAuth.Substring(9, 1) == "1" ? true : false;
                        obUserData.AccessInvitation = objApUser.CanInvite;
                    }
                    // Invitation Access is independent of AssignAuth Permission Bits. For V10 this permission is user specific.

                }

                if (_isMTEnable)
                {
                    try
                    {
                        var tenantConfigurations = new TenantService().GetConfigurations(_tenantId);

                        try
                        {
                            var value = tenantConfigurations.Configurations.Where(c => c.KeyName == "IsOnlyIQ3ModeEnabled").FirstOrDefault().KeyValue;
                            SessionHandler.IsOnlyIQ3ModeEnabled = Convert.ToBoolean(value);

                            var iwbValue = tenantConfigurations.Configurations.Where(c => c.KeyName == "IsIWBModeEnabled").FirstOrDefault().KeyValue;
                            SessionHandler.IsIWBModeEnabled = Convert.ToBoolean(iwbValue);
                        }
                        catch (Exception)
                        {
                            SessionHandler.IsOnlyIQ3ModeEnabled = false;
                            SessionHandler.IsIWBModeEnabled = false;
                        }

                        try
                        {
                            var value = tenantConfigurations.Configurations.Where(c => c.KeyName == "IsRevcellEnabled").FirstOrDefault().KeyValue;
                            // Needs to implemented to support Tenant based Revcell enable/disable configuration
                            //SessionHandler.IsRevcellEnabled = Convert.ToBoolean(value);
                        }
                        catch (Exception)
                        {
                            //SessionHandler.IsRevcellEnabled = false;
                        }

                        try
                        {
                            var value = tenantConfigurations.Configurations.Where(c => c.KeyName == "IsCustomGraphicMarkerEnabled").FirstOrDefault().KeyValue;
                            SessionHandler.IsCustomGraphicMarkerEnabled = Convert.ToBoolean(value);
                        }
                        catch (Exception)
                        {
                            SessionHandler.IsCustomGraphicMarkerEnabled = false;
                        }

                        try
                        {
                            // This needs to be taken care from t_SerialKey table, there is no point to keep serial number at two different places

                            string systemSerialNumber = tenantConfigurations.Configurations.Where(c => c.KeyName == "SystemSerialNumber").FirstOrDefault().KeyValue;
                            SessionHandler.SystemSerialNumber = systemSerialNumber;
                        }
                        catch (Exception)
                        {
                            SessionHandler.SystemSerialNumber = string.Empty;
                        }

                        PlivoTenantConfiguration PlivoConfig = new PlivoTenantConfiguration();

                        try
                        {
                            string PlivoAuthID = tenantConfigurations.Configurations.Where(c => c.KeyName == "PlivoAuthID").FirstOrDefault().KeyValue;
                            PlivoConfig.PlivoAuthID = PlivoAuthID;
                        }
                        catch (Exception)
                        {
                            PlivoConfig.PlivoAuthID = SiteConfig.PlivoAuthID;
                        }

                        try
                        {
                            string PlivoAuthToken = tenantConfigurations.Configurations.Where(c => c.KeyName == "PlivoAuthToken").FirstOrDefault().KeyValue;
                            PlivoConfig.PlivoAuthToken = PlivoAuthToken;
                        }
                        catch (Exception)
                        {
                            PlivoConfig.PlivoAuthToken = SiteConfig.PlivoAuthToken;
                        }

                        try
                        {
                            string PlivoEndpointAppId = tenantConfigurations.Configurations.Where(c => c.KeyName == "PlivoEndpointAppId").FirstOrDefault().KeyValue;
                            PlivoConfig.PlivoEndpointAppId = PlivoEndpointAppId;
                        }
                        catch (Exception)
                        {
                            PlivoConfig.PlivoEndpointAppId = SiteConfig.PlivoEndpointAppId;
                        }

                        try
                        {
                            string PlivoPhoneNumberAppId = tenantConfigurations.Configurations.Where(c => c.KeyName == "PlivoPhoneNumberAppId").FirstOrDefault().KeyValue;
                            PlivoConfig.PlivoPhoneNumberAppId = PlivoPhoneNumberAppId;
                        }
                        catch (Exception)
                        {
                            PlivoConfig.PlivoPhoneNumberAppId = SiteConfig.PlivoPhoneNumberAppId;
                        }

                        SessionHandler.PlivoConfig = PlivoConfig;

                        //Exception : TODO
                        //Object does not match target type. 
                        //KeyValueConvertor kvConvertor = new KeyValueConvertor(config);
                        //var mtMMSConfig = kvConvertor.getMTMMSConfigurations();
                        //SessionHandler.IsOnlyIQ3ModeEnabled = mtMMSConfig.IsOnlyIQ3ModeEnabled;
                    }
                    catch (Exception ex)
                    {
                        SessionHandler.IsOnlyIQ3ModeEnabled = false;
                        SessionHandler.IsIWBModeEnabled = false;
                        SessionHandler.IsMTRModeEnabled = false;
                    }
                }
                else
                {
                    SessionHandler.IsOnlyIQ3ModeEnabled = SiteConfig.IsOnlyIQ3ModeEnabled;
                    SessionHandler.IsCustomGraphicMarkerEnabled = SiteConfig.IsCustomGraphicMarkerEnabled;

                    SessionHandler.IsIWBModeEnabled = SiteConfig.IsIWBModeEnabled;
                    SessionHandler.IsMTRModeEnabled = SiteConfig.IsMTRModeEnabled;
                    SessionHandler.IsOnlyIWBModeEnabled = SiteConfig.IsOnlyIWBModeEnabled;

                    PlivoTenantConfiguration PlivoConfig = new PlivoTenantConfiguration();

                    PlivoConfig.PlivoAuthID = SiteConfig.PlivoAuthID;
                    PlivoConfig.PlivoAuthToken = SiteConfig.PlivoAuthToken;
                    PlivoConfig.PlivoEndpointAppId = SiteConfig.PlivoEndpointAppId;
                    PlivoConfig.PlivoPhoneNumberAppId = SiteConfig.PlivoPhoneNumberAppId;

                    SessionHandler.PlivoConfig = PlivoConfig;
                }

                this.setConfig(this._umResponse.IsChainDBsConfigured);

                bool isECEnabled = SiteConfig.IsECEnabled;
                bool isEntRecorder = SiteConfig.IsEnterpriseRecorder;
                SessionHandler.IsECEnabled = isECEnabled;
                this._umResponse.IsECEnabled = isECEnabled;
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Login, "SessionRedirectUser", _tenantId, "isECEnabled = " + isECEnabled + " isEntRecorder = " + isEntRecorder));
                SessionHandler.IsEnterpriseRecorder = isEntRecorder;
                SessionHandler.LoadAudioNodesOnly = AppSettingsUtil.GetBool("LoadAudioNodesOnly", false);
                SessionHandler.HideTextAndScreensTree = AppSettingsUtil.GetBool("HideTextAndScreensTree", false);
                SetSessionForMultiTenancy();
                if (SessionHandler.IsECEnabled)
                {
                    if (SessionHandler.IsEnterpriseRecorder)
                    {
                        SessionHandler.Recorders = this._recorders;
                        SessionHandler.Recorders.FirstOrDefault(r => r.IsPrimary).ConnectionString = SiteConfig.DALConnectionString;
                    }
                    else
                        SessionHandler.Recorders = this._recorders.Where(rec => rec.IsPrimary == false).ToList();
                    SessionHandler.LocalRecorder = this._recorders.FirstOrDefault(rec => rec.IsPrimary == true);
                    SessionHandler.LocalRecorder.ConnectionString = SiteConfig.DALConnectionString;
                    // Do check here if user is admin or enterprise
                    //if (!SessionHandler.UserInformation.IsEnterpriseUser && SessionHandler.UserInformation.UserType != 1)
                    //{
                    //    SessionHandler.IsECEnabled = false;
                    //}
                }
                else
                {
                    SessionHandler.Recorders = this._recorders.Where(rec => rec.IsPrimary == true).ToList();
                    SessionHandler.Recorders.FirstOrDefault(r => r.IsPrimary).ConnectionString = SiteConfig.DALConnectionString;
                }
                if (_isMTEnable)
                {
                    if (SessionHandler.LocalRecorder != null) SessionHandler.LocalRecorder.ConnectionString = _tenantConnString;
                    SessionHandler.Recorders.FirstOrDefault(r => r.IsPrimary).ConnectionString = _tenantConnString;
                }

                if (Request.Browser.Type.ToUpper().Contains("IE") || Request.Browser.Type.ToUpper().Contains("INTERNETEXPLORER"))
                {
                    SessionHandler.IsECEnabled = false;
                }
                //SessionHandler.Recorders = isEntRecorder == true ? this._recorders : this._recorders.Where(rec => rec.IsPrimary == false).ToList();
                //SessionHandler.Recorders = this._recorders;

                foreach (var recorder in SessionHandler.Recorders)
                {
                    string connectionString = recorder.ConnectionString;
                    if (!connectionString.Contains("Data Source"))
                        recorder.ConnectionString = _encryption.DecryptionTechnique(connectionString, null, null);
                }

                SessionHandler.CurrentLanguage = !String.IsNullOrEmpty(Request.Form["hdnLanguage"]) ? Request.Form["hdnLanguage"] : AppSettingsHelper.GetValueAsString("defaultLang");
                if (SessionHandler.IsOnlyIQ3ModeEnabled == true)
                {
                    obUserData.AccessInstantRecall = false;
                    obUserData.AccessIRLite = false;
                }

                if (SessionHandler.IsIWBModeEnabled == true)
                {
                    obUserData.AccessInstantRecall = false;
                    obUserData.AccessIRLite = false;
                }

                //SessionHandler.QBUserPassword = cryptoHelper.Encrypt(obUserData.UserPW);


                string redirectURL = !String.IsNullOrEmpty(Request.QueryString["redirect"])
                    ? Request.QueryString["redirect"]
                    : ApplicationConstant.WebConstants.DEFAULT_PAGE;

                //if (obUserData.IsTempLogin)
                //{
                //    //redirectURL = "~/UserManager/ResetTempPassword.aspx";
                //    string temp = _password;
                //    string script = "<script type=\"text/javascript\"> $(document).ready(function() { ShowDialogResetPassword(\"" + temp + "\"); }); </script>";
                //    ClientScript.RegisterClientScriptBlock(this.GetType(), "ResetPassword", script);
                //    //return;
                //    //Response.Redirect(redirectURL, false);
                //}

                this.RemoveAllCache();
                if (obUserData.Status == 0)
                {
                    FailureText.Text = "You are not an active user.";
                    ActivityLogger.LogAsync(SessionHandler.UserID, 3, string.Format("Login failed for {0}. {0} is not an active user", obUserData.UserName), "AL_LoginFailNotActive", obUserData.UserName, "", IPHelper.GetVisitorIPAddress(HttpContext.Current));
                }
                else if (!IsValidemail(obUserData.UserID))
                {
                    redirectURL = "~/UserManager/EditUseremailPassword.aspx";
                    Response.Redirect(redirectURL, false);
                }
                else if (obUserData.IsTempLogin)
                {
                    //redirectURL = "~/UserManager/ResetTempPassword.aspx";
                    SessionHandler.LoginAttempts = 0;

                    string temp = _password;
                    string script = "<script type=\"text/javascript\"> $(document).ready(function() { ShowDialogResetPassword(\"" + temp + "\"); }); </script>";
                    ClientScript.RegisterClientScriptBlock(this.GetType(), "ResetPassword", script);
                    //return;
                    //Response.Redirect(redirectURL, false);
                    //ActivityLogger.LogAsync(SessionHandler.UserID, 3, string.Format("Resetting Temporary Password {0}.", obUserData.UserName), "AL_LoginFailNoPermissions", obUserData.UserName, "", IPHelper.GetVisitorIPAddress(HttpContext.Current));
                }
                else if (!obUserData.IsIwbUser && !obUserData.AccessSetup && !obUserData.AccessMonitor && !obUserData.AccessSearch
                    && !obUserData.AccessEvaluation && !obUserData.AccessReport && !obUserData.AccessInstantRecall && !obUserData.AccessIRLite && !obUserData.AccessDashboard) //&& !obUserData.AccessSaveFile)
                {
                    FailureText.Text = "Your account does not have any permissions assigned. <br /> Please contact your Revcord Administrator.";
                    ActivityLogger.LogAsync(SessionHandler.UserID, 3, string.Format("Login failed for {0}. No Permissions are assigned", obUserData.UserName), "AL_LoginFailNoPermissions", obUserData.UserName, "", IPHelper.GetVisitorIPAddress(HttpContext.Current));
                }
                else
                {
                    obUserData.TenantId = this._tenantId;
                    obUserData.TenantName = this._tenantName;

                    if ((SiteConfig.Is2FAEnabled || obUserData.Is2FAEnabled) && obUserData.UserNum != 1000)
                    {
                        Session[ApplicationConstant.SessionConstants.USER_INFO_FOR_OTP] = obUserData;
                        Session[ApplicationConstant.SessionConstants.LOGIN_OTP] = Convert.ToString(new Random().Next(1000, 9999)); //Convert.ToString(1110);

                        redirectURL = "~/OTPLogin.aspx";
                        Response.Redirect(redirectURL, false);
                        SessionHandler.LoginAttempts = 0; // ADDED BY KM IFUTURZ - CAPTCHA
                        ActivityLogger.LogAsync(SessionHandler.UserID, 2, string.Format("{0} has redirected to OTP Page successfully", obUserData.UserName), "AL_UserLoggedIn", SessionHandler.UserName, "", IPHelper.GetVisitorIPAddress(HttpContext.Current));
                    }
                    else
                    {
                        Session[ApplicationConstant.SessionConstants.USER_INFO] = obUserData;
                        SessionHandler.UserInformation = obUserData;
                        SessionHandler.UserID = obUserData.UserNum;
                        SessionHandler.UserName = obUserData.UserName;
                        SessionHandler.IsAvrisView = obUserData.IsAvrisViewEnabled;
                        SessionHandler.IsIQ3View = obUserData.IsIQ3ViewEnabled;
                        SessionHandler.IsIQ3Enabled = obUserData.IsIQ3Enabled;


                        if (SessionHandler.IsECEnabled)
                        {
                            // Do check here if user is admin or enterprise
                            if (SessionHandler.UserInformation.UserNum != 1000)
                            {
                                if (SessionHandler.UserInformation.UserID != "<EMAIL>")
                                {
                                    if (!SessionHandler.UserInformation.IsEnterpriseUser)
                                    {
                                        SessionHandler.IsECEnabled = false;
                                    }
                                }
                            }
                        }

                        SessionHandler.LoginTime = DateTime.Now;
                        SessionHandler.UserInformation.HasShiftRest = obUserData.HasShiftRest;
                        SessionHandler.UserInformation.IsDeviceUser = obUserData.IsDeviceUser;

                        if (SiteConfig.IsECEnabled == true && !SessionHandler.IsEnterpriseRecorder)
                            obUserData.AccessDashboard = false;
                        //if (!obUserData.IsEventSpecific)
                        //{
                        //if (!obUserData.AccessDashboard)
                        if (!obUserData.AccessSearch)
                        {
                            if (obUserData.AccessMonitor)
                                redirectURL = "~/Monitor/Default.aspx";
                            else if (obUserData.AccessEvaluation)
                                redirectURL = "~/Evaluation/Default.aspx";
                            else if (obUserData.AccessReport)
                                redirectURL = "~/Reports/MMSReports.aspx";
                            //else if (SessionHandler.UserInformation.AccessEvalReports)
                            //    redirectURL = "~/Reports/EvaluationReport.aspx";
                            else if (obUserData.AccessSetup)
                                redirectURL = "~/UserManager/Default.aspx";
                            else if (obUserData.AccessInstantRecall)
                                redirectURL = "~/InstantRecall/InstantRecall.aspx";
                            else if (obUserData.AccessIRLite)
                                redirectURL = "~/InstantRecall/IRSlimBlank.aspx";
                            else if (obUserData.AccessDashboard)
                                redirectURL = "~/Dashboard/Default.aspx";
                        }
                        if (SessionHandler.IsOnlyIWBModeEnabled)
                        {
                            //redirectURL = "~/Iwb/ManageWelders.aspx";
                            redirectURL = SessionHandler.UserInformation.IwbUserRole.Modules.FirstOrDefault().URL;
                        }

                        Response.Redirect(redirectURL, false);
                        SessionHandler.LoginAttempts = 0; // ADDED BY KM IFUTURZ - CAPTCHA
                        ActivityLogger.LogAsync(SessionHandler.UserID, 2, string.Format("{0} has logged in successfully", SessionHandler.UserName), "AL_UserLoggedIn", SessionHandler.UserName, "", IPHelper.GetVisitorIPAddress(HttpContext.Current));
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        private string EncryptQueryString(string strQueryString)
        {
            return encryptDecrypt.Encrypt(strQueryString, _encryptDecryptKey);
        }

        private string DecryptQueryString(string strQueryString)
        {
            return encryptDecrypt.Decrypt(strQueryString, _encryptDecryptKey);
        }

        public void SetSessionForMultiTenancy()
        {
            if (_isMTEnable)
            {
                if (SessionHandler.TenantConfiguration == null) SessionHandler.TenantConfiguration = new TenantConfiguration();

                SessionHandler.TenantConfiguration.IsChainDBsConfigured = this._umResponse.IsChainDBsConfigured;
                SessionHandler.TenantConfiguration.DBConnectionString = _tenantConnString;
                SessionHandler.TenantConfiguration.IsECEnabled = this._umResponse.IsECEnabled;
                SessionHandler.TenantConfiguration.IsEnterpriseRecorder = this._umResponse.IsEnterpriseRecorder;

                // UpdateSessionForMultiTenancy();
            }
        }
        public void UpdateSessionForMultiTenancy()
        {
            SessionHandler.IsECEnabled = SessionHandler.TenantConfiguration.IsECEnabled;
            SessionHandler.IsEnterpriseRecorder = SessionHandler.TenantConfiguration.IsEnterpriseRecorder;
        }

        private void RemoveAllCache()
        {
            IDictionaryEnumerator enumerator = HttpContext.Current.Cache.GetEnumerator();

            while (enumerator.MoveNext())
            {
                Cache.Remove(enumerator.Key.ToString());
            }
        }

        private void CreateTenantIconDirectory(int tenantId)
        {
            try
            {
                var tenantIconsDirPath = Server.MapPath(string.Format("~/SystemUploadedFiles/MediaIcons/Tenant_{0}", tenantId));
                var defaultIconsPath = Server.MapPath("~/SystemUploadedFiles/MediaIcons/DefaultIcons");
                if (!Directory.Exists(tenantIconsDirPath))
                {
                    Directory.CreateDirectory(tenantIconsDirPath);
                    string[] sourceFiles = Directory.GetFiles(defaultIconsPath, "*.png").ToArray();

                    foreach (var sourceFile in sourceFiles)
                    {
                        string file = sourceFile.ToString();

                        string destination = tenantIconsDirPath + @"\" + Path.GetFileName(file.ToString());
                        File.Copy(file, destination, true);
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }

}