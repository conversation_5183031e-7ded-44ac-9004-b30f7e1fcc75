﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;
using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.Criteria;
using RevCord.DataContracts.IWBEntities;
using RevCord.DataContracts.MessageBase;
using RevCord.DataContracts.Messages;
using RevCord.DataContracts.Request;
using RevCord.DataContracts.UserManagement;
using RevCord.ServiceImplementation;
using RevCord.Util;
using RevCord.VoiceRec.WebUIClient.Classes;
using RevCord.VoiceRec.WebUIClient.Classes.Common;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using System.Web;
using System.Web.Script.Serialization;
using iTextSharp.text;
using iTextSharp.text.pdf;
using RevCord.VoiceRec.WebUIClient.Classes.Util.RevSignUtil;
using System.Text.RegularExpressions;
using System.Text;
using ExtraEmailConfigurationLib.Helpers;

namespace RevCord.VoiceRec.WebUIClient.Handlers.IwbHandlers
{
    /// <summary>
    /// Summary description for IwbHandler
    /// </summary>
    public class IwbHandler : BaseHandler//IHttpHandler, IReadOnlySessionState
    {

        #region ----- Attributs -----
        private IwbRequest _iwbRequest = null;
        private IwbResponse _iwbResponse = null;

        public int TenantId { get { return SessionHandler.UserInformation.TenantId; } } //{ get { return 0; } }
        public int UserNum { get { return SessionHandler.UserInformation.UserNum; } }//{ get { return 1000; } }

        private string _webURL = SiteConfig.WebURL;
        private string _iwbDocumentPath = HttpContext.Current.Server.MapPath("~/SystemUploadedFiles/Iwb/");
        private string _iwbJsonResponsePath = HttpContext.Current.Server.MapPath("~/SystemUploadedFiles/Iwb/OCRJsonResponse/");
        private string _iwbOutputReportsPdfPath = HttpContext.Current.Server.MapPath("~/SystemUploadedFiles/Iwb/OutputReports/");
        private IwbDocumentType _fileType;
        private HttpPostedFile _postedFile = null;
        private string _uploadFilePath = "";
        private string _aiApiUrl = SiteConfig.AISERVEROCRURL;
        private string _outputFileName = "";
        private string _jsonFileName = "";
        private int _aiTimeoutSeconds = 600;


        private string _zohoBaseUrlAccount = AppSettingsHelper.GetValueAsString("zohoBillingBaseAccountUrl"); 
        private string _zohoBaseUrlBilling = AppSettingsHelper.GetValueAsString("zohoBillingBaseBillingUrl");
        private string _zohoClientId = AppSettingsHelper.GetValueAsString("zohoBillingClientId");
        private string _zohoClientSecret = AppSettingsHelper.GetValueAsString("zohoBillingClientSecret");
        private string _zohoOrganizationId = AppSettingsHelper.GetValueAsString("zohoBillingOrganizationId");
        private string _zohoRefreshToken = AppSettingsHelper.GetValueAsString("zohoBillingRefreshToken");
        private ZohoTokenResponse _zohoToken = null;


        #endregion

        #region ----- IHttpHandler Members -----



        public override void ProcessRequest(HttpContext context)
        {
            string method = context.Request.QueryString["method"];
            string acceptEncoding = context.Request.Headers["Accept-Encoding"];

            if (string.IsNullOrEmpty(method))//get from request instead of QueryString
                method = context.Request["method"];

            //var file = context.Request.Form["docFile"];
            //HttpPostedFile fileName = (HttpPostedFile)context.Request.Form["docFile"];

            switch (method)
            {

                case "SaveWps":
                    {
                        this.saveWps(context);
                        break;
                    }
                case "GetWpss":
                    context.Response.ContentType = "application/json";
                    this.getAllWps(context);
                    break;

                case "SaveJob":
                    {
                        this.saveJob(context);
                        break;
                    }
                case "AssignJob":
                    {
                        this.assignJob(context);
                        break;
                    }
                case "GetJobs":
                    context.Response.ContentType = "application/json";
                    this.getJobs(context);
                    break;
                case "UpdateJobStatus":
                    {
                        this.updateJobStatus(context);
                        break;
                    }
                case "UpdateJobApplicantStatus":
                    {
                        this.updateJobApplicantStauts(context);
                        break;
                    }

                case "ApplyForJob":
                    {
                        this.applyForJob(context);
                        break;
                    }
                case "GetJobApplicants":
                    {
                        this.getJobApplicants(context);
                        break;
                    }

                case "GetWelders":
                    context.Response.ContentType = "application/json";
                    this.getWelders(context);
                    break;
                case "SaveWelder":
                    {
                        this.saveWelder(context);
                        break;
                    }
                case "GetWorkHistory":
                    context.Response.ContentType = "application/json";
                    this.getWorkHistory(context);
                    break;

                case "SaveOrganization":
                    {
                        this.saveOrganization(context);
                        break;
                    }

                case "GetOrganizations":
                    context.Response.ContentType = "application/json";
                    this.getOrganizations(context);
                    break;

                case "SaveLocation":
                    {
                        this.saveLocation(context);
                        break;
                    }
                case "GetLocations":
                    context.Response.ContentType = "application/json";
                    this.getLocations(context);
                    break;

                case "DeleteLocation":
                    context.Response.ContentType = "application/json";
                    this.deleteLocation(context);
                    break;

                case "SaveWpq":
                    {
                        this.saveWPQ(context);
                        break;
                    }
                case "GetWpqs":
                    {
                        context.Response.ContentType = "application/json";
                        this.getUserWPQs(context);
                        break;
                    }
                case "GetJobsByWelder":
                    {
                        context.Response.ContentType = "application/json";
                        this.getJobsByWelder(context);
                        break;
                    }
                case "GetUserDataByUser":
                    {
                        context.Response.ContentType = "application/json";
                        this.getUserDataByUser(context);
                        break;
                    }
                case "UploadWpq":
                    {
                        this.uploadWpq(context);
                        break;
                    }

                case "SaveDocument":
                    {
                        this.saveDocument(context);
                        break;
                    }
                case "GetDocuments":
                    {
                        context.Response.ContentType = "application/json";
                        this.getDocuments(context);
                        break;
                    }
                case "GetDocumentById":
                    {
                        context.Response.ContentType = "application/json";
                        this.getDocumentById(context);
                        break;
                    }

                case "GetTestTypes":
                    {
                        context.Response.ContentType = "application/json";
                        this.getAllTestTypes(context);
                        break;
                    }
                case "SaveTest":
                    {
                        this.saveTest(context);
                        break;
                    }
                case "GetTests":
                    {
                        context.Response.ContentType = "application/json";
                        this.getTests(context);
                        break;
                    }
                case "RegisterWelderForTest":
                    {
                        this.registerWelderForTest(context);
                        break;
                    }
                case "GetTestAttendees":
                    {
                        context.Response.ContentType = "application/json";
                        this.getTestAttendees(context);
                        break;
                    }
                case "GetSignOffRequestUsers":
                    {
                        context.Response.ContentType = "application/json";
                        this.getSignOffRequestUsers(context);
                        break;
                    }
                case "GetWelderRatings":
                    {
                        context.Response.ContentType = "application/json";
                        this.getWelderRatings(context);
                        break;
                    }
                case "GetJobTitles":
                    {
                        context.Response.ContentType = "application/json";
                        this.getJobTitles(context);
                        break;
                    }
                case "GetJobDetailsById":
                    {
                        context.Response.ContentType = "application/json";
                        this.getJobDetailsById(context);
                        break;
                    }
                case "UpdateWelderTestStatus":
                    context.Response.ContentType = "application/json";
                    this.UpdateWelderTestStatus(context);
                    break;
                case "SaveTestInvitation":
                    {
                        context.Response.ContentType = "application/json";
                        this.saveTestInvitation(context);
                        break;
                    }
                case "WelderDashboard":
                    {
                        context.Response.ContentType = "application/json";
                        this.getWelderDashboardData(context);
                        break;
                    }
                case "SaveOCRResponse":
                    this.SaveOCRResponse(context);
                    break;
                case "SaveModifiedAIResponse":
                    this.SaveModifiedAIResponse(context);
                    break;
                case "ExportSignedPDF":
                    this.ExportSignedPDF(context);
                    break;
                case "SaveWelderRatings":
                    this.SaveWelderRatings(context);
                    break;
                case "TryAI":
                    this.TryAI(context);
                    break;
                case "UpdateTestStatus":
                    this.UpdateTestStatus(context);
                    break;
                case "RegisterUser":
                    {
                        this.registerUser(context);
                        break;
                    }
                case "CreateZohoInvoice":
                    {
                        this.CreateZohoInvoice(context);
                        break;
                    }
                case "GetZohoTransactionHistory":
                    GetZohoTransactionHistory(context);
                    break;
                case "RegisterUserForPayment":
                    {
                        this.registerCustomerOnZoho(context);
                        break;
                    }
                case "UpdateUserForCustomerId":
                    {
                        this.updateUserForCustomerId(context);
                        break;
                    }
                case "GetCustomerData":
                    {
                        this.getCustomerFromZoho(context);
                        break;
                    }

                default:
                    context.Response.ContentType = "application/json";
                    context.Response.Write(new JavaScriptSerializer().Serialize(new { status = false, message = "no method with this name exists" }));
                    break;
            }
            context.ApplicationInstance.CompleteRequest();
        }




        #endregion

        #region ----- WPS -----


        private void saveWps(HttpContext context)
        {
            try
            {
                var wps = JSONUtil.JsonToClass<Wps>(context.Request.Form["wps"]);
                wps.CreatedDate = DateTime.Now;

                this._iwbRequest = new IwbRequest
                {
                    UserId = UserNum,
                    TenantId = TenantId,
                    Wps = wps,
                };

                this._iwbResponse = new IwbService().SaveWPS(this._iwbRequest);
                switch (this._iwbResponse.Acknowledge)
                {
                    case AcknowledgeType.Success:
                        context.Response.Write(new JavaScriptSerializer().Serialize(
                                new
                                {
                                    success = true,
                                    message = "WPS has been saved successfully.",
                                    data = this._iwbResponse.IwbId,
                                }));
                        break;
                    case AcknowledgeType.Failure:
                        context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, message = "Error", data = this._iwbResponse.Message }));
                        break;
                }
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Handler, "IwbHandler->saveWps", 0, "saveWps executed successfully."));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->saveWps", 0, "An error has occurred inside saveWps function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, data = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud." }));
            }
        }

        private void getAllWps(HttpContext context)
        {
            try
            {
                this._iwbRequest = new IwbRequest { TenantId = TenantId, UserId = UserNum };
                var response = new IwbService().GetAllWPS(this._iwbRequest);


                if (response != null)
                {
                    context.Response.Write(JsonConvert.SerializeObject(
                        new
                        {
                            success = true,
                            message = "Documents have been fetched successfully.",
                            data = response.WPSs,
                        }));
                }
                else
                {
                    context.Response.Write(JsonConvert.SerializeObject(
                        new
                        {
                            success = false,
                            message = "There is a problem occour while fetching wps.",
                            data = new List<Wps>(),
                        }));
                }


                /*this._iwbRequest = new IwbRequest { TenantId = TenantId, UserId = UserNum };
                this._iwbRequest.Criteria = new IwbCriteria
                {
                    DocumentTypes = new List<IwbDocumentType>
                    {
                        IwbDocumentType.WPS,
                    },
                    OrganizationId = SessionHandler.UserInformation.OrganizationId
                };
                this._iwbResponse = new IwbService().GetDocumentsByWhereClause(this._iwbRequest);

                context.Response.Write(JsonConvert.SerializeObject(
                    new
                    {
                        success = true,
                        message = "Documents have been fetched successfully.",
                        data = this._iwbResponse.Documents,
                    }));*/
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->GetAllWps", 0, "An error has occurred inside GetAllWps function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, message = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud.", data = -1, }));
            }
        }

        #endregion


        #region ----- Work History -----

        private void saveWorkHistory(HttpContext context)
        {
            try
            {
                var workHistory = JSONUtil.JsonToClass<UserWorkHistory>(context.Request.Form["workHistory"]);
                workHistory.CreatedDate = DateTime.Now;
                workHistory.OrganizationId = SessionHandler.UserInformation.OrganizationId;

                this._iwbRequest = new IwbRequest
                {
                    UserId = UserNum,
                    TenantId = TenantId,
                    WorkHistory = workHistory,
                };

                this._iwbResponse = new IwbService().SaveWorkHistory(this._iwbRequest);
                switch (this._iwbResponse.Acknowledge)
                {
                    case AcknowledgeType.Success:
                        context.Response.Write(new JavaScriptSerializer().Serialize(
                                new
                                {
                                    success = true,
                                    message = "Work history has been saved successfully.",
                                    data = this._iwbResponse.IwbId,
                                }));
                        break;
                    case AcknowledgeType.Failure:
                        context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, message = "Error", data = this._iwbResponse.Message }));
                        break;
                }
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Handler, "IwbHandler->saveWorkHistory", 0, "saveJob executed successfully."));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->saveWorkHistory", 0, "An error has occurred inside saveWorkHistory function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, data = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud." }));
            }
        }

        private void getWorkHistory(HttpContext context)
        {
            try
            {
                var userId = Convert.ToInt32(context.Request["userId"]);
                this._iwbRequest = new IwbRequest { TenantId = TenantId, UserId = userId };

                this._iwbResponse = new IwbService().GetWorkHistory(this._iwbRequest);

                context.Response.Write(JsonConvert.SerializeObject(
                new
                {
                    success = true,
                    message = "Work history have been fetched successfully.",
                    user = this._iwbResponse.User,
                    workHistories = this._iwbResponse.UserWorkHistories,
                }));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->getWorkHistory", TenantId, "An error has occurred while calling the function getJobs. Exception Message = " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(string.Format("[ERROR: {0}]", ex.Message));
            }
        }


        #endregion


        #region ----- Job -----

        private void saveJob(HttpContext context)
        {
            try
            {
                var job = JSONUtil.JsonToClass<Job>(context.Request.Form["job"]);
                job.InitiatorId = UserNum;
                job.CreatedDate = DateTime.Now;
                job.CallId = Guid.NewGuid().ToString("N");

                this._iwbRequest = new IwbRequest
                {
                    UserId = UserNum,
                    TenantId = TenantId,
                    Job = job,
                };

                this._iwbResponse = new IwbService().SaveJob(this._iwbRequest);
                switch (this._iwbResponse.Acknowledge)
                {
                    case AcknowledgeType.Success:
                        context.Response.Write(new JavaScriptSerializer().Serialize(
                                new
                                {
                                    success = true,
                                    message = "Job has been saved successfully.",
                                    data = this._iwbResponse.IwbId,
                                }));
                        break;
                    case AcknowledgeType.Failure:
                        context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, message = "Error", data = this._iwbResponse.Message }));
                        break;
                }
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Handler, "IwbHandler->saveJob", 0, "saveJob executed successfully."));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->saveJob", 0, "An error has occurred inside saveJob function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, data = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud." }));
            }
        }

        private void assignJob(HttpContext context)
        {
            try
            {
                var jobId = Convert.ToInt32(context.Request["jobId"]);
                var welderId = Convert.ToInt32(context.Request["userId"]);

                this._iwbRequest = new IwbRequest
                {
                    UserId = UserNum,
                    TenantId = TenantId,
                    JobId = jobId,
                    WelderId = welderId,
                    JobApplicationStatusId = 2,
                };

                this._iwbResponse = new IwbService().AssignJob(this._iwbRequest);
                switch (this._iwbResponse.Acknowledge)
                {
                    case AcknowledgeType.Success:
                        context.Response.Write(new JavaScriptSerializer().Serialize(
                                new
                                {
                                    success = true,
                                    message = "Job has been assigned successfully.",
                                    data = this._iwbResponse.LastSavedId,
                                }));
                        break;
                    case AcknowledgeType.Failure:
                        context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, message = "Error", data = this._iwbResponse.Message }));
                        break;
                }
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Handler, "IwbHandler->assignJob", 0, "saveJob executed successfully."));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->assignJob", 0, "An error has occurred inside assignJob function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, data = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud." }));
            }
        }

        private void getJobs(HttpContext context)
        {
            try
            {
                var pageIndex = Convert.ToInt32(context.Request["pageIndex"]);
                var pageSize = Convert.ToInt32(context.Request["pageSize"]);
                var jobName = Convert.ToString(context.Request["jobName"]);
                var jobWps = Convert.ToString(context.Request["jobWps"]);

                var welderId = Convert.ToInt32(context.Request["welderId"]);

                this._iwbRequest = new IwbRequest { TenantId = TenantId };
                this._iwbRequest.Criteria = new IwbCriteria
                {
                    WelderId = welderId,
                    JobStatuses = new List<IwbJobStatus>
                    {
                        IwbJobStatus.Initiated,
                        IwbJobStatus.InProgress,
                        IwbJobStatus.Completed
                    },
                    PageIndex = pageIndex,
                    PageSize = pageSize,
                    JobName = jobName,
                    JobWps = jobWps,
                    //CreatedBy = SessionHandler.UserInformation.UserType == 1 ? 0 : UserNum,
                    CreatedBy = (SessionHandler.UserInformation.UserType == 1 || SessionHandler.UserInformation.UserType == 8) ? 0 : UserNum,
                };

                this._iwbResponse = new IwbService().GetJobsByWhereClause(this._iwbRequest);

                context.Response.Write(JsonConvert.SerializeObject(
                    new
                    {
                        success = true,
                        message = "Jobs have been fetched successfully.",
                        data = this._iwbResponse.Jobs,
                        rowCount = this._iwbResponse.TotalRecords,
                        totalPages = this._iwbResponse.TotalPages,
                    }));

            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->getJobs", TenantId, "An error has occurred while calling the function getJobs. Exception Message = " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(string.Format("[ERROR: {0}]", ex.Message));
            }

        }
        private void updateJobStatus(HttpContext context)
        {
            try
            {
                var jobId = Convert.ToInt32(context.Request["jobId"]);
                var statusId = Convert.ToInt32(context.Request["statusId"]);


                this._iwbRequest = new IwbRequest
                {
                    UserId = UserNum,
                    TenantId = TenantId,
                    JobId = jobId,
                    JobStatusId = statusId,
                };

                this._iwbResponse = new IwbService().UpdateJobStatus(this._iwbRequest);
                switch (this._iwbResponse.Acknowledge)
                {
                    case AcknowledgeType.Success:
                        context.Response.Write(new JavaScriptSerializer().Serialize(
                                new
                                {
                                    success = true,
                                    message = "Job has been saved successfully.",
                                    data = this._iwbResponse.IwbId,
                                }));
                        break;
                    case AcknowledgeType.Failure:
                        context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, message = "Error", data = this._iwbResponse.Message }));
                        break;
                }
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Handler, "IwbHandler->updateJobStatus", 0, "updateJobStatus executed successfully."));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->updateJobStatus", 0, "An error has occurred inside updateJobStatus function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, data = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud." }));
            }
        }

        private void applyForJob(HttpContext context)
        {
            try
            {
                var jobApplicant = JSONUtil.JsonToClass<JobApplicant>(context.Request.Form["jobApplicant"]);
                //jobApplicant.ApplicantId = UserNum;
                jobApplicant.CurrentStatus = IwbJobApplicationStatus.Applied;
                jobApplicant.CreatedDate = DateTime.Now;

                this._iwbRequest = new IwbRequest
                {
                    UserId = UserNum,
                    TenantId = TenantId,
                    JobApplicant = jobApplicant,
                };

                this._iwbResponse = new IwbService().ApplyForJob(this._iwbRequest);
                switch (this._iwbResponse.Acknowledge)
                {
                    case AcknowledgeType.Success:
                        context.Response.Write(new JavaScriptSerializer().Serialize(
                                new
                                {
                                    success = true,
                                    message = "Job application has been submitted successfully.",
                                    data = this._iwbResponse.IwbId,
                                }));
                        break;
                    case AcknowledgeType.Failure:
                        context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, message = "Error", data = this._iwbResponse.Message }));
                        break;
                }
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Handler, "IwbHandler->applyForJob", 0, "applyForJob executed successfully."));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->applyForJob", 0, "An error has occurred inside applyForJob function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, data = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud." }));
            }
        }

        private void getJobApplicants(HttpContext context)
        {
            try
            {
                var jobId = Convert.ToInt32(context.Request["jobId"]);

                this._iwbRequest = new IwbRequest { TenantId = TenantId, JobId = jobId };
                this._iwbResponse = new IwbService().GetJobApplicants(this._iwbRequest);

                context.Response.Write(JsonConvert.SerializeObject(
                    new
                    {
                        success = true,
                        message = "Job Applicants have been fetched successfully.",
                        data = this._iwbResponse.JobApplicants,
                    }));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->getJobApplicants", TenantId, "An error has occurred while calling the function getJobApplicants. Exception Message = " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(string.Format("[ERROR: {0}]", ex.Message));
            }

        }

        private void updateJobApplicantStauts(HttpContext context)
        {
            try
            {
                var jobId = Convert.ToInt32(context.Request["jobId"]);
                var statusId = Convert.ToInt32(context.Request["statusId"]);
                var applicantId = Convert.ToInt32(context.Request["applicantId"]);


                this._iwbRequest = new IwbRequest
                {
                    UserId = UserNum,
                    TenantId = TenantId,
                    JobApplicant = new JobApplicant
                    {
                        CurrentStatus = (IwbJobApplicationStatus)statusId,
                        ApplicantId = applicantId,
                        JobId = jobId,
                        CreatedBy = UserNum
                    }
                };

                this._iwbResponse = new IwbService().UpdateJobApplicantStatus(this._iwbRequest);
                switch (this._iwbResponse.Acknowledge)
                {
                    case AcknowledgeType.Success:
                        context.Response.Write(new JavaScriptSerializer().Serialize(
                                new
                                {
                                    success = true,
                                    message = "Applicant status has been saved successfully.",
                                    data = this._iwbResponse.IwbId,
                                }));
                        break;
                    case AcknowledgeType.Failure:
                        context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, message = "Error", data = this._iwbResponse.Message }));
                        break;
                }
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Handler, "IwbHandler->updateJobApplicantStauts", 0, "updateJobApplicantStauts executed successfully."));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->updateJobApplicantStauts", 0, "An error has occurred inside updateJobApplicantStauts function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, data = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud." }));
            }
        }

        private void getWelders(HttpContext context)
        {
            try
            {
                var welderName = Convert.ToString(context.Request["welderName"]);
                var stencilNo = Convert.ToString(context.Request["stencilNo"]);
                var pageIndex = Convert.ToInt32(context.Request["pageIndex"]);
                var pageSize = Convert.ToInt32(context.Request["pageSize"]);
                this._iwbRequest = new IwbRequest
                {
                    TenantId = TenantId,
                    UserId = UserNum,
                    Criteria = new IwbCriteria
                    {
                        OrganizationId = SessionHandler.UserInformation.OrganizationId,
                        WelderId = SessionHandler.UserInformation.UserType == 8 ? UserNum : 0,
                        Name = welderName,
                        StencilNumber = stencilNo
                    }
                };

                //var response = new UserManagementService().GetAppUsers(TenantId);
                var response = new IwbService().GetWeldersByWhereClause(this._iwbRequest);

                if (response != null && response.Users != null)
                {
                    var welders = response.Users.Where(u => u.UserType == 8);
                    var totalCount = welders.Count();
                    var pagedWelders = welders
                    .Skip((pageIndex - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();
                    context.Response.Write(JsonConvert.SerializeObject(
                        new
                        {
                            success = true,
                            message = "Welders fetched successfully.",
                            rowCount = totalCount,
                            data = pagedWelders
                        }));
                }
                else
                {
                    context.Response.Write(JsonConvert.SerializeObject(
                        new
                        {
                            success = false,
                            message = "There is a problem occour while fetching welders.",
                        }));
                }
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->getWelders", 0, "An error has occurred inside getWelders function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, message = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud.", data = -1, }));
            }
        }

        #endregion


        #region ----- Organization -----


        private void getOrganizations(HttpContext context)
        {
            try
            {
                var orgName = Convert.ToString(context.Request["OrganizationName"]);
                var orgTypeId = Convert.ToInt32(context.Request["OrganizationTypeId"]);
                var pageIndex = Convert.ToInt32(context.Request["pageIndex"]);
                var pageSize = Convert.ToInt32(context.Request["pageSize"]);

                this._iwbRequest = new IwbRequest
                {
                    TenantId = TenantId,
                    UserId = UserNum,
                    Criteria = new IwbCriteria
                    {
                        OrganizationId = SessionHandler.UserInformation.OrganizationId,
                        Name = orgName,
                        OrganizationTypeId = orgTypeId,
                        //CreatedBy = SessionHandler.UserInformation.UserType == 1 ? 0 : UserNum,
                    }
                };

                var response = new IwbService().GetAllOrganizations(this._iwbRequest);
                if (response != null)
                {
                    // Pagination logic: Apply pageIndex and pageSize
                    var pagedOrganizations = response.Organizations
                        .Skip((pageIndex - 1) * pageSize)  // Skip records for previous pages
                        .Take(pageSize)                   // Take only 'pageSize' records
                        .ToList();

                    // Get total count for pagination (no filtering by page here)
                    var totalCount = response.Organizations.Count;

                    // Return the response with pagination information
                    context.Response.Write(JsonConvert.SerializeObject(
                        new
                        {
                            success = true,
                            message = "Organizations fetched successfully.",
                            data = pagedOrganizations,
                            rowCount = totalCount // Total count for pagination purposes
                        }));
                }
                else
                {
                    context.Response.Write(JsonConvert.SerializeObject(
                        new
                        {
                            success = false,
                            message = "There is a problem occour while fetching organizations.",
                            data = new List<Organization>(),
                            rowCount = 0
                        }));
                }
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->getOrganizations", 0, "An error has occurred inside getOrganizations function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, message = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud.", data = -1, }));
            }
        }


        private void saveOrganization(HttpContext context)
        {
            try
            {
                var organization = JSONUtil.JsonToClass<Organization>(context.Request.Form["organization"]);
                var orgPassword = Convert.ToString(context.Request["password"]);

                organization.CreatedDate = DateTime.Now;
                organization.CreatedBy = UserNum;

                this._iwbRequest = new IwbRequest
                {
                    UserId = UserNum,
                    TenantId = TenantId,
                    Organization = organization,
                    OrganizationAccountPassword = orgPassword
                };

                this._iwbResponse = new IwbService().SaveOrganization(this._iwbRequest);

                //New USer
                int uType = 6;
                int roleId = 0;
                switch (organization.Type)
                {
                    case OrganizationType.Insurance:
                        uType = 5;
                        roleId = 5;
                        break;
                    case OrganizationType.Owner:
                        uType = 6;
                        roleId = 6;
                        break;
                    case OrganizationType.Contractor:
                        uType = 7;
                        roleId = 7;
                        break;
                    case OrganizationType.Testing:
                        uType = 9;
                        roleId = 9;
                        break;
                    default:
                        break;
                }
                var user = new User
                {
                    OrganizationId = this._iwbResponse.LastSavedId,
                    UserID = organization.Email,
                    UserEmail = organization.Email,
                    UserName = organization.Name,
                    UserPW = orgPassword,
                    UserPhone = organization.Phone,
                    UserType = uType,
                    RoleId = roleId,
                    IsIwbUser = true,

                    GroupNum = -1,
                    CreatedBy = UserNum,
                    //City = "",
                    //State = "",
                    //SocialSecurityNumber = "",
                };

                var umResponse = new UserManagementService().CreateAppUserAccount(new UserManagementRequest { User = user });

                switch (this._iwbResponse.Acknowledge)
                {
                    case AcknowledgeType.Success:
                        context.Response.Write(new JavaScriptSerializer().Serialize(
                                new
                                {
                                    success = true,
                                    message = "Organization has been saved successfully.",
                                    data = this._iwbResponse.LastSavedId,
                                    additionaldata = user,
                                }));
                        break;
                    case AcknowledgeType.Failure:
                        context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, message = "Error", data = this._iwbResponse.Message }));
                        break;
                }
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Handler, "IwbHandler->saveOrganization", 0, "saveOrganization executed successfully."));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->saveOrganization", 0, "An error has occurred inside saveOrganization function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, data = ex.Message == "user id is not unique..." ? ex.Message : SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud." }));
            }
        }

        private void saveLocation(HttpContext context)
        {
            try
            {
                var location = JSONUtil.JsonToClass<OrganizationLocation>(context.Request.Form["location"]);
                location.CreatedDate = DateTime.Now;
                //location.OrganizationId = 1;

                this._iwbRequest = new IwbRequest
                {
                    UserId = UserNum,
                    TenantId = TenantId,
                    Location = location,
                };
                this._iwbRequest.TenantId = 0;

                this._iwbResponse = new IwbService().SaveOrganizationLocation(this._iwbRequest);
                switch (this._iwbResponse.Acknowledge)
                {
                    case AcknowledgeType.Success:
                        context.Response.Write(new JavaScriptSerializer().Serialize(
                                new
                                {
                                    success = true,
                                    message = "Location has been saved successfully.",
                                    data = this._iwbResponse.LastSavedId,
                                }));
                        break;
                    case AcknowledgeType.Failure:
                        context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, message = "Error", data = this._iwbResponse.Message }));
                        break;
                }
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Handler, "IwbHandler->saveLocation", 0, "saveLocation executed successfully."));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->saveLocation", 0, "An error has occurred inside saveLocation function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, data = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud." }));
            }
        }


        private void getLocations(HttpContext context)
        {
            try
            {
                var organizationId = Convert.ToInt32(context.Request["organizationId"]);

                this._iwbResponse = new IwbService().GetLocationsByOrganizationId(organizationId, TenantId);

                context.Response.Write(JsonConvert.SerializeObject(
                new
                {
                    success = true,
                    message = "Locations have been fetched successfully.",
                    data = this._iwbResponse.Locations,
                }));

                //Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Handler, "IwbHandler->getLocations", TenantId, "getLocations executed successfully."));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->getLocations", TenantId, "An error has occurred while calling the function getLocations. Exception Message = " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(string.Format("[ERROR: {0}]", ex.Message));
            }

        }


        private void deleteLocation(HttpContext context)
        {
            try
            {
                var locationId = Convert.ToInt32(context.Request["id"]);
                this._iwbRequest = new IwbRequest { UserId = UserNum, ModifiedDate = DateTime.Now, TenantId = TenantId, LocationId = locationId };

                this._iwbResponse = new IwbService().DeleteLocation(this._iwbRequest);
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = true, data = this._iwbResponse.RowsAffected }));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->deleteLocation", SessionHandler.UserInformation.TenantId, "An exception has occurred inside deleteLocation function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, data = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud." }));
            }
        }


        #endregion
        private void getJobsByWelder(HttpContext context)
        {
            try
            {
                var welderId = Convert.ToInt32(context.Request["welderId"]);

                // Create request object
                this._iwbRequest = new IwbRequest
                {
                    TenantId = TenantId,
                    UserId = welderId
                };

                // Call your service method that fetches jobs
                var response = new IwbService().GetJobsByWelder(this._iwbRequest);

                if (response != null && response.JobWelders != null)
                {
                    context.Response.Write(new JavaScriptSerializer().Serialize(
                        new
                        {
                            success = true,
                            jobs = response.JobWelders
                        }));
                }
                else
                {
                    context.Response.Write(new JavaScriptSerializer().Serialize(
                        new
                        {
                            success = false,
                            message = "No jobs found for the welder.",
                            data = new List<JobWelder>()
                        }));
                }
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->deleteLocation", SessionHandler.UserInformation.TenantId, "An exception has occurred inside deleteLocation function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, data = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud." }));
            }
        }

        private void getUserDataByUser(HttpContext context)
        {
            try
            {
                var userId = Convert.ToInt32(context.Request["userId"]);

                this._iwbRequest = new IwbRequest
                {
                    TenantId = TenantId,
                    UserId = userId
                };

                var response = new IwbService().GetUserDataByUser(this._iwbRequest);

                if (response != null && response.UserInfoData != null)
                {
                    context.Response.Write(new JavaScriptSerializer().Serialize(
                        new
                        {
                            success = true,
                            user = response.UserInfoData
                        }));
                }
                else
                {
                    context.Response.Write(new JavaScriptSerializer().Serialize(
                        new
                        {
                            success = false,
                            message = "User data not found.",
                            user = new object()
                        }));
                }
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(
                    Originator.Handler, "IwbHandler->getUserDataByUser", SessionHandler.UserInformation.TenantId,
                    "An exception has occurred inside getUserDataByUser function. Exception Message -> " + ex.Message,
                    ex.StackTrace, 0, string.Empty, 0
                ));

                context.Response.Write(new JavaScriptSerializer().Serialize(new
                {
                    success = false,
                    message = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud."
                }));
            }
        }

        //private void uploadWpq(HttpContext context)
        //{
        //    try
        //    {
        //        var wpq = JSONUtil.JsonToClass<Wpq>(context.Request.Form["wpq"]);
        //        wpq.CreatedDate = DateTime.Now;
        //        wpq.CreatedBy = UserNum;

        //        this._iwbRequest = new IwbRequest
        //        {
        //            UserId = UserNum,
        //            TenantId = TenantId,
        //            Wpq = wpq,
        //        };

        //        this._iwbResponse = new IwbService().UploadWPQ(this._iwbRequest);
        //        switch (this._iwbResponse.Acknowledge)
        //        {
        //            case AcknowledgeType.Success:
        //                context.Response.Write(new JavaScriptSerializer().Serialize(
        //                        new
        //                        {
        //                            success = true,
        //                            message = "WPQ has been uploaded successfully.",
        //                            data = this._iwbResponse.IwbId,
        //                        }));
        //                break;
        //            case AcknowledgeType.Failure:
        //                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, message = "Error", data = this._iwbResponse.Message }));
        //                break;
        //        }
        //        Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Handler, "IwbHandler->uploadWPQ", 0, "uploadWPQ executed successfully."));
        //    }
        //    catch (Exception ex)
        //    {
        //        Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->saveWPQ", 0, "An error has occurred inside saveWPQ function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
        //        context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, data = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud." }));
        //    }
        //}

        private void uploadWpq(HttpContext context)
        {
            try
            {
                var wpqJson = context.Request.Form["wpq"];
                var wpq = JSONUtil.JsonToClass<Wpq>(wpqJson);
                wpq.CreatedDate = DateTime.Now;
                wpq.CreatedBy = UserNum;

                var _postedFile = context.Request.Files["file"];
                if (_postedFile != null && _postedFile.ContentLength > 0)
                {
                    string fileName = _postedFile.FileName;
                    string _uploadFilePath = Path.Combine(_iwbOutputReportsPdfPath, fileName);
                    string newFileName = fileName;

                    // Ensure unique filename if a file already exists
                    if (File.Exists(_uploadFilePath))
                    {
                        int counter = 1;
                        while (File.Exists(_uploadFilePath))
                        {
                            newFileName = $"{counter}-{fileName}";
                            _uploadFilePath = Path.Combine(_iwbOutputReportsPdfPath, newFileName);
                            counter++;
                        }

                        fileName = newFileName;
                    }

                    // Save the file
                    _postedFile.SaveAs(_uploadFilePath);
                }


                // Business logic
                this._iwbRequest = new IwbRequest
                {
                    UserId = UserNum,
                    TenantId = TenantId,
                    Wpq = wpq
                };

                this._iwbResponse = new IwbService().UploadWPQ(this._iwbRequest);

                var serializer = new JavaScriptSerializer();
                switch (this._iwbResponse.Acknowledge)
                {
                    case AcknowledgeType.Success:
                        context.Response.Write(serializer.Serialize(new
                        {
                            success = true,
                            message = "WPQ has been uploaded successfully.",
                            data = this._iwbResponse.IwbId
                        }));
                        break;

                    case AcknowledgeType.Failure:
                        context.Response.Write(serializer.Serialize(new
                        {
                            success = false,
                            message = "Error",
                            data = this._iwbResponse.Message
                        }));
                        break;
                }

                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Handler, "IwbHandler->uploadWPQ", 0, "uploadWPQ executed successfully."));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(
                    Originator.Handler,
                    "IwbHandler->uploadWPQ",
                    0,
                    "An error has occurred inside uploadWPQ. Exception: " + ex.Message,
                    ex.StackTrace,
                    0,
                    string.Empty,
                    0));

                context.Response.Write(new JavaScriptSerializer().Serialize(new
                {
                    success = false,
                    data = SessionHandler.CurrentLanguage == "en-US"
                        ? "There is a problem processing your request."
                        : "Hay un problema al procesar su solicitud."
                }));
            }
        }



        #region ----- WPQ -----


        private void saveWPQ(HttpContext context)
        {
            try
            {
                var wpq = JSONUtil.JsonToClass<Wpq>(context.Request.Form["wpq"]);
                wpq.CreatedDate = DateTime.Now;
                wpq.CreatedBy = UserNum;

                this._iwbRequest = new IwbRequest
                {
                    UserId = UserNum,
                    TenantId = TenantId,
                    Wpq = wpq,
                };

                this._iwbResponse = new IwbService().SaveWPQ(this._iwbRequest);
                switch (this._iwbResponse.Acknowledge)
                {
                    case AcknowledgeType.Success:
                        context.Response.Write(new JavaScriptSerializer().Serialize(
                                new
                                {
                                    success = true,
                                    message = "WPQ has been saved successfully.",
                                    data = this._iwbResponse.IwbId,
                                }));
                        break;
                    case AcknowledgeType.Failure:
                        context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, message = "Error", data = this._iwbResponse.Message }));
                        break;
                }
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Handler, "IwbHandler->saveWPQ", 0, "saveWPQ executed successfully."));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->saveWPQ", 0, "An error has occurred inside saveWPQ function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, data = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud." }));
            }
        }

        private void getUserWPQs(HttpContext context)
        {
            try
            {
                var welderId = Convert.ToInt32(context.Request["userId"]);
                this._iwbRequest = new IwbRequest { TenantId = TenantId, UserId = welderId };
                var response = new IwbService().GetWPQsByUserId(this._iwbRequest);
                if (response != null)
                {
                    context.Response.Write(new JavaScriptSerializer().Serialize(
                                    new
                                    {
                                        success = true,
                                        message = "WPQs fetched successfully.",
                                        data = response.WPQs,
                                    }));
                }
                else
                {
                    context.Response.Write(new JavaScriptSerializer().Serialize(
                                    new
                                    {
                                        success = false,
                                        message = "There is a problem occour while fetching user wpqs.",
                                        data = new List<Wpq>(),
                                    }));
                }
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->getUserWPQs", 0, "An error has occurred inside getUserWPQs function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, message = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud.", data = -1, }));
            }
        }

        #endregion



        #region ----- Document -----

        private void getDocuments(HttpContext context)
        {
            try
            {
                var pageIndex = Convert.ToInt32(context.Request["pageIndex"]);
                var pageSize = Convert.ToInt32(context.Request["pageSize"]);
                var docName = Convert.ToString(context.Request["docName"]);
                var docTypeId = Convert.ToInt32(context.Request["documentTypeId"]);
                var docAlias = Convert.ToString(context.Request["docAlias"]);

                var docTypes = new List<IwbDocumentType>();
                if (docTypeId == 0)
                {
                    docTypes.Add(IwbDocumentType.MTR);
                    //docTypes.Add(IwbDocumentType.WPS);
                    //docTypes.Add(IwbDocumentType.PQR);
                }
                else
                    docTypes.Add((IwbDocumentType)Enum.Parse(typeof(IwbDocumentType), Convert.ToString(docTypeId)));

                this._iwbRequest = new IwbRequest
                {
                    TenantId = TenantId,
                    UserId = UserNum,
                    Criteria = new IwbCriteria
                    {
                        DocumentTypes = docTypes,
                        OrganizationId = SessionHandler.UserInformation.OrganizationId,
                        Name = docName,
                        DocumentTypeId = docTypeId,
                        Alias = docAlias,
                        PageIndex = pageIndex,
                        PageSize = pageSize,
                        CreatedBy = SessionHandler.UserInformation.UserType == 1 ? 0 : UserNum,
                    }
                };

                this._iwbResponse = new IwbService().GetDocumentsByWhereClause(this._iwbRequest);

                for (int i = 0; i < this._iwbResponse.Documents.Count; i++)
                {
                    try
                    {
                        this._iwbResponse.Documents[i].SignLink = new RevSignHelper().getRevSignatureURL(this._iwbResponse.Documents[i].SignDocumentId,
                            this._iwbResponse.Documents[i].OwnerEmail, SessionHandler.UserInformation.TenantId, false, "");
                    }
                    catch (Exception)
                    {

                    }
                }

                context.Response.Write(JsonConvert.SerializeObject(
                    new
                    {
                        success = true,
                        message = "Documents have been fetched successfully.",
                        data = this._iwbResponse.Documents,
                        rowCount = this._iwbResponse.TotalRecords,
                        totalPages = this._iwbResponse.TotalPages,
                    }));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->getDocuments", TenantId, "An error has occurred while calling the function getLocations. Exception Message = " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(string.Format("[ERROR: {0}]", ex.Message));
            }

        }

        private void getDocumentById(HttpContext context)
        {
            try
            {
                context.Response.ContentType = "application/json";

                IwbRequest request = new IwbRequest
                {
                    TenantId = Convert.ToInt32(context.Request["tenantId"]),
                    DocumentId = Convert.ToInt32(context.Request["documentId"])
                };

                IwbResponse response = new IwbService().GetDocumentById(request);

                string jsonResponse = JsonConvert.SerializeObject(new
                {
                    Acknowledge = response.Acknowledge,
                    IwbId = response.IwbId,
                    Document = response.Document
                });

                context.Response.Write(jsonResponse);
            }
            catch (Exception ex)
            {
                RevAuditLogger.WriteException(Originator.IWB, "getDocumentById", 0, ex.Message, ex.StackTrace, 0, string.Empty, 0);
                var errorResponse = new IwbResponse
                {
                    Acknowledge = AcknowledgeType.Failure,
                    Message = "Error retrieving document by ID: " + ex.Message
                };
                context.Response.Write(JsonConvert.SerializeObject(errorResponse));
            }
        }


        private void saveDocument(HttpContext context)
        {
            JToken jsonResponseInfo = null;
            try
            {
                var document = JSONUtil.JsonToClass<IwbDocument>(context.Request.Form["doc"]);
                dynamic modifiedJsonData = JsonConvert.DeserializeObject(context.Request.Form["modifiedJsonData"]);
                dynamic orignalJsonData = JsonConvert.DeserializeObject(context.Request.Form["orignalJsonData"]);
                dynamic validationReportData = JsonConvert.DeserializeObject(context.Request.Form["validationReportJsonData"]);

                bool isUpdate = document.Id > 0;
                if (context.Request.Files.Count > 0)
                {
                    uploadDocument(context);
                    jsonResponseInfo = processDocumentForOCR(context, document, modifiedJsonData);
                }

                document.CreatedDate = DateTime.Now;
                document.CreatedBy = UserNum;
                document.UploadDate = DateTime.Now;
                document.OrganizationId = SessionHandler.UserInformation.OrganizationId;

                document.FileName = _postedFile.FileName;
                string safeFileName = document.FileName.Replace("#", "_");
                document.FileSize = _postedFile.ContentLength;
                document.FileExtension = Path.GetExtension(_postedFile.FileName);
                document.FilePath = _uploadFilePath;
                document.OutputFileName = jsonResponseInfo == null ? null : string.Format("{0}{1}/{2}/{3}/{4}", SiteConfig.WebURL, "SystemUploadedFiles", "Iwb", "OutputReports", _outputFileName);
                document.JsonFileName = jsonResponseInfo == null ? null : _jsonFileName;
                //document.OCRResult = JsonConvert.SerializeObject(jsonResponseInfo);
                document.OCRResult = JsonConvert.SerializeObject(validationReportData);
                //document.IsSaved = false;
                document.IsSaved = jsonResponseInfo == null ? false : true;

                this._iwbRequest = new IwbRequest
                {
                    UserId = UserNum,
                    TenantId = TenantId,
                    Document = document,
                };

                switch (document.Type)
                {
                    case IwbDocumentType.MTR:
                        var mtr = DeserializeAIResponseMTR();
                        document.MTR = mtr;
                        this._iwbResponse = new IwbService().SaveScannedDocument(this._iwbRequest);
                        break;
                    case IwbDocumentType.WPS:
                        var wps = DeserializeAIResponseWPS();
                        document.WPS = wps;
                        this._iwbResponse = new IwbService().SaveScannedDocument(this._iwbRequest);
                        break;
                    case IwbDocumentType.PQR:
                    default:
                        this._iwbResponse = new IwbService().SaveDocument(this._iwbRequest);
                        break;
                }

                switch (this._iwbResponse.Acknowledge)
                {
                    case AcknowledgeType.Success:
                        context.Response.Write(new JavaScriptSerializer().Serialize(
                                new
                                {
                                    success = true,
                                    message = isUpdate ? "Document has been updated successfully." : "Document has been saved successfully.",
                                    data = new
                                    {
                                        iwbDocumentid = this._iwbResponse.IwbId,
                                        pdfFileName = Path.GetFileName(_outputFileName),
                                        fileType = document.Type.ToString(),
                                        ocrResult = JsonConvert.SerializeObject(jsonResponseInfo),
                                        outputFile = document.OutputFileName
                                    }
                                }));
                        break;
                    case AcknowledgeType.Failure:
                        context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, message = "Error", data = this._iwbResponse.Message }));
                        break;
                    default:
                        context.Response.Write(new JavaScriptSerializer().Serialize(
                                new
                                {
                                    success = true,
                                    message = "Document has been saved successfully.",
                                    data = new
                                    {
                                        iwbDocumentid = this._iwbResponse.IwbId,
                                        pdfFileName = Path.GetFileName(_iwbDocumentPath),
                                        ocrResult = JsonConvert.SerializeObject(jsonResponseInfo),
                                        outputFile = document.OutputFileName
                                    },
                                }));
                        break;
                }
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Handler, "IwbHandler->saveDocument", 0, "saveDocument executed successfully."));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->saveDocument", 0, "An error has occurred inside saveDocument function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, data = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud." }));
            }
        }

        private IwbMTR DeserializeAIResponseMTR()
        {
            try
            {
                var mtrdocument = new IwbMTR();
                //_jsonFileName = "MTR Example - E22312-9.json";
                var mtrSections = new List<IwbMTRSection>();
                string jsonFileContents = "";
                var jsonFileNamePath = Path.Combine(_iwbJsonResponsePath, _jsonFileName);
                if (File.Exists(jsonFileNamePath))
                {
                    jsonFileContents = File.ReadAllText(jsonFileNamePath);

                    dynamic result = JsonConvert.DeserializeObject(jsonFileContents);

                    JToken tokens = JObject.Parse(jsonFileContents);

                    foreach (var section in tokens)
                    {
                        Console.WriteLine($"Section: {section.Path.ToString()}");

                        if (section.Path == "HeaderInformation")
                        {
                            var headerValues = ((JProperty)section).Value;
                            var mtrHeadInfo = JsonConvert.DeserializeObject<HeaderInformation>(headerValues.ToString());
                            mtrdocument.ASME = string.Join(",", mtrHeadInfo.StandardCodes.ASME);
                            mtrdocument.ASTM = string.Join(",", mtrHeadInfo.StandardCodes.ASTM);
                            mtrdocument.Uns = string.Join(",", mtrHeadInfo.UNS);
                            mtrdocument.Grade = string.Join(",", mtrHeadInfo.GRADE);
                        }
                        else if (section.Path == "Sections")
                        {
                            try
                            {
                                //var sectionsJson = ((JProperty)section).Value;
                                var sections = result.Sections;
                                mtrSections = JsonConvert.DeserializeObject<List<IwbMTRSection>>(sections.ToString());
                            }
                            catch (Exception ex)
                            {
                                throw;
                            }
                        }
                        else if (section.Path == "ResponeInformation")
                        {
                            var responseValues = ((JProperty)section).Value.ToString();
                            dynamic responseInfo = JsonConvert.DeserializeObject(responseValues);
                            mtrdocument.ComplianceStatus = responseInfo.CompliantStatus.ToString();
                        }
                    }
                }
                mtrdocument.MtrSections = mtrSections;
                return mtrdocument;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        private IwbWPS DeserializeAIResponseWPS()
        {
            var wpsdocument = new IwbWPS();
            string jsonFileContents = "";
            var jsonFileNamePath = Path.Combine(_iwbJsonResponsePath, _jsonFileName);
            if (File.Exists(jsonFileNamePath))
            {
                jsonFileContents = File.ReadAllText(jsonFileNamePath);
                //dynamic jsonContents = JsonConvert.DeserializeObject(jsonFileContents);

                wpsdocument = JsonConvert.DeserializeObject<IwbWPS>(jsonFileContents);
            }
            return wpsdocument;
        }

        private void uploadDocument(HttpContext context)
        {
            try
            {
                _fileType = (IwbDocumentType)Convert.ToInt32(context.Request.Form["Type"]);
                switch (_fileType)
                {
                    case IwbDocumentType.MTR:
                        _iwbDocumentPath = Path.Combine(_iwbDocumentPath, "MTR");
                        break;
                    case IwbDocumentType.WPQ:
                        _iwbDocumentPath = Path.Combine(_iwbDocumentPath, "WPQ");
                        break;
                    case IwbDocumentType.WPS:
                        _iwbDocumentPath = Path.Combine(_iwbDocumentPath, "WPS");
                        break;
                    case IwbDocumentType.PQR:
                        _iwbDocumentPath = Path.Combine(_iwbDocumentPath, "PQR");
                        break;
                    default:
                        break;
                }
                if (!Directory.Exists(_iwbDocumentPath)) Directory.CreateDirectory(_iwbDocumentPath);


                if (context.Request.Files.Count > 0)
                    _postedFile = context.Request.Files[0];




                //savepath = tempPath;
                string fileName = _postedFile.FileName;
                _uploadFilePath = Path.Combine(_iwbDocumentPath, fileName);
                string newFileName = fileName;

                if (File.Exists(_uploadFilePath))
                {
                    int counter = 1;
                    while (File.Exists(_uploadFilePath))
                    {
                        newFileName = $"{counter}-{fileName}";
                        _uploadFilePath = Path.Combine(_iwbDocumentPath, newFileName);

                        counter++;
                    }

                    fileName = newFileName;
                }

                _postedFile.SaveAs(_uploadFilePath);

                //var response = new IwbService().SaveDocument(tId, fileName, filePath);
                //context.Response.StatusCode = 200;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->ProcessRequest", base.TenantId, "An error has occurred while calling the function ProcessRequest. " + " Exception Message = " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud.");
            }
        }


        private JToken processDocumentForOCR(HttpContext context, IwbDocument document, dynamic modifiedData)
        {
            JToken jsonResponseInfo = null;
            try
            {
                _postedFile = context.Request.Files[0];
                byte[] bytes = null;

                var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(_postedFile.FileName);

                using (var stream = new MemoryStream())
                {
                    _postedFile.InputStream.CopyTo(stream);
                    bytes = stream.ToArray();
                }

                var client = new RestClient(_aiApiUrl + "/process_pdf");
                var request = new RestRequest(Method.POST);
                request.AddFile("file", bytes, _postedFile.FileName, "application/pdf");
                request.AddParameter("docType", document.Type);
                request.Timeout = _aiTimeoutSeconds * 1000;

                var response = client.Execute(request);
                // Generate timestamp (e.g., for uniqueness)
                var tStamp = DateTime.Now.ToString("yyyyMMddHHmmss");
                _outputFileName = fileNameWithoutExtension + tStamp + ".pdf";
                _jsonFileName = fileNameWithoutExtension + ".json";

                try
                {

                    Task.Run(() => RevAuditLogger.WriteInformation(Originator.Handler, "IwbHandler->TryAI", 0, response.Content));

                    AIResponse aiRequest = new AIResponse();
                    aiRequest.RequestUrl = _aiApiUrl + "/process_pdf";
                    aiRequest.ResponseJson = response.Content;
                    if (response.StatusCode != HttpStatusCode.OK)
                    {
                        aiRequest.IsError = true;
                        aiRequest.ErrorDescription = response.Content;
                    }
                    else
                    {
                        aiRequest.IsError = false;
                        aiRequest.ErrorDescription = "";
                    }

                    this._iwbRequest = new IwbRequest();
                    this._iwbRequest.Data = aiRequest;
                    this._iwbResponse = new IwbService().SaveAIResponse(this._iwbRequest);
                }
                catch (Exception)
                {

                }

                if (response.StatusCode == HttpStatusCode.OK)
                {
                    dynamic jsonResponse = JsonConvert.DeserializeObject(response.Content);

                    if (!Directory.Exists(_iwbJsonResponsePath)) Directory.CreateDirectory(_iwbJsonResponsePath);

                    var jsonFileNamePath = Path.Combine(_iwbJsonResponsePath, _jsonFileName);
                    string newFileName = _jsonFileName;

                    if (File.Exists(jsonFileNamePath))
                    {
                        int counter = 1;
                        while (File.Exists(jsonFileNamePath))
                        {
                            newFileName = $"{counter}-{_jsonFileName}";
                            jsonFileNamePath = Path.Combine(_iwbJsonResponsePath, newFileName);

                            counter++;
                        }
                        _jsonFileName = newFileName;
                    }
                    using (StreamWriter file = File.CreateText(jsonFileNamePath))
                    {
                        JsonSerializer serializer = new JsonSerializer();
                        serializer.Serialize(file, jsonResponse);
                    }
                    if (document.Type == IwbDocumentType.MTR)
                    {
                        jsonResponseInfo = CreateMTRJsonPdf(jsonResponse, _outputFileName, bytes, modifiedData);
                    }
                }
                else
                {
                    Console.WriteLine($"Error: {response.ErrorMessage}");
                    Console.WriteLine(response.StatusCode + "\n" + response.StatusDescription);
                }
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->processDocumentForOCR", base.TenantId, "An error has occurred while calling the function processDocumentForOCR. " + " Exception Message = " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud.");
            }

            return jsonResponseInfo;
        }
        private JToken CreateMTRJsonPdf(dynamic jsonResponse, string outputFileName, byte[] originalFileBytes, dynamic modifiedData)
        {
            JToken jsonResponseInfo = null;

            try
            {
                // Combine paths and ensure folder exists
                var jsonFileNamePath = Path.Combine(_iwbOutputReportsPdfPath, outputFileName);
                var generatedPdfPath = jsonFileNamePath;
                Directory.CreateDirectory(_iwbOutputReportsPdfPath); // Ensure the folder exists

                // Create the PDF
                using (FileStream stream = new FileStream(generatedPdfPath, FileMode.Create))
                {
                    iTextSharp.text.Document document = new iTextSharp.text.Document(PageSize.A4, 36, 36, 36, 36);
                    PdfWriter writer = PdfWriter.GetInstance(document, stream);

                    document.Open();

                    JToken jsonValidationResult = null;

                    try
                    {
                        jsonValidationResult = jsonResponse["ValidationResult"];
                    }
                    catch (Exception) { }

                    JToken jsonHeaderInfo = null;

                    try
                    {
                        jsonHeaderInfo = jsonValidationResult["HeaderInformation"];
                    }
                    catch (Exception) { }

                    try
                    {
                        jsonResponseInfo = jsonValidationResult["ResponeInformation"];
                    }
                    catch (Exception) { }

                    try
                    {
                        jsonResponse.Sections = jsonValidationResult["Sections"];
                    }
                    catch (Exception) { }

                    // Add content to the PDF
                    AddCoverPage(document, jsonHeaderInfo, modifiedData);

                    if (jsonResponse.Sections != null)
                    {
                        foreach (var section in jsonResponse.Sections)
                        {
                            switch (section.Name.ToString())
                            {
                                case "CHEMICAL":
                                    AddChemicalCompositionsSection(document, section.Elements, modifiedData);
                                    break;
                                case "MECHANICAL":
                                    AddMechanicalTestsSection(document, section.Elements, modifiedData);
                                    break;
                                case "HEAT_TREATMENT":
                                    AddHeatTreatmentSection(document, writer, section.Elements, modifiedData);
                                    break;
                                default:
                                    break;
                            }
                        }
                    }

                    // Add a blank page at the end
                    document.NewPage(); // Creates a new blank page
                    document.Add(new Paragraph(" ")); // Optional: Ensure the page is entirely blank by adding an empty space

                    document.Close();
                    writer.Close();
                }

                try
                {
                    string mergedPdfPath = Path.Combine(_iwbOutputReportsPdfPath, "Merged_" + outputFileName);

                    using (var originalReader = new PdfReader(originalFileBytes))
                    using (var generatedReader = new PdfReader(generatedPdfPath))
                    using (var fs = new FileStream(mergedPdfPath, FileMode.Create))
                    using (var mergedDocument = new Document())
                    using (var copy = new PdfCopy(mergedDocument, fs))
                    {
                        mergedDocument.Open();

                        for (int i = 1; i <= originalReader.NumberOfPages; i++)
                        {
                            copy.AddPage(copy.GetImportedPage(originalReader, i));
                        }

                        for (int i = 1; i <= generatedReader.NumberOfPages; i++)
                        {
                            copy.AddPage(copy.GetImportedPage(generatedReader, i));
                        }

                        mergedDocument.Close();
                    }

                    File.Delete(generatedPdfPath);
                    File.Move(mergedPdfPath, generatedPdfPath);
                }
                catch (Exception ex)
                {
                    Console.WriteLine("Failed to merge original PDF: " + ex.Message);
                }
            }
            catch (Exception ex)
            {
                // Log or handle the exception
                Console.WriteLine($"Error creating PDF: {ex.Message}");
            }

            return jsonResponseInfo;
        }

        //private void AddCoverPage(Document document, JToken jsonHeaderInfo)
        //{
        //    var titleFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 24);
        //    var subtitleFont = FontFactory.GetFont(FontFactory.HELVETICA, 16);
        //    var spacer = new Paragraph("\n\n");

        //    document.Add(new Paragraph("Material Test Report (MTR)", titleFont) { Alignment = Element.ALIGN_CENTER });
        //    document.Add(spacer);
        //    document.Add(new Paragraph("Generated by MTR AI System", subtitleFont) { Alignment = Element.ALIGN_CENTER });
        //    document.Add(spacer);
        //    document.Add(new Paragraph($"Date: {DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}", subtitleFont) { Alignment = Element.ALIGN_CENTER });

        //    if (jsonHeaderInfo != null)
        //    {
        //        JObject standardCodes = (JObject)jsonHeaderInfo["StandardCodes"];
        //        List<string> standardCodeKeys = new List<string>(standardCodes.Properties().Select(p => p.Name));

        //        if (standardCodeKeys.Count > 0)
        //        {
        //            document.Add(spacer);
        //            document.Add(new Paragraph("Standard Codes: " + string.Join(", ", standardCodeKeys), subtitleFont) { Alignment = Element.ALIGN_CENTER });

        //            foreach (var item in standardCodes)
        //            {
        //                document.Add(new Paragraph($"{item.Key}: {string.Join(", ", item.Value)}", subtitleFont) { Alignment = Element.ALIGN_CENTER });
        //            }

        //            JObject jsonObject = (JObject)jsonHeaderInfo;
        //            if (jsonObject != null)
        //            {
        //                foreach (var property in jsonObject.Properties())
        //                {
        //                    if (property.Name != "StandardCodes")
        //                    {
        //                        if (property.Name == "CodebookInfo")
        //                        {
        //                            document.Add(new Paragraph($"{property.Name}: {property.Value}", subtitleFont) { Alignment = Element.ALIGN_CENTER });
        //                        }
        //                        else
        //                        {
        //                            document.Add(new Paragraph($"{property.Name}: {string.Join(", ", property.Value)}", subtitleFont) { Alignment = Element.ALIGN_CENTER });
        //                        }
        //                    }
        //                }
        //            }
        //        };
        //    }


        //    document.NewPage();
        //}

        private void AddCoverPage(Document document, JToken jsonHeaderInfo, dynamic modifiedData)
        {
            var titleFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 24);
            var subtitleFont = FontFactory.GetFont(FontFactory.HELVETICA, 16);
            var spacer = new Paragraph("\n");

            document.Add(new Paragraph("Material Test Report (MTR)", titleFont) { Alignment = Element.ALIGN_CENTER });
            document.Add(spacer);
            document.Add(new Paragraph("Generated by MTR AI System", subtitleFont) { Alignment = Element.ALIGN_CENTER });
            document.Add(spacer);
            document.Add(new Paragraph($"Date: {DateTime.Now:yyyy-MM-dd HH:mm:ss}", subtitleFont) { Alignment = Element.ALIGN_CENTER });

            if (jsonHeaderInfo != null)
            {
                JObject standardCodes = (JObject)jsonHeaderInfo["StandardCodes"];
                List<string> standardCodeKeys = new List<string>(standardCodes.Properties().Select(p => p.Name));

                if (standardCodeKeys.Count > 0)
                {
                    document.Add(spacer);
                    document.Add(new Paragraph("Standard Codes: " + string.Join(", ", standardCodeKeys), subtitleFont) { Alignment = Element.ALIGN_CENTER });

                    foreach (var item in standardCodes)
                    {
                        document.Add(new Paragraph($"{item.Key}: {string.Join(", ", item.Value)}", subtitleFont) { Alignment = Element.ALIGN_CENTER });
                    }
                }

                // Get arrays
                var codeInfo = jsonHeaderInfo["CodeInfo"] as JArray;
                var codebookInfo = jsonHeaderInfo["CodebookInfo"] as JArray;
                var heatData = jsonHeaderInfo["HeatNumbers"] as JArray;
                var classData = jsonHeaderInfo["CLASS"] as JArray;
                var gradeData = jsonHeaderInfo["GRADE"] as JArray;
                var unsData = jsonHeaderInfo["UNS"] as JArray;

                if ((object)modifiedData != null)
                {
                    /*foreach (var chem in modifiedData.CHEMICAL)
                    {
                        System.Diagnostics.Debug.WriteLine($"ELEMENT= {chem.ELEMENT} and ModifiedValue= {chem.VALUE}.");
                    }*/

                    JArray chemicals = (JArray)modifiedData.CHEMICAL;
                }

                var heatNumbers = String.Join(",", heatData.Select(p => p.ToString()).ToArray());
                var classInfo = String.Join(",", classData.Select(p => p.ToString()).ToArray());
                var gradeInfo = String.Join(",", gradeData.Select(p => p.ToString()).ToArray());
                var unsInfo = String.Join(",", unsData.Select(p => p.ToString()).ToArray());

                if (codeInfo != null && codebookInfo != null)
                {
                    for (int i = 0; i < codeInfo.Count; i++)
                    {
                        document.Add(spacer);
                        document.Add(new Paragraph($"Code Section [{i + 1}]", subtitleFont) { Alignment = Element.ALIGN_CENTER });
                        document.Add(new Paragraph($"CodeInfo: {codeInfo[i]}", subtitleFont) { Alignment = Element.ALIGN_CENTER });
                        document.Add(new Paragraph($"CodebookInfo: {codebookInfo[i]}", subtitleFont) { Alignment = Element.ALIGN_CENTER });

                        //if (heatNumbers != null && heatNumbers.Count > i)
                        ////    document.Add(new Paragraph($"HeatNumber: {heatNumbers[i]}", subtitleFont) { Alignment = Element.ALIGN_CENTER });
                        //    document.Add(new Paragraph($"HeatNumber: {heatNumbers.ToString().Replace("\r\n", string.Empty)}", subtitleFont) { Alignment = Element.ALIGN_CENTER });
                    }
                    document.Add(new Paragraph($"HeatNumber: {(heatNumbers == "" ? "N/A" : heatNumbers)}", subtitleFont) { Alignment = Element.ALIGN_CENTER });
                    document.Add(new Paragraph($"Class: {(classInfo == "" ? "N/A" : classInfo)}", subtitleFont) { Alignment = Element.ALIGN_CENTER });
                    document.Add(new Paragraph($"Grade: {(gradeInfo == "" ? "N/A" : gradeInfo)}", subtitleFont) { Alignment = Element.ALIGN_CENTER });
                    document.Add(new Paragraph($"UNS: {(unsInfo == "" ? "N/A" : unsInfo)}", subtitleFont) { Alignment = Element.ALIGN_CENTER });
                }
            }

            document.NewPage();
        }

        //private void AddChemicalCompositionsSection(Document document, dynamic chemicalCompositions)
        //{
        //    try
        //    {
        //        var headerFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 18);
        //        var tableFont = FontFactory.GetFont(FontFactory.HELVETICA, 12);

        //        document.Add(new Paragraph("Chemical Compositions", headerFont));
        //        document.Add(new Paragraph("\n"));

        //        PdfPTable table = new PdfPTable(5) { WidthPercentage = 100 };
        //        table.SetWidths(new float[] { 2, 2, 2, 2, 2 });

        //        AddTableHeader(table, new[] { "Element", "Input Value", "Standard Value", "Heat No", "Status" });

        //        foreach (var item in chemicalCompositions)
        //        {
        //            table.AddCell(new PdfPCell(new Phrase(item.Name.ToString(), tableFont)));

        //            table.AddCell(new PdfPCell(new Phrase(item.Input?.ToString() ?? "N/A", tableFont)));

        //            table.AddCell(new PdfPCell(new Phrase(item.Standard?.ToString() ?? "N/A", tableFont)));

        //            //string heatNoRaw = item.HeatNo?.ToString() ?? "N/A";
        //            //string heatNoClean = Regex.Replace(heatNoRaw, @"\[primary key\s*:\s*(.*?)\]", "$1").Trim();
        //            //table.AddCell(new PdfPCell(new Phrase(heatNoClean, tableFont)));

        //            string heatNoRaw = item.HeatNo?.ToString() ?? "N/A";
        //            Match match = Regex.Match(heatNoRaw, @"[A-Z0-9]+", RegexOptions.IgnoreCase);
        //            string heatNoClean = match.Success ? match.Value : heatNoRaw;
        //            table.AddCell(new PdfPCell(new Phrase(heatNoClean, tableFont)));

        //            // Status cell with font color
        //            string status = item.Status?.ToString() ?? "N/A";
        //            Font statusFont;
        //            switch (status.ToLower())
        //            {
        //                case "pass":
        //                    statusFont = FontFactory.GetFont(FontFactory.HELVETICA, 12, new BaseColor(0, 128, 0)); // Green
        //                    break;
        //                case "fail":
        //                    statusFont = FontFactory.GetFont(FontFactory.HELVETICA, 12, new BaseColor(255, 0, 0)); // Red
        //                    break;
        //                case "unknown":
        //                    statusFont = FontFactory.GetFont(FontFactory.HELVETICA, 12, new BaseColor(255, 165, 0)); // Orange
        //                    break;
        //                default:
        //                    statusFont = FontFactory.GetFont(FontFactory.HELVETICA, 12, new BaseColor(128, 128, 128)); // Gray
        //                    break;
        //            }

        //            PdfPCell statusCell = new PdfPCell(new Phrase(status, statusFont))
        //            {
        //                HorizontalAlignment = Element.ALIGN_CENTER
        //            };

        //            table.AddCell(statusCell);
        //        }

        //        document.Add(table);
        //        document.Add(new Paragraph("\n"));
        //    }
        //    catch (Exception) { }
        //}

        private void AddChemicalCompositionsSection(Document document, dynamic chemicalCompositions, dynamic modifiedData)
        {
            var sectionTitleFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 18);
            var cellFont = FontFactory.GetFont(FontFactory.HELVETICA, 10);

            document.NewPage();
            document.Add(new Paragraph("Chemical Composition", sectionTitleFont) { Alignment = Element.ALIGN_CENTER });
            document.Add(new Paragraph("\n"));

            PdfPTable table = new PdfPTable(9); // 8 columns
            table.WidthPercentage = 100;

            // Add table headers
            string[] headers = { "Element", "Symbol", "Input", "Standard", "Difference", "Grade", "Reason", "Heat No", "Status" };
            foreach (string header in headers)
            {
                PdfPCell cell = new PdfPCell(new Phrase(header, cellFont))
                {
                    BackgroundColor = BaseColor.LIGHT_GRAY,
                    HorizontalAlignment = Element.ALIGN_CENTER
                };
                table.AddCell(cell);
            }

            // Loop through outer array
            foreach (var elementGroup in chemicalCompositions)
            {
                // Loop through each element object
                foreach (var element in elementGroup)
                {
                    string inputValue = (string)element.Input;
                    //modifiedData
                    /*if ((object)modifiedData != null)
                    {
                        //foreach (var chem in modifiedData.CHEMICAL)
                        //{
                        //    System.Diagnostics.Debug.WriteLine($"ELEMENT= {chem.ELEMENT} and ModifiedValue= {chem.VALUE}.");
                        //}
                        JArray chemicals = (JArray)modifiedData.CHEMICAL;
                        var jParsedItems = JArray.Parse(chemicals.ToString());
                        var jItem = jParsedItems.FirstOrDefault(r => r["ELEMENT"].Value<string>() == (string)element.Name);
                        //var jItem1 = jParsedItems.FirstOrDefault(r => (string)r["ELEMENT"] == (string)element.Name);
                        string modifiedValue = (string)jItem["VALUE"];
                        System.Diagnostics.Debug.WriteLine($"Actual Value= {element.Input} and Modified Value= {modifiedValue}.");
                        inputValue = modifiedValue;
                    }*/
                    table.AddCell(new Phrase((string)element.Name, cellFont));
                    table.AddCell(new Phrase((string)element.Symbol, cellFont));
                    table.AddCell(new Phrase(inputValue, cellFont));
                    table.AddCell(new Phrase((string)element.Standard, cellFont));
                    table.AddCell(new Phrase((string)element.Difference, cellFont));
                    table.AddCell(new Phrase((string)element.Grade, cellFont));
                    table.AddCell(new Phrase((string)element.Reason, cellFont));

                    // Clean the HeatNo string (since it's in the format "['OC522552']")
                    string heatNo = ((string)element.HeatNo).Replace("[", "").Replace("]", "").Replace("'", "");
                    table.AddCell(new Phrase(heatNo, cellFont));
                    //table.AddCell(new Phrase((string)element.Status, cellFont));
                    string status = (element.Status?.ToString() ?? "N/A").ToLower();

                    // Set font color based on status
                    Font statusFont;
                    switch (status)
                    {
                        case "pass":
                        case "compliant":
                            statusFont = FontFactory.GetFont(FontFactory.HELVETICA, 12, new BaseColor(0, 128, 0)); // Green
                            break;
                        case "fail":
                            statusFont = FontFactory.GetFont(FontFactory.HELVETICA, 12, new BaseColor(255, 0, 0)); // Red
                            break;
                        default:
                            statusFont = FontFactory.GetFont(FontFactory.HELVETICA, 12, new BaseColor(128, 128, 128)); // Gray
                            break;
                    }

                    // Add colored status cell
                    table.AddCell(new Phrase(element.Status?.ToString() ?? "N/A", statusFont));
                }
            }

            document.Add(table);
        }


        //private void AddMechanicalTestsSection(Document document, dynamic mechanicalTests)
        //{
        //    try
        //    {
        //        var headerFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 18);
        //        var tableFont = FontFactory.GetFont(FontFactory.HELVETICA, 12);

        //        document.Add(new Paragraph("Mechanical Tests", headerFont));
        //        document.Add(new Paragraph("\n"));

        //        PdfPTable table = new PdfPTable(5) { WidthPercentage = 100 };
        //        table.SetWidths(new float[] { 3, 2, 2, 2, 2 });

        //        AddTableHeader(table, new[] { "Property", "Input Value", "Standard Value", "Heat No", "Status" });

        //        foreach (var item in mechanicalTests)
        //        {
        //            table.AddCell(new PdfPCell(new Phrase(item.Name.ToString(), tableFont)));

        //            table.AddCell(new PdfPCell(new Phrase(item.Input?.ToString() ?? "N/A", tableFont)));

        //            table.AddCell(new PdfPCell(new Phrase(item.Standard?.ToString() ?? "N/A", tableFont)));

        //            //string heatNoRaw = item.HeatNo?.ToString() ?? "N/A";
        //            //string heatNoClean = Regex.Replace(heatNoRaw, @"\[primary key\s*:\s*(.*?)\]", "$1").Trim();
        //            //table.AddCell(new PdfPCell(new Phrase(heatNoClean, tableFont)));

        //            string heatNoRaw = item.HeatNo?.ToString() ?? "N/A";
        //            Match match = Regex.Match(heatNoRaw, @"[A-Z0-9]+", RegexOptions.IgnoreCase);
        //            string heatNoClean = match.Success ? match.Value : heatNoRaw;
        //            table.AddCell(new PdfPCell(new Phrase(heatNoClean, tableFont)));

        //            // Status cell with font color
        //            string status = item.Status?.ToString() ?? "N/A";
        //            Font statusFont;
        //            switch (status.ToLower())
        //            {
        //                case "pass":
        //                    statusFont = FontFactory.GetFont(FontFactory.HELVETICA, 12, new BaseColor(0, 128, 0)); // Green
        //                    break;
        //                case "fail":
        //                    statusFont = FontFactory.GetFont(FontFactory.HELVETICA, 12, new BaseColor(255, 0, 0)); // Red
        //                    break;
        //                default:
        //                    statusFont = FontFactory.GetFont(FontFactory.HELVETICA, 12, new BaseColor(128, 128, 128)); // Gray
        //                    break;
        //            }

        //            PdfPCell statusCell = new PdfPCell(new Phrase(status, statusFont))
        //            {
        //                HorizontalAlignment = Element.ALIGN_CENTER
        //            };

        //            table.AddCell(statusCell);
        //        }

        //        document.Add(table);
        //        document.Add(new Paragraph("\n"));
        //    }
        //    catch (Exception)
        //    {

        //    }
        //}

        private void AddMechanicalTestsSection(Document document, dynamic mechanicalTests, dynamic modifiedData)
        {
            try
            {
                // Unwrap outer array to get inner array of test objects
                mechanicalTests = mechanicalTests[0];

                var headerFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 18);
                var tableFont = FontFactory.GetFont(FontFactory.HELVETICA, 12);

                var mechanicalTitle = new Paragraph("Mechanical Tests", headerFont)
                {
                    Alignment = Element.ALIGN_CENTER
                };
                document.Add(mechanicalTitle);
                document.Add(new Paragraph("\n"));

                PdfPTable table = new PdfPTable(8) { WidthPercentage = 100 };
                table.SetWidths(new float[] { 3, 2, 2, 2, 2, 3, 2, 2 });

                AddTableHeader(table, new[] { "Property", "Input Value", "Standard Value", "Heat No", "Grade", "Difference", "Reason", "Status" });

                foreach (var item in mechanicalTests)
                {
                    var inputValue = (string)item.Input;
                    //modifiedData
                    /*if ((object)modifiedData != null)
                    {
                        JArray mechanicals = (JArray)modifiedData.MECHANICAL;
                        if (mechanicals != null)
                        {
                            var jParsedItems = JArray.Parse(mechanicals.ToString());
                            var jItem = jParsedItems.FirstOrDefault(r => r["ELEMENT"].Value<string>() == (string)item.Name);
                            string modifiedValue = (string)jItem["VALUE"];
                            System.Diagnostics.Debug.WriteLine($"Actual Value= {item.Input} and Modified Value= {modifiedValue}.");
                            inputValue = modifiedValue;
                        }
                    }*/


                    table.AddCell(new PdfPCell(new Phrase(item.Name?.ToString() ?? "N/A", tableFont)));
                    table.AddCell(new PdfPCell(new Phrase(inputValue ?? "N/A", tableFont)));
                    table.AddCell(new PdfPCell(new Phrase(item.Standard?.ToString() ?? "N/A", tableFont)));

                    string heatNoRaw = item.HeatNo?.ToString() ?? "N/A";

                    // Extract the heat number value from the string like "['OC522552']"
                    //Match match = Regex.Match(heatNoRaw, @"[A-Z0-9]+", RegexOptions.IgnoreCase);
                    Match match = Regex.Match(heatNoRaw, @"[A-Za-z0-9-]+", RegexOptions.IgnoreCase);
                    string heatNoClean = match.Success ? match.Value : heatNoRaw;

                    table.AddCell(new PdfPCell(new Phrase(heatNoClean, tableFont)));

                    table.AddCell(new PdfPCell(new Phrase(item.Grade?.ToString() ?? "N/A", tableFont)));
                    table.AddCell(new PdfPCell(new Phrase(item.Difference?.ToString() ?? "N/A", tableFont)));
                    table.AddCell(new PdfPCell(new Phrase(item.Reason?.ToString() ?? "N/A", tableFont)));
                    string status = item.Status?.ToString() ?? "N/A";
                    Font statusFont;
                    switch (status.ToLower())
                    {
                        case "pass":
                        case "compliant":  // Also treat "Compliant" as pass/green
                            statusFont = FontFactory.GetFont(FontFactory.HELVETICA, 12, new BaseColor(0, 128, 0)); // Green
                            break;
                        case "fail":
                            statusFont = FontFactory.GetFont(FontFactory.HELVETICA, 12, new BaseColor(255, 0, 0)); // Red
                            break;
                        default:
                            statusFont = FontFactory.GetFont(FontFactory.HELVETICA, 12, new BaseColor(128, 128, 128)); // Gray
                            break;
                    }

                    PdfPCell statusCell = new PdfPCell(new Phrase(status, statusFont))
                    {
                        HorizontalAlignment = Element.ALIGN_CENTER
                    };

                    table.AddCell(statusCell);
                }

                document.Add(table);
                document.Add(new Paragraph("\n"));
            }
            catch (Exception ex)
            {
                // log or handle exception here
                Console.WriteLine(ex.Message);
            }
        }


        //private void AddHeatTreatmentSection(Document document, dynamic heatTreatment)
        //{
        //    try
        //    {
        //        heatTreatment = heatTreatment[0];
        //        var headerFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 18);
        //        var tableFont = FontFactory.GetFont(FontFactory.HELVETICA, 12);

        //        document.Add(new Paragraph("Heat Treatment", headerFont));
        //        document.Add(new Paragraph("\n"));

        //        PdfPTable table = new PdfPTable(4) { WidthPercentage = 100 };
        //        table.SetWidths(new float[] { 3, 3, 2, 2 });

        //        AddTableHeader(table, new[] { "Input", "Standard", "Heat No", "Status" });

        //        table.AddCell(new PdfPCell(new Phrase(heatTreatment.Input?.ToString() ?? "N/A", tableFont)));
        //        table.AddCell(new PdfPCell(new Phrase(heatTreatment.Standard?.ToString() ?? "N/A", tableFont)));
        //        //string heatNoRaw = heatTreatment.HeatNo?.ToString() ?? "N/A";
        //        //string heatNoClean = Regex.Replace(heatNoRaw, @"\[primary key\s*:\s*(.*?)\]", "$1").Trim();
        //        //table.AddCell(new PdfPCell(new Phrase(heatNoClean, tableFont)));

        //        string heatNoRaw = heatTreatment.HeatNo?.ToString() ?? "N/A";
        //        Match match = Regex.Match(heatNoRaw, @"[A-Z0-9]+", RegexOptions.IgnoreCase);
        //        string heatNoClean = match.Success ? match.Value : heatNoRaw;
        //        table.AddCell(new PdfPCell(new Phrase(heatNoClean, tableFont)));

        //        // Status cell with font color
        //        string status = heatTreatment.Status?.ToString() ?? "N/A";
        //        Font statusFont;
        //        switch (status.ToLower())
        //        {
        //            case "pass":
        //                statusFont = FontFactory.GetFont(FontFactory.HELVETICA, 12, new BaseColor(0, 128, 0)); // Green
        //                break;
        //            case "fail":
        //                statusFont = FontFactory.GetFont(FontFactory.HELVETICA, 12, new BaseColor(255, 0, 0)); // Red
        //                break;
        //            default:
        //                statusFont = FontFactory.GetFont(FontFactory.HELVETICA, 12, new BaseColor(128, 128, 128)); // Gray
        //                break;
        //        }

        //        PdfPCell statusCell = new PdfPCell(new Phrase(status, statusFont))
        //        {
        //            HorizontalAlignment = Element.ALIGN_CENTER
        //        };

        //        table.AddCell(statusCell);

        //        document.Add(table);
        //        document.Add(new Paragraph("\n"));
        //    }
        //    catch (Exception)
        //    {

        //    }
        //}

        private void AddHeatTreatmentSection(Document document, PdfWriter writer, dynamic heatTreatment, dynamic modifiedData)
        {
            try
            {
                // heatTreatment is an array of arrays, so get the first inner array
                var innerArray = heatTreatment;

                var headerFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 18);
                var tableFont = FontFactory.GetFont(FontFactory.HELVETICA, 12);

                //document.Add(new Paragraph("Heat Treatment", headerFont));
                var HeatTitle = new Paragraph("Heat Treatment", headerFont)
                {
                    Alignment = Element.ALIGN_CENTER
                };
                document.Add(HeatTitle);
                document.Add(new Paragraph("\n"));

                PdfPTable table = new PdfPTable(6) { WidthPercentage = 100 };
                table.SetWidths(new float[] { 3, 3, 4, 5, 3, 3 });

                AddTableHeader(table, new[] { "Name", "Input", "Reason", "Standard", "Difference", "Status" });

                // Now iterate over the objects in the inner array
                foreach (var item in innerArray)
                {
                    JObject firstItem = (JObject)item[0];
                    System.Diagnostics.Debug.WriteLine($"Name: {(string)firstItem["Name"]}, Input: {firstItem["Input"]}, Reason: {firstItem["Reason"]}, Standard: {firstItem["Standard"]}, Difference: {firstItem["Difference"]}, Status: {firstItem["Status"]}");

                    table.AddCell(new PdfPCell(new Phrase(firstItem.Value<string>("Name") ?? "N/A", tableFont)));
                    table.AddCell(new PdfPCell(new Phrase(firstItem.Value<string>("Input") ?? "N/A", tableFont)));
                    table.AddCell(new PdfPCell(new Phrase(firstItem.Value<string>("Reason") ?? "N/A", tableFont)));
                    table.AddCell(new PdfPCell(new Phrase(firstItem.Value<string>("Standard") ?? "N/A", tableFont)));
                    table.AddCell(new PdfPCell(new Phrase(firstItem.Value<string>("Difference") ?? "N/A", tableFont)));

                    string status = firstItem.Value<string>("Status") ?? "N/A";
                    Font statusFont;
                    switch (status.ToLower())
                    {
                        case "pass":
                        case "compliant":
                            statusFont = FontFactory.GetFont(FontFactory.HELVETICA, 12, new BaseColor(0, 128, 0));
                            break;
                        case "fail":
                        case "non-compliant":
                            statusFont = FontFactory.GetFont(FontFactory.HELVETICA, 12, new BaseColor(255, 0, 0));
                            break;
                        default:
                            statusFont = FontFactory.GetFont(FontFactory.HELVETICA, 12, new BaseColor(128, 128, 128));
                            break;
                    }

                    PdfPCell statusCell = new PdfPCell(new Phrase(status, statusFont))
                    {
                        HorizontalAlignment = Element.ALIGN_CENTER
                    };
                    table.AddCell(statusCell);
                }

                document.Add(table);
                document.Add(new Paragraph("\n"));
                // Change Log Section
                // Change Log Section
                var changeLogTitle = new Paragraph("Change Log", headerFont)
                {
                    Alignment = Element.ALIGN_CENTER
                };
                document.Add(changeLogTitle);
                document.Add(new Paragraph("\n"));

                PdfPTable logTable = new PdfPTable(2)
                {
                    WidthPercentage = 100,
                    HorizontalAlignment = Element.ALIGN_CENTER
                };
                logTable.SetWidths(new float[] { 1, 1 });

                var logHeaderFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 12);
                tableFont = FontFactory.GetFont(FontFactory.HELVETICA, 10);

                // Headers
                logTable.AddCell(new PdfPCell(new Phrase("Description", logHeaderFont)));
                logTable.AddCell(new PdfPCell(new Phrase("Date & Time", logHeaderFont)));

                try
                {
                    // TimeZone: IST
                    TimeZoneInfo indiaZone = TimeZoneInfo.FindSystemTimeZoneById("India Standard Time");

                    // Created On: local server time (converted to IST)
                    DateTime createdOn = TimeZoneInfo.ConvertTime(DateTime.Now, indiaZone);

                    // Modified On: passed from JS (convert from UTC to IST)
                    if (modifiedData != null && modifiedData.ModifiedOn != null)
                    {
                        DateTime modifiedUtc = DateTime.Parse(modifiedData.ModifiedOn.ToString(), null, System.Globalization.DateTimeStyles.AdjustToUniversal);
                        DateTime modifiedOn = TimeZoneInfo.ConvertTimeFromUtc(modifiedUtc, indiaZone);

                        logTable.AddCell(new PdfPCell(new Phrase("AI Response File Created Date", tableFont)));
                        logTable.AddCell(new PdfPCell(new Phrase(modifiedOn.ToString("yyyy-MM-dd HH:mm:ss"), tableFont)));

                        logTable.AddCell(new PdfPCell(new Phrase("AI Response File Modified Date", tableFont)));
                        logTable.AddCell(new PdfPCell(new Phrase(createdOn.ToString("yyyy-MM-dd HH:mm:ss"), tableFont)));
                    }
                    else
                    {
                        logTable.AddCell(new PdfPCell(new Phrase("AI Response File Created Date", tableFont)));
                        logTable.AddCell(new PdfPCell(new Phrase(createdOn.ToString("yyyy-MM-dd HH:mm:ss"), tableFont)));

                        logTable.AddCell(new PdfPCell(new Phrase("AI Response File Modified Date", tableFont)));
                        logTable.AddCell(new PdfPCell(new Phrase("N/A", tableFont)));
                    }
                }
                catch (Exception ex)
                {
                    logTable.AddCell(new PdfPCell(new Phrase("Log Error", tableFont)));
                    logTable.AddCell(new PdfPCell(new Phrase("N/A", tableFont)));
                    Console.WriteLine(ex.Message);
                }

                document.Add(logTable);

            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
        }


        private void AddTableHeader(PdfPTable table, string[] headers)
        {
            var headerFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 12);
            foreach (var header in headers)
            {
                table.AddCell(new PdfPCell(new Phrase(header, headerFont))
                {
                    BackgroundColor = new BaseColor(240, 240, 240),
                    HorizontalAlignment = Element.ALIGN_CENTER
                });
            }
        }


        private void SaveOCRResponse(HttpContext context)
        {
            try
            {
                this._iwbRequest = new IwbRequest
                {
                    TenantId = SessionHandler.UserInformation.TenantId,
                    UserId = SessionHandler.UserInformation.UserNum
                };

                this._iwbRequest.Document = new IwbDocument();
                this._iwbRequest.Document.Id = Convert.ToInt32(context.Request["docuemntId"]);

                this._iwbResponse = new IwbService().VerifyDocument(this._iwbRequest);

                context.Response.Write(JsonConvert.SerializeObject(
                    new
                    {
                        success = true,
                        message = "IWB Document saved successfully",
                        data = this._iwbResponse.WPQs,
                    }));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->SaveOCRResponse", 0, "An error has occurred inside SaveOCRResponse function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, data = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud." }));
            }
        }

        private void SaveModifiedAIResponse(HttpContext context)
        {
            string scanAPIUrl = _aiApiUrl + "/scan_input";
            try
            {
                //string jsonData = Convert.ToString(context.Request["jsonData"]);
                string originalKeyValues = Convert.ToString(context.Request["originalKeyValues"]);

                var client = new RestClient(scanAPIUrl);
                var request = new RestRequest(Method.POST);

                request.AddParameter("application/json", originalKeyValues, ParameterType.RequestBody);

                var response = client.Execute(request);

                if (response.StatusCode == HttpStatusCode.OK)
                {
                    context.Response.Write(JsonConvert.SerializeObject(
                        new
                        {
                            success = true,
                            message = "Processed successfully",
                            data = response.Content
                        }));
                }
                else
                {
                    context.Response.Write(JsonConvert.SerializeObject(
                        new
                        {
                            success = false,
                            message = "Fail to Process the input results",
                            data = response.ErrorMessage
                        }));
                }
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->SaveOCRResSaveModifiedAIResponseponse", 0, "An error has occurred inside SaveModifiedAIResponse function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, data = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud." }));
            }
        }

        private void SaveWelderRatings(HttpContext context)
        {
            try
            {
                using (var reader = new StreamReader(context.Request.InputStream))
                {
                    string json = reader.ReadToEnd();

                    var ratingData = JsonConvert.DeserializeObject<WelderRatingModel>(json);

                    this._iwbRequest = new IwbRequest
                    {
                        TenantId = SessionHandler.UserInformation.TenantId,
                        UserId = SessionHandler.UserInformation.UserNum,
                        WelderRating = ratingData
                    };

                    this._iwbResponse = new IwbService().SaveWelderRatings(this._iwbRequest);

                    context.Response.ContentType = "application/json";
                    context.Response.Write(JsonConvert.SerializeObject(new
                    {
                        success = true,
                        message = "Welder rating submitted successfully.",
                        data = this._iwbResponse
                    }));
                }
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->SaveWelderRatingsResponse", 0, "An error has occurred inside SaveWelderRatingsResponse function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, data = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud." }));
            }
        }

        private void getWelderRatings(HttpContext context)
        {
            try
            {
                var welderId = Convert.ToInt32(context.Request["welderId"]);
                this._iwbRequest = new IwbRequest
                {
                    WelderRating = new WelderRatingModel
                    {
                        WelderId = welderId
                    },
                    TenantId = TenantId,
                    UserId = UserNum
                };

                this._iwbResponse = new IwbService().GetWelderRatingsById(this._iwbRequest);

                context.Response.Write(JsonConvert.SerializeObject(
                    new
                    {
                        success = true,
                        message = "Welder ratings fetched successfully.",
                        data = this._iwbResponse.WelderRatings,
                    }));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->SaveWelderRatingsResponse", 0, "An error has occurred inside SaveWelderRatingsResponse function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, data = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud." }));
            }
        }

        private void getJobTitles(HttpContext context)
        {
            try
            {
                this._iwbRequest = new IwbRequest
                {
                    TenantId = TenantId,
                    UserId = UserNum
                };

                this._iwbResponse = new IwbService().GetJobTitles(this._iwbRequest);

                context.Response.Write(JsonConvert.SerializeObject(
                    new
                    {
                        success = true,
                        message = "Job titles fetched successfully.",
                        data = this._iwbResponse.JobTitles
                    }));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->getJobTitles", 0, "An error has occurred inside getJobTitles function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, data = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud." }));
            }
        }

        private void getJobDetailsById(HttpContext context)
        {
            try
            {
                var jobId = Convert.ToInt32(context.Request["jobId"]);

                this._iwbRequest = new IwbRequest
                {
                    TenantId = TenantId,
                    UserId = UserNum,
                    JobId = jobId
                };

                this._iwbResponse = new IwbService().GetJobDetailsById(this._iwbRequest);

                context.Response.Write(JsonConvert.SerializeObject(
                    new
                    {
                        success = true,
                        message = "Job details fetched successfully.",
                        data = this._iwbResponse.JobDetails
                    }));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->getJobDetailsById", 0, "An error has occurred inside getJobDetailsById function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, data = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud." }));
            }
        }


        private void TryAI(HttpContext context)
        {
            try
            {
                string docType = context.Request.Form["docType"].ToString();
                _postedFile = context.Request.Files[0];
                byte[] bytes = null;

                var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(_postedFile.FileName);

                using (var stream = new MemoryStream())
                {
                    _postedFile.InputStream.CopyTo(stream);
                    bytes = stream.ToArray();
                }

                var client = new RestClient(_aiApiUrl + "/process_pdf");
                var request = new RestRequest(Method.POST);
                request.AddFile("file", bytes, _postedFile.FileName, "application/pdf");
                request.AddParameter("docType", docType);
                request.Timeout = _aiTimeoutSeconds * 1000;

                var response = client.Execute(request);

                try
                {
                    Task.Run(() => RevAuditLogger.WriteInformation(Originator.Handler, "IwbHandler->TryAI", 0, response.Content));

                    AIResponse aiRequest = new AIResponse();
                    aiRequest.RequestUrl = _aiApiUrl + "/process_pdf";
                    aiRequest.ResponseJson = response.Content;
                    if (response.StatusCode != HttpStatusCode.OK)
                    {
                        aiRequest.IsError = true;
                        aiRequest.ErrorDescription = response.Content;
                    }
                    else
                    {
                        aiRequest.IsError = false;
                        aiRequest.ErrorDescription = "";
                    }

                    this._iwbRequest = new IwbRequest();
                    this._iwbRequest.Data = aiRequest;
                    this._iwbResponse = new IwbService().SaveAIResponse(this._iwbRequest);
                }
                catch (Exception)
                {

                }

                context.Response.Write(response.Content);
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->TryAI", 0, "An error has occurred inside TryAI function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, data = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud." }));
            }
        }
        #endregion

        #region Welders

        public void saveWelder(HttpContext context)
        {
            var _umRequest = new UserManagementRequest();
            try
            {
                _umRequest.TenantId = TenantId;
                var _user = JSONUtil.JsonToClass<User>(context.Request.Form["UserData"]);
                _user.GroupNum = 1000;
                _user.CreatedBy = SessionHandler.UserInformation.UserNum;
                _user.OrganizationId = SessionHandler.UserInformation.OrganizationId;
                _user.RoleId = 8;
                _umRequest.User = _user;
                _umRequest.IsOnlyIQ3ModeEnabled = SessionHandler.IsOnlyIQ3ModeEnabled;
                //var recorder = SessionHandler.Recorders.FirstOrDefault(r => r.Id == this._user.RecId);
                var recorder = (SessionHandler.IsECEnabled && !SessionHandler.IsEnterpriseRecorder) == true ? SessionHandler.LocalRecorder : SessionHandler.Recorders.FirstOrDefault(r => r.Id == _user.RecId);
                string profileImageName = "";
                if (_user.UserNum == 0)
                {
                    ////Insert

                    if (context.Request.Files.Count > 0)
                    {
                        _postedFile = context.Request.Files[0];

                        profileImageName = _postedFile.FileName;

                        var _imgUploadFilePath = HttpContext.Current.Server.MapPath("~/Uploads/UserImages/");
                        if (!Directory.Exists(_imgUploadFilePath))
                            Directory.CreateDirectory(_imgUploadFilePath);

                        _uploadFilePath = Path.Combine(_imgUploadFilePath, profileImageName);
                        string newFileName = profileImageName;

                        if (File.Exists(_uploadFilePath))
                        {
                            int counter = 1;
                            while (File.Exists(_uploadFilePath))
                            {
                                newFileName = $"{counter}-{profileImageName}";
                                _uploadFilePath = Path.Combine(_imgUploadFilePath, newFileName);

                                counter++;
                            }

                            profileImageName = newFileName;
                        }
                        _postedFile.SaveAs(_uploadFilePath);
                        _user.UserPic = profileImageName;
                    }

                    var _umResponse = new UserManagementService().CreateAppUserAccount(_umRequest);
                    _user = _umResponse.Users.FirstOrDefault();

                    if (_user.UserNum > 0 && SessionHandler.IsIWBModeEnabled && _user.IsIwbUser)
                        EnableIWBPermissionForNewUser(_user);

                    if (_umResponse != null && _user != null)
                    {
                        context.Response.Write(new JavaScriptSerializer().Serialize(
                                    new
                                    {
                                        success = true,
                                        message = "User Data has been saved successfully.",
                                        data = _user.UserNum,
                                    }));
                        ActivityLogger.LogAsync(13, string.Format("{0}¶{1} ({2})", SessionHandler.UserInformation.UserName, _user.UserName, _user.UserID), context.Request.Form["UserData"]);
                    }
                    else
                    {
                        context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, message = "Error", data = this._iwbResponse.Message }));
                    }
                }
                /*else
                {
                    ////Update
                    bool updateUserPwd = Convert.ToBoolean(Request.Form["updatePwd"]);
                    var _umResponse = new UserManagementService().UpdateAppUserAccount(recorder, _umRequest, updateUserPwd);
                    _user = _umResponse.Users.FirstOrDefault();
                    if (_umResponse.FlagStatus)
                    {
                        Response.Write(GetResponse("Success", "User Data has been saved successfully.", _user));
                        ActivityLogger.LogAsync(10, string.Format("{0}¶{1} ({2})", SessionHandler.UserInformation.UserName, _user.UserName, _user.UserID), context.Request.Form["UserData"]);
                    }
                }*/
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->saveWelder", _umRequest.TenantId, "An error has occurred inside saveWelder. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                //Response.Write(GetResponse("Error", ex.Message == "user id is not unique..." ? ex.Message : SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud.", -1));
                //context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, data = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud." }));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, data = ex.Message == "user id is not unique..." ? ex.Message : SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud." }));

            }
        }
        private bool EnableIWBPermissionForNewUser(User user)
        {
            var _umRequest = new UserManagementRequest();
            try
            {
                bool returnValue = false;
                _umRequest.TenantId = TenantId;
                _umRequest.User = user;

                var _umResponse = new UserManagementService().IwbAddExtension(_umRequest);

                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Handler, "IwbHandler->EnableIWBPermissionForNewUser", _umRequest.TenantId, "EnableIWBPermissionForNewUser executed successfully"));
                returnValue = true;
                return returnValue;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->EnableIWBPermissionForNewUser", _umRequest.TenantId, "An error has occurred inside EnableDisableIwbUser. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        #endregion

        #region Test Schedule

        private void getAllTestTypes(HttpContext context)
        {
            try
            {
                this._iwbRequest = new IwbRequest { TenantId = TenantId, UserId = UserNum };
                this._iwbRequest.Criteria = new IwbCriteria
                {
                    OrganizationId = SessionHandler.UserInformation.OrganizationId
                };
                this._iwbResponse = new IwbService().GetTestingTypes(this._iwbRequest);
                context.Response.Write(JsonConvert.SerializeObject(
                    new
                    {
                        success = true,
                        message = "Test Types have been fetched successfully.",
                        data = this._iwbResponse.TestTypes,
                    }));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->getAllTestTypes", 0, "An error has occurred inside getAllTestTypes function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, message = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud.", data = -1, }));
            }
        }

        private void saveTest(HttpContext context)
        {
            try
            {
                var test = JSONUtil.JsonToClass<IwbTest>(context.Request.Form["test"]);
                //test.InitiatorId = UserNum;
                test.CurrentStatus = IwbTestStatus.Scheduled;
                test.CreatedDate = DateTime.Now;
                test.OrganizationId = SessionHandler.UserInformation.OrganizationId;
                test.CreatedBy = UserNum;

                this._iwbRequest = new IwbRequest
                {
                    UserId = UserNum,
                    TenantId = TenantId,
                    Test = test,
                };

                this._iwbResponse = new IwbService().SaveTest(this._iwbRequest);
                switch (this._iwbResponse.Acknowledge)
                {
                    case AcknowledgeType.Success:
                        context.Response.Write(new JavaScriptSerializer().Serialize(
                                new
                                {
                                    success = true,
                                    message = "Test has been saved successfully.",
                                    data = this._iwbResponse.IwbId,
                                }));
                        break;
                    case AcknowledgeType.Failure:
                        context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, message = "Error", data = this._iwbResponse.Message }));
                        break;
                }
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Handler, "IwbHandler->saveTest", 0, "saveTest executed successfully."));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->saveTest", 0, "An error has occurred inside saveTest function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, data = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud.", exception = ex.Message }));
            }
        }

        private void getTests(HttpContext context)
        {
            try
            {
                var pageIndex = Convert.ToInt32(context.Request["pageIndex"]);
                var pageSize = Convert.ToInt32(context.Request["pageSize"]);
                var testName = Convert.ToString(context.Request["testName"]);
                var testTypeId = Convert.ToString(context.Request["testTypeId"]);
                var schStatus = Convert.ToString(context.Request["schStatus"]);

                //    this._iwbRequest = new IwbRequest
                //    {
                //        TenantId = TenantId,
                //        UserId = UserNum,
                //        Criteria = new IwbCriteria
                //        {
                //            TestStatuses = new List<IwbTestStatus>
                //            {
                //                IwbTestStatus.Scheduled,
                //                IwbTestStatus.InProgress,
                //                IwbTestStatus.Completed
                //            },
                //            OrganizationId = SessionHandler.UserInformation.OrganizationId,
                //            Name = testName,
                //            TypeCode = testTypeId,
                //            //StartDate = schStatus,
                //            PageIndex = pageIndex,
                //            PageSize = pageSize,

                //        }
                //    };

                //    this._iwbResponse = new IwbService().GetTestsByWhereClause(this._iwbRequest);

                //    context.Response.Write(JsonConvert.SerializeObject(
                //        new
                //        {
                //            success = true,
                //            message = "Tests have been fetched successfully.",
                //            data = this._iwbResponse.Tests,
                //            rowCount = this._iwbResponse.TotalRecords,
                //            totalPages = this._iwbResponse.TotalPages,
                //        }));
                //}

                List<IwbTestStatus> selectedStatuses;

                if (string.IsNullOrWhiteSpace(schStatus))
                {
                    selectedStatuses = new List<IwbTestStatus>
                    {
                        IwbTestStatus.Scheduled,
                        IwbTestStatus.InProgress,
                        IwbTestStatus.Completed
                    };
                }
                else
                {
                    selectedStatuses = new List<IwbTestStatus>();
                    var statusIds = schStatus.Split(',');

                    foreach (var statusId in statusIds)
                    {
                        if (Enum.TryParse<IwbTestStatus>(statusId.Trim(), out IwbTestStatus parsedStatus))
                        {
                            selectedStatuses.Add(parsedStatus);
                        }
                    }
                }

                this._iwbRequest = new IwbRequest
                {
                    TenantId = TenantId,
                    UserId = UserNum,
                    Criteria = new IwbCriteria
                    {
                        TestStatuses = selectedStatuses,
                        OrganizationId = SessionHandler.UserInformation.OrganizationId,
                        Name = testName,
                        TypeCode = testTypeId,
                        PageIndex = pageIndex,
                        PageSize = pageSize,
                        CreatedBy = (SessionHandler.UserInformation.UserType == 1 || SessionHandler.UserInformation.UserType == 8) ? 0 : UserNum,
                    }
                };

                this._iwbResponse = new IwbService().GetTestsByWhereClause(this._iwbRequest);

                context.Response.Write(JsonConvert.SerializeObject(
                    new
                    {
                        success = true,
                        message = "Tests have been fetched successfully.",
                        data = this._iwbResponse.Tests,
                        rowCount = this._iwbResponse.TotalRecords,
                        totalPages = this._iwbResponse.TotalPages,
                    }));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->getTests", TenantId, "An error has occurred while calling the function getTests. Exception Message = " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(string.Format("[ERROR: {0}]", ex.Message));
            }

        }


        private void registerWelderForTest(HttpContext context)
        {
            try
            {
                //var welderId = UserNum;
                var welderId = Convert.ToInt32(context.Request["welderId"]);
                var testId = Convert.ToInt32(context.Request["testId"]);

                this._iwbRequest = new IwbRequest
                {
                    //UserId = UserNum,
                    UserId = welderId,
                    TestId = testId,
                    TenantId = TenantId,
                };

                this._iwbResponse = new IwbService().RegisterWelderForTest(this._iwbRequest);
                switch (this._iwbResponse.Acknowledge)
                {
                    case AcknowledgeType.Success:
                        context.Response.Write(new JavaScriptSerializer().Serialize(
                                new
                                {
                                    success = true,
                                    message = "Welder has been successfully registered for test.",
                                    data = this._iwbResponse.IwbId,
                                }));
                        break;
                    case AcknowledgeType.Failure:
                        context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, message = "Error", data = this._iwbResponse.Message }));
                        break;
                }
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Handler, "IwbHandler->registerWelderForTest", 0, "registerWelderForTest executed successfully."));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->registerWelderForTest", 0, "An error has occurred inside registerWelderForTest function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, data = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud." }));
            }
        }


        private void getTestAttendees(HttpContext context)
        {
            try
            {
                var testId = Convert.ToInt32(context.Request["testId"]);
                this._iwbRequest = new IwbRequest
                {
                    TestId = testId,
                    TenantId = TenantId,
                    UserId = UserNum
                };
                this._iwbResponse = new IwbService().GetTestAttendees(this._iwbRequest);
                context.Response.Write(JsonConvert.SerializeObject(
                    new
                    {
                        success = true,
                        message = "Test Attendees have been fetched successfully.",
                        data = this._iwbResponse.TestAttendees,
                    }));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->getTestAttendees", 0, "An error has occurred inside getTestAttendees function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, message = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud.", data = -1, }));
            }
        }

        private void getSignOffRequestUsers(HttpContext context)
        {
            try
            {
                var documentId = Convert.ToInt32(context.Request["documentId"]);
                this._iwbRequest = new IwbRequest
                {
                    TestId = documentId,
                    TenantId = TenantId
                };
                this._iwbResponse = new IwbService().GetSignOffRequestUsers(this._iwbRequest);
                context.Response.Write(JsonConvert.SerializeObject(
                    new
                    {
                        success = true,
                        message = "SignOff Request Users have been fetched successfully.",
                        data = this._iwbResponse.ResultData
                    }));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->getTestAttendees", 0, "An error has occurred inside getSignOffRequestUsers function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, message = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud.", data = -1, }));
            }
        }
        private void ExportSignedPDF(HttpContext context)
        {
            try
            {
                string selectedIdsString = context.Request["selectedIds"].ToString();
                string signerEmail = context.Request["signerEmail"].ToString();
                int documentId = Convert.ToInt32(context.Request["documentId"]);

                string signedPdfUlr = new RevSignHelper().getRevSignatureURL(documentId, signerEmail, TenantId, true, selectedIdsString);

                context.Response.ContentType = "application/json";
                context.Response.Write(JsonConvert.SerializeObject(new
                {
                    success = true,
                    message = "Export completed successfully.",
                    downloadUrl = signedPdfUlr
                }));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->ExportSignedPDF", TenantId, $"Error during PDF export: {ex.Message}", ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, message = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud.", data = -1, }));
            }
        }

        private void UpdateTestStatus(HttpContext context)
        {
            try
            {
                var testId = Convert.ToInt32(context.Request["Id"]);
                var statusId = Convert.ToInt32(context.Request["Status"]);

                this._iwbRequest = new IwbRequest
                {
                    TestId = testId,
                    TenantId = TenantId,
                    UserId = UserNum
                };
                this._iwbRequest.Test = new IwbTest();
                this._iwbRequest.Test.Id = testId;
                this._iwbRequest.Test.CurrentStatus = (IwbTestStatus)statusId;
                this._iwbRequest.Test.CreatedBy = UserNum;

                this._iwbResponse = new IwbService().UpdateTestStatus(this._iwbRequest);
                context.Response.Write(JsonConvert.SerializeObject(
                    new
                    {
                        success = true,
                        message = "Test Attendees have been fetched successfully.",
                        data = this._iwbResponse.TestAttendees,
                    }));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->getTestAttendees", 0, "An error has occurred inside getTestAttendees function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, message = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud.", data = -1, }));
            }
        }

        private void UpdateWelderTestStatus(HttpContext context)
        {
            try
            {
                IwbTestAttendee attendeeData = new IwbTestAttendee();
                attendeeData.Id = Convert.ToInt32(context.Request["recordid"]);
                attendeeData.PassStatus = (IwbTestPassStatus)Convert.ToInt32(context.Request["PassStatus"]);

                this._iwbRequest = new IwbRequest
                {
                    TenantId = TenantId,
                    UserId = UserNum
                };


                this._iwbRequest.Data = attendeeData;

                this._iwbResponse = new IwbService().UpdateWelderTestStatus(this._iwbRequest);
                context.Response.Write(JsonConvert.SerializeObject(
                    new
                    {
                        success = true,
                        message = "Updated test attendee status successfully."
                    }));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->UpdateWelderTestStatus", 0, "An error has occurred inside UpdateWelderTestStatus function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, message = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud.", data = -1, }));
            }
        }

        private void saveTestInvitation(HttpContext context)
        {
            try
            {
                var welderIds = JSONUtil.JsonToList<Int32>(context.Request.Form["welderIds"]);
                var testId = Convert.ToInt32(context.Request["testId"]);

                //var welderEmails = JSONUtil.JsonToList<String>(context.Request.Form["welderEmails"]);
                //var welderEmails = Convert.ToString(context.Request.Form["welderEmails"]).Split(',').ToList();
                IEnumerable<string> welderEmails = new JavaScriptSerializer().Deserialize<IEnumerable<string>>(context.Request.Form["welderEmails"]);

                this._iwbRequest = new IwbRequest
                {
                    UserId = UserNum,
                    TenantId = TenantId,
                    ModifiedDate = DateTime.Now,
                    OrganizationId = SessionHandler.UserInformation.OrganizationId,

                    Ids = welderIds,
                    TestId = testId,
                };

                this._iwbResponse = new IwbService().SaveTestInvitation(this._iwbRequest);
                switch (this._iwbResponse.Acknowledge)
                {
                    case AcknowledgeType.Success:
                        context.Response.Write(new JavaScriptSerializer().Serialize(
                                new
                                {
                                    success = true,
                                    message = "Test Invitation has been saved successfully.",
                                    data = this._iwbResponse.RowsAffected,
                                }));
                        Task.Run(async () =>
                        {
                            foreach (var welder in welderEmails)
                            {
                                string subject = string.Empty;
                                subject = "Invitation to attend Test from IWB";
                                string body = "Hello, you are being invited to attend the IWB testing event. Please make sure to reach on time!";
                                if (!string.IsNullOrEmpty(welder))
                                {
                                    new EmailSendHelper().SendMail(welder, subject, body, true);
                                }
                            }
                        });
                        break;
                    case AcknowledgeType.Failure:
                        context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, message = "Error", data = this._iwbResponse.Message }));
                        break;
                }
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Handler, "IwbHandler->saveTestInvitation", 0, "saveTestInvitation executed successfully."));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->saveTestInvitation", 0, "An error has occurred inside saveTestInvitation function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, data = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud." }));
            }
        }


        #endregion

        #region Dashboard

        private void getWelderDashboardData(HttpContext context)
        {
            try
            {
                this._iwbRequest = new IwbRequest { TenantId = TenantId };
                this._iwbRequest.Criteria = new IwbCriteria
                {
                    JobStatuses = new List<IwbJobStatus>
                    {
                        IwbJobStatus.Initiated,
                        IwbJobStatus.InProgress,
                        IwbJobStatus.Completed
                    }
                };

                this._iwbResponse = new IwbService().GetWelderDashboardData(this._iwbRequest);
                context.Response.Write(JsonConvert.SerializeObject(
                    new
                    {
                        success = true,
                        message = "Welder dashboard data have been fetched successfully.",
                        jobs = this._iwbResponse.Jobs,
                        tests = this._iwbResponse.Tests,
                    }));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->getWelderDashboardData", TenantId, "An error has occurred while calling the function getWelderDashboardData. Exception Message = " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(string.Format("[ERROR: {0}]", ex.Message));
            }

        }

        #endregion

        #region Register User

        private void registerUser(HttpContext context)
        {
            try
            {
                var organization = JSONUtil.JsonToClass<Organization>(context.Request.Form["organization"]);
                var user = JSONUtil.JsonToClass<IwbUser>(context.Request.Form["user"]);
                var password = Convert.ToString(context.Request["password"]);

                organization.CreatedDate = DateTime.Now;
                organization.CreatedBy = 1000;

                user.CreatedDate = DateTime.Now;
                user.CreatedBy = 1000;


                this._iwbRequest = new IwbRequest
                {
                    UserId = 1000, //UserNum,
                    TenantId = 0, //TenantId,
                    Organization = organization,
                    OrganizationAccountPassword = password
                };

                int orgId = 1000;
                if (user.Type != IwbUserType.Welder)
                {
                    this._iwbResponse = new IwbService().SaveOrganization(this._iwbRequest);
                    orgId = this._iwbResponse.LastSavedId;
                }


                this._iwbRequest = new IwbRequest
                {
                    UserId = 1000, //UserNum,
                    TenantId = 0, //TenantId,
                    User = user,
                    OrganizationAccountPassword = password,
                    OrganizationId = orgId
                };
                _iwbResponse = new IwbService().RegisterUser(this._iwbRequest);
                switch (this._iwbResponse.Acknowledge)
                {
                    case AcknowledgeType.Success:
                        context.Response.Write(new JavaScriptSerializer().Serialize(
                                new
                                {
                                    success = true,
                                    message = "User has been saved successfully.",
                                    data = this._iwbResponse.LastSavedId,
                                }));
                        break;
                    case AcknowledgeType.Failure:
                        context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, message = "Error", data = this._iwbResponse.Message }));
                        break;
                }
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Handler, "IwbHandler->registerUser", 0, "registerUser executed successfully."));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->registerUser", 0, "An error has occurred inside registerUser function. Exception Message -> " + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                //context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, data = SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud." }));
                context.Response.Write(new JavaScriptSerializer().Serialize(new { success = false, data = ex.Message == "user id is not unique..." ? ex.Message : SessionHandler.CurrentLanguage == "en-US" ? "There is a problem processing your request." : "Hay un problema al procesar su solicitud." }));
            }
        }


        #endregion

        #region ZOHO

        private void getZohoToken()
        {
            var client = new RestClient(_zohoBaseUrlAccount);
            var request = new RestRequest();
            request.Method = Method.POST;

            request.AddParameter("client_id", _zohoClientId);
            request.AddParameter("client_secret", _zohoClientSecret);
            request.AddParameter("refresh_token", _zohoRefreshToken);
            request.AddParameter("grant_type", "refresh_token");//authorization_code

            System.Diagnostics.Debug.WriteLine("Executing '{0}' request to '{1}'...", request.Method, client.BuildUri(request));

            var response = client.Execute(request);

            if (response.IsSuccessful)
            {
                _zohoToken = JsonConvert.DeserializeObject<ZohoTokenResponse>(response.Content);
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"Error: {response.StatusCode} - {response.StatusDescription}");
            }
        }


        private void registerCustomerOnZoho(HttpContext context)
        {
            getZohoToken();

            var _user = JSONUtil.JsonToClass<User>(context.Request.Form["user"]);

            var client = new RestClient(_zohoBaseUrlBilling);
            var request = new RestRequest("/customers", Method.POST);

            request.AddHeader("Authorization", $"Zoho-oauthtoken {_zohoToken.AccessToken}");
            request.AddJsonBody(new
            {
                display_name = _user.UserName,
                first_name = _user.UserName,
                last_name = "Customer",
                email = _user.UserEmail,
                company_name = _user.CompanyName,
                phone = _user.UserPhone,
                mobile = _user.UserPhone,
                notes = _user.Descr,
                can_add_card = true,
                can_add_bank_account = true,
            });

            System.Diagnostics.Debug.WriteLine("Executing '{0}' request to '{1}'...", request.Method, client.BuildUri(request));

            var response = client.Execute(request);
            if (response.IsSuccessful)
            {
                dynamic newCustomer = JsonConvert.DeserializeObject(response.Content);

                Console.WriteLine($"Customer: {newCustomer.customer_id}");
            }
            else
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->registerCustomerOnZoho", 0, "An error has occurred inside registerCustomerOnZoho. Message -> " + " - " + response.Content.ToString(), response.StatusCode.ToString(), 0, string.Empty, 0));
                System.Diagnostics.Debug.WriteLine($"Error: {response.StatusCode} - {response.StatusDescription} - {response.Content.ToString()}");
            }
        }

        private void updateUserForCustomerId(HttpContext context)
        {
            var loggedInUserId = Convert.ToInt32(context.Request["loggedInUserId"]);
            var customerId = Convert.ToString(context.Request["customerId"]);

            this._iwbRequest = new IwbRequest { UserId = loggedInUserId, CustomerIdZoho = customerId };
            this._iwbResponse = new IwbService().SetCustomerIdForUser(this._iwbRequest);
        }

       
        private void CreateZohoInvoice(HttpContext context)
        {
            try
            {
                
                string jsonBody;
                using (var reader = new StreamReader(context.Request.InputStream))
                {
                    jsonBody = reader.ReadToEnd();
                }

                var wrapper = JsonConvert.DeserializeObject<Dictionary<string, object>>(jsonBody);

                if (wrapper != null && wrapper.ContainsKey("Data"))
                {
                    var rawData = wrapper["Data"].ToString();
                    var invoicePayload = JsonConvert.DeserializeObject<ZohoInvoicePayload>(rawData);

                    if (invoicePayload != null)
                    {
                        // Wrap into IwbRequest
                        var request = new IwbRequest
                        {
                            TenantId = 0,
                            ObjectData = invoicePayload
                        };

                        //var response = new IwbService().CreateZohoInvoice(request);

                        getZohoToken(); 

                        var accessToken = _zohoToken.AccessToken;
                        var orgId = _zohoOrganizationId; 
                        var endpoint = $"{_zohoBaseUrlBilling}/invoices";

                        request.ContextData = new Dictionary<string, object>
                            {
                                { "AccessToken", accessToken },
                                { "OrgId", orgId },
                                { "Endpoint", endpoint }
                            };

                        var response = new IwbService().CreateZohoInvoice(request);


                        if (response != null && !string.IsNullOrEmpty((string)response.Result))
                        {
                            var invoiceJson = response.Result;
                            dynamic invoiceObj = JsonConvert.DeserializeObject((string)invoiceJson);
                            string invoiceUrl = invoiceObj?.invoice?.invoice_url;
                            string invoiceId = invoiceObj?.invoice?.invoice_id;

                            context.Response.ContentType = "application/json";
                            context.Response.StatusCode = (int)HttpStatusCode.OK;
                            context.Response.Write(JsonConvert.SerializeObject(new
                            {
                                Status = true,
                                InvoiceId = invoiceId,
                                InvoiceUrl = invoiceUrl,
                                Data = invoiceJson
                            }));
                        }
                        else
                        {
                            WriteJsonResponse("{\"success\": false, \"message\": \"Empty or null result from CreateZohoInvoice.\"}", HttpStatusCode.InternalServerError, "Error");
                        }
                    }
                    else
                    {
                        WriteJsonResponse("{\"error\":\"Failed to deserialize invoice payload.\"}", HttpStatusCode.BadRequest, "Bad Request");
                    }
                }
                else
                {
                    WriteJsonResponse("{\"error\":\"Missing 'Data' in request.\"}", HttpStatusCode.BadRequest, "Bad Request");
                }
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "CreateZohoInvoice", 0, "Error: " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(JsonConvert.SerializeObject(new
                {
                    success = false,
                    message = "Exception: " + ex.Message
                }));
            }
        }

        private void GetZohoTransactionHistory(HttpContext context)
        {
            try
            {
                string customerId = context.Request["customerId"];

                if (string.IsNullOrEmpty(customerId))
                {
                    customerId = "2717252000000046003"; 
                }

                this._iwbRequest = new IwbRequest
                {
                    TenantId = TenantId,
                    UserId = SessionHandler.UserInformation.UserNum,
                    CustomerId = customerId
                };

                this._iwbResponse = new IwbService().GetZohoTransactionHistory(this._iwbRequest);

                context.Response.Write(JsonConvert.SerializeObject(new
                {
                    success = true,
                    message = "Transaction history fetched successfully.",
                    data = this._iwbResponse.TransactionHistory
                }));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "GetZohoTransactionHistory", TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                context.Response.Write(JsonConvert.SerializeObject(new
                {
                    success = false,
                    message = "Error fetching transaction history: " + ex.Message
                }));
            }
        }


        private void getCustomerFromZoho(HttpContext context)
        {
            var customerId = Convert.ToString(context.Request["customerId"]);

            getZohoToken();

            var client = new RestClient(_zohoBaseUrlBilling);
            var request = new RestRequest($"/customers/{customerId}", Method.GET);

            request.AddHeader("Authorization", $"Zoho-oauthtoken {_zohoToken.AccessToken}");

            var response = client.Execute(request);
            if (response.IsSuccessful)
            {
                //var customer = JsonConvert.DeserializeObject<ZohoCustomer>(response.Content);
                var zohoResponse = JsonConvert.DeserializeObject<ZohoCustomerResponse>(response.Content);
                System.Diagnostics.Debug.WriteLine($"Customer: {zohoResponse.Customer?.DisplayName} - {zohoResponse.Customer?.CustomerId}");

                context.Response.Write(JsonConvert.SerializeObject(
                    new
                    {
                        success = true,
                        message = "Customer Fetched successfully",
                        data = zohoResponse.Customer
                    }));
            }
            else
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Handler, "IwbHandler->getCustomerFromZoho", 0, "An error has occurred inside getCustomerFromZoho. Message -> " + " - " + response.Content.ToString(), response.StatusCode.ToString(), 0, string.Empty, 0));
                System.Diagnostics.Debug.WriteLine($"Error: {response.StatusCode} - {response.StatusDescription} - {response.Content.ToString()}");

                context.Response.Write(JsonConvert.SerializeObject(
                    new
                    {
                        success = false,
                        message = "Fail to fetvh the customer",
                        data = response.Content.ToString()
                    }));
            }
        }



        #endregion
    }

    public class HeaderInformation
    {
        public StandardCode StandardCodes { get; set; }
        public List<string> UNS { get; set; }
        public List<string> GRADE { get; set; }
    }
    public class StandardCode
    {
        public List<string> ASME { get; set; }
        public List<string> ASTM { get; set; }
    }
}