<%@ Page Title="MMS::Add New Document" Language="C#" MasterPageFile="~/MasterPages/2.0/2Columns.Master" AutoEventWireup="true" CodeBehind="AddDocument.aspx.cs" Inherits="RevCord.VoiceRec.WebUIClient.Iwb.AddDocument1" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">

    <link href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/min/dropzone.min.css" rel="stylesheet" />

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">

    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- ✅ Then jQuery-dependent libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/min/dropzone.min.js"></script>

    <!-- ✅ PDF.js comes last -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.14.305/pdf.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.12.313/pdf.min.js"></script>

    <link href="<%= Page.ResolveClientUrl("~/asset/lib/select2/css/select2.min.css") %>" rel="stylesheet" />
    <link href="<%= Page.ResolveClientUrl("~/asset/lib/select2-bootstrap-5-theme/select2-bootstrap-5-theme.min.css") %>" rel="stylesheet" />

    <style>
        .wizard-steps {
            display: flex;
            gap: 20px;
            margin: 30px 20px;
            flex-wrap: wrap;
        }

        .wizard-step {
            position: relative;
            display: inline-block;
            padding: 12px 20px;
            min-width: 160px;
            text-align: center;
            background: linear-gradient(to right, #f0f0f0, #e0e0e0);
            clip-path: polygon(0 0, 90% 0, 100% 50%, 90% 100%, 0 100%);
            color: #999;
            font-size: 14px;
            font-weight: normal;
            cursor: pointer;
        }

            .wizard-step label {
                display: block;
                font-weight: bold;
                color: #999;
                margin-bottom: 0;
            }

            .wizard-step span {
                font-size: 13px;
                color: #999;
            }

            .wizard-step.active {
                background: linear-gradient(to right, #e6f0fc, #d6e9f9);
            }

                .wizard-step.active label {
                    color: #0056b3;
                }

                .wizard-step.active span {
                    color: darkred;
                }
            /* Grayed out and non-clickable by default */
            /* .wizard-step:not(.active) {
                opacity: 0.6;
                pointer-events: none;
                cursor: default;
            } */

            /* Optional if you want a more reusable 'disabled' class */
            .wizard-step.disabled {
                opacity: 0.6;
                pointer-events: none;
                cursor: default;
            }

        .step-section {
            padding: 13px;
            margin: 0 20px;
            border: 1px solid #ccc;
            border-radius: 6px;
            background-color: #f9f9f9;
        }

        .d-none {
            display: none !important;
        }

        canvas {
            display: block;
            margin: 0 auto;
        }

        #step2Content .col-md-4 {
            min-height: 450px; /* Match PDF preview height */
        }
        /* Upload Container Styles */

        .upload-container {
            border: 2px dashed #4682b4;
            border-radius: 6px;
            padding: 30px;
            margin-bottom: 20px;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 130px;
            height: 187px;
            /*  max-height: 200px;*/
        }

        .upload-icon {
            margin-bottom: 15px;
        }

        .upload-text {
            margin-bottom: 5px;
            font-size: 14px;
            color: #333;
        }

        .upload-info {
            font-size: 12px;
            color: #6c757d;
        }

        #uploadFileLink:hover {
            text-decoration: underline !important;
        }
        /* Hide default dropzone elements */

        .upload-container .dz-message,
        .upload-container .dz-preview,
        .upload-container .dz-default {
            display: none !important;
        }

        /* Ensure our custom content is visible */

        .upload-container .upload-icon,
        .upload-container .upload-text,
        .upload-container .upload-info {
            display: block !important;
        }

        /* PDF Container with Scrolling */

        .pdf-container {
            width: 100%;
            max-width: 900px;
            height: 600px;
            max-height: 548px;
            overflow: auto;
            background: #fff;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            position: relative;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }


            .pdf-container::-webkit-scrollbar {
                width: 12px;
                height: 12px;
            }

            .pdf-container::-webkit-scrollbar-track {
                background: #f1f1f1;
                border-radius: 6px;
            }

            .pdf-container::-webkit-scrollbar-thumb {
                background: #c1c1c1;
                border-radius: 6px;
            }

                .pdf-container::-webkit-scrollbar-thumb:hover {
                    background: #a8a8a8;
                }

        .pdf-preview-area {
            width: max-content;
            height: auto;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 10px;
        }

        canvas {
            display: block;
            margin: 0 auto;
        }

        .pdf-controls-unified {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 8px 16px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            display: inline-flex;
            align-items: center;
            gap: 12px;
        }

        #emptyState {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            min-height: 500px;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }

        .empty-state-icon {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        #emptyState p {
            font-size: 16px;
            color: #6c757d;
            margin: 0;
        }



        #reviewPanel input {
            width: 100px;
        }

        .header-bar {
            background-color: #0053A0;
            color: white;
            padding: 12px 20px;
            /*border-top-left-radius: 8px;
            border-top-right-radius: 8px;*/
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 600;
            font-size: 1.75rem;
            width: 96%;
            box-sizing: border-box;
            Margin-left: 30px;
        }

        .header-title i {
            margin-right: 8px;
        }

        .header-close {
            cursor: pointer;
            font-size: 20px;
            font-weight: normal;
        }



        .step4-controls {
            background: #e9ecef;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 64px;
            padding: 16px 0;
            border-radius: 8px;
            margin-top: 16px;
            font-family: 'Inter', 'Segoe UI', Arial, sans-serif;
        }

        .step4-btn {
            background: #dee2e6;
            border: none;
            border-radius: 8px;
            padding: 8px 24px;
            font-size: 1rem;
            color: #495057;
            font-family: inherit;
            font-weight: 400;
            box-shadow: none;
            transition: background 0.2s;
            cursor: pointer;
        }

            .step4-btn:hover, .step4-btn:focus {
                background: #d3d7db;
                outline: none;
            }

        .step4-page {
            font-size: 1.1rem;
            color: #495057;
            font-family: inherit;
            font-weight: 400;
            min-width: 120px;
            text-align: center;
            letter-spacing: 0.01em;
        }

        #text-compliance-summary-view table {
            /*table-layout: fixed;*/
        }

        #text-compliance-summary-view .badge {
            min-width: 50px;
            text-align: center;
        }

        @media screen and (max-width: 1366px) and (max-height: 768px) {
            .sidebar {
                height: 131.6vh !important;
            }
        }

        @media screen and (max-width: 1280px) and (max-height: 720px) {
            .sidebar {
                height: 142vh !important; /* or your preferred value */
            }
        }

        #txtDescription {
            resize: none;
            height: 80px; /* adjust as needed */
            width: 100%;
        }

        .limit-reached {
            border: 2px solid red !important;
        }

        .pdf-viewer-wrapper {
            max-width: 900px;
            margin: auto;
        }

        .pdf-container,
        #pdfControlsStep1 {
            width: 100%;
        }

        .wizard-step.disabled {
            opacity: 0.6;
            pointer-events: none;
            cursor: default;
        }

        .wizard-step.completed {
            opacity: 1;
            pointer-events: auto;
            cursor: pointer;
        }

        .pdf-container {
            height: 600px; /* or whatever you want */
            overflow: auto;
        }

        #text-compliance-summary-view {
            max-height: 600px; /* or whatever you want */
            overflow: auto;
        }
    </style>

</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphFilterContents" runat="server">
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="cphPageContents" runat="server">
    <div class="header-bar">
        <span class="header-title">
            <i class="fa fa-file-text-o" aria-hidden="true"></i>Add New Document
        </span>
        <span class="header-close" onclick="goToManageDocument()">
            <i class="fa fa-times-circle" aria-hidden="true"></i>
        </span>
    </div>
    <div class="container-fluid mt-4" style="max-width: 100%;">
        <!-- Step Wizard -->
        <div class="wizard-steps">
            <div class="wizard-step active" data-step="1">
                <label>Step 1</label>
                <span>Document</span>
            </div>
            <div class="wizard-step" data-step="2">
                <label>Step 2</label>
                <span>Code Selection</span>
            </div>
            <div class="wizard-step" data-step="3">
                <label>Step 3</label>
                <span>HeatNo Selection</span>
            </div>
            <div class="wizard-step" data-step="4">
                <label>Step 4</label>
                <span>Status Summary</span>
            </div>
            <div class="wizard-step" data-step="5">
                <label>Step 5</label>
                <span>Validation Report</span>
            </div>
        </div>

        <!-- Step 1 -->
        <div class="step-section" id="step1Content">
            <div class="row">
                <!-- PDF Viewer -->
                <div class="col-md-8">
                    <div class="pdf-viewer-wrapper" style="max-width: 900px; margin: auto;">
                        <div class="pdf-container" style="width: 100%;">
                            <div id="pdfPreviewArea" class="pdf-preview-area text-center text-muted">
                                <canvas id="pdfCanvas"></canvas>
                                <div id="emptyState"
                                    class="d-flex flex-column justify-content-center align-items-center h-100 w-100 position-absolute top-0 start-0 text-muted"
                                    style="display: none;">
                                    <div class="empty-state-icon mb-3">
                                        <i class="fas fa-file-upload" style="font-size: 48px; color: #6c757d;"></i>
                                    </div>
                                    <p class="m-0">Upload a PDF to begin</p>
                                </div>
                            </div>
                        </div>
                        <div id="pdfControlsStep1" class="step4-controls d-flex flex-wrap justify-content-center gap-2 mt-2" style="width: 100%;">
                            <button type="button" id="zoomOutStep1" class="step4-btn">Zoom Out</button>
                            <span id="pageCountStep1" class="step4-page align-self-center">Page 0 of 0</span>
                            <button type="button" id="zoomInStep1" class="step4-btn">Zoom In</button>
                            <button type="button" id="prevPageStep1" class="step4-btn">Prev</button>
                            <button type="button" id="nextPageStep1" class="step4-btn">Next</button>
                        </div>
                    </div>
                </div>

                <!-- Upload Form -->
                <div class="col-md-4 d-flex flex-column" style="height: 100%;">
                    <h6 class="mb-3">Upload Document</h6>
                    <div class="upload-container" id="uploadDropzone">
                        <div class="upload-icon">
                            <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI0OCIgaGVpZ2h0PSI0OCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM2YzZjNmMiIHN0cm9rZS13aWR0aD0iMS41Ij48cmVjdCB4PSIzIiB5PSIzIiB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHJ4PSIyIiByeT0iMiIvPjxjaXJjbGUgY3g9IjguNSIgY3k9IjguNSIgcj0iMS41Ii8+PHBvbHlsaW5lIHBvaW50cz0iMjEgMTUgMTYgMTAgNSAyMSIvPjwvc3ZnPg==" alt="Upload" width="48" height="48">
                        </div>
                        <div class="upload-text">
                            <a href="javascript:void(0);" id="uploadFileLink" style="color: #007bff; text-decoration: none;">Upload a file</a> or drag and drop
                        </div>
                        <div class="upload-info">
                            <span style="color: #6c757d; font-size: 12px;">PDF up to 10MB</span>
                        </div>

                        <div id="uploadedFileContainer" class="text-success mt-2 d-none d-flex align-items-center justify-content-between" style="word-break: break-all;">
                            <span id="uploadedFilename"></span>
                            <button type="button" id="removeUploadedFile" class="btn btn-sm text-danger ms-2" title="Remove">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>

                        <!-- 🔽 This is the error message element -->
                        <div id="uploadErrorMsg" class="text-danger mt-2" style="font-size: 13px;"></div>
                    </div>

                    <input type="file" id="fileInput" accept=".pdf" style="display: none;" />

                    <div class="form-group">
                        <label class="control-label">Name <span class="text-danger fw-bold">*</span> :</label>
                        <input id="txtName" type="text" placeholder="Document Name" class="form-control" maxlength="255" />
                        <div id="name-char-counter" class="text-muted small mb-1">0/255 characters</div>
                    </div>


                    <div id="add-validation-summary" class="validation-summary text-danger mb-3"></div>

                    <div class="form-group mt-3">
                        <label class="control-label">Document Type <span class="text-danger fw-bold">*</span> :</label>
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" value="1" name="rbDoc" id="rbDocType1" checked>
                                    <label class="form-check-label" for="rbDocType1">MTR</label>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" value="3" name="rbDoc" id="rbDocType3">
                                    <label class="form-check-label" for="rbDocType3">WPS</label>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" value="4" name="rbDoc" id="rbDocType4">
                                    <label class="form-check-label" for="rbDocType4">PQR</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mt-3">
                        <label class="control-label">Description</label>
                        <textarea id="txtDescription" class="form-control" placeholder="Description" maxlength="255"></textarea>
                        <div id="desc-char-counter" class="text-muted small mb-1">0/255 characters</div>
                        <div id="desc-validation-summary" class="validation-summary text-danger mb-3"></div>
                    </div>

                    <div class="mt-auto modal-footer">
                        <button type="button" class="btn btn-primary w-100" id="btnTryAI">Run MTR AI Validation</button>
                  <%--  </div>
                     <button type="button" class="btn btn-warning w-100 mt-2" id="btnTestPayment">Test Payment Flow</button>
                </div>--%>
            </div>
        </div>

        <!-- Step 2 -->
        <%--<div class="step-section d-none" id="step2Content">
    <div class="row">
      <!-- PDF Viewer -->
      <div class="col-md-8 d-flex flex-column align-items-center justify-content-start border rounded p-3" style="min-height: 450px;">
        <canvas id="pdfCanvasStep2"></canvas>
      </div>

      <!-- Heat Number List -->
      <!-- Right Column: Heat Number List -->
<div class="col-md-4 d-flex flex-column justify-content-between" style="min-height: 100%;">
  <div>
    <h6>Select Heat Numbers</h6>
    <p class="text-muted">The AI found heat numbers. Select which to include in the review.</p>
    <div id="heatNumberList" class="mb-3"></div>
  </div>
  <div class="mt-auto">
    <button class="btn btn-primary w-100" id="btnContinueReview">Continue to Review</button>
  </div>
</div>

    </div>
  </div>--%>

        <!-- Step 2 -->
        <div class="step-section d-none" id="step2Content">
            <div class="row">
                <!-- PDF Viewer -->
                <div class="col-md-8">
                    <div class="pdf-container">
                        <div class="pdf-preview-area text-center">
                            <canvas id="pdfCanvasStep2"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Right Column: CodeInfo List Only -->
                <div class="col-md-4 d-flex flex-column justify-content-between" style="min-height: 100%;">
                    <div>
                        <!-- ✅ CodeInfo Checkboxes Section -->
                        <h6>Select Code Info</h6>
                        <p class="text-muted">Select one or more Code Info values identified by AI.</p>
                        <div id="codeInfoList" class="mb-3"></div>
                    </div>
                    <div class="mt-auto">
                        <button type="button" class="btn btn-primary w-100" id="btnContinueReview">Continue to Review</button>
                    </div>
                </div>
            </div>
        </div>


        <!-- Step 3 -->
        <div class="step-section d-none" id="step3Content">
            <div class="row">

                <!-- Left: PDF Viewer -->

                <div class="col-md-8">
                    <div class="pdf-container">
                        <div class="pdf-preview-area text-center">
                            <canvas id="pdfCanvasStep3"></canvas>
                        </div>
                    </div>
                </div>
                <%-- <div class="col-md-8 d-flex flex-column align-items-center justify-content-start border rounded p-3" style="min-height: 450px;">
                    <canvas id="pdfCanvasStep3"></canvas>
                </div>--%>

                <!-- Right: Heat Number List -->
                <div class="col-md-4 d-flex flex-column justify-content-between" style="min-height: 100%;">
                    <div>
                        <h6>Select Heat Numbers</h6>
                        <p class="text-muted" id="heatNoSummaryStep3"></p>
                        <div id="heatNumberListStep3" class="mb-3"></div>
                    </div>
                    <!-- Optionally: Add a button -->
                    <div class="mt-auto">
                        <button type="button" class="btn btn-primary w-100" id="btnContinueToStep4">Continue to Review</button>
                    </div>
                </div>

            </div>
        </div>
        <div class="step-section d-none" id="step4Content" style="height: 654px;">
            <div class="row" style="height: 700px;">

                <!-- Left Panel -->
                <div class="col-md-6 d-flex flex-column border rounded p-3" style="height: 91%;">
                    <!-- PDF Viewer -->
                    <div class="pdf-container flex-grow-1 overflow-auto border">
                        <div class="pdf-preview-area text-center">
                            <canvas id="pdfCanvasStep4" style="display: block; margin: 0 auto;"></canvas>
                        </div>
                    </div>

                    <!-- PDF Controls -->
                    <div id="pdfControlsStep4" class="d-flex flex-wrap justify-content-center gap-2 mt-3">
                        <button type="button" id="zoomOutStep4" class="step4-btn">Zoom Out</button>
                        <span id="pageCountStep4" class="step4-page align-self-center">Page 1 of 1</span>
                        <button type="button" id="zoomInStep4" class="step4-btn">Zoom In</button>
                        <button type="button" id="prevPageStep4" class="step4-btn">Prev</button>
                        <button type="button" id="nextPageStep4" class="step4-btn">Next</button>
                    </div>
                </div>

                <!-- ✅ Right Panel -->
                <div class="col-md-6 d-flex flex-column border rounded p-3" style="height: 91%; max-width: 800px;">
                    <!-- Compliance Summary Scrollable Area -->
                    <div id="text-compliance-summary-view" class="flex-grow-1 overflow-auto">
                        <div id="compliance-summary-container"></div>
                        <div id="inline-compliance-detail-panel" class="mt-3"></div>
                    </div>

                    <!-- ✅ Finalize Button aligned with PDF controls row -->
                    <div class="mt-auto">
                        <button type="button" class="btn btn-primary w-100" id="btnContinueToStep5">Continue to Review</button>
                    </div>

                </div>
            </div>
        </div>


        <!-- Step 5 -->
        <div class="step-section d-none" id="step5Content">
            <div class="d-flex flex-column bg-light p-3 overflow-auto" style="min-height: 200px; height: calc(-100px + 100vh); overflow: hidden auto; max-height: 600px;">
                <div id="div-rptvalidation">
                    Validation Report
                </div>
                <div class="d-flex justify-content-center mt-3">
                    <button id="save-doc" type="button" class="btn btn-primary">Finish and Save</button>
                </div>
            </div>
        </div>

        <!-- Summary View -->
        <!-- Compliance Edit Modal -->
        <div id="complianceModal" class="modal" style="display: none;">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Compliance Review</h5>
                        <button type="button" class="btn-close" onclick="closeComplianceModal()"></button>
                    </div>
                    <div class="modal-body d-flex" style="min-height: 500px;">
                        <!-- Left PDF Viewer -->
                        <div id="modalPdfViewer" class="w-50 border-end p-3" style="overflow-y: auto;"></div>


                        <!-- Right Info Panel -->
                        <div class="w-50 p-3">
                            <a href="#" onclick="backToSummary()" class="text-primary d-block mb-3">&larr; Back to Summary</a>
                            <h5 id="modalSectionTitle" class="fw-bold"></h5>
                            <p id="modalSubheading" class="text-muted small mb-3"></p>

                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>PROPERTY</th>
                                        <th>VALUE FOUND</th>
                                        <th>REQUIRED</th>
                                        <th>STATUS</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr id="modalRow">
                                        <td id="modalProperty" class="fw-bold"></td>
                                        <td>
                                            <input id="modalInputValue" class="form-control" type="text" /></td>
                                        <td id="modalStandard" class="text-muted"></td>
                                        <td><span id="modalStatusBadge" class="badge"></span></td>
                                    </tr>
                                </tbody>
                            </table>

                            <hr />
                            <h6>Actions for: <span id="modalActionName"></span></h6>
                            <button class="btn btn-primary" onclick="confirmCorrection(event)">Confirm Correction</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</asp:Content>
<asp:Content ID="Content4" ContentPlaceHolderID="cphPageScripts" runat="server">
    <script src="<%= Page.ResolveClientUrl("~/assets/js/jQuery/jquery-1.8.2.min.js") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src='<%= Page.ResolveClientUrl("~/assets/js/localization/lang.js") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>' type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/jQuery/jquery-ui-custom-mmsDialog.js") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/Scripts/jsnlog.min.js") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/jQuery/jquery.signalR-2.1.2.min.js") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/signalr/hubs") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/documentReady.js") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/jQuery/jquery.blockUI.js") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/jQuery/jquery.cookie.js") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/Store_JS/store.min.js") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/Store_JS/store_json2.min.js") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/maps/markerclusterer_compiled.js")+ RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/maps/mapwithmarker.js")+ RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/tables/jquery.dataTables.Search.js") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/tables/dataTables.colReorderWithResize.js") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/common/colorPick.js") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/common/BookmarkFlagcolorPick.js") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%=Page.ResolveClientUrl("../assets/js/common/dialogManager.js") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>

    <script src="<%= Page.ResolveClientUrl("~/assets/js/TimelineView/custom.js")+ RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/TimelineView/timeline.js")+ RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/TimelineView/underscore-min.js") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <link href="../assets/css/TimelineView/vis.css" rel="stylesheet" type="text/css" />

    <script src="<%= Page.ResolveClientUrl("~/assets/js/ajax/xmlhttpRequestionMethod.js") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/jQuery/jquery.checkboxtree.js") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/jQuery/jquery.tooltip.min.js") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/uploadify/jquery.uploadify.v2.1.4.min.js") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/uploadify/swfobject.js") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/AES/pbkdf2.js") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/AES/aes.js") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src='<%= Page.ResolveClientUrl("~/assets/js/common/revview.js") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>' type="text/javascript"></script>
    <script>
        Dropzone.autoDiscover = false;

        let pdfDoc = null;
        let pdfDocData = null;
        let pageNum = 1;
        let pageCount = 0;
        let scale = 1.2;
        const canvas = document.getElementById('pdfCanvas');
        const ctx = canvas.getContext('2d');

        function getOriginalRotation(page) {
            return page.rotate || 0;
        }

        function renderPage(num) {
            pdfDoc.getPage(num).then(function (page) {
                const rotation = getOriginalRotation(page);
                const viewport = page.getViewport({ scale: scale, rotation: rotation });
                canvas.height = viewport.height;
                canvas.width = viewport.width;
                page.render({ canvasContext: ctx, viewport });
                document.getElementById('pageCountStep1').textContent = `Page ${pageNum} of ${pageCount}`;
                $('#pageCountStep1').show();
            });
        }

        function renderStep2PDF() {
            if (!pdfDoc) return;
            const canvasStep2 = document.getElementById('pdfCanvasStep2');
            const ctx2 = canvasStep2.getContext('2d');
            pdfDoc.getPage(1).then(function (page) {
                const rotation = getOriginalRotation(page);
                const viewport = page.getViewport({ scale: scale, rotation: rotation });
                canvasStep2.width = viewport.width;
                canvasStep2.height = viewport.height;
                page.render({ canvasContext: ctx2, viewport });
            });
        }

        function renderStep3PDF() {
            //debugger
            console.log('console.log 02')
            if (!pdfDocData) return;
            const canvasStep3 = document.getElementById('pdfCanvasStep3');
            const ctx3 = canvasStep3.getContext('2d');
            pdfDoc.getPage(1).then(function (page) {
                const rotation = getOriginalRotation(page);
                const viewport = page.getViewport({ scale: scale, rotation: rotation });
                canvasStep3.width = viewport.width;
                canvasStep3.height = viewport.height;
                page.render({ canvasContext: ctx3, viewport });
            });
        }

        let pageNumStep4 = 1;
        let scaleStep4 = 1.2;
        let pdfDocStep4 = null;
        let pageCountStep4 = 0;

        function renderStep4PDF() {
            if (!pdfDocData) return;
            const canvasStep4 = document.getElementById('pdfCanvasStep4');
            const ctx4 = canvasStep4.getContext('2d');
            pdfjsLib.getDocument({ data: pdfDocData }).promise.then(function (pdf) {
                pdfDocStep4 = pdf;
                pageCountStep4 = pdf.numPages;
                if (pageNumStep4 > pageCountStep4) pageNumStep4 = 1;
                pdf.getPage(pageNumStep4).then(function (page) {
                    const viewport = page.getViewport({ scale: scaleStep4, rotation: page.rotate });
                    canvasStep4.width = viewport.width;
                    canvasStep4.height = viewport.height;
                    page.render({ canvasContext: ctx4, viewport }).promise.then(() => {
                        updateStep4Controls();
                    });
                });
            });
        }

        function updateStep4Controls() {
            $('#pageCountStep4').text(`Page ${pageNumStep4} of ${pageCountStep4}`);
        }


        $('#prevPageStep4').on('click', function () {
            if (pageNumStep4 <= 1) return;
            pageNumStep4--;
            renderStep4PDF();
        });

        $('#nextPageStep4').on('click', function () {
            if (pageNumStep4 >= pageCountStep4) return;
            pageNumStep4++;
            renderStep4PDF();
        });

        $('#zoomInStep4').on('click', function () {
            if (scaleStep4 < 4.0) {
                scaleStep4 += 0.2;
                renderStep4PDF();
            }
        });

        $('#zoomOutStep4').on('click', function () {
            if (scaleStep4 > 0.4) {
                scaleStep4 -= 0.2;
                renderStep4PDF();
            }
        });

        $('#prevPageStep1').on('click', function () {
            if (pageNum <= 1) return;
            pageNum--;
            renderPage(pageNum);
        });

        $('#nextPageStep1').on('click', function () {
            if (pageNum >= pageCount) return;
            pageNum++;
            renderPage(pageNum);
        });

        $('#zoomInStep1').on('click', function () {
            if (scale < 4.0) {
                scale += 0.2;
                renderPage(pageNum);
            }
        });

        $('#zoomOutStep1').on('click', function () {
            if (scale > 0.4) {
                scale -= 0.2;
                renderPage(pageNum);
            }
        });

        function populateHeatNumbers(heatNumbers) {
            const container = $('#heatNumberList');
            container.empty();
            if (!heatNumbers || heatNumbers.length === 0) {
                container.append('<p class="text-muted">No heat numbers found.</p>');
                return;
            }

            heatNumbers.forEach((heat, index) => {
                const id = `heat_${index}`;
                container.append(`
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="${heat}" id="${id}" checked>
                    <label class="form-check-label" for="${id}">${heat}</label>
                  </div>
                `);
            });
        }


        //$(document).ready(function () {
        //    console.log('console.log 03')
        //    debugger;
        //    $('.wizard-step').on('click', function () {
        //        const step = $(this).data('step');
        //        $('.wizard-step').removeClass('active');
        //        $(this).addClass('active');
        //        $('.step-section').addClass('d-none');
        //        $('#step' + step + 'Content').removeClass('d-none');

        //        if (step === 2) {
        //            console.log('console.log 04')
        //            renderStep2PDF();

        //            $.ajax({
        //                url: '/api/getHeatNumbers', // your endpoint
        //                method: 'POST',
        //                contentType: 'application/json',
        //                data: JSON.stringify({ filename: uploadedFilename }), // or other input
        //                success: function (res) {
        //                    populateHeatNumbers(res.heatNumbers);
        //                },
        //                error: function () {
        //                    populateHeatNumbers([]);
        //                }
        //            });
        //        }
        //        else if (step === 3) {
        //            renderStep3PDF();
        //        } else if (step === 4) {
        //            renderStep4PDF();
        //        }

        //    });

        $(document).ready(function () {
            console.log('console.log 03')
            $('.wizard-step').on('click', function () {
                if ($(this).hasClass('disabled')) {
                    return false; // Prevent click if disabled
                }
                const step = $(this).data('step');
                setActiveStep(step);
            });

            const myDropzone = new Dropzone("#uploadDropzone", {
                url: "#",
                maxFiles: 1,
                maxFilesize: 10, // MB
                acceptedFiles: ".pdf",
                autoProcessQueue: true,
                addRemoveLinks: false,
                clickable: "#uploadFileLink",
                dictDefaultMessage: "",
                previewTemplate: '<div style="display:none;"></div>',

                init: function () {
                    this.on("addedfile", function (file) {
                        const errorMsgEl = document.getElementById('uploadErrorMsg');
                        errorMsgEl.textContent = ""; // Clear old messages

                        // Validate type
                        if (file.type !== "application/pdf") {
                            this.removeFile(file);
                            errorMsgEl.textContent = "Only PDF files are allowed.";
                            return;
                        }

                        // Validate size
                        if (file.size > 10 * 1024 * 1024) {
                            this.removeFile(file);
                            errorMsgEl.textContent = "File size exceeds the 10 MB limit.";
                            return;
                        }

                        // Remove previous file if more than 1
                        if (this.files.length > 1) {
                            this.removeFile(this.files[0]);
                        }

                        $('#btnTryAI').prop('disabled', false);

                        const reader = new FileReader();
                        reader.onload = function (e) {
                            pdfDocData = new Uint8Array(e.target.result);

                            // Destroy previous doc if exists
                            if (pdfDoc !== null) {
                                pdfDoc.destroy();
                                pdfDoc = null;
                            }

                            ctx.clearRect(0, 0, canvas.width, canvas.height);
                            canvas.style.display = 'block';
                            document.getElementById('emptyState')?.classList.add('d-none');

                            // Load PDF
                            pdfjsLib.getDocument({ data: pdfDocData }).promise.then(function (pdf) {
                                pdfDoc = pdf;
                                pageCount = pdf.numPages;
                                pageNum = 1;
                                renderPage(pageNum);
                            });
                        };
                        reader.readAsArrayBuffer(file);

                        $('#uploadedFilename').text(file.name);
                        $('#uploadedFileContainer').removeClass("d-none");
                        $('#btnTryAI').data('uploadedFilename', file.name);
                    });

                    this.on("removedfile", function () {
                        ctx.clearRect(0, 0, canvas.width, canvas.height);
                        canvas.style.display = 'none';
                        document.getElementById('emptyState')?.classList.remove('d-none');
                        $('#uploadedFilename').text('');
                        $('#uploadedFileContainer').addClass("d-none");
                        $('#btnTryAI').prop('disabled', true);
                        document.getElementById('pageCountStep1').textContent = '';
                        $('#pageCountStep1').hide();
                    });
                }
            });

            // Manual remove (trash button)
            document.getElementById('removeUploadedFile')?.addEventListener('click', function () {
                if (myDropzone.files.length > 0) {
                    Swal.fire({
                        title: "Are you sure?",
                        text: "You want to delete the uploaded document.",
                        icon: "warning",
                        showCancelButton: true,
                        confirmButtonColor: "#3085d6",
                        cancelButtonColor: "#d33",
                        confirmButtonText: "Yes, delete it!"

                    }).then((result) => {
                        if (result.isConfirmed) {
                            myDropzone.removeFile(myDropzone.files[0]);
                            // ✅ Optional: show success toast after removal
                            Swal.fire({
                                title: "Deleted!",
                                text: "The uploaded document has been removed.",
                                icon: "success",
                                timer: 1500,
                                showConfirmButton: false
                            });
                        }
                    });
                }
            });



            //// Manual remove button
            //$('#removeUploadedFile').on('click', function () {
            //    if (myDropzone.files.length > 0) {
            //        myDropzone.removeFile(myDropzone.files[0]);
            //    }
            //});


        });




        //function setActiveStep(step) {
        //    debugger
        //    console.log('console.log 05')
        //    $('.wizard-step').removeClass('active completed');

        //    $('.wizard-step').each(function () {
        //        const currentStep = $(this).data('step');
        //        if (currentStep < step) {
        //            $(this).addClass('completed');
        //        } else if (currentStep === step) {
        //            $(this).addClass('active');
        //        }
        //    });

        //    $('.step-section').addClass('d-none');
        //    $('#step' + step + 'Content').removeClass('d-none');

        //    if (step === 2) renderStep2PDF();
        //    else if (step === 3) renderStep3PDF();
        //    else if (step === 4) renderStep4PDF();
        //    else if (step === 5) {
        //        console.log('create MTR validation report');
        //        debugger
        //        if (mtrResponse != null) {
        //            createValidationReportMTR(mtrResponse);
        //        }
        //    }
        //}

        function setActiveStep(step) {
            currentStep = step;
            $('.wizard-step').removeClass('active completed disabled');
            $('.wizard-step').each(function () {
                const s = $(this).data('step');
                if (s < step) {
                    $(this).addClass('completed'); // show as completed
                }
                if (s > step) {
                    $(this).addClass('disabled'); // disable future steps
                }
                if (s === step) {
                    $(this).addClass('active'); // mark current as active
                }
            });

            $('.step-section').addClass('d-none');
            $('#step' + step + 'Content').removeClass('d-none');
            if (step === 2) renderStep2PDF();
            else if (step === 3) renderStep3PDF();
            else if (step === 4) renderStep4PDF();

        }

        $(document).ready(function () {
            //debugger;
            console.log('console.log 06')
            $('.wizard-step').on('click', function () {
                const step = $(this).data('step');
                setActiveStep(step);
            });

            $('#btnContinueReview').on('click', function () {
                debugger;
                console.log('console.log 07')
                const selectedCodes = $('.codeinfo-checkbox:checked').map(function () {
                    return $(this).val();
                }).get();

                if (selectedCodes.length === 0) {
                    alert('Please select at least one Code Info value.');
                    return;
                }

                const $heatContainer = $('#heatNumberListStep3').empty();
                $('#heatNoSummaryStep3').text(`The AI found ${globalHeatNumbers.length} heat number${globalHeatNumbers.length !== 1 ? 's' : ''}. Select which to include in the review.`);

                if (globalHeatNumbers.length === 0) {
                    $heatContainer.append('<p class="text-muted">No heat numbers detected.</p>');
                } else {
                    globalHeatNumbers.forEach((heat, idx) => {
                        const id = `step3_heat_${idx}`;
                        $heatContainer.append(`
                          <div class="form-check">
                            <input class="form-check-input step3-heat-checkbox" type="checkbox" value="${heat}" id="${id}" checked>
                            <label class="form-check-label" for="${id}">${heat}</label>
                          </div>
                        `);
                    });
                }

                setActiveStep(3);
            });

        });

        $(document).ready(function () {
            // Step change logic
            function showStep(stepNumber) {
                $('.step-section').addClass('d-none');
                $('#step' + stepNumber + 'Content').removeClass('d-none');
            }

            // Status toggle logic
            function setStatus(status) {
                if (status === 'Pass') {
                    $('#sulfurValue').removeClass('is-invalid').addClass('is-valid');
                    $('#passBtn').removeClass('btn-outline-success').addClass('btn-success');
                    $('#failBtn').removeClass('btn-danger').addClass('btn-outline-danger');
                } else {
                    $('#sulfurValue').removeClass('is-valid').addClass('is-invalid');
                    $('#failBtn').removeClass('btn-outline-danger').addClass('btn-danger');
                    $('#passBtn').removeClass('btn-success').addClass('btn-outline-success');
                }
            }

            // Review confirmation logic
            function confirmCorrection() {
                const newValue = $('#sulfurValue').val();
                alert('Correction Confirmed: Sulfur = ' + newValue);
                // Optional: Add AJAX to update backend
            }

            function flagForReview() {
                alert('Flagged for Engineer Review');
            }

            // Bind status buttons
            $('#passBtn').on('click', function () {
                setStatus('Pass');
            });

            $('#failBtn').on('click', function () {
                setStatus('Fail');
            });

            $('#btnConfirmCorrection').on('click', function () {
                confirmCorrection();
            });

            $('#btnFlagReview').on('click', function () {
                flagForReview();
            });

            // Show/hide edit panel logic
            $(document).on('click', '.btn-review', function () {
                const prop = $(this).data('prop');
                const spec = $(this).data('spec');
                const type = $(this).data('type');

                $('#editProp').text(prop);
                $('#editSpec').text(spec);
                $('#editPanel').removeClass('d-none');
                $('#complianceSummaryPanel').addClass('d-none');

                // Optionally prefill input
                $('#sulfurValue').val($(this).data('value'));
                setStatus($(this).data('status'));
            });

            $('#btnBackToSummary').on('click', function () {
                $('#editPanel').addClass('d-none');
                $('#complianceSummaryPanel').removeClass('d-none');
            });
        });
        function goToManageDocument() {
            // Redirects to ManageDocument.aspx
            window.location.href = 'ManageDocuments.aspx';
        }
        $(document).ready(function () {
            function restrictInput($input, $summary, $counter, fieldName) {
                function updateCounter() {
                    var val = $input.val();
                    $counter.text(val.length + '/255 characters');
                }

                $input.on('input', function (e) {
                    var val = $input.val();
                    if (val.length > 255) {
                        $input.val(val.substring(0, 255));
                        $summary.text(fieldName + ' cannot exceed 255 characters.').show();
                    } else {
                        $summary.text('');
                    }
                    updateCounter();

                });

                $input.on('paste', function (e) {
                    var paste = (e.originalEvent || e).clipboardData.getData('text');
                    var current = $input.val();
                    if ((current.length + paste.length) > 255) {
                        e.preventDefault();
                        $summary.text(fieldName + ' cannot exceed 255 characters.').show();
                        $input.val((current + paste).substring(0, 255));
                    }
                    setTimeout(updateCounter, 0);
                });

                // Initialize counter on page load
                updateCounter();

            }

            restrictInput($('#txtName'), $('#add-validation-summary'), $('#name-char-counter'), 'Name');

            restrictInput($('#txtDescription'), $('#desc-validation-summary'), $('#desc-char-counter'), 'Description');

        });



    </script>
    <script src='../assetsNew/js/plugins/jquery.pnotify.js'></script>
    <script src='../assetsNew/js/plugins/jquery.sparkline.min.js'></script>
    <script src='../assetsNew/js/plugins/mwheelIntent.js'></script>
    <script src='../assetsNew/js/plugins/mousewheel.js'></script>
    <script src='../assetsNew/js/bootstrap/tab.js'></script>
    <script src='../assetsNew/js/bootstrap/dropdown.js'></script>
    <script src='../assetsNew/js/bootstrap/tooltip.js'></script>
    <script src='../assetsNew/js/bootstrap/collapse.js'></script>
    <script src='../assetsNew/js/bootstrap/scrollspy.js'></script>
    <script src='../assetsNew/js/bootstrap/popover.js'></script>
    <script src='../assetsNew/js/plugins/bootstrap-datepicker.js'></script>
    <script src='../assetsNew/js/bootstrap/transition.js'></script>
    <script src='../assetsNew/js/plugins/jquery.knob.js'></script>
    <script src='../assetsNew/js/plugins/jquery.flot.min.js'></script>
    <script src='../assetsNew/js/plugins/fullcalendar.js'></script>
    <script src='../assetsNew/js/plugins/chosen.jquery.min.js'></script>
    <script src='../assetsNew/js/plugins/colpick.js'></script>
    <script src='../assetsNew/js/plugins/moment-2.17.1.min.js'></script>
    <script src="../assetsNew/js/plugins/moment-duration-format.min.js"></script>
    <script type="text/javascript" src="../assetsNew/js/daterangepicker.min.js"></script>
    <script src='../assetsNew/js/bootstrap/modal.js'></script>
    <script src='../assetsNew/js/plugins/raphael-min.js'></script>
    <script src='../assetsNew/js/plugins/morris-0.4.3.min.js'></script>
    <script src='../assetsNew/js/plugins/justgage.1.0.1.min.js'></script>
    <script src='../assetsNew/js/plugins/jquery.maskedinput.min.js'></script>
    <script src='../assetsNew/js/plugins/jquery.maskmoney.js'></script>
    <script src='../assetsNew/js/plugins/summernote.js'></script>
    <script src='../assetsNew/js/plugins/jquery.validate.min.js'></script>
    <script src='../assetsNew/js/plugins/jquery.bootstrap.wizard.min.js'></script>
    <script src='../assetsNew/js/plugins/jscrollpane.min.js'></script>
    <script src='../assetsNew/js/application.js'></script>

    <script src="<%= Page.ResolveClientUrl("~/assets/js/common/masterPage.js") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/DropzoneJS/assets/dropzone.js") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/jQuery/jquery.contextmenu.js") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/pages/iwb.js") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/asset/lib/select2/js/select2.full.min.js") %>" type="text/javascript"></script>
    <script type="text/javascript" id="zsiqchat">var $zoho = $zoho || {}; $zoho.salesiq = $zoho.salesiq || { widgetcode: "3b92f7954c8a215562ad2d89fac735cf67d6b187d79a5d21dfe0c6a59a59d65f", values: {}, ready: function () { } }; var d = document; s = d.createElement("script"); s.type = "text/javascript"; s.id = "zsiqscript"; s.defer = true; s.src = "https://salesiq.zoho.com/widget"; t = d.getElementsByTagName("script")[0]; t.parentNode.insertBefore(s, t);</script>
</asp:Content>
