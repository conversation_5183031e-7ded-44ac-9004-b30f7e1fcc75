﻿<%@ Master Language="C#" AutoEventWireup="true" CodeBehind="2Columns.master.cs" Inherits="RevCord.VoiceRec.WebUIClient.MasterPages._2._0._2Columns" %>
<%@ Import Namespace="RevCord.Util" %>
<%@ Import Namespace="RevCord.VoiceRec.WebUIClient.Classes" %>
<!DOCTYPE html>

<html>
<head runat="server">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link href="<%= Page.ResolveClientUrl("~/asset/lib/bootstrap/css/bootstrap.css") %>" rel="stylesheet" type="text/css" />
    <link href="<%= Page.ResolveClientUrl("~/asset/lib/font-awesome/css/all.min.css") %>" rel="stylesheet" type="text/css" />
    <link href="<%= Page.ResolveClientUrl("~/asset/lib/bootstrap-datepicker/css/bootstrap-datepicker.min.css") %>" rel="stylesheet" />
    <link href="<%= Page.ResolveClientUrl("~/asset/lib/select2/css/select2.min.css") %>" rel="stylesheet" />
    <link href="<%= Page.ResolveClientUrl("~/asset/lib/select2-bootstrap-5-theme/select2-bootstrap-5-theme.min.css") %>" rel="stylesheet" />
    <link href="<%= Page.ResolveClientUrl("~/asset/lib/sweetalert2/sweetalert2.min.css") %>" rel="stylesheet" />
    <link href="<%= Page.ResolveClientUrl("~/asset/lib/toastr.js/toastr.min.css") %>" rel="stylesheet" />
    <link href="<%= Page.ResolveClientUrl("~/asset/lib/pnotify/brightTheme.min.css") %>" rel="stylesheet" />
    <link href="<%= Page.ResolveClientUrl("~/assets/css/jQuery/jquery-ui-custom-mmsDialog.css") %>" rel="stylesheet" />
    <link href="<%= Page.ResolveClientUrl("~/asset/css/site.css") %>" rel="stylesheet" type="text/css" />
    
    <title>MMS::</title>
    <link rel="shortcut icon" href="../../asset/images/favicon.ico" />
    <!--<link rel="shortcut icon" href="../../assets/images/favicon.ico" />-->
    <style type="text/css">
        /*.sidebar .fa-solid::before {
            border: 1px solid red;
        }*/
        .left-menu-icon {
            padding: .2em 0.4em;
        }
    </style>

    <asp:ContentPlaceHolder ID="head" runat="server">

        
    </asp:ContentPlaceHolder>
</head>
<body>
    <form id="form1" runat="server">

        <!-- Navbar -->
        <nav class="navbar navbar-expand-lg navbar-light bg-white p-0"><!--sticky-top-->
            <div class="container-fluid">
                <!-- Brand Logo -->
                <a class="navbar-brand">
                    <img src="<%=ResolveClientUrl("~/asset/images/logo.png")%>" alt="REVCORD Logo" class="img-fluid" style="max-width: 130px;">
                </a>

                <!-- Toggle Button for Mobile View -->
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation" id="navbarToggle">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <!-- Navbar Right Side -->
                <div class="collapse navbar-collapse" id="navbarNav">
                    <div class="d-flex align-items-center ms-auto">
                        <div class="d-flex gap-4  align-items-center">
                            <!-- Notification Icon -->
                            <%--<button class="btn btn-outline-secondary btn-sm">
                                🔔
                            </button>--%>
                            
                            <label id="lblLoginEmail" class="text-info"></label>

                            <!-- User Avatar -->
                            <div class="dropdown d-flex align-items-center">
                                <img
                                    src="<%=ResolveClientUrl("~/asset/images/download (1).jpg")%>" 
                                    alt="User"
                                    class="rounded-circle"
                                    width="40px"
                                    data-bs-toggle="dropdown"
                                    aria-expanded="false"
                                    style="cursor: pointer;">
                                <i
                                    class="bi bi-chevron-down ms-2"
                                    data-bs-toggle="dropdown"
                                    aria-expanded="false"
                                    style="cursor: pointer;"></i>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li>
                                      <a class="dropdown-item view-user-profile" href="#" data-bs-toggle="modal" id="btnProfile">
                                        Profile <i class="fa fa-chevron-right" style="margin-left: 79px;"></i>
                                      </a>
                                    </li>

<%--                                    <li>
                                        <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                                        Change Password <i class="fa fa-chevron-right"></i>
                                        </a>
                                    </li>
                                 <li>
                                 <div class="d-flex align-items-center justify-content-between px-3">
                                    <label class="mb-0" for="compactToggle">Compact</label>
                                    <div class="form-check form-switch m-0">
                                        <input style="margin-left:-2px" class="form-check-input" type="checkbox" id="compactToggle" aria-label="Compact Mode Toggle" />
                                    </div>
                                </div>
                                </li>

                                    <li>
                                        <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#disclaimerModal">Disclaimer <i class="fa fa-chevron-right float-end"></i>
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="#" data-bs-toggle="modal">Live Chat <i class="fa fa-chevron-right float-end"></i>
                                        </a>
                                    </li>--%>
                                    <li>
                                        <asp:LinkButton ID="lnkLogout" runat="server" OnClick="lnkLogout_Click" CssClass="dropdown-item" Text="Logout <i class='fa fa-chevron-right float-end'></i>"></asp:LinkButton>
                                    </li>

                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </nav>


        <div class="d-flex">
            <!-- Sidebar -->
            <div id="sidebar" class="sidebar mt-1" style="display: flex; flex-direction: column; border-radius: 0px 10px; padding: 10px; height: 88.6vh; width: 15%; overflow: auto;">
                <!-- Other Links -->
                <!--<a href="<%=ResolveClientUrl("~/Iwb/Dashboard.aspx")%>" class="d-block mb-2 mt-3"> <i class="fa-solid fa-signal"></i>&nbsp; Dashboard</a>
                <a href="<%=ResolveClientUrl("~/Iwb/ManageDocuments.aspx")%>" class="d-block mb-2 mt-3 lm-doc"> <i class="fa-solid fa-file"></i>&nbsp; Manage Document</a>
                <a href="<%=ResolveClientUrl("~/Iwb/ManageOrganizations.aspx")%>" class="d-block mb-2 mt-3 lm-org d-none"> <i class="fa-solid fa-address-card"></i>&nbsp; Manage Organization</a>
                <a href="<%=ResolveClientUrl("~/Iwb/ManageWelders.aspx")%>" class="d-block mb-2 mt-3 d-none"> <i class="fa-solid fa-user"></i>&nbsp; Manage Welder</a>
                <a href="<%=ResolveClientUrl("~/Iwb/ManageJobs.aspx")%>" class="d-block mb-2 mt-3 lm-job d-none"> <i class="fa-solid fa-briefcase"></i>&nbsp; Manage Jobs</a>
                <a href="<%=ResolveClientUrl("~/Iwb/ManageTests.aspx")%>" class="d-block mb-2 mt-3 lm-test d-none"> <i class="fa-regular fa-calendar-days"></i>&nbsp; Manage Test</a>
                <a href="<%=ResolveClientUrl("~/Iwb/WorkHistory.aspx")%>" class="d-block mb-2 mt-3 lm-work d-none"> <i class="fa-solid fa-people-arrows"></i>&nbsp; Work History</a>
                <a href="<%=ResolveClientUrl("~/Playlist/Default.aspx")%>" class="d-block mb-2 mt-3"> <i class="fa-solid fa-play"></i>&nbsp; Repository</a>
                <a href="<%=ResolveClientUrl("~/Iwb/MyWPQ.aspx")%>" class="d-block mb-2 mt-3 lm-mywpq" id="mywpqpage"> <i class="fa-solid fa-play"></i>&nbsp; My WPQ</a>
                <a href="<%=ResolveClientUrl("~/Reports/ContractReport.aspx")%>" class="d-block mb-2 mt-3 lm-report"> <i class="fa-solid fa-file"></i>&nbsp; Reports</a>-->


                <a id="lnkLmDashboard" runat="server" ClientIDMode="Static" visible="false" class="mb-2 mt-3">
                    <span class="left-menu-icon"><i class="fa-solid fa-signal fa-fw"></i></span>
                    Dashboard</a>
                <a id="lnkLmDocuments" runat="server" ClientIDMode="Static" visible="false" class="mb-2 mt-3 lm-doc">
                    <span class="left-menu-icon"><i class="fa-solid fa-file fa-fw"></i></span>
                    Manage Document</a>
                <a id="lnkLmOrganizations" runat="server" ClientIDMode="Static" visible="false" class="mb-2 mt-3 lm-org">
                    <span class="left-menu-icon"><i class="fa-solid fa-address-card fa-fw"></i></span>
                    Manage Organization</a>
                <a id="lnkLmWelders" runat="server" ClientIDMode="Static" visible="false" class="mb-2 mt-3">
                    <span class="left-menu-icon"><i class="fa-solid fa-user fa-fw"></i></span>
                    Manage Welder</a>
                <a id="lnkLmViewJobs" runat="server" ClientIDMode="Static" visible="false" class="mb-2 mt-3">
                    <span class="left-menu-icon"><i class="fa-solid fa-briefcase fa-fw"></i></span>
                    View Jobs</a>
                <a id="lnkLmJobs" runat="server" ClientIDMode="Static" visible="false" class="mb-2 mt-3 lm-job">
                    <span class="left-menu-icon"><i class="fa-solid fa-briefcase fa-fw"></i></span>
                    Manage Jobs</a>
                <a id="lnkLmTests" runat="server" ClientIDMode="Static" visible="false" class="mb-2 mt-3 lm-test">
                    <span class="left-menu-icon"><i class="fa-regular fa-calendar-days fa-fw"></i></span>
                    Manage Test</a>
                <a id="lnkLmViewTests" runat="server" ClientIDMode="Static" visible="false" class="mb-2 mt-3">
                    <span class="left-menu-icon"><i class="fa-solid fa-briefcase fa-fw"></i></span>
                    View Tests</a>
                <a id="lnkLmWorkHistory" runat="server" ClientIDMode="Static" visible="false" class="mb-2 mt-3 lm-work">
                    <span class="left-menu-icon"><i class="fa-solid fa-people-arrows fa-fw"></i></span>
                    Work History</a>
                <a id="lnkLmPlaylist" runat="server" ClientIDMode="Static" visible="false" class="mb-2 mt-3">
                    <span class="left-menu-icon"><i class="fa-solid fa-play fa-fw"></i></span>
                    Repository</a>
                <a id="lnkLmMyWpq" runat="server" ClientIDMode="Static" visible="false" class="mb-2 mt-3 lm-mywpq">
                    <span class="left-menu-icon"><i class="fa-solid fa-play fa-fw"></i></span>
                    My WPQ</a>
                <a id="lnkLmReport" runat="server" ClientIDMode="Static" visible="false" class="mb-2 mt-3 lm-report">
                    <span class="left-menu-icon"><i class="fa-solid fa-file fa-fw"></i></span>
                    Reports</a>
                <!-- Bottom links and button with margin-top auto to push to bottom -->
                <div class="mt-auto">
                    <a href="#" class="d-block mb-2 mt-3 d-none"> <i class="fa-solid fa-circle-play"></i> Demo</a>
                    <a href="#" class="d-block mb-2 mt-3 d-none"> <i class="fa-solid fa-question"></i> Help</a>
                    <button id="cancelButton" class="btn btn-cancel" style="bottom: 16px; left: 18.6rem; width: 48px;">
                        <i class="fa-solid fa-xmark"></i>
                    </button>
                </div>
            </div>
                

            <!-- Main Content -->
            <div class="container-fluid ps-2 pe-3 pt-1" id="conatinerboady">
                <!-- Sidebar Toggle Button (visible on mobile only) -->
                <%--<button class="btn btn-primary mb-3" id="sidebarToggle">
                    <i class="fa-solid fa-bars"></i> Toggle Sidebar
                </button>--%>
        
                <!-- Filter Row -->
                <asp:ContentPlaceHolder ID="cphFilterContents" runat="server">
                </asp:ContentPlaceHolder>


                <!-- Contents/Table Section -->
                <asp:ContentPlaceHolder ID="cphPageContents" runat="server">
                </asp:ContentPlaceHolder>

                <div id="divDialogContents" style="display: none;"></div>
                <div id="divAddDialogContents" style="display: none;"></div>
                <div id="divEditDialogContents" style="display: none;"></div>


            </div>


        </div>


    </form>
</body>
    
    <script src="<%= Page.ResolveClientUrl("~/asset/lib/jquery/jquery.min.js") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/asset/lib/bootstrap/js/bootstrap.bundle.min.js") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/asset/lib/jquery-cookie/jquery.cookie.js") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/localization/lang.js")%>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/asset/lib/datatables.net/dataTables.min.js") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/asset/lib/moment.js/moment.min.js") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/asset/lib/select2/js/select2.full.min.js") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/asset/lib/bootstrap-datepicker/js/bootstrap-datepicker.min.js") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/asset/lib/jquery.bsmodal/jquery.bsmodal.js") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/asset/lib/jQuery-blockUI/jquery.blockUI.min.js") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/asset/lib/sweetalert2/sweetalert2.all.min.js") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/asset/lib/toastr.js/toastr.min.js") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/asset/lib/pnotify/pNotify.min.js") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/jQuery/jquery-ui-custom-mmsDialog.js") %>" type="text/javascript"></script>


    <script type="text/javascript">
        var currentLanguage = $.cookie('RVCLANG') != null ? $.cookie('RVCLANG') : 'en';
        var handlersBaseURL = '<%= ResolveClientUrl("~/Handlers") %>';
        var iwburl = '<%= ResolveClientUrl("~/Handlers/IwbHandlers/IwbHandler.ashx") %>';
        var clientRootURL = '<%= SiteConfig.WebURL %>';

        var loginUserType, loginUserId, loginUserName = '', loginUserEmail = '';
        loginUserType = <%= SessionHandler.UserInformation.UserType%>;
        loginUserId = <%= SessionHandler.UserInformation.UserNum%>;
        loginUserName = "<% = SessionHandler.UserName %>";
        loginUserEmail = "<%= SessionHandler.UserInformation.UserID%>";

        var customerId = '';
        customerId = "<%= SessionHandler.UserInformation.CustomerId%>";


        tenantId = <%= SessionHandler.UserInformation.TenantId %>;
        pageNotificationTimeout = 500;
        var setupPageNotificationTimeout = 500;
        var pageSize = 10;


      $(document).ready(function () {
    // Sidebar toggle function for mobile
    $('#sidebarToggle').on('click', function (e) {
        e.preventDefault();
        $('#sidebar').toggleClass('show');
    });

    $('#cancelButton').on('click', function (e) {
        e.preventDefault();
        $('#sidebar').removeClass('show');
    });

    function toggleSidebar() {
        let currentWidth = $(window).width();
        if (currentWidth >= 1024) {
            $('#sidebar').css('width', '15%');
            $('#containerbody').css('width', '85%');
            $('#cancelButton').hide();
            $('#sidebarToggle').hide();
        } else {
            $('#sidebar').css('width', '100%');
            $('#sidebarToggle').show();
            $('#cancelButton').show();
        }
    }

    toggleSidebar();

    $(window).resize(function () {
        toggleSidebar();
    });

    $('#lblLoginEmail').text(loginUserEmail);

    // ✅ Compact toggle initialization and persistence
    if (localStorage.getItem('compactMode') === 'true') {
        $('#compactToggle').prop('checked', true);
        $('body').addClass('compact-mode'); // optional styling class
    }

    $('#compactToggle').on('change', function () {
        var isChecked = $(this).is(':checked');
        localStorage.setItem('compactMode', isChecked);

        if (isChecked) {
            $('body').addClass('compact-mode');
        } else {
            $('body').removeClass('compact-mode');
        }
    });
});


    </script>

    <asp:ContentPlaceHolder ID="cphPageScripts" runat="server">
    </asp:ContentPlaceHolder>

</html>
