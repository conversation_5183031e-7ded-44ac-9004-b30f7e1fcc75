<?xml version="1.0" encoding="utf-8"?>
<SiteConfiguration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <!--<DALConnectionString>Data Source=69.238.217.103\REVCORD,1533;Initial Catalog=VoiceRec;User ID=sa;Password=******;Persist Security Info=True;</DALConnectionString>-->
	<DALConnectionString>Data Source=192.168.100.234,1433;Initial Catalog=VoiceRec;User ID=sa;Password=*************;</DALConnectionString>
  <RevLogDBConnectionString>Data Source=69.238.217.103\REVCORD,1533;Initial Catalog=RevLog;User ID=sa;Password=******;Persist Security Info=True;</RevLogDBConnectionString>
  <MasterDBConnectionString>Data Source=v12test.revcord.com\REVCORD,1533;Initial Catalog=RevMasterDB;User ID=sa;Password=******;Persist Security Info=True;</MasterDBConnectionString>
  <MGOMasterDBConnectionString>Data Source=v12test.revcord.com\REVCORD,1533;Initial Catalog=RevMasterDB;User ID=sa;Password=******;Persist Security Info=True;</MGOMasterDBConnectionString>
  <RevSyncDALConnectionString>yu818JOpYPhLuZb2lB9PqhCHlt83Q2cLqXjYeDYAhcWsn+XSTpxlYxFb5BWNl7LaSuUzKXL5fbFVWc58EMzHq++GsNZ9RM+mNYNVcd6lAeP7x1g5fsXncwMz0vU2J3DjPW+Th16og0/c4k0nl03frPZkY+uQ+mND</RevSyncDALConnectionString>
  <RevSyncMasterDALConnectionString>yu818JOpYPhLuZb2lB9PqhCHlt83Q2cLqXjYeDYAhcWsn+XSTpxlYxFb5BWNl7LaSuUzKXL5fbFVWc58EMzHq++GsNZ9RM+mNYNVcd6lAeP7x1g5fsXncwMz0vU2J3DjPW+Th16og0/c4k0nl03frPZkY+uQ+mND</RevSyncMasterDALConnectionString>
  <SearchPageSize>500</SearchPageSize>
  <!--<WebUrl>https://iq3pro.revcord.com/vrec/</WebUrl>-->
  <WebUrl>http://localhost:2705/</WebUrl>
  <HelperServiceAddress>https://iq3pro.revcord.com</HelperServiceAddress>
  <RevRecAddress>iq3pro.revcord.com</RevRecAddress>
  <MMSIQ3URL>https://iq3pro.revcord.com/</MMSIQ3URL>
  <RevSyncURL>http://127.0.0.1</RevSyncURL>
  <RevSyncEnabled>false</RevSyncEnabled>
  <RevSyncTenantID>1</RevSyncTenantID>
  <RevSyncISMT>false</RevSyncISMT>
  <IsMTEnable>false</IsMTEnable>
  <TenantID>1</TenantID>
  <RealTimeServer>http://127.0.0.1:4520/LiveServer</RealTimeServer>
  <VodServer>http://127.0.0.1:4510/VOD/VSWebVODAgent.dll?</VodServer>
  <IRStartupPlayer>File</IRStartupPlayer>
  <IRView>Non-Stack</IRView>
  <FolderPathToZip>D:\SaveTracksTemp\Export\</FolderPathToZip>
  <IsECEnabled>false</IsECEnabled>
  <IsEnterpriseRecorder>true</IsEnterpriseRecorder>
  <IsChainDBsConfigured>false</IsChainDBsConfigured>
  <GoogleMapApiKey>AIzaSyCWq_VtLtOHL77w3VUlyass8lm336C6fJo</GoogleMapApiKey>
  <PlaylistUploadRootFolder>D:\Development\Revcord\Apps\01-WebUI\MD-IQ3\WebUI\RevCord.VoiceRec.WebUIClient\SystemUploadedFiles\Playlist\</PlaylistUploadRootFolder>
  <PlaylistUploadHttpRoot>http://localhost:2705/SystemUploadedFiles/Playlist/</PlaylistUploadHttpRoot>
  <PreviewImageMaxWidth>1000</PreviewImageMaxWidth>
  <PreviewImageMaxHeight>1000</PreviewImageMaxHeight>
  <Host>smtp.gmail.com</Host>
  <Port>587</Port>
  <EnableSsl>true</EnableSsl>
  <UserName><EMAIL></UserName>
  <Password>kt734a9m</Password>
  <TranscriptLength>5</TranscriptLength>
  <DVREnabled>false</DVREnabled>
  <MaxExtensionCount>25</MaxExtensionCount>
  <AccountSid>hYrNYiZye3O9+nyJcPp5pygqDSXxdI/MkWgWTof6SZOGORqkvfiI/g==</AccountSid>
  <ApiSid>xaRohPHYP9D2luh4FlcE0dHTFn6Cb869igj9hIDtRDPK7ayPSsQB+A==</ApiSid>
  <ApiSecret>vs/GYlaIHC+5Pri5zAryGrssDlQACMPut+DHzviidpuyD07F9cjxkQ==</ApiSecret>
  <ChatServiceSid>8zK+R5mHaOLO+8erJ9X7FDh3nD7dFibWpKsD0jM6OBPyrQQ+MuSsng==</ChatServiceSid>
  <TwilioSMSAccountSid>hYrNYiZye3O9+nyJcPp5pygqDSXxdI/MkWgWTof6SZOGORqkvfiI/g==</TwilioSMSAccountSid>
  <TwilioSMSAuthToken>VC40z6eipjzHM+OaONbVILqtXFSnhOID+3mU/FrBFVjr6FaNQY7sFQ==</TwilioSMSAuthToken>
  <TwilioSMSPhoneNo>neelhUXt9ztRi0OHVhC3cQ==</TwilioSMSPhoneNo>
  <ConversationFilePath>D:\DissTech\Diss\</ConversationFilePath>
  <IsRevcellEnabled>true</IsRevcellEnabled>
  <RevcellProjectId>49acbed7-dc98-473e-b446-6286bd27e0e8</RevcellProjectId>
  <RevcellAuthToken>PTc158e82c725ce419db9d337f6bae10603466e7063956ef92</RevcellAuthToken>
  <IsActiveDirectory>false</IsActiveDirectory>
  <PlivoAuthID>MAN2JJYZHMYTK2NZNLNZ</PlivoAuthID>
  <PlivoAuthToken>ZjMxNWI0YmFhOGMyMjgyYzhmNTYyYmE1NjM4OWQ5</PlivoAuthToken>
  <PlivoEndpointAppId>*****************</PlivoEndpointAppId>
  <PlivoPhoneNumberAppId>*****************</PlivoPhoneNumberAppId>
  <PlivoDomain>phone.plivo.com</PlivoDomain>
  <IsRevLogEnabled>false</IsRevLogEnabled>
  <RevLogServiceURL>http://localhost:27336/RevLogSvc.svc</RevLogServiceURL>
  <RevViewServerURL>https://revview.revcord.com/</RevViewServerURL>
  <InspectionServerURL>https://iq3pro.revcord.com/</InspectionServerURL>
  <RevMTAPIServerURL>https://iq3pro.revcord.com/</RevMTAPIServerURL>
  <IsHostedOnLocalNetwork>false</IsHostedOnLocalNetwork>
  <RapidSOSClientID>D1GUaknA2DvsIxPYkt8hmN6zKVMv5U6Y9HG7C1JuwgmGsz84RHoBCQ==</RapidSOSClientID>
  <RapidSOSClientSecret>j5020HbZahULdopsuPF6e97CB+KGfH+I</RapidSOSClientSecret>
  <RapidSOSUserName>sSNH6V7NwmZuHis2JbllLMWDloSEMh/M</RapidSOSUserName>
  <RapidSOSPassword>R22x9p1MZxZNLybHZqFp2g==</RapidSOSPassword>
  <IsOnlyIQ3ModeEnabled>false</IsOnlyIQ3ModeEnabled>
  <IsRoleBasedAccessEnabled>false</IsRoleBasedAccessEnabled>
  <IsTeamsEnabled>false</IsTeamsEnabled>
  <ShowMarketingLinks>false</ShowMarketingLinks>
  <Is2FAEnabled>false</Is2FAEnabled>
  <IsCustomGraphicMarkerEnabled>false</IsCustomGraphicMarkerEnabled>
  <IsIWBModeEnabled>true</IsIWBModeEnabled>
  <IsMTRModeEnabled>true</IsMTRModeEnabled>
  <IsOnlyIWBModeEnabled>true</IsOnlyIWBModeEnabled>
  <AISERVEROCRURL>https://iwbai.revcord.com/api</AISERVEROCRURL>
</SiteConfiguration>
