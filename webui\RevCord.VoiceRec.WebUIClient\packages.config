﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="BouncyCastle" version="1.8.9" targetFramework="net46" />
  <package id="Common.Logging" version="3.3.1" targetFramework="net45" />
  <package id="Common.Logging.Core" version="3.3.1" targetFramework="net45" />
  <package id="Common.Logging.NLog20" version="3.3.1" targetFramework="net45" />
  <package id="DocumentFormat.OpenXml" version="2.5" targetFramework="net45" />
  <package id="DocX" version="1.8.0" targetFramework="net46" />
  <package id="EPPlus" version="4.5.3.1" targetFramework="net45" />
  <package id="iTextSharp" version="5.5.13.3" targetFramework="net46" />
  <package id="itextsharp.xmlworker" version="5.5.13.3" targetFramework="net46" />
  <package id="jQuery" version="1.6.4" targetFramework="net40" />
  <package id="JSNLog" version="2.22.1" targetFramework="net45" />
  <package id="JSNLog.NLog" version="2.22.1" targetFramework="net45" />
  <package id="JWT" version="6.1.4" targetFramework="net46" />
  <package id="Microsoft.AspNet.Cors" version="5.0.0" targetFramework="net45" />
  <package id="Microsoft.AspNet.Mvc" version="5.3.0" targetFramework="net46" />
  <package id="Microsoft.AspNet.Razor" version="3.3.0" targetFramework="net46" />
  <package id="Microsoft.AspNet.SignalR" version="1.2.1" targetFramework="net40" />
  <package id="Microsoft.AspNet.SignalR.Client" version="2.2.0" targetFramework="net45" />
  <package id="Microsoft.AspNet.SignalR.Core" version="1.2.1" targetFramework="net40" />
  <package id="Microsoft.AspNet.SignalR.JS" version="1.2.1" targetFramework="net40" />
  <package id="Microsoft.AspNet.SignalR.Owin" version="1.2.1" targetFramework="net40" requireReinstallation="true" />
  <package id="Microsoft.AspNet.SignalR.SystemWeb" version="1.2.1" targetFramework="net40" requireReinstallation="true" />
  <package id="Microsoft.AspNet.SignalR.Utils" version="1.1.2" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.3" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.3" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi.Owin" version="5.2.3" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi.OwinSelfHost" version="5.2.3" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebPages" version="3.3.0" targetFramework="net46" />
  <package id="Microsoft.Bcl" version="1.1.8" targetFramework="net45" />
  <package id="Microsoft.Bcl.Async" version="1.0.168" targetFramework="net45" />
  <package id="Microsoft.Bcl.Build" version="1.0.14" targetFramework="net45" />
  <package id="Microsoft.IdentityModel.JsonWebTokens" version="6.8.0" targetFramework="net46" />
  <package id="Microsoft.IdentityModel.Logging" version="6.8.0" targetFramework="net46" />
  <package id="Microsoft.IdentityModel.Tokens" version="6.8.0" targetFramework="net46" />
  <package id="Microsoft.Office.Interop.Word" version="15.0.4797.1003" targetFramework="net46" />
  <package id="Microsoft.Owin" version="3.0.1" targetFramework="net45" />
  <package id="Microsoft.Owin.Cors" version="3.0.1" targetFramework="net45" />
  <package id="Microsoft.Owin.Host.HttpListener" version="2.0.2" targetFramework="net45" />
  <package id="Microsoft.Owin.Host.SystemWeb" version="1.0.1" targetFramework="net40" requireReinstallation="true" />
  <package id="Microsoft.Owin.Hosting" version="2.0.2" targetFramework="net45" />
  <package id="Microsoft.Owin.Security" version="3.0.1" targetFramework="net45" />
  <package id="Microsoft.ReportingServices.ReportViewerControl.WebForms" version="140.340.80" targetFramework="net46" />
  <package id="Microsoft.ReportViewer.WebForms.v12" version="********" targetFramework="net46" />
  <package id="Microsoft.SqlServer.Types" version="14.0.314.76" targetFramework="net46" />
  <package id="Microsoft.Web.Infrastructure" version="*******" targetFramework="net40" />
  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net46" />
  <package id="NLog" version="2.0.0.2000" targetFramework="net45" />
  <package id="Owin" version="1.0" targetFramework="net40" />
  <package id="PDFsharp" version="1.50.5147" targetFramework="net46" />
  <package id="Plivo" version="4.15.1" targetFramework="net46" />
  <package id="RestSharp" version="106.15.0" targetFramework="net46" />
  <package id="SharpZipLib" version="1.1.0" targetFramework="net45" />
  <package id="System.IdentityModel.Tokens.Jwt" version="5.1.2" targetFramework="net46" />
  <package id="System.IO.FileSystem" version="4.3.0" targetFramework="net46" />
  <package id="System.IO.FileSystem.Primitives" version="4.3.0" targetFramework="net46" />
  <package id="System.Net.Http" version="4.3.4" targetFramework="net46" />
  <package id="System.Security.Cryptography.Algorithms" version="4.3.0" targetFramework="net46" />
  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="net46" />
  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="net46" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.3.0" targetFramework="net46" />
  <package id="System.Text.Encoding.Extensions" version="4.3.0" targetFramework="net46" />
  <package id="Twilio" version="6.2.0" targetFramework="net46" />
  <package id="WebActivatorEx" version="2.0" targetFramework="net45" />
  <package id="WMPLib" version="1.0.0" targetFramework="net46" developmentDependency="true" />
</packages>