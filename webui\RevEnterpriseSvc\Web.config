﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<configSections>
		<section name="jsnlog" type="JSNLog.ConfigurationSec<PERSON><PERSON><PERSON><PERSON>, JSNLog" requirePermission="false" />
		<sectionGroup name="common">
			<section name="logging" type="Common.Logging.ConfigurationSectionHandler, Common.Logging" />
		</sectionGroup>
		<sectionGroup name="system.web.webPages.razor" type="System.Web.WebPages.Razor.Configuration.RazorWebSectionGroup, System.Web.WebPages.Razor">
			<section name="pages" type="System.Web.WebPages.Razor.Configuration.RazorPagesSection, System.Web.WebPages.Razor" requirePermission="false" />
		</sectionGroup>
	</configSections>
	<system.web.webPages.razor>
		<pages pageBaseType="System.Web.Mvc.WebViewPage">
			<namespaces>
				<add namespace="System.Web.Mvc" />
				<add namespace="System.Web.Mvc.Ajax" />
				<add namespace="System.Web.Mvc.Html" />
				<add namespace="System.Web.Routing" />
				<add namespace="RevCord.DataContracts.RevSignEntities" />
			</namespaces>
		</pages>
	</system.web.webPages.razor>
	<!--<connectionStrings>
    <add name="MasterDBConnectionString" connectionString="Data Source=129.213.162.172\REVCORD,1533;Initial Catalog=00-VoiceRec-Master;User ID=sa;Password=******;Persist Security Info=True;Connect Timeout=200;pooling='true';Max Pool Size=200" providerName="System.Data.SqlClient" />
  </connectionStrings>-->
	<appSettings>
		<add key="ValidationSettings:UnobtrusiveValidationMode" value="None" />
		<add key="webUrl" value="https://v12test.revcord.com/vrec/" />
		<add key="exportSourceDir" value="~/ExportTemplates" />
		<add key="pathSplitLevel" value="1" />
		<add key="defaultPageSize" value="50" />
		<add key="defaultPageSizeIR" value="500" />
		<add key="maxPlaylistItem" value="500" />
		<add key="exportCallsExtension" value=".xls" />
		<add key="exportCallsMimeType" value="application/msexcel" />
		<add key="exportPaperClipsDir" value="~/PaperClips" />
		<add key="surveyUploadDir" value="~/SystemUploadedFiles/SurveyForms" />
		<add key="PlaylistaddfilesUploadDir" value="~/SystemUploadedFiles/Playlist" />
		<add key="dbChannelsCacheTime" value="0" />
		<add key="bufName" value="EvalSearchCalls" />
		<add key="dbEncodeType" value="iso-8859-1" />
		<add key="liveMinMilliSecond" value="5000" />
		<add key="chartColors" value="#4572A7,#DDDF00,#89A54E,#A4A4A4,#AA4643,#FF9655,#FFF263,#FEED01" />
		<add key="monitorTreeViewType" value="1" />
		<add key="latitudeValue" value="0" />
		<add key="longitudeValue" value="1" />
		<add key="callGUIDRange" value="16,10" />
		<!--<add key="maxMapReportsCalls" value="2485"/>-->
		<add key="maxMapReportsCalls" value="0" />
		<add key="loadReportDetails" value="false" />
		<add key="commandTimeout" value="300" />
		<add key="dashboardCommandTimeout" value="300" />
		<add key="defaultLang" value="en-US" />
		<add key="screensPath" value="D:\DissTech\VideoFiles\" />
		<add key="inquirePath" value="D:\DissTech\Inquire\" />
		<add key="RevcellPath" value="D:\DissTech\Revcell\" />
		<add key="RevviewPath" value="D:\DissTech\Revview\" />
		<add key="exportUserAddedItems" value="true" />
		<add key="isCaptchaEnabled" value="false" />
		<add key="PBX911ReportsEnabled" value="true" />
		<add key="autoSttRunning" value="true" />
		<add key="autoSttDelayHours" value="-48" />
		<add key="isDemo" value="false" />
		<add key="logUserActivity" value="true" />
		<add key="encryptDecryptKey" value="r0b1nr0y" />
		<add key="GoogleOAuth2JsonPath" value="~/APIKeys/GoogleCalendar/developer/events-244417-747d5e9b2a7f.json" />
		<add key="GoogleOAuth2CertificatePath" value="~/APIKeys/GoogleCalendar/developer/events-244417-645bbbbd214a.p12" />
		<add key="GoogleOAuth2EmailAddress" value="<EMAIL>" />
		<add key="GoogleOAuth2PrivateKey" value="notasecret" />
		<add key="GoogleAccount" value="<EMAIL>" />
		<add key="GoogleCalendarAPIKey" value="AIzaSyDASmGPE9XRh6KiI250E0M-iui93T0QtGk" />
		<add key="logPath" value="D:\\Disstech\\Logs\\EnterpriseLog\\" />
		<add key="demoText911FilesPath" value="~/SystemUploadedFiles/DemoText911" />
		<add key="nLogPath" value="D:\\Disstech\\Logs\\MMSServerSideLog\\${shortdate}.log" />
		<add key="nLogConfig" value="C:\\Program Files\\Git\\work\\BitBucket\\WebUI\\RevCord.VoiceRec.WebUIClient\\NLog.config" />
		<add key="demoRecName" value="LOCALREC" />
		<add key="rptTitleName" value="City of Houston " />
		<add key="secRecAdmDefault" value="1000" />
		<add key="encryptionPassword" value="******" />
		<add key="versionNo" value="?version=13.0.0" />
		<add key="softwareVersionNo" value="V13" />
		<add key="loPath" value="C:\Program Files\LibreOffice\program" />
		<add key="temploPath" value="C:\\DissTech\\VRec\\Temp\\LibreOffice" />
		<add key="sttService" value="RevcordSTTService/STTService.svc/" />
		<add key="sttJsonFilePath" value="D:\DissTech\STT" />
		<add key="SSLDomainName" value="revcordssl.com" />
		<add key="RevcellSpace" value="https://revcell.signalwire.com/" />
		<!--<add key="site" value="D:\SoftwareDevelopment\SourceCode\Revcord\WebUI\RevCord.VoiceRec.WebUIClient\Site.config" />-->
		<add key="site" value="D:\sugumar\sugumar\Revcord_IWB\NEW_IWB\IWB\webui\RevCord.VoiceRec.WebUIClient\Site.config" />
		<add key="owin:AutomaticAppStartup" value="false" />
		<add key="PageInspector:ServerCodeMappingSupport" value="Disabled" />
		<!--<add key="zohoClientId" value="SEBwWfgV4Lg7/EIQKg1DauNwVAeXWdWwhMsSGFjIlLOJ+qNMhKR4vA==" />
    <add key="zohoClientSecret" value="EpMq3QNpCv6wWmn+0nBxbN73meIFnJz3uVGT9k6HE+aWdR05nfsMne1D3mHULIpy" />
    <add key="zohoRedirectURI" value="jm4K47m+4M897WTgouZAm8D0fsnj3W2G3Z9VIRdtpb0=" />
    <add key="zohoAccessType" value="MSb69+TowW8=" />
    <add key="zohoMinLogLevel" value="BwMwqh2RgYU=" />
    <add key="zohoUserEmail" value="3KIh1Ec5DKgK8MpLqPA/2+P45R5EXuXZ" />
    <add key="RefreshToken" value="nVoOCoBqbCjiUL95R5Mu0VgIebSzyeMKdFGvbrYZBUPcvMQ5k0+aNTMr4EO+r56xfJYaN7sXx8OVNeAZzuINuXQkVIUM+nB/" />-->
		<add key="zohoClientId" value="r7sGC/x3LNfioFa6KSzE7s3hNuiAJ7ByBX3Ya1a53zj/oRH08algyQ==" />
		<add key="zohoClientSecret" value="q3dN+63nahGhqMgNZ2m12k1KfB9JvSIXbGRhfGXbtC1UyzxT1Ws9cxEpbDaHCaa2" />
		<add key="zohoRedirectURI" value="jm4K47m+4M897WTgouZAm8D0fsnj3W2G3Z9VIRdtpb0=" />
		<add key="zohoAccessType" value="MSb69+TowW8=" />
		<add key="zohoMinLogLevel" value="BwMwqh2RgYU=" />
		<add key="zohoUserEmail" value="9kkGvDpnnVfN8t76xnIy6twGQRhD8G1B" />
		<add key="RefreshToken" value="l/EiDlSzlJmqKV2OVnR3FravKLJWtnPwEx6sEgwUvnbhltHIltrHKM6KeU1x5SI8OToGqJFmo5Bwyk5qjJVus7FI6jLa2/23" />
		<add key="passwordExpiryInDays" value="90" />
		<add key="passwordReminderInDays" value="3" />
		<add key="passwordLockoutInvalidAttempts" value="5" />
		<add key="passwordEnableLockout" value="true" />
		<add key="passwordForceChange" value="true" />
		<add key="RevcellManualDownloadLink" value="~/SystemUploadedFiles/Manuals" />
		<add key="LoadSubServerUsers" value="false" />
		<add key="LoadAudioNodesOnly" value="false" />
		<add key="LoadEmptySubGroup" value="false" />
		<add key="HideTextAndScreensTree" value="false" />
		<add key="EnableURLShortener" value="true" />
		<add key="RevShieldCheckEnabled" value="true" />
		<add key="AccessTokenConfig" value="C:\\DissTech\\VRec\\tokens.config" />
		<add key="StopCentralHub" value="false" />
		<add key="StopRevViewHub" value="false" />
		<add key="MTMMSRedirect" value="https://iq3mdmerge.revcord.com/MTVRec/" />
		<add key="MTMMSRedirectV12.3" value="http://localhost:2705/" />
		<add key="MTMMSRedirectV12.3.1" value="http://localhost:2705/" />
		<!--<add key="MTConnectionHost" value="*********\\Revcord,1433;" />-->
		<add key="MTConnectionHost" value="mtdev.revcord.com\\Revcord,1533;" />
		<add key="RevSignPath" value="D:\DissTech\RevSign\" />
		<add key="VRecPath" value="C:\Disstech\VRec\" />
		<add key="MGOMasterDBConnectionString" value="Data Source=mgo-api.revcord.com\Revcord,1533;Initial Catalog=RevMasterDB;User ID=sa;Password=******;Persist Security Info=True;" />


		<!--<add key="zohoBillingBaseAccountUrl" value="https://accounts.zoho.com/oauth/v2/token" />
		<add key="zohoBillingBaseBillingUrl" value="https://www.zohoapis.com/billing/v1" />
		<add key="zohoBillingClientId" value="1000.RIYMQ3W0NZY7FUBG2X1MLH4EZZOSOV" />
		<add key="zohoBillingClientSecret" value="c4b30a8db359f96cf20c728e57f271fb41fe6ac361" />
		<add key="zohoBillingAccessToken" value="**********************************************************************" />
		<add key="zohoBillingRefreshToken" value="**********************************************************************" />
		<add key="zohoBillingOrganizationId" value="*********" />-->


	</appSettings>
	<!--
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.5" />
      </system.Web>
  -->
	<system.web>
		<sessionState timeout="10080" />
		<!--<sessionState timeout="30" />-->
		<customErrors mode="Off" />
		<pages validateRequest="false" controlRenderingCompatibilityVersion="4.0" />
		<!--<httpRuntime maxUrlLength="10999" maxQueryStringLength="2097151" />-->
		<!--<httpRuntime maxQueryStringLength="9999" requestValidationMode="2.0" />-->
		<httpRuntime maxQueryStringLength="2097151" requestValidationMode="2.0" executionTimeout="90000" maxRequestLength="1048576" targetFramework="4.5" />
		<httpHandlers>
			<!--<add path="Reserved.ReportViewerWebControl.axd" verb="*" type="Microsoft.Reporting.WebForms.HttpHandler, Microsoft.ReportViewer.WebForms, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91" validate="false" />-->
			<add verb="*" path="Reserved.ReportViewerWebControl.axd" type="Microsoft.Reporting.WebForms.HttpHandler, Microsoft.ReportViewer.WebForms, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91" />
			<add verb="*" path="*.logger" type="JSNLog.LoggerHandler, JSNLog" />
		</httpHandlers>
		<compilation debug="true" targetFramework="4.6">
			<assemblies>
				<!--<add assembly="Microsoft.ReportViewer.WebForms, Version=10.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="Microsoft.ReportViewer.Common, Version=10.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />-->
				<!--<add assembly="Microsoft.Build.Framework, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />-->
				<add assembly="System.Management, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
				<add assembly="Microsoft.ReportViewer.WebForms, Version=********, Culture=neutral, PublicKeyToken=89845DCD8080CC91" />
				<add assembly="Microsoft.ReportViewer.Common, Version=********, Culture=neutral, PublicKeyToken=89845DCD8080CC91" />
			</assemblies>
			<buildProviders>
				<add extension=".rdlc" type="Microsoft.Reporting.RdlBuildProvider, Microsoft.ReportViewer.WebForms, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			</buildProviders>
		</compilation>
	</system.web>
	<system.net>
		<mailSettings>
			<smtp deliveryMethod="Network">
				<network host="smtp.office365.com" port="587" enableSsl="true" userName="<EMAIL>" password="YM7q9!qp@$q7U5" />
			</smtp>
		</mailSettings>
	</system.net>
	<system.webServer>
		<modules runAllManagedModulesForAllRequests="true" />
		<security>
			<requestFiltering>
				<requestLimits maxQueryString="2097151" />
			</requestFiltering>
		</security>
		<handlers>
			<remove name="ExtensionlessUrlHandler-Integrated-4.0" />
			<remove name="OPTIONSVerbHandler" />
			<remove name="TRACEVerbHandler" />
			<add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
			<add name="ReportViewerWebControlHandler" preCondition="integratedMode" verb="*" path="Reserved.ReportViewerWebControl.axd" type="Microsoft.Reporting.WebForms.HttpHandler, Microsoft.ReportViewer.WebForms, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91" />
			<add name="LoggerHandler" verb="*" path="*.logger" type="JSNLog.LoggerHandler, JSNLog" resourceType="Unspecified" preCondition="integratedMode" />
			<add name="LoggerHandler-Classic" path="*.logger" verb="*" modules="IsapiModule" scriptProcessor="%windir%\Microsoft.NET\Framework64\v4.0.30319\aspnet_isapi.dll" resourceType="Unspecified" preCondition="classicMode" />
		</handlers>
		<validation validateIntegratedModeConfiguration="false" />
		<httpProtocol>
			<customHeaders>
				<add name="Access-Control-Allow-Origin" value="*" />
			</customHeaders>
		</httpProtocol>
	</system.webServer>
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Build.Framework" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="EnvDTE" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.SqlServer.Types" publicKeyToken="89845DCD8080CC91" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-10.0.0.0" newVersion="10.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.AspNet.SignalR.Core" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Owin.Security" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Owin.Host.SystemWeb" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="ICSharpCode.SharpZipLib" publicKeyToken="1b03e6acf1164f73" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*********" newVersion="*********" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="NLog" publicKeyToken="5120e14c03d0593c" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Net.Http" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Security.Cryptography.Primitives" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.IO.FileSystem" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.2.0" newVersion="4.0.2.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.IdentityModel.Tokens" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-6.8.0.0" newVersion="6.8.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.IdentityModel.Logging" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-6.8.0.0" newVersion="6.8.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.ReportViewer.WebForms" publicKeyToken="89845dcd8080cc91" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.ReportViewer.Common" publicKeyToken="89845dcd8080cc91" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="PLUSManaged" publicKeyToken="5d94a04f4eca59fc" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-5.20.1.0" newVersion="5.20.1.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="*******-3.0.0.0" newVersion="3.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="*******-3.0.0.0" newVersion="3.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="*******-*******" newVersion="*******" />
			</dependentAssembly>
		</assemblyBinding>
	</runtime>
	<system.serviceModel>
		<bindings>
			<basicHttpBinding>
				<binding name="BasicHttpBinding_IRevEnterpriseSvc" maxBufferSize="2147483647" maxReceivedMessageSize="2147483647" />
				<binding name="BasicHttpBinding_IRevLogSvc" />
			</basicHttpBinding>
		</bindings>
		<client>
			<endpoint address="http://localhost/RevEnterpriseSvc/RevEnterpriseSvc.svc" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_IRevEnterpriseSvc" contract="RevcordEnterpriseSvc.IRevEnterpriseSvc" name="BasicHttpBinding_IRevEnterpriseSvc" />
			<!--<endpoint address="http://localhost:27336/RevLogSvc.svc" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_IRevLogSvc" contract="RevLogSvc.IRevLogSvc" name="BasicHttpBinding_IRevLogSvc" />-->
			<endpoint address="http://localhost/RevLogService/RevLogSvc.svc" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_IRevLogSvc" contract="RevLogSvc.IRevLogSvc" name="BasicHttpBinding_IRevLogSvc" />
		</client>
	</system.serviceModel>

	<jsnlog enabled="true" serverSideLevel="FATAL" defaultAjaxUrl="/jsnlog.logger" serverSideMessageFormat="%requestId Send: %date, %message" productionLibraryPath="~/Scripts/jsnlog.min.js">
		<ajaxAppender name="ajaxAppender_root" />
		<logger name="JSLogger.EventLogger" level="FATAL" appenders="ajaxAppender_root" />
		<logger name="JSLogger.DashboardLogger" level="FATAL" appenders="ajaxAppender_root" />
		<logger name="JSLogger.UserManagerLogger" level="FATAL" appenders="ajaxAppender_root" />
		<logger name="JSLogger.SiteConfigLogger" level="FATAL" appenders="ajaxAppender_root" />
		<logger name="JSLogger.SearchLogger" level="FATAL" appenders="ajaxAppender_root" />
		<logger name="JSLogger.ReportsLogger" level="FATAL" appenders="ajaxAppender_root" />
		<logger name="JSLogger.EvaluationLogger" level="FATAL" appenders="ajaxAppender_root" />
		<logger name="JSLogger.PlaylistLogger" level="FATAL" appenders="ajaxAppender_root" />
		<logger name="JSLogger.ScheduleEventLogger" level="FATAL" appenders="ajaxAppender_root" />
		<logger name="JSLogger" level="FATAL" appenders="ajaxAppender_root" />
		<logger level="FATAL" appenders="ajaxAppender_root" />
	</jsnlog>

	<common>
		<logging>
			<factoryAdapter type="Common.Logging.NLog.NLogLoggerFactoryAdapter, Common.Logging.NLog20">
				<arg key="configType" value="FILE" />
				<arg key="configFile" value="~/NLog.config" />
			</factoryAdapter>
		</logging>
	</common>
</configuration>
