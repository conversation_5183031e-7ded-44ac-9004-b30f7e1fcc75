<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Google.Apis.Calendar.v3</name>
    </assembly>
    <members>
        <member name="T:Google.Apis.Calendar.v3.CalendarService">
            <summary>The Calendar Service.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.CalendarService.Version">
            <summary>The API version.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.CalendarService.DiscoveryVersionUsed">
            <summary>The discovery version used to generate this service.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarService.#ctor">
            <summary>Constructs a new service.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarService.#ctor(Google.Apis.Services.BaseClientService.Initializer)">
            <summary>Constructs a new service.</summary>
            <param name="initializer">The service initializer.</param>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarService.Features">
            <summary>Gets the service supported features.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarService.Name">
            <summary>Gets the service name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarService.BaseUri">
            <summary>Gets the service base URI.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarService.BasePath">
            <summary>Gets the service base path.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarService.BatchUri">
            <summary>Gets the batch base URI; <c>null</c> if unspecified.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarService.BatchPath">
            <summary>Gets the batch base path; <c>null</c> if unspecified.</summary>
        </member>
        <member name="T:Google.Apis.Calendar.v3.CalendarService.Scope">
            <summary>Available OAuth 2.0 scopes for use with the Calendar API.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.CalendarService.Scope.Calendar">
            <summary>See, edit, share, and permanently delete all the calendars you can access using Google
            Calendar</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.CalendarService.Scope.CalendarEvents">
            <summary>View and edit events on all your calendars</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.CalendarService.Scope.CalendarEventsReadonly">
            <summary>View events on all your calendars</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.CalendarService.Scope.CalendarReadonly">
            <summary>View your calendars</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.CalendarService.Scope.CalendarSettingsReadonly">
            <summary>View your Calendar settings</summary>
        </member>
        <member name="T:Google.Apis.Calendar.v3.CalendarService.ScopeConstants">
            <summary>Available OAuth 2.0 scope constants for use with the Calendar API.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.CalendarService.ScopeConstants.Calendar">
            <summary>See, edit, share, and permanently delete all the calendars you can access using Google
            Calendar</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.CalendarService.ScopeConstants.CalendarEvents">
            <summary>View and edit events on all your calendars</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.CalendarService.ScopeConstants.CalendarEventsReadonly">
            <summary>View events on all your calendars</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.CalendarService.ScopeConstants.CalendarReadonly">
            <summary>View your calendars</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.CalendarService.ScopeConstants.CalendarSettingsReadonly">
            <summary>View your Calendar settings</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarService.Acl">
            <summary>Gets the Acl resource.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarService.CalendarList">
            <summary>Gets the CalendarList resource.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarService.Calendars">
            <summary>Gets the Calendars resource.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarService.Channels">
            <summary>Gets the Channels resource.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarService.Colors">
            <summary>Gets the Colors resource.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarService.Events">
            <summary>Gets the Events resource.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarService.Freebusy">
            <summary>Gets the Freebusy resource.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarService.Settings">
            <summary>Gets the Settings resource.</summary>
        </member>
        <member name="T:Google.Apis.Calendar.v3.CalendarBaseServiceRequest`1">
            <summary>A base abstract class for Calendar requests.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarBaseServiceRequest`1.#ctor(Google.Apis.Services.IClientService)">
            <summary>Constructs a new CalendarBaseServiceRequest instance.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarBaseServiceRequest`1.Alt">
            <summary>Data format for the response.</summary>
            [default: json]
        </member>
        <member name="T:Google.Apis.Calendar.v3.CalendarBaseServiceRequest`1.AltEnum">
            <summary>Data format for the response.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.CalendarBaseServiceRequest`1.AltEnum.Json">
            <summary>Responses with Content-Type of application/json</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarBaseServiceRequest`1.Fields">
            <summary>Selector specifying which fields to include in a partial response.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarBaseServiceRequest`1.Key">
            <summary>API key. Your API key identifies your project and provides you with API access, quota, and reports.
            Required unless you provide an OAuth 2.0 token.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarBaseServiceRequest`1.OauthToken">
            <summary>OAuth 2.0 token for the current user.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarBaseServiceRequest`1.PrettyPrint">
            <summary>Returns response with indentations and line breaks.</summary>
            [default: true]
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarBaseServiceRequest`1.QuotaUser">
            <summary>An opaque string that represents a user for quota purposes. Must not exceed 40
            characters.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarBaseServiceRequest`1.UserIp">
            <summary>Deprecated. Please use quotaUser instead.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarBaseServiceRequest`1.InitParameters">
            <summary>Initializes Calendar parameter list.</summary>
        </member>
        <member name="T:Google.Apis.Calendar.v3.AclResource">
            <summary>The "acl" collection of methods.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.AclResource.service">
            <summary>The service which this resource belongs to.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.AclResource.#ctor(Google.Apis.Services.IClientService)">
            <summary>Constructs a new resource.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.AclResource.Delete(System.String,System.String)">
             <summary>Deletes an access control rule.</summary>
             <param name="calendarId">Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you
             want to access the primary calendar of the currently logged in user, use the "primary" keyword.</param>
            
             <param name="ruleId">ACL rule identifier.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.AclResource.DeleteRequest">
            <summary>Deletes an access control rule.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.AclResource.DeleteRequest.#ctor(Google.Apis.Services.IClientService,System.String,System.String)">
            <summary>Constructs a new Delete request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.DeleteRequest.CalendarId">
            <summary>Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to
            access the primary calendar of the currently logged in user, use the "primary" keyword.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.DeleteRequest.RuleId">
            <summary>ACL rule identifier.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.DeleteRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.DeleteRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.DeleteRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.AclResource.DeleteRequest.InitParameters">
            <summary>Initializes Delete parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.AclResource.Get(System.String,System.String)">
             <summary>Returns an access control rule.</summary>
             <param name="calendarId">Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you
             want to access the primary calendar of the currently logged in user, use the "primary" keyword.</param>
            
             <param name="ruleId">ACL rule identifier.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.AclResource.GetRequest">
            <summary>Returns an access control rule.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.AclResource.GetRequest.#ctor(Google.Apis.Services.IClientService,System.String,System.String)">
            <summary>Constructs a new Get request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.GetRequest.CalendarId">
            <summary>Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to
            access the primary calendar of the currently logged in user, use the "primary" keyword.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.GetRequest.RuleId">
            <summary>ACL rule identifier.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.GetRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.GetRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.GetRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.AclResource.GetRequest.InitParameters">
            <summary>Initializes Get parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.AclResource.Insert(Google.Apis.Calendar.v3.Data.AclRule,System.String)">
            <summary>Creates an access control rule.</summary>
            <param name="body">The body of the request.</param>
            <param name="calendarId">Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you
            want to access the primary calendar of the currently logged in user, use the "primary" keyword.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.AclResource.InsertRequest">
            <summary>Creates an access control rule.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.AclResource.InsertRequest.#ctor(Google.Apis.Services.IClientService,Google.Apis.Calendar.v3.Data.AclRule,System.String)">
            <summary>Constructs a new Insert request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.InsertRequest.CalendarId">
            <summary>Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to
            access the primary calendar of the currently logged in user, use the "primary" keyword.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.InsertRequest.SendNotifications">
            <summary>Whether to send notifications about the calendar sharing change. Optional. The default is
            True.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.InsertRequest.Body">
            <summary>Gets or sets the body of this request.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.AclResource.InsertRequest.GetBody">
            <summary>Returns the body of the request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.InsertRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.InsertRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.InsertRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.AclResource.InsertRequest.InitParameters">
            <summary>Initializes Insert parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.AclResource.List(System.String)">
            <summary>Returns the rules in the access control list for the calendar.</summary>
            <param name="calendarId">Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you
            want to access the primary calendar of the currently logged in user, use the "primary" keyword.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.AclResource.ListRequest">
            <summary>Returns the rules in the access control list for the calendar.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.AclResource.ListRequest.#ctor(Google.Apis.Services.IClientService,System.String)">
            <summary>Constructs a new List request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.ListRequest.CalendarId">
            <summary>Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to
            access the primary calendar of the currently logged in user, use the "primary" keyword.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.ListRequest.MaxResults">
            <summary>Maximum number of entries returned on one result page. By default the value is 100 entries. The
            page size can never be larger than 250 entries. Optional.</summary>
            [minimum: 1]
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.ListRequest.PageToken">
            <summary>Token specifying which result page to return. Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.ListRequest.ShowDeleted">
            <summary>Whether to include deleted ACLs in the result. Deleted ACLs are represented by role equal to
            "none". Deleted ACLs will always be included if syncToken is provided. Optional. The default is
            False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.ListRequest.SyncToken">
            <summary>Token obtained from the nextSyncToken field returned on the last page of results from the
            previous list request. It makes the result of this list request contain only entries that have changed
            since then. All entries deleted since the previous list request will always be in the result set and it
            is not allowed to set showDeleted to False. If the syncToken expires, the server will respond with a 410
            GONE response code and the client should clear its storage and perform a full synchronization without
            any syncToken. Learn more about incremental synchronization. Optional. The default is to return all
            entries.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.ListRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.ListRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.ListRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.AclResource.ListRequest.InitParameters">
            <summary>Initializes List parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.AclResource.Patch(Google.Apis.Calendar.v3.Data.AclRule,System.String,System.String)">
             <summary>Updates an access control rule. This method supports patch semantics.</summary>
             <param name="body">The body of the request.</param>
             <param name="calendarId">Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you
             want to access the primary calendar of the currently logged in user, use the "primary" keyword.</param>
            
             <param name="ruleId">ACL rule identifier.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.AclResource.PatchRequest">
            <summary>Updates an access control rule. This method supports patch semantics.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.AclResource.PatchRequest.#ctor(Google.Apis.Services.IClientService,Google.Apis.Calendar.v3.Data.AclRule,System.String,System.String)">
            <summary>Constructs a new Patch request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.PatchRequest.CalendarId">
            <summary>Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to
            access the primary calendar of the currently logged in user, use the "primary" keyword.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.PatchRequest.RuleId">
            <summary>ACL rule identifier.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.PatchRequest.SendNotifications">
            <summary>Whether to send notifications about the calendar sharing change. Note that there are no
            notifications on access removal. Optional. The default is True.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.PatchRequest.Body">
            <summary>Gets or sets the body of this request.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.AclResource.PatchRequest.GetBody">
            <summary>Returns the body of the request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.PatchRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.PatchRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.PatchRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.AclResource.PatchRequest.InitParameters">
            <summary>Initializes Patch parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.AclResource.Update(Google.Apis.Calendar.v3.Data.AclRule,System.String,System.String)">
             <summary>Updates an access control rule.</summary>
             <param name="body">The body of the request.</param>
             <param name="calendarId">Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you
             want to access the primary calendar of the currently logged in user, use the "primary" keyword.</param>
            
             <param name="ruleId">ACL rule identifier.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.AclResource.UpdateRequest">
            <summary>Updates an access control rule.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.AclResource.UpdateRequest.#ctor(Google.Apis.Services.IClientService,Google.Apis.Calendar.v3.Data.AclRule,System.String,System.String)">
            <summary>Constructs a new Update request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.UpdateRequest.CalendarId">
            <summary>Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to
            access the primary calendar of the currently logged in user, use the "primary" keyword.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.UpdateRequest.RuleId">
            <summary>ACL rule identifier.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.UpdateRequest.SendNotifications">
            <summary>Whether to send notifications about the calendar sharing change. Note that there are no
            notifications on access removal. Optional. The default is True.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.UpdateRequest.Body">
            <summary>Gets or sets the body of this request.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.AclResource.UpdateRequest.GetBody">
            <summary>Returns the body of the request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.UpdateRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.UpdateRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.UpdateRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.AclResource.UpdateRequest.InitParameters">
            <summary>Initializes Update parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.AclResource.Watch(Google.Apis.Calendar.v3.Data.Channel,System.String)">
            <summary>Watch for changes to ACL resources.</summary>
            <param name="body">The body of the request.</param>
            <param name="calendarId">Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you
            want to access the primary calendar of the currently logged in user, use the "primary" keyword.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.AclResource.WatchRequest">
            <summary>Watch for changes to ACL resources.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.AclResource.WatchRequest.#ctor(Google.Apis.Services.IClientService,Google.Apis.Calendar.v3.Data.Channel,System.String)">
            <summary>Constructs a new Watch request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.WatchRequest.CalendarId">
            <summary>Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to
            access the primary calendar of the currently logged in user, use the "primary" keyword.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.WatchRequest.MaxResults">
            <summary>Maximum number of entries returned on one result page. By default the value is 100 entries. The
            page size can never be larger than 250 entries. Optional.</summary>
            [minimum: 1]
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.WatchRequest.PageToken">
            <summary>Token specifying which result page to return. Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.WatchRequest.ShowDeleted">
            <summary>Whether to include deleted ACLs in the result. Deleted ACLs are represented by role equal to
            "none". Deleted ACLs will always be included if syncToken is provided. Optional. The default is
            False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.WatchRequest.SyncToken">
            <summary>Token obtained from the nextSyncToken field returned on the last page of results from the
            previous list request. It makes the result of this list request contain only entries that have changed
            since then. All entries deleted since the previous list request will always be in the result set and it
            is not allowed to set showDeleted to False. If the syncToken expires, the server will respond with a 410
            GONE response code and the client should clear its storage and perform a full synchronization without
            any syncToken. Learn more about incremental synchronization. Optional. The default is to return all
            entries.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.WatchRequest.Body">
            <summary>Gets or sets the body of this request.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.AclResource.WatchRequest.GetBody">
            <summary>Returns the body of the request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.WatchRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.WatchRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.AclResource.WatchRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.AclResource.WatchRequest.InitParameters">
            <summary>Initializes Watch parameter list.</summary>
        </member>
        <member name="T:Google.Apis.Calendar.v3.CalendarListResource">
            <summary>The "calendarList" collection of methods.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.CalendarListResource.service">
            <summary>The service which this resource belongs to.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarListResource.#ctor(Google.Apis.Services.IClientService)">
            <summary>Constructs a new resource.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarListResource.Delete(System.String)">
            <summary>Removes a calendar from the user's calendar list.</summary>
            <param name="calendarId">Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you
            want to access the primary calendar of the currently logged in user, use the "primary" keyword.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.CalendarListResource.DeleteRequest">
            <summary>Removes a calendar from the user's calendar list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarListResource.DeleteRequest.#ctor(Google.Apis.Services.IClientService,System.String)">
            <summary>Constructs a new Delete request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.DeleteRequest.CalendarId">
            <summary>Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to
            access the primary calendar of the currently logged in user, use the "primary" keyword.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.DeleteRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.DeleteRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.DeleteRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarListResource.DeleteRequest.InitParameters">
            <summary>Initializes Delete parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarListResource.Get(System.String)">
            <summary>Returns a calendar from the user's calendar list.</summary>
            <param name="calendarId">Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you
            want to access the primary calendar of the currently logged in user, use the "primary" keyword.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.CalendarListResource.GetRequest">
            <summary>Returns a calendar from the user's calendar list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarListResource.GetRequest.#ctor(Google.Apis.Services.IClientService,System.String)">
            <summary>Constructs a new Get request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.GetRequest.CalendarId">
            <summary>Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to
            access the primary calendar of the currently logged in user, use the "primary" keyword.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.GetRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.GetRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.GetRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarListResource.GetRequest.InitParameters">
            <summary>Initializes Get parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarListResource.Insert(Google.Apis.Calendar.v3.Data.CalendarListEntry)">
            <summary>Inserts an existing calendar into the user's calendar list.</summary>
            <param name="body">The body of the request.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.CalendarListResource.InsertRequest">
            <summary>Inserts an existing calendar into the user's calendar list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarListResource.InsertRequest.#ctor(Google.Apis.Services.IClientService,Google.Apis.Calendar.v3.Data.CalendarListEntry)">
            <summary>Constructs a new Insert request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.InsertRequest.ColorRgbFormat">
            <summary>Whether to use the foregroundColor and backgroundColor fields to write the calendar colors
            (RGB). If this feature is used, the index-based colorId field will be set to the best matching option
            automatically. Optional. The default is False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.InsertRequest.Body">
            <summary>Gets or sets the body of this request.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarListResource.InsertRequest.GetBody">
            <summary>Returns the body of the request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.InsertRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.InsertRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.InsertRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarListResource.InsertRequest.InitParameters">
            <summary>Initializes Insert parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarListResource.List">
            <summary>Returns the calendars on the user's calendar list.</summary>
        </member>
        <member name="T:Google.Apis.Calendar.v3.CalendarListResource.ListRequest">
            <summary>Returns the calendars on the user's calendar list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarListResource.ListRequest.#ctor(Google.Apis.Services.IClientService)">
            <summary>Constructs a new List request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.ListRequest.MaxResults">
            <summary>Maximum number of entries returned on one result page. By default the value is 100 entries. The
            page size can never be larger than 250 entries. Optional.</summary>
            [minimum: 1]
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.ListRequest.MinAccessRole">
            <summary>The minimum access role for the user in the returned entries. Optional. The default is no
            restriction.</summary>
        </member>
        <member name="T:Google.Apis.Calendar.v3.CalendarListResource.ListRequest.MinAccessRoleEnum">
            <summary>The minimum access role for the user in the returned entries. Optional. The default is no
            restriction.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.CalendarListResource.ListRequest.MinAccessRoleEnum.FreeBusyReader">
            <summary>The user can read free/busy information.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.CalendarListResource.ListRequest.MinAccessRoleEnum.Owner">
            <summary>The user can read and modify events and access control lists.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.CalendarListResource.ListRequest.MinAccessRoleEnum.Reader">
            <summary>The user can read events that are not private.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.CalendarListResource.ListRequest.MinAccessRoleEnum.Writer">
            <summary>The user can read and modify events.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.ListRequest.PageToken">
            <summary>Token specifying which result page to return. Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.ListRequest.ShowDeleted">
            <summary>Whether to include deleted calendar list entries in the result. Optional. The default is
            False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.ListRequest.ShowHidden">
            <summary>Whether to show hidden entries. Optional. The default is False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.ListRequest.SyncToken">
            <summary>Token obtained from the nextSyncToken field returned on the last page of results from the
            previous list request. It makes the result of this list request contain only entries that have changed
            since then. If only read-only fields such as calendar properties or ACLs have changed, the entry won't
            be returned. All entries deleted and hidden since the previous list request will always be in the result
            set and it is not allowed to set showDeleted neither showHidden to False. To ensure client state
            consistency minAccessRole query parameter cannot be specified together with nextSyncToken. If the
            syncToken expires, the server will respond with a 410 GONE response code and the client should clear its
            storage and perform a full synchronization without any syncToken. Learn more about incremental
            synchronization. Optional. The default is to return all entries.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.ListRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.ListRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.ListRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarListResource.ListRequest.InitParameters">
            <summary>Initializes List parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarListResource.Patch(Google.Apis.Calendar.v3.Data.CalendarListEntry,System.String)">
            <summary>Updates an existing calendar on the user's calendar list. This method supports patch
            semantics.</summary>
            <param name="body">The body of the request.</param>
            <param name="calendarId">Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you
            want to access the primary calendar of the currently logged in user, use the "primary" keyword.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.CalendarListResource.PatchRequest">
            <summary>Updates an existing calendar on the user's calendar list. This method supports patch
            semantics.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarListResource.PatchRequest.#ctor(Google.Apis.Services.IClientService,Google.Apis.Calendar.v3.Data.CalendarListEntry,System.String)">
            <summary>Constructs a new Patch request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.PatchRequest.CalendarId">
            <summary>Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to
            access the primary calendar of the currently logged in user, use the "primary" keyword.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.PatchRequest.ColorRgbFormat">
            <summary>Whether to use the foregroundColor and backgroundColor fields to write the calendar colors
            (RGB). If this feature is used, the index-based colorId field will be set to the best matching option
            automatically. Optional. The default is False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.PatchRequest.Body">
            <summary>Gets or sets the body of this request.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarListResource.PatchRequest.GetBody">
            <summary>Returns the body of the request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.PatchRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.PatchRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.PatchRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarListResource.PatchRequest.InitParameters">
            <summary>Initializes Patch parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarListResource.Update(Google.Apis.Calendar.v3.Data.CalendarListEntry,System.String)">
            <summary>Updates an existing calendar on the user's calendar list.</summary>
            <param name="body">The body of the request.</param>
            <param name="calendarId">Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you
            want to access the primary calendar of the currently logged in user, use the "primary" keyword.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.CalendarListResource.UpdateRequest">
            <summary>Updates an existing calendar on the user's calendar list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarListResource.UpdateRequest.#ctor(Google.Apis.Services.IClientService,Google.Apis.Calendar.v3.Data.CalendarListEntry,System.String)">
            <summary>Constructs a new Update request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.UpdateRequest.CalendarId">
            <summary>Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to
            access the primary calendar of the currently logged in user, use the "primary" keyword.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.UpdateRequest.ColorRgbFormat">
            <summary>Whether to use the foregroundColor and backgroundColor fields to write the calendar colors
            (RGB). If this feature is used, the index-based colorId field will be set to the best matching option
            automatically. Optional. The default is False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.UpdateRequest.Body">
            <summary>Gets or sets the body of this request.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarListResource.UpdateRequest.GetBody">
            <summary>Returns the body of the request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.UpdateRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.UpdateRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.UpdateRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarListResource.UpdateRequest.InitParameters">
            <summary>Initializes Update parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarListResource.Watch(Google.Apis.Calendar.v3.Data.Channel)">
            <summary>Watch for changes to CalendarList resources.</summary>
            <param name="body">The body of the request.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.CalendarListResource.WatchRequest">
            <summary>Watch for changes to CalendarList resources.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarListResource.WatchRequest.#ctor(Google.Apis.Services.IClientService,Google.Apis.Calendar.v3.Data.Channel)">
            <summary>Constructs a new Watch request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.WatchRequest.MaxResults">
            <summary>Maximum number of entries returned on one result page. By default the value is 100 entries. The
            page size can never be larger than 250 entries. Optional.</summary>
            [minimum: 1]
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.WatchRequest.MinAccessRole">
            <summary>The minimum access role for the user in the returned entries. Optional. The default is no
            restriction.</summary>
        </member>
        <member name="T:Google.Apis.Calendar.v3.CalendarListResource.WatchRequest.MinAccessRoleEnum">
            <summary>The minimum access role for the user in the returned entries. Optional. The default is no
            restriction.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.CalendarListResource.WatchRequest.MinAccessRoleEnum.FreeBusyReader">
            <summary>The user can read free/busy information.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.CalendarListResource.WatchRequest.MinAccessRoleEnum.Owner">
            <summary>The user can read and modify events and access control lists.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.CalendarListResource.WatchRequest.MinAccessRoleEnum.Reader">
            <summary>The user can read events that are not private.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.CalendarListResource.WatchRequest.MinAccessRoleEnum.Writer">
            <summary>The user can read and modify events.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.WatchRequest.PageToken">
            <summary>Token specifying which result page to return. Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.WatchRequest.ShowDeleted">
            <summary>Whether to include deleted calendar list entries in the result. Optional. The default is
            False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.WatchRequest.ShowHidden">
            <summary>Whether to show hidden entries. Optional. The default is False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.WatchRequest.SyncToken">
            <summary>Token obtained from the nextSyncToken field returned on the last page of results from the
            previous list request. It makes the result of this list request contain only entries that have changed
            since then. If only read-only fields such as calendar properties or ACLs have changed, the entry won't
            be returned. All entries deleted and hidden since the previous list request will always be in the result
            set and it is not allowed to set showDeleted neither showHidden to False. To ensure client state
            consistency minAccessRole query parameter cannot be specified together with nextSyncToken. If the
            syncToken expires, the server will respond with a 410 GONE response code and the client should clear its
            storage and perform a full synchronization without any syncToken. Learn more about incremental
            synchronization. Optional. The default is to return all entries.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.WatchRequest.Body">
            <summary>Gets or sets the body of this request.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarListResource.WatchRequest.GetBody">
            <summary>Returns the body of the request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.WatchRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.WatchRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarListResource.WatchRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarListResource.WatchRequest.InitParameters">
            <summary>Initializes Watch parameter list.</summary>
        </member>
        <member name="T:Google.Apis.Calendar.v3.CalendarsResource">
            <summary>The "calendars" collection of methods.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.CalendarsResource.service">
            <summary>The service which this resource belongs to.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarsResource.#ctor(Google.Apis.Services.IClientService)">
            <summary>Constructs a new resource.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarsResource.Clear(System.String)">
            <summary>Clears a primary calendar. This operation deletes all events associated with the primary calendar
            of an account.</summary>
            <param name="calendarId">Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you
            want to access the primary calendar of the currently logged in user, use the "primary" keyword.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.CalendarsResource.ClearRequest">
            <summary>Clears a primary calendar. This operation deletes all events associated with the primary calendar
            of an account.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarsResource.ClearRequest.#ctor(Google.Apis.Services.IClientService,System.String)">
            <summary>Constructs a new Clear request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarsResource.ClearRequest.CalendarId">
            <summary>Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to
            access the primary calendar of the currently logged in user, use the "primary" keyword.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarsResource.ClearRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarsResource.ClearRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarsResource.ClearRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarsResource.ClearRequest.InitParameters">
            <summary>Initializes Clear parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarsResource.Delete(System.String)">
            <summary>Deletes a secondary calendar. Use calendars.clear for clearing all events on primary
            calendars.</summary>
            <param name="calendarId">Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you
            want to access the primary calendar of the currently logged in user, use the "primary" keyword.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.CalendarsResource.DeleteRequest">
            <summary>Deletes a secondary calendar. Use calendars.clear for clearing all events on primary
            calendars.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarsResource.DeleteRequest.#ctor(Google.Apis.Services.IClientService,System.String)">
            <summary>Constructs a new Delete request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarsResource.DeleteRequest.CalendarId">
            <summary>Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to
            access the primary calendar of the currently logged in user, use the "primary" keyword.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarsResource.DeleteRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarsResource.DeleteRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarsResource.DeleteRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarsResource.DeleteRequest.InitParameters">
            <summary>Initializes Delete parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarsResource.Get(System.String)">
            <summary>Returns metadata for a calendar.</summary>
            <param name="calendarId">Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you
            want to access the primary calendar of the currently logged in user, use the "primary" keyword.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.CalendarsResource.GetRequest">
            <summary>Returns metadata for a calendar.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarsResource.GetRequest.#ctor(Google.Apis.Services.IClientService,System.String)">
            <summary>Constructs a new Get request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarsResource.GetRequest.CalendarId">
            <summary>Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to
            access the primary calendar of the currently logged in user, use the "primary" keyword.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarsResource.GetRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarsResource.GetRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarsResource.GetRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarsResource.GetRequest.InitParameters">
            <summary>Initializes Get parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarsResource.Insert(Google.Apis.Calendar.v3.Data.Calendar)">
            <summary>Creates a secondary calendar.</summary>
            <param name="body">The body of the request.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.CalendarsResource.InsertRequest">
            <summary>Creates a secondary calendar.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarsResource.InsertRequest.#ctor(Google.Apis.Services.IClientService,Google.Apis.Calendar.v3.Data.Calendar)">
            <summary>Constructs a new Insert request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarsResource.InsertRequest.Body">
            <summary>Gets or sets the body of this request.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarsResource.InsertRequest.GetBody">
            <summary>Returns the body of the request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarsResource.InsertRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarsResource.InsertRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarsResource.InsertRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarsResource.InsertRequest.InitParameters">
            <summary>Initializes Insert parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarsResource.Patch(Google.Apis.Calendar.v3.Data.Calendar,System.String)">
            <summary>Updates metadata for a calendar. This method supports patch semantics.</summary>
            <param name="body">The body of the request.</param>
            <param name="calendarId">Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you
            want to access the primary calendar of the currently logged in user, use the "primary" keyword.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.CalendarsResource.PatchRequest">
            <summary>Updates metadata for a calendar. This method supports patch semantics.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarsResource.PatchRequest.#ctor(Google.Apis.Services.IClientService,Google.Apis.Calendar.v3.Data.Calendar,System.String)">
            <summary>Constructs a new Patch request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarsResource.PatchRequest.CalendarId">
            <summary>Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to
            access the primary calendar of the currently logged in user, use the "primary" keyword.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarsResource.PatchRequest.Body">
            <summary>Gets or sets the body of this request.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarsResource.PatchRequest.GetBody">
            <summary>Returns the body of the request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarsResource.PatchRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarsResource.PatchRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarsResource.PatchRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarsResource.PatchRequest.InitParameters">
            <summary>Initializes Patch parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarsResource.Update(Google.Apis.Calendar.v3.Data.Calendar,System.String)">
            <summary>Updates metadata for a calendar.</summary>
            <param name="body">The body of the request.</param>
            <param name="calendarId">Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you
            want to access the primary calendar of the currently logged in user, use the "primary" keyword.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.CalendarsResource.UpdateRequest">
            <summary>Updates metadata for a calendar.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarsResource.UpdateRequest.#ctor(Google.Apis.Services.IClientService,Google.Apis.Calendar.v3.Data.Calendar,System.String)">
            <summary>Constructs a new Update request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarsResource.UpdateRequest.CalendarId">
            <summary>Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to
            access the primary calendar of the currently logged in user, use the "primary" keyword.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarsResource.UpdateRequest.Body">
            <summary>Gets or sets the body of this request.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarsResource.UpdateRequest.GetBody">
            <summary>Returns the body of the request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarsResource.UpdateRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarsResource.UpdateRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.CalendarsResource.UpdateRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.CalendarsResource.UpdateRequest.InitParameters">
            <summary>Initializes Update parameter list.</summary>
        </member>
        <member name="T:Google.Apis.Calendar.v3.ChannelsResource">
            <summary>The "channels" collection of methods.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.ChannelsResource.service">
            <summary>The service which this resource belongs to.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.ChannelsResource.#ctor(Google.Apis.Services.IClientService)">
            <summary>Constructs a new resource.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.ChannelsResource.Stop(Google.Apis.Calendar.v3.Data.Channel)">
            <summary>Stop watching resources through this channel</summary>
            <param name="body">The body of the request.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.ChannelsResource.StopRequest">
            <summary>Stop watching resources through this channel</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.ChannelsResource.StopRequest.#ctor(Google.Apis.Services.IClientService,Google.Apis.Calendar.v3.Data.Channel)">
            <summary>Constructs a new Stop request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.ChannelsResource.StopRequest.Body">
            <summary>Gets or sets the body of this request.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.ChannelsResource.StopRequest.GetBody">
            <summary>Returns the body of the request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.ChannelsResource.StopRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.ChannelsResource.StopRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.ChannelsResource.StopRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.ChannelsResource.StopRequest.InitParameters">
            <summary>Initializes Stop parameter list.</summary>
        </member>
        <member name="T:Google.Apis.Calendar.v3.ColorsResource">
            <summary>The "colors" collection of methods.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.ColorsResource.service">
            <summary>The service which this resource belongs to.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.ColorsResource.#ctor(Google.Apis.Services.IClientService)">
            <summary>Constructs a new resource.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.ColorsResource.Get">
            <summary>Returns the color definitions for calendars and events.</summary>
        </member>
        <member name="T:Google.Apis.Calendar.v3.ColorsResource.GetRequest">
            <summary>Returns the color definitions for calendars and events.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.ColorsResource.GetRequest.#ctor(Google.Apis.Services.IClientService)">
            <summary>Constructs a new Get request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.ColorsResource.GetRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.ColorsResource.GetRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.ColorsResource.GetRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.ColorsResource.GetRequest.InitParameters">
            <summary>Initializes Get parameter list.</summary>
        </member>
        <member name="T:Google.Apis.Calendar.v3.EventsResource">
            <summary>The "events" collection of methods.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.EventsResource.service">
            <summary>The service which this resource belongs to.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.#ctor(Google.Apis.Services.IClientService)">
            <summary>Constructs a new resource.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.Delete(System.String,System.String)">
             <summary>Deletes an event.</summary>
             <param name="calendarId">Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you
             want to access the primary calendar of the currently logged in user, use the "primary" keyword.</param>
            
             <param name="eventId">Event identifier.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.EventsResource.DeleteRequest">
            <summary>Deletes an event.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.DeleteRequest.#ctor(Google.Apis.Services.IClientService,System.String,System.String)">
            <summary>Constructs a new Delete request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.DeleteRequest.CalendarId">
            <summary>Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to
            access the primary calendar of the currently logged in user, use the "primary" keyword.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.DeleteRequest.EventId">
            <summary>Event identifier.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.DeleteRequest.SendNotifications">
             <summary>Deprecated. Please use sendUpdates instead.
            
             Whether to send notifications about the deletion of the event. Note that some emails might still be sent
             even if you set the value to false. The default is false.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.DeleteRequest.SendUpdates">
            <summary>Guests who should receive notifications about the deletion of the event.</summary>
        </member>
        <member name="T:Google.Apis.Calendar.v3.EventsResource.DeleteRequest.SendUpdatesEnum">
            <summary>Guests who should receive notifications about the deletion of the event.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.EventsResource.DeleteRequest.SendUpdatesEnum.All">
            <summary>Notifications are sent to all guests.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.EventsResource.DeleteRequest.SendUpdatesEnum.ExternalOnly">
            <summary>Notifications are sent to non-Google Calendar guests only.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.EventsResource.DeleteRequest.SendUpdatesEnum.None">
            <summary>No notifications are sent. This value should only be used for migration use cases (note
            that in most migration cases the import method should be used).</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.DeleteRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.DeleteRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.DeleteRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.DeleteRequest.InitParameters">
            <summary>Initializes Delete parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.Get(System.String,System.String)">
             <summary>Returns an event.</summary>
             <param name="calendarId">Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you
             want to access the primary calendar of the currently logged in user, use the "primary" keyword.</param>
            
             <param name="eventId">Event identifier.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.EventsResource.GetRequest">
            <summary>Returns an event.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.GetRequest.#ctor(Google.Apis.Services.IClientService,System.String,System.String)">
            <summary>Constructs a new Get request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.GetRequest.CalendarId">
            <summary>Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to
            access the primary calendar of the currently logged in user, use the "primary" keyword.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.GetRequest.EventId">
            <summary>Event identifier.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.GetRequest.AlwaysIncludeEmail">
            <summary>Whether to always include a value in the email field for the organizer, creator and attendees,
            even if no real email is available (i.e. a generated, non-working value will be provided). The use of
            this option is discouraged and should only be used by clients which cannot handle the absence of an
            email address value in the mentioned places. Optional. The default is False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.GetRequest.MaxAttendees">
            <summary>The maximum number of attendees to include in the response. If there are more than the
            specified number of attendees, only the participant is returned. Optional.</summary>
            [minimum: 1]
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.GetRequest.TimeZone">
            <summary>Time zone used in the response. Optional. The default is the time zone of the
            calendar.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.GetRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.GetRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.GetRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.GetRequest.InitParameters">
            <summary>Initializes Get parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.Import(Google.Apis.Calendar.v3.Data.Event,System.String)">
            <summary>Imports an event. This operation is used to add a private copy of an existing event to a
            calendar.</summary>
            <param name="body">The body of the request.</param>
            <param name="calendarId">Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you
            want to access the primary calendar of the currently logged in user, use the "primary" keyword.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.EventsResource.ImportRequest">
            <summary>Imports an event. This operation is used to add a private copy of an existing event to a
            calendar.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.ImportRequest.#ctor(Google.Apis.Services.IClientService,Google.Apis.Calendar.v3.Data.Event,System.String)">
            <summary>Constructs a new Import request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.ImportRequest.CalendarId">
            <summary>Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to
            access the primary calendar of the currently logged in user, use the "primary" keyword.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.ImportRequest.ConferenceDataVersion">
            <summary>Version number of conference data supported by the API client. Version 0 assumes no conference
            data support and ignores conference data in the event's body. Version 1 enables support for copying of
            ConferenceData as well as for creating new conferences using the createRequest field of conferenceData.
            The default is 0.</summary>
            [minimum: 0]
            [maximum: 1]
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.ImportRequest.SupportsAttachments">
            <summary>Whether API client performing operation supports event attachments. Optional. The default is
            False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.ImportRequest.Body">
            <summary>Gets or sets the body of this request.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.ImportRequest.GetBody">
            <summary>Returns the body of the request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.ImportRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.ImportRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.ImportRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.ImportRequest.InitParameters">
            <summary>Initializes Import parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.Insert(Google.Apis.Calendar.v3.Data.Event,System.String)">
            <summary>Creates an event.</summary>
            <param name="body">The body of the request.</param>
            <param name="calendarId">Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you
            want to access the primary calendar of the currently logged in user, use the "primary" keyword.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.EventsResource.InsertRequest">
            <summary>Creates an event.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.InsertRequest.#ctor(Google.Apis.Services.IClientService,Google.Apis.Calendar.v3.Data.Event,System.String)">
            <summary>Constructs a new Insert request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.InsertRequest.CalendarId">
            <summary>Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to
            access the primary calendar of the currently logged in user, use the "primary" keyword.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.InsertRequest.ConferenceDataVersion">
            <summary>Version number of conference data supported by the API client. Version 0 assumes no conference
            data support and ignores conference data in the event's body. Version 1 enables support for copying of
            ConferenceData as well as for creating new conferences using the createRequest field of conferenceData.
            The default is 0.</summary>
            [minimum: 0]
            [maximum: 1]
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.InsertRequest.MaxAttendees">
            <summary>The maximum number of attendees to include in the response. If there are more than the
            specified number of attendees, only the participant is returned. Optional.</summary>
            [minimum: 1]
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.InsertRequest.SendNotifications">
             <summary>Deprecated. Please use sendUpdates instead.
            
             Whether to send notifications about the creation of the new event. Note that some emails might still be
             sent even if you set the value to false. The default is false.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.InsertRequest.SendUpdates">
            <summary>Whether to send notifications about the creation of the new event. Note that some emails might
            still be sent. The default is false.</summary>
        </member>
        <member name="T:Google.Apis.Calendar.v3.EventsResource.InsertRequest.SendUpdatesEnum">
            <summary>Whether to send notifications about the creation of the new event. Note that some emails might
            still be sent. The default is false.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.EventsResource.InsertRequest.SendUpdatesEnum.All">
            <summary>Notifications are sent to all guests.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.EventsResource.InsertRequest.SendUpdatesEnum.ExternalOnly">
            <summary>Notifications are sent to non-Google Calendar guests only.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.EventsResource.InsertRequest.SendUpdatesEnum.None">
            <summary>No notifications are sent. This value should only be used for migration use cases (note
            that in most migration cases the import method should be used).</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.InsertRequest.SupportsAttachments">
            <summary>Whether API client performing operation supports event attachments. Optional. The default is
            False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.InsertRequest.Body">
            <summary>Gets or sets the body of this request.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.InsertRequest.GetBody">
            <summary>Returns the body of the request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.InsertRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.InsertRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.InsertRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.InsertRequest.InitParameters">
            <summary>Initializes Insert parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.Instances(System.String,System.String)">
             <summary>Returns instances of the specified recurring event.</summary>
             <param name="calendarId">Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you
             want to access the primary calendar of the currently logged in user, use the "primary" keyword.</param>
            
             <param name="eventId">Recurring event identifier.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.EventsResource.InstancesRequest">
            <summary>Returns instances of the specified recurring event.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.InstancesRequest.#ctor(Google.Apis.Services.IClientService,System.String,System.String)">
            <summary>Constructs a new Instances request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.InstancesRequest.CalendarId">
            <summary>Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to
            access the primary calendar of the currently logged in user, use the "primary" keyword.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.InstancesRequest.EventId">
            <summary>Recurring event identifier.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.InstancesRequest.AlwaysIncludeEmail">
            <summary>Whether to always include a value in the email field for the organizer, creator and attendees,
            even if no real email is available (i.e. a generated, non-working value will be provided). The use of
            this option is discouraged and should only be used by clients which cannot handle the absence of an
            email address value in the mentioned places. Optional. The default is False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.InstancesRequest.MaxAttendees">
            <summary>The maximum number of attendees to include in the response. If there are more than the
            specified number of attendees, only the participant is returned. Optional.</summary>
            [minimum: 1]
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.InstancesRequest.MaxResults">
            <summary>Maximum number of events returned on one result page. By default the value is 250 events. The
            page size can never be larger than 2500 events. Optional.</summary>
            [minimum: 1]
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.InstancesRequest.OriginalStart">
            <summary>The original start time of the instance in the result. Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.InstancesRequest.PageToken">
            <summary>Token specifying which result page to return. Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.InstancesRequest.ShowDeleted">
            <summary>Whether to include deleted events (with status equals "cancelled") in the result. Cancelled
            instances of recurring events will still be included if singleEvents is False. Optional. The default is
            False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.InstancesRequest.TimeMax">
            <summary>Upper bound (exclusive) for an event's start time to filter by. Optional. The default is not to
            filter by start time. Must be an RFC3339 timestamp with mandatory time zone offset.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.InstancesRequest.TimeMin">
            <summary>Lower bound (inclusive) for an event's end time to filter by. Optional. The default is not to
            filter by end time. Must be an RFC3339 timestamp with mandatory time zone offset.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.InstancesRequest.TimeZone">
            <summary>Time zone used in the response. Optional. The default is the time zone of the
            calendar.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.InstancesRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.InstancesRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.InstancesRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.InstancesRequest.InitParameters">
            <summary>Initializes Instances parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.List(System.String)">
            <summary>Returns events on the specified calendar.</summary>
            <param name="calendarId">Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you
            want to access the primary calendar of the currently logged in user, use the "primary" keyword.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.EventsResource.ListRequest">
            <summary>Returns events on the specified calendar.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.ListRequest.#ctor(Google.Apis.Services.IClientService,System.String)">
            <summary>Constructs a new List request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.ListRequest.CalendarId">
            <summary>Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to
            access the primary calendar of the currently logged in user, use the "primary" keyword.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.ListRequest.AlwaysIncludeEmail">
            <summary>Whether to always include a value in the email field for the organizer, creator and attendees,
            even if no real email is available (i.e. a generated, non-working value will be provided). The use of
            this option is discouraged and should only be used by clients which cannot handle the absence of an
            email address value in the mentioned places. Optional. The default is False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.ListRequest.ICalUID">
            <summary>Specifies event ID in the iCalendar format to be included in the response. Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.ListRequest.MaxAttendees">
            <summary>The maximum number of attendees to include in the response. If there are more than the
            specified number of attendees, only the participant is returned. Optional.</summary>
            [minimum: 1]
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.ListRequest.MaxResults">
            <summary>Maximum number of events returned on one result page. The number of events in the resulting
            page may be less than this value, or none at all, even if there are more events matching the query.
            Incomplete pages can be detected by a non-empty nextPageToken field in the response. By default the
            value is 250 events. The page size can never be larger than 2500 events. Optional.</summary>
            [default: 250]
            [minimum: 1]
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.ListRequest.OrderBy">
            <summary>The order of the events returned in the result. Optional. The default is an unspecified, stable
            order.</summary>
        </member>
        <member name="T:Google.Apis.Calendar.v3.EventsResource.ListRequest.OrderByEnum">
            <summary>The order of the events returned in the result. Optional. The default is an unspecified, stable
            order.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.EventsResource.ListRequest.OrderByEnum.StartTime">
            <summary>Order by the start date/time (ascending). This is only available when querying single
            events (i.e. the parameter singleEvents is True)</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.EventsResource.ListRequest.OrderByEnum.Updated">
            <summary>Order by last modification time (ascending).</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.ListRequest.PageToken">
            <summary>Token specifying which result page to return. Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.ListRequest.PrivateExtendedProperty">
            <summary>Extended properties constraint specified as propertyName=value. Matches only private
            properties. This parameter might be repeated multiple times to return events that match all given
            constraints.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.ListRequest.Q">
            <summary>Free text search terms to find events that match these terms in any field, except for extended
            properties. Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.ListRequest.SharedExtendedProperty">
            <summary>Extended properties constraint specified as propertyName=value. Matches only shared properties.
            This parameter might be repeated multiple times to return events that match all given
            constraints.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.ListRequest.ShowDeleted">
            <summary>Whether to include deleted events (with status equals "cancelled") in the result. Cancelled
            instances of recurring events (but not the underlying recurring event) will still be included if
            showDeleted and singleEvents are both False. If showDeleted and singleEvents are both True, only single
            instances of deleted events (but not the underlying recurring events) are returned. Optional. The
            default is False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.ListRequest.ShowHiddenInvitations">
            <summary>Whether to include hidden invitations in the result. Optional. The default is False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.ListRequest.SingleEvents">
            <summary>Whether to expand recurring events into instances and only return single one-off events and
            instances of recurring events, but not the underlying recurring events themselves. Optional. The default
            is False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.ListRequest.SyncToken">
             <summary>Token obtained from the nextSyncToken field returned on the last page of results from the
             previous list request. It makes the result of this list request contain only entries that have changed
             since then. All events deleted since the previous list request will always be in the result set and it
             is not allowed to set showDeleted to False. There are several query parameters that cannot be specified
             together with nextSyncToken to ensure consistency of the client state.
            
             These are: - iCalUID - orderBy - privateExtendedProperty - q - sharedExtendedProperty - timeMin -
             timeMax - updatedMin If the syncToken expires, the server will respond with a 410 GONE response code and
             the client should clear its storage and perform a full synchronization without any syncToken. Learn more
             about incremental synchronization. Optional. The default is to return all entries.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.ListRequest.TimeMax">
            <summary>Upper bound (exclusive) for an event's start time to filter by. Optional. The default is not to
            filter by start time. Must be an RFC3339 timestamp with mandatory time zone offset, for example,
            2011-06-03T10:00:00-07:00, 2011-06-03T10:00:00Z. Milliseconds may be provided but are ignored. If
            timeMin is set, timeMax must be greater than timeMin.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.ListRequest.TimeMin">
            <summary>Lower bound (exclusive) for an event's end time to filter by. Optional. The default is not to
            filter by end time. Must be an RFC3339 timestamp with mandatory time zone offset, for example,
            2011-06-03T10:00:00-07:00, 2011-06-03T10:00:00Z. Milliseconds may be provided but are ignored. If
            timeMax is set, timeMin must be smaller than timeMax.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.ListRequest.TimeZone">
            <summary>Time zone used in the response. Optional. The default is the time zone of the
            calendar.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.ListRequest.UpdatedMin">
            <summary>Lower bound for an event's last modification time (as a RFC3339 timestamp) to filter by. When
            specified, entries deleted since this time will always be included regardless of showDeleted. Optional.
            The default is not to filter by last modification time.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.ListRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.ListRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.ListRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.ListRequest.InitParameters">
            <summary>Initializes List parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.Move(System.String,System.String,System.String)">
             <summary>Moves an event to another calendar, i.e. changes an event's organizer.</summary>
             <param name="calendarId">Calendar identifier of the source calendar where the event currently is on.</param>
            
             <param name="eventId">Event identifier.</param>
             <param name="destination">Calendar identifier of the target
             calendar where the event is to be moved to.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.EventsResource.MoveRequest">
            <summary>Moves an event to another calendar, i.e. changes an event's organizer.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.MoveRequest.#ctor(Google.Apis.Services.IClientService,System.String,System.String,System.String)">
            <summary>Constructs a new Move request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.MoveRequest.CalendarId">
            <summary>Calendar identifier of the source calendar where the event currently is on.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.MoveRequest.EventId">
            <summary>Event identifier.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.MoveRequest.Destination">
            <summary>Calendar identifier of the target calendar where the event is to be moved to.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.MoveRequest.SendNotifications">
             <summary>Deprecated. Please use sendUpdates instead.
            
             Whether to send notifications about the change of the event's organizer. Note that some emails might
             still be sent even if you set the value to false. The default is false.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.MoveRequest.SendUpdates">
            <summary>Guests who should receive notifications about the change of the event's organizer.</summary>
        </member>
        <member name="T:Google.Apis.Calendar.v3.EventsResource.MoveRequest.SendUpdatesEnum">
            <summary>Guests who should receive notifications about the change of the event's organizer.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.EventsResource.MoveRequest.SendUpdatesEnum.All">
            <summary>Notifications are sent to all guests.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.EventsResource.MoveRequest.SendUpdatesEnum.ExternalOnly">
            <summary>Notifications are sent to non-Google Calendar guests only.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.EventsResource.MoveRequest.SendUpdatesEnum.None">
            <summary>No notifications are sent. This value should only be used for migration use cases (note
            that in most migration cases the import method should be used).</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.MoveRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.MoveRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.MoveRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.MoveRequest.InitParameters">
            <summary>Initializes Move parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.Patch(Google.Apis.Calendar.v3.Data.Event,System.String,System.String)">
             <summary>Updates an event. This method supports patch semantics.</summary>
             <param name="body">The body of the request.</param>
             <param name="calendarId">Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you
             want to access the primary calendar of the currently logged in user, use the "primary" keyword.</param>
            
             <param name="eventId">Event identifier.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.EventsResource.PatchRequest">
            <summary>Updates an event. This method supports patch semantics.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.PatchRequest.#ctor(Google.Apis.Services.IClientService,Google.Apis.Calendar.v3.Data.Event,System.String,System.String)">
            <summary>Constructs a new Patch request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.PatchRequest.CalendarId">
            <summary>Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to
            access the primary calendar of the currently logged in user, use the "primary" keyword.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.PatchRequest.EventId">
            <summary>Event identifier.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.PatchRequest.AlwaysIncludeEmail">
            <summary>Whether to always include a value in the email field for the organizer, creator and attendees,
            even if no real email is available (i.e. a generated, non-working value will be provided). The use of
            this option is discouraged and should only be used by clients which cannot handle the absence of an
            email address value in the mentioned places. Optional. The default is False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.PatchRequest.ConferenceDataVersion">
            <summary>Version number of conference data supported by the API client. Version 0 assumes no conference
            data support and ignores conference data in the event's body. Version 1 enables support for copying of
            ConferenceData as well as for creating new conferences using the createRequest field of conferenceData.
            The default is 0.</summary>
            [minimum: 0]
            [maximum: 1]
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.PatchRequest.MaxAttendees">
            <summary>The maximum number of attendees to include in the response. If there are more than the
            specified number of attendees, only the participant is returned. Optional.</summary>
            [minimum: 1]
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.PatchRequest.SendNotifications">
             <summary>Deprecated. Please use sendUpdates instead.
            
             Whether to send notifications about the event update (for example, description changes, etc.). Note that
             some emails might still be sent even if you set the value to false. The default is false.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.PatchRequest.SendUpdates">
            <summary>Guests who should receive notifications about the event update (for example, title changes,
            etc.).</summary>
        </member>
        <member name="T:Google.Apis.Calendar.v3.EventsResource.PatchRequest.SendUpdatesEnum">
            <summary>Guests who should receive notifications about the event update (for example, title changes,
            etc.).</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.EventsResource.PatchRequest.SendUpdatesEnum.All">
            <summary>Notifications are sent to all guests.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.EventsResource.PatchRequest.SendUpdatesEnum.ExternalOnly">
            <summary>Notifications are sent to non-Google Calendar guests only.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.EventsResource.PatchRequest.SendUpdatesEnum.None">
            <summary>No notifications are sent. This value should only be used for migration use cases (note
            that in most migration cases the import method should be used).</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.PatchRequest.SupportsAttachments">
            <summary>Whether API client performing operation supports event attachments. Optional. The default is
            False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.PatchRequest.Body">
            <summary>Gets or sets the body of this request.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.PatchRequest.GetBody">
            <summary>Returns the body of the request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.PatchRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.PatchRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.PatchRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.PatchRequest.InitParameters">
            <summary>Initializes Patch parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.QuickAdd(System.String,System.String)">
             <summary>Creates an event based on a simple text string.</summary>
             <param name="calendarId">Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you
             want to access the primary calendar of the currently logged in user, use the "primary" keyword.</param>
            
             <param name="text">The text describing the event to be created.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.EventsResource.QuickAddRequest">
            <summary>Creates an event based on a simple text string.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.QuickAddRequest.#ctor(Google.Apis.Services.IClientService,System.String,System.String)">
            <summary>Constructs a new QuickAdd request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.QuickAddRequest.CalendarId">
            <summary>Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to
            access the primary calendar of the currently logged in user, use the "primary" keyword.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.QuickAddRequest.Text">
            <summary>The text describing the event to be created.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.QuickAddRequest.SendNotifications">
             <summary>Deprecated. Please use sendUpdates instead.
            
             Whether to send notifications about the creation of the event. Note that some emails might still be sent
             even if you set the value to false. The default is false.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.QuickAddRequest.SendUpdates">
            <summary>Guests who should receive notifications about the creation of the new event.</summary>
        </member>
        <member name="T:Google.Apis.Calendar.v3.EventsResource.QuickAddRequest.SendUpdatesEnum">
            <summary>Guests who should receive notifications about the creation of the new event.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.EventsResource.QuickAddRequest.SendUpdatesEnum.All">
            <summary>Notifications are sent to all guests.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.EventsResource.QuickAddRequest.SendUpdatesEnum.ExternalOnly">
            <summary>Notifications are sent to non-Google Calendar guests only.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.EventsResource.QuickAddRequest.SendUpdatesEnum.None">
            <summary>No notifications are sent. This value should only be used for migration use cases (note
            that in most migration cases the import method should be used).</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.QuickAddRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.QuickAddRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.QuickAddRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.QuickAddRequest.InitParameters">
            <summary>Initializes QuickAdd parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.Update(Google.Apis.Calendar.v3.Data.Event,System.String,System.String)">
             <summary>Updates an event.</summary>
             <param name="body">The body of the request.</param>
             <param name="calendarId">Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you
             want to access the primary calendar of the currently logged in user, use the "primary" keyword.</param>
            
             <param name="eventId">Event identifier.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.EventsResource.UpdateRequest">
            <summary>Updates an event.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.UpdateRequest.#ctor(Google.Apis.Services.IClientService,Google.Apis.Calendar.v3.Data.Event,System.String,System.String)">
            <summary>Constructs a new Update request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.UpdateRequest.CalendarId">
            <summary>Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to
            access the primary calendar of the currently logged in user, use the "primary" keyword.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.UpdateRequest.EventId">
            <summary>Event identifier.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.UpdateRequest.AlwaysIncludeEmail">
            <summary>Whether to always include a value in the email field for the organizer, creator and attendees,
            even if no real email is available (i.e. a generated, non-working value will be provided). The use of
            this option is discouraged and should only be used by clients which cannot handle the absence of an
            email address value in the mentioned places. Optional. The default is False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.UpdateRequest.ConferenceDataVersion">
            <summary>Version number of conference data supported by the API client. Version 0 assumes no conference
            data support and ignores conference data in the event's body. Version 1 enables support for copying of
            ConferenceData as well as for creating new conferences using the createRequest field of conferenceData.
            The default is 0.</summary>
            [minimum: 0]
            [maximum: 1]
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.UpdateRequest.MaxAttendees">
            <summary>The maximum number of attendees to include in the response. If there are more than the
            specified number of attendees, only the participant is returned. Optional.</summary>
            [minimum: 1]
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.UpdateRequest.SendNotifications">
             <summary>Deprecated. Please use sendUpdates instead.
            
             Whether to send notifications about the event update (for example, description changes, etc.). Note that
             some emails might still be sent even if you set the value to false. The default is false.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.UpdateRequest.SendUpdates">
            <summary>Guests who should receive notifications about the event update (for example, title changes,
            etc.).</summary>
        </member>
        <member name="T:Google.Apis.Calendar.v3.EventsResource.UpdateRequest.SendUpdatesEnum">
            <summary>Guests who should receive notifications about the event update (for example, title changes,
            etc.).</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.EventsResource.UpdateRequest.SendUpdatesEnum.All">
            <summary>Notifications are sent to all guests.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.EventsResource.UpdateRequest.SendUpdatesEnum.ExternalOnly">
            <summary>Notifications are sent to non-Google Calendar guests only.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.EventsResource.UpdateRequest.SendUpdatesEnum.None">
            <summary>No notifications are sent. This value should only be used for migration use cases (note
            that in most migration cases the import method should be used).</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.UpdateRequest.SupportsAttachments">
            <summary>Whether API client performing operation supports event attachments. Optional. The default is
            False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.UpdateRequest.Body">
            <summary>Gets or sets the body of this request.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.UpdateRequest.GetBody">
            <summary>Returns the body of the request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.UpdateRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.UpdateRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.UpdateRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.UpdateRequest.InitParameters">
            <summary>Initializes Update parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.Watch(Google.Apis.Calendar.v3.Data.Channel,System.String)">
            <summary>Watch for changes to Events resources.</summary>
            <param name="body">The body of the request.</param>
            <param name="calendarId">Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you
            want to access the primary calendar of the currently logged in user, use the "primary" keyword.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.EventsResource.WatchRequest">
            <summary>Watch for changes to Events resources.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.WatchRequest.#ctor(Google.Apis.Services.IClientService,Google.Apis.Calendar.v3.Data.Channel,System.String)">
            <summary>Constructs a new Watch request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.WatchRequest.CalendarId">
            <summary>Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to
            access the primary calendar of the currently logged in user, use the "primary" keyword.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.WatchRequest.AlwaysIncludeEmail">
            <summary>Whether to always include a value in the email field for the organizer, creator and attendees,
            even if no real email is available (i.e. a generated, non-working value will be provided). The use of
            this option is discouraged and should only be used by clients which cannot handle the absence of an
            email address value in the mentioned places. Optional. The default is False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.WatchRequest.ICalUID">
            <summary>Specifies event ID in the iCalendar format to be included in the response. Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.WatchRequest.MaxAttendees">
            <summary>The maximum number of attendees to include in the response. If there are more than the
            specified number of attendees, only the participant is returned. Optional.</summary>
            [minimum: 1]
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.WatchRequest.MaxResults">
            <summary>Maximum number of events returned on one result page. The number of events in the resulting
            page may be less than this value, or none at all, even if there are more events matching the query.
            Incomplete pages can be detected by a non-empty nextPageToken field in the response. By default the
            value is 250 events. The page size can never be larger than 2500 events. Optional.</summary>
            [default: 250]
            [minimum: 1]
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.WatchRequest.OrderBy">
            <summary>The order of the events returned in the result. Optional. The default is an unspecified, stable
            order.</summary>
        </member>
        <member name="T:Google.Apis.Calendar.v3.EventsResource.WatchRequest.OrderByEnum">
            <summary>The order of the events returned in the result. Optional. The default is an unspecified, stable
            order.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.EventsResource.WatchRequest.OrderByEnum.StartTime">
            <summary>Order by the start date/time (ascending). This is only available when querying single
            events (i.e. the parameter singleEvents is True)</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.EventsResource.WatchRequest.OrderByEnum.Updated">
            <summary>Order by last modification time (ascending).</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.WatchRequest.PageToken">
            <summary>Token specifying which result page to return. Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.WatchRequest.PrivateExtendedProperty">
            <summary>Extended properties constraint specified as propertyName=value. Matches only private
            properties. This parameter might be repeated multiple times to return events that match all given
            constraints.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.WatchRequest.Q">
            <summary>Free text search terms to find events that match these terms in any field, except for extended
            properties. Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.WatchRequest.SharedExtendedProperty">
            <summary>Extended properties constraint specified as propertyName=value. Matches only shared properties.
            This parameter might be repeated multiple times to return events that match all given
            constraints.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.WatchRequest.ShowDeleted">
            <summary>Whether to include deleted events (with status equals "cancelled") in the result. Cancelled
            instances of recurring events (but not the underlying recurring event) will still be included if
            showDeleted and singleEvents are both False. If showDeleted and singleEvents are both True, only single
            instances of deleted events (but not the underlying recurring events) are returned. Optional. The
            default is False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.WatchRequest.ShowHiddenInvitations">
            <summary>Whether to include hidden invitations in the result. Optional. The default is False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.WatchRequest.SingleEvents">
            <summary>Whether to expand recurring events into instances and only return single one-off events and
            instances of recurring events, but not the underlying recurring events themselves. Optional. The default
            is False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.WatchRequest.SyncToken">
             <summary>Token obtained from the nextSyncToken field returned on the last page of results from the
             previous list request. It makes the result of this list request contain only entries that have changed
             since then. All events deleted since the previous list request will always be in the result set and it
             is not allowed to set showDeleted to False. There are several query parameters that cannot be specified
             together with nextSyncToken to ensure consistency of the client state.
            
             These are: - iCalUID - orderBy - privateExtendedProperty - q - sharedExtendedProperty - timeMin -
             timeMax - updatedMin If the syncToken expires, the server will respond with a 410 GONE response code and
             the client should clear its storage and perform a full synchronization without any syncToken. Learn more
             about incremental synchronization. Optional. The default is to return all entries.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.WatchRequest.TimeMax">
            <summary>Upper bound (exclusive) for an event's start time to filter by. Optional. The default is not to
            filter by start time. Must be an RFC3339 timestamp with mandatory time zone offset, for example,
            2011-06-03T10:00:00-07:00, 2011-06-03T10:00:00Z. Milliseconds may be provided but are ignored. If
            timeMin is set, timeMax must be greater than timeMin.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.WatchRequest.TimeMin">
            <summary>Lower bound (exclusive) for an event's end time to filter by. Optional. The default is not to
            filter by end time. Must be an RFC3339 timestamp with mandatory time zone offset, for example,
            2011-06-03T10:00:00-07:00, 2011-06-03T10:00:00Z. Milliseconds may be provided but are ignored. If
            timeMax is set, timeMin must be smaller than timeMax.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.WatchRequest.TimeZone">
            <summary>Time zone used in the response. Optional. The default is the time zone of the
            calendar.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.WatchRequest.UpdatedMin">
            <summary>Lower bound for an event's last modification time (as a RFC3339 timestamp) to filter by. When
            specified, entries deleted since this time will always be included regardless of showDeleted. Optional.
            The default is not to filter by last modification time.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.WatchRequest.Body">
            <summary>Gets or sets the body of this request.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.WatchRequest.GetBody">
            <summary>Returns the body of the request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.WatchRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.WatchRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.EventsResource.WatchRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.EventsResource.WatchRequest.InitParameters">
            <summary>Initializes Watch parameter list.</summary>
        </member>
        <member name="T:Google.Apis.Calendar.v3.FreebusyResource">
            <summary>The "freebusy" collection of methods.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.FreebusyResource.service">
            <summary>The service which this resource belongs to.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.FreebusyResource.#ctor(Google.Apis.Services.IClientService)">
            <summary>Constructs a new resource.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.FreebusyResource.Query(Google.Apis.Calendar.v3.Data.FreeBusyRequest)">
            <summary>Returns free/busy information for a set of calendars.</summary>
            <param name="body">The body of the request.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.FreebusyResource.QueryRequest">
            <summary>Returns free/busy information for a set of calendars.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.FreebusyResource.QueryRequest.#ctor(Google.Apis.Services.IClientService,Google.Apis.Calendar.v3.Data.FreeBusyRequest)">
            <summary>Constructs a new Query request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.FreebusyResource.QueryRequest.Body">
            <summary>Gets or sets the body of this request.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.FreebusyResource.QueryRequest.GetBody">
            <summary>Returns the body of the request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.FreebusyResource.QueryRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.FreebusyResource.QueryRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.FreebusyResource.QueryRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.FreebusyResource.QueryRequest.InitParameters">
            <summary>Initializes Query parameter list.</summary>
        </member>
        <member name="T:Google.Apis.Calendar.v3.SettingsResource">
            <summary>The "settings" collection of methods.</summary>
        </member>
        <member name="F:Google.Apis.Calendar.v3.SettingsResource.service">
            <summary>The service which this resource belongs to.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.SettingsResource.#ctor(Google.Apis.Services.IClientService)">
            <summary>Constructs a new resource.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.SettingsResource.Get(System.String)">
            <summary>Returns a single user setting.</summary>
            <param name="setting">The id of the user setting.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.SettingsResource.GetRequest">
            <summary>Returns a single user setting.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.SettingsResource.GetRequest.#ctor(Google.Apis.Services.IClientService,System.String)">
            <summary>Constructs a new Get request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.SettingsResource.GetRequest.Setting">
            <summary>The id of the user setting.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.SettingsResource.GetRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.SettingsResource.GetRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.SettingsResource.GetRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.SettingsResource.GetRequest.InitParameters">
            <summary>Initializes Get parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.SettingsResource.List">
            <summary>Returns all user settings for the authenticated user.</summary>
        </member>
        <member name="T:Google.Apis.Calendar.v3.SettingsResource.ListRequest">
            <summary>Returns all user settings for the authenticated user.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.SettingsResource.ListRequest.#ctor(Google.Apis.Services.IClientService)">
            <summary>Constructs a new List request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.SettingsResource.ListRequest.MaxResults">
            <summary>Maximum number of entries returned on one result page. By default the value is 100 entries. The
            page size can never be larger than 250 entries. Optional.</summary>
            [minimum: 1]
        </member>
        <member name="P:Google.Apis.Calendar.v3.SettingsResource.ListRequest.PageToken">
            <summary>Token specifying which result page to return. Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.SettingsResource.ListRequest.SyncToken">
            <summary>Token obtained from the nextSyncToken field returned on the last page of results from the
            previous list request. It makes the result of this list request contain only entries that have changed
            since then. If the syncToken expires, the server will respond with a 410 GONE response code and the
            client should clear its storage and perform a full synchronization without any syncToken. Learn more
            about incremental synchronization. Optional. The default is to return all entries.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.SettingsResource.ListRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.SettingsResource.ListRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.SettingsResource.ListRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.SettingsResource.ListRequest.InitParameters">
            <summary>Initializes List parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.SettingsResource.Watch(Google.Apis.Calendar.v3.Data.Channel)">
            <summary>Watch for changes to Settings resources.</summary>
            <param name="body">The body of the request.</param>
        </member>
        <member name="T:Google.Apis.Calendar.v3.SettingsResource.WatchRequest">
            <summary>Watch for changes to Settings resources.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.SettingsResource.WatchRequest.#ctor(Google.Apis.Services.IClientService,Google.Apis.Calendar.v3.Data.Channel)">
            <summary>Constructs a new Watch request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.SettingsResource.WatchRequest.MaxResults">
            <summary>Maximum number of entries returned on one result page. By default the value is 100 entries. The
            page size can never be larger than 250 entries. Optional.</summary>
            [minimum: 1]
        </member>
        <member name="P:Google.Apis.Calendar.v3.SettingsResource.WatchRequest.PageToken">
            <summary>Token specifying which result page to return. Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.SettingsResource.WatchRequest.SyncToken">
            <summary>Token obtained from the nextSyncToken field returned on the last page of results from the
            previous list request. It makes the result of this list request contain only entries that have changed
            since then. If the syncToken expires, the server will respond with a 410 GONE response code and the
            client should clear its storage and perform a full synchronization without any syncToken. Learn more
            about incremental synchronization. Optional. The default is to return all entries.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.SettingsResource.WatchRequest.Body">
            <summary>Gets or sets the body of this request.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.SettingsResource.WatchRequest.GetBody">
            <summary>Returns the body of the request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.SettingsResource.WatchRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.SettingsResource.WatchRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.SettingsResource.WatchRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Calendar.v3.SettingsResource.WatchRequest.InitParameters">
            <summary>Initializes Watch parameter list.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Acl.ETag">
            <summary>ETag of the collection.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Acl.Items">
            <summary>List of rules on the access control list.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Acl.Kind">
            <summary>Type of the collection ("calendar#acl").</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Acl.NextPageToken">
            <summary>Token used to access the next page of this result. Omitted if no further results are available, in
            which case nextSyncToken is provided.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Acl.NextSyncToken">
            <summary>Token used at a later point in time to retrieve only the entries that have changed since this
            result was returned. Omitted if further results are available, in which case nextPageToken is
            provided.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.AclRule.ETag">
            <summary>ETag of the resource.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.AclRule.Id">
            <summary>Identifier of the ACL rule.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.AclRule.Kind">
            <summary>Type of the resource ("calendar#aclRule").</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.AclRule.Role">
            <summary>The role assigned to the scope. Possible values are: - "none" - Provides no access. -
            "freeBusyReader" - Provides read access to free/busy information. - "reader" - Provides read access to the
            calendar. Private events will appear to users with reader access, but event details will be hidden. -
            "writer" - Provides read and write access to the calendar. Private events will appear to users with writer
            access, and event details will be visible. - "owner" - Provides ownership of the calendar. This role has all
            of the permissions of the writer role with the additional ability to see and manipulate ACLs.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.AclRule.Scope">
            <summary>The scope of the rule.</summary>
        </member>
        <member name="T:Google.Apis.Calendar.v3.Data.AclRule.ScopeData">
            <summary>The scope of the rule.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.AclRule.ScopeData.Type">
            <summary>The type of the scope. Possible values are: - "default" - The public scope. This is the default
            value. - "user" - Limits the scope to a single user. - "group" - Limits the scope to a group. - "domain"
            - Limits the scope to a domain.  Note: The permissions granted to the "default", or public, scope apply
            to any user, authenticated or not.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.AclRule.ScopeData.Value">
            <summary>The email address of a user or group, or the name of a domain, depending on the scope type.
            Omitted for type "default".</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Calendar.ConferenceProperties">
            <summary>Conferencing properties for this calendar, for example what types of conferences are
            allowed.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Calendar.Description">
            <summary>Description of the calendar. Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Calendar.ETag">
            <summary>ETag of the resource.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Calendar.Id">
            <summary>Identifier of the calendar. To retrieve IDs call the calendarList.list() method.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Calendar.Kind">
            <summary>Type of the resource ("calendar#calendar").</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Calendar.Location">
            <summary>Geographic location of the calendar as free-form text. Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Calendar.Summary">
            <summary>Title of the calendar.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Calendar.TimeZone">
            <summary>The time zone of the calendar. (Formatted as an IANA Time Zone Database name, e.g.
            "Europe/Zurich".) Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.CalendarList.ETag">
            <summary>ETag of the collection.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.CalendarList.Items">
            <summary>Calendars that are present on the user's calendar list.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.CalendarList.Kind">
            <summary>Type of the collection ("calendar#calendarList").</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.CalendarList.NextPageToken">
            <summary>Token used to access the next page of this result. Omitted if no further results are available, in
            which case nextSyncToken is provided.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.CalendarList.NextSyncToken">
            <summary>Token used at a later point in time to retrieve only the entries that have changed since this
            result was returned. Omitted if further results are available, in which case nextPageToken is
            provided.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.CalendarListEntry.AccessRole">
            <summary>The effective access role that the authenticated user has on the calendar. Read-only. Possible
            values are: - "freeBusyReader" - Provides read access to free/busy information. - "reader" - Provides read
            access to the calendar. Private events will appear to users with reader access, but event details will be
            hidden. - "writer" - Provides read and write access to the calendar. Private events will appear to users
            with writer access, and event details will be visible. - "owner" - Provides ownership of the calendar. This
            role has all of the permissions of the writer role with the additional ability to see and manipulate
            ACLs.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.CalendarListEntry.BackgroundColor">
            <summary>The main color of the calendar in the hexadecimal format "#0088aa". This property supersedes the
            index-based colorId property. To set or change this property, you need to specify colorRgbFormat=true in the
            parameters of the insert, update and patch methods. Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.CalendarListEntry.ColorId">
            <summary>The color of the calendar. This is an ID referring to an entry in the calendar section of the
            colors definition (see the colors endpoint). This property is superseded by the backgroundColor and
            foregroundColor properties and can be ignored when using these properties. Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.CalendarListEntry.ConferenceProperties">
            <summary>Conferencing properties for this calendar, for example what types of conferences are
            allowed.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.CalendarListEntry.DefaultReminders">
            <summary>The default reminders that the authenticated user has for this calendar.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.CalendarListEntry.Deleted">
            <summary>Whether this calendar list entry has been deleted from the calendar list. Read-only. Optional. The
            default is False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.CalendarListEntry.Description">
            <summary>Description of the calendar. Optional. Read-only.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.CalendarListEntry.ETag">
            <summary>ETag of the resource.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.CalendarListEntry.ForegroundColor">
            <summary>The foreground color of the calendar in the hexadecimal format "#ffffff". This property supersedes
            the index-based colorId property. To set or change this property, you need to specify colorRgbFormat=true in
            the parameters of the insert, update and patch methods. Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.CalendarListEntry.Hidden">
            <summary>Whether the calendar has been hidden from the list. Optional. The default is False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.CalendarListEntry.Id">
            <summary>Identifier of the calendar.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.CalendarListEntry.Kind">
            <summary>Type of the resource ("calendar#calendarListEntry").</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.CalendarListEntry.Location">
            <summary>Geographic location of the calendar as free-form text. Optional. Read-only.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.CalendarListEntry.NotificationSettings">
            <summary>The notifications that the authenticated user is receiving for this calendar.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.CalendarListEntry.Primary">
            <summary>Whether the calendar is the primary calendar of the authenticated user. Read-only. Optional. The
            default is False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.CalendarListEntry.Selected">
            <summary>Whether the calendar content shows up in the calendar UI. Optional. The default is False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.CalendarListEntry.Summary">
            <summary>Title of the calendar. Read-only.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.CalendarListEntry.SummaryOverride">
            <summary>The summary that the authenticated user has set for this calendar. Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.CalendarListEntry.TimeZone">
            <summary>The time zone of the calendar. Optional. Read-only.</summary>
        </member>
        <member name="T:Google.Apis.Calendar.v3.Data.CalendarListEntry.NotificationSettingsData">
            <summary>The notifications that the authenticated user is receiving for this calendar.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.CalendarListEntry.NotificationSettingsData.Notifications">
            <summary>The list of notifications set for this calendar.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.CalendarNotification.Method">
            <summary>The method used to deliver the notification. Possible values are: - "email" - Notifications are
            sent via email. - "sms" - Deprecated. Once this feature is shutdown, the API will no longer return
            notifications using this method. Any newly added SMS notifications will be ignored. See  Google Calendar SMS
            notifications to be removed for more information. Notifications are sent via SMS. This value is read-only
            and is ignored on inserts and updates. SMS notifications are only available for G Suite customers. Required
            when adding a notification.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.CalendarNotification.Type">
            <summary>The type of notification. Possible values are: - "eventCreation" - Notification sent when a new
            event is put on the calendar. - "eventChange" - Notification sent when an event is changed. -
            "eventCancellation" - Notification sent when an event is cancelled. - "eventResponse" - Notification sent
            when an attendee responds to the event invitation. - "agenda" - An agenda with the events of the day (sent
            out in the morning). Required when adding a notification.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.CalendarNotification.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Channel.Address">
            <summary>The address where notifications are delivered for this channel.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Channel.Expiration">
            <summary>Date and time of notification channel expiration, expressed as a Unix timestamp, in milliseconds.
            Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Channel.Id">
            <summary>A UUID or similar unique string that identifies this channel.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Channel.Kind">
            <summary>Identifies this as a notification channel used to watch for changes to a resource. Value: the fixed
            string "api#channel".</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Channel.Params__">
            <summary>Additional parameters controlling delivery channel behavior. Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Channel.Payload">
            <summary>A Boolean value to indicate whether payload is wanted. Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Channel.ResourceId">
            <summary>An opaque ID that identifies the resource being watched on this channel. Stable across different
            API versions.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Channel.ResourceUri">
            <summary>A version-specific identifier for the watched resource.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Channel.Token">
            <summary>An arbitrary string delivered to the target address with each notification delivered over this
            channel. Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Channel.Type">
            <summary>The type of delivery mechanism used for this channel.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Channel.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.ColorDefinition.Background">
            <summary>The background color associated with this color definition.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.ColorDefinition.Foreground">
            <summary>The foreground color that can be used to write on top of a background with 'background'
            color.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.ColorDefinition.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Colors.Calendar">
            <summary>A global palette of calendar colors, mapping from the color ID to its definition. A
            calendarListEntry resource refers to one of these color IDs in its color field. Read-only.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Colors.Event__">
            <summary>A global palette of event colors, mapping from the color ID to its definition. An event resource
            may refer to one of these color IDs in its color field. Read-only.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Colors.Kind">
            <summary>Type of the resource ("calendar#colors").</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Colors.UpdatedRaw">
            <summary>Last modification time of the color palette (as a RFC3339 timestamp). Read-only.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Colors.Updated">
            <summary><seealso cref="T:System.DateTime"/> representation of <see cref="P:Google.Apis.Calendar.v3.Data.Colors.UpdatedRaw"/>.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Colors.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.ConferenceData.ConferenceId">
            <summary>The ID of the conference. Can be used by developers to keep track of conferences, should not be
            displayed to users. Values for solution types: - "eventHangout": unset. - "eventNamedHangout": the name of
            the Hangout. - "hangoutsMeet": the 10-letter meeting code, for example "aaa-bbbb-ccc".  Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.ConferenceData.ConferenceSolution">
            <summary>The conference solution, such as Hangouts or Hangouts Meet. Unset for a conference with a failed
            create request. Either conferenceSolution and at least one entryPoint, or createRequest is
            required.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.ConferenceData.CreateRequest">
            <summary>A request to generate a new conference and attach it to the event. The data is generated
            asynchronously. To see whether the data is present check the status field. Either conferenceSolution and at
            least one entryPoint, or createRequest is required.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.ConferenceData.EntryPoints">
            <summary>Information about individual conference entry points, such as URLs or phone numbers. All of them
            must belong to the same conference. Either conferenceSolution and at least one entryPoint, or createRequest
            is required.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.ConferenceData.Notes">
            <summary>Additional notes (such as instructions from the domain administrator, legal notices) to display to
            the user. Can contain HTML. The maximum length is 2048 characters. Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.ConferenceData.Parameters">
            <summary>Additional properties related to a conference. An example would be a solution-specific setting for
            enabling video streaming.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.ConferenceData.Signature">
            <summary>The signature of the conference data. Generated on server side. Must be preserved while copying the
            conference data between events, otherwise the conference data will not be copied. Unset for a conference
            with a failed create request. Optional for a conference with a pending create request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.ConferenceData.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.ConferenceParameters.AddOnParameters">
            <summary>Additional add-on specific data.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.ConferenceParameters.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.ConferenceParametersAddOnParameters.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.ConferenceProperties.AllowedConferenceSolutionTypes">
            <summary>The types of conference solutions that are supported for this calendar. The possible values are: -
            "eventHangout" - "eventNamedHangout" - "hangoutsMeet"  Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.ConferenceProperties.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.ConferenceRequestStatus.StatusCode">
            <summary>The current status of the conference create request. Read-only. The possible values are: -
            "pending": the conference create request is still being processed. - "success": the conference create
            request succeeded, the entry points are populated. - "failure": the conference create request failed, there
            are no entry points.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.ConferenceRequestStatus.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.ConferenceSolution.IconUri">
            <summary>The user-visible icon for this solution.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.ConferenceSolution.Key">
            <summary>The key which can uniquely identify the conference solution for this event.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.ConferenceSolution.Name">
            <summary>The user-visible name of this solution. Not localized.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.ConferenceSolution.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.ConferenceSolutionKey.Type">
            <summary>The conference solution type. If a client encounters an unfamiliar or empty type, it should still
            be able to display the entry points. However, it should disallow modifications. The possible values are: -
            "eventHangout" for Hangouts for consumers (http://hangouts.google.com) - "eventNamedHangout" for classic
            Hangouts for G Suite users (http://hangouts.google.com) - "hangoutsMeet" for Hangouts Meet
            (http://meet.google.com)</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.ConferenceSolutionKey.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.CreateConferenceRequest.ConferenceSolutionKey">
            <summary>The conference solution, such as Hangouts or Hangouts Meet.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.CreateConferenceRequest.RequestId">
            <summary>The client-generated unique ID for this request. Clients should regenerate this ID for every new
            request. If an ID provided is the same as for the previous request, the request is ignored.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.CreateConferenceRequest.Status">
            <summary>The status of the conference create request.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.CreateConferenceRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EntryPoint.AccessCode">
            <summary>The access code to access the conference. The maximum length is 128 characters. When creating new
            conference data, populate only the subset of {meetingCode, accessCode, passcode, password, pin} fields that
            match the terminology that the conference provider uses. Only the populated fields should be displayed.
            Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EntryPoint.EntryPointFeatures">
            <summary>Features of the entry point, such as being toll or toll-free. One entry point can have multiple
            features. However, toll and toll-free cannot be both set on the same entry point.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EntryPoint.EntryPointType">
            <summary>The type of the conference entry point. Possible values are: - "video" - joining a conference over
            HTTP. A conference can have zero or one video entry point. - "phone" - joining a conference by dialing a
            phone number. A conference can have zero or more phone entry points. - "sip" - joining a conference over
            SIP. A conference can have zero or one sip entry point. - "more" - further conference joining instructions,
            for example additional phone numbers. A conference can have zero or one more entry point. A conference with
            only a more entry point is not a valid conference.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EntryPoint.Label">
            <summary>The label for the URI. Visible to end users. Not localized. The maximum length is 512 characters.
            Examples: - for video: meet.google.com/aaa-bbbb-ccc - for phone: ****** 268 2601 - for sip:
            <EMAIL> - for more: should not be filled Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EntryPoint.MeetingCode">
            <summary>The meeting code to access the conference. The maximum length is 128 characters. When creating new
            conference data, populate only the subset of {meetingCode, accessCode, passcode, password, pin} fields that
            match the terminology that the conference provider uses. Only the populated fields should be displayed.
            Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EntryPoint.Passcode">
            <summary>The passcode to access the conference. The maximum length is 128 characters. When creating new
            conference data, populate only the subset of {meetingCode, accessCode, passcode, password, pin} fields that
            match the terminology that the conference provider uses. Only the populated fields should be
            displayed.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EntryPoint.Password">
            <summary>The password to access the conference. The maximum length is 128 characters. When creating new
            conference data, populate only the subset of {meetingCode, accessCode, passcode, password, pin} fields that
            match the terminology that the conference provider uses. Only the populated fields should be displayed.
            Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EntryPoint.Pin">
            <summary>The PIN to access the conference. The maximum length is 128 characters. When creating new
            conference data, populate only the subset of {meetingCode, accessCode, passcode, password, pin} fields that
            match the terminology that the conference provider uses. Only the populated fields should be displayed.
            Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EntryPoint.RegionCode">
            <summary>The CLDR/ISO 3166 region code for the country associated with this phone access. Example: "SE" for
            Sweden. Calendar backend will populate this field only for EntryPointType.PHONE.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EntryPoint.Uri">
            <summary>The URI of the entry point. The maximum length is 1300 characters. Format: - for video, http: or
            https: schema is required. - for phone, tel: schema is required. The URI should include the entire dial
            sequence (e.g., tel:+12345678900,,,123456789;1234). - for sip, sip: schema is required, e.g.,
            sip:<EMAIL>. - for more, http: or https: schema is required.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EntryPoint.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Error.Domain">
            <summary>Domain, or broad category, of the error.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Error.Reason">
            <summary>Specific reason for the error. Some of the possible values are: - "groupTooBig" - The group of
            users requested is too large for a single query. - "tooManyCalendarsRequested" - The number of calendars
            requested is too large for a single query. - "notFound" - The requested resource was not found. -
            "internalError" - The API service has encountered an internal error.  Additional error types may be added in
            the future, so clients should gracefully handle additional error statuses not included in this
            list.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Error.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.AnyoneCanAddSelf">
            <summary>Whether anyone can invite themselves to the event (currently works for Google+ events only).
            Optional. The default is False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.Attachments">
            <summary>File attachments for the event. Currently only Google Drive attachments are supported. In order to
            modify attachments the supportsAttachments request parameter should be set to true. There can be at most 25
            attachments per event,</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.Attendees">
            <summary>The attendees of the event. See the Events with attendees guide for more information on scheduling
            events with other calendar users.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.AttendeesOmitted">
            <summary>Whether attendees may have been omitted from the event's representation. When retrieving an event,
            this may be due to a restriction specified by the maxAttendee query parameter. When updating an event, this
            can be used to only update the participant's response. Optional. The default is False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.ColorId">
            <summary>The color of the event. This is an ID referring to an entry in the event section of the colors
            definition (see the  colors endpoint). Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.ConferenceData">
            <summary>The conference-related information, such as details of a Hangouts Meet conference. To create new
            conference details use the createRequest field. To persist your changes, remember to set the
            conferenceDataVersion request parameter to 1 for all event modification requests.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.CreatedRaw">
            <summary>Creation time of the event (as a RFC3339 timestamp). Read-only.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.Created">
            <summary><seealso cref="T:System.DateTime"/> representation of <see cref="P:Google.Apis.Calendar.v3.Data.Event.CreatedRaw"/>.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.Creator">
            <summary>The creator of the event. Read-only.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.Description">
            <summary>Description of the event. Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.End">
            <summary>The (exclusive) end time of the event. For a recurring event, this is the end time of the first
            instance.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.EndTimeUnspecified">
            <summary>Whether the end time is actually unspecified. An end time is still provided for compatibility
            reasons, even if this attribute is set to True. The default is False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.ETag">
            <summary>ETag of the resource.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.ExtendedProperties">
            <summary>Extended properties of the event.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.Gadget">
            <summary>A gadget that extends this event.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.GuestsCanInviteOthers">
            <summary>Whether attendees other than the organizer can invite others to the event. Optional. The default is
            True.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.GuestsCanModify">
            <summary>Whether attendees other than the organizer can modify the event. Optional. The default is
            False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.GuestsCanSeeOtherGuests">
            <summary>Whether attendees other than the organizer can see who the event's attendees are. Optional. The
            default is True.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.HangoutLink">
            <summary>An absolute link to the Google+ hangout associated with this event. Read-only.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.HtmlLink">
            <summary>An absolute link to this event in the Google Calendar Web UI. Read-only.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.ICalUID">
            <summary>Event unique identifier as defined in RFC5545. It is used to uniquely identify events accross
            calendaring systems and must be supplied when importing events via the import method. Note that the icalUID
            and the id are not identical and only one of them should be supplied at event creation time. One difference
            in their semantics is that in recurring events, all occurrences of one event have different ids while they
            all share the same icalUIDs.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.Id">
            <summary>Opaque identifier of the event. When creating new single or recurring events, you can specify their
            IDs. Provided IDs must follow these rules: - characters allowed in the ID are those used in base32hex
            encoding, i.e. lowercase letters a-v and digits 0-9, see section 3.1.2 in RFC2938 - the length of the ID
            must be between 5 and 1024 characters - the ID must be unique per calendar  Due to the globally distributed
            nature of the system, we cannot guarantee that ID collisions will be detected at event creation time. To
            minimize the risk of collisions we recommend using an established UUID algorithm such as one described in
            RFC4122. If you do not specify an ID, it will be automatically generated by the server. Note that the
            icalUID and the id are not identical and only one of them should be supplied at event creation time. One
            difference in their semantics is that in recurring events, all occurrences of one event have different ids
            while they all share the same icalUIDs.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.Kind">
            <summary>Type of the resource ("calendar#event").</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.Location">
            <summary>Geographic location of the event as free-form text. Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.Locked">
            <summary>Whether this is a locked event copy where no changes can be made to the main event fields
            "summary", "description", "location", "start", "end" or "recurrence". The default is False. Read-
            Only.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.Organizer">
            <summary>The organizer of the event. If the organizer is also an attendee, this is indicated with a separate
            entry in attendees with the organizer field set to True. To change the organizer, use the move operation.
            Read-only, except when importing an event.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.OriginalStartTime">
            <summary>For an instance of a recurring event, this is the time at which this event would start according to
            the recurrence data in the recurring event identified by recurringEventId. It uniquely identifies the
            instance within the recurring event series even if the instance was moved to a different time.
            Immutable.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.PrivateCopy">
            <summary>Whether this is a private event copy where changes are not shared with other copies on other
            calendars. Optional. Immutable. The default is False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.Recurrence">
            <summary>List of RRULE, EXRULE, RDATE and EXDATE lines for a recurring event, as specified in RFC5545. Note
            that DTSTART and DTEND lines are not allowed in this field; event start and end times are specified in the
            start and end fields. This field is omitted for single events or instances of recurring events.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.RecurringEventId">
            <summary>For an instance of a recurring event, this is the id of the recurring event to which this instance
            belongs. Immutable.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.Reminders">
            <summary>Information about the event's reminders for the authenticated user.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.Sequence">
            <summary>Sequence number as per iCalendar.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.Source">
            <summary>Source from which the event was created. For example, a web page, an email message or any document
            identifiable by an URL with HTTP or HTTPS scheme. Can only be seen or modified by the creator of the
            event.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.Start">
            <summary>The (inclusive) start time of the event. For a recurring event, this is the start time of the first
            instance.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.Status">
            <summary>Status of the event. Optional. Possible values are: - "confirmed" - The event is confirmed. This is
            the default status. - "tentative" - The event is tentatively confirmed. - "cancelled" - The event is
            cancelled (deleted). The list method returns cancelled events only on incremental sync (when syncToken or
            updatedMin are specified) or if the showDeleted flag is set to true. The get method always returns them. A
            cancelled status represents two different states depending on the event type: - Cancelled exceptions of an
            uncancelled recurring event indicate that this instance should no longer be presented to the user. Clients
            should store these events for the lifetime of the parent recurring event. Cancelled exceptions are only
            guaranteed to have values for the id, recurringEventId and originalStartTime fields populated. The other
            fields might be empty. - All other cancelled events represent deleted events. Clients should remove their
            locally synced copies. Such cancelled events will eventually disappear, so do not rely on them being
            available indefinitely. Deleted events are only guaranteed to have the id field populated.   On the
            organizer's calendar, cancelled events continue to expose event details (summary, location, etc.) so that
            they can be restored (undeleted). Similarly, the events to which the user was invited and that they manually
            removed continue to provide details. However, incremental sync requests with showDeleted set to false will
            not return these details. If an event changes its organizer (for example via the move operation) and the
            original organizer is not on the attendee list, it will leave behind a cancelled event where only the id
            field is guaranteed to be populated.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.Summary">
            <summary>Title of the event.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.Transparency">
            <summary>Whether the event blocks time on the calendar. Optional. Possible values are: - "opaque" - Default
            value. The event does block time on the calendar. This is equivalent to setting Show me as to Busy in the
            Calendar UI. - "transparent" - The event does not block time on the calendar. This is equivalent to setting
            Show me as to Available in the Calendar UI.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.UpdatedRaw">
            <summary>Last modification time of the event (as a RFC3339 timestamp). Read-only.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.Updated">
            <summary><seealso cref="T:System.DateTime"/> representation of <see cref="P:Google.Apis.Calendar.v3.Data.Event.UpdatedRaw"/>.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.Visibility">
            <summary>Visibility of the event. Optional. Possible values are: - "default" - Uses the default visibility
            for events on the calendar. This is the default value. - "public" - The event is public and event details
            are visible to all readers of the calendar. - "private" - The event is private and only event attendees may
            view event details. - "confidential" - The event is private. This value is provided for compatibility
            reasons.</summary>
        </member>
        <member name="T:Google.Apis.Calendar.v3.Data.Event.CreatorData">
            <summary>The creator of the event. Read-only.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.CreatorData.DisplayName">
            <summary>The creator's name, if available.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.CreatorData.Email">
            <summary>The creator's email address, if available.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.CreatorData.Id">
            <summary>The creator's Profile ID, if available. It corresponds to the id field in the People collection
            of the Google+ API</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.CreatorData.Self">
            <summary>Whether the creator corresponds to the calendar on which this copy of the event appears. Read-
            only. The default is False.</summary>
        </member>
        <member name="T:Google.Apis.Calendar.v3.Data.Event.ExtendedPropertiesData">
            <summary>Extended properties of the event.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.ExtendedPropertiesData.Private__">
            <summary>Properties that are private to the copy of the event that appears on this calendar.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.ExtendedPropertiesData.Shared">
            <summary>Properties that are shared between copies of the event on other attendees' calendars.</summary>
        </member>
        <member name="T:Google.Apis.Calendar.v3.Data.Event.GadgetData">
            <summary>A gadget that extends this event.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.GadgetData.Display">
            <summary>The gadget's display mode. Optional. Possible values are: - "icon" - The gadget displays next
            to the event's title in the calendar view. - "chip" - The gadget displays when the event is
            clicked.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.GadgetData.Height">
            <summary>The gadget's height in pixels. The height must be an integer greater than 0.
            Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.GadgetData.IconLink">
            <summary>The gadget's icon URL. The URL scheme must be HTTPS.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.GadgetData.Link">
            <summary>The gadget's URL. The URL scheme must be HTTPS.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.GadgetData.Preferences">
            <summary>Preferences.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.GadgetData.Title">
            <summary>The gadget's title.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.GadgetData.Type">
            <summary>The gadget's type.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.GadgetData.Width">
            <summary>The gadget's width in pixels. The width must be an integer greater than 0. Optional.</summary>
        </member>
        <member name="T:Google.Apis.Calendar.v3.Data.Event.OrganizerData">
            <summary>The organizer of the event. If the organizer is also an attendee, this is indicated with a separate
            entry in attendees with the organizer field set to True. To change the organizer, use the move operation.
            Read-only, except when importing an event.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.OrganizerData.DisplayName">
            <summary>The organizer's name, if available.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.OrganizerData.Email">
            <summary>The organizer's email address, if available. It must be a valid email address as per
            RFC5322.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.OrganizerData.Id">
            <summary>The organizer's Profile ID, if available. It corresponds to the id field in the People
            collection of the Google+ API</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.OrganizerData.Self">
            <summary>Whether the organizer corresponds to the calendar on which this copy of the event appears.
            Read-only. The default is False.</summary>
        </member>
        <member name="T:Google.Apis.Calendar.v3.Data.Event.RemindersData">
            <summary>Information about the event's reminders for the authenticated user.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.RemindersData.Overrides">
            <summary>If the event doesn't use the default reminders, this lists the reminders specific to the event,
            or, if not set, indicates that no reminders are set for this event. The maximum number of override
            reminders is 5.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.RemindersData.UseDefault">
            <summary>Whether the default reminders of the calendar apply to the event.</summary>
        </member>
        <member name="T:Google.Apis.Calendar.v3.Data.Event.SourceData">
            <summary>Source from which the event was created. For example, a web page, an email message or any document
            identifiable by an URL with HTTP or HTTPS scheme. Can only be seen or modified by the creator of the
            event.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.SourceData.Title">
            <summary>Title of the source; for example a title of a web page or an email subject.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Event.SourceData.Url">
            <summary>URL of the source pointing to a resource. The URL scheme must be HTTP or HTTPS.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EventAttachment.FileId">
            <summary>ID of the attached file. Read-only. For Google Drive files, this is the ID of the corresponding
            Files resource entry in the Drive API.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EventAttachment.FileUrl">
            <summary>URL link to the attachment. For adding Google Drive file attachments use the same format as in
            alternateLink property of the Files resource in the Drive API. Required when adding an attachment.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EventAttachment.IconLink">
            <summary>URL link to the attachment's icon. Read-only.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EventAttachment.MimeType">
            <summary>Internet media type (MIME type) of the attachment.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EventAttachment.Title">
            <summary>Attachment title.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EventAttachment.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EventAttendee.AdditionalGuests">
            <summary>Number of additional guests. Optional. The default is 0.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EventAttendee.Comment">
            <summary>The attendee's response comment. Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EventAttendee.DisplayName">
            <summary>The attendee's name, if available. Optional.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EventAttendee.Email">
            <summary>The attendee's email address, if available. This field must be present when adding an attendee. It
            must be a valid email address as per RFC5322. Required when adding an attendee.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EventAttendee.Id">
            <summary>The attendee's Profile ID, if available. It corresponds to the id field in the People collection of
            the Google+ API</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EventAttendee.Optional">
            <summary>Whether this is an optional attendee. Optional. The default is False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EventAttendee.Organizer">
            <summary>Whether the attendee is the organizer of the event. Read-only. The default is False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EventAttendee.Resource">
            <summary>Whether the attendee is a resource. Can only be set when the attendee is added to the event for the
            first time. Subsequent modifications are ignored. Optional. The default is False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EventAttendee.ResponseStatus">
            <summary>The attendee's response status. Possible values are: - "needsAction" - The attendee has not
            responded to the invitation. - "declined" - The attendee has declined the invitation. - "tentative" - The
            attendee has tentatively accepted the invitation. - "accepted" - The attendee has accepted the
            invitation.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EventAttendee.Self">
            <summary>Whether this entry represents the calendar on which this copy of the event appears. Read-only. The
            default is False.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EventAttendee.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EventDateTime.Date">
            <summary>The date, in the format "yyyy-mm-dd", if this is an all-day event.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EventDateTime.DateTimeRaw">
            <summary>The time, as a combined date-time value (formatted according to RFC3339). A time zone offset is
            required unless a time zone is explicitly specified in timeZone.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EventDateTime.DateTime">
            <summary><seealso cref="T:System.DateTime"/> representation of <see cref="P:Google.Apis.Calendar.v3.Data.EventDateTime.DateTimeRaw"/>.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EventDateTime.TimeZone">
            <summary>The time zone in which the time is specified. (Formatted as an IANA Time Zone Database name, e.g.
            "Europe/Zurich".) For recurring events this field is required and specifies the time zone in which the
            recurrence is expanded. For single events this field is optional and indicates a custom time zone for the
            event start/end.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EventDateTime.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EventReminder.Method">
            <summary>The method used by this reminder. Possible values are: - "email" - Reminders are sent via email. -
            "sms" - Deprecated. Once this feature is shutdown, the API will no longer return reminders using this
            method. Any newly added SMS reminders will be ignored. See  Google Calendar SMS notifications to be removed
            for more information. Reminders are sent via SMS. These are only available for G Suite customers. Requests
            to set SMS reminders for other account types are ignored. - "popup" - Reminders are sent via a UI popup.
            Required when adding a reminder.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EventReminder.Minutes">
            <summary>Number of minutes before the start of the event when the reminder should trigger. Valid values are
            between 0 and 40320 (4 weeks in minutes). Required when adding a reminder.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.EventReminder.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Events.AccessRole">
            <summary>The user's access role for this calendar. Read-only. Possible values are: - "none" - The user has
            no access. - "freeBusyReader" - The user has read access to free/busy information. - "reader" - The user has
            read access to the calendar. Private events will appear to users with reader access, but event details will
            be hidden. - "writer" - The user has read and write access to the calendar. Private events will appear to
            users with writer access, and event details will be visible. - "owner" - The user has ownership of the
            calendar. This role has all of the permissions of the writer role with the additional ability to see and
            manipulate ACLs.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Events.DefaultReminders">
            <summary>The default reminders on the calendar for the authenticated user. These reminders apply to all
            events on this calendar that do not explicitly override them (i.e. do not have reminders.useDefault set to
            True).</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Events.Description">
            <summary>Description of the calendar. Read-only.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Events.ETag">
            <summary>ETag of the collection.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Events.Items">
            <summary>List of events on the calendar.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Events.Kind">
            <summary>Type of the collection ("calendar#events").</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Events.NextPageToken">
            <summary>Token used to access the next page of this result. Omitted if no further results are available, in
            which case nextSyncToken is provided.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Events.NextSyncToken">
            <summary>Token used at a later point in time to retrieve only the entries that have changed since this
            result was returned. Omitted if further results are available, in which case nextPageToken is
            provided.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Events.Summary">
            <summary>Title of the calendar. Read-only.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Events.TimeZone">
            <summary>The time zone of the calendar. Read-only.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Events.UpdatedRaw">
            <summary>Last modification time of the calendar (as a RFC3339 timestamp). Read-only.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Events.Updated">
            <summary><seealso cref="T:System.DateTime"/> representation of <see cref="P:Google.Apis.Calendar.v3.Data.Events.UpdatedRaw"/>.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.FreeBusyCalendar.Busy">
            <summary>List of time ranges during which this calendar should be regarded as busy.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.FreeBusyCalendar.Errors">
            <summary>Optional error(s) (if computation for the calendar failed).</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.FreeBusyCalendar.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.FreeBusyGroup.Calendars">
            <summary>List of calendars' identifiers within a group.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.FreeBusyGroup.Errors">
            <summary>Optional error(s) (if computation for the group failed).</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.FreeBusyGroup.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.FreeBusyRequest.CalendarExpansionMax">
            <summary>Maximal number of calendars for which FreeBusy information is to be provided. Optional. Maximum
            value is 50.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.FreeBusyRequest.GroupExpansionMax">
            <summary>Maximal number of calendar identifiers to be provided for a single group. Optional. An error is
            returned for a group with more members than this value. Maximum value is 100.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.FreeBusyRequest.Items">
            <summary>List of calendars and/or groups to query.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.FreeBusyRequest.TimeMaxRaw">
            <summary>The end of the interval for the query formatted as per RFC3339.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.FreeBusyRequest.TimeMax">
            <summary><seealso cref="T:System.DateTime"/> representation of <see cref="P:Google.Apis.Calendar.v3.Data.FreeBusyRequest.TimeMaxRaw"/>.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.FreeBusyRequest.TimeMinRaw">
            <summary>The start of the interval for the query formatted as per RFC3339.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.FreeBusyRequest.TimeMin">
            <summary><seealso cref="T:System.DateTime"/> representation of <see cref="P:Google.Apis.Calendar.v3.Data.FreeBusyRequest.TimeMinRaw"/>.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.FreeBusyRequest.TimeZone">
            <summary>Time zone used in the response. Optional. The default is UTC.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.FreeBusyRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.FreeBusyRequestItem.Id">
            <summary>The identifier of a calendar or a group.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.FreeBusyRequestItem.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.FreeBusyResponse.Calendars">
            <summary>List of free/busy information for calendars.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.FreeBusyResponse.Groups">
            <summary>Expansion of groups.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.FreeBusyResponse.Kind">
            <summary>Type of the resource ("calendar#freeBusy").</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.FreeBusyResponse.TimeMaxRaw">
            <summary>The end of the interval.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.FreeBusyResponse.TimeMax">
            <summary><seealso cref="T:System.DateTime"/> representation of <see cref="P:Google.Apis.Calendar.v3.Data.FreeBusyResponse.TimeMaxRaw"/>.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.FreeBusyResponse.TimeMinRaw">
            <summary>The start of the interval.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.FreeBusyResponse.TimeMin">
            <summary><seealso cref="T:System.DateTime"/> representation of <see cref="P:Google.Apis.Calendar.v3.Data.FreeBusyResponse.TimeMinRaw"/>.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.FreeBusyResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Setting.ETag">
            <summary>ETag of the resource.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Setting.Id">
            <summary>The id of the user setting.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Setting.Kind">
            <summary>Type of the resource ("calendar#setting").</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Setting.Value">
            <summary>Value of the user setting. The format of the value depends on the ID of the setting. It must always
            be a UTF-8 string of length up to 1024 characters.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Settings.ETag">
            <summary>Etag of the collection.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Settings.Items">
            <summary>List of user settings.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Settings.Kind">
            <summary>Type of the collection ("calendar#settings").</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Settings.NextPageToken">
            <summary>Token used to access the next page of this result. Omitted if no further results are available, in
            which case nextSyncToken is provided.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.Settings.NextSyncToken">
            <summary>Token used at a later point in time to retrieve only the entries that have changed since this
            result was returned. Omitted if further results are available, in which case nextPageToken is
            provided.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.TimePeriod.EndRaw">
            <summary>The (exclusive) end of the time period.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.TimePeriod.End">
            <summary><seealso cref="T:System.DateTime"/> representation of <see cref="P:Google.Apis.Calendar.v3.Data.TimePeriod.EndRaw"/>.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.TimePeriod.StartRaw">
            <summary>The (inclusive) start of the time period.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.TimePeriod.Start">
            <summary><seealso cref="T:System.DateTime"/> representation of <see cref="P:Google.Apis.Calendar.v3.Data.TimePeriod.StartRaw"/>.</summary>
        </member>
        <member name="P:Google.Apis.Calendar.v3.Data.TimePeriod.ETag">
            <summary>The ETag of the item.</summary>
        </member>
    </members>
</doc>
