<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Google.Apis.Core</name>
    </assembly>
    <members>
        <member name="T:Google.ApplicationContext">
            <summary>Defines the context in which this library runs. It allows setting up custom loggers.</summary>
        </member>
        <member name="P:Google.ApplicationContext.Logger">
            <summary>Returns the logger used within this application context.</summary>
            <remarks>It creates a <see cref="T:Google.Apis.Logging.NullLogger"/> if no logger was registered previously</remarks>
        </member>
        <member name="M:Google.ApplicationContext.RegisterLogger(Google.Apis.Logging.ILogger)">
            <summary>Registers a logger with this application context.</summary>
            <exception cref="T:System.InvalidOperationException">Thrown if a logger was already registered.</exception>
        </member>
        <member name="T:Google.Apis.Discovery.DiscoveryVersion">
            <summary>An enumeration of all supported discovery versions.</summary>
        </member>
        <member name="F:Google.Apis.Discovery.DiscoveryVersion.Version_1_0">
            <summary>Discovery version 1.0.</summary>
        </member>
        <member name="T:Google.Apis.Discovery.Features">
            <summary>
            Specifies a list of features which can be defined within the discovery document of a service.
            </summary>
        </member>
        <member name="F:Google.Apis.Discovery.Features.LegacyDataResponse">
            <summary>
            If this feature is specified, then the data of a response is encapsulated within a "data" resource.
            </summary>
        </member>
        <member name="T:Google.Apis.Discovery.IParameter">
            <summary>Represents a parameter for a method.</summary>
        </member>
        <member name="P:Google.Apis.Discovery.IParameter.Name">
            <summary>Gets the name of the parameter.</summary>
        </member>
        <member name="P:Google.Apis.Discovery.IParameter.Pattern">
            <summary>Gets the pattern that this parameter must follow.</summary>
        </member>
        <member name="P:Google.Apis.Discovery.IParameter.IsRequired">
            <summary>Gets an indication whether this parameter is optional or required.</summary>
        </member>
        <member name="P:Google.Apis.Discovery.IParameter.DefaultValue">
            <summary>Gets the default value of this parameter.</summary>
        </member>
        <member name="P:Google.Apis.Discovery.IParameter.ParameterType">
            <summary>Gets the type of the parameter.</summary>
        </member>
        <member name="T:Google.Apis.Discovery.Parameter">
            <summary>Represents a method's parameter.</summary>
        </member>
        <member name="P:Google.Apis.Discovery.Parameter.Name">
            <inheritdoc/>
        </member>
        <member name="P:Google.Apis.Discovery.Parameter.Pattern">
            <inheritdoc/>
        </member>
        <member name="P:Google.Apis.Discovery.Parameter.IsRequired">
            <inheritdoc/>
        </member>
        <member name="P:Google.Apis.Discovery.Parameter.ParameterType">
            <inheritdoc/>
        </member>
        <member name="P:Google.Apis.Discovery.Parameter.DefaultValue">
            <inheritdoc/>
        </member>
        <member name="T:Google.Apis.Http.BackOffHandler">
            <summary>
            A thread-safe back-off handler which handles an abnormal HTTP response or an exception with 
            <see cref="T:Google.Apis.Util.IBackOff"/>.
            </summary>
        </member>
        <member name="T:Google.Apis.Http.BackOffHandler.Initializer">
            <summary>An initializer class to initialize a back-off handler.</summary>
        </member>
        <member name="P:Google.Apis.Http.BackOffHandler.Initializer.BackOff">
            <summary>Gets the back-off policy used by this back-off handler.</summary>
        </member>
        <member name="P:Google.Apis.Http.BackOffHandler.Initializer.MaxTimeSpan">
            <summary>
            Gets or sets the maximum time span to wait. If the back-off instance returns a greater time span than
            this value, this handler returns <c>false</c> to both <c>HandleExceptionAsync</c> and 
            <c>HandleResponseAsync</c>. Default value is 16 seconds per a retry request.
            </summary>
        </member>
        <member name="P:Google.Apis.Http.BackOffHandler.Initializer.HandleUnsuccessfulResponseFunc">
            <summary>
            Gets or sets a delegate function which indicates whether this back-off handler should handle an 
            abnormal HTTP response. The default is <see cref="F:Google.Apis.Http.BackOffHandler.Initializer.DefaultHandleUnsuccessfulResponseFunc"/>. 
            </summary>
        </member>
        <member name="P:Google.Apis.Http.BackOffHandler.Initializer.HandleExceptionFunc">
            <summary>
            Gets or sets a delegate function which indicates whether this back-off handler should handle an 
            exception. The default is <see cref="F:Google.Apis.Http.BackOffHandler.Initializer.DefaultHandleExceptionFunc"/>. 
            </summary>
        </member>
        <member name="F:Google.Apis.Http.BackOffHandler.Initializer.DefaultHandleUnsuccessfulResponseFunc">
            <summary>Default function which handles server errors (503).</summary>
        </member>
        <member name="F:Google.Apis.Http.BackOffHandler.Initializer.DefaultHandleExceptionFunc">
            <summary>
            Default function which handles exception which aren't 
            <see cref="T:System.Threading.Tasks.TaskCanceledException"/> or 
            <see cref="T:System.OperationCanceledException"/>. Those exceptions represent a task or an operation
            which was canceled and shouldn't be retried.
            </summary>
        </member>
        <member name="M:Google.Apis.Http.BackOffHandler.Initializer.#ctor(Google.Apis.Util.IBackOff)">
            <summary>Constructs a new initializer by the given back-off.</summary>
        </member>
        <member name="P:Google.Apis.Http.BackOffHandler.BackOff">
            <summary>Gets the back-off policy used by this back-off handler.</summary>
        </member>
        <member name="P:Google.Apis.Http.BackOffHandler.MaxTimeSpan">
            <summary>
            Gets the maximum time span to wait. If the back-off instance returns a greater time span, the handle method
            returns <c>false</c>. Default value is 16 seconds per a retry request.
            </summary>
        </member>
        <member name="P:Google.Apis.Http.BackOffHandler.HandleUnsuccessfulResponseFunc">
            <summary>
            Gets a delegate function which indicates whether this back-off handler should handle an abnormal HTTP 
            response. The default is <see cref="F:Google.Apis.Http.BackOffHandler.Initializer.DefaultHandleUnsuccessfulResponseFunc"/>. 
            </summary>
        </member>
        <member name="P:Google.Apis.Http.BackOffHandler.HandleExceptionFunc">
            <summary>
            Gets a delegate function which indicates whether this back-off handler should handle an exception. The 
            default is <see cref="F:Google.Apis.Http.BackOffHandler.Initializer.DefaultHandleExceptionFunc"/>. 
            </summary>
        </member>
        <member name="M:Google.Apis.Http.BackOffHandler.#ctor(Google.Apis.Util.IBackOff)">
            <summary>Constructs a new back-off handler with the given back-off.</summary>
            <param name="backOff">The back-off policy.</param>
        </member>
        <member name="M:Google.Apis.Http.BackOffHandler.#ctor(Google.Apis.Http.BackOffHandler.Initializer)">
            <summary>Constructs a new back-off handler with the given initializer.</summary>
        </member>
        <member name="M:Google.Apis.Http.BackOffHandler.HandleResponseAsync(Google.Apis.Http.HandleUnsuccessfulResponseArgs)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Http.BackOffHandler.HandleExceptionAsync(Google.Apis.Http.HandleExceptionArgs)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Http.BackOffHandler.HandleAsync(System.Boolean,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Handles back-off. In case the request doesn't support retry or the back-off time span is greater than the
            maximum time span allowed for a request, the handler returns <c>false</c>. Otherwise the current thread 
            will block for x milliseconds (x is defined by the <see cref="P:Google.Apis.Http.BackOffHandler.BackOff"/> instance), and this handler 
            returns <c>true</c>.
            </summary>
        </member>
        <member name="M:Google.Apis.Http.BackOffHandler.Wait(System.TimeSpan,System.Threading.CancellationToken)">
            <summary>Waits the given time span. Overriding this method is recommended for mocking purposes.</summary>
            <param name="ts">TimeSpan to wait (and block the current thread).</param>
            <param name="cancellationToken">The cancellation token in case the user wants to cancel the operation in 
            the middle.</param>
        </member>
        <member name="T:Google.Apis.Http.ConfigurableHttpClient">
            <summary>
            Configurable HTTP client inherits from <see cref="T:System.Net.Http.HttpClient"/> and contains a reference to 
            <see cref="T:Google.Apis.Http.ConfigurableMessageHandler"/>.
            </summary>
        </member>
        <member name="P:Google.Apis.Http.ConfigurableHttpClient.MessageHandler">
            <summary>Gets the configurable message handler.</summary>
        </member>
        <member name="M:Google.Apis.Http.ConfigurableHttpClient.#ctor(Google.Apis.Http.ConfigurableMessageHandler)">
            <summary>Constructs a new HTTP client.</summary>
        </member>
        <member name="T:Google.Apis.Http.ConfigurableMessageHandler">
            <summary>
            A message handler which contains the main logic of our HTTP requests. It contains a list of 
            <see cref="T:Google.Apis.Http.IHttpUnsuccessfulResponseHandler"/>s for handling abnormal responses, a list of 
            <see cref="T:Google.Apis.Http.IHttpExceptionHandler"/>s for handling exception in a request and a list of 
            <see cref="T:Google.Apis.Http.IHttpExecuteInterceptor"/>s for intercepting a request before it has been sent to the server.
            It also contains important properties like number of tries, follow redirect, etc.
            </summary>
        </member>
        <member name="F:Google.Apis.Http.ConfigurableMessageHandler.Logger">
            <summary>The class logger.</summary>
        </member>
        <member name="F:Google.Apis.Http.ConfigurableMessageHandler.MaxAllowedNumTries">
            <summary>Maximum allowed number of tries.</summary>
        </member>
        <member name="F:Google.Apis.Http.ConfigurableMessageHandler.UnsuccessfulResponseHandlerKey">
            <summary>
            Key for unsuccessful response handlers in an <see cref="T:System.Net.Http.HttpRequestMessage"/> properties.
            </summary>
        </member>
        <member name="F:Google.Apis.Http.ConfigurableMessageHandler.ExceptionHandlerKey">
            <summary>
            Key for exception handlers in an <see cref="T:System.Net.Http.HttpRequestMessage"/> properties.
            </summary>
        </member>
        <member name="F:Google.Apis.Http.ConfigurableMessageHandler.ExecuteInterceptorKey">
            <summary>
            Key for execute handlers in an <see cref="T:System.Net.Http.HttpRequestMessage"/> properties.
            </summary>
        </member>
        <member name="F:Google.Apis.Http.ConfigurableMessageHandler.ResponseStreamInterceptorProviderKey">
            <summary>
            Key for a stream response interceptor provider in an <see cref="T:System.Net.Http.HttpRequestMessage"/> properties.
            </summary>
        </member>
        <member name="F:Google.Apis.Http.ConfigurableMessageHandler.ApiVersion">
            <summary>The current API version of this client library.</summary>
        </member>
        <member name="F:Google.Apis.Http.ConfigurableMessageHandler.UserAgentSuffix">
            <summary>The User-Agent suffix header which contains the <see cref="F:Google.Apis.Http.ConfigurableMessageHandler.ApiVersion"/>.</summary>
        </member>
        <member name="F:Google.Apis.Http.ConfigurableMessageHandler.unsuccessfulResponseHandlers">
            <summary>A list of <see cref="T:Google.Apis.Http.IHttpUnsuccessfulResponseHandler"/>.</summary>
        </member>
        <member name="F:Google.Apis.Http.ConfigurableMessageHandler.exceptionHandlers">
            <summary>A list of <see cref="T:Google.Apis.Http.IHttpExceptionHandler"/>.</summary>
        </member>
        <member name="F:Google.Apis.Http.ConfigurableMessageHandler.executeInterceptors">
            <summary>A list of <see cref="T:Google.Apis.Http.IHttpExecuteInterceptor"/>.</summary>
        </member>
        <member name="P:Google.Apis.Http.ConfigurableMessageHandler.UnsuccessfulResponseHandlers">
            <summary>
            Gets a list of <see cref="T:Google.Apis.Http.IHttpUnsuccessfulResponseHandler"/>s.
            <remarks>
            Since version 1.10, <see cref="M:Google.Apis.Http.ConfigurableMessageHandler.AddUnsuccessfulResponseHandler(Google.Apis.Http.IHttpUnsuccessfulResponseHandler)"/> and
            <see cref="M:Google.Apis.Http.ConfigurableMessageHandler.RemoveUnsuccessfulResponseHandler(Google.Apis.Http.IHttpUnsuccessfulResponseHandler)"/> were added in order to keep this class thread-safe.
            More information is available on
            <a href="https://github.com/google/google-api-dotnet-client/issues/592">#592</a>.
            </remarks>
            </summary>
        </member>
        <member name="M:Google.Apis.Http.ConfigurableMessageHandler.AddUnsuccessfulResponseHandler(Google.Apis.Http.IHttpUnsuccessfulResponseHandler)">
            <summary>Adds the specified handler to the list of unsuccessful response handlers.</summary>
        </member>
        <member name="M:Google.Apis.Http.ConfigurableMessageHandler.RemoveUnsuccessfulResponseHandler(Google.Apis.Http.IHttpUnsuccessfulResponseHandler)">
            <summary>Removes the specified handler from the list of unsuccessful response handlers.</summary>
        </member>
        <member name="P:Google.Apis.Http.ConfigurableMessageHandler.ExceptionHandlers">
            <summary>
            Gets a list of <see cref="T:Google.Apis.Http.IHttpExceptionHandler"/>s.
            <remarks>
            Since version 1.10, <see cref="M:Google.Apis.Http.ConfigurableMessageHandler.AddExceptionHandler(Google.Apis.Http.IHttpExceptionHandler)"/> and <see cref="M:Google.Apis.Http.ConfigurableMessageHandler.RemoveExceptionHandler(Google.Apis.Http.IHttpExceptionHandler)"/> were added
            in order to keep this class thread-safe.  More information is available on
            <a href="https://github.com/google/google-api-dotnet-client/issues/592">#592</a>.
            </remarks>
            </summary>
        </member>
        <member name="M:Google.Apis.Http.ConfigurableMessageHandler.AddExceptionHandler(Google.Apis.Http.IHttpExceptionHandler)">
            <summary>Adds the specified handler to the list of exception handlers.</summary>
        </member>
        <member name="M:Google.Apis.Http.ConfigurableMessageHandler.RemoveExceptionHandler(Google.Apis.Http.IHttpExceptionHandler)">
            <summary>Removes the specified handler from the list of exception handlers.</summary>
        </member>
        <member name="P:Google.Apis.Http.ConfigurableMessageHandler.ExecuteInterceptors">
            <summary>
            Gets a list of <see cref="T:Google.Apis.Http.IHttpExecuteInterceptor"/>s.
            <remarks>
            Since version 1.10, <see cref="M:Google.Apis.Http.ConfigurableMessageHandler.AddExecuteInterceptor(Google.Apis.Http.IHttpExecuteInterceptor)"/> and <see cref="M:Google.Apis.Http.ConfigurableMessageHandler.RemoveExecuteInterceptor(Google.Apis.Http.IHttpExecuteInterceptor)"/> were
            added in order to keep this class thread-safe.  More information is available on
            <a href="https://github.com/google/google-api-dotnet-client/issues/592">#592</a>.
            </remarks>
            </summary>
        </member>
        <member name="M:Google.Apis.Http.ConfigurableMessageHandler.AddExecuteInterceptor(Google.Apis.Http.IHttpExecuteInterceptor)">
            <summary>Adds the specified interceptor to the list of execute interceptors.</summary>
        </member>
        <member name="M:Google.Apis.Http.ConfigurableMessageHandler.RemoveExecuteInterceptor(Google.Apis.Http.IHttpExecuteInterceptor)">
            <summary>Removes the specified interceptor from the list of execute interceptors.</summary>
        </member>
        <member name="P:Google.Apis.Http.ConfigurableMessageHandler.InstanceLogger">
            <summary>
            For testing only.
            This defaults to the static <see cref="F:Google.Apis.Http.ConfigurableMessageHandler.Logger"/>, but can be overridden for fine-grain testing. 
            </summary>
        </member>
        <member name="F:Google.Apis.Http.ConfigurableMessageHandler.numTries">
            <summary>Number of tries. Default is <c>3</c>.</summary>
        </member>
        <member name="P:Google.Apis.Http.ConfigurableMessageHandler.NumTries">
            <summary>
            Gets or sets the number of tries that will be allowed to execute. Retries occur as a result of either
            <see cref="T:Google.Apis.Http.IHttpUnsuccessfulResponseHandler"/> or <see cref="T:Google.Apis.Http.IHttpExceptionHandler"/> which handles the
            abnormal HTTP response or exception before being terminated. 
            Set <c>1</c> for not retrying requests. The default value is <c>3</c>.
            <remarks>
            The number of allowed redirects (3xx) is defined by <see cref="P:Google.Apis.Http.ConfigurableMessageHandler.NumRedirects"/>. This property defines
            only the allowed tries for >=400 responses, or when an exception is thrown. For example if you set 
            <see cref="P:Google.Apis.Http.ConfigurableMessageHandler.NumTries"/> to 1 and <see cref="P:Google.Apis.Http.ConfigurableMessageHandler.NumRedirects"/> to 5, the library will send up to five redirect
            requests, but will not send any retry requests due to an error HTTP status code.
            </remarks>
            </summary>
        </member>
        <member name="F:Google.Apis.Http.ConfigurableMessageHandler.numRedirects">
            <summary>Number of redirects allowed. Default is <c>10</c>.</summary>
        </member>
        <member name="P:Google.Apis.Http.ConfigurableMessageHandler.NumRedirects">
            <summary>
            Gets or sets the number of redirects that will be allowed to execute. The default value is <c>10</c>.
            See <see cref="P:Google.Apis.Http.ConfigurableMessageHandler.NumTries"/> for more information.
            </summary>
        </member>
        <member name="P:Google.Apis.Http.ConfigurableMessageHandler.FollowRedirect">
            <summary>
            Gets or sets whether the handler should follow a redirect when a redirect response is received. Default 
            value is <c>true</c>.
            </summary>
        </member>
        <member name="P:Google.Apis.Http.ConfigurableMessageHandler.IsLoggingEnabled">
            <summary>Gets or sets whether logging is enabled. Default value is <c>true</c>.</summary>
        </member>
        <member name="T:Google.Apis.Http.ConfigurableMessageHandler.LogEventType">
            <summary>
            Specifies the type(s) of request/response events to log.
            </summary>
        </member>
        <member name="F:Google.Apis.Http.ConfigurableMessageHandler.LogEventType.None">
            <summary>
            Log no request/response information.
            </summary>
        </member>
        <member name="F:Google.Apis.Http.ConfigurableMessageHandler.LogEventType.RequestUri">
            <summary>
            Log the request URI.
            </summary>
        </member>
        <member name="F:Google.Apis.Http.ConfigurableMessageHandler.LogEventType.RequestHeaders">
            <summary>
            Log the request headers.
            </summary>
        </member>
        <member name="F:Google.Apis.Http.ConfigurableMessageHandler.LogEventType.RequestBody">
            <summary>
            Log the request body. The body is assumed to be ASCII, and non-printable charaters are replaced by '.'.
            Warning: This causes the body content to be buffered in memory, so use with care for large requests.
            </summary>
        </member>
        <member name="F:Google.Apis.Http.ConfigurableMessageHandler.LogEventType.ResponseStatus">
            <summary>
            Log the response status.
            </summary>
        </member>
        <member name="F:Google.Apis.Http.ConfigurableMessageHandler.LogEventType.ResponseHeaders">
            <summary>
            Log the response headers.
            </summary>
        </member>
        <member name="F:Google.Apis.Http.ConfigurableMessageHandler.LogEventType.ResponseBody">
            <summary>
            Log the response body. The body is assumed to be ASCII, and non-printable characters are replaced by '.'.
            Warning: This causes the body content to be buffered in memory, so use with care for large responses.
            </summary>
        </member>
        <member name="F:Google.Apis.Http.ConfigurableMessageHandler.LogEventType.ResponseAbnormal">
            <summary>
            Log abnormal response messages.
            </summary>
        </member>
        <member name="P:Google.Apis.Http.ConfigurableMessageHandler.LogEvents">
            <summary>
            The request/response types to log.
            </summary>
        </member>
        <member name="P:Google.Apis.Http.ConfigurableMessageHandler.ApplicationName">
            <summary>Gets or sets the application name which will be used on the User-Agent header.</summary>
        </member>
        <member name="M:Google.Apis.Http.ConfigurableMessageHandler.#ctor(System.Net.Http.HttpMessageHandler)">
            <summary>Constructs a new configurable message handler.</summary>
        </member>
        <member name="M:Google.Apis.Http.ConfigurableMessageHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
            <summary>
            The main logic of sending a request to the server. This send method adds the User-Agent header to a request
            with <see cref="P:Google.Apis.Http.ConfigurableMessageHandler.ApplicationName"/> and the library version. It also calls interceptors before each attempt,
            and unsuccessful response handler or exception handlers when abnormal response or exception occurred.
            </summary>
        </member>
        <member name="M:Google.Apis.Http.ConfigurableMessageHandler.HandleRedirect(System.Net.Http.HttpResponseMessage)">
            <summary>
            Handles redirect if the response's status code is redirect, redirects are turned on, and the header has
            a location.
            When the status code is <c>303</c> the method on the request is changed to a GET as per the RFC2616 
            specification. On a redirect, it also removes the <c>Authorization</c> and all <c>If-*</c> request headers.
            </summary>
            <returns> Whether this method changed the request and handled redirect successfully. </returns>
        </member>
        <member name="T:Google.Apis.Http.StreamInterceptor">
            <summary>
            A delegate used to intercept stream data without modifying it.
            The parameters should always be validated before the delegate is called,
            so the delegate itself does not need to validate them again.
            </summary>
            <param name="buffer">The buffer containing the data.</param>
            <param name="offset">The offset into the buffer.</param>
            <param name="count">The number of bytes being read/written.</param>
        </member>
        <member name="T:Google.Apis.Http.ExponentialBackOffPolicy">
            <summary>
            Indicates if exponential back-off is used automatically on exceptions in a service requests and \ or when 503 
            responses is returned form the server.
            </summary>
        </member>
        <member name="F:Google.Apis.Http.ExponentialBackOffPolicy.None">
            <summary>Exponential back-off is disabled.</summary>
        </member>
        <member name="F:Google.Apis.Http.ExponentialBackOffPolicy.Exception">
            <summary>Exponential back-off is enabled only for exceptions.</summary>
        </member>
        <member name="F:Google.Apis.Http.ExponentialBackOffPolicy.UnsuccessfulResponse503">
            <summary>Exponential back-off is enabled only for 503 HTTP Status code.</summary>
        </member>
        <member name="T:Google.Apis.Http.ExponentialBackOffInitializer">
            <summary>
            An initializer which adds exponential back-off as exception handler and \ or unsuccessful response handler by
            the given <see cref="T:Google.Apis.Http.ExponentialBackOffPolicy"/>.
            </summary>
        </member>
        <member name="P:Google.Apis.Http.ExponentialBackOffInitializer.Policy">
            <summary>Gets or sets the used back-off policy.</summary>
        </member>
        <member name="P:Google.Apis.Http.ExponentialBackOffInitializer.CreateBackOff">
            <summary>Gets or sets the back-off handler creation function.</summary>
        </member>
        <member name="M:Google.Apis.Http.ExponentialBackOffInitializer.#ctor(Google.Apis.Http.ExponentialBackOffPolicy,System.Func{Google.Apis.Http.BackOffHandler})">
            <summary>
            Constructs a new back-off initializer with the given policy and back-off handler create function.
            </summary>
        </member>
        <member name="M:Google.Apis.Http.ExponentialBackOffInitializer.Initialize(Google.Apis.Http.ConfigurableHttpClient)">
            <inheritdoc/>
        </member>
        <member name="T:Google.Apis.Http.HttpClientFactory">
            <summary>The default implementation of the HTTP client factory.</summary>
        </member>
        <member name="F:Google.Apis.Http.HttpClientFactory.Logger">
            <summary>The class logger.</summary>
        </member>
        <member name="M:Google.Apis.Http.HttpClientFactory.CreateHttpClient(Google.Apis.Http.CreateHttpClientArgs)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Http.HttpClientFactory.CreateHandler(Google.Apis.Http.CreateHttpClientArgs)">
            <summary>Creates a HTTP message handler. Override this method to mock a message handler.</summary>
        </member>
        <member name="M:Google.Apis.Http.HttpClientFactory.CreateSimpleClientHandler">
            <summary>
            Creates a simple client handler with redirection and compression disabled.
            </summary>
        </member>
        <member name="T:Google.Apis.Http.HttpConsts">
            <summary>HTTP constants.</summary>
        </member>
        <member name="F:Google.Apis.Http.HttpConsts.Get">
            <summary>Http GET request</summary>
        </member>
        <member name="F:Google.Apis.Http.HttpConsts.Delete">
            <summary>Http DELETE request</summary>
        </member>
        <member name="F:Google.Apis.Http.HttpConsts.Put">
            <summary>Http PUT request</summary>
        </member>
        <member name="F:Google.Apis.Http.HttpConsts.Post">
            <summary>Http POST request</summary>
        </member>
        <member name="F:Google.Apis.Http.HttpConsts.Patch">
            <summary>Http PATCH request</summary>
        </member>
        <member name="T:Google.Apis.Http.HttpExtenstions">
            <summary>
            Extension methods to <see cref="T:System.Net.Http.HttpRequestMessage"/> and 
            <see cref="T:System.Net.Http.HttpResponseMessage"/>.
            </summary>
        </member>
        <member name="M:Google.Apis.Http.HttpExtenstions.IsRedirectStatusCode(System.Net.Http.HttpResponseMessage)">
            <summary>Returns <c>true</c> if the response contains one of the redirect status codes.</summary>
        </member>
        <member name="M:Google.Apis.Http.HttpExtenstions.SetEmptyContent(System.Net.Http.HttpRequestMessage)">
            <summary>A Google.Apis utility method for setting an empty HTTP content.</summary>
        </member>
        <member name="T:Google.Apis.Http.IConfigurableHttpClientInitializer">
            <summary> 
            HTTP client initializer for changing the default behavior of HTTP client. 
            Use this initializer to change default values like timeout and number of tries. 
            You can also set different handlers and interceptors like <see cref="T:Google.Apis.Http.IHttpUnsuccessfulResponseHandler"/>s, 
            <see cref="T:Google.Apis.Http.IHttpExceptionHandler"/>s and <see cref="T:Google.Apis.Http.IHttpExecuteInterceptor"/>s.
            </summary>
        </member>
        <member name="M:Google.Apis.Http.IConfigurableHttpClientInitializer.Initialize(Google.Apis.Http.ConfigurableHttpClient)">
            <summary>Initializes a HTTP client after it was created.</summary>
        </member>
        <member name="T:Google.Apis.Http.CreateHttpClientArgs">
            <summary>Arguments for creating a HTTP client.</summary>
        </member>
        <member name="P:Google.Apis.Http.CreateHttpClientArgs.GZipEnabled">
            <summary>Gets or sets whether GZip is enabled.</summary>
        </member>
        <member name="P:Google.Apis.Http.CreateHttpClientArgs.ApplicationName">
            <summary>Gets or sets the application name that is sent in the User-Agent header.</summary>
        </member>
        <member name="P:Google.Apis.Http.CreateHttpClientArgs.Initializers">
            <summary>Gets a list of initializers to initialize the HTTP client instance.</summary>
        </member>
        <member name="M:Google.Apis.Http.CreateHttpClientArgs.#ctor">
            <summary>Constructs a new argument instance.</summary>
        </member>
        <member name="T:Google.Apis.Http.IHttpClientFactory">
            <summary>
            HTTP client factory creates configurable HTTP clients. A unique HTTP client should be created for each service.
            </summary>
        </member>
        <member name="M:Google.Apis.Http.IHttpClientFactory.CreateHttpClient(Google.Apis.Http.CreateHttpClientArgs)">
            <summary>Creates a new configurable HTTP client.</summary>
        </member>
        <member name="T:Google.Apis.Http.HandleExceptionArgs">
            <summary>Argument class to <see cref="M:Google.Apis.Http.IHttpExceptionHandler.HandleExceptionAsync(Google.Apis.Http.HandleExceptionArgs)"/>.</summary>
        </member>
        <member name="P:Google.Apis.Http.HandleExceptionArgs.Request">
            <summary>Gets or sets the sent request.</summary>
        </member>
        <member name="P:Google.Apis.Http.HandleExceptionArgs.Exception">
            <summary>Gets or sets the exception which occurred during sending the request.</summary>
        </member>
        <member name="P:Google.Apis.Http.HandleExceptionArgs.TotalTries">
            <summary>Gets or sets the total number of tries to send the request.</summary>
        </member>
        <member name="P:Google.Apis.Http.HandleExceptionArgs.CurrentFailedTry">
            <summary>Gets or sets the current failed try.</summary>
        </member>
        <member name="P:Google.Apis.Http.HandleExceptionArgs.SupportsRetry">
            <summary>Gets an indication whether a retry will occur if the handler returns <c>true</c>.</summary>
        </member>
        <member name="P:Google.Apis.Http.HandleExceptionArgs.CancellationToken">
            <summary>Gets or sets the request's cancellation token.</summary>
        </member>
        <member name="T:Google.Apis.Http.IHttpExceptionHandler">
            <summary>Exception handler is invoked when an exception is thrown during a HTTP request.</summary>
        </member>
        <member name="M:Google.Apis.Http.IHttpExceptionHandler.HandleExceptionAsync(Google.Apis.Http.HandleExceptionArgs)">
            <summary>
            Handles an exception thrown when sending a HTTP request. 
            A simple rule must be followed, if you modify the request object in a way that the exception can be 
            resolved, you must return <c>true</c>.
            </summary>
            <param name="args">
            Handle exception argument which properties such as the request, exception, current failed try.
            </param>
            <returns>Whether this handler has made a change that requires the request to be resent.</returns>
        </member>
        <member name="T:Google.Apis.Http.IHttpExecuteInterceptor">
            <summary>
            HTTP request execute interceptor to intercept a <see cref="T:System.Net.Http.HttpRequestMessage"/> before it has
            been sent. Sample usage is attaching "Authorization" header to a request.
            </summary>
        </member>
        <member name="M:Google.Apis.Http.IHttpExecuteInterceptor.InterceptAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
            <summary>
            <summary>Invoked before the request is being sent.</summary>
            </summary>
            <param name="request">The HTTP request message.</param>
            <param name="cancellationToken">Cancellation token to cancel the operation.</param>
        </member>
        <member name="T:Google.Apis.Http.HandleUnsuccessfulResponseArgs">
            <summary>Argument class to <see cref="M:Google.Apis.Http.IHttpUnsuccessfulResponseHandler.HandleResponseAsync(Google.Apis.Http.HandleUnsuccessfulResponseArgs)"/>.</summary>
        </member>
        <member name="P:Google.Apis.Http.HandleUnsuccessfulResponseArgs.Request">
            <summary>Gets or sets the sent request.</summary>
        </member>
        <member name="P:Google.Apis.Http.HandleUnsuccessfulResponseArgs.Response">
            <summary>Gets or sets the abnormal response.</summary>
        </member>
        <member name="P:Google.Apis.Http.HandleUnsuccessfulResponseArgs.TotalTries">
            <summary>Gets or sets the total number of tries to send the request.</summary>
        </member>
        <member name="P:Google.Apis.Http.HandleUnsuccessfulResponseArgs.CurrentFailedTry">
            <summary>Gets or sets the current failed try.</summary>
        </member>
        <member name="P:Google.Apis.Http.HandleUnsuccessfulResponseArgs.SupportsRetry">
            <summary>Gets an indication whether a retry will occur if the handler returns <c>true</c>.</summary>
        </member>
        <member name="P:Google.Apis.Http.HandleUnsuccessfulResponseArgs.CancellationToken">
            <summary>Gets or sets the request's cancellation token.</summary>
        </member>
        <member name="T:Google.Apis.Http.IHttpUnsuccessfulResponseHandler">
            <summary>
            Unsuccessful response handler which is invoked when an abnormal HTTP response is returned when sending a HTTP
            request.
            </summary>
        </member>
        <member name="M:Google.Apis.Http.IHttpUnsuccessfulResponseHandler.HandleResponseAsync(Google.Apis.Http.HandleUnsuccessfulResponseArgs)">
            <summary>
            Handles an abnormal response when sending a HTTP request. 
            A simple rule must be followed, if you modify the request object in a way that the abnormal response can 
            be resolved, you must return <c>true</c>.
            </summary>
            <param name="args">
            Handle response argument which contains properties such as the request, response, current failed try.
            </param>
            <returns>Whether this handler has made a change that requires the request to be resent.</returns>
        </member>
        <member name="T:Google.Apis.Http.MaxUrlLengthInterceptor">
            <summary>
            Intercepts HTTP GET requests with a URLs longer than a specified maximum number of characters.
            The interceptor will change such requests as follows:
            <list type="bullet">
            <item>The request's method will be changed to POST</item>
            <item>A <c>X-HTTP-Method-Override</c> header will be added with the value <c>GET</c></item>
            <item>Any query parameters from the URI will be moved into the body of the request.</item>
            <item>If query parameters are moved, the content type is set to <c>application/x-www-form-urlencoded</c></item>
            </list>
            </summary>
        </member>
        <member name="M:Google.Apis.Http.MaxUrlLengthInterceptor.#ctor(System.UInt32)">
            <summary>Constructs a new Max URL length interceptor with the given max length.</summary>
        </member>
        <member name="M:Google.Apis.Http.MaxUrlLengthInterceptor.InterceptAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="T:Google.Apis.Http.StreamInterceptionHandler">
            <summary>
            An HttpMessageHandler that (conditionally) intercepts response streams, allowing inline
            stream processing. An interceptor provider function is fetched from each request via the
            <see cref="F:Google.Apis.Http.ConfigurableMessageHandler.ResponseStreamInterceptorProviderKey"/> property;
            if the property is not present on the request (or is null), the response will definitely not be
            intercepted. If the property is present and non-null, the interceptor provider is called for
            the response. This may return a null reference, indicating that interception isn't required, and
            the response can be returned as-is. Otherwise, we use a <see cref="T:Google.Apis.Http.StreamReplacingHttpContent"/>
            with an intercepting stream which passes all data read to the interceptor.
            </summary>
        </member>
        <member name="M:Google.Apis.Http.StreamInterceptionHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
            <summary>
            For each request, check whether we 
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="T:Google.Apis.Http.TwoWayDelegatingHandler">
            <summary>
            An HttpMessageHandler that delegates to one of two inner handlers based on a condition
            checked on each request.
            </summary>
        </member>
        <member name="T:Google.Apis.Http.TwoWayDelegatingHandler.AccessibleDelegatingHandler">
            <summary>
            Handler to wrap another, just so that we can effectively expose its SendAsync method.
            </summary>
        </member>
        <member name="T:Google.Apis.Http.GzipDeflateHandler">
            <summary>
            An HttpMessageHandler that performs decompression for Deflate and Gzip content.
            </summary>
        </member>
        <member name="T:Google.Apis.Http.StreamReplacingHttpContent">
            <summary>
            An HttpContent based on an existing one, but allowing the stream to be replaced.
            This is similar to StreamContent, but it defers the stream creation until it's requested by the client.
            (An alternative would be to use StreamContent with a custom stream that only retrieved the stream when
            first used.) Headers are copied from the original content.
            </summary>
        </member>
        <member name="T:Google.Apis.ISerializer">
            <summary>Serialization interface that supports serialize and deserialize methods.</summary>
        </member>
        <member name="P:Google.Apis.ISerializer.Format">
            <summary>Gets the application format this serializer supports (e.g. "json", "xml", etc.).</summary>
        </member>
        <member name="M:Google.Apis.ISerializer.Serialize(System.Object,System.IO.Stream)">
            <summary>Serializes the specified object into a Stream.</summary>
        </member>
        <member name="M:Google.Apis.ISerializer.Serialize(System.Object)">
            <summary>Serializes the specified object into a string.</summary>
        </member>
        <member name="M:Google.Apis.ISerializer.Deserialize``1(System.String)">
            <summary>Deserializes the string into an object.</summary>
        </member>
        <member name="M:Google.Apis.ISerializer.Deserialize(System.String,System.Type)">
            <summary>Deserializes the string into an object.</summary>
        </member>
        <member name="M:Google.Apis.ISerializer.Deserialize``1(System.IO.Stream)">
            <summary>Deserializes the stream into an object.</summary>
        </member>
        <member name="T:Google.Apis.Json.IJsonSerializer">
            <summary>Represents a JSON serializer.</summary>
        </member>
        <member name="T:Google.Apis.Json.JsonExplicitNull">
            <summary>
            Provides values which are explicitly expressed as <c>null</c> when converted to JSON.
            </summary>
        </member>
        <member name="M:Google.Apis.Json.JsonExplicitNull.ForIList``1">
            <summary>
            Get an <see cref="T:System.Collections.Generic.IList`1"/> that is explicitly expressed as <c>null</c> when converted to JSON.
            </summary>
            <returns>An <see cref="T:System.Collections.Generic.IList`1"/> that is explicitly expressed as <c>null</c> when converted to JSON.</returns>
        </member>
        <member name="T:Google.Apis.Json.JsonExplicitNullAttribute">
            <summary>
            All values of a type with this attribute are represented as a literal <c>null</c> in JSON.
            </summary>
        </member>
        <member name="T:Google.Apis.Json.RFC3339DateTimeConverter">
            <summary>
            A JSON converter which honers RFC 3339 and the serialized date is accepted by Google services.
            </summary>
        </member>
        <member name="P:Google.Apis.Json.RFC3339DateTimeConverter.CanRead">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Json.RFC3339DateTimeConverter.ReadJson(Newtonsoft.Json.JsonReader,System.Type,System.Object,Newtonsoft.Json.JsonSerializer)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Json.RFC3339DateTimeConverter.CanConvert(System.Type)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Json.RFC3339DateTimeConverter.WriteJson(Newtonsoft.Json.JsonWriter,System.Object,Newtonsoft.Json.JsonSerializer)">
            <inheritdoc/>
        </member>
        <member name="T:Google.Apis.Json.ExplicitNullConverter">
            <summary>
            A JSON converter to write <c>null</c> literals into JSON when explicitly requested.
            </summary>
        </member>
        <member name="P:Google.Apis.Json.ExplicitNullConverter.CanRead">
            <inheritdoc />
        </member>
        <member name="M:Google.Apis.Json.ExplicitNullConverter.CanConvert(System.Type)">
            <inheritdoc />
        </member>
        <member name="M:Google.Apis.Json.ExplicitNullConverter.ReadJson(Newtonsoft.Json.JsonReader,System.Type,System.Object,Newtonsoft.Json.JsonSerializer)">
            <inheritdoc />
        </member>
        <member name="M:Google.Apis.Json.ExplicitNullConverter.WriteJson(Newtonsoft.Json.JsonWriter,System.Object,Newtonsoft.Json.JsonSerializer)">
            <inheritdoc />
        </member>
        <member name="T:Google.Apis.Json.NewtonsoftJsonContractResolver">
            <summary>
            A JSON contract resolver to apply <see cref="T:Google.Apis.Json.RFC3339DateTimeConverter"/> and <see cref="F:Google.Apis.Json.NewtonsoftJsonContractResolver.ExplicitNullConverter"/> as necessary.
            </summary>
            <remarks>
            Using a contract resolver is recommended in the Json.NET performance tips: https://www.newtonsoft.com/json/help/html/Performance.htm#JsonConverters
            </remarks>
        </member>
        <member name="M:Google.Apis.Json.NewtonsoftJsonContractResolver.CreateContract(System.Type)">
            <inheritdoc />
        </member>
        <member name="T:Google.Apis.Json.NewtonsoftJsonSerializer">
            <summary>Class for serialization and deserialization of JSON documents using the Newtonsoft Library.</summary>
        </member>
        <member name="P:Google.Apis.Json.NewtonsoftJsonSerializer.Instance">
            <summary>The default instance of the Newtonsoft JSON Serializer, with default settings.</summary>
        </member>
        <member name="M:Google.Apis.Json.NewtonsoftJsonSerializer.#ctor">
            <summary>
            Constructs a new instance with the default serialization settings, equivalent to <see cref="P:Google.Apis.Json.NewtonsoftJsonSerializer.Instance"/>.
            </summary>
        </member>
        <member name="M:Google.Apis.Json.NewtonsoftJsonSerializer.#ctor(Newtonsoft.Json.JsonSerializerSettings)">
            <summary>
            Constructs a new instance with the given settings.
            </summary>
            <param name="settings">The settings to apply when serializing and deserializing. Must not be null.</param>
        </member>
        <member name="M:Google.Apis.Json.NewtonsoftJsonSerializer.CreateDefaultSettings">
            <summary>
            Creates a new instance of <see cref="T:Newtonsoft.Json.JsonSerializerSettings"/> with the same behavior
            as the ones used in <see cref="P:Google.Apis.Json.NewtonsoftJsonSerializer.Instance"/>. This method is expected to be used to construct
            settings which are then passed to <see cref="M:Google.Apis.Json.NewtonsoftJsonSerializer.#ctor(Newtonsoft.Json.JsonSerializerSettings)"/>.
            </summary>
            <returns>A new set of default settings.</returns>
        </member>
        <member name="P:Google.Apis.Json.NewtonsoftJsonSerializer.Format">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Json.NewtonsoftJsonSerializer.Serialize(System.Object,System.IO.Stream)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Json.NewtonsoftJsonSerializer.Serialize(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Json.NewtonsoftJsonSerializer.Deserialize``1(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Json.NewtonsoftJsonSerializer.Deserialize(System.String,System.Type)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Json.NewtonsoftJsonSerializer.Deserialize``1(System.IO.Stream)">
            <inheritdoc/>
        </member>
        <member name="T:Google.Apis.Logging.BaseLogger">
            <summary>
            An abstract base logger, upon which real loggers may be built.
            </summary>
        </member>
        <member name="M:Google.Apis.Logging.BaseLogger.#ctor(Google.Apis.Logging.LogLevel,Google.Apis.Util.IClock,System.Type)">
            <summary>
            Construct a <see cref="T:Google.Apis.Logging.BaseLogger"/>. 
            </summary>
            <param name="minimumLogLevel">Logging will be enabled at this level and all higher levels.</param>
            <param name="clock">The <see cref="T:Google.Apis.Util.IClock"/> to use to timestamp log entries.</param>
            <param name="forType">The type from which entries are being logged. May be <c>null</c>.</param>
        </member>
        <member name="P:Google.Apis.Logging.BaseLogger.Clock">
            <summary>
            The <see cref="T:Google.Apis.Util.IClock"/> being used to timestamp log entries. 
            </summary>
        </member>
        <member name="P:Google.Apis.Logging.BaseLogger.LoggerForType">
            <summary>
            The type from which entries are being logged. May be <c>null</c>.
            </summary>
        </member>
        <member name="P:Google.Apis.Logging.BaseLogger.MinimumLogLevel">
            <summary>
            Logging is enabled at this level and all higher levels.
            </summary>
        </member>
        <member name="P:Google.Apis.Logging.BaseLogger.IsDebugEnabled">
            <summary>
            Is Debug level logging enabled?
            </summary>
        </member>
        <member name="P:Google.Apis.Logging.BaseLogger.IsInfoEnabled">
            <summary>
            Is info level logging enabled?
            </summary>
        </member>
        <member name="P:Google.Apis.Logging.BaseLogger.IsWarningEnabled">
            <summary>
            Is warning level logging enabled?
            </summary>
        </member>
        <member name="P:Google.Apis.Logging.BaseLogger.IsErrorEnabled">
            <summary>
            Is error level logging enabled?
            </summary>
        </member>
        <member name="M:Google.Apis.Logging.BaseLogger.BuildNewLogger(System.Type)">
            <summary>
            Build a new logger of the derived concrete type, for use to log from the specified type.
            </summary>
            <param name="type">The type from which entries are being logged.</param>
            <returns>A new <see cref="T:Google.Apis.Logging.ILogger"/> instance, logging from the specified type.</returns>
        </member>
        <member name="M:Google.Apis.Logging.BaseLogger.ForType``1">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Logging.BaseLogger.ForType(System.Type)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Logging.BaseLogger.Log(Google.Apis.Logging.LogLevel,System.String)">
            <summary>
            Perform the actual logging.
            </summary>
            <param name="logLevel">The <see cref="T:Google.Apis.Logging.LogLevel"/> of this log entry.</param>
            <param name="formattedMessage">The fully formatted log message, ready for logging.</param>
        </member>
        <member name="M:Google.Apis.Logging.BaseLogger.Debug(System.String,System.Object[])">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Logging.BaseLogger.Info(System.String,System.Object[])">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Logging.BaseLogger.Warning(System.String,System.Object[])">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Logging.BaseLogger.Error(System.Exception,System.String,System.Object[])">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Logging.BaseLogger.Error(System.String,System.Object[])">
            <inheritdoc/>
        </member>
        <member name="T:Google.Apis.Logging.ConsoleLogger">
            <summary>
            A logger than logs to StdError or StdOut.
            </summary>
        </member>
        <member name="M:Google.Apis.Logging.ConsoleLogger.#ctor(Google.Apis.Logging.LogLevel,System.Boolean,Google.Apis.Util.IClock)">
            <summary>
            Construct a <see cref="T:Google.Apis.Logging.ConsoleLogger"/>. 
            </summary>
            <param name="minimumLogLevel">Logging will be enabled at this level and all higher levels.</param>
            <param name="logToStdOut"><c>true</c> to log to StdOut, defaults to logging to StdError.</param>
            <param name="clock">Optional <see cref="T:Google.Apis.Util.IClock"/>; will use the system clock if <c>null</c>.</param>
        </member>
        <member name="P:Google.Apis.Logging.ConsoleLogger.LogToStdOut">
            <summary>
            <c>false</c> to log to StdError; <c>true</c> to log to StdOut.
            </summary>
        </member>
        <member name="M:Google.Apis.Logging.ConsoleLogger.BuildNewLogger(System.Type)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Logging.ConsoleLogger.Log(Google.Apis.Logging.LogLevel,System.String)">
            <inheritdoc/>
        </member>
        <member name="T:Google.Apis.Logging.ILogger">
            <summary>Describes a logging interface which is used for outputting messages.</summary>
        </member>
        <member name="P:Google.Apis.Logging.ILogger.IsDebugEnabled">
            <summary>Gets an indication whether debug output is logged or not.</summary>
        </member>
        <member name="M:Google.Apis.Logging.ILogger.ForType(System.Type)">
            <summary>Returns a logger which will be associated with the specified type.</summary>
            <param name="type">Type to which this logger belongs.</param>
            <returns>A type-associated logger.</returns>
        </member>
        <member name="M:Google.Apis.Logging.ILogger.ForType``1">
            <summary>Returns a logger which will be associated with the specified type.</summary>
            <returns>A type-associated logger.</returns>
        </member>
        <member name="M:Google.Apis.Logging.ILogger.Debug(System.String,System.Object[])">
            <summary>Logs a debug message.</summary>
            <param name="message">The message to log.</param>
            <param name="formatArgs">String.Format arguments (if applicable).</param>
        </member>
        <member name="M:Google.Apis.Logging.ILogger.Info(System.String,System.Object[])">
            <summary>Logs an info message.</summary>
            <param name="message">The message to log.</param>
            <param name="formatArgs">String.Format arguments (if applicable).</param>
        </member>
        <member name="M:Google.Apis.Logging.ILogger.Warning(System.String,System.Object[])">
            <summary>Logs a warning.</summary>
            <param name="message">The message to log.</param>
            <param name="formatArgs">String.Format arguments (if applicable).</param>
        </member>
        <member name="M:Google.Apis.Logging.ILogger.Error(System.Exception,System.String,System.Object[])">
            <summary>Logs an error message resulting from an exception.</summary>
            <param name="exception"></param>
            <param name="message">The message to log.</param>
            <param name="formatArgs">String.Format arguments (if applicable).</param>
        </member>
        <member name="M:Google.Apis.Logging.ILogger.Error(System.String,System.Object[])">
            <summary>Logs an error message.</summary>
            <param name="message">The message to log.</param>
            <param name="formatArgs">String.Format arguments (if applicable).</param>
        </member>
        <member name="T:Google.Apis.Logging.LogLevel">
            <summary>
            The supported logging levels.
            </summary>
        </member>
        <member name="F:Google.Apis.Logging.LogLevel.All">
            <summary>
            A value lower than all logging levels.
            </summary>
        </member>
        <member name="F:Google.Apis.Logging.LogLevel.Debug">
            <summary>
            Debug logging.
            </summary>
        </member>
        <member name="F:Google.Apis.Logging.LogLevel.Info">
            <summary>
            Info logging.
            </summary>
        </member>
        <member name="F:Google.Apis.Logging.LogLevel.Warning">
            <summary>
            Warning logging.
            </summary>
        </member>
        <member name="F:Google.Apis.Logging.LogLevel.Error">
            <summary>
            Error logging.
            </summary>
        </member>
        <member name="F:Google.Apis.Logging.LogLevel.None">
            <summary>
            A value higher than all logging levels.
            </summary>
        </member>
        <member name="T:Google.Apis.Logging.MemoryLogger">
            <summary>
            A logger than logs to an in-memory buffer.
            Generally for use during tests.
            </summary>
        </member>
        <member name="M:Google.Apis.Logging.MemoryLogger.#ctor(Google.Apis.Logging.LogLevel,System.Int32,Google.Apis.Util.IClock)">
            <summary>
            Construct a <see cref="T:Google.Apis.Logging.MemoryLogger"/>. 
            </summary>
            <param name="minimumLogLevel">Logging will be enabled at this level and all higher levels.</param>
            <param name="maximumEntryCount">The maximum number of log entries. Further log entries will be silently discarded.</param>
            <param name="clock">Optional <see cref="T:Google.Apis.Util.IClock"/>; will use the system clock if <c>null</c>.</param>
        </member>
        <member name="P:Google.Apis.Logging.MemoryLogger.LogEntries">
            <summary>
            The list of log entries.
            </summary>
        </member>
        <member name="M:Google.Apis.Logging.MemoryLogger.BuildNewLogger(System.Type)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Logging.MemoryLogger.Log(Google.Apis.Logging.LogLevel,System.String)">
            <inheritdoc/>
        </member>
        <member name="T:Google.Apis.Logging.NullLogger">
            <summary>
            Represents a NullLogger which does not do any logging.
            </summary>
        </member>
        <member name="P:Google.Apis.Logging.NullLogger.IsDebugEnabled">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Logging.NullLogger.ForType(System.Type)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Logging.NullLogger.ForType``1">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Logging.NullLogger.Info(System.String,System.Object[])">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Logging.NullLogger.Warning(System.String,System.Object[])">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Logging.NullLogger.Debug(System.String,System.Object[])">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Logging.NullLogger.Error(System.Exception,System.String,System.Object[])">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Logging.NullLogger.Error(System.String,System.Object[])">
            <inheritdoc/>
        </member>
        <member name="T:Google.Apis.Requests.Parameters.ParameterCollection">
            <summary>A collection of parameters (key value pairs). May contain duplicate keys.</summary>
        </member>
        <member name="M:Google.Apis.Requests.Parameters.ParameterCollection.#ctor">
            <summary>Constructs a new parameter collection.</summary>
        </member>
        <member name="M:Google.Apis.Requests.Parameters.ParameterCollection.#ctor(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
            <summary>Constructs a new parameter collection from the given collection.</summary>
        </member>
        <member name="M:Google.Apis.Requests.Parameters.ParameterCollection.Add(System.String,System.String)">
            <summary>Adds a single parameter to this collection.</summary>
        </member>
        <member name="M:Google.Apis.Requests.Parameters.ParameterCollection.ContainsKey(System.String)">
            <summary>Returns <c>true</c> if this parameter is set within the collection.</summary>
        </member>
        <member name="M:Google.Apis.Requests.Parameters.ParameterCollection.TryGetValue(System.String,System.String@)">
            <summary>
            Tries to find the a key within the specified key value collection. Returns true if the key was found.
            If a pair was found the out parameter value will contain the value of that pair.
            </summary>
        </member>
        <member name="M:Google.Apis.Requests.Parameters.ParameterCollection.GetFirstMatch(System.String)">
            <summary>
            Returns the value of the first matching key, or throws a KeyNotFoundException if the parameter is not
            present within the collection.
            </summary>
        </member>
        <member name="M:Google.Apis.Requests.Parameters.ParameterCollection.GetAllMatches(System.String)">
            <summary>
            Returns all matches for the specified key. May return an empty enumeration if the key is not present.
            </summary>
        </member>
        <member name="P:Google.Apis.Requests.Parameters.ParameterCollection.Item(System.String)">
            <summary>
            Returns all matches for the specified key. May return an empty enumeration if the key is not present.
            </summary>
        </member>
        <member name="M:Google.Apis.Requests.Parameters.ParameterCollection.FromQueryString(System.String)">
            <summary>
            Creates a parameter collection from the specified URL encoded query string.
            Example: 
                The query string "foo=bar&amp;chocolate=cookie" would result in two parameters (foo and bar)
                with the values "bar" and "cookie" set.
            </summary>
        </member>
        <member name="M:Google.Apis.Requests.Parameters.ParameterCollection.FromDictionary(System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Creates a parameter collection from the specified dictionary.
            If the value is an enumerable, a parameter pair will be added for each value.
            Otherwise the value will be converted into a string using the .ToString() method.
            </summary>
        </member>
        <member name="T:Google.Apis.Requests.Parameters.ParameterUtils">
            <summary>
            Utility class for iterating on <see cref="T:Google.Apis.Util.RequestParameterAttribute"/> properties in a request object.
            </summary>
        </member>
        <member name="M:Google.Apis.Requests.Parameters.ParameterUtils.CreateFormUrlEncodedContent(System.Object)">
            <summary>
            Creates a <see cref="T:System.Net.Http.FormUrlEncodedContent"/> with all the specified parameters in 
            the input request. It uses reflection to iterate over all properties with
            <see cref="T:Google.Apis.Util.RequestParameterAttribute"/> attribute.
            </summary>
            <param name="request">
            A request object which contains properties with 
            <see cref="T:Google.Apis.Util.RequestParameterAttribute"/> attribute. Those properties will be serialized
            to the returned <see cref="T:System.Net.Http.FormUrlEncodedContent"/>.
            </param>
            <returns>
            A <see cref="T:System.Net.Http.FormUrlEncodedContent"/> which contains the all the given object required 
            values.
            </returns>
        </member>
        <member name="M:Google.Apis.Requests.Parameters.ParameterUtils.CreateParameterDictionary(System.Object)">
            <summary>
            Creates a parameter dictionary by using reflection to iterate over all properties with
            <see cref="T:Google.Apis.Util.RequestParameterAttribute"/> attribute.
            </summary>
            <param name="request">
            A request object which contains properties with 
            <see cref="T:Google.Apis.Util.RequestParameterAttribute"/> attribute. Those properties will be set
            in the output dictionary.
            </param>
        </member>
        <member name="M:Google.Apis.Requests.Parameters.ParameterUtils.InitParameters(Google.Apis.Requests.RequestBuilder,System.Object)">
            <summary>
            Sets query parameters in the given builder with all all properties with the
            <see cref="T:Google.Apis.Util.RequestParameterAttribute"/> attribute.
            </summary>
            <param name="builder">The request builder</param>
            <param name="request">
            A request object which contains properties with 
            <see cref="T:Google.Apis.Util.RequestParameterAttribute"/> attribute. Those properties will be set in the 
            given request builder object
            </param>
        </member>
        <member name="M:Google.Apis.Requests.Parameters.ParameterUtils.IterateParameters(System.Object,System.Action{Google.Apis.Util.RequestParameterType,System.String,System.Object})">
            <summary>
            Iterates over all <see cref="T:Google.Apis.Util.RequestParameterAttribute"/> properties in the request
            object and invokes the specified action for each of them.
            </summary>
            <param name="request">A request object</param>
            <param name="action">An action to invoke which gets the parameter type, name and its value</param>
        </member>
        <member name="T:Google.Apis.Requests.Parameters.ParameterValidator">
            <summary>Logic for validating a parameter.</summary>
        </member>
        <member name="M:Google.Apis.Requests.Parameters.ParameterValidator.ValidateRegex(Google.Apis.Discovery.IParameter,System.String)">
            <summary>Validates a parameter value against the methods regex.</summary>
        </member>
        <member name="M:Google.Apis.Requests.Parameters.ParameterValidator.ValidateParameter(Google.Apis.Discovery.IParameter,System.String)">
            <summary>Validates if a parameter is valid.</summary>
        </member>
        <member name="T:Google.Apis.Requests.RequestBuilder">
            <summary>Utility class for building a URI using <see cref="M:Google.Apis.Requests.RequestBuilder.BuildUri"/> or a HTTP request using 
            <see cref="M:Google.Apis.Requests.RequestBuilder.CreateRequest"/> from the query and path parameters of a REST call.</summary>
        </member>
        <member name="F:Google.Apis.Requests.RequestBuilder.PathParametersPattern">
            <summary>Pattern to get the groups that are part of the path.</summary>
        </member>
        <member name="F:Google.Apis.Requests.RequestBuilder.SupportedMethods">
            <summary>Supported HTTP methods.</summary> 
        </member>
        <member name="P:Google.Apis.Requests.RequestBuilder.PathParameters">
            <summary>
            A dictionary containing the parameters which will be inserted into the path of the URI. These parameters 
            will be substituted into the URI path where the path contains "{key}". See 
            http://tools.ietf.org/html/rfc6570 for more information.
            </summary>
        </member>
        <member name="P:Google.Apis.Requests.RequestBuilder.QueryParameters">
            <summary>
            A dictionary containing the parameters which will apply to the query portion of this request.
            </summary>
        </member>
        <member name="P:Google.Apis.Requests.RequestBuilder.BaseUri">
            <summary>The base URI for this request (usually applies to the service itself).</summary>
        </member>
        <member name="P:Google.Apis.Requests.RequestBuilder.Path">
            <summary>
            The path portion of this request. It's appended to the <see cref="P:Google.Apis.Requests.RequestBuilder.BaseUri"/> and the parameters are 
            substituted from the <see cref="P:Google.Apis.Requests.RequestBuilder.PathParameters"/> dictionary.
            </summary>
        </member>
        <member name="F:Google.Apis.Requests.RequestBuilder.method">
            <summary>The HTTP method used for this request.</summary> 
        </member>
        <member name="P:Google.Apis.Requests.RequestBuilder.Method">
            <summary>The HTTP method used for this request (such as GET, PUT, POST, etc...).</summary>
            <remarks>The default Value is <see cref="F:Google.Apis.Http.HttpConsts.Get"/>.</remarks>
        </member>
        <member name="M:Google.Apis.Requests.RequestBuilder.#ctor">
            <summary>Construct a new request builder.</summary> 
            TODO(peleyal): Consider using the Factory pattern here.
        </member>
        <member name="M:Google.Apis.Requests.RequestBuilder.BuildUri">
            <summary>Constructs a Uri as defined by the parts of this request builder.</summary> 
        </member>
        <member name="F:Google.Apis.Requests.RequestBuilder.OPERATORS">
            <summary>Operator list that can appear in the path argument.</summary>
        </member>
        <member name="M:Google.Apis.Requests.RequestBuilder.BuildRestPath">
            <summary>
            Builds the REST path string builder based on <see cref="P:Google.Apis.Requests.RequestBuilder.PathParameters"/> and the URI template spec
            http://tools.ietf.org/html/rfc6570.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Google.Apis.Requests.RequestBuilder.AddParameter(Google.Apis.Util.RequestParameterType,System.String,System.String)">
            <summary>Adds a parameter value.</summary> 
            <param name="type">Type of the parameter (must be 'Path' or 'Query').</param>
            <param name="name">Parameter name.</param>
            <param name="value">Parameter value.</param>
        </member>
        <member name="M:Google.Apis.Requests.RequestBuilder.CreateRequest">
            <summary>Creates a new HTTP request message.</summary> 
        </member>
        <member name="T:Google.Apis.Requests.RequestError">
            <summary>
            Collection of server errors
            </summary>
        </member>
        <member name="T:Google.Apis.Requests.RequestError.ErrorCodes">
            <summary>
            Enumeration of known error codes which may occur during a request.
            </summary>
        </member>
        <member name="F:Google.Apis.Requests.RequestError.ErrorCodes.ETagConditionFailed">
            <summary>
            The ETag condition specified caused the ETag verification to fail. 
            Depending on the ETagAction of the request this either means that a change to the object has been
            made on the server, or that the object in question is still the same and has not been changed.
            </summary>
        </member>
        <member name="P:Google.Apis.Requests.RequestError.Errors">
            <summary>
            Contains a list of all errors
            </summary>
        </member>
        <member name="P:Google.Apis.Requests.RequestError.Code">
            <summary>
            The error code returned
            </summary>
        </member>
        <member name="P:Google.Apis.Requests.RequestError.Message">
            <summary>
            The error message returned
            </summary>
        </member>
        <member name="M:Google.Apis.Requests.RequestError.ToString">
            <summary>
            Returns a string summary of this error
            </summary>
            <returns>A string summary of this error</returns>
        </member>
        <member name="T:Google.Apis.Requests.SingleError">
            <summary>
            A single server error
            </summary>
        </member>
        <member name="P:Google.Apis.Requests.SingleError.Domain">
            <summary>
            The domain in which the error occured
            </summary>
        </member>
        <member name="P:Google.Apis.Requests.SingleError.Reason">
            <summary>
            The reason the error was thrown
            </summary>
        </member>
        <member name="P:Google.Apis.Requests.SingleError.Message">
            <summary>
            The error message
            </summary>
        </member>
        <member name="P:Google.Apis.Requests.SingleError.LocationType">
            <summary>
            Type of the location
            </summary>
        </member>
        <member name="P:Google.Apis.Requests.SingleError.Location">
            <summary>
            Location where the error was thrown
            </summary>
        </member>
        <member name="M:Google.Apis.Requests.SingleError.ToString">
            <summary>
            Returns a string summary of this error
            </summary>
            <returns>A string summary of this error</returns>
        </member>
        <member name="T:Google.Apis.Testing.VisibleForTestOnly">
            <summary>
            Marker Attribute to indicate a Method/Class/Property has been made more visible for purpose of testing.
            Mark the member as internal and make the testing assembly a friend using
            <code>[assembly: InternalsVisibleTo("Full.Name.Of.Testing.Assembly")]</code>
            </summary>
        </member>
        <member name="T:Google.Apis.Util.ExponentialBackOff">
            <summary>
            Implementation of <see cref="T:Google.Apis.Util.IBackOff"/> that increases the back-off period for each retry attempt using a 
            randomization function that grows exponentially. In addition, it also adds a randomize number of milliseconds 
            for each attempt.
            </summary>
        </member>
        <member name="F:Google.Apis.Util.ExponentialBackOff.MaxAllowedNumRetries">
            <summary>The maximum allowed number of retries.</summary>
        </member>
        <member name="P:Google.Apis.Util.ExponentialBackOff.DeltaBackOff">
            <summary>
            Gets the delta time span used to generate a random milliseconds to add to the next back-off.
            If the value is <see cref="F:System.TimeSpan.Zero"/> then the generated back-off will be exactly 1, 2, 4,
            8, 16, etc. seconds. A valid value is between zero and one second. The default value is 250ms, which means
            that the generated back-off will be [0.75-1.25]sec, [1.75-2.25]sec, [3.75-4.25]sec, and so on.
            </summary>
        </member>
        <member name="P:Google.Apis.Util.ExponentialBackOff.MaxNumOfRetries">
            <summary>Gets the maximum number of retries. Default value is <c>10</c>.</summary>
        </member>
        <member name="F:Google.Apis.Util.ExponentialBackOff.random">
            <summary>The random instance which generates a random number to add the to next back-off.</summary>
        </member>
        <member name="M:Google.Apis.Util.ExponentialBackOff.#ctor">
            <summary>Constructs a new exponential back-off with default values.</summary>
        </member>
        <member name="M:Google.Apis.Util.ExponentialBackOff.#ctor(System.TimeSpan,System.Int32)">
            <summary>Constructs a new exponential back-off with the given delta and maximum retries.</summary>
        </member>
        <member name="M:Google.Apis.Util.ExponentialBackOff.GetNextBackOff(System.Int32)">
            <inheritdoc/>
        </member>
        <member name="T:Google.Apis.Util.IBackOff">
            <summary>Strategy interface to control back-off between retry attempts.</summary>
        </member>
        <member name="M:Google.Apis.Util.IBackOff.GetNextBackOff(System.Int32)">
            <summary>
            Gets the a time span to wait before next retry. If the current retry reached the maximum number of retries,
            the returned value is <see cref="F:System.TimeSpan.MinValue"/>.
            </summary>
        </member>
        <member name="P:Google.Apis.Util.IBackOff.MaxNumOfRetries">
            <summary>Gets the maximum number of retries.</summary>
        </member>
        <member name="T:Google.Apis.Util.IClock">
            <summary>Clock wrapper for getting the current time.</summary>
        </member>
        <member name="P:Google.Apis.Util.IClock.Now">
            <summary>
            Gets a <see cref="T:System.DateTime"/> object that is set to the current date and time on this computer, 
            expressed as the local time.
            </summary>
        </member>
        <member name="P:Google.Apis.Util.IClock.UtcNow">
            <summary>
            Gets a <see cref="T:System.DateTime"/> object that is set to the current date and time on this computer, 
            expressed as UTC time.
            </summary>
        </member>
        <member name="T:Google.Apis.Util.SystemClock">
            <summary>
            A default clock implementation that wraps the <see cref="P:System.DateTime.UtcNow"/>
            and <see cref="P:System.DateTime.Now"/> properties.
            </summary>
        </member>
        <member name="M:Google.Apis.Util.SystemClock.#ctor">
            <summary>Constructs a new system clock.</summary>
        </member>
        <member name="F:Google.Apis.Util.SystemClock.Default">
            <summary>The default instance.</summary>
        </member>
        <member name="P:Google.Apis.Util.SystemClock.Now">
            <inheritdoc/>
        </member>
        <member name="P:Google.Apis.Util.SystemClock.UtcNow">
            <inheritdoc/>
        </member>
        <member name="T:Google.Apis.Util.Repeatable`1">
            <summary>
            Repeatable class which allows you to both pass a single element, as well as an array, as a parameter value.
            </summary>
        </member>
        <member name="M:Google.Apis.Util.Repeatable`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
            <summary>Creates a repeatable value.</summary>
        </member>
        <member name="M:Google.Apis.Util.Repeatable`1.GetEnumerator">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Util.Repeatable`1.op_Implicit(`0)~Google.Apis.Util.Repeatable{`0}">
            <summary>Converts the single element into a repeatable.</summary>
        </member>
        <member name="M:Google.Apis.Util.Repeatable`1.op_Implicit(`0[])~Google.Apis.Util.Repeatable{`0}">
            <summary>Converts a number of elements into a repeatable.</summary>
        </member>
        <member name="M:Google.Apis.Util.Repeatable`1.op_Implicit(System.Collections.Generic.List{`0})~Google.Apis.Util.Repeatable{`0}">
            <summary>Converts a number of elements into a repeatable.</summary>
        </member>
        <member name="T:Google.Apis.Util.RequestParameterAttribute">
            <summary>
            An attribute which is used to specially mark a property for reflective purposes, 
            assign a name to the property and indicate it's location in the request as either
            in the path or query portion of the request URL.
            </summary>
        </member>
        <member name="P:Google.Apis.Util.RequestParameterAttribute.Name">
            <summary>Gets the name of the parameter.</summary>
        </member>
        <member name="P:Google.Apis.Util.RequestParameterAttribute.Type">
            <summary>Gets the type of the parameter, Path or Query.</summary>
        </member>
        <member name="M:Google.Apis.Util.RequestParameterAttribute.#ctor(System.String)">
            <summary>
            Constructs a new property attribute to be a part of a REST URI. 
            This constructor uses <see cref="F:Google.Apis.Util.RequestParameterType.Query"/> as the parameter's type.
            </summary>
            <param name="name">
            The name of the parameter. If the parameter is a path parameter this name will be used to substitute the 
            string value into the path, replacing {name}. If the parameter is a query parameter, this parameter will be
            added to the query string, in the format "name=value".
            </param>
        </member>
        <member name="M:Google.Apis.Util.RequestParameterAttribute.#ctor(System.String,Google.Apis.Util.RequestParameterType)">
            <summary>Constructs a new property attribute to be a part of a REST URI.</summary>
            <param name="name">
            The name of the parameter. If the parameter is a path parameter this name will be used to substitute the 
            string value into the path, replacing {name}. If the parameter is a query parameter, this parameter will be
            added to the query string, in the format "name=value".
            </param>
            <param name="type">The type of the parameter, either Path, Query or UserDefinedQueries.</param>
        </member>
        <member name="T:Google.Apis.Util.RequestParameterType">
            <summary>Describe the type of this parameter (Path, Query or UserDefinedQueries).</summary>
        </member>
        <member name="F:Google.Apis.Util.RequestParameterType.Path">
            <summary>A path parameter which is inserted into the path portion of the request URI.</summary>
        </member>
        <member name="F:Google.Apis.Util.RequestParameterType.Query">
            <summary>A query parameter which is inserted into the query portion of the request URI.</summary>
        </member>
        <member name="F:Google.Apis.Util.RequestParameterType.UserDefinedQueries">
            <summary>
            A group of user-defined parameters that will be added in to the query portion of the request URI. If this
            type is being used, the name of the RequestParameterAttirbute is meaningless.
            </summary>
        </member>
        <member name="T:Google.Apis.Util.StandardResponse`1">
            <summary>
            Calls to Google Api return StandardResponses as Json with
            two properties Data, being the return type of the method called
            and Error, being any errors that occure.
            </summary>
        </member>
        <member name="P:Google.Apis.Util.StandardResponse`1.Data">
            <summary>May be null if call failed.</summary>
        </member>
        <member name="P:Google.Apis.Util.StandardResponse`1.Error">
            <summary>May be null if call succedded.</summary>
        </member>
        <member name="T:Google.Apis.Util.Store.IDataStore">
            <summary>
            Stores and manages data objects, where the key is a string and the value is an object.
            <para>
            <c>null</c> keys are not allowed.
            </para>
            </summary>
        </member>
        <member name="M:Google.Apis.Util.Store.IDataStore.StoreAsync``1(System.String,``0)">
            <summary>Asynchronously stores the given value for the given key (replacing any existing value).</summary>
            <typeparam name="T">The type to store in the data store.</typeparam>
            <param name="key">The key.</param>
            <param name="value">The value to store.</param>
        </member>
        <member name="M:Google.Apis.Util.Store.IDataStore.DeleteAsync``1(System.String)">
            <summary>
            Asynchronously deletes the given key. The type is provided here as well because the "real" saved key should
            contain type information as well, so the data store will be able to store the same key for different types.
            </summary>
            <typeparam name="T">The type to delete from the data store.</typeparam>
            <param name="key">The key to delete.</param>
        </member>
        <member name="M:Google.Apis.Util.Store.IDataStore.GetAsync``1(System.String)">
            <summary>Asynchronously returns the stored value for the given key or <c>null</c> if not found.</summary>
            <typeparam name="T">The type to retrieve from the data store.</typeparam>
            <param name="key">The key to retrieve its value.</param>
            <returns>The stored object.</returns>
        </member>
        <member name="M:Google.Apis.Util.Store.IDataStore.ClearAsync">
            <summary>Asynchronously clears all values in the data store.</summary>
        </member>
        <member name="T:Google.Apis.Util.StringValueAttribute">
            <summary>Defines an attribute containing a string representation of the member.</summary>
        </member>
        <member name="P:Google.Apis.Util.StringValueAttribute.Text">
            <summary>The text which belongs to this member.</summary>
        </member>
        <member name="M:Google.Apis.Util.StringValueAttribute.#ctor(System.String)">
            <summary>Creates a new string value attribute with the specified text.</summary>
        </member>
        <member name="T:Google.Apis.Util.UriPatcher">
             <summary>
             Workarounds for some unfortunate behaviors in the .NET Framework's
             implementation of System.Uri
             </summary>
             <remarks>
             UriPatcher lets us work around some unfortunate behaviors in the .NET Framework's
             implementation of System.Uri.
            
             == Problem 1: Slashes and dots
            
             Prior to .NET 4.5, System.Uri would always unescape "%2f" ("/") and "%5c" ("\\").
             Relative path components were also compressed.
            
             As a result, this:     "http://www.example.com/.%2f.%5c./"
             ... turned into this:  "http://www.example.com/"
            
             This breaks API requests where slashes or dots appear in path parameters. Such requests
             arise, for example, when these characters appear in the name of a GCS object.
            
             == Problem 2: Fewer unreserved characters
            
             Unless IDN/IRI parsing is enabled -- which it is not, by default, prior to .NET 4.5 --
             Uri.EscapeDataString uses the set of "unreserved" characters from RFC 2396 instead of the
             newer, *smaller* list from RFC 3986. We build requests using URI templating as described
             by RFC 6570, which specifies that the latter definition (RFC 3986) should be used.
            
             This breaks API requests with parameters including any of: !*'()
            
             == Solutions
            
             Though the default behaviors changed in .NET 4.5, these "quirks" remain for compatibility
             unless the application explicitly targets the new runtime.  Usually, that means adding a
             TargetFrameworkAttribute to the entry assembly.
            
             Applications running on .NET 4.0 or later can also set "DontUnescapePathDotsAndSlashes"
             and enable IDN/IRI parsing using app.config or web.config.
            
             As a class library, we can't control app.config or the entry assembly, so we can't take
             either approach. Instead, we resort to reflection trickery to try to solve these problems
             if we detect they exist. Sorry.
             </remarks>
        </member>
        <member name="M:Google.Apis.Util.UriPatcher.PatchUriQuirks">
            <summary>
            Patch URI quirks in System.Uri. See class summary for details.
            </summary>
        </member>
        <member name="T:Google.Apis.Util.Utilities">
            <summary>A utility class which contains helper methods and extension methods.</summary>
        </member>
        <member name="M:Google.Apis.Util.Utilities.GetLibraryVersion">
            <summary>Returns the version of the core library.</summary>
        </member>
        <member name="M:Google.Apis.Util.Utilities.ThrowIfNull``1(``0,System.String)">
            <summary>
            A Google.Apis utility method for throwing an <see cref="T:System.ArgumentNullException"/> if the object is
            <c>null</c>.
            </summary>
        </member>
        <member name="M:Google.Apis.Util.Utilities.ThrowIfNullOrEmpty(System.String,System.String)">
            <summary>
            A Google.Apis utility method for throwing an <see cref="T:System.ArgumentNullException"/> if the string is
            <c>null</c> or empty.
            </summary>
            <returns>The original string.</returns>
        </member>
        <member name="M:Google.Apis.Util.Utilities.IsNullOrEmpty``1(System.Collections.Generic.IEnumerable{``0})">
            <summary>Returns <c>true</c> in case the enumerable is <c>null</c> or empty.</summary>
        </member>
        <member name="M:Google.Apis.Util.Utilities.GetCustomAttribute``1(System.Reflection.MemberInfo)">
            <summary>
            A Google.Apis utility method for returning the first matching custom attribute (or <c>null</c>) of the specified member.
            </summary>
        </member>
        <member name="M:Google.Apis.Util.Utilities.GetStringValue(System.Enum)">
            <summary>Returns the defined string value of an Enum.</summary>
        </member>
        <member name="M:Google.Apis.Util.Utilities.GetEnumStringValue(System.Enum)">
            <summary>
            Returns the defined string value of an Enum. Use for test purposes or in other Google.Apis projects.
            </summary>
        </member>
        <member name="M:Google.Apis.Util.Utilities.ConvertToString(System.Object)">
            <summary>
            Tries to convert the specified object to a string. Uses custom type converters if available.
            Returns null for a null object.
            </summary>
        </member>
        <member name="M:Google.Apis.Util.Utilities.ConvertToRFC3339(System.DateTime)">
            <summary>Converts the input date into a RFC3339 string (http://www.ietf.org/rfc/rfc3339.txt).</summary>
        </member>
        <member name="M:Google.Apis.Util.Utilities.GetDateTimeFromString(System.String)">
            <summary>
            Parses the input string and returns <see cref="T:System.DateTime"/> if the input is a valid 
            representation of a date. Otherwise it returns <c>null</c>.
            </summary>
        </member>
        <member name="M:Google.Apis.Util.Utilities.GetStringFromDateTime(System.Nullable{System.DateTime})">
            <summary>Returns a string (by RFC3339) form the input <see cref="T:System.DateTime"/> instance.</summary>
        </member>
        <member name="T:Google.GoogleApiException">
            <summary>Represents an exception thrown by an API Service.</summary>
        </member>
        <member name="P:Google.GoogleApiException.ServiceName">
            <summary>Gets the service name which related to this exception.</summary>
        </member>
        <member name="M:Google.GoogleApiException.#ctor(System.String,System.String,System.Exception)">
            <summary>Creates an API Service exception.</summary>
        </member>
        <member name="M:Google.GoogleApiException.#ctor(System.String,System.String)">
            <summary>Creates an API Service exception.</summary>
        </member>
        <member name="P:Google.GoogleApiException.Error">
            <summary>The Error which was returned from the server, or <c>null</c> if unavailable.</summary>
        </member>
        <member name="P:Google.GoogleApiException.HttpStatusCode">
            <summary>The HTTP status code which was returned along with this error, or 0 if unavailable.</summary>
        </member>
        <member name="M:Google.GoogleApiException.ToString">
            <summary>
            Returns a summary of this exception.
            </summary>
            <returns>A summary of this exception.</returns>
        </member>
    </members>
</doc>
