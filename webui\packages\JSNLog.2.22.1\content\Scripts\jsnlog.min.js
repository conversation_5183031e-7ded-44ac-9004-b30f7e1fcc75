/* 
 * JSNLog 2.22.1
 * Open source under the MIT License.
 * Copyright 2016 Mattijs Perdeck All rights reserved.
 */
var __extends=this&&this.__extends||function(c,d){function k(){this.constructor=c}for(var e in d)d.hasOwnProperty(e)&&(c[e]=d[e]);c.prototype=null===d?Object.create(d):(k.prototype=d.prototype,new k)};
function JL(c){if(!c)return JL.__;Array.prototype.reduce||(Array.prototype.reduce=function(c,d){for(var g=d,h=0;h<this.length;h++)g=c(g,this[h],h,this);return g});var d="";return("."+c).split(".").reduce(function(c,e,g,h){d=d?d+("."+e):e;e=c["__"+d];void 0===e&&(JL.Logger.prototype=c,e=new J<PERSON>.<PERSON>(d),c["__"+d]=e);return e},JL.__)}
(function(c){function d(b,a,c){void 0!==a[b]&&(null===a[b]?delete c[b]:c[b]=a[b])}function k(b){if(null!=c.enabled&&!c.enabled||null!=c.maxMessages&&1>c.maxMessages)return!1;try{if(b.userAgentRegex&&!(new RegExp(b.userAgentRegex)).test(navigator.userAgent))return!1}catch(a){}try{if(b.ipRegex&&c.clientIP&&!(new RegExp(b.ipRegex)).test(c.clientIP))return!1}catch(a){}return!0}function e(b,a){try{if(b.disallow&&(new RegExp(b.disallow)).test(a))return!1}catch(c){}return!0}function g(b){return"function"==
typeof b?b instanceof RegExp?b.toString():b():b}function h(b){b=g(b);var a;switch(typeof b){case "string":return new l(b,null,b);case "number":return a=b.toString(),new l(a,null,a);case "boolean":return a=b.toString(),new l(a,null,a);case "undefined":return new l("undefined",null,"undefined");case "object":if(b instanceof RegExp||b instanceof String||b instanceof Number||b instanceof Boolean)return a=b.toString(),new l(a,null,a);a="function"===typeof c.serialize?c.serialize.call(this,b):JSON.stringify(b);
return new l(null,b,a);default:return new l("unknown",null,"unknown")}}c.requestId="";var l=function(){return function(b,a,c){this.msg=b;this.meta=a;this.finalString=c}}();c.setOptions=function(b){d("enabled",b,this);d("maxMessages",b,this);d("defaultAjaxUrl",b,this);d("clientIP",b,this);d("requestId",b,this);d("defaultBeforeSend",b,this);d("serialize",b,this);return this};c.getAllLevel=function(){return-2147483648};c.getTraceLevel=function(){return 1E3};c.getDebugLevel=function(){return 2E3};c.getInfoLevel=
function(){return 3E3};c.getWarnLevel=function(){return 4E3};c.getErrorLevel=function(){return 5E3};c.getFatalLevel=function(){return 6E3};c.getOffLevel=function(){return 2147483647};var f=function(){return function(b,a){this.inner=a;this.name="JL.Exception";this.message=h(b).finalString}}();c.Exception=f;f.prototype=Error();var q=function(){return function(b,a,c,d){this.l=b;this.m=a;this.n=c;this.t=d}}();c.LogItem=q;f=function(){function b(a,b){this.appenderName=a;this.sendLogItems=b;this.level=
c.getTraceLevel();this.sendWithBufferLevel=2147483647;this.storeInBufferLevel=-2147483648;this.bufferSize=0;this.batchSize=1;this.buffer=[];this.batchBuffer=[]}b.prototype.setOptions=function(a){d("level",a,this);d("ipRegex",a,this);d("userAgentRegex",a,this);d("disallow",a,this);d("sendWithBufferLevel",a,this);d("storeInBufferLevel",a,this);d("bufferSize",a,this);d("batchSize",a,this);this.bufferSize<this.buffer.length&&(this.buffer.length=this.bufferSize);return this};b.prototype.log=function(a,
b,c,d,m,f,g){!k(this)||!e(this,f)||m<this.storeInBufferLevel||(a=new q(m,f,g,(new Date).getTime()),m<this.level?0<this.bufferSize&&(this.buffer.push(a),this.buffer.length>this.bufferSize&&this.buffer.shift()):(m<this.sendWithBufferLevel||!this.buffer.length||(this.batchBuffer=this.batchBuffer.concat(this.buffer),this.buffer.length=0),this.batchBuffer.push(a),this.batchBuffer.length>=this.batchSize&&this.sendBatch()))};b.prototype.sendBatch=function(){0==this.batchBuffer.length||null!=c.maxMessages&&
1>c.maxMessages||(null!=c.maxMessages&&(c.maxMessages-=this.batchBuffer.length),this.sendLogItems(this.batchBuffer),this.batchBuffer.length=0)};return b}();c.Appender=f;var n=function(b){function a(c){b.call(this,c,a.prototype.sendLogItemsAjax)}__extends(a,b);a.prototype.setOptions=function(a){d("url",a,this);d("beforeSend",a,this);b.prototype.setOptions.call(this,a);return this};a.prototype.sendLogItemsAjax=function(a){try{var b="/jsnlog.logger";null!=c.defaultAjaxUrl&&(b=c.defaultAjaxUrl);this.url&&
(b=this.url);var d=this.getXhr(b),e={r:c.requestId,lg:a};"function"===typeof this.beforeSend?this.beforeSend.call(this,d,e):"function"===typeof c.defaultBeforeSend&&c.defaultBeforeSend.call(this,d,e);var f=JSON.stringify(e);d.send(f)}catch(g){}};a.prototype.getXhr=function(a){var b=new XMLHttpRequest;if(!("withCredentials"in b)&&"undefined"!=typeof XDomainRequest)return b=new XDomainRequest,b.open("POST",a),b;b.open("POST",a);b.setRequestHeader("Content-Type","application/json");b.setRequestHeader("JSNLog-RequestId",
c.requestId);return b};return a}(f);c.AjaxAppender=n;var p=function(b){function a(c){b.call(this,c,a.prototype.sendLogItemsConsole)}__extends(a,b);a.prototype.clog=function(a){console.log(a)};a.prototype.cerror=function(a){console.error?console.error(a):this.clog(a)};a.prototype.cwarn=function(a){console.warn?console.warn(a):this.clog(a)};a.prototype.cinfo=function(a){console.info?console.info(a):this.clog(a)};a.prototype.cdebug=function(a){console.debug?console.debug(a):this.cinfo(a)};a.prototype.sendLogItemsConsole=
function(a){try{if(console){var b;for(b=0;b<a.length;++b){var d=a[b],e=d.n+": "+d.m;"undefined"===typeof window&&(e=new Date(d.t)+" | "+e);d.l<=c.getDebugLevel()?this.cdebug(e):d.l<=c.getInfoLevel()?this.cinfo(e):d.l<=c.getWarnLevel()?this.cwarn(e):this.cerror(e)}}}catch(f){}};return a}(f);c.ConsoleAppender=p;f=function(){function b(a){this.loggerName=a;this.seenRegexes=[]}b.prototype.setOptions=function(a){d("level",a,this);d("userAgentRegex",a,this);d("disallow",a,this);d("ipRegex",a,this);d("appenders",
a,this);d("onceOnly",a,this);this.seenRegexes=[];return this};b.prototype.buildExceptionObject=function(a){var b={};a.stack?b.stack=a.stack:b.e=a;a.message&&(b.message=a.message);a.name&&(b.name=a.name);a.data&&(b.data=a.data);a.inner&&(b.inner=this.buildExceptionObject(a.inner));return b};b.prototype.log=function(a,b,c){var d=0;if(!this.appenders)return this;if(a>=this.level&&k(this)&&(c?(d=this.buildExceptionObject(c),d.logData=g(b)):d=b,b=h(d),e(this,b.finalString))){if(this.onceOnly)for(d=this.onceOnly.length-
1;0<=d;){if((new RegExp(this.onceOnly[d])).test(b.finalString)){if(this.seenRegexes[d])return this;this.seenRegexes[d]=!0}d--}b.meta=b.meta||{};b.meta.loggerName=this.loggerName;for(d=this.appenders.length-1;0<=d;)this.appenders[d].log(1E3>=a?"trace":2E3>=a?"debug":3E3>=a?"info":4E3>=a?"warn":5E3>=a?"error":"fatal",b.msg,b.meta,function(){},a,b.finalString,this.loggerName),d--}return this};b.prototype.trace=function(a){return this.log(1E3,a)};b.prototype.debug=function(a){return this.log(2E3,a)};
b.prototype.info=function(a){return this.log(3E3,a)};b.prototype.warn=function(a){return this.log(4E3,a)};b.prototype.error=function(a){return this.log(5E3,a)};b.prototype.fatal=function(a){return this.log(6E3,a)};b.prototype.fatalException=function(a,b){return this.log(6E3,a,b)};return b}();c.Logger=f;c.createAjaxAppender=function(b){return new n(b)};c.createConsoleAppender=function(b){return new p(b)};f=new n("");"undefined"===typeof window&&(f=new p(""));c.__=new c.Logger("");c.__.setOptions({level:c.getDebugLevel(),
appenders:[f]})})(JL||(JL={}));"undefined"!==typeof exports&&(exports.JL=JL);var define;"function"==typeof define&&define.amd&&define("jsnlog",[],function(){return JL});"function"==typeof __jsnlog_configure&&__jsnlog_configure(JL);"undefined"===typeof window||window.onerror||(window.onerror=function(c,d,k,e,g){JL("onerrorLogger").fatalException({msg:"Uncaught Exception",errorMsg:c,url:d,"line number":k,column:e},g);return!1});
"undefined"===typeof window||window.onunhandledrejection||(window.onunhandledrejection=function(c){JL("onerrorLogger").fatalException({msg:"unhandledrejection",errorMsg:c.reason?c.reason.message:null},c.reason);return!1});
