<configuration>
    <configSections>
        <section name="jsnlog" type="JSNLog.ConfigurationSection<PERSON><PERSON><PERSON>, JSNLog" requirePermission="false"/>
    </configSections>
    
    <jsnlog productionLibraryPath="~/Scripts/jsnlog.min.js" >
    </jsnlog>

	<system.web>
		<httpHandlers>
			<add verb="*" path="*.logger" type="JSNLog.LoggerHandler, JSNLog"/>
		</httpHandlers>
	</system.web>
	
    <system.webServer>
        <validation validateIntegratedModeConfiguration="false"/>
        <handlers>
            <add name="LoggerHandler" verb="*" path="*.logger" type="JSNLog.LoggerHandler, JSNLog" resourceType="Unspecified" preCondition="integratedMode"/>
            <add name="LoggerHandler-Classic" path="*.logger" verb="*" modules="IsapiModule" scriptProcessor="%windir%\Microsoft.NET\Framework64\v4.0.30319\aspnet_isapi.dll" resourceType="Unspecified" preCondition="classicMode" />
        </handlers>
    </system.webServer>

</configuration>
