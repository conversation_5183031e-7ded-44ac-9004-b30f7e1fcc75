/*!
 * ASP.NET SignalR JavaScript Library v1.2.1
 * http://signalr.net/
 *
 * Copyright Microsoft Open Technologies, Inc. All rights reserved.
 * Licensed under the Apache 2.0
 * https://github.com/SignalR/SignalR/blob/master/LICENSE.md
 *
 */
(function(n,t,i){"use strict";function c(t,i){var u,f;if(n.isArray(t)){for(u=t.length-1;u>=0;u--)f=t[u],n.type(t)==="object"||n.type(f)==="string"&&r.transports[f]||(i.log("Invalid transport: "+f+", removing it from the transports list."),t.splice(u,1));t.length===0&&(i.log("No transports remain within the specified transport array."),t=null)}else if(n.type(t)==="object"||r.transports[t]||t==="auto"){if(t==="auto"&&r._.ieVersion<=8)return["longPolling"]}else i.log("Invalid transport: "+t.toString()+"."),t=null;return t}function l(n){return n==="http:"?80:n==="https:"?443:void 0}function h(n,t){return t.match(/:\d+$/)?t:t+":"+l(n)}if(typeof n!="function")throw new Error("SignalR: jQuery not found. Please ensure jQuery is referenced before the SignalR.js file.");if(!t.JSON)throw new Error("SignalR: No JSON parser found. Please ensure json2.js is referenced before the SignalR.js file if you need to support clients without native JSON parsing support, e.g. IE<8.");var r,s,o=t.document.readyState==="complete",f=n(t),u={onStart:"onStart",onStarting:"onStarting",onReceived:"onReceived",onError:"onError",onConnectionSlow:"onConnectionSlow",onReconnecting:"onReconnecting",onReconnect:"onReconnect",onStateChanged:"onStateChanged",onDisconnect:"onDisconnect"},a={processData:!0,timeout:null,async:!0,global:!1,cache:!1},v=function(n,i){if(i!==!1){var r;typeof t.console!="undefined"&&(r="["+(new Date).toTimeString()+"] SignalR: "+n,t.console.debug?t.console.debug(r):t.console.log&&t.console.log(r))}},e=function(t,i,r){return i===t.state?(t.state=r,n(t).triggerHandler(u.onStateChanged,[{oldState:i,newState:r}]),!0):!1},y=function(n){return n.state===r.connectionState.disconnected},p=function(n){var u,i;n._.configuredStopReconnectingTimeout||(i=function(n){n.log("Couldn't reconnect within the configured timeout ("+n.disconnectTimeout+"ms), disconnecting."),n.stop(!1,!1)},n.reconnecting(function(){var n=this;n.state===r.connectionState.reconnecting&&(u=t.setTimeout(function(){i(n)},n.disconnectTimeout))}),n.stateChanged(function(n){n.oldState===r.connectionState.reconnecting&&t.clearTimeout(u)}),n._.configuredStopReconnectingTimeout=!0)};r=function(n,t,i){return new r.fn.init(n,t,i)},r._={defaultContentType:"application/x-www-form-urlencoded; charset=UTF-8",ieVersion:function(){var i,n;return t.navigator.appName==="Microsoft Internet Explorer"&&(n=/MSIE ([0-9]+\.[0-9]+)/.exec(t.navigator.userAgent),n&&(i=t.parseFloat(n[1]))),i}(),firefoxMajorVersion:function(n){var t=n.match(/Firefox\/(\d+)/);return!t||!t.length||t.length<2?0:parseInt(t[1],10)},configurePingInterval:function(i){var f=i._,e=function(t){n(i).triggerHandler(u.onError,[t])};!f.pingIntervalId&&f.pingInterval&&(f.pingIntervalId=t.setInterval(function(){r.transports._logic.pingServer(i).fail(e)},f.pingInterval))}},r.events=u,r.ajaxDefaults=a,r.changeState=e,r.isDisconnecting=y,r.connectionState={connecting:0,connected:1,reconnecting:2,disconnected:4},r.hub={start:function(){throw new Error("SignalR: Error loading hubs. Ensure your hubs reference is correct, e.g. <script src='/signalr/hubs'><\/script>.");}},f.load(function(){o=!0}),r.fn=r.prototype={init:function(n,t,i){this.url=n,this.qs=t,this._={keepAliveData:{},negotiateAbortText:"__Negotiate Aborted__",pingAbortText:"__Ping Aborted__",pingIntervalId:null,pingInterval:3e5,pollTimeoutId:null,reconnectTimeoutId:null,lastMessageAt:+new Date,lastActiveAt:+new Date,beatInterval:5e3,beatHandle:null,activePings:{},nextPingId:0},typeof i=="boolean"&&(this.logging=i)},_parseResponse:function(n){var t=this;return n?t.ajaxDataType==="text"?t.json.parse(n):n:n},json:t.JSON,isCrossDomain:function(i,r){var u;return(i=n.trim(i),r=r||t.location,i.indexOf("http")!==0)?!1:(u=t.document.createElement("a"),u.href=i,u.protocol+h(u.protocol,u.host)!==r.protocol+h(r.protocol,r.host))},ajaxDataType:"text",contentType:"application/json; charset=UTF-8",logging:!1,state:r.connectionState.disconnected,reconnectDelay:2e3,disconnectTimeout:3e4,reconnectWindow:3e4,keepAliveWarnAt:2/3,start:function(s,h){var l=this,a={pingInterval:3e5,waitForPageLoad:!0,transport:"auto",jsonp:!1},b,v=l._deferral||n.Deferred(),y=t.document.createElement("a"),w,k;if(l._deferral=v,n.type(s)==="function"?h=s:n.type(s)==="object"&&(n.extend(a,s),n.type(a.callback)==="function"&&(h=a.callback)),a.transport=c(a.transport,l),!a.transport)throw new Error("SignalR: Invalid transport(s) specified, aborting start.");return(l._.config=a,l._.pingInterval=a.pingInterval,!o&&a.waitForPageLoad===!0)?(l._.deferredStartHandler=function(){l.start(s,h)},f.bind("load",l._.deferredStartHandler),v.promise()):l.state===r.connectionState.connecting?v.promise():e(l,r.connectionState.disconnected,r.connectionState.connecting)===!1?(v.resolve(l),v.promise()):(p(l),y.href=l.url,y.protocol&&y.protocol!==":"?(l.protocol=y.protocol,l.host=y.host,l.baseUrl=y.protocol+"//"+y.host):(l.protocol=t.document.location.protocol,l.host=t.document.location.host,l.baseUrl=l.protocol+"//"+l.host),l.wsProtocol=l.protocol==="https:"?"wss://":"ws://",a.transport==="auto"&&a.jsonp===!0&&(a.transport="longPolling"),l.url.indexOf("//")===0&&(l.url=t.location.protocol+l.url,l.log("Protocol relative URL detected, normalizing it to '"+l.url+"'.")),this.isCrossDomain(l.url)&&(l.log("Auto detected cross domain url."),a.transport==="auto"&&(a.transport=["webSockets","longPolling"]),a.withCredentials===i&&(a.withCredentials=!0),a.jsonp||(a.jsonp=!n.support.cors,a.jsonp&&l.log("Using jsonp because this browser doesn't support CORS.")),l.contentType=r._.defaultContentType),l.withCredentials=a.withCredentials,l.ajaxDataType=a.jsonp?"jsonp":"text",n(l).bind(u.onStart,function(){n.type(h)==="function"&&h.call(l),v.resolve(l)}),b=function(i,o){if(o=o||0,o>=i.length){n(l).triggerHandler(u.onError,["SignalR: No transport could be initialized successfully. Try specifying a different transport or none at all for auto initialization."]),v.reject("SignalR: No transport could be initialized successfully. Try specifying a different transport or none at all for auto initialization."),l.stop();return}if(l.state!==r.connectionState.disconnected){var s=i[o],h=n.type(s)==="object"?s:r.transports[s];if(l.transport=h,s.indexOf("_")===0){b(i,o+1);return}h.start(l,function(){var o=r._.firefoxMajorVersion(t.navigator.userAgent)>=11,i=!!l.withCredentials&&o;l.state!==r.connectionState.disconnected&&(h.supportsKeepAlive&&l._.keepAliveData.activated&&r.transports._logic.monitorKeepAlive(l),r.transports._logic.startHeartbeat(l),r._.configurePingInterval(l),e(l,r.connectionState.connecting,r.connectionState.connected),n(l).triggerHandler(u.onStart),f.bind("unload",function(){l.log("Window unloading, stopping the connection."),l.stop(i)}),r._.firefoxMajorVersion(t.navigator.userAgent)>=11&&f.bind("beforeunload",function(){t.setTimeout(function(){l.stop(i)},0)}))},function(){b(i,o+1)})}},n(l).triggerHandler(u.onStarting),w=l.url+"/negotiate",k=function(t,i){n(i).triggerHandler(u.onError,[t.responseText]),v.reject("SignalR: Error during negotiation request: "+t.responseText),i.stop()},w=r.transports._logic.prepareQueryString(l,w),l.log("Negotiating with '"+w+"'."),l._.negotiateRequest=n.ajax(n.extend({},n.signalR.ajaxDefaults,{xhrFields:{withCredentials:l.withCredentials},url:w,type:"GET",contentType:l.contentType,data:{},dataType:l.ajaxDataType,error:function(n,t){t!==l._.negotiateAbortText?k(n,l):v.reject("Stopped the connection while negotiating.")},success:function(t){var i,f=l._.keepAliveData,o,e;try{i=l._parseResponse(t)}catch(s){k(s,l);return}if(f=l._.keepAliveData,l.appRelativeUrl=i.Url,l.id=i.ConnectionId,l.token=i.ConnectionToken,l.webSocketServerUrl=i.WebSocketServerUrl,l.disconnectTimeout=i.DisconnectTimeout*1e3,i.KeepAliveTimeout?(f.activated=!0,f.timeout=i.KeepAliveTimeout*1e3,f.timeoutWarning=f.timeout*l.keepAliveWarnAt,l._.beatInterval=(f.timeout-f.timeoutWarning)/3):f.activated=!1,!i.ProtocolVersion||i.ProtocolVersion!=="1.2"){n(l).triggerHandler(u.onError,["You are using a version of the client that isn't compatible with the server. Client version 1.2, server version "+i.ProtocolVersion+"."]),v.reject("You are using a version of the client that isn't compatible with the server. Client version 1.2, server version "+i.ProtocolVersion+".");return}o=[],e=[],n.each(r.transports,function(n){if(n==="webSockets"&&!i.TryWebSockets)return!0;e.push(n)}),n.isArray(a.transport)?n.each(a.transport,function(){var t=this;(n.type(t)==="object"||n.type(t)==="string"&&n.inArray(""+t,e)>=0)&&o.push(n.type(t)==="string"?""+t:t)}):n.type(a.transport)==="object"||n.inArray(a.transport,e)>=0?o.push(a.transport):o=e,b(o)}})),v.promise())},starting:function(t){var i=this;return n(i).bind(u.onStarting,function(){t.call(i)}),i},send:function(n){var t=this;if(t.state===r.connectionState.disconnected)throw new Error("SignalR: Connection must be started before data can be sent. Call .start() before .send()");if(t.state===r.connectionState.connecting)throw new Error("SignalR: Connection has not been fully initialized. Use .start().done() or .start().fail() to run logic after the connection has started.");return t.transport.send(t,n),t},received:function(t){var i=this;return n(i).bind(u.onReceived,function(n,r){t.call(i,r)}),i},stateChanged:function(t){var i=this;return n(i).bind(u.onStateChanged,function(n,r){t.call(i,r)}),i},error:function(t){var i=this;return n(i).bind(u.onError,function(n,r,u){t.call(i,r,u)}),i},disconnected:function(t){var i=this;return n(i).bind(u.onDisconnect,function(){t.call(i)}),i},connectionSlow:function(t){var i=this;return n(i).bind(u.onConnectionSlow,function(){t.call(i)}),i},reconnecting:function(t){var i=this;return n(i).bind(u.onReconnecting,function(){t.call(i)}),i},reconnected:function(t){var i=this;return n(i).bind(u.onReconnect,function(){t.call(i)}),i},stop:function(i,s){var h=this,l=h._deferral,c=h._.config;if(h._.deferredStartHandler&&f.unbind("load",h._.deferredStartHandler),delete h._deferral,delete h._.config,delete h._.deferredStartHandler,!o&&(!c||c.waitForPageLoad===!0)){h.log("Stopping connection prior to negotiate."),l&&l.reject("The connection was stopped during page load.");return}if(h.state!==r.connectionState.disconnected)return h.log("Stopping connection."),e(h,h.state,r.connectionState.disconnected),t.clearTimeout(h._.beatHandle),t.clearInterval(h._.pingIntervalId),t.clearTimeout(h._.pingLoopId),h.transport&&(s!==!1&&h.transport.abort(h,i),h.transport.supportsKeepAlive&&h._.keepAliveData.activated&&r.transports._logic.stopMonitoringKeepAlive(h),h.transport.stop(h),h.transport=null),h._.negotiateRequest&&(h._.negotiateRequest.abort(h._.negotiateAbortText),delete h._.negotiateRequest),n.each(h._.activePings,function(n,t){h.log("Aborting ping "+n+"."),t.abort(h._.pingAbortText)}),n(h).triggerHandler(u.onDisconnect),delete h.messageId,delete h.groupsToken,delete h.id,delete h._.pingIntervalId,delete h._.lastMessageAt,delete h._.lastActiveAt,delete h._.pingLoopId,h},log:function(n){v(n,this.logging)}},r.fn.init.prototype=r.fn,r.noConflict=function(){return n.connection===r&&(n.connection=s),r},n.connection&&(s=n.connection),n.connection=n.signalR=r})(window.jQuery,window),function(n,t){"use strict";function e(n){n._.keepAliveData.monitoring&&c(n),r.markActive(n)&&(n._.beatHandle=t.setTimeout(function(){e(n)},n._.beatInterval))}function c(t){var i=t._.keepAliveData,r;t.state===u.connectionState.connected&&(r=+new Date-t._.lastMessageAt,r>=i.timeout?(t.log("Keep alive timed out.  Notifying transport that connection has been lost."),t.transport.lostConnection(t)):r>=i.timeoutWarning?i.userNotified||(t.log("Keep alive has been missed, connection may be dead/slow."),n(t).triggerHandler(f.onConnectionSlow),i.userNotified=!0):i.userNotified=!1)}function o(n){return n.state===u.connectionState.connected||n.state===u.connectionState.reconnecting}function s(n,i){var r=n.indexOf("?")!==-1?"&":"?";return i&&(n+=r+"connectionData="+t.encodeURIComponent(i)),n}var u=n.signalR,f=n.signalR.events,h=n.signalR.changeState,r;u.transports={},r=u.transports._logic={pingServer:function(t){var u,i=n.Deferred(),e=t._.activePings,f=t._.nextPingId++;return u=t.url+"/ping",u=r.prepareQueryString(t,u),e[f]=n.ajax(n.extend({},n.signalR.ajaxDefaults,{xhrFields:{withCredentials:t.withCredentials},url:u,type:"GET",contentType:t.contentType,data:{},dataType:t.ajaxDataType,success:function(n){var r;try{r=t._parseResponse(n)}catch(u){i.reject("Failed to parse ping server response, stopping the connection: "+n),t.stop();return}r.Response==="pong"?i.resolve():i.reject("SignalR: Invalid ping response when pinging server: "+r.Response)},error:function(n,r){n.status===401||n.status===403?(i.reject("Failed to ping server. Server responded with a "+n.status+" status code, stopping the connection."),t.stop()):r===t._.pingAbortText?t.log("Ping "+f+" aborted."):i.reject("SignalR: Error pinging server: "+(n.responseText||n.statusText))},complete:function(){delete e[f]}})),i.promise()},prepareQueryString:function(n,t){return t=r.addQs(t,n),s(t,n.data)},addQs:function(t,i){var u=t.indexOf("?")!==-1?"&":"?",r;if(!i.qs)return t;if(typeof i.qs=="object")return t+u+n.param(i.qs);if(typeof i.qs=="string")return r=i.qs.charAt(0),(r==="?"||r==="&")&&(u=""),t+u+i.qs;throw new Error("Connections query string property must be either a string or object.");},getUrl:function(n,i,u,f){var s=i==="webSockets"?"":n.baseUrl,e=s+n.appRelativeUrl,o="transport="+i+"&connectionToken="+t.encodeURIComponent(n.token);return n.groupsToken&&(o+="&groupsToken="+t.encodeURIComponent(n.groupsToken)),u?(e+=f?"/poll":"/reconnect",n.messageId&&(o+="&messageId="+t.encodeURIComponent(n.messageId))):e+="/connect",e+="?"+o,e=r.prepareQueryString(n,e),e+="&tid="+Math.floor(Math.random()*11)},maximizePersistentResponse:function(n){return{MessageId:n.C,Messages:n.M,Disconnect:typeof n.D!="undefined"?!0:!1,ShouldReconnect:typeof n.T!="undefined"?!0:!1,LongPollDelay:n.L,GroupsToken:n.G}},updateGroups:function(n,t){t&&(n.groupsToken=t)},ajaxSend:function(i,e){var o=i.url+"/send?transport="+i.transport.name+"&connectionToken="+t.encodeURIComponent(i.token),s=function(t,i){n(i).triggerHandler(f.onError,[t,e])};return o=r.prepareQueryString(i,o),n.ajax(n.extend({},n.signalR.ajaxDefaults,{xhrFields:{withCredentials:i.withCredentials},url:o,type:i.ajaxDataType==="jsonp"?"GET":"POST",contentType:u._.defaultContentType,dataType:i.ajaxDataType,data:{data:e},success:function(t){var r;if(t){try{r=i._parseResponse(t)}catch(u){s(u,i),i.stop();return}n(i).triggerHandler(f.onReceived,[r])}},error:function(n,t){t!=="abort"&&t!=="parsererror"&&s(n,i)}}))},ajaxAbort:function(i,u){if(typeof i.transport!="undefined"){u=typeof u=="undefined"?!0:u;var f=i.url+"/abort?transport="+i.transport.name+"&connectionToken="+t.encodeURIComponent(i.token);f=r.prepareQueryString(i,f),n.ajax(n.extend({},n.signalR.ajaxDefaults,{xhrFields:{withCredentials:i.withCredentials},url:f,async:u,timeout:1e3,type:"POST",contentType:i.contentType,dataType:i.ajaxDataType,data:{}})),i.log("Fired ajax abort async = "+u+".")}},processMessages:function(t,i){var u,e;if(t.transport){if(e=n(t),r.markLastMessage(t),!i)return;if(u=this.maximizePersistentResponse(i),u.Disconnect){t.log("Disconnect command received from server."),t.stop(!1,!1);return}this.updateGroups(t,u.GroupsToken),u.Messages&&n.each(u.Messages,function(n,t){e.triggerHandler(f.onReceived,[t])}),u.MessageId&&(t.messageId=u.MessageId)}},monitorKeepAlive:function(t){var i=t._.keepAliveData;i.monitoring?t.log("Tried to monitor keep alive but it's already being monitored."):(i.monitoring=!0,r.markLastMessage(t),t._.keepAliveData.reconnectKeepAliveUpdate=function(){r.markLastMessage(t)},n(t).bind(f.onReconnect,t._.keepAliveData.reconnectKeepAliveUpdate),t.log("Now monitoring keep alive with a warning timeout of "+i.timeoutWarning+" and a connection lost timeout of "+i.timeout+"."))},stopMonitoringKeepAlive:function(t){var i=t._.keepAliveData;i.monitoring&&(i.monitoring=!1,n(t).unbind(f.onReconnect,t._.keepAliveData.reconnectKeepAliveUpdate),t._.keepAliveData={},t.log("Stopping the monitoring of the keep alive."))},startHeartbeat:function(n){e(n)},markLastMessage:function(n){n._.lastMessageAt=+new Date},markActive:function(n){return r.verifyLastActive(n)?(n._.lastActiveAt=+new Date,!0):!1},ensureReconnectingState:function(t){return h(t,u.connectionState.connected,u.connectionState.reconnecting)===!0&&n(t).triggerHandler(f.onReconnecting),t.state===u.connectionState.reconnecting},clearReconnectTimeout:function(n){n&&n._.reconnectTimeout&&(t.clearTimeout(n._.reconnectTimeout),delete n._.reconnectTimeout)},verifyLastActive:function(n){return+new Date-n._.lastActiveAt>=n.reconnectWindow?(n.log("There has not been an active server connection for an extended periord of time. Stopping connection."),n.stop(),!1):!0},reconnect:function(n,i){var f=u.transports[i],e=this;if(o(n)&&!n._.reconnectTimeout){if(!r.verifyLastActive(n))return;n._.reconnectTimeout=t.setTimeout(function(){r.verifyLastActive(n)&&(f.stop(n),e.ensureReconnectingState(n)&&(n.log(i+" reconnecting."),f.start(n)))},n.reconnectDelay)}},handleParseFailure:function(t,i,r,e){t.state===u.connectionState.connecting?(t.log("Failed to parse server response while attempting to connect."),e()):(n(t).triggerHandler(f.onError,["SignalR: failed at parsing response: "+i+".  With error: "+r]),t.stop())},foreverFrame:{count:0,connections:{}}}}(window.jQuery,window),function(n,t){"use strict";var u=n.signalR,f=n.signalR.events,e=n.signalR.changeState,r=u.transports._logic;u.transports.webSockets={name:"webSockets",supportsKeepAlive:!0,timeOut:3e3,send:function(n,t){n.socket.send(t)},start:function(i,o,s){var h,v=!1,l=this,y,c,a=!o;if(!t.WebSocket){s();return}i.socket||(h=i.webSocketServerUrl?i.webSocketServerUrl:i.wsProtocol+i.host,h+=r.getUrl(i,this.name,a),i.log("Connecting to websocket endpoint '"+h+"'."),i.socket=new t.WebSocket(h),s&&(y=i.socket,c=t.setTimeout(function(){y===i.socket&&(i.log("WebSocket timed out trying to connect."),s())},l.timeOut)),i.socket.onopen=function(){t.clearTimeout(c),v=!0,i.log("Websocket opened.")},i.socket.onclose=function(r){if(t.clearTimeout(c),this===i.socket){if(v)typeof r.wasClean!="undefined"&&r.wasClean===!1?(n(i).triggerHandler(f.onError,[r.reason]),i.log("Unclean disconnect from websocket: "+r.reason||"[no reason given].")):i.log("Websocket closed.");else{s?s():a&&l.reconnect(i);return}l.reconnect(i)}},i.socket.onmessage=function(t){var h,c=n(i);try{h=i._parseResponse(t.data)}catch(l){r.handleParseFailure(i,t.data,l.message,s);return}o?(o(),o=null):e(i,u.connectionState.reconnecting,u.connectionState.connected)===!0&&c.triggerHandler(f.onReconnect),r.clearReconnectTimeout(i),h&&(n.isEmptyObject(h)||h.M?r.processMessages(i,h):c.triggerHandler(f.onReceived,[h]))})},reconnect:function(n){r.reconnect(n,this.name)},lostConnection:function(n){this.reconnect(n)},stop:function(n){r.clearReconnectTimeout(n),n.socket!==null&&(n.log("Closing the Websocket."),n.socket.close(),n.socket=null)},abort:function(n,t){r.ajaxAbort(n,t)}}}(window.jQuery,window),function(n,t){"use strict";var u=n.signalR,f=n.signalR.events,e=n.signalR.changeState,r=u.transports._logic;u.transports.serverSentEvents={name:"serverSentEvents",supportsKeepAlive:!0,timeOut:3e3,start:function(i,o,s){var h=this,l=!1,y=n(i),c=!o,v,a;if(i.eventSource&&(i.log("The connection already has an event source. Stopping it."),i.stop()),!t.EventSource){s&&(i.log("This browser doesn't support SSE."),s());return}v=r.getUrl(i,this.name,c);try{i.log("Attempting to connect to SSE endpoint '"+v+"'."),i.eventSource=new t.EventSource(v)}catch(p){i.log("EventSource failed trying to connect with error "+p.Message+"."),s?s():(y.triggerHandler(f.onError,[p]),c&&h.reconnect(i));return}a=t.setTimeout(function(){l===!1&&i.eventSource&&(i.log("EventSource timed out trying to connect."),i.log("EventSource readyState: "+i.eventSource.readyState+"."),c||h.stop(i),c?i.eventSource.readyState!==t.EventSource.OPEN&&h.reconnect(i):s&&s())},h.timeOut),i.eventSource.addEventListener("open",function(){i.log("EventSource connected."),a&&t.clearTimeout(a),r.clearReconnectTimeout(i),l===!1&&(l=!0,o?o():e(i,u.connectionState.reconnecting,u.connectionState.connected)===!0&&y.triggerHandler(f.onReconnect))},!1),i.eventSource.addEventListener("message",function(n){var t;if(n.data!=="initialized"){try{t=i._parseResponse(n.data)}catch(u){r.handleParseFailure(i,n.data,u.message,s);return}r.processMessages(i,t,o)}},!1),i.eventSource.addEventListener("error",function(n){if(this===i.eventSource){if(!l){s&&s();return}i.log("EventSource readyState: "+i.eventSource.readyState+"."),n.eventPhase===t.EventSource.CLOSED?(i.log("EventSource reconnecting due to the server connection ending."),h.reconnect(i)):(i.log("EventSource error."),y.triggerHandler(f.onError))}},!1)},reconnect:function(n){r.reconnect(n,this.name)},lostConnection:function(n){this.reconnect(n)},send:function(n,t){r.ajaxSend(n,t)},stop:function(n){r.clearReconnectTimeout(n),n&&n.eventSource&&(n.log("EventSource calling close()."),n.eventSource.close(),n.eventSource=null,delete n.eventSource)},abort:function(n,t){r.ajaxAbort(n,t)}}}(window.jQuery,window),function(n,t){"use strict";var u=n.signalR,o=n.signalR.events,s=n.signalR.changeState,r=u.transports._logic,f=function(){var n=t.document.createElement("iframe");return n.setAttribute("style","position:absolute;top:0;left:0;width:0;height:0;visibility:hidden;"),n},e=function(){var i=null,r=1e3,n=0;return{prevent:function(){u._.ieVersion<=8&&(n===0&&(i=t.setInterval(function(){var n=f();t.document.body.appendChild(n),t.document.body.removeChild(n),n=null},r)),n++)},cancel:function(){n===1&&t.clearInterval(i),n>0&&n--}}}();u.transports.foreverFrame={name:"foreverFrame",supportsKeepAlive:!0,timeOut:3e3,iframeClearThreshold:50,start:function(n,i,u){var s=this,c=r.foreverFrame.count+=1,h,o=f(),l=function(){n.log("Forever frame iframe finished loading and is no longer receiving messages, reconnecting."),s.reconnect(n)};if(t.EventSource){u&&(n.log("This browser supports SSE, skipping Forever Frame."),u());return}o.setAttribute("data-signalr-connection-id",n.id),e.prevent(),h=r.getUrl(n,this.name),h+="&frameId="+c,t.document.body.appendChild(o),n.log("Binding to iframe's load event."),o.addEventListener?o.addEventListener("load",l,!1):o.attachEvent&&o.attachEvent("onload",l),o.src=h,r.foreverFrame.connections[c]=n,n.frame=o,n.frameId=c,i&&(n.onSuccess=function(){n.log("Iframe transport started."),i()}),t.setTimeout(function(){n.onSuccess&&(n.log("Failed to connect using forever frame source, it timed out after "+s.timeOut+"ms."),s.stop(n),u&&u())},s.timeOut)},reconnect:function(n){var i=this;r.verifyLastActive(n)&&t.setTimeout(function(){if(r.verifyLastActive(n)&&n.frame&&r.ensureReconnectingState(n)){var u=n.frame,t=r.getUrl(n,i.name,!0)+"&frameId="+n.frameId;n.log("Updating iframe src to '"+t+"'."),u.src=t}},n.reconnectDelay)},lostConnection:function(n){this.reconnect(n)},send:function(n,t){r.ajaxSend(n,t)},receive:function(t,i){var f,e;if(r.processMessages(t,i),t.frameMessageCount=(t.frameMessageCount||0)+1,t.frameMessageCount>u.transports.foreverFrame.iframeClearThreshold&&t.state===n.signalR.connectionState.connected&&(t.frameMessageCount=0,f=t.frame.contentWindow||t.frame.contentDocument,f&&f.document&&f.document.body))for(e=f.document.body;e.firstChild;)e.removeChild(e.firstChild)},stop:function(n){var i=null;if(e.cancel(),n.frame){if(n.frame.stop)n.frame.stop();else try{i=n.frame.contentWindow||n.frame.contentDocument,i.document&&i.document.execCommand&&i.document.execCommand("Stop")}catch(u){n.log("Error occured when stopping foreverFrame transport. Message = "+u.message+".")}n.frame.parentNode===t.document.body&&t.document.body.removeChild(n.frame),delete r.foreverFrame.connections[n.frameId],n.frame=null,n.frameId=null,delete n.frame,delete n.frameId,delete n.frameMessageCount,n.log("Stopping forever frame.")}},abort:function(n,t){r.ajaxAbort(n,t)},getConnection:function(n){return r.foreverFrame.connections[n]},started:function(t){t.onSuccess?(t.onSuccess(),t.onSuccess=null,delete t.onSuccess):s(t,u.connectionState.reconnecting,u.connectionState.connected)===!0&&n(t).triggerHandler(o.onReconnect)}}}(window.jQuery,window),function(n,t){"use strict";var u=n.signalR,e=n.signalR.events,o=n.signalR.changeState,f=n.signalR.isDisconnecting,r=u.transports._logic;u.transports.longPolling={name:"longPolling",supportsKeepAlive:!1,reconnectDelay:3e3,init:function(n,i){var o=this,u,e=function(i){f(n)===!1&&(n.log("Server ping failed because '"+i+"', re-trying ping."),n._.pingLoopId=t.setTimeout(u,o.reconnectDelay))};n.log("Initializing long polling connection with server."),u=function(){r.pingServer(n).done(i).fail(e)},u()},start:function(i,s,h){var l=this,v=function(){y=v=n.noop,i.log("Longpolling connected."),s(),h=null},y=function(){return h?(h(),h=null,i.log("LongPolling failed to connect."),!0):!1},c=i._,a=0,p=function(r){t.clearTimeout(c.reconnectTimeoutId),c.reconnectTimeoutId=null,o(i,u.connectionState.reconnecting,u.connectionState.connected)===!0&&(i.log("Raising the reconnect event."),n(r).triggerHandler(e.onReconnect))},w=36e5;i.pollXhr&&(i.log("Polling xhr requests already exists, aborting."),i.stop()),c.reconnectTimeoutId=null,c.pollTimeoutId=null,l.init(i,function(){i.messageId=null,c.pollTimeoutId=t.setTimeout(function(){(function o(s,h){var nt=s.messageId,d=nt===null,b=!d,g=!h,k=r.getUrl(s,l.name,b,g);f(s)!==!0&&(i.log("Opening long polling request to '"+k+"'."),s.pollXhr=n.ajax(n.extend({},n.signalR.ajaxDefaults,{xhrFields:{withCredentials:i.withCredentials},url:k,type:"GET",dataType:i.ajaxDataType,contentType:i.contentType,success:function(u){var w=0,l,e,h;i.log("Long poll complete."),a=0;try{l=i._parseResponse(u)}catch(b){r.handleParseFailure(s,u,b.message,y);return}(c.reconnectTimeoutId!==null&&p(),v(),l&&(e=r.maximizePersistentResponse(l)),r.processMessages(s,l),e&&n.type(e.LongPollDelay)==="number"&&(w=e.LongPollDelay),e&&e.Disconnect)||f(s)!==!0&&(h=e&&e.ShouldReconnect,h&&r.ensureReconnectingState(s),w>0?c.pollTimeoutId=t.setTimeout(function(){o(s,h)},w):o(s,h))},error:function(f,h){if(t.clearTimeout(c.reconnectTimeoutId),c.reconnectTimeoutId=null,h==="abort"){i.log("Aborted xhr requst.");return}if(!y()){if(a++,i.state!==u.connectionState.reconnecting&&(i.log("An error occurred using longPolling. Status = "+h+".  Response = "+f.responseText+"."),n(s).triggerHandler(e.onError,[f.responseText])),(i.state===u.connectionState.connected||i.state===u.connectionState.reconnecting)&&!r.verifyLastActive(i))return;if(!r.ensureReconnectingState(s))return;c.pollTimeoutId=t.setTimeout(function(){l.init(s,function(){o(s,!0)})},l.reconnectDelay)}}})),b&&h===!0&&(c.reconnectTimeoutId=t.setTimeout(function(){p(s)},Math.min(1e3*(Math.pow(2,a)-1),w))))})(i),t.setTimeout(function(){v()},250)},250)})},lostConnection:function(){throw new Error("Lost Connection not handled for LongPolling");},send:function(n,t){r.ajaxSend(n,t)},stop:function(n){t.clearTimeout(n._.pollTimeoutId),t.clearTimeout(n._.reconnectTimeoutId),delete n._.pollTimeoutId,delete n._.reconnectTimeoutId,n.pollXhr&&(n.pollXhr.abort(),n.pollXhr=null,delete n.pollXhr)},abort:function(n,t){r.ajaxAbort(n,t)}}}(window.jQuery,window),function(n){"use strict";function f(n){return n+h}function s(n,t,i){for(var f=n.length,u=[],r=0;r<f;r+=1)n.hasOwnProperty(r)&&(u[r]=t.call(i,n[r],r,n));return u}function c(t){return n.isFunction(t)?null:n.type(t)==="undefined"?null:t}function e(n){for(var t in n)if(n.hasOwnProperty(t))return!0;return!1}function o(n,t){var r=n._.invocationCallbacks,i,u;e(r)&&n.log("Clearing hub invocation callbacks with error: "+t+"."),n._.invocationCallbackId=0,delete n._.invocationCallbacks,n._.invocationCallbacks={};for(u in r)i=r[u],i.method.call(i.scope,{E:t})}function u(n,t){return new u.fn.init(n,t)}function r(t,i){var u={qs:null,logging:!1,useDefaultPath:!0};return n.extend(u,i),(!t||u.useDefaultPath)&&(t=(t||"")+"/signalr"),new r.fn.init(t,u)}var h=".hubProxy";u.fn=u.prototype={init:function(n,t){this.state={},this.connection=n,this.hubName=t,this._={callbackMap:{}}},hasSubscriptions:function(){return e(this._.callbackMap)},on:function(t,i){var u=this,r=u._.callbackMap;return t=t.toLowerCase(),r[t]||(r[t]={}),r[t][i]=function(n,t){i.apply(u,t)},n(u).bind(f(t),r[t][i]),u},off:function(t,i){var u=this,o=u._.callbackMap,r;return t=t.toLowerCase(),r=o[t],r&&(r[i]?(n(u).unbind(f(t),r[i]),delete r[i],e(r)||delete o[t]):i||(n(u).unbind(f(t)),delete o[t])),u},invoke:function(t){var i=this,r=i.connection,o=n.makeArray(arguments).slice(1),h=s(o,c),f={H:i.hubName,M:t,A:h,I:r._.invocationCallbackId},u=n.Deferred(),e=function(t){var f=i._maximizeHubResponse(t);n.extend(i.state,f.State),f.Error?(f.StackTrace&&r.log(f.Error+"\n"+f.StackTrace+"."),u.rejectWith(i,[f.Error])):u.resolveWith(i,[f.Result])};return r._.invocationCallbacks[r._.invocationCallbackId.toString()]={scope:i,method:e},r._.invocationCallbackId+=1,n.isEmptyObject(i.state)||(f.S=i.state),r.send(i.connection.json.stringify(f)),u.promise()},_maximizeHubResponse:function(n){return{State:n.S,Result:n.R,Id:n.I,Error:n.E,StackTrace:n.T}}},u.fn.init.prototype=u.fn,r.fn=r.prototype=n.connection(),r.fn.init=function(t,i){var u={qs:null,logging:!1,useDefaultPath:!0},r=this;n.extend(u,i),n.signalR.fn.init.call(r,t,u.qs,u.logging),r.proxies={},r._.invocationCallbackId=0,r._.invocationCallbacks={},r.received(function(t){var i,o,e,u,h,s;t&&(typeof t.I!="undefined"?(e=t.I.toString(),u=r._.invocationCallbacks[e],u&&(r._.invocationCallbacks[e]=null,delete r._.invocationCallbacks[e],u.method.call(u.scope,t))):(i=this._maximizeClientHubInvocation(t),r.log("Triggering client hub event '"+i.Method+"' on hub '"+i.Hub+"'."),h=i.Hub.toLowerCase(),s=i.Method.toLowerCase(),o=this.proxies[h],n.extend(o.state,i.State),n(o).triggerHandler(f(s),[i.Args])))}),r.error(function(n,t){var u,i;if((!r.transport||r.transport.name!=="webSockets")&&t){try{t=r.json.parse(t)}catch(f){return}u=t.I,i=r._.invocationCallbacks[u],i&&(r._.invocationCallbacks[u]=null,delete r._.invocationCallbacks[u],i.method.call(i.scope,{E:n}))}}),r.reconnecting(function(){r.transport&&r.transport.name==="webSockets"&&o(r,"Connection started reconnecting before invocation result was received.")}),r.disconnected(function(){o(r,"Connection was disconnected before invocation result was received.")})},r.fn._maximizeClientHubInvocation=function(n){return{Hub:n.H,Method:n.M,Args:n.A,State:n.S}},r.fn._registerSubscribedHubs=function(){var t=this;t._subscribedToHubs||(t._subscribedToHubs=!0,t.starting(function(){var i=[];n.each(t.proxies,function(n){this.hasSubscriptions()&&(i.push({name:n}),t.log("Client subscribed to hub '"+n+"'."))}),i.length===0&&t.log("No hubs have been subscribed to.  The client will not receive data from hubs.  To fix, declare at least one client side function prior to connection start for each hub you wish to subscribe to."),t.data=t.json.stringify(i)}))},r.fn.createHubProxy=function(n){n=n.toLowerCase();var t=this.proxies[n];return t||(t=u(this,n),this.proxies[n]=t),this._registerSubscribedHubs(),t},r.fn.init.prototype=r.fn,n.hubConnection=r}(window.jQuery,window),function(n){n.signalR.version="1.2.1"}(window.jQuery)