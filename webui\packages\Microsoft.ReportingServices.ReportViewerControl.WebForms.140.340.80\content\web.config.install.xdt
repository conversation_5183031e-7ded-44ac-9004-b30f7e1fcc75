﻿<?xml version="1.0"?>
<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">

  <system.web xdt:Transform="InsertIfMissing" />
  <system.web>
    <compilation xdt:Transform="InsertIfMissing" />
    <compilation>
      <buildProviders xdt:Transform="InsertIfMissing" />
      <buildProviders>
        <add extension=".rdlc" type="Microsoft.Reporting.RdlBuildProvider, Microsoft.ReportViewer.WebForms, Version=14.0.0.0, Culture=neutral, PublicKeyToken=89845DCD8080CC91" xdt:Locator="Match(extension)" xdt:Transform="InsertIfMissing" />
      </buildProviders>
      <assemblies xdt:Transform="InsertIfMissing" />
      <assemblies>
        <add assembly="Microsoft.ReportViewer.Common, Version=14.0.0.0, Culture=neutral, PublicKeyToken=89845DCD8080CC91" xdt:Locator="Condition(contains(@assembly, 'Microsoft.ReportViewer.Common'))" xdt:Transform="InsertIfMissing" />
        <add assembly="Microsoft.ReportViewer.WebForms, Version=14.0.0.0, Culture=neutral, PublicKeyToken=89845DCD8080CC91" xdt:Locator="Condition(contains(@assembly, 'Microsoft.ReportViewer.WebForms'))" xdt:Transform="InsertIfMissing" />
      </assemblies>
    </compilation>
    <httpHandlers xdt:Transform="InsertIfMissing" />
    <httpHandlers>
      <add path="Reserved.ReportViewerWebControl.axd" verb="*" type="Microsoft.Reporting.WebForms.HttpHandler, Microsoft.ReportViewer.WebForms, Version=14.0.0.0, Culture=neutral, PublicKeyToken=89845DCD8080CC91" validate="false" xdt:Locator="Match(path)" xdt:Transform="InsertIfMissing" />
    </httpHandlers>
  </system.web>

  <system.webServer xdt:Transform="InsertIfMissing" />
  <system.webServer>
    <validation validateIntegratedModeConfiguration="false" xdt:Transform="InsertIfMissing" />
    <modules runAllManagedModulesForAllRequests="true" xdt:Transform="InsertIfMissing" />
    <handlers xdt:Transform="InsertIfMissing" />
    <handlers>
      <add name="ReportViewerWebControlHandler" verb="*" path="Reserved.ReportViewerWebControl.axd" preCondition="integratedMode" type="Microsoft.Reporting.WebForms.HttpHandler, Microsoft.ReportViewer.WebForms, Version=14.0.0.0, Culture=neutral, PublicKeyToken=89845DCD8080CC91" xdt:Locator="Match(name)" xdt:Transform="InsertIfMissing" />
    </handlers>
  </system.webServer>

</configuration>