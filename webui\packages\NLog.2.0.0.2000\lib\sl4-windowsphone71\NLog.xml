<?xml version="1.0"?>
<doc>
    <assembly>
        <name>NLog</name>
    </assembly>
    <members>
        <member name="T:NLog.Common.AsyncContinuation">
            <summary>
            Asynchronous continuation delegate - function invoked at the end of asynchronous
            processing.
            </summary>
            <param name="exception">Exception during asynchronous processing or null if no exception
            was thrown.</param>
        </member>
        <member name="T:NLog.Common.AsyncHelpers">
            <summary>
            Helpers for asynchronous operations.
            </summary>
        </member>
        <member name="M:NLog.Common.AsyncHelpers.ForEachItemSequentially``1(System.Collections.Generic.IEnumerable{``0},NLog.Common.AsyncContinuation,NLog.Common.AsynchronousAction{``0})">
            <summary>
            Iterates over all items in the given collection and runs the specified action
            in sequence (each action executes only after the preceding one has completed without an error).
            </summary>
            <typeparam name="T">Type of each item.</typeparam>
            <param name="items">The items to iterate.</param>
            <param name="asyncContinuation">The asynchronous continuation to invoke once all items
            have been iterated.</param>
            <param name="action">The action to invoke for each item.</param>
        </member>
        <member name="M:NLog.Common.AsyncHelpers.Repeat(System.Int32,NLog.Common.AsyncContinuation,NLog.Common.AsynchronousAction)">
            <summary>
            Repeats the specified asynchronous action multiple times and invokes asynchronous continuation at the end.
            </summary>
            <param name="repeatCount">The repeat count.</param>
            <param name="asyncContinuation">The asynchronous continuation to invoke at the end.</param>
            <param name="action">The action to invoke.</param>
        </member>
        <member name="M:NLog.Common.AsyncHelpers.PrecededBy(NLog.Common.AsyncContinuation,NLog.Common.AsynchronousAction)">
            <summary>
            Modifies the continuation by pre-pending given action to execute just before it.
            </summary>
            <param name="asyncContinuation">The async continuation.</param>
            <param name="action">The action to pre-pend.</param>
            <returns>Continuation which will execute the given action before forwarding to the actual continuation.</returns>
        </member>
        <member name="M:NLog.Common.AsyncHelpers.WithTimeout(NLog.Common.AsyncContinuation,System.TimeSpan)">
            <summary>
            Attaches a timeout to a continuation which will invoke the continuation when the specified
            timeout has elapsed.
            </summary>
            <param name="asyncContinuation">The asynchronous continuation.</param>
            <param name="timeout">The timeout.</param>
            <returns>Wrapped continuation.</returns>
        </member>
        <member name="M:NLog.Common.AsyncHelpers.ForEachItemInParallel``1(System.Collections.Generic.IEnumerable{``0},NLog.Common.AsyncContinuation,NLog.Common.AsynchronousAction{``0})">
            <summary>
            Iterates over all items in the given collection and runs the specified action
            in parallel (each action executes on a thread from thread pool).
            </summary>
            <typeparam name="T">Type of each item.</typeparam>
            <param name="values">The items to iterate.</param>
            <param name="asyncContinuation">The asynchronous continuation to invoke once all items
            have been iterated.</param>
            <param name="action">The action to invoke for each item.</param>
        </member>
        <member name="M:NLog.Common.AsyncHelpers.RunSynchronously(NLog.Common.AsynchronousAction)">
            <summary>
            Runs the specified asynchronous action synchronously (blocks until the continuation has
            been invoked).
            </summary>
            <param name="action">The action.</param>
            <remarks>
            Using this method is not recommended because it will block the calling thread.
            </remarks>
        </member>
        <member name="M:NLog.Common.AsyncHelpers.PreventMultipleCalls(NLog.Common.AsyncContinuation)">
            <summary>
            Wraps the continuation with a guard which will only make sure that the continuation function
            is invoked only once.
            </summary>
            <param name="asyncContinuation">The asynchronous continuation.</param>
            <returns>Wrapped asynchronous continuation.</returns>
        </member>
        <member name="M:NLog.Common.AsyncHelpers.GetCombinedException(System.Collections.Generic.IList{System.Exception})">
            <summary>
            Gets the combined exception from all exceptions in the list.
            </summary>
            <param name="exceptions">The exceptions.</param>
            <returns>Combined exception or null if no exception was thrown.</returns>
        </member>
        <member name="T:NLog.Common.AsynchronousAction">
            <summary>
            Asynchronous action.
            </summary>
            <param name="asyncContinuation">Continuation to be invoked at the end of action.</param>
        </member>
        <member name="T:NLog.Common.AsynchronousAction`1">
            <summary>
            Asynchronous action with one argument.
            </summary>
            <typeparam name="T">Type of the argument.</typeparam>
            <param name="argument">Argument to the action.</param>
            <param name="asyncContinuation">Continuation to be invoked at the end of action.</param>
        </member>
        <member name="T:NLog.Common.AsyncLogEventInfo">
            <summary>
            Represents the logging event with asynchronous continuation.
            </summary>
        </member>
        <member name="M:NLog.Common.AsyncLogEventInfo.#ctor(NLog.LogEventInfo,NLog.Common.AsyncContinuation)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Common.AsyncLogEventInfo"/> struct.
            </summary>
            <param name="logEvent">The log event.</param>
            <param name="continuation">The continuation.</param>
        </member>
        <member name="M:NLog.Common.AsyncLogEventInfo.op_Equality(NLog.Common.AsyncLogEventInfo,NLog.Common.AsyncLogEventInfo)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="eventInfo1">The event info1.</param>
            <param name="eventInfo2">The event info2.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:NLog.Common.AsyncLogEventInfo.op_Inequality(NLog.Common.AsyncLogEventInfo,NLog.Common.AsyncLogEventInfo)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="eventInfo1">The event info1.</param>
            <param name="eventInfo2">The event info2.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:NLog.Common.AsyncLogEventInfo.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object"/> is equal to this instance.
            </summary>
            <param name="obj">The <see cref="T:System.Object"/> to compare with this instance.</param>
            <returns>
            A value of <c>true</c> if the specified <see cref="T:System.Object"/> is equal to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:NLog.Common.AsyncLogEventInfo.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
            <returns>
            A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table. 
            </returns>
        </member>
        <member name="P:NLog.Common.AsyncLogEventInfo.LogEvent">
            <summary>
            Gets the log event.
            </summary>
        </member>
        <member name="P:NLog.Common.AsyncLogEventInfo.Continuation">
            <summary>
            Gets the continuation.
            </summary>
        </member>
        <member name="T:NLog.Common.InternalLogger">
            <summary>
            NLog internal logger.
            </summary>
        </member>
        <member name="M:NLog.Common.InternalLogger.#cctor">
            <summary>
            Initializes static members of the InternalLogger class.
            </summary>
        </member>
        <member name="M:NLog.Common.InternalLogger.Log(NLog.LogLevel,System.String,System.Object[])">
            <summary>
            Logs the specified message at the specified level.
            </summary>
            <param name="level">Log level.</param>
            <param name="message">Message which may include positional parameters.</param>
            <param name="args">Arguments to the message.</param>
        </member>
        <member name="M:NLog.Common.InternalLogger.Log(NLog.LogLevel,System.String)">
            <summary>
            Logs the specified message at the specified level.
            </summary>
            <param name="level">Log level.</param>
            <param name="message">Log message.</param>
        </member>
        <member name="M:NLog.Common.InternalLogger.Trace(System.String,System.Object[])">
            <summary>
            Logs the specified message at the Trace level.
            </summary>
            <param name="message">Message which may include positional parameters.</param>
            <param name="args">Arguments to the message.</param>
        </member>
        <member name="M:NLog.Common.InternalLogger.Trace(System.String)">
            <summary>
            Logs the specified message at the Trace level.
            </summary>
            <param name="message">Log message.</param>
        </member>
        <member name="M:NLog.Common.InternalLogger.Debug(System.String,System.Object[])">
            <summary>
            Logs the specified message at the Debug level.
            </summary>
            <param name="message">Message which may include positional parameters.</param>
            <param name="args">Arguments to the message.</param>
        </member>
        <member name="M:NLog.Common.InternalLogger.Debug(System.String)">
            <summary>
            Logs the specified message at the Debug level.
            </summary>
            <param name="message">Log message.</param>
        </member>
        <member name="M:NLog.Common.InternalLogger.Info(System.String,System.Object[])">
            <summary>
            Logs the specified message at the Info level.
            </summary>
            <param name="message">Message which may include positional parameters.</param>
            <param name="args">Arguments to the message.</param>
        </member>
        <member name="M:NLog.Common.InternalLogger.Info(System.String)">
            <summary>
            Logs the specified message at the Info level.
            </summary>
            <param name="message">Log message.</param>
        </member>
        <member name="M:NLog.Common.InternalLogger.Warn(System.String,System.Object[])">
            <summary>
            Logs the specified message at the Warn level.
            </summary>
            <param name="message">Message which may include positional parameters.</param>
            <param name="args">Arguments to the message.</param>
        </member>
        <member name="M:NLog.Common.InternalLogger.Warn(System.String)">
            <summary>
            Logs the specified message at the Warn level.
            </summary>
            <param name="message">Log message.</param>
        </member>
        <member name="M:NLog.Common.InternalLogger.Error(System.String,System.Object[])">
            <summary>
            Logs the specified message at the Error level.
            </summary>
            <param name="message">Message which may include positional parameters.</param>
            <param name="args">Arguments to the message.</param>
        </member>
        <member name="M:NLog.Common.InternalLogger.Error(System.String)">
            <summary>
            Logs the specified message at the Error level.
            </summary>
            <param name="message">Log message.</param>
        </member>
        <member name="M:NLog.Common.InternalLogger.Fatal(System.String,System.Object[])">
            <summary>
            Logs the specified message at the Fatal level.
            </summary>
            <param name="message">Message which may include positional parameters.</param>
            <param name="args">Arguments to the message.</param>
        </member>
        <member name="M:NLog.Common.InternalLogger.Fatal(System.String)">
            <summary>
            Logs the specified message at the Fatal level.
            </summary>
            <param name="message">Log message.</param>
        </member>
        <member name="P:NLog.Common.InternalLogger.LogLevel">
            <summary>
            Gets or sets the internal log level.
            </summary>
        </member>
        <member name="P:NLog.Common.InternalLogger.LogToConsole">
            <summary>
            Gets or sets a value indicating whether internal messages should be written to the console output stream.
            </summary>
        </member>
        <member name="P:NLog.Common.InternalLogger.LogToConsoleError">
            <summary>
            Gets or sets a value indicating whether internal messages should be written to the console error stream.
            </summary>
        </member>
        <member name="P:NLog.Common.InternalLogger.LogFile">
            <summary>
            Gets or sets the name of the internal log file.
            </summary>
            <remarks>A value of <see langword="null" /> value disables internal logging to a file.</remarks>
        </member>
        <member name="P:NLog.Common.InternalLogger.LogWriter">
            <summary>
            Gets or sets the text writer that will receive internal logs.
            </summary>
        </member>
        <member name="P:NLog.Common.InternalLogger.IncludeTimestamp">
            <summary>
            Gets or sets a value indicating whether timestamp should be included in internal log output.
            </summary>
        </member>
        <member name="P:NLog.Common.InternalLogger.IsTraceEnabled">
            <summary>
            Gets a value indicating whether internal log includes Trace messages.
            </summary>
        </member>
        <member name="P:NLog.Common.InternalLogger.IsDebugEnabled">
            <summary>
            Gets a value indicating whether internal log includes Debug messages.
            </summary>
        </member>
        <member name="P:NLog.Common.InternalLogger.IsInfoEnabled">
            <summary>
            Gets a value indicating whether internal log includes Info messages.
            </summary>
        </member>
        <member name="P:NLog.Common.InternalLogger.IsWarnEnabled">
            <summary>
            Gets a value indicating whether internal log includes Warn messages.
            </summary>
        </member>
        <member name="P:NLog.Common.InternalLogger.IsErrorEnabled">
            <summary>
            Gets a value indicating whether internal log includes Error messages.
            </summary>
        </member>
        <member name="P:NLog.Common.InternalLogger.IsFatalEnabled">
            <summary>
            Gets a value indicating whether internal log includes Fatal messages.
            </summary>
        </member>
        <member name="T:NLog.Common.LogEventInfoBuffer">
            <summary>
            A cyclic buffer of <see cref="T:NLog.LogEventInfo"/> object.
            </summary>
        </member>
        <member name="M:NLog.Common.LogEventInfoBuffer.#ctor(System.Int32,System.Boolean,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Common.LogEventInfoBuffer"/> class.
            </summary>
            <param name="size">Buffer size.</param>
            <param name="growAsNeeded">Whether buffer should grow as it becomes full.</param>
            <param name="growLimit">The maximum number of items that the buffer can grow to.</param>
        </member>
        <member name="M:NLog.Common.LogEventInfoBuffer.Append(NLog.Common.AsyncLogEventInfo)">
            <summary>
            Adds the specified log event to the buffer.
            </summary>
            <param name="eventInfo">Log event.</param>
            <returns>The number of items in the buffer.</returns>
        </member>
        <member name="M:NLog.Common.LogEventInfoBuffer.GetEventsAndClear">
            <summary>
            Gets the array of events accumulated in the buffer and clears the buffer as one atomic operation.
            </summary>
            <returns>Events in the buffer.</returns>
        </member>
        <member name="P:NLog.Common.LogEventInfoBuffer.Size">
            <summary>
            Gets the number of items in the array.
            </summary>
        </member>
        <member name="T:NLog.Conditions.ConditionAndExpression">
            <summary>
            Condition <b>and</b> expression.
            </summary>
        </member>
        <member name="T:NLog.Conditions.ConditionExpression">
            <summary>
            Base class for representing nodes in condition expression trees.
            </summary>
        </member>
        <member name="M:NLog.Conditions.ConditionExpression.op_Implicit(System.String)~NLog.Conditions.ConditionExpression">
            <summary>
            Converts condition text to a condition expression tree.
            </summary>
            <param name="conditionExpressionText">Condition text to be converted.</param>
            <returns>Condition expression tree.</returns>
        </member>
        <member name="M:NLog.Conditions.ConditionExpression.Evaluate(NLog.LogEventInfo)">
            <summary>
            Evaluates the expression.
            </summary>
            <param name="context">Evaluation context.</param>
            <returns>Expression result.</returns>
        </member>
        <member name="M:NLog.Conditions.ConditionExpression.ToString">
            <summary>
            Returns a string representation of the expression.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents the condition expression.
            </returns>
        </member>
        <member name="M:NLog.Conditions.ConditionExpression.EvaluateNode(NLog.LogEventInfo)">
            <summary>
            Evaluates the expression.
            </summary>
            <param name="context">Evaluation context.</param>
            <returns>Expression result.</returns>
        </member>
        <member name="M:NLog.Conditions.ConditionAndExpression.#ctor(NLog.Conditions.ConditionExpression,NLog.Conditions.ConditionExpression)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Conditions.ConditionAndExpression"/> class.
            </summary>
            <param name="left">Left hand side of the AND expression.</param>
            <param name="right">Right hand side of the AND expression.</param>
        </member>
        <member name="M:NLog.Conditions.ConditionAndExpression.ToString">
            <summary>
            Returns a string representation of this expression.
            </summary>
            <returns>A concatenated '(Left) and (Right)' string.</returns>
        </member>
        <member name="M:NLog.Conditions.ConditionAndExpression.EvaluateNode(NLog.LogEventInfo)">
            <summary>
            Evaluates the expression by evaluating <see cref="P:NLog.Conditions.ConditionAndExpression.Left"/> and <see cref="P:NLog.Conditions.ConditionAndExpression.Right"/> recursively.
            </summary>
            <param name="context">Evaluation context.</param>
            <returns>The value of the conjunction operator.</returns>
        </member>
        <member name="P:NLog.Conditions.ConditionAndExpression.Left">
            <summary>
            Gets the left hand side of the AND expression.
            </summary>
        </member>
        <member name="P:NLog.Conditions.ConditionAndExpression.Right">
            <summary>
            Gets the right hand side of the AND expression.
            </summary>
        </member>
        <member name="T:NLog.Conditions.ConditionEvaluationException">
            <summary>
            Exception during evaluation of condition expression.
            </summary>
        </member>
        <member name="M:NLog.Conditions.ConditionEvaluationException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Conditions.ConditionEvaluationException"/> class.
            </summary>
        </member>
        <member name="M:NLog.Conditions.ConditionEvaluationException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Conditions.ConditionEvaluationException"/> class.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:NLog.Conditions.ConditionEvaluationException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Conditions.ConditionEvaluationException"/> class.
            </summary>
            <param name="message">The message.</param>
            <param name="innerException">The inner exception.</param>
        </member>
        <member name="T:NLog.Conditions.ConditionLayoutExpression">
            <summary>
            Condition layout expression (represented by a string literal
            with embedded ${}).
            </summary>
        </member>
        <member name="M:NLog.Conditions.ConditionLayoutExpression.#ctor(NLog.Layouts.Layout)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Conditions.ConditionLayoutExpression"/> class.
            </summary>
            <param name="layout">The layout.</param>
        </member>
        <member name="M:NLog.Conditions.ConditionLayoutExpression.ToString">
            <summary>
            Returns a string representation of this expression.
            </summary>
            <returns>String literal in single quotes.</returns>
        </member>
        <member name="M:NLog.Conditions.ConditionLayoutExpression.EvaluateNode(NLog.LogEventInfo)">
            <summary>
            Evaluates the expression by calculating the value
            of the layout in the specified evaluation context.
            </summary>
            <param name="context">Evaluation context.</param>
            <returns>The value of the layout.</returns>
        </member>
        <member name="P:NLog.Conditions.ConditionLayoutExpression.Layout">
            <summary>
            Gets the layout.
            </summary>
            <value>The layout.</value>
        </member>
        <member name="T:NLog.Conditions.ConditionLevelExpression">
            <summary>
            Condition level expression (represented by the <b>level</b> keyword).
            </summary>
        </member>
        <member name="M:NLog.Conditions.ConditionLevelExpression.ToString">
            <summary>
            Returns a string representation of the expression.
            </summary>
            <returns>The '<b>level</b>' string.</returns>
        </member>
        <member name="M:NLog.Conditions.ConditionLevelExpression.EvaluateNode(NLog.LogEventInfo)">
            <summary>
            Evaluates to the current log level.
            </summary>
            <param name="context">Evaluation context. Ignored.</param>
            <returns>The <see cref="T:NLog.LogLevel"/> object representing current log level.</returns>
        </member>
        <member name="T:NLog.Conditions.ConditionLiteralExpression">
            <summary>
            Condition literal expression (numeric, <b>LogLevel.XXX</b>, <b>true</b> or <b>false</b>).
            </summary>
        </member>
        <member name="M:NLog.Conditions.ConditionLiteralExpression.#ctor(System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Conditions.ConditionLiteralExpression"/> class.
            </summary>
            <param name="literalValue">Literal value.</param>
        </member>
        <member name="M:NLog.Conditions.ConditionLiteralExpression.ToString">
            <summary>
            Returns a string representation of the expression.
            </summary>
            <returns>The literal value.</returns>
        </member>
        <member name="M:NLog.Conditions.ConditionLiteralExpression.EvaluateNode(NLog.LogEventInfo)">
            <summary>
            Evaluates the expression.
            </summary>
            <param name="context">Evaluation context.</param>
            <returns>The literal value as passed in the constructor.</returns>
        </member>
        <member name="P:NLog.Conditions.ConditionLiteralExpression.LiteralValue">
            <summary>
            Gets the literal value.
            </summary>
            <value>The literal value.</value>
        </member>
        <member name="T:NLog.Conditions.ConditionLoggerNameExpression">
            <summary>
            Condition logger name expression (represented by the <b>logger</b> keyword).
            </summary>
        </member>
        <member name="M:NLog.Conditions.ConditionLoggerNameExpression.ToString">
            <summary>
            Returns a string representation of this expression.
            </summary>
            <returns>A <b>logger</b> string.</returns>
        </member>
        <member name="M:NLog.Conditions.ConditionLoggerNameExpression.EvaluateNode(NLog.LogEventInfo)">
            <summary>
            Evaluates to the logger name.
            </summary>
            <param name="context">Evaluation context.</param>
            <returns>The logger name.</returns>
        </member>
        <member name="T:NLog.Conditions.ConditionMessageExpression">
            <summary>
            Condition message expression (represented by the <b>message</b> keyword).
            </summary>
        </member>
        <member name="M:NLog.Conditions.ConditionMessageExpression.ToString">
            <summary>
            Returns a string representation of this expression.
            </summary>
            <returns>The '<b>message</b>' string.</returns>
        </member>
        <member name="M:NLog.Conditions.ConditionMessageExpression.EvaluateNode(NLog.LogEventInfo)">
            <summary>
            Evaluates to the logger message.
            </summary>
            <param name="context">Evaluation context.</param>
            <returns>The logger message.</returns>
        </member>
        <member name="T:NLog.Conditions.ConditionMethodAttribute">
            <summary>
            Marks class as a log event Condition and assigns a name to it.
            </summary>
        </member>
        <member name="T:NLog.Config.NameBaseAttribute">
            <summary>
            Attaches a simple name to an item (such as <see cref="T:NLog.Targets.Target"/>, 
            <see cref="T:NLog.LayoutRenderers.LayoutRenderer"/>, <see cref="T:NLog.Layouts.Layout"/>, etc.).
            </summary>
        </member>
        <member name="M:NLog.Config.NameBaseAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Config.NameBaseAttribute"/> class.
            </summary>
            <param name="name">The name of the item.</param>
        </member>
        <member name="P:NLog.Config.NameBaseAttribute.Name">
            <summary>
            Gets the name of the item.
            </summary>
            <value>The name of the item.</value>
        </member>
        <member name="M:NLog.Conditions.ConditionMethodAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Conditions.ConditionMethodAttribute"/> class.
            </summary>
            <param name="name">Condition method name.</param>
        </member>
        <member name="T:NLog.Conditions.ConditionMethodExpression">
            <summary>
            Condition method invocation expression (represented by <b>method(p1,p2,p3)</b> syntax).
            </summary>
        </member>
        <member name="M:NLog.Conditions.ConditionMethodExpression.#ctor(System.String,System.Reflection.MethodInfo,System.Collections.Generic.IEnumerable{NLog.Conditions.ConditionExpression})">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Conditions.ConditionMethodExpression"/> class.
            </summary>
            <param name="conditionMethodName">Name of the condition method.</param>
            <param name="methodInfo"><see cref="P:NLog.Conditions.ConditionMethodExpression.MethodInfo"/> of the condition method.</param>
            <param name="methodParameters">The method parameters.</param>
        </member>
        <member name="M:NLog.Conditions.ConditionMethodExpression.ToString">
            <summary>
            Returns a string representation of the expression.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents the condition expression.
            </returns>
        </member>
        <member name="M:NLog.Conditions.ConditionMethodExpression.EvaluateNode(NLog.LogEventInfo)">
            <summary>
            Evaluates the expression.
            </summary>
            <param name="context">Evaluation context.</param>
            <returns>Expression result.</returns>
        </member>
        <member name="P:NLog.Conditions.ConditionMethodExpression.MethodInfo">
            <summary>
            Gets the method info.
            </summary>
        </member>
        <member name="P:NLog.Conditions.ConditionMethodExpression.MethodParameters">
            <summary>
            Gets the method parameters.
            </summary>
            <value>The method parameters.</value>
        </member>
        <member name="T:NLog.Conditions.ConditionMethods">
            <summary>
            A bunch of utility methods (mostly predicates) which can be used in
            condition expressions. Parially inspired by XPath 1.0.
            </summary>
        </member>
        <member name="M:NLog.Conditions.ConditionMethods.Equals2(System.Object,System.Object)">
            <summary>
            Compares two values for equality.
            </summary>
            <param name="firstValue">The first value.</param>
            <param name="secondValue">The second value.</param>
            <returns><b>true</b> when two objects are equal, <b>false</b> otherwise.</returns>
        </member>
        <member name="M:NLog.Conditions.ConditionMethods.Contains(System.String,System.String)">
            <summary>
            Gets or sets a value indicating whether the second string is a substring of the first one.
            </summary>
            <param name="haystack">The first string.</param>
            <param name="needle">The second string.</param>
            <returns><b>true</b> when the second string is a substring of the first string, <b>false</b> otherwise.</returns>
        </member>
        <member name="M:NLog.Conditions.ConditionMethods.StartsWith(System.String,System.String)">
            <summary>
            Gets or sets a value indicating whether the second string is a prefix of the first one.
            </summary>
            <param name="haystack">The first string.</param>
            <param name="needle">The second string.</param>
            <returns><b>true</b> when the second string is a prefix of the first string, <b>false</b> otherwise.</returns>
        </member>
        <member name="M:NLog.Conditions.ConditionMethods.EndsWith(System.String,System.String)">
            <summary>
            Gets or sets a value indicating whether the second string is a suffix of the first one.
            </summary>
            <param name="haystack">The first string.</param>
            <param name="needle">The second string.</param>
            <returns><b>true</b> when the second string is a prefix of the first string, <b>false</b> otherwise.</returns>
        </member>
        <member name="M:NLog.Conditions.ConditionMethods.Length(System.String)">
            <summary>
            Returns the length of a string.
            </summary>
            <param name="text">A string whose lengths is to be evaluated.</param>
            <returns>The length of the string.</returns>
        </member>
        <member name="T:NLog.Conditions.ConditionMethodsAttribute">
            <summary>
            Marks the class as containing condition methods.
            </summary>
        </member>
        <member name="T:NLog.Conditions.ConditionNotExpression">
            <summary>
            Condition <b>not</b> expression.
            </summary>
        </member>
        <member name="M:NLog.Conditions.ConditionNotExpression.#ctor(NLog.Conditions.ConditionExpression)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Conditions.ConditionNotExpression"/> class.
            </summary>
            <param name="expression">The expression.</param>
        </member>
        <member name="M:NLog.Conditions.ConditionNotExpression.ToString">
            <summary>
            Returns a string representation of the expression.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents the condition expression.
            </returns>
        </member>
        <member name="M:NLog.Conditions.ConditionNotExpression.EvaluateNode(NLog.LogEventInfo)">
            <summary>
            Evaluates the expression.
            </summary>
            <param name="context">Evaluation context.</param>
            <returns>Expression result.</returns>
        </member>
        <member name="P:NLog.Conditions.ConditionNotExpression.Expression">
            <summary>
            Gets the expression to be negated.
            </summary>
            <value>The expression.</value>
        </member>
        <member name="T:NLog.Conditions.ConditionOrExpression">
            <summary>
            Condition <b>or</b> expression.
            </summary>
        </member>
        <member name="M:NLog.Conditions.ConditionOrExpression.#ctor(NLog.Conditions.ConditionExpression,NLog.Conditions.ConditionExpression)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Conditions.ConditionOrExpression"/> class.
            </summary>
            <param name="left">Left hand side of the OR expression.</param>
            <param name="right">Right hand side of the OR expression.</param>
        </member>
        <member name="M:NLog.Conditions.ConditionOrExpression.ToString">
            <summary>
            Returns a string representation of the expression.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents the condition expression.
            </returns>
        </member>
        <member name="M:NLog.Conditions.ConditionOrExpression.EvaluateNode(NLog.LogEventInfo)">
            <summary>
            Evaluates the expression by evaluating <see cref="P:NLog.Conditions.ConditionOrExpression.LeftExpression"/> and <see cref="P:NLog.Conditions.ConditionOrExpression.RightExpression"/> recursively.
            </summary>
            <param name="context">Evaluation context.</param>
            <returns>The value of the alternative operator.</returns>
        </member>
        <member name="P:NLog.Conditions.ConditionOrExpression.LeftExpression">
            <summary>
            Gets the left expression.
            </summary>
            <value>The left expression.</value>
        </member>
        <member name="P:NLog.Conditions.ConditionOrExpression.RightExpression">
            <summary>
            Gets the right expression.
            </summary>
            <value>The right expression.</value>
        </member>
        <member name="T:NLog.Conditions.ConditionParseException">
            <summary>
            Exception during parsing of condition expression.
            </summary>
        </member>
        <member name="M:NLog.Conditions.ConditionParseException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Conditions.ConditionParseException"/> class.
            </summary>
        </member>
        <member name="M:NLog.Conditions.ConditionParseException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Conditions.ConditionParseException"/> class.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:NLog.Conditions.ConditionParseException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Conditions.ConditionParseException"/> class.
            </summary>
            <param name="message">The message.</param>
            <param name="innerException">The inner exception.</param>
        </member>
        <member name="T:NLog.Conditions.ConditionParser">
            <summary>
            Condition parser. Turns a string representation of condition expression
            into an expression tree.
            </summary>
        </member>
        <member name="M:NLog.Conditions.ConditionParser.#ctor(NLog.Internal.SimpleStringReader,NLog.Config.ConfigurationItemFactory)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Conditions.ConditionParser"/> class.
            </summary>
            <param name="stringReader">The string reader.</param>
            <param name="configurationItemFactory">Instance of <see cref="T:NLog.Config.ConfigurationItemFactory"/> used to resolve references to condition methods and layout renderers.</param>
        </member>
        <member name="M:NLog.Conditions.ConditionParser.ParseExpression(System.String)">
            <summary>
            Parses the specified condition string and turns it into
            <see cref="T:NLog.Conditions.ConditionExpression"/> tree.
            </summary>
            <param name="expressionText">The expression to be parsed.</param>
            <returns>The root of the expression syntax tree which can be used to get the value of the condition in a specified context.</returns>
        </member>
        <member name="M:NLog.Conditions.ConditionParser.ParseExpression(System.String,NLog.Config.ConfigurationItemFactory)">
            <summary>
            Parses the specified condition string and turns it into
            <see cref="T:NLog.Conditions.ConditionExpression"/> tree.
            </summary>
            <param name="expressionText">The expression to be parsed.</param>
            <param name="configurationItemFactories">Instance of <see cref="T:NLog.Config.ConfigurationItemFactory"/> used to resolve references to condition methods and layout renderers.</param>
            <returns>The root of the expression syntax tree which can be used to get the value of the condition in a specified context.</returns>
        </member>
        <member name="M:NLog.Conditions.ConditionParser.ParseExpression(NLog.Internal.SimpleStringReader,NLog.Config.ConfigurationItemFactory)">
            <summary>
            Parses the specified condition string and turns it into
            <see cref="T:NLog.Conditions.ConditionExpression"/> tree.
            </summary>
            <param name="stringReader">The string reader.</param>
            <param name="configurationItemFactories">Instance of <see cref="T:NLog.Config.ConfigurationItemFactory"/> used to resolve references to condition methods and layout renderers.</param>
            <returns>
            The root of the expression syntax tree which can be used to get the value of the condition in a specified context.
            </returns>
        </member>
        <member name="T:NLog.Conditions.ConditionRelationalExpression">
            <summary>
            Condition relational (<b>==</b>, <b>!=</b>, <b>&lt;</b>, <b>&lt;=</b>,
            <b>&gt;</b> or <b>&gt;=</b>) expression.
            </summary>
        </member>
        <member name="M:NLog.Conditions.ConditionRelationalExpression.#ctor(NLog.Conditions.ConditionExpression,NLog.Conditions.ConditionExpression,NLog.Conditions.ConditionRelationalOperator)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Conditions.ConditionRelationalExpression"/> class.
            </summary>
            <param name="leftExpression">The left expression.</param>
            <param name="rightExpression">The right expression.</param>
            <param name="relationalOperator">The relational operator.</param>
        </member>
        <member name="M:NLog.Conditions.ConditionRelationalExpression.ToString">
            <summary>
            Returns a string representation of the expression.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents the condition expression.
            </returns>
        </member>
        <member name="M:NLog.Conditions.ConditionRelationalExpression.EvaluateNode(NLog.LogEventInfo)">
            <summary>
            Evaluates the expression.
            </summary>
            <param name="context">Evaluation context.</param>
            <returns>Expression result.</returns>
        </member>
        <member name="M:NLog.Conditions.ConditionRelationalExpression.Compare(System.Object,System.Object,NLog.Conditions.ConditionRelationalOperator)">
            <summary>
            Compares the specified values using specified relational operator.
            </summary>
            <param name="leftValue">The first value.</param>
            <param name="rightValue">The second value.</param>
            <param name="relationalOperator">The relational operator.</param>
            <returns>Result of the given relational operator.</returns>
        </member>
        <member name="P:NLog.Conditions.ConditionRelationalExpression.LeftExpression">
            <summary>
            Gets the left expression.
            </summary>
            <value>The left expression.</value>
        </member>
        <member name="P:NLog.Conditions.ConditionRelationalExpression.RightExpression">
            <summary>
            Gets the right expression.
            </summary>
            <value>The right expression.</value>
        </member>
        <member name="P:NLog.Conditions.ConditionRelationalExpression.RelationalOperator">
            <summary>
            Gets the relational operator.
            </summary>
            <value>The operator.</value>
        </member>
        <member name="T:NLog.Conditions.ConditionRelationalOperator">
            <summary>
            Relational operators used in conditions.
            </summary>
        </member>
        <member name="F:NLog.Conditions.ConditionRelationalOperator.Equal">
            <summary>
            Equality (==).
            </summary>
        </member>
        <member name="F:NLog.Conditions.ConditionRelationalOperator.NotEqual">
            <summary>
            Inequality (!=).
            </summary>
        </member>
        <member name="F:NLog.Conditions.ConditionRelationalOperator.Less">
            <summary>
            Less than (&lt;).
            </summary>
        </member>
        <member name="F:NLog.Conditions.ConditionRelationalOperator.Greater">
            <summary>
            Greater than (&gt;).
            </summary>
        </member>
        <member name="F:NLog.Conditions.ConditionRelationalOperator.LessOrEqual">
            <summary>
            Less than or equal (&lt;=).
            </summary>
        </member>
        <member name="F:NLog.Conditions.ConditionRelationalOperator.GreaterOrEqual">
            <summary>
            Greater than or equal (&gt;=).
            </summary>
        </member>
        <member name="T:NLog.Conditions.ConditionTokenizer">
            <summary>
            Hand-written tokenizer for conditions.
            </summary>
        </member>
        <member name="M:NLog.Conditions.ConditionTokenizer.#ctor(NLog.Internal.SimpleStringReader)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Conditions.ConditionTokenizer"/> class.
            </summary>
            <param name="stringReader">The string reader.</param>
        </member>
        <member name="M:NLog.Conditions.ConditionTokenizer.Expect(NLog.Conditions.ConditionTokenType)">
            <summary>
            Asserts current token type and advances to the next token.
            </summary>
            <param name="tokenType">Expected token type.</param>
            <remarks>If token type doesn't match, an exception is thrown.</remarks>
        </member>
        <member name="M:NLog.Conditions.ConditionTokenizer.EatKeyword">
            <summary>
            Asserts that current token is a keyword and returns its value and advances to the next token.
            </summary>
            <returns>Keyword value.</returns>
        </member>
        <member name="M:NLog.Conditions.ConditionTokenizer.IsKeyword(System.String)">
            <summary>
            Gets or sets a value indicating whether current keyword is equal to the specified value.
            </summary>
            <param name="keyword">The keyword.</param>
            <returns>
            A value of <c>true</c> if current keyword is equal to the specified value; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:NLog.Conditions.ConditionTokenizer.IsEOF">
            <summary>
            Gets or sets a value indicating whether the tokenizer has reached the end of the token stream.
            </summary>
            <returns>
            A value of <c>true</c> if the tokenizer has reached the end of the token stream; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:NLog.Conditions.ConditionTokenizer.IsNumber">
            <summary>
            Gets or sets a value indicating whether current token is a number.
            </summary>
            <returns>
            A value of <c>true</c> if current token is a number; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:NLog.Conditions.ConditionTokenizer.IsToken(NLog.Conditions.ConditionTokenType)">
            <summary>
            Gets or sets a value indicating whether the specified token is of specified type.
            </summary>
            <param name="tokenType">The token type.</param>
            <returns>
            A value of <c>true</c> if current token is of specified type; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:NLog.Conditions.ConditionTokenizer.GetNextToken">
            <summary>
            Gets the next token and sets <see cref="P:NLog.Conditions.ConditionTokenizer.TokenType"/> and <see cref="P:NLog.Conditions.ConditionTokenizer.TokenValue"/> properties.
            </summary>
        </member>
        <member name="P:NLog.Conditions.ConditionTokenizer.TokenPosition">
            <summary>
            Gets the token position.
            </summary>
            <value>The token position.</value>
        </member>
        <member name="P:NLog.Conditions.ConditionTokenizer.TokenType">
            <summary>
            Gets the type of the token.
            </summary>
            <value>The type of the token.</value>
        </member>
        <member name="P:NLog.Conditions.ConditionTokenizer.TokenValue">
            <summary>
            Gets the token value.
            </summary>
            <value>The token value.</value>
        </member>
        <member name="P:NLog.Conditions.ConditionTokenizer.StringTokenValue">
            <summary>
            Gets the value of a string token.
            </summary>
            <value>The string token value.</value>
        </member>
        <member name="T:NLog.Conditions.ConditionTokenizer.CharToTokenType">
            <summary>
            Mapping between characters and token types for punctuations.
            </summary>
        </member>
        <member name="M:NLog.Conditions.ConditionTokenizer.CharToTokenType.#ctor(System.Char,NLog.Conditions.ConditionTokenType)">
            <summary>
            Initializes a new instance of the CharToTokenType struct.
            </summary>
            <param name="character">The character.</param>
            <param name="tokenType">Type of the token.</param>
        </member>
        <member name="T:NLog.Conditions.ConditionTokenType">
            <summary>
            Token types for condition expressions.
            </summary>
        </member>
        <member name="T:NLog.Config.AdvancedAttribute">
            <summary>
            Marks the class or a member as advanced. Advanced classes and members are hidden by 
            default in generated documentation.
            </summary>
        </member>
        <member name="M:NLog.Config.AdvancedAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Config.AdvancedAttribute"/> class.
            </summary>
        </member>
        <member name="T:NLog.Config.AppDomainFixedOutputAttribute">
            <summary>
            Identifies that the output of layout or layout render does not change for the lifetime of the current appdomain.
            </summary>
        </member>
        <member name="T:NLog.Config.ArrayParameterAttribute">
            <summary>
            Used to mark configurable parameters which are arrays. 
            Specifies the mapping between XML elements and .NET types.
            </summary>
        </member>
        <member name="M:NLog.Config.ArrayParameterAttribute.#ctor(System.Type,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Config.ArrayParameterAttribute"/> class.
            </summary>
            <param name="itemType">The type of the array item.</param>
            <param name="elementName">The XML element name that represents the item.</param>
        </member>
        <member name="P:NLog.Config.ArrayParameterAttribute.ItemType">
            <summary>
            Gets the .NET type of the array item.
            </summary>
        </member>
        <member name="P:NLog.Config.ArrayParameterAttribute.ElementName">
            <summary>
            Gets the XML element name.
            </summary>
        </member>
        <member name="T:NLog.Config.ConfigurationItemCreator">
            <summary>
            Constructs a new instance the configuration item (target, layout, layout renderer, etc.) given its type.
            </summary>
            <param name="itemType">Type of the item.</param>
            <returns>Created object of the specified type.</returns>
        </member>
        <member name="T:NLog.Config.ConfigurationItemFactory">
            <summary>
            Provides registration information for named items (targets, layouts, layout renderers, etc.) managed by NLog.
            </summary>
        </member>
        <member name="M:NLog.Config.ConfigurationItemFactory.#cctor">
            <summary>
            Initializes static members of the <see cref="T:NLog.Config.ConfigurationItemFactory"/> class.
            </summary>
        </member>
        <member name="M:NLog.Config.ConfigurationItemFactory.#ctor(System.Reflection.Assembly[])">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Config.ConfigurationItemFactory"/> class.
            </summary>
            <param name="assemblies">The assemblies to scan for named items.</param>
        </member>
        <member name="M:NLog.Config.ConfigurationItemFactory.RegisterItemsFromAssembly(System.Reflection.Assembly)">
            <summary>
            Registers named items from the assembly.
            </summary>
            <param name="assembly">The assembly.</param>
        </member>
        <member name="M:NLog.Config.ConfigurationItemFactory.RegisterItemsFromAssembly(System.Reflection.Assembly,System.String)">
            <summary>
            Registers named items from the assembly.
            </summary>
            <param name="assembly">The assembly.</param>
            <param name="itemNamePrefix">Item name prefix.</param>
        </member>
        <member name="M:NLog.Config.ConfigurationItemFactory.Clear">
            <summary>
            Clears the contents of all factories.
            </summary>
        </member>
        <member name="M:NLog.Config.ConfigurationItemFactory.RegisterType(System.Type,System.String)">
            <summary>
            Registers the type.
            </summary>
            <param name="type">The type to register.</param>
            <param name="itemNamePrefix">The item name prefix.</param>
        </member>
        <member name="M:NLog.Config.ConfigurationItemFactory.BuildDefaultFactory">
            <summary>
            Builds the default configuration item factory.
            </summary>
            <returns>Default factory.</returns>
        </member>
        <member name="M:NLog.Config.ConfigurationItemFactory.RegisterExtendedItems">
            <summary>
            Registers items in NLog.Extended.dll using late-bound types, so that we don't need a reference to NLog.Extended.dll.
            </summary>
        </member>
        <member name="P:NLog.Config.ConfigurationItemFactory.Default">
            <summary>
            Gets or sets default singleton instance of <see cref="T:NLog.Config.ConfigurationItemFactory"/>.
            </summary>
        </member>
        <member name="P:NLog.Config.ConfigurationItemFactory.CreateInstance">
            <summary>
            Gets or sets the creator delegate used to instantiate configuration objects.
            </summary>
            <remarks>
            By overriding this property, one can enable dependency injection or interception for created objects.
            </remarks>
        </member>
        <member name="P:NLog.Config.ConfigurationItemFactory.Targets">
            <summary>
            Gets the <see cref="T:NLog.Targets.Target"/> factory.
            </summary>
            <value>The target factory.</value>
        </member>
        <member name="P:NLog.Config.ConfigurationItemFactory.Filters">
            <summary>
            Gets the <see cref="T:NLog.Filters.Filter"/> factory.
            </summary>
            <value>The filter factory.</value>
        </member>
        <member name="P:NLog.Config.ConfigurationItemFactory.LayoutRenderers">
            <summary>
            Gets the <see cref="T:NLog.LayoutRenderers.LayoutRenderer"/> factory.
            </summary>
            <value>The layout renderer factory.</value>
        </member>
        <member name="P:NLog.Config.ConfigurationItemFactory.Layouts">
            <summary>
            Gets the <see cref="T:NLog.LayoutRenderers.LayoutRenderer"/> factory.
            </summary>
            <value>The layout factory.</value>
        </member>
        <member name="P:NLog.Config.ConfigurationItemFactory.AmbientProperties">
            <summary>
            Gets the ambient property factory.
            </summary>
            <value>The ambient property factory.</value>
        </member>
        <member name="P:NLog.Config.ConfigurationItemFactory.ConditionMethods">
            <summary>
            Gets the condition method factory.
            </summary>
            <value>The condition method factory.</value>
        </member>
        <member name="T:NLog.Config.DefaultParameterAttribute">
            <summary>
            Attribute used to mark the default parameters for layout renderers.
            </summary>
        </member>
        <member name="M:NLog.Config.DefaultParameterAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Config.DefaultParameterAttribute"/> class.
            </summary>
        </member>
        <member name="T:NLog.Config.Factory`2">
            <summary>
            Factory for class-based items.
            </summary>
            <typeparam name="TBaseType">The base type of each item.</typeparam>
            <typeparam name="TAttributeType">The type of the attribute used to annotate itemss.</typeparam>
        </member>
        <member name="T:NLog.Config.INamedItemFactory`2">
            <summary>
            Represents a factory of named items (such as targets, layouts, layout renderers, etc.).
            </summary>
            <typeparam name="TInstanceType">Base type for each item instance.</typeparam>
            <typeparam name="TDefinitionType">Item definition type (typically <see cref="T:System.Type"/> or <see cref="T:System.Reflection.MethodInfo"/>).</typeparam>
        </member>
        <member name="M:NLog.Config.INamedItemFactory`2.RegisterDefinition(System.String,`1)">
            <summary>
            Registers new item definition.
            </summary>
            <param name="itemName">Name of the item.</param>
            <param name="itemDefinition">Item definition.</param>
        </member>
        <member name="M:NLog.Config.INamedItemFactory`2.TryGetDefinition(System.String,`1@)">
            <summary>
            Tries to get registed item definition.
            </summary>
            <param name="itemName">Name of the item.</param>
            <param name="result">Reference to a variable which will store the item definition.</param>
            <returns>Item definition.</returns>
        </member>
        <member name="M:NLog.Config.INamedItemFactory`2.CreateInstance(System.String)">
            <summary>
            Creates item instance.
            </summary>
            <param name="itemName">Name of the item.</param>
            <returns>Newly created item instance.</returns>
        </member>
        <member name="M:NLog.Config.INamedItemFactory`2.TryCreateInstance(System.String,`0@)">
            <summary>
            Tries to create an item instance.
            </summary>
            <param name="itemName">Name of the item.</param>
            <param name="result">The result.</param>
            <returns>True if instance was created successfully, false otherwise.</returns>
        </member>
        <member name="T:NLog.Config.IFactory">
            <summary>
            Provides means to populate factories of named items (such as targets, layouts, layout renderers, etc.).
            </summary>
        </member>
        <member name="M:NLog.Config.Factory`2.ScanAssembly(System.Reflection.Assembly,System.String)">
            <summary>
            Scans the assembly.
            </summary>
            <param name="theAssembly">The assembly.</param>
            <param name="prefix">The prefix.</param>
        </member>
        <member name="M:NLog.Config.Factory`2.RegisterType(System.Type,System.String)">
            <summary>
            Registers the type.
            </summary>
            <param name="type">The type to register.</param>
            <param name="itemNamePrefix">The item name prefix.</param>
        </member>
        <member name="M:NLog.Config.Factory`2.RegisterNamedType(System.String,System.String)">
            <summary>
            Registers the item based on a type name.
            </summary>
            <param name="itemName">Name of the item.</param>
            <param name="typeName">Name of the type.</param>
        </member>
        <member name="M:NLog.Config.Factory`2.Clear">
            <summary>
            Clears the contents of the factory.
            </summary>
        </member>
        <member name="M:NLog.Config.Factory`2.RegisterDefinition(System.String,System.Type)">
            <summary>
            Registers a single type definition.
            </summary>
            <param name="name">The item name.</param>
            <param name="type">The type of the item.</param>
        </member>
        <member name="M:NLog.Config.Factory`2.TryGetDefinition(System.String,System.Type@)">
            <summary>
            Tries to get registed item definition.
            </summary>
            <param name="itemName">Name of the item.</param>
            <param name="result">Reference to a variable which will store the item definition.</param>
            <returns>Item definition.</returns>
        </member>
        <member name="M:NLog.Config.Factory`2.TryCreateInstance(System.String,`0@)">
            <summary>
            Tries to create an item instance.
            </summary>
            <param name="itemName">Name of the item.</param>
            <param name="result">The result.</param>
            <returns>True if instance was created successfully, false otherwise.</returns>
        </member>
        <member name="M:NLog.Config.Factory`2.CreateInstance(System.String)">
            <summary>
            Creates an item instance.
            </summary>
            <param name="name">The name of the item.</param>
            <returns>Created item.</returns>
        </member>
        <member name="T:NLog.Config.IInstallable">
            <summary>
            Implemented by objects which support installation and uninstallation.
            </summary>
        </member>
        <member name="M:NLog.Config.IInstallable.Install(NLog.Config.InstallationContext)">
            <summary>
            Performs installation which requires administrative permissions.
            </summary>
            <param name="installationContext">The installation context.</param>
        </member>
        <member name="M:NLog.Config.IInstallable.Uninstall(NLog.Config.InstallationContext)">
            <summary>
            Performs uninstallation which requires administrative permissions.
            </summary>
            <param name="installationContext">The installation context.</param>
        </member>
        <member name="M:NLog.Config.IInstallable.IsInstalled(NLog.Config.InstallationContext)">
            <summary>
            Determines whether the item is installed.
            </summary>
            <param name="installationContext">The installation context.</param>
            <returns>
            Value indicating whether the item is installed or null if it is not possible to determine.
            </returns>
        </member>
        <member name="T:NLog.Config.InstallationContext">
            <summary>
            Provides context for install/uninstall operations.
            </summary>
        </member>
        <member name="M:NLog.Config.InstallationContext.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Config.InstallationContext"/> class.
            </summary>
        </member>
        <member name="M:NLog.Config.InstallationContext.#ctor(System.IO.TextWriter)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Config.InstallationContext"/> class.
            </summary>
            <param name="logOutput">The log output.</param>
        </member>
        <member name="M:NLog.Config.InstallationContext.Trace(System.String,System.Object[])">
            <summary>
            Logs the specified trace message.
            </summary>
            <param name="message">The message.</param>
            <param name="arguments">The arguments.</param>
        </member>
        <member name="M:NLog.Config.InstallationContext.Debug(System.String,System.Object[])">
            <summary>
            Logs the specified debug message.
            </summary>
            <param name="message">The message.</param>
            <param name="arguments">The arguments.</param>
        </member>
        <member name="M:NLog.Config.InstallationContext.Info(System.String,System.Object[])">
            <summary>
            Logs the specified informational message.
            </summary>
            <param name="message">The message.</param>
            <param name="arguments">The arguments.</param>
        </member>
        <member name="M:NLog.Config.InstallationContext.Warning(System.String,System.Object[])">
            <summary>
            Logs the specified warning message.
            </summary>
            <param name="message">The message.</param>
            <param name="arguments">The arguments.</param>
        </member>
        <member name="M:NLog.Config.InstallationContext.Error(System.String,System.Object[])">
            <summary>
            Logs the specified error message.
            </summary>
            <param name="message">The message.</param>
            <param name="arguments">The arguments.</param>
        </member>
        <member name="M:NLog.Config.InstallationContext.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="M:NLog.Config.InstallationContext.CreateLogEvent">
            <summary>
            Creates the log event which can be used to render layouts during installation/uninstallations.
            </summary>
            <returns>Log event info object.</returns>
        </member>
        <member name="P:NLog.Config.InstallationContext.LogLevel">
            <summary>
            Gets or sets the installation log level.
            </summary>
        </member>
        <member name="P:NLog.Config.InstallationContext.IgnoreFailures">
            <summary>
            Gets or sets a value indicating whether to ignore failures during installation.
            </summary>
        </member>
        <member name="P:NLog.Config.InstallationContext.Parameters">
            <summary>
            Gets the installation parameters.
            </summary>
        </member>
        <member name="P:NLog.Config.InstallationContext.LogOutput">
            <summary>
            Gets or sets the log output.
            </summary>
        </member>
        <member name="T:NLog.Config.LoggingConfiguration">
            <summary>
            Keeps logging configuration and provides simple API
            to modify it.
            </summary>
        </member>
        <member name="M:NLog.Config.LoggingConfiguration.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Config.LoggingConfiguration"/> class.
            </summary>
        </member>
        <member name="M:NLog.Config.LoggingConfiguration.AddTarget(System.String,NLog.Targets.Target)">
            <summary>
            Registers the specified target object under a given name.
            </summary>
            <param name="name">
            Name of the target.
            </param>
            <param name="target">
            The target object.
            </param>
        </member>
        <member name="M:NLog.Config.LoggingConfiguration.FindTargetByName(System.String)">
            <summary>
            Finds the target with the specified name.
            </summary>
            <param name="name">
            The name of the target to be found.
            </param>
            <returns>
            Found target or <see langword="null"/> when the target is not found.
            </returns>
        </member>
        <member name="M:NLog.Config.LoggingConfiguration.Reload">
            <summary>
            Called by LogManager when one of the log configuration files changes.
            </summary>
            <returns>
            A new instance of <see cref="T:NLog.Config.LoggingConfiguration"/> that represents the updated configuration.
            </returns>
        </member>
        <member name="M:NLog.Config.LoggingConfiguration.RemoveTarget(System.String)">
            <summary>
            Removes the specified named target.
            </summary>
            <param name="name">
            Name of the target.
            </param>
        </member>
        <member name="M:NLog.Config.LoggingConfiguration.Install(NLog.Config.InstallationContext)">
            <summary>
            Installs target-specific objects on current system.
            </summary>
            <param name="installationContext">The installation context.</param>
            <remarks>
            Installation typically runs with administrative permissions.
            </remarks>
        </member>
        <member name="M:NLog.Config.LoggingConfiguration.Uninstall(NLog.Config.InstallationContext)">
            <summary>
            Uninstalls target-specific objects from current system.
            </summary>
            <param name="installationContext">The installation context.</param>
            <remarks>
            Uninstallation typically runs with administrative permissions.
            </remarks>
        </member>
        <member name="M:NLog.Config.LoggingConfiguration.Close">
            <summary>
            Closes all targets and releases any unmanaged resources.
            </summary>
        </member>
        <member name="M:NLog.Config.LoggingConfiguration.FlushAllTargets(NLog.Common.AsyncContinuation)">
            <summary>
            Flushes any pending log messages on all appenders.
            </summary>
            <param name="asyncContinuation">The asynchronous continuation.</param>
        </member>
        <member name="M:NLog.Config.LoggingConfiguration.ValidateConfig">
            <summary>
            Validates the configuration.
            </summary>
        </member>
        <member name="P:NLog.Config.LoggingConfiguration.ConfiguredNamedTargets">
            <summary>
            Gets a collection of named targets specified in the configuration.
            </summary>
            <returns>
            A list of named targets.
            </returns>
            <remarks>
            Unnamed targets (such as those wrapped by other targets) are not returned.
            </remarks>
        </member>
        <member name="P:NLog.Config.LoggingConfiguration.FileNamesToWatch">
            <summary>
            Gets the collection of file names which should be watched for changes by NLog.
            </summary>
        </member>
        <member name="P:NLog.Config.LoggingConfiguration.LoggingRules">
            <summary>
            Gets the collection of logging rules.
            </summary>
        </member>
        <member name="P:NLog.Config.LoggingConfiguration.AllTargets">
            <summary>
            Gets all targets.
            </summary>
        </member>
        <member name="T:NLog.Config.LoggingConfigurationChangedEventArgs">
            <summary>
            Arguments for <see cref="E:NLog.LogFactory.ConfigurationChanged"/> events.
            </summary>
        </member>
        <member name="M:NLog.Config.LoggingConfigurationChangedEventArgs.#ctor(NLog.Config.LoggingConfiguration,NLog.Config.LoggingConfiguration)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Config.LoggingConfigurationChangedEventArgs"/> class.
            </summary>
            <param name="oldConfiguration">The old configuration.</param>
            <param name="newConfiguration">The new configuration.</param>
        </member>
        <member name="P:NLog.Config.LoggingConfigurationChangedEventArgs.OldConfiguration">
            <summary>
            Gets the old configuration.
            </summary>
            <value>The old configuration.</value>
        </member>
        <member name="P:NLog.Config.LoggingConfigurationChangedEventArgs.NewConfiguration">
            <summary>
            Gets the new configuration.
            </summary>
            <value>The new configuration.</value>
        </member>
        <member name="T:NLog.Config.LoggingRule">
            <summary>
            Represents a logging rule. An equivalent of &lt;logger /&gt; configuration element.
            </summary>
        </member>
        <member name="M:NLog.Config.LoggingRule.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Config.LoggingRule"/> class.
            </summary>
        </member>
        <member name="M:NLog.Config.LoggingRule.#ctor(System.String,NLog.LogLevel,NLog.Targets.Target)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Config.LoggingRule"/> class.
            </summary>
            <param name="loggerNamePattern">Logger name pattern. It may include the '*' wildcard at the beginning, at the end or at both ends.</param>
            <param name="minLevel">Minimum log level needed to trigger this rule.</param>
            <param name="target">Target to be written to when the rule matches.</param>
        </member>
        <member name="M:NLog.Config.LoggingRule.#ctor(System.String,NLog.Targets.Target)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Config.LoggingRule"/> class.
            </summary>
            <param name="loggerNamePattern">Logger name pattern. It may include the '*' wildcard at the beginning, at the end or at both ends.</param>
            <param name="target">Target to be written to when the rule matches.</param>
            <remarks>By default no logging levels are defined. You should call <see cref="M:NLog.Config.LoggingRule.EnableLoggingForLevel(NLog.LogLevel)"/> and <see cref="M:NLog.Config.LoggingRule.DisableLoggingForLevel(NLog.LogLevel)"/> to set them.</remarks>
        </member>
        <member name="M:NLog.Config.LoggingRule.EnableLoggingForLevel(NLog.LogLevel)">
            <summary>
            Enables logging for a particular level.
            </summary>
            <param name="level">Level to be enabled.</param>
        </member>
        <member name="M:NLog.Config.LoggingRule.DisableLoggingForLevel(NLog.LogLevel)">
            <summary>
            Disables logging for a particular level.
            </summary>
            <param name="level">Level to be disabled.</param>
        </member>
        <member name="M:NLog.Config.LoggingRule.ToString">
            <summary>
            Returns a string representation of <see cref="T:NLog.Config.LoggingRule"/>. Used for debugging.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="M:NLog.Config.LoggingRule.IsLoggingEnabledForLevel(NLog.LogLevel)">
            <summary>
            Checks whether te particular log level is enabled for this rule.
            </summary>
            <param name="level">Level to be checked.</param>
            <returns>A value of <see langword="true"/> when the log level is enabled, <see langword="false" /> otherwise.</returns>
        </member>
        <member name="M:NLog.Config.LoggingRule.NameMatches(System.String)">
            <summary>
            Checks whether given name matches the logger name pattern.
            </summary>
            <param name="loggerName">String to be matched.</param>
            <returns>A value of <see langword="true"/> when the name matches, <see langword="false" /> otherwise.</returns>
        </member>
        <member name="P:NLog.Config.LoggingRule.Targets">
            <summary>
            Gets a collection of targets that should be written to when this rule matches.
            </summary>
        </member>
        <member name="P:NLog.Config.LoggingRule.ChildRules">
            <summary>
            Gets a collection of child rules to be evaluated when this rule matches.
            </summary>
        </member>
        <member name="P:NLog.Config.LoggingRule.Filters">
            <summary>
            Gets a collection of filters to be checked before writing to targets.
            </summary>
        </member>
        <member name="P:NLog.Config.LoggingRule.Final">
            <summary>
            Gets or sets a value indicating whether to quit processing any further rule when this one matches.
            </summary>
        </member>
        <member name="P:NLog.Config.LoggingRule.LoggerNamePattern">
            <summary>
            Gets or sets logger name pattern.
            </summary>
            <remarks>
            Logger name pattern. It may include the '*' wildcard at the beginning, at the end or at both ends but not anywhere else.
            </remarks>
        </member>
        <member name="P:NLog.Config.LoggingRule.Levels">
            <summary>
            Gets the collection of log levels enabled by this rule.
            </summary>
        </member>
        <member name="T:NLog.Config.MethodFactory`2">
            <summary>
            Factory for locating methods.
            </summary>
            <typeparam name="TClassAttributeType">The type of the class marker attribute.</typeparam>
            <typeparam name="TMethodAttributeType">The type of the method marker attribute.</typeparam>
        </member>
        <member name="M:NLog.Config.MethodFactory`2.ScanAssembly(System.Reflection.Assembly,System.String)">
            <summary>
            Scans the assembly for classes marked with <typeparamref name="TClassAttributeType"/>
            and methods marked with <typeparamref name="TMethodAttributeType"/> and adds them 
            to the factory.
            </summary>
            <param name="theAssembly">The assembly.</param>
            <param name="prefix">The prefix to use for names.</param>
        </member>
        <member name="M:NLog.Config.MethodFactory`2.RegisterType(System.Type,System.String)">
            <summary>
            Registers the type.
            </summary>
            <param name="type">The type to register.</param>
            <param name="itemNamePrefix">The item name prefix.</param>
        </member>
        <member name="M:NLog.Config.MethodFactory`2.Clear">
            <summary>
            Clears contents of the factory.
            </summary>
        </member>
        <member name="M:NLog.Config.MethodFactory`2.RegisterDefinition(System.String,System.Reflection.MethodInfo)">
            <summary>
            Registers the definition of a single method.
            </summary>
            <param name="name">The method name.</param>
            <param name="methodInfo">The method info.</param>
        </member>
        <member name="M:NLog.Config.MethodFactory`2.TryCreateInstance(System.String,System.Reflection.MethodInfo@)">
            <summary>
            Tries to retrieve method by name.
            </summary>
            <param name="name">The method name.</param>
            <param name="result">The result.</param>
            <returns>A value of <c>true</c> if the method was found, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:NLog.Config.MethodFactory`2.CreateInstance(System.String)">
            <summary>
            Retrieves method by name.
            </summary>
            <param name="name">Method name.</param>
            <returns>MethodInfo object.</returns>
        </member>
        <member name="M:NLog.Config.MethodFactory`2.TryGetDefinition(System.String,System.Reflection.MethodInfo@)">
            <summary>
            Tries to get method definition.
            </summary>
            <param name="name">The method .</param>
            <param name="result">The result.</param>
            <returns>A value of <c>true</c> if the method was found, <c>false</c> otherwise.</returns>
        </member>
        <member name="P:NLog.Config.MethodFactory`2.AllRegisteredItems">
            <summary>
            Gets a collection of all registered items in the factory.
            </summary>
            <returns>
            Sequence of key/value pairs where each key represents the name
            of the item and value is the <see cref="T:System.Reflection.MethodInfo"/> of
            the item.
            </returns>
        </member>
        <member name="T:NLog.Config.NLogConfigurationItemAttribute">
            <summary>
            Marks the object as configuration item for NLog.
            </summary>
        </member>
        <member name="M:NLog.Config.NLogConfigurationItemAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Config.NLogConfigurationItemAttribute"/> class.
            </summary>
        </member>
        <member name="T:NLog.Config.NLogXmlElement">
            <summary>
            Represents simple XML element with case-insensitive attribute semantics.
            </summary>
        </member>
        <member name="M:NLog.Config.NLogXmlElement.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Config.NLogXmlElement"/> class.
            </summary>
            <param name="inputUri">The input URI.</param>
        </member>
        <member name="M:NLog.Config.NLogXmlElement.#ctor(System.Xml.XmlReader)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Config.NLogXmlElement"/> class.
            </summary>
            <param name="reader">The reader to initialize element from.</param>
        </member>
        <member name="M:NLog.Config.NLogXmlElement.#ctor">
            <summary>
            Prevents a default instance of the <see cref="T:NLog.Config.NLogXmlElement"/> class from being created.
            </summary>
        </member>
        <member name="M:NLog.Config.NLogXmlElement.Elements(System.String)">
            <summary>
            Returns children elements with the specified element name.
            </summary>
            <param name="elementName">Name of the element.</param>
            <returns>Children elements with the specified element name.</returns>
        </member>
        <member name="M:NLog.Config.NLogXmlElement.GetRequiredAttribute(System.String)">
            <summary>
            Gets the required attribute.
            </summary>
            <param name="attributeName">Name of the attribute.</param>
            <returns>Attribute value.</returns>
            <remarks>Throws if the attribute is not specified.</remarks>
        </member>
        <member name="M:NLog.Config.NLogXmlElement.GetOptionalBooleanAttribute(System.String,System.Boolean)">
            <summary>
            Gets the optional boolean attribute value.
            </summary>
            <param name="attributeName">Name of the attribute.</param>
            <param name="defaultValue">Default value to return if the attribute is not found.</param>
            <returns>Boolean attribute value or default.</returns>
        </member>
        <member name="M:NLog.Config.NLogXmlElement.GetOptionalAttribute(System.String,System.String)">
            <summary>
            Gets the optional attribute value.
            </summary>
            <param name="attributeName">Name of the attribute.</param>
            <param name="defaultValue">The default value.</param>
            <returns>Value of the attribute or default value.</returns>
        </member>
        <member name="M:NLog.Config.NLogXmlElement.AssertName(System.String[])">
            <summary>
            Asserts that the name of the element is among specified element names.
            </summary>
            <param name="allowedNames">The allowed names.</param>
        </member>
        <member name="P:NLog.Config.NLogXmlElement.LocalName">
            <summary>
            Gets the element name.
            </summary>
        </member>
        <member name="P:NLog.Config.NLogXmlElement.AttributeValues">
            <summary>
            Gets the dictionary of attribute values.
            </summary>
        </member>
        <member name="P:NLog.Config.NLogXmlElement.Children">
            <summary>
            Gets the collection of child elements.
            </summary>
        </member>
        <member name="P:NLog.Config.NLogXmlElement.Value">
            <summary>
            Gets the value of the element.
            </summary>
        </member>
        <member name="T:NLog.Config.RequiredParameterAttribute">
            <summary>
            Attribute used to mark the required parameters for targets,
            layout targets and filters.
            </summary>
        </member>
        <member name="T:NLog.Config.SimpleConfigurator">
            <summary>
            Provides simple programmatic configuration API used for trivial logging cases.
            </summary>
        </member>
        <member name="M:NLog.Config.SimpleConfigurator.ConfigureForConsoleLogging">
            <summary>
            Configures NLog for console logging so that all messages above and including
            the <see cref="F:NLog.LogLevel.Info"/> level are output to the console.
            </summary>
        </member>
        <member name="M:NLog.Config.SimpleConfigurator.ConfigureForConsoleLogging(NLog.LogLevel)">
            <summary>
            Configures NLog for console logging so that all messages above and including
            the specified level are output to the console.
            </summary>
            <param name="minLevel">The minimal logging level.</param>
        </member>
        <member name="M:NLog.Config.SimpleConfigurator.ConfigureForTargetLogging(NLog.Targets.Target)">
            <summary>
            Configures NLog for to log to the specified target so that all messages 
            above and including the <see cref="F:NLog.LogLevel.Info"/> level are output.
            </summary>
            <param name="target">The target to log all messages to.</param>
        </member>
        <member name="M:NLog.Config.SimpleConfigurator.ConfigureForTargetLogging(NLog.Targets.Target,NLog.LogLevel)">
            <summary>
            Configures NLog for to log to the specified target so that all messages 
            above and including the specified level are output.
            </summary>
            <param name="target">The target to log all messages to.</param>
            <param name="minLevel">The minimal logging level.</param>
        </member>
        <member name="T:NLog.Config.StackTraceUsage">
            <summary>
            Value indicating how stack trace should be captured when processing the log event.
            </summary>
        </member>
        <member name="F:NLog.Config.StackTraceUsage.None">
            <summary>
            Stack trace should not be captured.
            </summary>
        </member>
        <member name="F:NLog.Config.StackTraceUsage.WithoutSource">
            <summary>
            Stack trace should be captured without source-level information.
            </summary>
        </member>
        <member name="F:NLog.Config.StackTraceUsage.Max">
            <summary>
            Capture maximum amount of the stack trace information supported on the plaform.
            </summary>
        </member>
        <member name="T:NLog.Config.ThreadAgnosticAttribute">
            <summary>
            Marks the layout or layout renderer as producing correct results regardless of the thread
            it's running on.
            </summary>
        </member>
        <member name="T:NLog.Config.XmlLoggingConfiguration">
            <summary>
            A class for configuring NLog through an XML configuration file 
            (App.config style or App.nlog style).
            </summary>
        </member>
        <member name="M:NLog.Config.XmlLoggingConfiguration.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Config.XmlLoggingConfiguration"/> class.
            </summary>
            <param name="fileName">Configuration file to be read.</param>
        </member>
        <member name="M:NLog.Config.XmlLoggingConfiguration.#ctor(System.String,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Config.XmlLoggingConfiguration"/> class.
            </summary>
            <param name="fileName">Configuration file to be read.</param>
            <param name="ignoreErrors">Ignore any errors during configuration.</param>
        </member>
        <member name="M:NLog.Config.XmlLoggingConfiguration.#ctor(System.Xml.XmlReader,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Config.XmlLoggingConfiguration"/> class.
            </summary>
            <param name="reader"><see cref="T:System.Xml.XmlReader"/> containing the configuration section.</param>
            <param name="fileName">Name of the file that contains the element (to be used as a base for including other files).</param>
        </member>
        <member name="M:NLog.Config.XmlLoggingConfiguration.#ctor(System.Xml.XmlReader,System.String,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Config.XmlLoggingConfiguration"/> class.
            </summary>
            <param name="reader"><see cref="T:System.Xml.XmlReader"/> containing the configuration section.</param>
            <param name="fileName">Name of the file that contains the element (to be used as a base for including other files).</param>
            <param name="ignoreErrors">Ignore any errors during configuration.</param>
        </member>
        <member name="M:NLog.Config.XmlLoggingConfiguration.Reload">
            <summary>
            Re-reads the original configuration file and returns the new <see cref="T:NLog.Config.LoggingConfiguration"/> object.
            </summary>
            <returns>The new <see cref="T:NLog.Config.XmlLoggingConfiguration"/> object.</returns>
        </member>
        <member name="M:NLog.Config.XmlLoggingConfiguration.Initialize(System.Xml.XmlReader,System.String,System.Boolean)">
            <summary>
            Initializes the configuration.
            </summary>
            <param name="reader"><see cref="T:System.Xml.XmlReader"/> containing the configuration section.</param>
            <param name="fileName">Name of the file that contains the element (to be used as a base for including other files).</param>
            <param name="ignoreErrors">Ignore any errors during configuration.</param>
        </member>
        <member name="P:NLog.Config.XmlLoggingConfiguration.AutoReload">
            <summary>
            Gets or sets a value indicating whether the configuration files
            should be watched for changes and reloaded automatically when changed.
            </summary>
        </member>
        <member name="P:NLog.Config.XmlLoggingConfiguration.FileNamesToWatch">
            <summary>
            Gets the collection of file names which should be watched for changes by NLog.
            This is the list of configuration files processed.
            If the <c>autoReload</c> attribute is not set it returns empty collection.
            </summary>
        </member>
        <member name="T:NLog.Filters.ConditionBasedFilter">
            <summary>
            Matches when the specified condition is met.
            </summary>
            <remarks>
            Conditions are expressed using a simple language 
            described <a href="conditions.html">here</a>.
            </remarks>
        </member>
        <member name="T:NLog.Filters.Filter">
            <summary>
            An abstract filter class. Provides a way to eliminate log messages
            based on properties other than logger name and log level.
            </summary>
        </member>
        <member name="M:NLog.Filters.Filter.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Filters.Filter"/> class.
            </summary>
        </member>
        <member name="M:NLog.Filters.Filter.GetFilterResult(NLog.LogEventInfo)">
            <summary>
            Gets the result of evaluating filter against given log event.
            </summary>
            <param name="logEvent">The log event.</param>
            <returns>Filter result.</returns>
        </member>
        <member name="M:NLog.Filters.Filter.Check(NLog.LogEventInfo)">
            <summary>
            Checks whether log event should be logged or not.
            </summary>
            <param name="logEvent">Log event.</param>
            <returns>
            <see cref="F:NLog.Filters.FilterResult.Ignore"/> - if the log event should be ignored<br/>
            <see cref="F:NLog.Filters.FilterResult.Neutral"/> - if the filter doesn't want to decide<br/>
            <see cref="F:NLog.Filters.FilterResult.Log"/> - if the log event should be logged<br/>
            .</returns>
        </member>
        <member name="P:NLog.Filters.Filter.Action">
            <summary>
            Gets or sets the action to be taken when filter matches.
            </summary>
            <docgen category='Filtering Options' order='10' />
        </member>
        <member name="M:NLog.Filters.ConditionBasedFilter.Check(NLog.LogEventInfo)">
            <summary>
            Checks whether log event should be logged or not.
            </summary>
            <param name="logEvent">Log event.</param>
            <returns>
            <see cref="F:NLog.Filters.FilterResult.Ignore"/> - if the log event should be ignored<br/>
            <see cref="F:NLog.Filters.FilterResult.Neutral"/> - if the filter doesn't want to decide<br/>
            <see cref="F:NLog.Filters.FilterResult.Log"/> - if the log event should be logged<br/>
            .</returns>
        </member>
        <member name="P:NLog.Filters.ConditionBasedFilter.Condition">
            <summary>
            Gets or sets the condition expression.
            </summary>
            <docgen category='Filtering Options' order='10' />
        </member>
        <member name="T:NLog.Filters.FilterAttribute">
            <summary>
            Marks class as a layout renderer and assigns a name to it.
            </summary>
        </member>
        <member name="M:NLog.Filters.FilterAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Filters.FilterAttribute"/> class.
            </summary>
            <param name="name">Name of the filter.</param>
        </member>
        <member name="T:NLog.Filters.FilterResult">
            <summary>
            Filter result.
            </summary>
        </member>
        <member name="F:NLog.Filters.FilterResult.Neutral">
            <summary>
            The filter doesn't want to decide whether to log or discard the message.
            </summary>
        </member>
        <member name="F:NLog.Filters.FilterResult.Log">
            <summary>
            The message should be logged.
            </summary>
        </member>
        <member name="F:NLog.Filters.FilterResult.Ignore">
            <summary>
            The message should not be logged.
            </summary>
        </member>
        <member name="F:NLog.Filters.FilterResult.LogFinal">
            <summary>
            The message should be logged and processing should be finished.
            </summary>
        </member>
        <member name="F:NLog.Filters.FilterResult.IgnoreFinal">
            <summary>
            The message should not be logged and processing should be finished.
            </summary>
        </member>
        <member name="T:NLog.Filters.LayoutBasedFilter">
            <summary>
            A base class for filters that are based on comparing a value to a layout.
            </summary>
        </member>
        <member name="M:NLog.Filters.LayoutBasedFilter.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Filters.LayoutBasedFilter"/> class.
            </summary>
        </member>
        <member name="P:NLog.Filters.LayoutBasedFilter.Layout">
            <summary>
            Gets or sets the layout to be used to filter log messages.
            </summary>
            <value>The layout.</value>
            <docgen category='Filtering Options' order='10' />
        </member>
        <member name="T:NLog.Filters.WhenContainsFilter">
            <summary>
            Matches when the calculated layout contains the specified substring. 
            This filter is deprecated in favour of <c>&lt;when /&gt;</c> which is based on <a href="conditions.html">contitions</a>.
            </summary>
        </member>
        <member name="M:NLog.Filters.WhenContainsFilter.Check(NLog.LogEventInfo)">
            <summary>
            Checks whether log event should be logged or not.
            </summary>
            <param name="logEvent">Log event.</param>
            <returns>
            <see cref="F:NLog.Filters.FilterResult.Ignore"/> - if the log event should be ignored<br/>
            <see cref="F:NLog.Filters.FilterResult.Neutral"/> - if the filter doesn't want to decide<br/>
            <see cref="F:NLog.Filters.FilterResult.Log"/> - if the log event should be logged<br/>
            .</returns>
        </member>
        <member name="P:NLog.Filters.WhenContainsFilter.IgnoreCase">
            <summary>
            Gets or sets a value indicating whether to ignore case when comparing strings.
            </summary>
            <docgen category='Filtering Options' order='10' />
        </member>
        <member name="P:NLog.Filters.WhenContainsFilter.Substring">
            <summary>
            Gets or sets the substring to be matched.
            </summary>
            <docgen category='Filtering Options' order='10' />
        </member>
        <member name="T:NLog.Filters.WhenEqualFilter">
            <summary>
            Matches when the calculated layout is equal to the specified substring.
            This filter is deprecated in favour of <c>&lt;when /&gt;</c> which is based on <a href="conditions.html">contitions</a>.
            </summary>
        </member>
        <member name="M:NLog.Filters.WhenEqualFilter.Check(NLog.LogEventInfo)">
            <summary>
            Checks whether log event should be logged or not.
            </summary>
            <param name="logEvent">Log event.</param>
            <returns>
            <see cref="F:NLog.Filters.FilterResult.Ignore"/> - if the log event should be ignored<br/>
            <see cref="F:NLog.Filters.FilterResult.Neutral"/> - if the filter doesn't want to decide<br/>
            <see cref="F:NLog.Filters.FilterResult.Log"/> - if the log event should be logged<br/>
            .</returns>
        </member>
        <member name="P:NLog.Filters.WhenEqualFilter.IgnoreCase">
            <summary>
            Gets or sets a value indicating whether to ignore case when comparing strings.
            </summary>
            <docgen category='Filtering Options' order='10' />
        </member>
        <member name="P:NLog.Filters.WhenEqualFilter.CompareTo">
            <summary>
            Gets or sets a string to compare the layout to.
            </summary>
            <docgen category='Filtering Options' order='10' />
        </member>
        <member name="T:NLog.Filters.WhenNotContainsFilter">
            <summary>
            Matches when the calculated layout does NOT contain the specified substring.
            This filter is deprecated in favour of <c>&lt;when /&gt;</c> which is based on <a href="conditions.html">contitions</a>.
            </summary>
        </member>
        <member name="M:NLog.Filters.WhenNotContainsFilter.Check(NLog.LogEventInfo)">
            <summary>
            Checks whether log event should be logged or not.
            </summary>
            <param name="logEvent">Log event.</param>
            <returns>
            <see cref="F:NLog.Filters.FilterResult.Ignore"/> - if the log event should be ignored<br/>
            <see cref="F:NLog.Filters.FilterResult.Neutral"/> - if the filter doesn't want to decide<br/>
            <see cref="F:NLog.Filters.FilterResult.Log"/> - if the log event should be logged<br/>
            .</returns>
        </member>
        <member name="P:NLog.Filters.WhenNotContainsFilter.Substring">
            <summary>
            Gets or sets the substring to be matched.
            </summary>
            <docgen category='Filtering Options' order='10' />
        </member>
        <member name="P:NLog.Filters.WhenNotContainsFilter.IgnoreCase">
            <summary>
            Gets or sets a value indicating whether to ignore case when comparing strings.
            </summary>
            <docgen category='Filtering Options' order='10' />
        </member>
        <member name="T:NLog.Filters.WhenNotEqualFilter">
            <summary>
            Matches when the calculated layout is NOT equal to the specified substring.
            This filter is deprecated in favour of <c>&lt;when /&gt;</c> which is based on <a href="conditions.html">contitions</a>.
            </summary>
        </member>
        <member name="M:NLog.Filters.WhenNotEqualFilter.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Filters.WhenNotEqualFilter"/> class.
            </summary>
        </member>
        <member name="M:NLog.Filters.WhenNotEqualFilter.Check(NLog.LogEventInfo)">
            <summary>
            Checks whether log event should be logged or not.
            </summary>
            <param name="logEvent">Log event.</param>
            <returns>
            <see cref="F:NLog.Filters.FilterResult.Ignore"/> - if the log event should be ignored<br/>
            <see cref="F:NLog.Filters.FilterResult.Neutral"/> - if the filter doesn't want to decide<br/>
            <see cref="F:NLog.Filters.FilterResult.Log"/> - if the log event should be logged<br/>
            .</returns>
        </member>
        <member name="P:NLog.Filters.WhenNotEqualFilter.CompareTo">
            <summary>
            Gets or sets a string to compare the layout to.
            </summary>
            <docgen category='Filtering Options' order='10' />
        </member>
        <member name="P:NLog.Filters.WhenNotEqualFilter.IgnoreCase">
            <summary>
            Gets or sets a value indicating whether to ignore case when comparing strings.
            </summary>
            <docgen category='Filtering Options' order='10' />
        </member>
        <member name="T:NLog.GDC">
            <summary>
            Global Diagnostics Context - used for log4net compatibility.
            </summary>
        </member>
        <member name="M:NLog.GDC.Set(System.String,System.String)">
            <summary>
            Sets the Global Diagnostics Context item to the specified value.
            </summary>
            <param name="item">Item name.</param>
            <param name="value">Item value.</param>
        </member>
        <member name="M:NLog.GDC.Get(System.String)">
            <summary>
            Gets the Global Diagnostics Context named item.
            </summary>
            <param name="item">Item name.</param>
            <returns>The item value of string.Empty if the value is not present.</returns>
        </member>
        <member name="M:NLog.GDC.Contains(System.String)">
            <summary>
            Checks whether the specified item exists in the Global Diagnostics Context.
            </summary>
            <param name="item">Item name.</param>
            <returns>A boolean indicating whether the specified item exists in current thread GDC.</returns>
        </member>
        <member name="M:NLog.GDC.Remove(System.String)">
            <summary>
            Removes the specified item from the Global Diagnostics Context.
            </summary>
            <param name="item">Item name.</param>
        </member>
        <member name="M:NLog.GDC.Clear">
            <summary>
            Clears the content of the GDC.
            </summary>
        </member>
        <member name="T:NLog.GlobalDiagnosticsContext">
            <summary>
            Global Diagnostics Context - a dictionary structure to hold per-application-instance values.
            </summary>
        </member>
        <member name="M:NLog.GlobalDiagnosticsContext.Set(System.String,System.String)">
            <summary>
            Sets the Global Diagnostics Context item to the specified value.
            </summary>
            <param name="item">Item name.</param>
            <param name="value">Item value.</param>
        </member>
        <member name="M:NLog.GlobalDiagnosticsContext.Get(System.String)">
            <summary>
            Gets the Global Diagnostics Context named item.
            </summary>
            <param name="item">Item name.</param>
            <returns>The item value of string.Empty if the value is not present.</returns>
        </member>
        <member name="M:NLog.GlobalDiagnosticsContext.Contains(System.String)">
            <summary>
            Checks whether the specified item exists in the Global Diagnostics Context.
            </summary>
            <param name="item">Item name.</param>
            <returns>A boolean indicating whether the specified item exists in current thread GDC.</returns>
        </member>
        <member name="M:NLog.GlobalDiagnosticsContext.Remove(System.String)">
            <summary>
            Removes the specified item from the Global Diagnostics Context.
            </summary>
            <param name="item">Item name.</param>
        </member>
        <member name="M:NLog.GlobalDiagnosticsContext.Clear">
            <summary>
            Clears the content of the GDC.
            </summary>
        </member>
        <member name="T:NLog.Internal.CurrentTimeGetter">
            <summary>
            Optimized methods to get current time.
            </summary>
        </member>
        <member name="P:NLog.Internal.CurrentTimeGetter.Now">
            <summary>
            Gets the current time in an optimized fashion.
            </summary>
            <value>Current time.</value>
        </member>
        <member name="T:NLog.Internal.DictionaryAdapter`2">
            <summary>
            Provides untyped IDictionary interface on top of generic IDictionary.
            </summary>
            <typeparam name="TKey">The type of the key.</typeparam>
            <typeparam name="TValue">The type of the value.</typeparam>
        </member>
        <member name="M:NLog.Internal.DictionaryAdapter`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
            <summary>
            Initializes a new instance of the DictionaryAdapter class.
            </summary>
            <param name="implementation">The implementation.</param>
        </member>
        <member name="M:NLog.Internal.DictionaryAdapter`2.Add(System.Object,System.Object)">
            <summary>
            Adds an element with the provided key and value to the <see cref="T:System.Collections.IDictionary"/> object.
            </summary>
            <param name="key">The <see cref="T:System.Object"/> to use as the key of the element to add.</param>
            <param name="value">The <see cref="T:System.Object"/> to use as the value of the element to add.</param>
        </member>
        <member name="M:NLog.Internal.DictionaryAdapter`2.Clear">
            <summary>
            Removes all elements from the <see cref="T:System.Collections.IDictionary"/> object.
            </summary>
        </member>
        <member name="M:NLog.Internal.DictionaryAdapter`2.Contains(System.Object)">
            <summary>
            Determines whether the <see cref="T:System.Collections.IDictionary"/> object contains an element with the specified key.
            </summary>
            <param name="key">The key to locate in the <see cref="T:System.Collections.IDictionary"/> object.</param>
            <returns>
            True if the <see cref="T:System.Collections.IDictionary"/> contains an element with the key; otherwise, false.
            </returns>
        </member>
        <member name="M:NLog.Internal.DictionaryAdapter`2.GetEnumerator">
            <summary>
            Returns an <see cref="T:System.Collections.IDictionaryEnumerator"/> object for the <see cref="T:System.Collections.IDictionary"/> object.
            </summary>
            <returns>
            An <see cref="T:System.Collections.IDictionaryEnumerator"/> object for the <see cref="T:System.Collections.IDictionary"/> object.
            </returns>
        </member>
        <member name="M:NLog.Internal.DictionaryAdapter`2.Remove(System.Object)">
            <summary>
            Removes the element with the specified key from the <see cref="T:System.Collections.IDictionary"/> object.
            </summary>
            <param name="key">The key of the element to remove.</param>
        </member>
        <member name="M:NLog.Internal.DictionaryAdapter`2.CopyTo(System.Array,System.Int32)">
            <summary>
            Copies the elements of the <see cref="T:System.Collections.ICollection"/> to an <see cref="T:System.Array"/>, starting at a particular <see cref="T:System.Array"/> index.
            </summary>
            <param name="array">The one-dimensional <see cref="T:System.Array"/> that is the destination of the elements copied from <see cref="T:System.Collections.ICollection"/>. The <see cref="T:System.Array"/> must have zero-based indexing.</param>
            <param name="index">The zero-based index in <paramref name="array"/> at which copying begins.</param>
        </member>
        <member name="M:NLog.Internal.DictionaryAdapter`2.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>
            An <see cref="T:System.Collections.IEnumerator"/> object that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="P:NLog.Internal.DictionaryAdapter`2.Values">
            <summary>
            Gets an <see cref="T:System.Collections.ICollection"/> object containing the values in the <see cref="T:System.Collections.IDictionary"/> object.
            </summary>
            <value></value>
            <returns>
            An <see cref="T:System.Collections.ICollection"/> object containing the values in the <see cref="T:System.Collections.IDictionary"/> object.
            </returns>
        </member>
        <member name="P:NLog.Internal.DictionaryAdapter`2.Count">
            <summary>
            Gets the number of elements contained in the <see cref="T:System.Collections.ICollection"/>.
            </summary>
            <value></value>
            <returns>
            The number of elements contained in the <see cref="T:System.Collections.ICollection"/>.
            </returns>
        </member>
        <member name="P:NLog.Internal.DictionaryAdapter`2.IsSynchronized">
            <summary>
            Gets a value indicating whether access to the <see cref="T:System.Collections.ICollection"/> is synchronized (thread safe).
            </summary>
            <value></value>
            <returns>true if access to the <see cref="T:System.Collections.ICollection"/> is synchronized (thread safe); otherwise, false.
            </returns>
        </member>
        <member name="P:NLog.Internal.DictionaryAdapter`2.SyncRoot">
            <summary>
            Gets an object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection"/>.
            </summary>
            <value></value>
            <returns>
            An object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection"/>.
            </returns>
        </member>
        <member name="P:NLog.Internal.DictionaryAdapter`2.IsFixedSize">
            <summary>
            Gets a value indicating whether the <see cref="T:System.Collections.IDictionary"/> object has a fixed size.
            </summary>
            <value></value>
            <returns>true if the <see cref="T:System.Collections.IDictionary"/> object has a fixed size; otherwise, false.
            </returns>
        </member>
        <member name="P:NLog.Internal.DictionaryAdapter`2.IsReadOnly">
            <summary>
            Gets a value indicating whether the <see cref="T:System.Collections.IDictionary"/> object is read-only.
            </summary>
            <value></value>
            <returns>true if the <see cref="T:System.Collections.IDictionary"/> object is read-only; otherwise, false.
            </returns>
        </member>
        <member name="P:NLog.Internal.DictionaryAdapter`2.Keys">
            <summary>
            Gets an <see cref="T:System.Collections.ICollection"/> object containing the keys of the <see cref="T:System.Collections.IDictionary"/> object.
            </summary>
            <value></value>
            <returns>
            An <see cref="T:System.Collections.ICollection"/> object containing the keys of the <see cref="T:System.Collections.IDictionary"/> object.
            </returns>
        </member>
        <member name="P:NLog.Internal.DictionaryAdapter`2.Item(System.Object)">
            <summary>
            Gets or sets the <see cref="T:System.Object"/> with the specified key.
            </summary>
            <param name="key">Dictionary key.</param>
            <returns>Value corresponding to key or null if not found</returns>
        </member>
        <member name="T:NLog.Internal.DictionaryAdapter`2.MyEnumerator">
            <summary>
            Wrapper IDictionaryEnumerator.
            </summary>
        </member>
        <member name="M:NLog.Internal.DictionaryAdapter`2.MyEnumerator.#ctor(System.Collections.Generic.IEnumerator{System.Collections.Generic.KeyValuePair{`0,`1}})">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Internal.DictionaryAdapter`2.MyEnumerator"/> class.
            </summary>
            <param name="wrapped">The wrapped.</param>
        </member>
        <member name="M:NLog.Internal.DictionaryAdapter`2.MyEnumerator.MoveNext">
            <summary>
            Advances the enumerator to the next element of the collection.
            </summary>
            <returns>
            True if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.
            </returns>
        </member>
        <member name="M:NLog.Internal.DictionaryAdapter`2.MyEnumerator.Reset">
            <summary>
            Sets the enumerator to its initial position, which is before the first element in the collection.
            </summary>
        </member>
        <member name="P:NLog.Internal.DictionaryAdapter`2.MyEnumerator.Entry">
            <summary>
            Gets both the key and the value of the current dictionary entry.
            </summary>
            <value></value>
            <returns>
            A <see cref="T:System.Collections.DictionaryEntry"/> containing both the key and the value of the current dictionary entry.
            </returns>
        </member>
        <member name="P:NLog.Internal.DictionaryAdapter`2.MyEnumerator.Key">
            <summary>
            Gets the key of the current dictionary entry.
            </summary>
            <value></value>
            <returns>
            The key of the current element of the enumeration.
            </returns>
        </member>
        <member name="P:NLog.Internal.DictionaryAdapter`2.MyEnumerator.Value">
            <summary>
            Gets the value of the current dictionary entry.
            </summary>
            <value></value>
            <returns>
            The value of the current element of the enumeration.
            </returns>
        </member>
        <member name="P:NLog.Internal.DictionaryAdapter`2.MyEnumerator.Current">
            <summary>
            Gets the current element in the collection.
            </summary>
            <value></value>
            <returns>
            The current element in the collection.
            </returns>
        </member>
        <member name="T:NLog.Internal.EnumerableHelpers">
            <summary>
            LINQ-like helpers (cannot use LINQ because we must work with .NET 2.0 profile).
            </summary>
        </member>
        <member name="M:NLog.Internal.EnumerableHelpers.OfType``1(System.Collections.IEnumerable)">
            <summary>
            Filters the given enumerable to return only items of the specified type.
            </summary>
            <typeparam name="T">
            Type of the item.
            </typeparam>
            <param name="enumerable">
            The enumerable.
            </param>
            <returns>
            Items of specified type.
            </returns>
        </member>
        <member name="M:NLog.Internal.EnumerableHelpers.Reverse``1(System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Reverses the specified enumerable.
            </summary>
            <typeparam name="T">
            Type of enumerable item.
            </typeparam>
            <param name="enumerable">
            The enumerable.
            </param>
            <returns>
            Reversed enumerable.
            </returns>
        </member>
        <member name="M:NLog.Internal.EnumerableHelpers.Any``1(System.Collections.Generic.IEnumerable{``0},System.Predicate{``0})">
            <summary>
            Determines is the given predicate is met by any element of the enumerable.
            </summary>
            <typeparam name="T">Element type.</typeparam>
            <param name="enumerable">The enumerable.</param>
            <param name="predicate">The predicate.</param>
            <returns>True if predicate returns true for any element of the collection, false otherwise.</returns>
        </member>
        <member name="M:NLog.Internal.EnumerableHelpers.ToList``1(System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Converts the enumerable to list.
            </summary>
            <typeparam name="T">Type of the list element.</typeparam>
            <param name="enumerable">The enumerable.</param>
            <returns>List of elements.</returns>
        </member>
        <member name="T:NLog.Internal.EnvironmentHelper">
            <summary>
            Safe way to get environment variables.
            </summary>
        </member>
        <member name="T:NLog.Internal.ExceptionHelper">
            <summary>
            Helper class for dealing with exceptions.
            </summary>
        </member>
        <member name="M:NLog.Internal.ExceptionHelper.MustBeRethrown(System.Exception)">
            <summary>
            Determines whether the exception must be rethrown.
            </summary>
            <param name="exception">The exception.</param>
            <returns>True if the exception must be rethrown, false otherwise.</returns>
        </member>
        <member name="T:NLog.Internal.FactoryHelper">
            <summary>
            Object construction helper.
            </summary>
        </member>
        <member name="T:NLog.Internal.FileAppenders.BaseFileAppender">
            <summary>
            Base class for optimized file appenders.
            </summary>
        </member>
        <member name="M:NLog.Internal.FileAppenders.BaseFileAppender.#ctor(System.String,NLog.Internal.FileAppenders.ICreateFileParameters)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Internal.FileAppenders.BaseFileAppender"/> class.
            </summary>
            <param name="fileName">Name of the file.</param>
            <param name="createParameters">The create parameters.</param>
        </member>
        <member name="M:NLog.Internal.FileAppenders.BaseFileAppender.Write(System.Byte[])">
            <summary>
            Writes the specified bytes.
            </summary>
            <param name="bytes">The bytes.</param>
        </member>
        <member name="M:NLog.Internal.FileAppenders.BaseFileAppender.Flush">
            <summary>
            Flushes this instance.
            </summary>
        </member>
        <member name="M:NLog.Internal.FileAppenders.BaseFileAppender.Close">
            <summary>
            Closes this instance.
            </summary>
        </member>
        <member name="M:NLog.Internal.FileAppenders.BaseFileAppender.GetFileInfo(System.DateTime@,System.Int64@)">
            <summary>
            Gets the file info.
            </summary>
            <param name="lastWriteTime">The last write time.</param>
            <param name="fileLength">Length of the file.</param>
            <returns>True if the operation succeeded, false otherwise.</returns>
        </member>
        <member name="M:NLog.Internal.FileAppenders.BaseFileAppender.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="M:NLog.Internal.FileAppenders.BaseFileAppender.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
            <param name="disposing">True to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="M:NLog.Internal.FileAppenders.BaseFileAppender.FileTouched">
            <summary>
            Records the last write time for a file.
            </summary>
        </member>
        <member name="M:NLog.Internal.FileAppenders.BaseFileAppender.FileTouched(System.DateTime)">
            <summary>
            Records the last write time for a file to be specific date.
            </summary>
            <param name="dateTime">Date and time when the last write occurred.</param>
        </member>
        <member name="M:NLog.Internal.FileAppenders.BaseFileAppender.CreateFileStream(System.Boolean)">
            <summary>
            Creates the file stream.
            </summary>
            <param name="allowConcurrentWrite">If set to <c>true</c> allow concurrent writes.</param>
            <returns>A <see cref="T:System.IO.FileStream"/> object which can be used to write to the file.</returns>
        </member>
        <member name="P:NLog.Internal.FileAppenders.BaseFileAppender.FileName">
            <summary>
            Gets the name of the file.
            </summary>
            <value>The name of the file.</value>
        </member>
        <member name="P:NLog.Internal.FileAppenders.BaseFileAppender.LastWriteTime">
            <summary>
            Gets the last write time.
            </summary>
            <value>The last write time.</value>
        </member>
        <member name="P:NLog.Internal.FileAppenders.BaseFileAppender.OpenTime">
            <summary>
            Gets the open time of the file.
            </summary>
            <value>The open time.</value>
        </member>
        <member name="P:NLog.Internal.FileAppenders.BaseFileAppender.CreateFileParameters">
            <summary>
            Gets the file creation parameters.
            </summary>
            <value>The file creation parameters.</value>
        </member>
        <member name="T:NLog.Internal.FileAppenders.CountingSingleProcessFileAppender">
            <summary>
            Implementation of <see cref="T:NLog.Internal.FileAppenders.BaseFileAppender"/> which caches 
            file information.
            </summary>
        </member>
        <member name="M:NLog.Internal.FileAppenders.CountingSingleProcessFileAppender.#ctor(System.String,NLog.Internal.FileAppenders.ICreateFileParameters)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Internal.FileAppenders.CountingSingleProcessFileAppender"/> class.
            </summary>
            <param name="fileName">Name of the file.</param>
            <param name="parameters">The parameters.</param>
        </member>
        <member name="M:NLog.Internal.FileAppenders.CountingSingleProcessFileAppender.Close">
            <summary>
            Closes this instance of the appender.
            </summary>
        </member>
        <member name="M:NLog.Internal.FileAppenders.CountingSingleProcessFileAppender.Flush">
            <summary>
            Flushes this current appender.
            </summary>
        </member>
        <member name="M:NLog.Internal.FileAppenders.CountingSingleProcessFileAppender.GetFileInfo(System.DateTime@,System.Int64@)">
            <summary>
            Gets the file info.
            </summary>
            <param name="lastWriteTime">The last write time.</param>
            <param name="fileLength">Length of the file.</param>
            <returns>True if the operation succeeded, false otherwise.</returns>
        </member>
        <member name="M:NLog.Internal.FileAppenders.CountingSingleProcessFileAppender.Write(System.Byte[])">
            <summary>
            Writes the specified bytes to a file.
            </summary>
            <param name="bytes">The bytes to be written.</param>
        </member>
        <member name="T:NLog.Internal.FileAppenders.CountingSingleProcessFileAppender.Factory">
            <summary>
            Factory class which creates <see cref="T:NLog.Internal.FileAppenders.CountingSingleProcessFileAppender"/> objects.
            </summary>
        </member>
        <member name="T:NLog.Internal.FileAppenders.IFileAppenderFactory">
            <summary>
            Interface implemented by all factories capable of creating file appenders.
            </summary>
        </member>
        <member name="M:NLog.Internal.FileAppenders.IFileAppenderFactory.Open(System.String,NLog.Internal.FileAppenders.ICreateFileParameters)">
            <summary>
            Opens the appender for given file name and parameters.
            </summary>
            <param name="fileName">Name of the file.</param>
            <param name="parameters">Creation parameters.</param>
            <returns>Instance of <see cref="T:NLog.Internal.FileAppenders.BaseFileAppender"/> which can be used to write to the file.</returns>
        </member>
        <member name="M:NLog.Internal.FileAppenders.CountingSingleProcessFileAppender.Factory.NLog#Internal#FileAppenders#IFileAppenderFactory#Open(System.String,NLog.Internal.FileAppenders.ICreateFileParameters)">
            <summary>
            Opens the appender for given file name and parameters.
            </summary>
            <param name="fileName">Name of the file.</param>
            <param name="parameters">Creation parameters.</param>
            <returns>
            Instance of <see cref="T:NLog.Internal.FileAppenders.BaseFileAppender"/> which can be used to write to the file.
            </returns>
        </member>
        <member name="T:NLog.Internal.FileAppenders.ICreateFileParameters">
            <summary>
            Interface that provides parameters for create file function.
            </summary>
        </member>
        <member name="T:NLog.Internal.FileAppenders.RetryingMultiProcessFileAppender">
            <summary>
            Multi-process and multi-host file appender which attempts
            to get exclusive write access and retries if it's not available.
            </summary>
        </member>
        <member name="M:NLog.Internal.FileAppenders.RetryingMultiProcessFileAppender.#ctor(System.String,NLog.Internal.FileAppenders.ICreateFileParameters)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Internal.FileAppenders.RetryingMultiProcessFileAppender"/> class.
            </summary>
            <param name="fileName">Name of the file.</param>
            <param name="parameters">The parameters.</param>
        </member>
        <member name="M:NLog.Internal.FileAppenders.RetryingMultiProcessFileAppender.Write(System.Byte[])">
            <summary>
            Writes the specified bytes.
            </summary>
            <param name="bytes">The bytes.</param>
        </member>
        <member name="M:NLog.Internal.FileAppenders.RetryingMultiProcessFileAppender.Flush">
            <summary>
            Flushes this instance.
            </summary>
        </member>
        <member name="M:NLog.Internal.FileAppenders.RetryingMultiProcessFileAppender.Close">
            <summary>
            Closes this instance.
            </summary>
        </member>
        <member name="M:NLog.Internal.FileAppenders.RetryingMultiProcessFileAppender.GetFileInfo(System.DateTime@,System.Int64@)">
            <summary>
            Gets the file info.
            </summary>
            <param name="lastWriteTime">The last write time.</param>
            <param name="fileLength">Length of the file.</param>
            <returns>
            True if the operation succeeded, false otherwise.
            </returns>
        </member>
        <member name="T:NLog.Internal.FileAppenders.RetryingMultiProcessFileAppender.Factory">
            <summary>
            Factory class.
            </summary>
        </member>
        <member name="M:NLog.Internal.FileAppenders.RetryingMultiProcessFileAppender.Factory.NLog#Internal#FileAppenders#IFileAppenderFactory#Open(System.String,NLog.Internal.FileAppenders.ICreateFileParameters)">
            <summary>
            Opens the appender for given file name and parameters.
            </summary>
            <param name="fileName">Name of the file.</param>
            <param name="parameters">Creation parameters.</param>
            <returns>
            Instance of <see cref="T:NLog.Internal.FileAppenders.BaseFileAppender"/> which can be used to write to the file.
            </returns>
        </member>
        <member name="T:NLog.Internal.FileAppenders.SingleProcessFileAppender">
            <summary>
            Optimized single-process file appender which keeps the file open for exclusive write.
            </summary>
        </member>
        <member name="M:NLog.Internal.FileAppenders.SingleProcessFileAppender.#ctor(System.String,NLog.Internal.FileAppenders.ICreateFileParameters)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Internal.FileAppenders.SingleProcessFileAppender"/> class.
            </summary>
            <param name="fileName">Name of the file.</param>
            <param name="parameters">The parameters.</param>
        </member>
        <member name="M:NLog.Internal.FileAppenders.SingleProcessFileAppender.Write(System.Byte[])">
            <summary>
            Writes the specified bytes.
            </summary>
            <param name="bytes">The bytes.</param>
        </member>
        <member name="M:NLog.Internal.FileAppenders.SingleProcessFileAppender.Flush">
            <summary>
            Flushes this instance.
            </summary>
        </member>
        <member name="M:NLog.Internal.FileAppenders.SingleProcessFileAppender.Close">
            <summary>
            Closes this instance.
            </summary>
        </member>
        <member name="M:NLog.Internal.FileAppenders.SingleProcessFileAppender.GetFileInfo(System.DateTime@,System.Int64@)">
            <summary>
            Gets the file info.
            </summary>
            <param name="lastWriteTime">The last write time.</param>
            <param name="fileLength">Length of the file.</param>
            <returns>
            True if the operation succeeded, false otherwise.
            </returns>
        </member>
        <member name="T:NLog.Internal.FileAppenders.SingleProcessFileAppender.Factory">
            <summary>
            Factory class.
            </summary>
        </member>
        <member name="M:NLog.Internal.FileAppenders.SingleProcessFileAppender.Factory.NLog#Internal#FileAppenders#IFileAppenderFactory#Open(System.String,NLog.Internal.FileAppenders.ICreateFileParameters)">
            <summary>
            Opens the appender for given file name and parameters.
            </summary>
            <param name="fileName">Name of the file.</param>
            <param name="parameters">Creation parameters.</param>
            <returns>
            Instance of <see cref="T:NLog.Internal.FileAppenders.BaseFileAppender"/> which can be used to write to the file.
            </returns>
        </member>
        <member name="T:NLog.Internal.FileInfoHelper">
            <summary>
            Optimized routines to get the size and last write time of the specified file.
            </summary>
        </member>
        <member name="M:NLog.Internal.FileInfoHelper.#cctor">
            <summary>
            Initializes static members of the FileInfoHelper class.
            </summary>
        </member>
        <member name="M:NLog.Internal.FileInfoHelper.GetFileInfo(System.String,System.IntPtr,System.DateTime@,System.Int64@)">
            <summary>
            Gets the information about a file.
            </summary>
            <param name="fileName">Name of the file.</param>
            <param name="fileHandle">The file handle.</param>
            <param name="lastWriteTime">The last write time of the file.</param>
            <param name="fileLength">Length of the file.</param>
            <returns>A value of <c>true</c> if file information was retrieved successfully, <c>false</c> otherwise.</returns>
        </member>
        <member name="T:NLog.Internal.IRenderable">
            <summary>
            Interface implemented by layouts and layout renderers.
            </summary>
        </member>
        <member name="M:NLog.Internal.IRenderable.Render(NLog.LogEventInfo)">
            <summary>
            Renders the the value of layout or layout renderer in the context of the specified log event.
            </summary>
            <param name="logEvent">The log event.</param>
            <returns>String representation of a layout.</returns>
        </member>
        <member name="T:NLog.Internal.ISupportsInitialize">
            <summary>
            Supports object initialization and termination.
            </summary>
        </member>
        <member name="M:NLog.Internal.ISupportsInitialize.Initialize(NLog.Config.LoggingConfiguration)">
            <summary>
            Initializes this instance.
            </summary>
            <param name="configuration">The configuration.</param>
        </member>
        <member name="M:NLog.Internal.ISupportsInitialize.Close">
            <summary>
            Closes this instance.
            </summary>
        </member>
        <member name="T:NLog.Internal.IUsesStackTrace">
            <summary>
            Allows components to request stack trace information to be provided in the <see cref="T:NLog.LogEventInfo"/>.
            </summary>
        </member>
        <member name="P:NLog.Internal.IUsesStackTrace.StackTraceUsage">
            <summary>
            Gets the level of stack trace information required by the implementing class.
            </summary>
        </member>
        <member name="T:System.ComponentModel.LocalizableAttribute">
            <summary>
            Define Localizable attribute for platforms that don't have it.
            </summary>
        </member>
        <member name="M:System.ComponentModel.LocalizableAttribute.#ctor(System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:System.ComponentModel.LocalizableAttribute"/> class.
            </summary>
            <param name="isLocalizable">Determines whether the target is localizable.</param>
        </member>
        <member name="P:System.ComponentModel.LocalizableAttribute.IsLocalizable">
            <summary>
            Gets or sets a value indicating whether the target is localizable.
            </summary>
        </member>
        <member name="T:NLog.Internal.LoggerConfiguration">
            <summary>
            Logger configuration.
            </summary>
        </member>
        <member name="M:NLog.Internal.LoggerConfiguration.#ctor(NLog.Internal.TargetWithFilterChain[])">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Internal.LoggerConfiguration"/> class.
            </summary>
            <param name="targetsByLevel">The targets by level.</param>
        </member>
        <member name="M:NLog.Internal.LoggerConfiguration.GetTargetsForLevel(NLog.LogLevel)">
            <summary>
            Gets targets for the specified level.
            </summary>
            <param name="level">The level.</param>
            <returns>Chain of targets with attached filters.</returns>
        </member>
        <member name="M:NLog.Internal.LoggerConfiguration.IsEnabled(NLog.LogLevel)">
            <summary>
            Determines whether the specified level is enabled.
            </summary>
            <param name="level">The level.</param>
            <returns>
            A value of <c>true</c> if the specified level is enabled; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="T:NLog.Internal.MessageBoxHelper">
            <summary>
            Message Box helper.
            </summary>
        </member>
        <member name="M:NLog.Internal.MessageBoxHelper.Show(System.String,System.String)">
            <summary>
            Shows the specified message using platform-specific message box.
            </summary>
            <param name="message">The message.</param>
            <param name="caption">The caption.</param>
        </member>
        <member name="T:NLog.Internal.NetworkSenders.HttpNetworkSender">
            <summary>
            Network sender which uses HTTP or HTTPS POST.
            </summary>
        </member>
        <member name="T:NLog.Internal.NetworkSenders.NetworkSender">
            <summary>
            A base class for all network senders. Supports one-way sending of messages
            over various protocols.
            </summary>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.NetworkSender.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Internal.NetworkSenders.NetworkSender"/> class.
            </summary>
            <param name="url">The network URL.</param>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.NetworkSender.Finalize">
            <summary>
            Finalizes an instance of the NetworkSender class.
            </summary>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.NetworkSender.Initialize">
            <summary>
            Initializes this network sender.
            </summary>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.NetworkSender.Close(NLog.Common.AsyncContinuation)">
            <summary>
            Closes the sender and releases any unmanaged resources.
            </summary>
            <param name="continuation">The continuation.</param>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.NetworkSender.FlushAsync(NLog.Common.AsyncContinuation)">
            <summary>
            Flushes any pending messages and invokes a continuation.
            </summary>
            <param name="continuation">The continuation.</param>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.NetworkSender.Send(System.Byte[],System.Int32,System.Int32,NLog.Common.AsyncContinuation)">
            <summary>
            Send the given text over the specified protocol.
            </summary>
            <param name="bytes">Bytes to be sent.</param>
            <param name="offset">Offset in buffer.</param>
            <param name="length">Number of bytes to send.</param>
            <param name="asyncContinuation">The asynchronous continuation.</param>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.NetworkSender.Dispose">
            <summary>
            Closes the sender and releases any unmanaged resources.
            </summary>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.NetworkSender.DoInitialize">
            <summary>
            Performs sender-specific initialization.
            </summary>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.NetworkSender.DoClose(NLog.Common.AsyncContinuation)">
            <summary>
            Performs sender-specific close operation.
            </summary>
            <param name="continuation">The continuation.</param>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.NetworkSender.DoFlush(NLog.Common.AsyncContinuation)">
            <summary>
            Performs sender-specific flush.
            </summary>
            <param name="continuation">The continuation.</param>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.NetworkSender.DoSend(System.Byte[],System.Int32,System.Int32,NLog.Common.AsyncContinuation)">
            <summary>
            Actually sends the given text over the specified protocol.
            </summary>
            <param name="bytes">The bytes to be sent.</param>
            <param name="offset">Offset in buffer.</param>
            <param name="length">Number of bytes to send.</param>
            <param name="asyncContinuation">The async continuation to be invoked after the buffer has been sent.</param>
            <remarks>To be overridden in inheriting classes.</remarks>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.NetworkSender.ParseEndpointAddress(System.Uri,System.Net.Sockets.AddressFamily)">
            <summary>
            Parses the URI into an endpoint address.
            </summary>
            <param name="uri">The URI to parse.</param>
            <param name="addressFamily">The address family.</param>
            <returns>Parsed endpoint.</returns>
        </member>
        <member name="P:NLog.Internal.NetworkSenders.NetworkSender.Address">
            <summary>
            Gets the address of the network endpoint.
            </summary>
        </member>
        <member name="P:NLog.Internal.NetworkSenders.NetworkSender.LastSendTime">
            <summary>
            Gets the last send time.
            </summary>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.HttpNetworkSender.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Internal.NetworkSenders.HttpNetworkSender"/> class.
            </summary>
            <param name="url">The network URL.</param>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.HttpNetworkSender.DoSend(System.Byte[],System.Int32,System.Int32,NLog.Common.AsyncContinuation)">
            <summary>
            Actually sends the given text over the specified protocol.
            </summary>
            <param name="bytes">The bytes to be sent.</param>
            <param name="offset">Offset in buffer.</param>
            <param name="length">Number of bytes to send.</param>
            <param name="asyncContinuation">The async continuation to be invoked after the buffer has been sent.</param>
            <remarks>To be overridden in inheriting classes.</remarks>
        </member>
        <member name="T:NLog.Internal.NetworkSenders.INetworkSenderFactory">
            <summary>
            Creates instances of <see cref="T:NLog.Internal.NetworkSenders.NetworkSender"/> objects for given URLs.
            </summary>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.INetworkSenderFactory.Create(System.String)">
            <summary>
            Creates a new instance of the network sender based on a network URL.
            </summary>
            <param name="url">
            URL that determines the network sender to be created.
            </param>
            <returns>
            A newly created network sender.
            </returns>
        </member>
        <member name="T:NLog.Internal.NetworkSenders.ISocket">
            <summary>
            Interface for mocking socket calls.
            </summary>
        </member>
        <member name="T:NLog.Internal.NetworkSenders.NetworkSenderFactory">
            <summary>
            Default implementation of <see cref="T:NLog.Internal.NetworkSenders.INetworkSenderFactory"/>.
            </summary>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.NetworkSenderFactory.Create(System.String)">
            <summary>
            Creates a new instance of the network sender based on a network URL:.
            </summary>
            <param name="url">
            URL that determines the network sender to be created.
            </param>
            <returns>
            A newly created network sender.
            </returns>
        </member>
        <member name="T:NLog.Internal.NetworkSenders.SocketProxy">
            <summary>
            Socket proxy for mocking Socket code.
            </summary>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.SocketProxy.#ctor(System.Net.Sockets.AddressFamily,System.Net.Sockets.SocketType,System.Net.Sockets.ProtocolType)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Internal.NetworkSenders.SocketProxy"/> class.
            </summary>
            <param name="addressFamily">The address family.</param>
            <param name="socketType">Type of the socket.</param>
            <param name="protocolType">Type of the protocol.</param>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.SocketProxy.Close">
            <summary>
            Closes the wrapped socket.
            </summary>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.SocketProxy.ConnectAsync(System.Net.Sockets.SocketAsyncEventArgs)">
            <summary>
            Invokes ConnectAsync method on the wrapped socket.
            </summary>
            <param name="args">The <see cref="T:System.Net.Sockets.SocketAsyncEventArgs"/> instance containing the event data.</param>
            <returns>Result of original method.</returns>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.SocketProxy.SendAsync(System.Net.Sockets.SocketAsyncEventArgs)">
            <summary>
            Invokes SendAsync method on the wrapped socket.
            </summary>
            <param name="args">The <see cref="T:System.Net.Sockets.SocketAsyncEventArgs"/> instance containing the event data.</param>
            <returns>Result of original method.</returns>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.SocketProxy.SendToAsync(System.Net.Sockets.SocketAsyncEventArgs)">
            <summary>
            Invokes SendToAsync method on the wrapped socket.
            </summary>
            <param name="args">The <see cref="T:System.Net.Sockets.SocketAsyncEventArgs"/> instance containing the event data.</param>
            <returns>Result of original method.</returns>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.SocketProxy.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="T:NLog.Internal.NetworkSenders.TcpNetworkSender">
            <summary>
            Sends messages over a TCP network connection.
            </summary>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.TcpNetworkSender.#ctor(System.String,System.Net.Sockets.AddressFamily)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Internal.NetworkSenders.TcpNetworkSender"/> class.
            </summary>
            <param name="url">URL. Must start with tcp://.</param>
            <param name="addressFamily">The address family.</param>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.TcpNetworkSender.CreateSocket(System.Net.Sockets.AddressFamily,System.Net.Sockets.SocketType,System.Net.Sockets.ProtocolType)">
            <summary>
            Creates the socket with given parameters. 
            </summary>
            <param name="addressFamily">The address family.</param>
            <param name="socketType">Type of the socket.</param>
            <param name="protocolType">Type of the protocol.</param>
            <returns>Instance of <see cref="T:NLog.Internal.NetworkSenders.ISocket"/> which represents the socket.</returns>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.TcpNetworkSender.DoInitialize">
            <summary>
            Performs sender-specific initialization.
            </summary>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.TcpNetworkSender.DoClose(NLog.Common.AsyncContinuation)">
            <summary>
            Closes the socket.
            </summary>
            <param name="continuation">The continuation.</param>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.TcpNetworkSender.DoFlush(NLog.Common.AsyncContinuation)">
            <summary>
            Performs sender-specific flush.
            </summary>
            <param name="continuation">The continuation.</param>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.TcpNetworkSender.DoSend(System.Byte[],System.Int32,System.Int32,NLog.Common.AsyncContinuation)">
            <summary>
            Sends the specified text over the connected socket.
            </summary>
            <param name="bytes">The bytes to be sent.</param>
            <param name="offset">Offset in buffer.</param>
            <param name="length">Number of bytes to send.</param>
            <param name="asyncContinuation">The async continuation to be invoked after the buffer has been sent.</param>
            <remarks>To be overridden in inheriting classes.</remarks>
        </member>
        <member name="T:NLog.Internal.NetworkSenders.TcpNetworkSender.MySocketAsyncEventArgs">
            <summary>
            Facilitates mocking of <see cref="T:System.Net.Sockets.SocketAsyncEventArgs"/> class.
            </summary>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.TcpNetworkSender.MySocketAsyncEventArgs.RaiseCompleted">
            <summary>
            Raises the Completed event.
            </summary>
        </member>
        <member name="T:NLog.Internal.NetworkSenders.UdpNetworkSender">
            <summary>
            Sends messages over the network as UDP datagrams.
            </summary>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.UdpNetworkSender.#ctor(System.String,System.Net.Sockets.AddressFamily)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Internal.NetworkSenders.UdpNetworkSender"/> class.
            </summary>
            <param name="url">URL. Must start with udp://.</param>
            <param name="addressFamily">The address family.</param>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.UdpNetworkSender.CreateSocket(System.Net.Sockets.AddressFamily,System.Net.Sockets.SocketType,System.Net.Sockets.ProtocolType)">
            <summary>
            Creates the socket.
            </summary>
            <param name="addressFamily">The address family.</param>
            <param name="socketType">Type of the socket.</param>
            <param name="protocolType">Type of the protocol.</param>
            <returns>Implementation of <see cref="T:NLog.Internal.NetworkSenders.ISocket"/> to use.</returns>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.UdpNetworkSender.DoInitialize">
            <summary>
            Performs sender-specific initialization.
            </summary>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.UdpNetworkSender.DoClose(NLog.Common.AsyncContinuation)">
            <summary>
            Closes the socket.
            </summary>
            <param name="continuation">The continuation.</param>
        </member>
        <member name="M:NLog.Internal.NetworkSenders.UdpNetworkSender.DoSend(System.Byte[],System.Int32,System.Int32,NLog.Common.AsyncContinuation)">
            <summary>
            Sends the specified text as a UDP datagram.
            </summary>
            <param name="bytes">The bytes to be sent.</param>
            <param name="offset">Offset in buffer.</param>
            <param name="length">Number of bytes to send.</param>
            <param name="asyncContinuation">The async continuation to be invoked after the buffer has been sent.</param>
            <remarks>To be overridden in inheriting classes.</remarks>
        </member>
        <member name="T:NLog.Internal.ObjectGraphScanner">
            <summary>
            Scans (breadth-first) the object graph following all the edges whose are 
            instances have <see cref="T:NLog.Config.NLogConfigurationItemAttribute"/> attached and returns 
            all objects implementing a specified interfaces.
            </summary>
        </member>
        <member name="M:NLog.Internal.ObjectGraphScanner.FindReachableObjects``1(System.Object[])">
            <summary>
            Finds the objects which have attached <see cref="T:NLog.Config.NLogConfigurationItemAttribute"/> which are reachable
            from any of the given root objects when traversing the object graph over public properties.
            </summary>
            <typeparam name="T">Type of the objects to return.</typeparam>
            <param name="rootObjects">The root objects.</param>
            <returns>Ordered list of objects implementing T.</returns>
        </member>
        <member name="T:NLog.Internal.ParameterUtils">
            <summary>
            Parameter validation utilities.
            </summary>
        </member>
        <member name="M:NLog.Internal.ParameterUtils.AssertNotNull(System.Object,System.String)">
            <summary>
            Asserts that the value is not null and throws <see cref="T:System.ArgumentNullException"/> otherwise.
            </summary>
            <param name="value">The value to check.</param>
            <param name="parameterName">Name of the parameter.</param>
        </member>
        <member name="T:NLog.Internal.PlatformDetector">
            <summary>
            Detects the platform the NLog is running on.
            </summary>
        </member>
        <member name="P:NLog.Internal.PlatformDetector.CurrentOS">
            <summary>
            Gets the current runtime OS.
            </summary>
        </member>
        <member name="P:NLog.Internal.PlatformDetector.IsDesktopWin32">
            <summary>
            Gets a value indicating whether current OS is a desktop version of Windows.
            </summary>
        </member>
        <member name="P:NLog.Internal.PlatformDetector.IsWin32">
            <summary>
            Gets a value indicating whether current OS is Win32-based (desktop or mobile).
            </summary>
        </member>
        <member name="P:NLog.Internal.PlatformDetector.IsUnix">
            <summary>
            Gets a value indicating whether current OS is Unix-based.
            </summary>
        </member>
        <member name="T:NLog.Internal.PortableFileInfoHelper">
            <summary>
            Portable implementation of <see cref="T:NLog.Internal.FileInfoHelper"/>.
            </summary>
        </member>
        <member name="M:NLog.Internal.PortableFileInfoHelper.GetFileInfo(System.String,System.IntPtr,System.DateTime@,System.Int64@)">
            <summary>
            Gets the information about a file.
            </summary>
            <param name="fileName">Name of the file.</param>
            <param name="fileHandle">The file handle.</param>
            <param name="lastWriteTime">The last write time of the file.</param>
            <param name="fileLength">Length of the file.</param>
            <returns>
            A value of <c>true</c> if file information was retrieved successfully, <c>false</c> otherwise.
            </returns>
        </member>
        <member name="T:NLog.Internal.PropertyHelper">
            <summary>
            Reflection helpers for accessing properties.
            </summary>
        </member>
        <member name="T:NLog.Internal.ReflectionHelpers">
            <summary>
            Reflection helpers.
            </summary>
        </member>
        <member name="M:NLog.Internal.ReflectionHelpers.SafeGetTypes(System.Reflection.Assembly)">
            <summary>
            Gets all usable exported types from the given assembly.
            </summary>
            <param name="assembly">Assembly to scan.</param>
            <returns>Usable types from the given assembly.</returns>
            <remarks>Types which cannot be loaded are skipped.</remarks>
        </member>
        <member name="T:NLog.Internal.RuntimeOS">
            <summary>
            Supported operating systems.
            </summary>
            <remarks>
            If you add anything here, make sure to add the appropriate detection
            code to <see cref="T:NLog.Internal.PlatformDetector"/>
            </remarks>
        </member>
        <member name="F:NLog.Internal.RuntimeOS.Any">
            <summary>
            Any operating system.
            </summary>
        </member>
        <member name="F:NLog.Internal.RuntimeOS.Unix">
            <summary>
            Unix/Linux operating systems.
            </summary>
        </member>
        <member name="F:NLog.Internal.RuntimeOS.WindowsCE">
            <summary>
            Windows CE.
            </summary>
        </member>
        <member name="F:NLog.Internal.RuntimeOS.Windows">
            <summary>
            Desktop versions of Windows (95,98,ME).
            </summary>
        </member>
        <member name="F:NLog.Internal.RuntimeOS.WindowsNT">
            <summary>
            Windows NT, 2000, 2003 and future versions based on NT technology.
            </summary>
        </member>
        <member name="F:NLog.Internal.RuntimeOS.Unknown">
            <summary>
            Unknown operating system.
            </summary>
        </member>
        <member name="T:NLog.Internal.SimpleStringReader">
            <summary>
            Simple character tokenizer.
            </summary>
        </member>
        <member name="M:NLog.Internal.SimpleStringReader.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Internal.SimpleStringReader"/> class.
            </summary>
            <param name="text">The text to be tokenized.</param>
        </member>
        <member name="T:NLog.Internal.SingleCallContinuation">
            <summary>
            Implements a single-call guard around given continuation function.
            </summary>
        </member>
        <member name="M:NLog.Internal.SingleCallContinuation.#ctor(NLog.Common.AsyncContinuation)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Internal.SingleCallContinuation"/> class.
            </summary>
            <param name="asyncContinuation">The asynchronous continuation.</param>
        </member>
        <member name="M:NLog.Internal.SingleCallContinuation.Function(System.Exception)">
            <summary>
            Continuation function which implements the single-call guard.
            </summary>
            <param name="exception">The exception.</param>
        </member>
        <member name="T:NLog.Internal.SortHelpers">
            <summary>
            Provides helpers to sort log events and associated continuations.
            </summary>
        </member>
        <member name="M:NLog.Internal.SortHelpers.BucketSort``2(System.Collections.Generic.IEnumerable{``0},NLog.Internal.SortHelpers.KeySelector{``0,``1})">
            <summary>
            Performs bucket sort (group by) on an array of items and returns a dictionary for easy traversal of the result set.
            </summary>
            <typeparam name="TValue">The type of the value.</typeparam>
            <typeparam name="TKey">The type of the key.</typeparam>
            <param name="inputs">The inputs.</param>
            <param name="keySelector">The key selector function.</param>
            <returns>
            Dictonary where keys are unique input keys, and values are lists of <see cref="T:NLog.Common.AsyncLogEventInfo"/>.
            </returns>
        </member>
        <member name="T:NLog.Internal.SortHelpers.KeySelector`2">
            <summary>
            Key selector delegate.
            </summary>
            <typeparam name="TValue">The type of the value.</typeparam>
            <typeparam name="TKey">The type of the key.</typeparam>
            <param name="value">Value to extract key information from.</param>
            <returns>Key selected from log event.</returns>
        </member>
        <member name="T:NLog.Internal.StackTraceUsageUtils">
            <summary>
            Utilities for dealing with <see cref="T:NLog.Config.StackTraceUsage"/> values.
            </summary>
        </member>
        <member name="T:NLog.Internal.TargetWithFilterChain">
            <summary>
            Represents target with a chain of filters which determine
            whether logging should happen.
            </summary>
        </member>
        <member name="M:NLog.Internal.TargetWithFilterChain.#ctor(NLog.Targets.Target,System.Collections.Generic.IList{NLog.Filters.Filter})">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Internal.TargetWithFilterChain"/> class.
            </summary>
            <param name="target">The target.</param>
            <param name="filterChain">The filter chain.</param>
        </member>
        <member name="M:NLog.Internal.TargetWithFilterChain.GetStackTraceUsage">
            <summary>
            Gets the stack trace usage.
            </summary>
            <returns>A <see cref="T:NLog.Config.StackTraceUsage"/> value that determines stack trace handling.</returns>
        </member>
        <member name="P:NLog.Internal.TargetWithFilterChain.Target">
            <summary>
            Gets the target.
            </summary>
            <value>The target.</value>
        </member>
        <member name="P:NLog.Internal.TargetWithFilterChain.FilterChain">
            <summary>
            Gets the filter chain.
            </summary>
            <value>The filter chain.</value>
        </member>
        <member name="P:NLog.Internal.TargetWithFilterChain.NextInChain">
            <summary>
            Gets or sets the next <see cref="T:NLog.Internal.TargetWithFilterChain"/> item in the chain.
            </summary>
            <value>The next item in the chain.</value>
        </member>
        <member name="T:NLog.Internal.ThreadLocalStorageHelper">
            <summary>
            Helper for dealing with thread-local storage.
            </summary>
        </member>
        <member name="M:NLog.Internal.ThreadLocalStorageHelper.AllocateDataSlot">
            <summary>
            Allocates the data slot for storing thread-local information.
            </summary>
            <returns>Allocated slot key.</returns>
        </member>
        <member name="M:NLog.Internal.ThreadLocalStorageHelper.GetDataForSlot``1(System.Object)">
            <summary>
            Gets the data for a slot in thread-local storage.
            </summary>
            <typeparam name="T">Type of the data.</typeparam>
            <param name="slot">The slot to get data for.</param>
            <returns>
            Slot data (will create T if null).
            </returns>
        </member>
        <member name="T:NLog.Internal.TimeoutContinuation">
            <summary>
            Wraps <see cref="T:NLog.Common.AsyncContinuation"/> with a timeout.
            </summary>
        </member>
        <member name="M:NLog.Internal.TimeoutContinuation.#ctor(NLog.Common.AsyncContinuation,System.TimeSpan)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Internal.TimeoutContinuation"/> class.
            </summary>
            <param name="asyncContinuation">The asynchronous continuation.</param>
            <param name="timeout">The timeout.</param>
        </member>
        <member name="M:NLog.Internal.TimeoutContinuation.Function(System.Exception)">
            <summary>
            Continuation function which implements the timeout logic.
            </summary>
            <param name="exception">The exception.</param>
        </member>
        <member name="M:NLog.Internal.TimeoutContinuation.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="T:NLog.Internal.UrlHelper">
            <summary>
            URL Encoding helper.
            </summary>
        </member>
        <member name="T:NLog.LayoutRenderers.AmbientPropertyAttribute">
            <summary>
            Designates a property of the class as an ambient property.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.AmbientPropertyAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LayoutRenderers.AmbientPropertyAttribute"/> class.
            </summary>
            <param name="name">Ambient property name.</param>
        </member>
        <member name="T:NLog.LayoutRenderers.CallSiteLayoutRenderer">
            <summary>
            The call site (class name, method name and source information).
            </summary>
        </member>
        <member name="T:NLog.LayoutRenderers.LayoutRenderer">
            <summary>
            Render environmental information related to logging events.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.LayoutRenderer.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:NLog.LayoutRenderers.LayoutRenderer.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.LayoutRenderer.Render(NLog.LogEventInfo)">
            <summary>
            Renders the the value of layout renderer in the context of the specified log event.
            </summary>
            <param name="logEvent">The log event.</param>
            <returns>String representation of a layout renderer.</returns>
        </member>
        <member name="M:NLog.LayoutRenderers.LayoutRenderer.NLog#Internal#ISupportsInitialize#Initialize(NLog.Config.LoggingConfiguration)">
            <summary>
            Initializes this instance.
            </summary>
            <param name="configuration">The configuration.</param>
        </member>
        <member name="M:NLog.LayoutRenderers.LayoutRenderer.NLog#Internal#ISupportsInitialize#Close">
            <summary>
            Closes this instance.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.LayoutRenderer.Initialize(NLog.Config.LoggingConfiguration)">
            <summary>
            Initializes this instance.
            </summary>
            <param name="configuration">The configuration.</param>
        </member>
        <member name="M:NLog.LayoutRenderers.LayoutRenderer.Close">
            <summary>
            Closes this instance.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.LayoutRenderer.Append(System.Text.StringBuilder,NLog.LogEventInfo)">
            <summary>
            Renders the specified environmental information and appends it to the specified <see cref="T:System.Text.StringBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:System.Text.StringBuilder"/> to append the rendered data to.</param>
            <param name="logEvent">Logging event.</param>
        </member>
        <member name="M:NLog.LayoutRenderers.LayoutRenderer.InitializeLayoutRenderer">
            <summary>
            Initializes the layout renderer.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.LayoutRenderer.CloseLayoutRenderer">
            <summary>
            Closes the layout renderer.
            </summary>      
        </member>
        <member name="M:NLog.LayoutRenderers.LayoutRenderer.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
            <param name="disposing">True to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="P:NLog.LayoutRenderers.LayoutRenderer.LoggingConfiguration">
            <summary>
            Gets the logging configuration this target is part of.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.CallSiteLayoutRenderer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LayoutRenderers.CallSiteLayoutRenderer"/> class.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.CallSiteLayoutRenderer.Append(System.Text.StringBuilder,NLog.LogEventInfo)">
            <summary>
            Renders the call site and appends it to the specified <see cref="T:System.Text.StringBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:System.Text.StringBuilder"/> to append the rendered data to.</param>
            <param name="logEvent">Logging event.</param>
        </member>
        <member name="P:NLog.LayoutRenderers.CallSiteLayoutRenderer.ClassName">
            <summary>
            Gets or sets a value indicating whether to render the class name.
            </summary>
            <docgen category='Rendering Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.CallSiteLayoutRenderer.MethodName">
            <summary>
            Gets or sets a value indicating whether to render the method name.
            </summary>
            <docgen category='Rendering Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.CallSiteLayoutRenderer.NLog#Internal#IUsesStackTrace#StackTraceUsage">
            <summary>
            Gets the level of stack trace information required by the implementing class.
            </summary>
        </member>
        <member name="T:NLog.LayoutRenderers.CounterLayoutRenderer">
            <summary>
            A counter value (increases on each layout rendering).
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.CounterLayoutRenderer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LayoutRenderers.CounterLayoutRenderer"/> class.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.CounterLayoutRenderer.Append(System.Text.StringBuilder,NLog.LogEventInfo)">
            <summary>
            Renders the specified counter value and appends it to the specified <see cref="T:System.Text.StringBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:System.Text.StringBuilder"/> to append the rendered data to.</param>
            <param name="logEvent">Logging event.</param>
        </member>
        <member name="P:NLog.LayoutRenderers.CounterLayoutRenderer.Value">
            <summary>
            Gets or sets the initial value of the counter.
            </summary>
            <docgen category='Counter Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.CounterLayoutRenderer.Increment">
            <summary>
            Gets or sets the value to be added to the counter after each layout rendering.
            </summary>
            <docgen category='Counter Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.CounterLayoutRenderer.Sequence">
            <summary>
            Gets or sets the name of the sequence. Different named sequences can have individual values.
            </summary>
            <docgen category='Counter Options' order='10' />
        </member>
        <member name="T:NLog.LayoutRenderers.DateLayoutRenderer">
            <summary>
            Current date and time.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.DateLayoutRenderer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LayoutRenderers.DateLayoutRenderer"/> class.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.DateLayoutRenderer.Append(System.Text.StringBuilder,NLog.LogEventInfo)">
            <summary>
            Renders the current date and appends it to the specified <see cref="T:System.Text.StringBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:System.Text.StringBuilder"/> to append the rendered data to.</param>
            <param name="logEvent">Logging event.</param>
        </member>
        <member name="P:NLog.LayoutRenderers.DateLayoutRenderer.Culture">
            <summary>
            Gets or sets the culture used for rendering. 
            </summary>
            <docgen category='Rendering Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.DateLayoutRenderer.Format">
            <summary>
            Gets or sets the date format. Can be any argument accepted by DateTime.ToString(format).
            </summary>
            <docgen category='Rendering Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.DateLayoutRenderer.UniversalTime">
            <summary>
            Gets or sets a value indicating whether to output UTC time instead of local time.
            </summary>
            <docgen category='Rendering Options' order='10' />
        </member>
        <member name="T:NLog.LayoutRenderers.EventContextLayoutRenderer">
            <summary>
            Log event context data.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.EventContextLayoutRenderer.Append(System.Text.StringBuilder,NLog.LogEventInfo)">
            <summary>
            Renders the specified log event context item and appends it to the specified <see cref="T:System.Text.StringBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:System.Text.StringBuilder"/> to append the rendered data to.</param>
            <param name="logEvent">Logging event.</param>
        </member>
        <member name="P:NLog.LayoutRenderers.EventContextLayoutRenderer.Item">
            <summary>
            Gets or sets the name of the item.
            </summary>
            <docgen category='Rendering Options' order='10' />
        </member>
        <member name="T:NLog.LayoutRenderers.ExceptionLayoutRenderer">
            <summary>
            Exception information provided through 
            a call to one of the Logger.*Exception() methods.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.ExceptionLayoutRenderer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LayoutRenderers.ExceptionLayoutRenderer"/> class.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.ExceptionLayoutRenderer.Append(System.Text.StringBuilder,NLog.LogEventInfo)">
            <summary>
            Renders the specified exception information and appends it to the specified <see cref="T:System.Text.StringBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:System.Text.StringBuilder"/> to append the rendered data to.</param>
            <param name="logEvent">Logging event.</param>
        </member>
        <member name="P:NLog.LayoutRenderers.ExceptionLayoutRenderer.Format">
            <summary>
            Gets or sets the format of the output. Must be a comma-separated list of exception
            properties: Message, Type, ShortType, ToString, Method, StackTrace.
            This parameter value is case-insensitive.
            </summary>
            <docgen category='Rendering Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.ExceptionLayoutRenderer.InnerFormat">
            <summary>
            Gets or sets the format of the output of inner exceptions. Must be a comma-separated list of exception
            properties: Message, Type, ShortType, ToString, Method, StackTrace.
            This parameter value is case-insensitive.
            </summary>
            <docgen category='Rendering Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.ExceptionLayoutRenderer.Separator">
            <summary>
            Gets or sets the separator used to concatenate parts specified in the Format.
            </summary>
            <docgen category='Rendering Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.ExceptionLayoutRenderer.MaxInnerExceptionLevel">
            <summary>
            Gets or sets the maximum number of inner exceptions to include in the output.
            By default inner exceptions are not enabled for compatibility with NLog 1.0.
            </summary>
            <docgen category='Rendering Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.ExceptionLayoutRenderer.InnerExceptionSeparator">
            <summary>
            Gets or sets the separator between inner exceptions.
            </summary>
            <docgen category='Rendering Options' order='10' />
        </member>
        <member name="T:NLog.LayoutRenderers.FileContentsLayoutRenderer">
            <summary>
            Renders contents of the specified file.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.FileContentsLayoutRenderer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LayoutRenderers.FileContentsLayoutRenderer"/> class.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.FileContentsLayoutRenderer.Append(System.Text.StringBuilder,NLog.LogEventInfo)">
            <summary>
            Renders the contents of the specified file and appends it to the specified <see cref="T:System.Text.StringBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:System.Text.StringBuilder"/> to append the rendered data to.</param>
            <param name="logEvent">Logging event.</param>
        </member>
        <member name="P:NLog.LayoutRenderers.FileContentsLayoutRenderer.FileName">
            <summary>
            Gets or sets the name of the file.
            </summary>
            <docgen category='File Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.FileContentsLayoutRenderer.Encoding">
            <summary>
            Gets or sets the encoding used in the file.
            </summary>
            <value>The encoding.</value>
            <docgen category='File Options' order='10' />
        </member>
        <member name="T:NLog.LayoutRenderers.GarbageCollectorInfoLayoutRenderer">
            <summary>
            The information about the garbage collector.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.GarbageCollectorInfoLayoutRenderer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LayoutRenderers.GarbageCollectorInfoLayoutRenderer"/> class.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.GarbageCollectorInfoLayoutRenderer.Append(System.Text.StringBuilder,NLog.LogEventInfo)">
            <summary>
            Renders the selected process information.
            </summary>
            <param name="builder">The <see cref="T:System.Text.StringBuilder"/> to append the rendered data to.</param>
            <param name="logEvent">Logging event.</param>
        </member>
        <member name="P:NLog.LayoutRenderers.GarbageCollectorInfoLayoutRenderer.Property">
            <summary>
            Gets or sets the property to retrieve.
            </summary>
            <docgen category='Rendering Options' order='10' />
        </member>
        <member name="T:NLog.LayoutRenderers.GarbageCollectorProperty">
            <summary>
            Gets or sets the property of System.GC to retrieve.
            </summary>
        </member>
        <member name="F:NLog.LayoutRenderers.GarbageCollectorProperty.TotalMemory">
            <summary>
            Total memory allocated.
            </summary>
        </member>
        <member name="F:NLog.LayoutRenderers.GarbageCollectorProperty.TotalMemoryForceCollection">
            <summary>
            Total memory allocated (perform full garbage collection first).
            </summary>
        </member>
        <member name="F:NLog.LayoutRenderers.GarbageCollectorProperty.CollectionCount0">
            <summary>
            Gets the number of Gen0 collections.
            </summary>
        </member>
        <member name="F:NLog.LayoutRenderers.GarbageCollectorProperty.CollectionCount1">
            <summary>
            Gets the number of Gen1 collections.
            </summary>
        </member>
        <member name="F:NLog.LayoutRenderers.GarbageCollectorProperty.CollectionCount2">
            <summary>
            Gets the number of Gen2 collections.
            </summary>
        </member>
        <member name="F:NLog.LayoutRenderers.GarbageCollectorProperty.MaxGeneration">
            <summary>
            Maximum generation number supported by GC.
            </summary>
        </member>
        <member name="T:NLog.LayoutRenderers.GdcLayoutRenderer">
            <summary>
            Global Diagnostics Context item. Provided for compatibility with log4net.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.GdcLayoutRenderer.Append(System.Text.StringBuilder,NLog.LogEventInfo)">
            <summary>
            Renders the specified Global Diagnostics Context item and appends it to the specified <see cref="T:System.Text.StringBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:System.Text.StringBuilder"/> to append the rendered data to.</param>
            <param name="logEvent">Logging event.</param>
        </member>
        <member name="P:NLog.LayoutRenderers.GdcLayoutRenderer.Item">
            <summary>
            Gets or sets the name of the item.
            </summary>
            <docgen category='Rendering Options' order='10' />
        </member>
        <member name="T:NLog.LayoutRenderers.GuidLayoutRenderer">
            <summary>
            Globally-unique identifier (GUID).
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.GuidLayoutRenderer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LayoutRenderers.GuidLayoutRenderer"/> class.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.GuidLayoutRenderer.Append(System.Text.StringBuilder,NLog.LogEventInfo)">
            <summary>
            Renders a newly generated GUID string and appends it to the specified <see cref="T:System.Text.StringBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:System.Text.StringBuilder"/> to append the rendered data to.</param>
            <param name="logEvent">Logging event.</param>
        </member>
        <member name="P:NLog.LayoutRenderers.GuidLayoutRenderer.Format">
            <summary>
            Gets or sets the GUID format as accepted by Guid.ToString() method.
            </summary>
            <docgen category='Rendering Options' order='10' />
        </member>
        <member name="T:NLog.LayoutRenderers.InstallContextLayoutRenderer">
            <summary>
            Installation parameter (passed to InstallNLogConfig).
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.InstallContextLayoutRenderer.Append(System.Text.StringBuilder,NLog.LogEventInfo)">
            <summary>
            Renders the specified installation parameter and appends it to the specified <see cref="T:System.Text.StringBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:System.Text.StringBuilder"/> to append the rendered data to.</param>
            <param name="logEvent">Logging event.</param>
        </member>
        <member name="P:NLog.LayoutRenderers.InstallContextLayoutRenderer.Parameter">
            <summary>
            Gets or sets the name of the parameter.
            </summary>
            <docgen category='Rendering Options' order='10' />
        </member>
        <member name="T:NLog.LayoutRenderers.LayoutRendererAttribute">
            <summary>
            Marks class as a layout renderer and assigns a format string to it.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.LayoutRendererAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LayoutRenderers.LayoutRendererAttribute"/> class.
            </summary>
            <param name="name">Name of the layout renderer.</param>
        </member>
        <member name="T:NLog.LayoutRenderers.LevelLayoutRenderer">
            <summary>
            The log level.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.LevelLayoutRenderer.Append(System.Text.StringBuilder,NLog.LogEventInfo)">
            <summary>
            Renders the current log level and appends it to the specified <see cref="T:System.Text.StringBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:System.Text.StringBuilder"/> to append the rendered data to.</param>
            <param name="logEvent">Logging event.</param>
        </member>
        <member name="T:NLog.LayoutRenderers.LiteralLayoutRenderer">
            <summary>
            A string literal.
            </summary>
            <remarks>
            This is used to escape '${' sequence 
            as ;${literal:text=${}'
            </remarks>
        </member>
        <member name="M:NLog.LayoutRenderers.LiteralLayoutRenderer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LayoutRenderers.LiteralLayoutRenderer"/> class.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.LiteralLayoutRenderer.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LayoutRenderers.LiteralLayoutRenderer"/> class.
            </summary>
            <param name="text">The literal text value.</param>
            <remarks>This is used by the layout compiler.</remarks>
        </member>
        <member name="M:NLog.LayoutRenderers.LiteralLayoutRenderer.Append(System.Text.StringBuilder,NLog.LogEventInfo)">
            <summary>
            Renders the specified string literal and appends it to the specified <see cref="T:System.Text.StringBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:System.Text.StringBuilder"/> to append the rendered data to.</param>
            <param name="logEvent">Logging event.</param>
        </member>
        <member name="P:NLog.LayoutRenderers.LiteralLayoutRenderer.Text">
            <summary>
            Gets or sets the literal text.
            </summary>
            <docgen category='Rendering Options' order='10' />
        </member>
        <member name="T:NLog.LayoutRenderers.Log4JXmlEventLayoutRenderer">
            <summary>
            XML event description compatible with log4j, Chainsaw and NLogViewer.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.Log4JXmlEventLayoutRenderer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LayoutRenderers.Log4JXmlEventLayoutRenderer"/> class.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.Log4JXmlEventLayoutRenderer.Append(System.Text.StringBuilder,NLog.LogEventInfo)">
            <summary>
            Renders the XML logging event and appends it to the specified <see cref="T:System.Text.StringBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:System.Text.StringBuilder"/> to append the rendered data to.</param>
            <param name="logEvent">Logging event.</param>
        </member>
        <member name="P:NLog.LayoutRenderers.Log4JXmlEventLayoutRenderer.IncludeNLogData">
            <summary>
            Gets or sets a value indicating whether to include NLog-specific extensions to log4j schema.
            </summary>
            <docgen category='Payload Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.Log4JXmlEventLayoutRenderer.IndentXml">
            <summary>
            Gets or sets a value indicating whether the XML should use spaces for indentation.
            </summary>
            <docgen category='Payload Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.Log4JXmlEventLayoutRenderer.AppInfo">
            <summary>
            Gets or sets the AppInfo field. By default it's the friendly name of the current AppDomain.
            </summary>
            <docgen category='Payload Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.Log4JXmlEventLayoutRenderer.IncludeCallSite">
            <summary>
            Gets or sets a value indicating whether to include call site (class and method name) in the information sent over the network.
            </summary>
            <docgen category='Payload Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.Log4JXmlEventLayoutRenderer.IncludeSourceInfo">
            <summary>
            Gets or sets a value indicating whether to include source info (file name and line number) in the information sent over the network.
            </summary>
            <docgen category='Payload Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.Log4JXmlEventLayoutRenderer.IncludeMdc">
            <summary>
            Gets or sets a value indicating whether to include contents of the <see cref="T:NLog.MappedDiagnosticsContext"/> dictionary.
            </summary>
            <docgen category="Payload Options" order="10"/>
        </member>
        <member name="P:NLog.LayoutRenderers.Log4JXmlEventLayoutRenderer.IncludeNdc">
            <summary>
            Gets or sets a value indicating whether to include contents of the <see cref="T:NLog.NestedDiagnosticsContext"/> stack.
            </summary>
            <docgen category="Payload Options" order="10"/>
        </member>
        <member name="P:NLog.LayoutRenderers.Log4JXmlEventLayoutRenderer.NdcItemSeparator">
            <summary>
            Gets or sets the NDC item separator.
            </summary>
            <docgen category='Payload Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.Log4JXmlEventLayoutRenderer.NLog#Internal#IUsesStackTrace#StackTraceUsage">
            <summary>
            Gets the level of stack trace information required by the implementing class.
            </summary>
        </member>
        <member name="T:NLog.LayoutRenderers.LoggerNameLayoutRenderer">
            <summary>
            The logger name.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.LoggerNameLayoutRenderer.Append(System.Text.StringBuilder,NLog.LogEventInfo)">
            <summary>
            Renders the logger name and appends it to the specified <see cref="T:System.Text.StringBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:System.Text.StringBuilder"/> to append the rendered data to.</param>
            <param name="logEvent">Logging event.</param>
        </member>
        <member name="P:NLog.LayoutRenderers.LoggerNameLayoutRenderer.ShortName">
            <summary>
            Gets or sets a value indicating whether to render short logger name (the part after the trailing dot character).
            </summary>
            <docgen category='Rendering Options' order='10' />
        </member>
        <member name="T:NLog.LayoutRenderers.LongDateLayoutRenderer">
            <summary>
            The date and time in a long, sortable format yyyy-MM-dd HH:mm:ss.mmm.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.LongDateLayoutRenderer.Append(System.Text.StringBuilder,NLog.LogEventInfo)">
            <summary>
            Renders the date in the long format (yyyy-MM-dd HH:mm:ss.mmm) and appends it to the specified <see cref="T:System.Text.StringBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:System.Text.StringBuilder"/> to append the rendered data to.</param>
            <param name="logEvent">Logging event.</param>
        </member>
        <member name="P:NLog.LayoutRenderers.LongDateLayoutRenderer.UniversalTime">
            <summary>
            Gets or sets a value indicating whether to output UTC time instead of local time.
            </summary>
            <docgen category='Rendering Options' order='10' />
        </member>
        <member name="T:NLog.LayoutRenderers.MdcLayoutRenderer">
            <summary>
            Mapped Diagnostic Context item. Provided for compatibility with log4net.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.MdcLayoutRenderer.Append(System.Text.StringBuilder,NLog.LogEventInfo)">
            <summary>
            Renders the specified MDC item and appends it to the specified <see cref="T:System.Text.StringBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:System.Text.StringBuilder"/> to append the rendered data to.</param>
            <param name="logEvent">Logging event.</param>
        </member>
        <member name="P:NLog.LayoutRenderers.MdcLayoutRenderer.Item">
            <summary>
            Gets or sets the name of the item.
            </summary>
            <docgen category='Rendering Options' order='10' />
        </member>
        <member name="T:NLog.LayoutRenderers.MessageLayoutRenderer">
            <summary>
            The formatted log message.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.MessageLayoutRenderer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LayoutRenderers.MessageLayoutRenderer"/> class.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.MessageLayoutRenderer.Append(System.Text.StringBuilder,NLog.LogEventInfo)">
            <summary>
            Renders the log message including any positional parameters and appends it to the specified <see cref="T:System.Text.StringBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:System.Text.StringBuilder"/> to append the rendered data to.</param>
            <param name="logEvent">Logging event.</param>
        </member>
        <member name="P:NLog.LayoutRenderers.MessageLayoutRenderer.WithException">
            <summary>
            Gets or sets a value indicating whether to log exception along with message.
            </summary>
            <docgen category='Layout Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.MessageLayoutRenderer.ExceptionSeparator">
            <summary>
            Gets or sets the string that separates message from the exception.
            </summary>
            <docgen category='Layout Options' order='10' />
        </member>
        <member name="T:NLog.LayoutRenderers.NdcLayoutRenderer">
            <summary>
            Nested Diagnostic Context item. Provided for compatibility with log4net.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.NdcLayoutRenderer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LayoutRenderers.NdcLayoutRenderer"/> class.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.NdcLayoutRenderer.Append(System.Text.StringBuilder,NLog.LogEventInfo)">
            <summary>
            Renders the specified Nested Diagnostics Context item and appends it to the specified <see cref="T:System.Text.StringBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:System.Text.StringBuilder"/> to append the rendered data to.</param>
            <param name="logEvent">Logging event.</param>
        </member>
        <member name="P:NLog.LayoutRenderers.NdcLayoutRenderer.TopFrames">
            <summary>
            Gets or sets the number of top stack frames to be rendered.
            </summary>
            <docgen category='Rendering Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.NdcLayoutRenderer.BottomFrames">
            <summary>
            Gets or sets the number of bottom stack frames to be rendered.
            </summary>
            <docgen category='Rendering Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.NdcLayoutRenderer.Separator">
            <summary>
            Gets or sets the separator to be used for concatenating nested diagnostics context output.
            </summary>
            <docgen category='Rendering Options' order='10' />
        </member>
        <member name="T:NLog.LayoutRenderers.NewLineLayoutRenderer">
            <summary>
            A newline literal.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.NewLineLayoutRenderer.Append(System.Text.StringBuilder,NLog.LogEventInfo)">
            <summary>
            Renders the specified string literal and appends it to the specified <see cref="T:System.Text.StringBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:System.Text.StringBuilder"/> to append the rendered data to.</param>
            <param name="logEvent">Logging event.</param>
        </member>
        <member name="T:NLog.LayoutRenderers.ProcessTimeLayoutRenderer">
            <summary>
            The process time in format HH:mm:ss.mmm.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.ProcessTimeLayoutRenderer.Append(System.Text.StringBuilder,NLog.LogEventInfo)">
            <summary>
            Renders the current process running time and appends it to the specified <see cref="T:System.Text.StringBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:System.Text.StringBuilder"/> to append the rendered data to.</param>
            <param name="logEvent">Logging event.</param>
        </member>
        <member name="T:NLog.LayoutRenderers.ShortDateLayoutRenderer">
            <summary>
            The short date in a sortable format yyyy-MM-dd.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.ShortDateLayoutRenderer.Append(System.Text.StringBuilder,NLog.LogEventInfo)">
            <summary>
            Renders the current short date string (yyyy-MM-dd) and appends it to the specified <see cref="T:System.Text.StringBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:System.Text.StringBuilder"/> to append the rendered data to.</param>
            <param name="logEvent">Logging event.</param>
        </member>
        <member name="P:NLog.LayoutRenderers.ShortDateLayoutRenderer.UniversalTime">
            <summary>
            Gets or sets a value indicating whether to output UTC time instead of local time.
            </summary>
            <docgen category='Rendering Options' order='10' />
        </member>
        <member name="T:NLog.LayoutRenderers.SpecialFolderLayoutRenderer">
            <summary>
            System special folder path (includes My Documents, My Music, Program Files, Desktop, and more).
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.SpecialFolderLayoutRenderer.Append(System.Text.StringBuilder,NLog.LogEventInfo)">
            <summary>
            Renders the directory where NLog is located and appends it to the specified <see cref="T:System.Text.StringBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:System.Text.StringBuilder"/> to append the rendered data to.</param>
            <param name="logEvent">Logging event.</param>
        </member>
        <member name="P:NLog.LayoutRenderers.SpecialFolderLayoutRenderer.Folder">
            <summary>
            Gets or sets the system special folder to use.
            </summary>
            <remarks>
            Full list of options is available at <a href="http://msdn2.microsoft.com/en-us/system.environment.specialfolder.aspx">MSDN</a>.
            The most common ones are:
            <ul>
            <li><b>ApplicationData</b> - roaming application data for current user.</li>
            <li><b>CommonApplicationData</b> - application data for all users.</li>
            <li><b>MyDocuments</b> - My Documents</li>
            <li><b>DesktopDirectory</b> - Desktop directory</li>
            <li><b>LocalApplicationData</b> - non roaming application data</li>
            <li><b>Personal</b> - user profile directory</li>
            <li><b>System</b> - System directory</li>
            </ul>
            </remarks>
            <docgen category='Rendering Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.SpecialFolderLayoutRenderer.File">
            <summary>
            Gets or sets the name of the file to be Path.Combine()'d with the directory name.
            </summary>
            <docgen category='Advanced Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.SpecialFolderLayoutRenderer.Dir">
            <summary>
            Gets or sets the name of the directory to be Path.Combine()'d with the directory name.
            </summary>
            <docgen category='Advanced Options' order='10' />
        </member>
        <member name="T:NLog.LayoutRenderers.StackTraceFormat">
            <summary>
            Format of the ${stacktrace} layout renderer output.
            </summary>
        </member>
        <member name="F:NLog.LayoutRenderers.StackTraceFormat.Raw">
            <summary>
            Raw format (multiline - as returned by StackFrame.ToString() method).
            </summary>
        </member>
        <member name="F:NLog.LayoutRenderers.StackTraceFormat.Flat">
            <summary>
            Flat format (class and method names displayed in a single line).
            </summary>
        </member>
        <member name="F:NLog.LayoutRenderers.StackTraceFormat.DetailedFlat">
            <summary>
            Detailed flat format (method signatures displayed in a single line).
            </summary>
        </member>
        <member name="T:NLog.LayoutRenderers.StackTraceLayoutRenderer">
            <summary>
            Stack trace renderer.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.StackTraceLayoutRenderer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LayoutRenderers.StackTraceLayoutRenderer"/> class.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.StackTraceLayoutRenderer.Append(System.Text.StringBuilder,NLog.LogEventInfo)">
            <summary>
            Renders the call site and appends it to the specified <see cref="T:System.Text.StringBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:System.Text.StringBuilder"/> to append the rendered data to.</param>
            <param name="logEvent">Logging event.</param>
        </member>
        <member name="P:NLog.LayoutRenderers.StackTraceLayoutRenderer.Format">
            <summary>
            Gets or sets the output format of the stack trace.
            </summary>
            <docgen category='Rendering Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.StackTraceLayoutRenderer.TopFrames">
            <summary>
            Gets or sets the number of top stack frames to be rendered.
            </summary>
            <docgen category='Rendering Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.StackTraceLayoutRenderer.Separator">
            <summary>
            Gets or sets the stack frame separator string.
            </summary>
            <docgen category='Rendering Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.StackTraceLayoutRenderer.NLog#Internal#IUsesStackTrace#StackTraceUsage">
            <summary>
            Gets the level of stack trace information required by the implementing class.
            </summary>
            <value></value>
        </member>
        <member name="T:NLog.LayoutRenderers.TempDirLayoutRenderer">
            <summary>
            A temporary directory.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.TempDirLayoutRenderer.Append(System.Text.StringBuilder,NLog.LogEventInfo)">
            <summary>
            Renders the directory where NLog is located and appends it to the specified <see cref="T:System.Text.StringBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:System.Text.StringBuilder"/> to append the rendered data to.</param>
            <param name="logEvent">Logging event.</param>
        </member>
        <member name="P:NLog.LayoutRenderers.TempDirLayoutRenderer.File">
            <summary>
            Gets or sets the name of the file to be Path.Combine()'d with the directory name.
            </summary>
            <docgen category='Advanced Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.TempDirLayoutRenderer.Dir">
            <summary>
            Gets or sets the name of the directory to be Path.Combine()'d with the directory name.
            </summary>
            <docgen category='Advanced Options' order='10' />
        </member>
        <member name="T:NLog.LayoutRenderers.ThreadIdLayoutRenderer">
            <summary>
            The identifier of the current thread.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.ThreadIdLayoutRenderer.Append(System.Text.StringBuilder,NLog.LogEventInfo)">
            <summary>
            Renders the current thread identifier and appends it to the specified <see cref="T:System.Text.StringBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:System.Text.StringBuilder"/> to append the rendered data to.</param>
            <param name="logEvent">Logging event.</param>
        </member>
        <member name="T:NLog.LayoutRenderers.ThreadNameLayoutRenderer">
            <summary>
            The name of the current thread.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.ThreadNameLayoutRenderer.Append(System.Text.StringBuilder,NLog.LogEventInfo)">
            <summary>
            Renders the current thread name and appends it to the specified <see cref="T:System.Text.StringBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:System.Text.StringBuilder"/> to append the rendered data to.</param>
            <param name="logEvent">Logging event.</param>
        </member>
        <member name="T:NLog.LayoutRenderers.TicksLayoutRenderer">
            <summary>
            The Ticks value of current date and time.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.TicksLayoutRenderer.Append(System.Text.StringBuilder,NLog.LogEventInfo)">
            <summary>
            Renders the ticks value of current time and appends it to the specified <see cref="T:System.Text.StringBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:System.Text.StringBuilder"/> to append the rendered data to.</param>
            <param name="logEvent">Logging event.</param>
        </member>
        <member name="T:NLog.LayoutRenderers.TimeLayoutRenderer">
            <summary>
            The time in a 24-hour, sortable format HH:mm:ss.mmm.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.TimeLayoutRenderer.Append(System.Text.StringBuilder,NLog.LogEventInfo)">
            <summary>
            Renders time in the 24-h format (HH:mm:ss.mmm) and appends it to the specified <see cref="T:System.Text.StringBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:System.Text.StringBuilder"/> to append the rendered data to.</param>
            <param name="logEvent">Logging event.</param>
        </member>
        <member name="P:NLog.LayoutRenderers.TimeLayoutRenderer.UniversalTime">
            <summary>
            Gets or sets a value indicating whether to output UTC time instead of local time.
            </summary>
            <docgen category='Rendering Options' order='10' />
        </member>
        <member name="T:NLog.LayoutRenderers.Wrappers.CachedLayoutRendererWrapper">
            <summary>
            Applies caching to another layout output.
            </summary>
            <remarks>
            The value of the inner layout will be rendered only once and reused subsequently.
            </remarks>
        </member>
        <member name="T:NLog.LayoutRenderers.Wrappers.WrapperLayoutRendererBase">
            <summary>
            Decodes text "encrypted" with ROT-13.
            </summary>
            <remarks>
            See <a href="http://en.wikipedia.org/wiki/ROT13">http://en.wikipedia.org/wiki/ROT13</a>.
            </remarks>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.WrapperLayoutRendererBase.Append(System.Text.StringBuilder,NLog.LogEventInfo)">
            <summary>
            Renders the inner message, processes it and appends it to the specified <see cref="T:System.Text.StringBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:System.Text.StringBuilder"/> to append the rendered data to.</param>
            <param name="logEvent">Logging event.</param>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.WrapperLayoutRendererBase.Transform(System.String)">
            <summary>
            Transforms the output of another layout.
            </summary>
            <param name="text">Output to be transform.</param>
            <returns>Transformed text.</returns>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.WrapperLayoutRendererBase.RenderInner(NLog.LogEventInfo)">
            <summary>
            Renders the inner layout contents.
            </summary>
            <param name="logEvent">The log event.</param>
            <returns>Contents of inner layout.</returns>
        </member>
        <member name="P:NLog.LayoutRenderers.Wrappers.WrapperLayoutRendererBase.Inner">
            <summary>
            Gets or sets the wrapped layout.
            </summary>
            <docgen category='Transformation Options' order='10' />
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.CachedLayoutRendererWrapper.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LayoutRenderers.Wrappers.CachedLayoutRendererWrapper"/> class.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.CachedLayoutRendererWrapper.InitializeLayoutRenderer">
            <summary>
            Initializes the layout renderer.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.CachedLayoutRendererWrapper.CloseLayoutRenderer">
            <summary>
            Closes the layout renderer.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.CachedLayoutRendererWrapper.Transform(System.String)">
            <summary>
            Transforms the output of another layout.
            </summary>
            <param name="text">Output to be transform.</param>
            <returns>Transformed text.</returns>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.CachedLayoutRendererWrapper.RenderInner(NLog.LogEventInfo)">
            <summary>
            Renders the inner layout contents.
            </summary>
            <param name="logEvent">The log event.</param>
            <returns>Contents of inner layout.</returns>
        </member>
        <member name="P:NLog.LayoutRenderers.Wrappers.CachedLayoutRendererWrapper.Cached">
            <summary>
            Gets or sets a value indicating whether this <see cref="T:NLog.LayoutRenderers.Wrappers.CachedLayoutRendererWrapper"/> is enabled.
            </summary>
            <docgen category="Caching Options" order="10"/>
        </member>
        <member name="T:NLog.LayoutRenderers.Wrappers.FileSystemNormalizeLayoutRendererWrapper">
            <summary>
            Filters characters not allowed in the file names by replacing them with safe character.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.FileSystemNormalizeLayoutRendererWrapper.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LayoutRenderers.Wrappers.FileSystemNormalizeLayoutRendererWrapper"/> class.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.FileSystemNormalizeLayoutRendererWrapper.Transform(System.String)">
            <summary>
            Post-processes the rendered message. 
            </summary>
            <param name="text">The text to be post-processed.</param>
            <returns>Padded and trimmed string.</returns>
        </member>
        <member name="P:NLog.LayoutRenderers.Wrappers.FileSystemNormalizeLayoutRendererWrapper.FSNormalize">
            <summary>
            Gets or sets a value indicating whether to modify the output of this renderer so it can be used as a part of file path
            (illegal characters are replaced with '_').
            </summary>
            <docgen category='Advanced Options' order='10' />
        </member>
        <member name="T:NLog.LayoutRenderers.Wrappers.JsonEncodeLayoutRendererWrapper">
            <summary>
            Escapes output of another layout using JSON rules.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.JsonEncodeLayoutRendererWrapper.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LayoutRenderers.Wrappers.JsonEncodeLayoutRendererWrapper"/> class.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.JsonEncodeLayoutRendererWrapper.Transform(System.String)">
            <summary>
            Post-processes the rendered message. 
            </summary>
            <param name="text">The text to be post-processed.</param>
            <returns>JSON-encoded string.</returns>
        </member>
        <member name="P:NLog.LayoutRenderers.Wrappers.JsonEncodeLayoutRendererWrapper.JsonEncode">
            <summary>
            Gets or sets a value indicating whether to apply JSON encoding.
            </summary>
            <docgen category="Transformation Options" order="10"/>
        </member>
        <member name="T:NLog.LayoutRenderers.Wrappers.LowercaseLayoutRendererWrapper">
            <summary>
            Converts the result of another layout output to lower case.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.LowercaseLayoutRendererWrapper.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LayoutRenderers.Wrappers.LowercaseLayoutRendererWrapper"/> class.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.LowercaseLayoutRendererWrapper.Transform(System.String)">
            <summary>
            Post-processes the rendered message. 
            </summary>
            <param name="text">The text to be post-processed.</param>
            <returns>Padded and trimmed string.</returns>
        </member>
        <member name="P:NLog.LayoutRenderers.Wrappers.LowercaseLayoutRendererWrapper.Lowercase">
            <summary>
            Gets or sets a value indicating whether lower case conversion should be applied.
            </summary>
            <value>A value of <c>true</c> if lower case conversion should be applied; otherwise, <c>false</c>.</value>
            <docgen category='Transformation Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.Wrappers.LowercaseLayoutRendererWrapper.Culture">
            <summary>
            Gets or sets the culture used for rendering. 
            </summary>
            <docgen category='Transformation Options' order='10' />
        </member>
        <member name="T:NLog.LayoutRenderers.Wrappers.OnExceptionLayoutRendererWrapper">
            <summary>
            Only outputs the inner layout when exception has been defined for log message.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.OnExceptionLayoutRendererWrapper.Transform(System.String)">
            <summary>
            Transforms the output of another layout.
            </summary>
            <param name="text">Output to be transform.</param>
            <returns>Transformed text.</returns>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.OnExceptionLayoutRendererWrapper.RenderInner(NLog.LogEventInfo)">
            <summary>
            Renders the inner layout contents.
            </summary>
            <param name="logEvent">The log event.</param>
            <returns>
            Contents of inner layout.
            </returns>
        </member>
        <member name="T:NLog.LayoutRenderers.Wrappers.PaddingLayoutRendererWrapper">
            <summary>
            Applies padding to another layout output.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.PaddingLayoutRendererWrapper.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LayoutRenderers.Wrappers.PaddingLayoutRendererWrapper"/> class.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.PaddingLayoutRendererWrapper.Transform(System.String)">
            <summary>
            Transforms the output of another layout.
            </summary>
            <param name="text">Output to be transform.</param>
            <returns>Transformed text.</returns>
        </member>
        <member name="P:NLog.LayoutRenderers.Wrappers.PaddingLayoutRendererWrapper.Padding">
            <summary>
            Gets or sets the number of characters to pad the output to. 
            </summary>
            <remarks>
            Positive padding values cause left padding, negative values 
            cause right padding to the desired width.
            </remarks>
            <docgen category='Transformation Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.Wrappers.PaddingLayoutRendererWrapper.PadCharacter">
            <summary>
            Gets or sets the padding character.
            </summary>
            <docgen category='Transformation Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.Wrappers.PaddingLayoutRendererWrapper.FixedLength">
            <summary>
            Gets or sets a value indicating whether to trim the 
            rendered text to the absolute value of the padding length.
            </summary>
            <docgen category='Transformation Options' order='10' />
        </member>
        <member name="T:NLog.LayoutRenderers.Wrappers.ReplaceLayoutRendererWrapper">
            <summary>
            Replaces a string in the output of another layout with another string.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.ReplaceLayoutRendererWrapper.InitializeLayoutRenderer">
            <summary>
            Initializes the layout renderer.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.ReplaceLayoutRendererWrapper.Transform(System.String)">
            <summary>
            Post-processes the rendered message. 
            </summary>
            <param name="text">The text to be post-processed.</param>
            <returns>Post-processed text.</returns>
        </member>
        <member name="P:NLog.LayoutRenderers.Wrappers.ReplaceLayoutRendererWrapper.SearchFor">
            <summary>
            Gets or sets the text to search for.
            </summary>
            <value>The text search for.</value>
            <docgen category='Search/Replace Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.Wrappers.ReplaceLayoutRendererWrapper.Regex">
            <summary>
            Gets or sets a value indicating whether regular expressions should be used.
            </summary>
            <value>A value of <c>true</c> if regular expressions should be used otherwise, <c>false</c>.</value>
            <docgen category='Search/Replace Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.Wrappers.ReplaceLayoutRendererWrapper.ReplaceWith">
            <summary>
            Gets or sets the replacement string.
            </summary>
            <value>The replacement string.</value>
            <docgen category='Search/Replace Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.Wrappers.ReplaceLayoutRendererWrapper.IgnoreCase">
            <summary>
            Gets or sets a value indicating whether to ignore case.
            </summary>
            <value>A value of <c>true</c> if case should be ignored when searching; otherwise, <c>false</c>.</value>
            <docgen category='Search/Replace Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.Wrappers.ReplaceLayoutRendererWrapper.WholeWords">
            <summary>
            Gets or sets a value indicating whether to search for whole words.
            </summary>
            <value>A value of <c>true</c> if whole words should be searched for; otherwise, <c>false</c>.</value>
            <docgen category='Search/Replace Options' order='10' />
        </member>
        <member name="T:NLog.LayoutRenderers.Wrappers.Rot13LayoutRendererWrapper">
            <summary>
            Decodes text "encrypted" with ROT-13.
            </summary>
            <remarks>
            See <a href="http://en.wikipedia.org/wiki/ROT13">http://en.wikipedia.org/wiki/ROT13</a>.
            </remarks>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.Rot13LayoutRendererWrapper.DecodeRot13(System.String)">
            <summary>
            Encodes/Decodes ROT-13-encoded string.
            </summary>
            <param name="encodedValue">The string to be encoded/decoded.</param>
            <returns>Encoded/Decoded text.</returns>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.Rot13LayoutRendererWrapper.Transform(System.String)">
            <summary>
            Transforms the output of another layout.
            </summary>
            <param name="text">Output to be transform.</param>
            <returns>Transformed text.</returns>
        </member>
        <member name="P:NLog.LayoutRenderers.Wrappers.Rot13LayoutRendererWrapper.Text">
            <summary>
            Gets or sets the layout to be wrapped.
            </summary>
            <value>The layout to be wrapped.</value>
            <remarks>This variable is for backwards compatibility</remarks>
            <docgen category='Transformation Options' order='10' />
        </member>
        <member name="T:NLog.LayoutRenderers.Wrappers.TrimWhiteSpaceLayoutRendererWrapper">
            <summary>
            Trims the whitespace from the result of another layout renderer.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.TrimWhiteSpaceLayoutRendererWrapper.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LayoutRenderers.Wrappers.TrimWhiteSpaceLayoutRendererWrapper"/> class.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.TrimWhiteSpaceLayoutRendererWrapper.Transform(System.String)">
            <summary>
            Post-processes the rendered message. 
            </summary>
            <param name="text">The text to be post-processed.</param>
            <returns>Trimmed string.</returns>
        </member>
        <member name="P:NLog.LayoutRenderers.Wrappers.TrimWhiteSpaceLayoutRendererWrapper.TrimWhiteSpace">
            <summary>
            Gets or sets a value indicating whether lower case conversion should be applied.
            </summary>
            <value>A value of <c>true</c> if lower case conversion should be applied; otherwise, <c>false</c>.</value>
            <docgen category='Transformation Options' order='10' />
        </member>
        <member name="T:NLog.LayoutRenderers.Wrappers.UppercaseLayoutRendererWrapper">
            <summary>
            Converts the result of another layout output to upper case.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.UppercaseLayoutRendererWrapper.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LayoutRenderers.Wrappers.UppercaseLayoutRendererWrapper"/> class.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.UppercaseLayoutRendererWrapper.Transform(System.String)">
            <summary>
            Post-processes the rendered message. 
            </summary>
            <param name="text">The text to be post-processed.</param>
            <returns>Padded and trimmed string.</returns>
        </member>
        <member name="P:NLog.LayoutRenderers.Wrappers.UppercaseLayoutRendererWrapper.Uppercase">
            <summary>
            Gets or sets a value indicating whether upper case conversion should be applied.
            </summary>
            <value>A value of <c>true</c> if upper case conversion should be applied otherwise, <c>false</c>.</value>
            <docgen category='Transformation Options' order='10' />
        </member>
        <member name="P:NLog.LayoutRenderers.Wrappers.UppercaseLayoutRendererWrapper.Culture">
            <summary>
            Gets or sets the culture used for rendering. 
            </summary>
            <docgen category='Transformation Options' order='10' />
        </member>
        <member name="T:NLog.LayoutRenderers.Wrappers.UrlEncodeLayoutRendererWrapper">
            <summary>
            Encodes the result of another layout output for use with URLs.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.UrlEncodeLayoutRendererWrapper.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LayoutRenderers.Wrappers.UrlEncodeLayoutRendererWrapper"/> class.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.UrlEncodeLayoutRendererWrapper.Transform(System.String)">
            <summary>
            Transforms the output of another layout.
            </summary>
            <param name="text">Output to be transform.</param>
            <returns>Transformed text.</returns>
        </member>
        <member name="P:NLog.LayoutRenderers.Wrappers.UrlEncodeLayoutRendererWrapper.SpaceAsPlus">
            <summary>
            Gets or sets a value indicating whether spaces should be translated to '+' or '%20'.
            </summary>
            <value>A value of <c>true</c> if space should be translated to '+'; otherwise, <c>false</c>.</value>
            <docgen category='Transformation Options' order='10' />
        </member>
        <member name="T:NLog.LayoutRenderers.Wrappers.WhenEmptyLayoutRendererWrapper">
            <summary>
            Outputs alternative layout when the inner layout produces empty result.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.WhenEmptyLayoutRendererWrapper.Transform(System.String)">
            <summary>
            Transforms the output of another layout.
            </summary>
            <param name="text">Output to be transform.</param>
            <returns>Transformed text.</returns>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.WhenEmptyLayoutRendererWrapper.RenderInner(NLog.LogEventInfo)">
            <summary>
            Renders the inner layout contents.
            </summary>
            <param name="logEvent">The log event.</param>
            <returns>
            Contents of inner layout.
            </returns>
        </member>
        <member name="P:NLog.LayoutRenderers.Wrappers.WhenEmptyLayoutRendererWrapper.WhenEmpty">
            <summary>
            Gets or sets the layout to be rendered when original layout produced empty result.
            </summary>
            <docgen category="Transformation Options" order="10"/>
        </member>
        <member name="T:NLog.LayoutRenderers.Wrappers.WhenLayoutRendererWrapper">
            <summary>
            Only outputs the inner layout when the specified condition has been met.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.WhenLayoutRendererWrapper.Transform(System.String)">
            <summary>
            Transforms the output of another layout.
            </summary>
            <param name="text">Output to be transform.</param>
            <returns>Transformed text.</returns>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.WhenLayoutRendererWrapper.RenderInner(NLog.LogEventInfo)">
            <summary>
            Renders the inner layout contents.
            </summary>
            <param name="logEvent">The log event.</param>
            <returns>
            Contents of inner layout.
            </returns>
        </member>
        <member name="P:NLog.LayoutRenderers.Wrappers.WhenLayoutRendererWrapper.When">
            <summary>
            Gets or sets the condition that must be met for the inner layout to be printed.
            </summary>
            <docgen category="Transformation Options" order="10"/>
        </member>
        <member name="T:NLog.LayoutRenderers.Wrappers.XmlEncodeLayoutRendererWrapper">
            <summary>
            Converts the result of another layout output to be XML-compliant.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.XmlEncodeLayoutRendererWrapper.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LayoutRenderers.Wrappers.XmlEncodeLayoutRendererWrapper"/> class.
            </summary>
        </member>
        <member name="M:NLog.LayoutRenderers.Wrappers.XmlEncodeLayoutRendererWrapper.Transform(System.String)">
            <summary>
            Post-processes the rendered message. 
            </summary>
            <param name="text">The text to be post-processed.</param>
            <returns>Padded and trimmed string.</returns>
        </member>
        <member name="P:NLog.LayoutRenderers.Wrappers.XmlEncodeLayoutRendererWrapper.XmlEncode">
            <summary>
            Gets or sets a value indicating whether to apply XML encoding.
            </summary>
            <docgen category="Transformation Options" order="10"/>
        </member>
        <member name="T:NLog.Layouts.CsvColumn">
            <summary>
            A column in the CSV.
            </summary>
        </member>
        <member name="M:NLog.Layouts.CsvColumn.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Layouts.CsvColumn"/> class.
            </summary>
        </member>
        <member name="M:NLog.Layouts.CsvColumn.#ctor(System.String,NLog.Layouts.Layout)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Layouts.CsvColumn"/> class.
            </summary>
            <param name="name">The name of the column.</param>
            <param name="layout">The layout of the column.</param>
        </member>
        <member name="P:NLog.Layouts.CsvColumn.Name">
            <summary>
            Gets or sets the name of the column.
            </summary>
            <docgen category='CSV Column Options' order='10' />
        </member>
        <member name="P:NLog.Layouts.CsvColumn.Layout">
            <summary>
            Gets or sets the layout of the column.
            </summary>
            <docgen category='CSV Column Options' order='10' />
        </member>
        <member name="T:NLog.Layouts.CsvColumnDelimiterMode">
            <summary>
            Specifies allowed column delimiters.
            </summary>
        </member>
        <member name="F:NLog.Layouts.CsvColumnDelimiterMode.Auto">
            <summary>
            Automatically detect from regional settings.
            </summary>
        </member>
        <member name="F:NLog.Layouts.CsvColumnDelimiterMode.Comma">
            <summary>
            Comma (ASCII 44).
            </summary>
        </member>
        <member name="F:NLog.Layouts.CsvColumnDelimiterMode.Semicolon">
            <summary>
            Semicolon (ASCII 59).
            </summary>
        </member>
        <member name="F:NLog.Layouts.CsvColumnDelimiterMode.Tab">
            <summary>
            Tab character (ASCII 9).
            </summary>
        </member>
        <member name="F:NLog.Layouts.CsvColumnDelimiterMode.Pipe">
            <summary>
            Pipe character (ASCII 124).
            </summary>
        </member>
        <member name="F:NLog.Layouts.CsvColumnDelimiterMode.Space">
            <summary>
            Space character (ASCII 32).
            </summary>
        </member>
        <member name="F:NLog.Layouts.CsvColumnDelimiterMode.Custom">
            <summary>
            Custom string, specified by the CustomDelimiter.
            </summary>
        </member>
        <member name="T:NLog.Layouts.CsvLayout">
            <summary>
            A specialized layout that renders CSV-formatted events.
            </summary>
        </member>
        <member name="T:NLog.Layouts.LayoutWithHeaderAndFooter">
            <summary>
            A specialized layout that supports header and footer.
            </summary>
        </member>
        <member name="T:NLog.Layouts.Layout">
            <summary>
            Abstract interface that layouts must implement.
            </summary>
        </member>
        <member name="M:NLog.Layouts.Layout.op_Implicit(System.String)~NLog.Layouts.Layout">
            <summary>
            Converts a given text to a <see cref="T:NLog.Layouts.Layout"/>.
            </summary>
            <param name="text">Text to be converted.</param>
            <returns><see cref="T:NLog.Layouts.SimpleLayout"/> object represented by the text.</returns>
        </member>
        <member name="M:NLog.Layouts.Layout.FromString(System.String)">
            <summary>
            Implicitly converts the specified string to a <see cref="T:NLog.Layouts.SimpleLayout"/>.
            </summary>
            <param name="layoutText">The layout string.</param>
            <returns>Instance of <see cref="T:NLog.Layouts.SimpleLayout"/>.</returns>
        </member>
        <member name="M:NLog.Layouts.Layout.FromString(System.String,NLog.Config.ConfigurationItemFactory)">
            <summary>
            Implicitly converts the specified string to a <see cref="T:NLog.Layouts.SimpleLayout"/>.
            </summary>
            <param name="layoutText">The layout string.</param>
            <param name="configurationItemFactory">The NLog factories to use when resolving layout renderers.</param>
            <returns>Instance of <see cref="T:NLog.Layouts.SimpleLayout"/>.</returns>
        </member>
        <member name="M:NLog.Layouts.Layout.Precalculate(NLog.LogEventInfo)">
            <summary>
            Precalculates the layout for the specified log event and stores the result
            in per-log event cache.
            </summary>
            <param name="logEvent">The log event.</param>
            <remarks>
            Calling this method enables you to store the log event in a buffer
            and/or potentially evaluate it in another thread even though the 
            layout may contain thread-dependent renderer.
            </remarks>
        </member>
        <member name="M:NLog.Layouts.Layout.Render(NLog.LogEventInfo)">
            <summary>
            Renders the event info in layout.
            </summary>
            <param name="logEvent">The event info.</param>
            <returns>String representing log event.</returns>
        </member>
        <member name="M:NLog.Layouts.Layout.NLog#Internal#ISupportsInitialize#Initialize(NLog.Config.LoggingConfiguration)">
            <summary>
            Initializes this instance.
            </summary>
            <param name="configuration">The configuration.</param>
        </member>
        <member name="M:NLog.Layouts.Layout.NLog#Internal#ISupportsInitialize#Close">
            <summary>
            Closes this instance.
            </summary>
        </member>
        <member name="M:NLog.Layouts.Layout.Initialize(NLog.Config.LoggingConfiguration)">
            <summary>
            Initializes this instance.
            </summary>
            <param name="configuration">The configuration.</param>
        </member>
        <member name="M:NLog.Layouts.Layout.Close">
            <summary>
            Closes this instance.
            </summary>
        </member>
        <member name="M:NLog.Layouts.Layout.InitializeLayout">
            <summary>
            Initializes the layout.
            </summary>
        </member>
        <member name="M:NLog.Layouts.Layout.CloseLayout">
            <summary>
            Closes the layout.
            </summary>
        </member>
        <member name="M:NLog.Layouts.Layout.GetFormattedMessage(NLog.LogEventInfo)">
            <summary>
            Renders the layout for the specified logging event by invoking layout renderers.
            </summary>
            <param name="logEvent">The logging event.</param>
            <returns>The rendered layout.</returns>
        </member>
        <member name="P:NLog.Layouts.Layout.IsThreadAgnostic">
            <summary>
            Gets a value indicating whether this layout is thread-agnostic (can be rendered on any thread).
            </summary>
            <remarks>
            Layout is thread-agnostic if it has been marked with [ThreadAgnostic] attribute and all its children are
            like that as well.
            Thread-agnostic layouts only use contents of <see cref="T:NLog.LogEventInfo"/> for its output.
            </remarks>
        </member>
        <member name="P:NLog.Layouts.Layout.LoggingConfiguration">
            <summary>
            Gets the logging configuration this target is part of.
            </summary>
        </member>
        <member name="M:NLog.Layouts.LayoutWithHeaderAndFooter.GetFormattedMessage(NLog.LogEventInfo)">
            <summary>
            Renders the layout for the specified logging event by invoking layout renderers.
            </summary>
            <param name="logEvent">The logging event.</param>
            <returns>The rendered layout.</returns>
        </member>
        <member name="P:NLog.Layouts.LayoutWithHeaderAndFooter.Layout">
            <summary>
            Gets or sets the body layout (can be repeated multiple times).
            </summary>
            <docgen category='Layout Options' order='10' />
        </member>
        <member name="P:NLog.Layouts.LayoutWithHeaderAndFooter.Header">
            <summary>
            Gets or sets the header layout.
            </summary>
            <docgen category='Layout Options' order='10' />
        </member>
        <member name="P:NLog.Layouts.LayoutWithHeaderAndFooter.Footer">
            <summary>
            Gets or sets the footer layout.
            </summary>
            <docgen category='Layout Options' order='10' />
        </member>
        <member name="M:NLog.Layouts.CsvLayout.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Layouts.CsvLayout"/> class.
            </summary>
        </member>
        <member name="M:NLog.Layouts.CsvLayout.InitializeLayout">
            <summary>
            Initializes the layout.
            </summary>
        </member>
        <member name="M:NLog.Layouts.CsvLayout.GetFormattedMessage(NLog.LogEventInfo)">
            <summary>
            Formats the log event for write.
            </summary>
            <param name="logEvent">The log event to be formatted.</param>
            <returns>A string representation of the log event.</returns>
        </member>
        <member name="P:NLog.Layouts.CsvLayout.Columns">
            <summary>
            Gets the array of parameters to be passed.
            </summary>
            <docgen category='CSV Options' order='10' />
        </member>
        <member name="P:NLog.Layouts.CsvLayout.WithHeader">
            <summary>
            Gets or sets a value indicating whether CVS should include header.
            </summary>
            <value>A value of <c>true</c> if CVS should include header; otherwise, <c>false</c>.</value>
            <docgen category='CSV Options' order='10' />
        </member>
        <member name="P:NLog.Layouts.CsvLayout.Delimiter">
            <summary>
            Gets or sets the column delimiter.
            </summary>
            <docgen category='CSV Options' order='10' />
        </member>
        <member name="P:NLog.Layouts.CsvLayout.Quoting">
            <summary>
            Gets or sets the quoting mode.
            </summary>
            <docgen category='CSV Options' order='10' />
        </member>
        <member name="P:NLog.Layouts.CsvLayout.QuoteChar">
            <summary>
            Gets or sets the quote Character.
            </summary>
            <docgen category='CSV Options' order='10' />
        </member>
        <member name="P:NLog.Layouts.CsvLayout.CustomColumnDelimiter">
            <summary>
            Gets or sets the custom column delimiter value (valid when ColumnDelimiter is set to 'Custom').
            </summary>
            <docgen category='CSV Options' order='10' />
        </member>
        <member name="T:NLog.Layouts.CsvLayout.CsvHeaderLayout">
            <summary>
            Header for CSV layout.
            </summary>
        </member>
        <member name="M:NLog.Layouts.CsvLayout.CsvHeaderLayout.#ctor(NLog.Layouts.CsvLayout)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Layouts.CsvLayout.CsvHeaderLayout"/> class.
            </summary>
            <param name="parent">The parent.</param>
        </member>
        <member name="M:NLog.Layouts.CsvLayout.CsvHeaderLayout.GetFormattedMessage(NLog.LogEventInfo)">
            <summary>
            Renders the layout for the specified logging event by invoking layout renderers.
            </summary>
            <param name="logEvent">The logging event.</param>
            <returns>The rendered layout.</returns>
        </member>
        <member name="T:NLog.Layouts.CsvQuotingMode">
            <summary>
            Specifies allowes CSV quoting modes.
            </summary>
        </member>
        <member name="F:NLog.Layouts.CsvQuotingMode.All">
            <summary>
            Quote all column.
            </summary>
        </member>
        <member name="F:NLog.Layouts.CsvQuotingMode.Nothing">
            <summary>
            Quote nothing.
            </summary>
        </member>
        <member name="F:NLog.Layouts.CsvQuotingMode.Auto">
            <summary>
            Quote only whose values contain the quote symbol or
            the separator.
            </summary>
        </member>
        <member name="T:NLog.Layouts.LayoutAttribute">
            <summary>
            Marks class as a layout renderer and assigns a format string to it.
            </summary>
        </member>
        <member name="M:NLog.Layouts.LayoutAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Layouts.LayoutAttribute"/> class.
            </summary>
            <param name="name">Layout name.</param>
        </member>
        <member name="T:NLog.Layouts.LayoutParser">
            <summary>
            Parses layout strings.
            </summary>
        </member>
        <member name="T:NLog.Layouts.Log4JXmlEventLayout">
            <summary>
            A specialized layout that renders Log4j-compatible XML events.
            </summary>
            <remarks>
            This layout is not meant to be used explicitly. Instead you can use ${log4jxmlevent} layout renderer.
            </remarks>
        </member>
        <member name="M:NLog.Layouts.Log4JXmlEventLayout.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Layouts.Log4JXmlEventLayout"/> class.
            </summary>
        </member>
        <member name="M:NLog.Layouts.Log4JXmlEventLayout.GetFormattedMessage(NLog.LogEventInfo)">
            <summary>
            Renders the layout for the specified logging event by invoking layout renderers.
            </summary>
            <param name="logEvent">The logging event.</param>
            <returns>The rendered layout.</returns>
        </member>
        <member name="P:NLog.Layouts.Log4JXmlEventLayout.Renderer">
            <summary>
            Gets the <see cref="T:NLog.LayoutRenderers.Log4JXmlEventLayoutRenderer"/> instance that renders log events.
            </summary>
        </member>
        <member name="T:NLog.Layouts.SimpleLayout">
            <summary>
            Represents a string with embedded placeholders that can render contextual information.
            </summary>
            <remarks>
            This layout is not meant to be used explicitly. Instead you can just use a string containing layout 
            renderers everywhere the layout is required.
            </remarks>
        </member>
        <member name="M:NLog.Layouts.SimpleLayout.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Layouts.SimpleLayout"/> class.
            </summary>
        </member>
        <member name="M:NLog.Layouts.SimpleLayout.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Layouts.SimpleLayout"/> class.
            </summary>
            <param name="txt">The layout string to parse.</param>
        </member>
        <member name="M:NLog.Layouts.SimpleLayout.#ctor(System.String,NLog.Config.ConfigurationItemFactory)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Layouts.SimpleLayout"/> class.
            </summary>
            <param name="txt">The layout string to parse.</param>
            <param name="configurationItemFactory">The NLog factories to use when creating references to layout renderers.</param>
        </member>
        <member name="M:NLog.Layouts.SimpleLayout.op_Implicit(System.String)~NLog.Layouts.SimpleLayout">
            <summary>
            Converts a text to a simple layout.
            </summary>
            <param name="text">Text to be converted.</param>
            <returns>A <see cref="T:NLog.Layouts.SimpleLayout"/> object.</returns>
        </member>
        <member name="M:NLog.Layouts.SimpleLayout.Escape(System.String)">
            <summary>
            Escapes the passed text so that it can
            be used literally in all places where
            layout is normally expected without being
            treated as layout.
            </summary>
            <param name="text">The text to be escaped.</param>
            <returns>The escaped text.</returns>
            <remarks>
            Escaping is done by replacing all occurences of
            '${' with '${literal:text=${}'
            </remarks>
        </member>
        <member name="M:NLog.Layouts.SimpleLayout.Evaluate(System.String,NLog.LogEventInfo)">
            <summary>
            Evaluates the specified text by expadinging all layout renderers.
            </summary>
            <param name="text">The text to be evaluated.</param>
            <param name="logEvent">Log event to be used for evaluation.</param>
            <returns>The input text with all occurences of ${} replaced with
            values provided by the appropriate layout renderers.</returns>
        </member>
        <member name="M:NLog.Layouts.SimpleLayout.Evaluate(System.String)">
            <summary>
            Evaluates the specified text by expadinging all layout renderers
            in new <see cref="T:NLog.LogEventInfo"/> context.
            </summary>
            <param name="text">The text to be evaluated.</param>
            <returns>The input text with all occurences of ${} replaced with
            values provided by the appropriate layout renderers.</returns>
        </member>
        <member name="M:NLog.Layouts.SimpleLayout.ToString">
            <summary>
            Returns a <see cref="T:System.String"></see> that represents the current object.
            </summary>
            <returns>
            A <see cref="T:System.String"></see> that represents the current object.
            </returns>
        </member>
        <member name="M:NLog.Layouts.SimpleLayout.GetFormattedMessage(NLog.LogEventInfo)">
            <summary>
            Renders the layout for the specified logging event by invoking layout renderers
            that make up the event.
            </summary>
            <param name="logEvent">The logging event.</param>
            <returns>The rendered layout.</returns>
        </member>
        <member name="P:NLog.Layouts.SimpleLayout.Text">
            <summary>
            Gets or sets the layout text.
            </summary>
            <docgen category='Layout Options' order='10' />
        </member>
        <member name="P:NLog.Layouts.SimpleLayout.Renderers">
            <summary>
            Gets a collection of <see cref="T:NLog.LayoutRenderers.LayoutRenderer"/> objects that make up this layout.
            </summary>
        </member>
        <member name="T:NLog.LogEventInfo">
            <summary>
            Represents the logging event.
            </summary>
        </member>
        <member name="F:NLog.LogEventInfo.ZeroDate">
            <summary>
            Gets the date of the first log event created.
            </summary>
        </member>
        <member name="M:NLog.LogEventInfo.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LogEventInfo"/> class.
            </summary>
        </member>
        <member name="M:NLog.LogEventInfo.#ctor(NLog.LogLevel,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LogEventInfo"/> class.
            </summary>
            <param name="level">Log level.</param>
            <param name="loggerName">Logger name.</param>
            <param name="message">Log message including parameter placeholders.</param>
        </member>
        <member name="M:NLog.LogEventInfo.#ctor(NLog.LogLevel,System.String,System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LogEventInfo"/> class.
            </summary>
            <param name="level">Log level.</param>
            <param name="loggerName">Logger name.</param>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="message">Log message including parameter placeholders.</param>
            <param name="parameters">Parameter array.</param>
        </member>
        <member name="M:NLog.LogEventInfo.#ctor(NLog.LogLevel,System.String,System.IFormatProvider,System.String,System.Object[],System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LogEventInfo"/> class.
            </summary>
            <param name="level">Log level.</param>
            <param name="loggerName">Logger name.</param>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="message">Log message including parameter placeholders.</param>
            <param name="parameters">Parameter array.</param>
            <param name="exception">Exception information.</param>
        </member>
        <member name="M:NLog.LogEventInfo.CreateNullEvent">
            <summary>
            Creates the null event.
            </summary>
            <returns>Null log event.</returns>
        </member>
        <member name="M:NLog.LogEventInfo.Create(NLog.LogLevel,System.String,System.String)">
            <summary>
            Creates the log event.
            </summary>
            <param name="logLevel">The log level.</param>
            <param name="loggerName">Name of the logger.</param>
            <param name="message">The message.</param>
            <returns>Instance of <see cref="T:NLog.LogEventInfo"/>.</returns>
        </member>
        <member name="M:NLog.LogEventInfo.Create(NLog.LogLevel,System.String,System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Creates the log event.
            </summary>
            <param name="logLevel">The log level.</param>
            <param name="loggerName">Name of the logger.</param>
            <param name="formatProvider">The format provider.</param>
            <param name="message">The message.</param>
            <param name="parameters">The parameters.</param>
            <returns>Instance of <see cref="T:NLog.LogEventInfo"/>.</returns>
        </member>
        <member name="M:NLog.LogEventInfo.Create(NLog.LogLevel,System.String,System.IFormatProvider,System.Object)">
            <summary>
            Creates the log event.
            </summary>
            <param name="logLevel">The log level.</param>
            <param name="loggerName">Name of the logger.</param>
            <param name="formatProvider">The format provider.</param>
            <param name="message">The message.</param>
            <returns>Instance of <see cref="T:NLog.LogEventInfo"/>.</returns>
        </member>
        <member name="M:NLog.LogEventInfo.Create(NLog.LogLevel,System.String,System.String,System.Exception)">
            <summary>
            Creates the log event.
            </summary>
            <param name="logLevel">The log level.</param>
            <param name="loggerName">Name of the logger.</param>
            <param name="message">The message.</param>
            <param name="exception">The exception.</param>
            <returns>Instance of <see cref="T:NLog.LogEventInfo"/>.</returns>
        </member>
        <member name="M:NLog.LogEventInfo.WithContinuation(NLog.Common.AsyncContinuation)">
            <summary>
            Creates <see cref="T:NLog.Common.AsyncLogEventInfo"/> from this <see cref="T:NLog.LogEventInfo"/> by attaching the specified asynchronous continuation.
            </summary>
            <param name="asyncContinuation">The asynchronous continuation.</param>
            <returns>Instance of <see cref="T:NLog.Common.AsyncLogEventInfo"/> with attached continuation.</returns>
        </member>
        <member name="M:NLog.LogEventInfo.ToString">
            <summary>
            Returns a string representation of this log event.
            </summary>
            <returns>String representation of the log event.</returns>
        </member>
        <member name="M:NLog.LogEventInfo.SetStackTrace(System.Diagnostics.StackTrace,System.Int32)">
            <summary>
            Sets the stack trace for the event info.
            </summary>
            <param name="stackTrace">The stack trace.</param>
            <param name="userStackFrame">Index of the first user stack frame within the stack trace.</param>
        </member>
        <member name="P:NLog.LogEventInfo.SequenceID">
            <summary>
            Gets the unique identifier of log event which is automatically generated
            and monotonously increasing.
            </summary>
        </member>
        <member name="P:NLog.LogEventInfo.TimeStamp">
            <summary>
            Gets or sets the timestamp of the logging event.
            </summary>
        </member>
        <member name="P:NLog.LogEventInfo.Level">
            <summary>
            Gets or sets the level of the logging event.
            </summary>
        </member>
        <member name="P:NLog.LogEventInfo.HasStackTrace">
            <summary>
            Gets a value indicating whether stack trace has been set for this event.
            </summary>
        </member>
        <member name="P:NLog.LogEventInfo.UserStackFrame">
            <summary>
            Gets the stack frame of the method that did the logging.
            </summary>
        </member>
        <member name="P:NLog.LogEventInfo.UserStackFrameNumber">
            <summary>
            Gets the number index of the stack frame that represents the user
            code (not the NLog code).
            </summary>
        </member>
        <member name="P:NLog.LogEventInfo.StackTrace">
            <summary>
            Gets the entire stack trace.
            </summary>
        </member>
        <member name="P:NLog.LogEventInfo.Exception">
            <summary>
            Gets or sets the exception information.
            </summary>
        </member>
        <member name="P:NLog.LogEventInfo.LoggerName">
            <summary>
            Gets or sets the logger name.
            </summary>
        </member>
        <member name="P:NLog.LogEventInfo.LoggerShortName">
            <summary>
            Gets the logger short name.
            </summary>
        </member>
        <member name="P:NLog.LogEventInfo.Message">
            <summary>
            Gets or sets the log message including any parameter placeholders.
            </summary>
        </member>
        <member name="P:NLog.LogEventInfo.Parameters">
            <summary>
            Gets or sets the parameter values or null if no parameters have been specified.
            </summary>
        </member>
        <member name="P:NLog.LogEventInfo.FormatProvider">
            <summary>
            Gets or sets the format provider that was provided while logging or <see langword="null" />
            when no formatProvider was specified.
            </summary>
        </member>
        <member name="P:NLog.LogEventInfo.FormattedMessage">
            <summary>
            Gets the formatted message.
            </summary>
        </member>
        <member name="P:NLog.LogEventInfo.Properties">
            <summary>
            Gets the dictionary of per-event context properties.
            </summary>
        </member>
        <member name="P:NLog.LogEventInfo.Context">
            <summary>
            Gets the dictionary of per-event context properties.
            </summary>
        </member>
        <member name="T:NLog.LogFactory">
            <summary>
            Creates and manages instances of <see cref="T:NLog.Logger" /> objects.
            </summary>
        </member>
        <member name="M:NLog.LogFactory.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LogFactory"/> class.
            </summary>
        </member>
        <member name="M:NLog.LogFactory.#ctor(NLog.Config.LoggingConfiguration)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LogFactory"/> class.
            </summary>
            <param name="config">The config.</param>
        </member>
        <member name="M:NLog.LogFactory.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="M:NLog.LogFactory.CreateNullLogger">
            <summary>
            Creates a logger that discards all log messages.
            </summary>
            <returns>Null logger instance.</returns>
        </member>
        <member name="M:NLog.LogFactory.GetCurrentClassLogger">
            <summary>
            Gets the logger named after the currently-being-initialized class.
            </summary>
            <returns>The logger.</returns>
            <remarks>This is a slow-running method. 
            Make sure you're not doing this in a loop.</remarks>
        </member>
        <member name="M:NLog.LogFactory.GetCurrentClassLogger(System.Type)">
            <summary>
            Gets the logger named after the currently-being-initialized class.
            </summary>
            <param name="loggerType">The type of the logger to create. The type must inherit from NLog.Logger.</param>
            <returns>The logger.</returns>
            <remarks>This is a slow-running method. 
            Make sure you're not doing this in a loop.</remarks>
        </member>
        <member name="M:NLog.LogFactory.GetLogger(System.String)">
            <summary>
            Gets the specified named logger.
            </summary>
            <param name="name">Name of the logger.</param>
            <returns>The logger reference. Multiple calls to <c>GetLogger</c> with the same argument aren't guaranteed to return the same logger reference.</returns>
        </member>
        <member name="M:NLog.LogFactory.GetLogger(System.String,System.Type)">
            <summary>
            Gets the specified named logger.
            </summary>
            <param name="name">Name of the logger.</param>
            <param name="loggerType">The type of the logger to create. The type must inherit from NLog.Logger.</param>
            <returns>The logger reference. Multiple calls to <c>GetLogger</c> with the 
            same argument aren't guaranteed to return the same logger reference.</returns>
        </member>
        <member name="M:NLog.LogFactory.ReconfigExistingLoggers">
            <summary>
            Loops through all loggers previously returned by GetLogger
            and recalculates their target and filter list. Useful after modifying the configuration programmatically
            to ensure that all loggers have been properly configured.
            </summary>
        </member>
        <member name="M:NLog.LogFactory.Flush(NLog.Common.AsyncContinuation)">
            <summary>
            Flush any pending log messages (in case of asynchronous targets).
            </summary>
            <param name="asyncContinuation">The asynchronous continuation.</param>
        </member>
        <member name="M:NLog.LogFactory.Flush(NLog.Common.AsyncContinuation,System.Int32)">
            <summary>
            Flush any pending log messages (in case of asynchronous targets).
            </summary>
            <param name="asyncContinuation">The asynchronous continuation.</param>
            <param name="timeoutMilliseconds">Maximum time to allow for the flush. Any messages after that time will be discarded.</param>
        </member>
        <member name="M:NLog.LogFactory.Flush(NLog.Common.AsyncContinuation,System.TimeSpan)">
            <summary>
            Flush any pending log messages (in case of asynchronous targets).
            </summary>
            <param name="asyncContinuation">The asynchronous continuation.</param>
            <param name="timeout">Maximum time to allow for the flush. Any messages after that time will be discarded.</param>
        </member>
        <member name="M:NLog.LogFactory.DisableLogging">
            <summary>Decreases the log enable counter and if it reaches -1 
            the logs are disabled.</summary>
            <remarks>Logging is enabled if the number of <see cref="M:NLog.LogFactory.EnableLogging"/> calls is greater 
            than or equal to <see cref="M:NLog.LogFactory.DisableLogging"/> calls.</remarks>
            <returns>An object that iplements IDisposable whose Dispose() method
            reenables logging. To be used with C# <c>using ()</c> statement.</returns>
        </member>
        <member name="M:NLog.LogFactory.EnableLogging">
            <summary>Increases the log enable counter and if it reaches 0 the logs are disabled.</summary>
            <remarks>Logging is enabled if the number of <see cref="M:NLog.LogFactory.EnableLogging"/> calls is greater 
            than or equal to <see cref="M:NLog.LogFactory.DisableLogging"/> calls.</remarks>
        </member>
        <member name="M:NLog.LogFactory.IsLoggingEnabled">
            <summary>
            Returns <see langword="true"/> if logging is currently enabled.
            </summary>
            <returns>A value of <see langword="true"/> if logging is currently enabled, 
            <see langword="false"/> otherwise.</returns>
            <remarks>Logging is enabled if the number of <see cref="M:NLog.LogFactory.EnableLogging"/> calls is greater 
            than or equal to <see cref="M:NLog.LogFactory.DisableLogging"/> calls.</remarks>
        </member>
        <member name="M:NLog.LogFactory.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
            <param name="disposing">True to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="E:NLog.LogFactory.ConfigurationChanged">
            <summary>
            Occurs when logging <see cref="P:NLog.LogFactory.Configuration"/> changes.
            </summary>
        </member>
        <member name="P:NLog.LogFactory.ThrowExceptions">
            <summary>
            Gets or sets a value indicating whether exceptions should be thrown.
            </summary>
            <value>A value of <c>true</c> if exceptiosn should be thrown; otherwise, <c>false</c>.</value>
            <remarks>By default exceptions
            are not thrown under any circumstances.
            </remarks>
        </member>
        <member name="P:NLog.LogFactory.Configuration">
            <summary>
            Gets or sets the current logging configuration.
            </summary>
        </member>
        <member name="P:NLog.LogFactory.GlobalThreshold">
            <summary>
            Gets or sets the global log threshold. Log events below this threshold are not logged.
            </summary>
        </member>
        <member name="T:NLog.LogFactory.LoggerCacheKey">
            <summary>
            Logger cache key.
            </summary>
        </member>
        <member name="M:NLog.LogFactory.LoggerCacheKey.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>
            A hash code for the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="M:NLog.LogFactory.LoggerCacheKey.Equals(System.Object)">
            <summary>
            Determines if two objects are equal in value.
            </summary>
            <param name="o">Other object to compare to.</param>
            <returns>True if objects are equal, false otherwise.</returns>
        </member>
        <member name="T:NLog.LogFactory.LogEnabler">
            <summary>
            Enables logging in <see cref="M:System.IDisposable.Dispose"/> implementation.
            </summary>
        </member>
        <member name="M:NLog.LogFactory.LogEnabler.#ctor(NLog.LogFactory)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LogFactory.LogEnabler"/> class.
            </summary>
            <param name="factory">The factory.</param>
        </member>
        <member name="M:NLog.LogFactory.LogEnabler.System#IDisposable#Dispose">
            <summary>
            Enables logging.
            </summary>
        </member>
        <member name="T:NLog.LogFactory`1">
            <summary>
            Specialized LogFactory that can return instances of custom logger types.
            </summary>
            <typeparam name="T">The type of the logger to be returned. Must inherit from <see cref="T:NLog.Logger"/>.</typeparam>
        </member>
        <member name="M:NLog.LogFactory`1.GetLogger(System.String)">
            <summary>
            Gets the logger.
            </summary>
            <param name="name">The logger name.</param>
            <returns>An instance of <typeparamref name="T"/>.</returns>
        </member>
        <member name="M:NLog.LogFactory`1.GetCurrentClassLogger">
            <summary>
            Gets the logger named after the currently-being-initialized class.
            </summary>
            <returns>The logger.</returns>
            <remarks>This is a slow-running method. 
            Make sure you're not doing this in a loop.</remarks>
        </member>
        <member name="T:NLog.Logger">
            <summary>
            Provides logging interface and utility functions.
            </summary>
        </member>
        <member name="M:NLog.Logger.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Logger"/> class.
            </summary>
        </member>
        <member name="M:NLog.Logger.IsEnabled(NLog.LogLevel)">
            <summary>
            Gets a value indicating whether logging is enabled for the specified level.
            </summary>
            <param name="level">Log level to be checked.</param>
            <returns>A value of <see langword="true" /> if logging is enabled for the specified level, otherwise it returns <see langword="false" />.</returns>
        </member>
        <member name="M:NLog.Logger.Log(NLog.LogEventInfo)">
            <summary>
            Writes the specified diagnostic message.
            </summary>
            <param name="logEvent">Log event.</param>
        </member>
        <member name="M:NLog.Logger.Log(System.Type,NLog.LogEventInfo)">
            <summary>
            Writes the specified diagnostic message.
            </summary>
            <param name="wrapperType">The name of the type that wraps Logger.</param>
            <param name="logEvent">Log event.</param>
        </member>
        <member name="M:NLog.Logger.Log``1(NLog.LogLevel,``0)">
            <overloads>
            Writes the diagnostic message at the specified level using the specified format provider and format parameters.
            </overloads>
            <summary>
            Writes the diagnostic message at the specified level.
            </summary>
            <typeparam name="T">Type of the value.</typeparam>
            <param name="level">The log level.</param>
            <param name="value">The value to be written.</param>
        </member>
        <member name="M:NLog.Logger.Log``1(NLog.LogLevel,System.IFormatProvider,``0)">
            <summary>
            Writes the diagnostic message at the specified level.
            </summary>
            <typeparam name="T">Type of the value.</typeparam>
            <param name="level">The log level.</param>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="value">The value to be written.</param>
        </member>
        <member name="M:NLog.Logger.Log(NLog.LogLevel,NLog.LogMessageGenerator)">
            <summary>
            Writes the diagnostic message at the specified level.
            </summary>
            <param name="level">The log level.</param>
            <param name="messageFunc">A function returning message to be written. Function is not evaluated if logging is not enabled.</param>
        </member>
        <member name="M:NLog.Logger.LogException(NLog.LogLevel,System.String,System.Exception)">
            <summary>
            Writes the diagnostic message and exception at the specified level.
            </summary>
            <param name="level">The log level.</param>
            <param name="message">A <see langword="string" /> to be written.</param>
            <param name="exception">An exception to be logged.</param>
        </member>
        <member name="M:NLog.Logger.Log(NLog.LogLevel,System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Writes the diagnostic message at the specified level using the specified parameters and formatting them with the supplied format provider.
            </summary>
            <param name="level">The log level.</param>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="message">A <see langword="string" /> containing format items.</param>
            <param name="args">Arguments to format.</param>
        </member>
        <member name="M:NLog.Logger.Log(NLog.LogLevel,System.String)">
            <summary>
            Writes the diagnostic message at the specified level.
            </summary>
            <param name="level">The log level.</param>
            <param name="message">Log message.</param>
        </member>
        <member name="M:NLog.Logger.Log(NLog.LogLevel,System.String,System.Object[])">
            <summary>
            Writes the diagnostic message at the specified level using the specified parameters.
            </summary>
            <param name="level">The log level.</param>
            <param name="message">A <see langword="string" /> containing format items.</param>
            <param name="args">Arguments to format.</param>
        </member>
        <member name="M:NLog.Logger.Log``1(NLog.LogLevel,System.IFormatProvider,System.String,``0)">
            <summary>
            Writes the diagnostic message at the specified level using the specified parameter and formatting it with the supplied format provider.
            </summary>
            <typeparam name="TArgument">The type of the argument.</typeparam>
            <param name="level">The log level.</param>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument">The argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Log``1(NLog.LogLevel,System.String,``0)">
            <summary>
            Writes the diagnostic message at the specified level using the specified parameter.
            </summary>
            <typeparam name="TArgument">The type of the argument.</typeparam>
            <param name="level">The log level.</param>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument">The argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Log``2(NLog.LogLevel,System.IFormatProvider,System.String,``0,``1)">
            <summary>
            Writes the diagnostic message at the specified level using the specified arguments formatting it with the supplied format provider.
            </summary>
            <typeparam name="TArgument1">The type of the first argument.</typeparam>
            <typeparam name="TArgument2">The type of the second argument.</typeparam>
            <param name="level">The log level.</param>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument1">The first argument to format.</param>
            <param name="argument2">The second argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Log``2(NLog.LogLevel,System.String,``0,``1)">
            <summary>
            Writes the diagnostic message at the specified level using the specified parameters.
            </summary>
            <typeparam name="TArgument1">The type of the first argument.</typeparam>
            <typeparam name="TArgument2">The type of the second argument.</typeparam>
            <param name="level">The log level.</param>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument1">The first argument to format.</param>
            <param name="argument2">The second argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Log``3(NLog.LogLevel,System.IFormatProvider,System.String,``0,``1,``2)">
            <summary>
            Writes the diagnostic message at the specified level using the specified arguments formatting it with the supplied format provider.
            </summary>
            <typeparam name="TArgument1">The type of the first argument.</typeparam>
            <typeparam name="TArgument2">The type of the second argument.</typeparam>
            <typeparam name="TArgument3">The type of the third argument.</typeparam>
            <param name="level">The log level.</param>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument1">The first argument to format.</param>
            <param name="argument2">The second argument to format.</param>
            <param name="argument3">The third argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Log``3(NLog.LogLevel,System.String,``0,``1,``2)">
            <summary>
            Writes the diagnostic message at the specified level using the specified parameters.
            </summary>
            <typeparam name="TArgument1">The type of the first argument.</typeparam>
            <typeparam name="TArgument2">The type of the second argument.</typeparam>
            <typeparam name="TArgument3">The type of the third argument.</typeparam>
            <param name="level">The log level.</param>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument1">The first argument to format.</param>
            <param name="argument2">The second argument to format.</param>
            <param name="argument3">The third argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Trace``1(``0)">
            <overloads>
            Writes the diagnostic message at the <c>Trace</c> level using the specified format provider and format parameters.
            </overloads>
            <summary>
            Writes the diagnostic message at the <c>Trace</c> level.
            </summary>
            <typeparam name="T">Type of the value.</typeparam>
            <param name="value">The value to be written.</param>
        </member>
        <member name="M:NLog.Logger.Trace``1(System.IFormatProvider,``0)">
            <summary>
            Writes the diagnostic message at the <c>Trace</c> level.
            </summary>
            <typeparam name="T">Type of the value.</typeparam>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="value">The value to be written.</param>
        </member>
        <member name="M:NLog.Logger.Trace(NLog.LogMessageGenerator)">
            <summary>
            Writes the diagnostic message at the <c>Trace</c> level.
            </summary>
            <param name="messageFunc">A function returning message to be written. Function is not evaluated if logging is not enabled.</param>
        </member>
        <member name="M:NLog.Logger.TraceException(System.String,System.Exception)">
            <summary>
            Writes the diagnostic message and exception at the <c>Trace</c> level.
            </summary>
            <param name="message">A <see langword="string" /> to be written.</param>
            <param name="exception">An exception to be logged.</param>
        </member>
        <member name="M:NLog.Logger.Trace(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Writes the diagnostic message at the <c>Trace</c> level using the specified parameters and formatting them with the supplied format provider.
            </summary>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="message">A <see langword="string" /> containing format items.</param>
            <param name="args">Arguments to format.</param>
        </member>
        <member name="M:NLog.Logger.Trace(System.String)">
            <summary>
            Writes the diagnostic message at the <c>Trace</c> level.
            </summary>
            <param name="message">Log message.</param>
        </member>
        <member name="M:NLog.Logger.Trace(System.String,System.Object[])">
            <summary>
            Writes the diagnostic message at the <c>Trace</c> level using the specified parameters.
            </summary>
            <param name="message">A <see langword="string" /> containing format items.</param>
            <param name="args">Arguments to format.</param>
        </member>
        <member name="M:NLog.Logger.Trace``1(System.IFormatProvider,System.String,``0)">
            <summary>
            Writes the diagnostic message at the <c>Trace</c> level using the specified parameter and formatting it with the supplied format provider.
            </summary>
            <typeparam name="TArgument">The type of the argument.</typeparam>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument">The argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Trace``1(System.String,``0)">
            <summary>
            Writes the diagnostic message at the <c>Trace</c> level using the specified parameter.
            </summary>
            <typeparam name="TArgument">The type of the argument.</typeparam>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument">The argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Trace``2(System.IFormatProvider,System.String,``0,``1)">
            <summary>
            Writes the diagnostic message at the <c>Trace</c> level using the specified arguments formatting it with the supplied format provider.
            </summary>
            <typeparam name="TArgument1">The type of the first argument.</typeparam>
            <typeparam name="TArgument2">The type of the second argument.</typeparam>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument1">The first argument to format.</param>
            <param name="argument2">The second argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Trace``2(System.String,``0,``1)">
            <summary>
            Writes the diagnostic message at the <c>Trace</c> level using the specified parameters.
            </summary>
            <typeparam name="TArgument1">The type of the first argument.</typeparam>
            <typeparam name="TArgument2">The type of the second argument.</typeparam>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument1">The first argument to format.</param>
            <param name="argument2">The second argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Trace``3(System.IFormatProvider,System.String,``0,``1,``2)">
            <summary>
            Writes the diagnostic message at the <c>Trace</c> level using the specified arguments formatting it with the supplied format provider.
            </summary>
            <typeparam name="TArgument1">The type of the first argument.</typeparam>
            <typeparam name="TArgument2">The type of the second argument.</typeparam>
            <typeparam name="TArgument3">The type of the third argument.</typeparam>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument1">The first argument to format.</param>
            <param name="argument2">The second argument to format.</param>
            <param name="argument3">The third argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Trace``3(System.String,``0,``1,``2)">
            <summary>
            Writes the diagnostic message at the <c>Trace</c> level using the specified parameters.
            </summary>
            <typeparam name="TArgument1">The type of the first argument.</typeparam>
            <typeparam name="TArgument2">The type of the second argument.</typeparam>
            <typeparam name="TArgument3">The type of the third argument.</typeparam>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument1">The first argument to format.</param>
            <param name="argument2">The second argument to format.</param>
            <param name="argument3">The third argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Debug``1(``0)">
            <overloads>
            Writes the diagnostic message at the <c>Debug</c> level using the specified format provider and format parameters.
            </overloads>
            <summary>
            Writes the diagnostic message at the <c>Debug</c> level.
            </summary>
            <typeparam name="T">Type of the value.</typeparam>
            <param name="value">The value to be written.</param>
        </member>
        <member name="M:NLog.Logger.Debug``1(System.IFormatProvider,``0)">
            <summary>
            Writes the diagnostic message at the <c>Debug</c> level.
            </summary>
            <typeparam name="T">Type of the value.</typeparam>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="value">The value to be written.</param>
        </member>
        <member name="M:NLog.Logger.Debug(NLog.LogMessageGenerator)">
            <summary>
            Writes the diagnostic message at the <c>Debug</c> level.
            </summary>
            <param name="messageFunc">A function returning message to be written. Function is not evaluated if logging is not enabled.</param>
        </member>
        <member name="M:NLog.Logger.DebugException(System.String,System.Exception)">
            <summary>
            Writes the diagnostic message and exception at the <c>Debug</c> level.
            </summary>
            <param name="message">A <see langword="string" /> to be written.</param>
            <param name="exception">An exception to be logged.</param>
        </member>
        <member name="M:NLog.Logger.Debug(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Writes the diagnostic message at the <c>Debug</c> level using the specified parameters and formatting them with the supplied format provider.
            </summary>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="message">A <see langword="string" /> containing format items.</param>
            <param name="args">Arguments to format.</param>
        </member>
        <member name="M:NLog.Logger.Debug(System.String)">
            <summary>
            Writes the diagnostic message at the <c>Debug</c> level.
            </summary>
            <param name="message">Log message.</param>
        </member>
        <member name="M:NLog.Logger.Debug(System.String,System.Object[])">
            <summary>
            Writes the diagnostic message at the <c>Debug</c> level using the specified parameters.
            </summary>
            <param name="message">A <see langword="string" /> containing format items.</param>
            <param name="args">Arguments to format.</param>
        </member>
        <member name="M:NLog.Logger.Debug``1(System.IFormatProvider,System.String,``0)">
            <summary>
            Writes the diagnostic message at the <c>Debug</c> level using the specified parameter and formatting it with the supplied format provider.
            </summary>
            <typeparam name="TArgument">The type of the argument.</typeparam>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument">The argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Debug``1(System.String,``0)">
            <summary>
            Writes the diagnostic message at the <c>Debug</c> level using the specified parameter.
            </summary>
            <typeparam name="TArgument">The type of the argument.</typeparam>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument">The argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Debug``2(System.IFormatProvider,System.String,``0,``1)">
            <summary>
            Writes the diagnostic message at the <c>Debug</c> level using the specified arguments formatting it with the supplied format provider.
            </summary>
            <typeparam name="TArgument1">The type of the first argument.</typeparam>
            <typeparam name="TArgument2">The type of the second argument.</typeparam>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument1">The first argument to format.</param>
            <param name="argument2">The second argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Debug``2(System.String,``0,``1)">
            <summary>
            Writes the diagnostic message at the <c>Debug</c> level using the specified parameters.
            </summary>
            <typeparam name="TArgument1">The type of the first argument.</typeparam>
            <typeparam name="TArgument2">The type of the second argument.</typeparam>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument1">The first argument to format.</param>
            <param name="argument2">The second argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Debug``3(System.IFormatProvider,System.String,``0,``1,``2)">
            <summary>
            Writes the diagnostic message at the <c>Debug</c> level using the specified arguments formatting it with the supplied format provider.
            </summary>
            <typeparam name="TArgument1">The type of the first argument.</typeparam>
            <typeparam name="TArgument2">The type of the second argument.</typeparam>
            <typeparam name="TArgument3">The type of the third argument.</typeparam>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument1">The first argument to format.</param>
            <param name="argument2">The second argument to format.</param>
            <param name="argument3">The third argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Debug``3(System.String,``0,``1,``2)">
            <summary>
            Writes the diagnostic message at the <c>Debug</c> level using the specified parameters.
            </summary>
            <typeparam name="TArgument1">The type of the first argument.</typeparam>
            <typeparam name="TArgument2">The type of the second argument.</typeparam>
            <typeparam name="TArgument3">The type of the third argument.</typeparam>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument1">The first argument to format.</param>
            <param name="argument2">The second argument to format.</param>
            <param name="argument3">The third argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Info``1(``0)">
            <overloads>
            Writes the diagnostic message at the <c>Info</c> level using the specified format provider and format parameters.
            </overloads>
            <summary>
            Writes the diagnostic message at the <c>Info</c> level.
            </summary>
            <typeparam name="T">Type of the value.</typeparam>
            <param name="value">The value to be written.</param>
        </member>
        <member name="M:NLog.Logger.Info``1(System.IFormatProvider,``0)">
            <summary>
            Writes the diagnostic message at the <c>Info</c> level.
            </summary>
            <typeparam name="T">Type of the value.</typeparam>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="value">The value to be written.</param>
        </member>
        <member name="M:NLog.Logger.Info(NLog.LogMessageGenerator)">
            <summary>
            Writes the diagnostic message at the <c>Info</c> level.
            </summary>
            <param name="messageFunc">A function returning message to be written. Function is not evaluated if logging is not enabled.</param>
        </member>
        <member name="M:NLog.Logger.InfoException(System.String,System.Exception)">
            <summary>
            Writes the diagnostic message and exception at the <c>Info</c> level.
            </summary>
            <param name="message">A <see langword="string" /> to be written.</param>
            <param name="exception">An exception to be logged.</param>
        </member>
        <member name="M:NLog.Logger.Info(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Writes the diagnostic message at the <c>Info</c> level using the specified parameters and formatting them with the supplied format provider.
            </summary>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="message">A <see langword="string" /> containing format items.</param>
            <param name="args">Arguments to format.</param>
        </member>
        <member name="M:NLog.Logger.Info(System.String)">
            <summary>
            Writes the diagnostic message at the <c>Info</c> level.
            </summary>
            <param name="message">Log message.</param>
        </member>
        <member name="M:NLog.Logger.Info(System.String,System.Object[])">
            <summary>
            Writes the diagnostic message at the <c>Info</c> level using the specified parameters.
            </summary>
            <param name="message">A <see langword="string" /> containing format items.</param>
            <param name="args">Arguments to format.</param>
        </member>
        <member name="M:NLog.Logger.Info``1(System.IFormatProvider,System.String,``0)">
            <summary>
            Writes the diagnostic message at the <c>Info</c> level using the specified parameter and formatting it with the supplied format provider.
            </summary>
            <typeparam name="TArgument">The type of the argument.</typeparam>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument">The argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Info``1(System.String,``0)">
            <summary>
            Writes the diagnostic message at the <c>Info</c> level using the specified parameter.
            </summary>
            <typeparam name="TArgument">The type of the argument.</typeparam>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument">The argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Info``2(System.IFormatProvider,System.String,``0,``1)">
            <summary>
            Writes the diagnostic message at the <c>Info</c> level using the specified arguments formatting it with the supplied format provider.
            </summary>
            <typeparam name="TArgument1">The type of the first argument.</typeparam>
            <typeparam name="TArgument2">The type of the second argument.</typeparam>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument1">The first argument to format.</param>
            <param name="argument2">The second argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Info``2(System.String,``0,``1)">
            <summary>
            Writes the diagnostic message at the <c>Info</c> level using the specified parameters.
            </summary>
            <typeparam name="TArgument1">The type of the first argument.</typeparam>
            <typeparam name="TArgument2">The type of the second argument.</typeparam>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument1">The first argument to format.</param>
            <param name="argument2">The second argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Info``3(System.IFormatProvider,System.String,``0,``1,``2)">
            <summary>
            Writes the diagnostic message at the <c>Info</c> level using the specified arguments formatting it with the supplied format provider.
            </summary>
            <typeparam name="TArgument1">The type of the first argument.</typeparam>
            <typeparam name="TArgument2">The type of the second argument.</typeparam>
            <typeparam name="TArgument3">The type of the third argument.</typeparam>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument1">The first argument to format.</param>
            <param name="argument2">The second argument to format.</param>
            <param name="argument3">The third argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Info``3(System.String,``0,``1,``2)">
            <summary>
            Writes the diagnostic message at the <c>Info</c> level using the specified parameters.
            </summary>
            <typeparam name="TArgument1">The type of the first argument.</typeparam>
            <typeparam name="TArgument2">The type of the second argument.</typeparam>
            <typeparam name="TArgument3">The type of the third argument.</typeparam>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument1">The first argument to format.</param>
            <param name="argument2">The second argument to format.</param>
            <param name="argument3">The third argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Warn``1(``0)">
            <overloads>
            Writes the diagnostic message at the <c>Warn</c> level using the specified format provider and format parameters.
            </overloads>
            <summary>
            Writes the diagnostic message at the <c>Warn</c> level.
            </summary>
            <typeparam name="T">Type of the value.</typeparam>
            <param name="value">The value to be written.</param>
        </member>
        <member name="M:NLog.Logger.Warn``1(System.IFormatProvider,``0)">
            <summary>
            Writes the diagnostic message at the <c>Warn</c> level.
            </summary>
            <typeparam name="T">Type of the value.</typeparam>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="value">The value to be written.</param>
        </member>
        <member name="M:NLog.Logger.Warn(NLog.LogMessageGenerator)">
            <summary>
            Writes the diagnostic message at the <c>Warn</c> level.
            </summary>
            <param name="messageFunc">A function returning message to be written. Function is not evaluated if logging is not enabled.</param>
        </member>
        <member name="M:NLog.Logger.WarnException(System.String,System.Exception)">
            <summary>
            Writes the diagnostic message and exception at the <c>Warn</c> level.
            </summary>
            <param name="message">A <see langword="string" /> to be written.</param>
            <param name="exception">An exception to be logged.</param>
        </member>
        <member name="M:NLog.Logger.Warn(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Writes the diagnostic message at the <c>Warn</c> level using the specified parameters and formatting them with the supplied format provider.
            </summary>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="message">A <see langword="string" /> containing format items.</param>
            <param name="args">Arguments to format.</param>
        </member>
        <member name="M:NLog.Logger.Warn(System.String)">
            <summary>
            Writes the diagnostic message at the <c>Warn</c> level.
            </summary>
            <param name="message">Log message.</param>
        </member>
        <member name="M:NLog.Logger.Warn(System.String,System.Object[])">
            <summary>
            Writes the diagnostic message at the <c>Warn</c> level using the specified parameters.
            </summary>
            <param name="message">A <see langword="string" /> containing format items.</param>
            <param name="args">Arguments to format.</param>
        </member>
        <member name="M:NLog.Logger.Warn``1(System.IFormatProvider,System.String,``0)">
            <summary>
            Writes the diagnostic message at the <c>Warn</c> level using the specified parameter and formatting it with the supplied format provider.
            </summary>
            <typeparam name="TArgument">The type of the argument.</typeparam>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument">The argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Warn``1(System.String,``0)">
            <summary>
            Writes the diagnostic message at the <c>Warn</c> level using the specified parameter.
            </summary>
            <typeparam name="TArgument">The type of the argument.</typeparam>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument">The argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Warn``2(System.IFormatProvider,System.String,``0,``1)">
            <summary>
            Writes the diagnostic message at the <c>Warn</c> level using the specified arguments formatting it with the supplied format provider.
            </summary>
            <typeparam name="TArgument1">The type of the first argument.</typeparam>
            <typeparam name="TArgument2">The type of the second argument.</typeparam>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument1">The first argument to format.</param>
            <param name="argument2">The second argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Warn``2(System.String,``0,``1)">
            <summary>
            Writes the diagnostic message at the <c>Warn</c> level using the specified parameters.
            </summary>
            <typeparam name="TArgument1">The type of the first argument.</typeparam>
            <typeparam name="TArgument2">The type of the second argument.</typeparam>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument1">The first argument to format.</param>
            <param name="argument2">The second argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Warn``3(System.IFormatProvider,System.String,``0,``1,``2)">
            <summary>
            Writes the diagnostic message at the <c>Warn</c> level using the specified arguments formatting it with the supplied format provider.
            </summary>
            <typeparam name="TArgument1">The type of the first argument.</typeparam>
            <typeparam name="TArgument2">The type of the second argument.</typeparam>
            <typeparam name="TArgument3">The type of the third argument.</typeparam>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument1">The first argument to format.</param>
            <param name="argument2">The second argument to format.</param>
            <param name="argument3">The third argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Warn``3(System.String,``0,``1,``2)">
            <summary>
            Writes the diagnostic message at the <c>Warn</c> level using the specified parameters.
            </summary>
            <typeparam name="TArgument1">The type of the first argument.</typeparam>
            <typeparam name="TArgument2">The type of the second argument.</typeparam>
            <typeparam name="TArgument3">The type of the third argument.</typeparam>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument1">The first argument to format.</param>
            <param name="argument2">The second argument to format.</param>
            <param name="argument3">The third argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Error``1(``0)">
            <overloads>
            Writes the diagnostic message at the <c>Error</c> level using the specified format provider and format parameters.
            </overloads>
            <summary>
            Writes the diagnostic message at the <c>Error</c> level.
            </summary>
            <typeparam name="T">Type of the value.</typeparam>
            <param name="value">The value to be written.</param>
        </member>
        <member name="M:NLog.Logger.Error``1(System.IFormatProvider,``0)">
            <summary>
            Writes the diagnostic message at the <c>Error</c> level.
            </summary>
            <typeparam name="T">Type of the value.</typeparam>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="value">The value to be written.</param>
        </member>
        <member name="M:NLog.Logger.Error(NLog.LogMessageGenerator)">
            <summary>
            Writes the diagnostic message at the <c>Error</c> level.
            </summary>
            <param name="messageFunc">A function returning message to be written. Function is not evaluated if logging is not enabled.</param>
        </member>
        <member name="M:NLog.Logger.ErrorException(System.String,System.Exception)">
            <summary>
            Writes the diagnostic message and exception at the <c>Error</c> level.
            </summary>
            <param name="message">A <see langword="string" /> to be written.</param>
            <param name="exception">An exception to be logged.</param>
        </member>
        <member name="M:NLog.Logger.Error(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Writes the diagnostic message at the <c>Error</c> level using the specified parameters and formatting them with the supplied format provider.
            </summary>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="message">A <see langword="string" /> containing format items.</param>
            <param name="args">Arguments to format.</param>
        </member>
        <member name="M:NLog.Logger.Error(System.String)">
            <summary>
            Writes the diagnostic message at the <c>Error</c> level.
            </summary>
            <param name="message">Log message.</param>
        </member>
        <member name="M:NLog.Logger.Error(System.String,System.Object[])">
            <summary>
            Writes the diagnostic message at the <c>Error</c> level using the specified parameters.
            </summary>
            <param name="message">A <see langword="string" /> containing format items.</param>
            <param name="args">Arguments to format.</param>
        </member>
        <member name="M:NLog.Logger.Error``1(System.IFormatProvider,System.String,``0)">
            <summary>
            Writes the diagnostic message at the <c>Error</c> level using the specified parameter and formatting it with the supplied format provider.
            </summary>
            <typeparam name="TArgument">The type of the argument.</typeparam>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument">The argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Error``1(System.String,``0)">
            <summary>
            Writes the diagnostic message at the <c>Error</c> level using the specified parameter.
            </summary>
            <typeparam name="TArgument">The type of the argument.</typeparam>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument">The argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Error``2(System.IFormatProvider,System.String,``0,``1)">
            <summary>
            Writes the diagnostic message at the <c>Error</c> level using the specified arguments formatting it with the supplied format provider.
            </summary>
            <typeparam name="TArgument1">The type of the first argument.</typeparam>
            <typeparam name="TArgument2">The type of the second argument.</typeparam>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument1">The first argument to format.</param>
            <param name="argument2">The second argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Error``2(System.String,``0,``1)">
            <summary>
            Writes the diagnostic message at the <c>Error</c> level using the specified parameters.
            </summary>
            <typeparam name="TArgument1">The type of the first argument.</typeparam>
            <typeparam name="TArgument2">The type of the second argument.</typeparam>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument1">The first argument to format.</param>
            <param name="argument2">The second argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Error``3(System.IFormatProvider,System.String,``0,``1,``2)">
            <summary>
            Writes the diagnostic message at the <c>Error</c> level using the specified arguments formatting it with the supplied format provider.
            </summary>
            <typeparam name="TArgument1">The type of the first argument.</typeparam>
            <typeparam name="TArgument2">The type of the second argument.</typeparam>
            <typeparam name="TArgument3">The type of the third argument.</typeparam>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument1">The first argument to format.</param>
            <param name="argument2">The second argument to format.</param>
            <param name="argument3">The third argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Error``3(System.String,``0,``1,``2)">
            <summary>
            Writes the diagnostic message at the <c>Error</c> level using the specified parameters.
            </summary>
            <typeparam name="TArgument1">The type of the first argument.</typeparam>
            <typeparam name="TArgument2">The type of the second argument.</typeparam>
            <typeparam name="TArgument3">The type of the third argument.</typeparam>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument1">The first argument to format.</param>
            <param name="argument2">The second argument to format.</param>
            <param name="argument3">The third argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Fatal``1(``0)">
            <overloads>
            Writes the diagnostic message at the <c>Fatal</c> level using the specified format provider and format parameters.
            </overloads>
            <summary>
            Writes the diagnostic message at the <c>Fatal</c> level.
            </summary>
            <typeparam name="T">Type of the value.</typeparam>
            <param name="value">The value to be written.</param>
        </member>
        <member name="M:NLog.Logger.Fatal``1(System.IFormatProvider,``0)">
            <summary>
            Writes the diagnostic message at the <c>Fatal</c> level.
            </summary>
            <typeparam name="T">Type of the value.</typeparam>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="value">The value to be written.</param>
        </member>
        <member name="M:NLog.Logger.Fatal(NLog.LogMessageGenerator)">
            <summary>
            Writes the diagnostic message at the <c>Fatal</c> level.
            </summary>
            <param name="messageFunc">A function returning message to be written. Function is not evaluated if logging is not enabled.</param>
        </member>
        <member name="M:NLog.Logger.FatalException(System.String,System.Exception)">
            <summary>
            Writes the diagnostic message and exception at the <c>Fatal</c> level.
            </summary>
            <param name="message">A <see langword="string" /> to be written.</param>
            <param name="exception">An exception to be logged.</param>
        </member>
        <member name="M:NLog.Logger.Fatal(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Writes the diagnostic message at the <c>Fatal</c> level using the specified parameters and formatting them with the supplied format provider.
            </summary>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="message">A <see langword="string" /> containing format items.</param>
            <param name="args">Arguments to format.</param>
        </member>
        <member name="M:NLog.Logger.Fatal(System.String)">
            <summary>
            Writes the diagnostic message at the <c>Fatal</c> level.
            </summary>
            <param name="message">Log message.</param>
        </member>
        <member name="M:NLog.Logger.Fatal(System.String,System.Object[])">
            <summary>
            Writes the diagnostic message at the <c>Fatal</c> level using the specified parameters.
            </summary>
            <param name="message">A <see langword="string" /> containing format items.</param>
            <param name="args">Arguments to format.</param>
        </member>
        <member name="M:NLog.Logger.Fatal``1(System.IFormatProvider,System.String,``0)">
            <summary>
            Writes the diagnostic message at the <c>Fatal</c> level using the specified parameter and formatting it with the supplied format provider.
            </summary>
            <typeparam name="TArgument">The type of the argument.</typeparam>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument">The argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Fatal``1(System.String,``0)">
            <summary>
            Writes the diagnostic message at the <c>Fatal</c> level using the specified parameter.
            </summary>
            <typeparam name="TArgument">The type of the argument.</typeparam>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument">The argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Fatal``2(System.IFormatProvider,System.String,``0,``1)">
            <summary>
            Writes the diagnostic message at the <c>Fatal</c> level using the specified arguments formatting it with the supplied format provider.
            </summary>
            <typeparam name="TArgument1">The type of the first argument.</typeparam>
            <typeparam name="TArgument2">The type of the second argument.</typeparam>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument1">The first argument to format.</param>
            <param name="argument2">The second argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Fatal``2(System.String,``0,``1)">
            <summary>
            Writes the diagnostic message at the <c>Fatal</c> level using the specified parameters.
            </summary>
            <typeparam name="TArgument1">The type of the first argument.</typeparam>
            <typeparam name="TArgument2">The type of the second argument.</typeparam>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument1">The first argument to format.</param>
            <param name="argument2">The second argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Fatal``3(System.IFormatProvider,System.String,``0,``1,``2)">
            <summary>
            Writes the diagnostic message at the <c>Fatal</c> level using the specified arguments formatting it with the supplied format provider.
            </summary>
            <typeparam name="TArgument1">The type of the first argument.</typeparam>
            <typeparam name="TArgument2">The type of the second argument.</typeparam>
            <typeparam name="TArgument3">The type of the third argument.</typeparam>
            <param name="formatProvider">An IFormatProvider that supplies culture-specific formatting information.</param>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument1">The first argument to format.</param>
            <param name="argument2">The second argument to format.</param>
            <param name="argument3">The third argument to format.</param>
        </member>
        <member name="M:NLog.Logger.Fatal``3(System.String,``0,``1,``2)">
            <summary>
            Writes the diagnostic message at the <c>Fatal</c> level using the specified parameters.
            </summary>
            <typeparam name="TArgument1">The type of the first argument.</typeparam>
            <typeparam name="TArgument2">The type of the second argument.</typeparam>
            <typeparam name="TArgument3">The type of the third argument.</typeparam>
            <param name="message">A <see langword="string" /> containing one format item.</param>
            <param name="argument1">The first argument to format.</param>
            <param name="argument2">The second argument to format.</param>
            <param name="argument3">The third argument to format.</param>
        </member>
        <member name="E:NLog.Logger.LoggerReconfigured">
            <summary>
            Occurs when logger configuration changes.
            </summary>
        </member>
        <member name="P:NLog.Logger.Name">
            <summary>
            Gets the name of the logger.
            </summary>
        </member>
        <member name="P:NLog.Logger.Factory">
            <summary>
            Gets the factory that created this logger.
            </summary>
        </member>
        <member name="P:NLog.Logger.IsTraceEnabled">
            <summary>
            Gets a value indicating whether logging is enabled for the <c>Trace</c> level.
            </summary>
            <returns>A value of <see langword="true" /> if logging is enabled for the <c>Trace</c> level, otherwise it returns <see langword="false" />.</returns>
        </member>
        <member name="P:NLog.Logger.IsDebugEnabled">
            <summary>
            Gets a value indicating whether logging is enabled for the <c>Debug</c> level.
            </summary>
            <returns>A value of <see langword="true" /> if logging is enabled for the <c>Debug</c> level, otherwise it returns <see langword="false" />.</returns>
        </member>
        <member name="P:NLog.Logger.IsInfoEnabled">
            <summary>
            Gets a value indicating whether logging is enabled for the <c>Info</c> level.
            </summary>
            <returns>A value of <see langword="true" /> if logging is enabled for the <c>Info</c> level, otherwise it returns <see langword="false" />.</returns>
        </member>
        <member name="P:NLog.Logger.IsWarnEnabled">
            <summary>
            Gets a value indicating whether logging is enabled for the <c>Warn</c> level.
            </summary>
            <returns>A value of <see langword="true" /> if logging is enabled for the <c>Warn</c> level, otherwise it returns <see langword="false" />.</returns>
        </member>
        <member name="P:NLog.Logger.IsErrorEnabled">
            <summary>
            Gets a value indicating whether logging is enabled for the <c>Error</c> level.
            </summary>
            <returns>A value of <see langword="true" /> if logging is enabled for the <c>Error</c> level, otherwise it returns <see langword="false" />.</returns>
        </member>
        <member name="P:NLog.Logger.IsFatalEnabled">
            <summary>
            Gets a value indicating whether logging is enabled for the <c>Fatal</c> level.
            </summary>
            <returns>A value of <see langword="true" /> if logging is enabled for the <c>Fatal</c> level, otherwise it returns <see langword="false" />.</returns>
        </member>
        <member name="T:NLog.LoggerImpl">
            <summary>
            Implementation of logging engine.
            </summary>
        </member>
        <member name="M:NLog.LoggerImpl.GetFilterResult(System.Collections.Generic.IEnumerable{NLog.Filters.Filter},NLog.LogEventInfo)">
            <summary>
            Gets the filter result.
            </summary>
            <param name="filterChain">The filter chain.</param>
            <param name="logEvent">The log event.</param>
            <returns>The result of the filter.</returns>
        </member>
        <member name="T:NLog.LogLevel">
            <summary>
            Defines available log levels.
            </summary>
        </member>
        <member name="F:NLog.LogLevel.Trace">
            <summary>
            Trace log level.
            </summary>
        </member>
        <member name="F:NLog.LogLevel.Debug">
            <summary>
            Debug log level.
            </summary>
        </member>
        <member name="F:NLog.LogLevel.Info">
            <summary>
            Info log level.
            </summary>
        </member>
        <member name="F:NLog.LogLevel.Warn">
            <summary>
            Warn log level.
            </summary>
        </member>
        <member name="F:NLog.LogLevel.Error">
            <summary>
            Error log level.
            </summary>
        </member>
        <member name="F:NLog.LogLevel.Fatal">
            <summary>
            Fatal log level.
            </summary>
        </member>
        <member name="F:NLog.LogLevel.Off">
            <summary>
            Off log level.
            </summary>
        </member>
        <member name="M:NLog.LogLevel.op_Equality(NLog.LogLevel,NLog.LogLevel)">
            <summary>
            Compares two <see cref="T:NLog.LogLevel"/> objects 
            and returns a value indicating whether 
            the first one is equal to the second one.
            </summary>
            <param name="level1">The first level.</param>
            <param name="level2">The second level.</param>
            <returns>The value of <c>level1.Ordinal == level2.Ordinal</c>.</returns>
        </member>
        <member name="M:NLog.LogLevel.op_Inequality(NLog.LogLevel,NLog.LogLevel)">
            <summary>
            Compares two <see cref="T:NLog.LogLevel"/> objects 
            and returns a value indicating whether 
            the first one is not equal to the second one.
            </summary>
            <param name="level1">The first level.</param>
            <param name="level2">The second level.</param>
            <returns>The value of <c>level1.Ordinal != level2.Ordinal</c>.</returns>
        </member>
        <member name="M:NLog.LogLevel.op_GreaterThan(NLog.LogLevel,NLog.LogLevel)">
            <summary>
            Compares two <see cref="T:NLog.LogLevel"/> objects 
            and returns a value indicating whether 
            the first one is greater than the second one.
            </summary>
            <param name="level1">The first level.</param>
            <param name="level2">The second level.</param>
            <returns>The value of <c>level1.Ordinal &gt; level2.Ordinal</c>.</returns>
        </member>
        <member name="M:NLog.LogLevel.op_GreaterThanOrEqual(NLog.LogLevel,NLog.LogLevel)">
            <summary>
            Compares two <see cref="T:NLog.LogLevel"/> objects 
            and returns a value indicating whether 
            the first one is greater than or equal to the second one.
            </summary>
            <param name="level1">The first level.</param>
            <param name="level2">The second level.</param>
            <returns>The value of <c>level1.Ordinal &gt;= level2.Ordinal</c>.</returns>
        </member>
        <member name="M:NLog.LogLevel.op_LessThan(NLog.LogLevel,NLog.LogLevel)">
            <summary>
            Compares two <see cref="T:NLog.LogLevel"/> objects 
            and returns a value indicating whether 
            the first one is less than the second one.
            </summary>
            <param name="level1">The first level.</param>
            <param name="level2">The second level.</param>
            <returns>The value of <c>level1.Ordinal &lt; level2.Ordinal</c>.</returns>
        </member>
        <member name="M:NLog.LogLevel.op_LessThanOrEqual(NLog.LogLevel,NLog.LogLevel)">
            <summary>
            Compares two <see cref="T:NLog.LogLevel"/> objects 
            and returns a value indicating whether 
            the first one is less than or equal to the second one.
            </summary>
            <param name="level1">The first level.</param>
            <param name="level2">The second level.</param>
            <returns>The value of <c>level1.Ordinal &lt;= level2.Ordinal</c>.</returns>
        </member>
        <member name="M:NLog.LogLevel.FromOrdinal(System.Int32)">
            <summary>
            Gets the <see cref="T:NLog.LogLevel"/> that corresponds to the specified ordinal.
            </summary>
            <param name="ordinal">The ordinal.</param>
            <returns>The <see cref="T:NLog.LogLevel"/> instance. For 0 it returns <see cref="F:NLog.LogLevel.Debug"/>, 1 gives <see cref="F:NLog.LogLevel.Info"/> and so on.</returns>
        </member>
        <member name="M:NLog.LogLevel.FromString(System.String)">
            <summary>
            Returns the <see cref="T:NLog.LogLevel"/> that corresponds to the supplied <see langword="string" />.
            </summary>
            <param name="levelName">The texual representation of the log level.</param>
            <returns>The enumeration value.</returns>
        </member>
        <member name="M:NLog.LogLevel.ToString">
            <summary>
            Returns a string representation of the log level.
            </summary>
            <returns>Log level name.</returns>
        </member>
        <member name="M:NLog.LogLevel.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
            <returns>
            A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table. 
            </returns>
        </member>
        <member name="M:NLog.LogLevel.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object"/> is equal to this instance.
            </summary>
            <param name="obj">The <see cref="T:System.Object"/> to compare with this instance.</param>
            <returns>
            Value of <c>true</c> if the specified <see cref="T:System.Object"/> is equal to this instance; otherwise, <c>false</c>.
            </returns>
            <exception cref="T:System.NullReferenceException">
            The <paramref name="obj"/> parameter is null.
            </exception>
        </member>
        <member name="M:NLog.LogLevel.CompareTo(System.Object)">
            <summary>
            Compares the level to the other <see cref="T:NLog.LogLevel"/> object.
            </summary>
            <param name="obj">
            The object object.
            </param>
            <returns>
            A value less than zero when this logger's <see cref="P:NLog.LogLevel.Ordinal"/> is 
            less than the other logger's ordinal, 0 when they are equal and 
            greater than zero when this ordinal is greater than the
            other ordinal.
            </returns>
        </member>
        <member name="P:NLog.LogLevel.Name">
            <summary>
            Gets the name of the log level.
            </summary>
        </member>
        <member name="P:NLog.LogLevel.Ordinal">
            <summary>
            Gets the ordinal of the log level.
            </summary>
        </member>
        <member name="T:NLog.LogManager">
            <summary>
            Creates and manages instances of <see cref="T:NLog.Logger" /> objects.
            </summary>
        </member>
        <member name="M:NLog.LogManager.#ctor">
            <summary>
            Prevents a default instance of the LogManager class from being created.
            </summary>
        </member>
        <member name="M:NLog.LogManager.GetCurrentClassLogger">
            <summary>
            Gets the logger named after the currently-being-initialized class.
            </summary>
            <returns>The logger.</returns>
            <remarks>This is a slow-running method. 
            Make sure you're not doing this in a loop.</remarks>
        </member>
        <member name="M:NLog.LogManager.GetCurrentClassLogger(System.Type)">
            <summary>
            Gets the logger named after the currently-being-initialized class.
            </summary>
            <param name="loggerType">The logger class. The class must inherit from <see cref="T:NLog.Logger"/>.</param>
            <returns>The logger.</returns>
            <remarks>This is a slow-running method. 
            Make sure you're not doing this in a loop.</remarks>
        </member>
        <member name="M:NLog.LogManager.CreateNullLogger">
            <summary>
            Creates a logger that discards all log messages.
            </summary>
            <returns>Null logger which discards all log messages.</returns>
        </member>
        <member name="M:NLog.LogManager.GetLogger(System.String)">
            <summary>
            Gets the specified named logger.
            </summary>
            <param name="name">Name of the logger.</param>
            <returns>The logger reference. Multiple calls to <c>GetLogger</c> with the same argument aren't guaranteed to return the same logger reference.</returns>
        </member>
        <member name="M:NLog.LogManager.GetLogger(System.String,System.Type)">
            <summary>
            Gets the specified named logger.
            </summary>
            <param name="name">Name of the logger.</param>
            <param name="loggerType">The logger class. The class must inherit from <see cref="T:NLog.Logger"/>.</param>
            <returns>The logger reference. Multiple calls to <c>GetLogger</c> with the same argument aren't guaranteed to return the same logger reference.</returns>
        </member>
        <member name="M:NLog.LogManager.ReconfigExistingLoggers">
            <summary>
            Loops through all loggers previously returned by GetLogger.
            and recalculates their target and filter list. Useful after modifying the configuration programmatically
            to ensure that all loggers have been properly configured.
            </summary>
        </member>
        <member name="M:NLog.LogManager.Flush(NLog.Common.AsyncContinuation)">
            <summary>
            Flush any pending log messages (in case of asynchronous targets).
            </summary>
            <param name="asyncContinuation">The asynchronous continuation.</param>
        </member>
        <member name="M:NLog.LogManager.Flush(NLog.Common.AsyncContinuation,System.TimeSpan)">
            <summary>
            Flush any pending log messages (in case of asynchronous targets).
            </summary>
            <param name="asyncContinuation">The asynchronous continuation.</param>
            <param name="timeout">Maximum time to allow for the flush. Any messages after that time will be discarded.</param>
        </member>
        <member name="M:NLog.LogManager.Flush(NLog.Common.AsyncContinuation,System.Int32)">
            <summary>
            Flush any pending log messages (in case of asynchronous targets).
            </summary>
            <param name="asyncContinuation">The asynchronous continuation.</param>
            <param name="timeoutMilliseconds">Maximum time to allow for the flush. Any messages after that time will be discarded.</param>
        </member>
        <member name="M:NLog.LogManager.DisableLogging">
            <summary>Decreases the log enable counter and if it reaches -1 
            the logs are disabled.</summary>
            <remarks>Logging is enabled if the number of <see cref="M:NLog.LogManager.EnableLogging"/> calls is greater 
            than or equal to <see cref="M:NLog.LogManager.DisableLogging"/> calls.</remarks>
            <returns>An object that iplements IDisposable whose Dispose() method
            reenables logging. To be used with C# <c>using ()</c> statement.</returns>
        </member>
        <member name="M:NLog.LogManager.EnableLogging">
            <summary>Increases the log enable counter and if it reaches 0 the logs are disabled.</summary>
            <remarks>Logging is enabled if the number of <see cref="M:NLog.LogManager.EnableLogging"/> calls is greater 
            than or equal to <see cref="M:NLog.LogManager.DisableLogging"/> calls.</remarks>
        </member>
        <member name="M:NLog.LogManager.IsLoggingEnabled">
            <summary>
            Returns <see langword="true"/> if logging is currently enabled.
            </summary>
            <returns>A value of <see langword="true"/> if logging is currently enabled, 
            <see langword="false"/> otherwise.</returns>
            <remarks>Logging is enabled if the number of <see cref="M:NLog.LogManager.EnableLogging"/> calls is greater 
            than or equal to <see cref="M:NLog.LogManager.DisableLogging"/> calls.</remarks>
        </member>
        <member name="E:NLog.LogManager.ConfigurationChanged">
            <summary>
            Occurs when logging <see cref="P:NLog.LogManager.Configuration"/> changes.
            </summary>
        </member>
        <member name="P:NLog.LogManager.ThrowExceptions">
            <summary>
            Gets or sets a value indicating whether NLog should throw exceptions. 
            By default exceptions are not thrown under any circumstances.
            </summary>
        </member>
        <member name="P:NLog.LogManager.Configuration">
            <summary>
            Gets or sets the current logging configuration.
            </summary>
        </member>
        <member name="P:NLog.LogManager.GlobalThreshold">
            <summary>
            Gets or sets the global log threshold. Log events below this threshold are not logged.
            </summary>
        </member>
        <member name="T:NLog.LogMessageGenerator">
            <summary>
            Returns a log message. Used to defer calculation of 
            the log message until it's actually needed.
            </summary>
            <returns>Log message.</returns>
        </member>
        <member name="T:NLog.LogReceiverService.ILogReceiverClient">
            <summary>
            Service contract for Log Receiver client.
            </summary>
        </member>
        <member name="M:NLog.LogReceiverService.ILogReceiverClient.BeginProcessLogMessages(NLog.LogReceiverService.NLogEvents,System.AsyncCallback,System.Object)">
            <summary>
            Begins processing of log messages.
            </summary>
            <param name="events">The events.</param>
            <param name="callback">The callback.</param>
            <param name="asyncState">Asynchronous state.</param>
            <returns>
            IAsyncResult value which can be passed to <see cref="M:NLog.LogReceiverService.ILogReceiverClient.EndProcessLogMessages(System.IAsyncResult)"/>.
            </returns>
        </member>
        <member name="M:NLog.LogReceiverService.ILogReceiverClient.EndProcessLogMessages(System.IAsyncResult)">
            <summary>
            Ends asynchronous processing of log messages.
            </summary>
            <param name="result">The result.</param>
        </member>
        <member name="T:NLog.LogReceiverService.ILogReceiverServer">
            <summary>
            Service contract for Log Receiver server.
            </summary>
        </member>
        <member name="M:NLog.LogReceiverService.ILogReceiverServer.ProcessLogMessages(NLog.LogReceiverService.NLogEvents)">
            <summary>
            Processes the log messages.
            </summary>
            <param name="events">The events.</param>
        </member>
        <member name="T:NLog.LogReceiverService.LogReceiverForwardingService">
            <summary>
            Implementation of <see cref="T:NLog.LogReceiverService.ILogReceiverServer"/> which forwards received logs through <see cref="T:NLog.LogManager"/> or a given <see cref="T:NLog.LogFactory"/>.
            </summary>
        </member>
        <member name="M:NLog.LogReceiverService.LogReceiverForwardingService.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LogReceiverService.LogReceiverForwardingService"/> class.
            </summary>
        </member>
        <member name="M:NLog.LogReceiverService.LogReceiverForwardingService.#ctor(NLog.LogFactory)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LogReceiverService.LogReceiverForwardingService"/> class.
            </summary>
            <param name="logFactory">The log factory.</param>
        </member>
        <member name="M:NLog.LogReceiverService.LogReceiverForwardingService.ProcessLogMessages(NLog.LogReceiverService.NLogEvents)">
            <summary>
            Processes the log messages.
            </summary>
            <param name="events">The events to process.</param>
        </member>
        <member name="M:NLog.LogReceiverService.LogReceiverForwardingService.ProcessLogMessages(NLog.LogEventInfo[])">
            <summary>
            Processes the log messages.
            </summary>
            <param name="logEvents">The log events.</param>
        </member>
        <member name="T:NLog.LogReceiverService.LogReceiverServiceConfig">
            <summary>
            Internal configuration of Log Receiver Service contracts.
            </summary>
        </member>
        <member name="T:NLog.LogReceiverService.NLogEvent">
            <summary>
            Wire format for NLog Event.
            </summary>
        </member>
        <member name="M:NLog.LogReceiverService.NLogEvent.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LogReceiverService.NLogEvent"/> class.
            </summary>
        </member>
        <member name="M:NLog.LogReceiverService.NLogEvent.ToEventInfo(NLog.LogReceiverService.NLogEvents,System.String)">
            <summary>
            Converts the <see cref="T:NLog.LogReceiverService.NLogEvent"/> to <see cref="T:NLog.LogEventInfo"/>.
            </summary>
            <param name="context">The <see cref="T:NLog.LogReceiverService.NLogEvent"/> object this <see cref="T:NLog.LogReceiverService.NLogEvent"/> is part of..</param>
            <param name="loggerNamePrefix">The logger name prefix to prepend in front of the logger name.</param>
            <returns>Converted <see cref="T:NLog.LogEventInfo"/>.</returns>
        </member>
        <member name="P:NLog.LogReceiverService.NLogEvent.Id">
            <summary>
            Gets or sets the client-generated identifier of the event.
            </summary>
        </member>
        <member name="P:NLog.LogReceiverService.NLogEvent.LevelOrdinal">
            <summary>
            Gets or sets the ordinal of the log level.
            </summary>
        </member>
        <member name="P:NLog.LogReceiverService.NLogEvent.LoggerOrdinal">
            <summary>
            Gets or sets the logger ordinal (index into <see cref="P:NLog.LogReceiverService.NLogEvents.Strings"/>.
            </summary>
            <value>The logger ordinal.</value>
        </member>
        <member name="P:NLog.LogReceiverService.NLogEvent.TimeDelta">
            <summary>
            Gets or sets the time delta (in ticks) between the time of the event and base time.
            </summary>
        </member>
        <member name="P:NLog.LogReceiverService.NLogEvent.MessageOrdinal">
            <summary>
            Gets or sets the message string index.
            </summary>
        </member>
        <member name="P:NLog.LogReceiverService.NLogEvent.Values">
            <summary>
            Gets or sets the collection of layout values.
            </summary>
        </member>
        <member name="P:NLog.LogReceiverService.NLogEvent.ValueIndexes">
            <summary>
            Gets the collection of indexes into <see cref="P:NLog.LogReceiverService.NLogEvents.Strings"/> array for each layout value.
            </summary>
        </member>
        <member name="T:NLog.LogReceiverService.NLogEvents">
            <summary>
            Wire format for NLog event package.
            </summary>
        </member>
        <member name="M:NLog.LogReceiverService.NLogEvents.ToEventInfo(System.String)">
            <summary>
            Converts the events to sequence of <see cref="T:NLog.LogEventInfo"/> objects suitable for routing through NLog.
            </summary>
            <param name="loggerNamePrefix">The logger name prefix to prepend in front of each logger name.</param>
            <returns>
            Sequence of <see cref="T:NLog.LogEventInfo"/> objects.
            </returns>
        </member>
        <member name="M:NLog.LogReceiverService.NLogEvents.ToEventInfo">
            <summary>
            Converts the events to sequence of <see cref="T:NLog.LogEventInfo"/> objects suitable for routing through NLog.
            </summary>
            <returns>
            Sequence of <see cref="T:NLog.LogEventInfo"/> objects.
            </returns>
        </member>
        <member name="P:NLog.LogReceiverService.NLogEvents.ClientName">
            <summary>
            Gets or sets the name of the client.
            </summary>
            <value>The name of the client.</value>
        </member>
        <member name="P:NLog.LogReceiverService.NLogEvents.BaseTimeUtc">
            <summary>
            Gets or sets the base time (UTC ticks) for all events in the package.
            </summary>
            <value>The base time UTC.</value>
        </member>
        <member name="P:NLog.LogReceiverService.NLogEvents.LayoutNames">
            <summary>
            Gets or sets the collection of layout names which are shared among all events.
            </summary>
            <value>The layout names.</value>
        </member>
        <member name="P:NLog.LogReceiverService.NLogEvents.Strings">
            <summary>
            Gets or sets the collection of logger names.
            </summary>
            <value>The logger names.</value>
        </member>
        <member name="P:NLog.LogReceiverService.NLogEvents.Events">
            <summary>
            Gets or sets the list of events.
            </summary>
            <value>The events.</value>
        </member>
        <member name="T:NLog.LogReceiverService.StringCollection">
            <summary>
            List of strings annotated for more terse serialization.
            </summary>
        </member>
        <member name="M:NLog.LogReceiverService.StringCollection.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LogReceiverService.StringCollection"/> class.
            </summary>
        </member>
        <member name="T:NLog.LogReceiverService.WcfLogReceiverClient">
            <summary>
            Log Receiver Client using WCF.
            </summary>
        </member>
        <member name="M:NLog.LogReceiverService.WcfLogReceiverClient.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LogReceiverService.WcfLogReceiverClient"/> class.
            </summary>
        </member>
        <member name="M:NLog.LogReceiverService.WcfLogReceiverClient.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LogReceiverService.WcfLogReceiverClient"/> class.
            </summary>
            <param name="endpointConfigurationName">Name of the endpoint configuration.</param>
        </member>
        <member name="M:NLog.LogReceiverService.WcfLogReceiverClient.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LogReceiverService.WcfLogReceiverClient"/> class.
            </summary>
            <param name="endpointConfigurationName">Name of the endpoint configuration.</param>
            <param name="remoteAddress">The remote address.</param>
        </member>
        <member name="M:NLog.LogReceiverService.WcfLogReceiverClient.#ctor(System.String,System.ServiceModel.EndpointAddress)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LogReceiverService.WcfLogReceiverClient"/> class.
            </summary>
            <param name="endpointConfigurationName">Name of the endpoint configuration.</param>
            <param name="remoteAddress">The remote address.</param>
        </member>
        <member name="M:NLog.LogReceiverService.WcfLogReceiverClient.#ctor(System.ServiceModel.Channels.Binding,System.ServiceModel.EndpointAddress)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.LogReceiverService.WcfLogReceiverClient"/> class.
            </summary>
            <param name="binding">The binding.</param>
            <param name="remoteAddress">The remote address.</param>
        </member>
        <member name="M:NLog.LogReceiverService.WcfLogReceiverClient.OpenAsync">
            <summary>
            Opens the client asynchronously.
            </summary>
        </member>
        <member name="M:NLog.LogReceiverService.WcfLogReceiverClient.OpenAsync(System.Object)">
            <summary>
            Opens the client asynchronously.
            </summary>
            <param name="userState">User-specific state.</param>
        </member>
        <member name="M:NLog.LogReceiverService.WcfLogReceiverClient.CloseAsync">
            <summary>
            Closes the client asynchronously.
            </summary>
        </member>
        <member name="M:NLog.LogReceiverService.WcfLogReceiverClient.CloseAsync(System.Object)">
            <summary>
            Closes the client asynchronously.
            </summary>
            <param name="userState">User-specific state.</param>
        </member>
        <member name="M:NLog.LogReceiverService.WcfLogReceiverClient.ProcessLogMessagesAsync(NLog.LogReceiverService.NLogEvents)">
            <summary>
            Processes the log messages asynchronously.
            </summary>
            <param name="events">The events to send.</param>
        </member>
        <member name="M:NLog.LogReceiverService.WcfLogReceiverClient.ProcessLogMessagesAsync(NLog.LogReceiverService.NLogEvents,System.Object)">
            <summary>
            Processes the log messages asynchronously.
            </summary>
            <param name="events">The events to send.</param>
            <param name="userState">User-specific state.</param>
        </member>
        <member name="M:NLog.LogReceiverService.WcfLogReceiverClient.NLog#LogReceiverService#ILogReceiverClient#BeginProcessLogMessages(NLog.LogReceiverService.NLogEvents,System.AsyncCallback,System.Object)">
            <summary>
            Begins processing of log messages.
            </summary>
            <param name="events">The events to send.</param>
            <param name="callback">The callback.</param>
            <param name="asyncState">Asynchronous state.</param>
            <returns>
            IAsyncResult value which can be passed to <see cref="M:NLog.LogReceiverService.ILogReceiverClient.EndProcessLogMessages(System.IAsyncResult)"/>.
            </returns>
        </member>
        <member name="M:NLog.LogReceiverService.WcfLogReceiverClient.NLog#LogReceiverService#ILogReceiverClient#EndProcessLogMessages(System.IAsyncResult)">
            <summary>
            Ends asynchronous processing of log messages.
            </summary>
            <param name="result">The result.</param>
        </member>
        <member name="M:NLog.LogReceiverService.WcfLogReceiverClient.CreateChannel">
            <summary>
            Returns a new channel from the client to the service.
            </summary>
            <returns>
            A channel of type <see cref="T:NLog.LogReceiverService.ILogReceiverClient"/> that identifies the type 
            of service contract encapsulated by this client object (proxy).
            </returns>
        </member>
        <member name="E:NLog.LogReceiverService.WcfLogReceiverClient.ProcessLogMessagesCompleted">
            <summary>
            Occurs when the log message processing has completed.
            </summary>
        </member>
        <member name="E:NLog.LogReceiverService.WcfLogReceiverClient.OpenCompleted">
            <summary>
            Occurs when Open operation has completed.
            </summary>
        </member>
        <member name="E:NLog.LogReceiverService.WcfLogReceiverClient.CloseCompleted">
            <summary>
            Occurs when Close operation has completed.
            </summary>
        </member>
        <member name="P:NLog.LogReceiverService.WcfLogReceiverClient.CookieContainer">
            <summary>
            Gets or sets the cookie container.
            </summary>
            <value>The cookie container.</value>
        </member>
        <member name="T:NLog.MappedDiagnosticsContext">
            <summary>
            Mapped Diagnostics Context - a thread-local structure that keeps a dictionary
            of strings and provides methods to output them in layouts. 
            Mostly for compatibility with log4net.
            </summary>
        </member>
        <member name="M:NLog.MappedDiagnosticsContext.Set(System.String,System.String)">
            <summary>
            Sets the current thread MDC item to the specified value.
            </summary>
            <param name="item">Item name.</param>
            <param name="value">Item value.</param>
        </member>
        <member name="M:NLog.MappedDiagnosticsContext.Get(System.String)">
            <summary>
            Gets the current thread MDC named item.
            </summary>
            <param name="item">Item name.</param>
            <returns>The item value of string.Empty if the value is not present.</returns>
        </member>
        <member name="M:NLog.MappedDiagnosticsContext.Contains(System.String)">
            <summary>
            Checks whether the specified item exists in current thread MDC.
            </summary>
            <param name="item">Item name.</param>
            <returns>A boolean indicating whether the specified item exists in current thread MDC.</returns>
        </member>
        <member name="M:NLog.MappedDiagnosticsContext.Remove(System.String)">
            <summary>
            Removes the specified item from current thread MDC.
            </summary>
            <param name="item">Item name.</param>
        </member>
        <member name="M:NLog.MappedDiagnosticsContext.Clear">
            <summary>
            Clears the content of current thread MDC.
            </summary>
        </member>
        <member name="T:NLog.MDC">
            <summary>
            Mapped Diagnostics Context - used for log4net compatibility.
            </summary>
        </member>
        <member name="M:NLog.MDC.Set(System.String,System.String)">
            <summary>
            Sets the current thread MDC item to the specified value.
            </summary>
            <param name="item">Item name.</param>
            <param name="value">Item value.</param>
        </member>
        <member name="M:NLog.MDC.Get(System.String)">
            <summary>
            Gets the current thread MDC named item.
            </summary>
            <param name="item">Item name.</param>
            <returns>The item value of string.Empty if the value is not present.</returns>
        </member>
        <member name="M:NLog.MDC.Contains(System.String)">
            <summary>
            Checks whether the specified item exists in current thread MDC.
            </summary>
            <param name="item">Item name.</param>
            <returns>A boolean indicating whether the specified item exists in current thread MDC.</returns>
        </member>
        <member name="M:NLog.MDC.Remove(System.String)">
            <summary>
            Removes the specified item from current thread MDC.
            </summary>
            <param name="item">Item name.</param>
        </member>
        <member name="M:NLog.MDC.Clear">
            <summary>
            Clears the content of current thread MDC.
            </summary>
        </member>
        <member name="T:NLog.NDC">
            <summary>
            Nested Diagnostics Context - for log4net compatibility.
            </summary>
        </member>
        <member name="M:NLog.NDC.Push(System.String)">
            <summary>
            Pushes the specified text on current thread NDC.
            </summary>
            <param name="text">The text to be pushed.</param>
            <returns>An instance of the object that implements IDisposable that returns the stack to the previous level when IDisposable.Dispose() is called. To be used with C# using() statement.</returns>
        </member>
        <member name="M:NLog.NDC.Pop">
            <summary>
            Pops the top message off the NDC stack.
            </summary>
            <returns>The top message which is no longer on the stack.</returns>
        </member>
        <member name="M:NLog.NDC.Clear">
            <summary>
            Clears current thread NDC stack.
            </summary>
        </member>
        <member name="M:NLog.NDC.GetAllMessages">
            <summary>
            Gets all messages on the stack.
            </summary>
            <returns>Array of strings on the stack.</returns>
        </member>
        <member name="P:NLog.NDC.TopMessage">
            <summary>
            Gets the top NDC message but doesn't remove it.
            </summary>
            <returns>The top message. .</returns>
        </member>
        <member name="T:NLog.NestedDiagnosticsContext">
            <summary>
            Nested Diagnostics Context - a thread-local structure that keeps a stack
            of strings and provides methods to output them in layouts
            Mostly for compatibility with log4net.
            </summary>
        </member>
        <member name="M:NLog.NestedDiagnosticsContext.Push(System.String)">
            <summary>
            Pushes the specified text on current thread NDC.
            </summary>
            <param name="text">The text to be pushed.</param>
            <returns>An instance of the object that implements IDisposable that returns the stack to the previous level when IDisposable.Dispose() is called. To be used with C# using() statement.</returns>
        </member>
        <member name="M:NLog.NestedDiagnosticsContext.Pop">
            <summary>
            Pops the top message off the NDC stack.
            </summary>
            <returns>The top message which is no longer on the stack.</returns>
        </member>
        <member name="M:NLog.NestedDiagnosticsContext.Clear">
            <summary>
            Clears current thread NDC stack.
            </summary>
        </member>
        <member name="M:NLog.NestedDiagnosticsContext.GetAllMessages">
            <summary>
            Gets all messages on the stack.
            </summary>
            <returns>Array of strings on the stack.</returns>
        </member>
        <member name="P:NLog.NestedDiagnosticsContext.TopMessage">
            <summary>
            Gets the top NDC message but doesn't remove it.
            </summary>
            <returns>The top message. .</returns>
        </member>
        <member name="T:NLog.NestedDiagnosticsContext.StackPopper">
            <summary>
            Resets the stack to the original count during <see cref="M:System.IDisposable.Dispose"/>.
            </summary>
        </member>
        <member name="M:NLog.NestedDiagnosticsContext.StackPopper.#ctor(System.Collections.Generic.Stack{System.String},System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.NestedDiagnosticsContext.StackPopper"/> class.
            </summary>
            <param name="stack">The stack.</param>
            <param name="previousCount">The previous count.</param>
        </member>
        <member name="M:NLog.NestedDiagnosticsContext.StackPopper.System#IDisposable#Dispose">
            <summary>
            Reverts the stack to original item count.
            </summary>
        </member>
        <member name="T:NLog.NLogConfigurationException">
            <summary>
            Exception thrown during NLog configuration.
            </summary>
        </member>
        <member name="M:NLog.NLogConfigurationException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.NLogConfigurationException"/> class.
            </summary>
        </member>
        <member name="M:NLog.NLogConfigurationException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.NLogConfigurationException"/> class.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:NLog.NLogConfigurationException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.NLogConfigurationException"/> class.
            </summary>
            <param name="message">The message.</param>
            <param name="innerException">The inner exception.</param>
        </member>
        <member name="T:NLog.NLogRuntimeException">
            <summary>
            Exception thrown during log event processing.
            </summary>
        </member>
        <member name="M:NLog.NLogRuntimeException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.NLogRuntimeException"/> class.
            </summary>
        </member>
        <member name="M:NLog.NLogRuntimeException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.NLogRuntimeException"/> class.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:NLog.NLogRuntimeException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.NLogRuntimeException"/> class.
            </summary>
            <param name="message">The message.</param>
            <param name="innerException">The inner exception.</param>
        </member>
        <member name="T:NLog.Targets.ChainsawTarget">
            <summary>
            Sends log messages to the remote instance of Chainsaw application from log4j. 
            </summary>
            <seealso href="http://nlog-project.org/wiki/Chainsaw_target">Documentation on NLog Wiki</seealso>
            <example>
            <p>
            To set up the target in the <a href="config.html">configuration file</a>, 
            use the following syntax:
            </p>
            <code lang="XML" source="examples/targets/Configuration File/Chainsaw/NLog.config" />
            <p>
            This assumes just one target and a single rule. More configuration
            options are described <a href="config.html">here</a>.
            </p>
            <p>
            To set up the log target programmatically use code like this:
            </p>
            <code lang="C#" source="examples/targets/Configuration API/Chainsaw/Simple/Example.cs" />
            <p>
            NOTE: If your receiver application is ever likely to be off-line, don't use TCP protocol
            or you'll get TCP timeouts and your application will crawl. 
            Either switch to UDP transport or use <a href="target.AsyncWrapper.html">AsyncWrapper</a> target
            so that your application threads will not be blocked by the timing-out connection attempts.
            </p>
            </example>
        </member>
        <member name="T:NLog.Targets.NLogViewerTarget">
            <summary>
            Sends log messages to the remote instance of NLog Viewer. 
            </summary>
            <seealso href="http://nlog-project.org/wiki/NLogViewer_target">Documentation on NLog Wiki</seealso>
            <example>
            <p>
            To set up the target in the <a href="config.html">configuration file</a>, 
            use the following syntax:
            </p>
            <code lang="XML" source="examples/targets/Configuration File/NLogViewer/NLog.config" />
            <p>
            This assumes just one target and a single rule. More configuration
            options are described <a href="config.html">here</a>.
            </p>
            <p>
            To set up the log target programmatically use code like this:
            </p>
            <code lang="C#" source="examples/targets/Configuration API/NLogViewer/Simple/Example.cs" />
            <p>
            NOTE: If your receiver application is ever likely to be off-line, don't use TCP protocol
            or you'll get TCP timeouts and your application will crawl. 
            Either switch to UDP transport or use <a href="target.AsyncWrapper.html">AsyncWrapper</a> target
            so that your application threads will not be blocked by the timing-out connection attempts.
            </p>
            </example>
        </member>
        <member name="T:NLog.Targets.NetworkTarget">
            <summary>
            Sends log messages over the network.
            </summary>
            <seealso href="http://nlog-project.org/wiki/Network_target">Documentation on NLog Wiki</seealso>
            <example>
            <p>
            To set up the target in the <a href="config.html">configuration file</a>, 
            use the following syntax:
            </p>
            <code lang="XML" source="examples/targets/Configuration File/Network/NLog.config" />
            <p>
            This assumes just one target and a single rule. More configuration
            options are described <a href="config.html">here</a>.
            </p>
            <p>
            To set up the log target programmatically use code like this:
            </p>
            <code lang="C#" source="examples/targets/Configuration API/Network/Simple/Example.cs" />
            <p>
            To print the results, use any application that's able to receive messages over
            TCP or UDP. <a href="http://m.nu/program/util/netcat/netcat.html">NetCat</a> is
            a simple but very powerful command-line tool that can be used for that. This image
            demonstrates the NetCat tool receiving log messages from Network target.
            </p>
            <img src="examples/targets/Screenshots/Network/Output.gif" />
            <p>
            NOTE: If your receiver application is ever likely to be off-line, don't use TCP protocol
            or you'll get TCP timeouts and your application will be very slow. 
            Either switch to UDP transport or use <a href="target.AsyncWrapper.html">AsyncWrapper</a> target
            so that your application threads will not be blocked by the timing-out connection attempts.
            </p>
            <p>
            There are two specialized versions of the Network target: <a href="target.Chainsaw.html">Chainsaw</a>
            and <a href="target.NLogViewer.html">NLogViewer</a> which write to instances of Chainsaw log4j viewer
            or NLogViewer application respectively.
            </p>
            </example>
        </member>
        <member name="T:NLog.Targets.TargetWithLayout">
            <summary>
            Represents target that supports string formatting using layouts.
            </summary>
        </member>
        <member name="T:NLog.Targets.Target">
            <summary>
            Represents logging target.
            </summary>
        </member>
        <member name="M:NLog.Targets.Target.NLog#Internal#ISupportsInitialize#Initialize(NLog.Config.LoggingConfiguration)">
            <summary>
            Initializes this instance.
            </summary>
            <param name="configuration">The configuration.</param>
        </member>
        <member name="M:NLog.Targets.Target.NLog#Internal#ISupportsInitialize#Close">
            <summary>
            Closes this instance.
            </summary>
        </member>
        <member name="M:NLog.Targets.Target.Dispose">
            <summary>
            Closes the target.
            </summary>
        </member>
        <member name="M:NLog.Targets.Target.Flush(NLog.Common.AsyncContinuation)">
            <summary>
            Flush any pending log messages (in case of asynchronous targets).
            </summary>
            <param name="asyncContinuation">The asynchronous continuation.</param>
        </member>
        <member name="M:NLog.Targets.Target.PrecalculateVolatileLayouts(NLog.LogEventInfo)">
            <summary>
            Calls the <see cref="M:NLog.Layouts.Layout.Precalculate(NLog.LogEventInfo)"/> on each volatile layout
            used by this target.
            </summary>
            <param name="logEvent">
            The log event.
            </param>
        </member>
        <member name="M:NLog.Targets.Target.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:NLog.Targets.Target.WriteAsyncLogEvent(NLog.Common.AsyncLogEventInfo)">
            <summary>
            Writes the log to the target.
            </summary>
            <param name="logEvent">Log event to write.</param>
        </member>
        <member name="M:NLog.Targets.Target.WriteAsyncLogEvents(NLog.Common.AsyncLogEventInfo[])">
            <summary>
            Writes the array of log events.
            </summary>
            <param name="logEvents">The log events.</param>
        </member>
        <member name="M:NLog.Targets.Target.Initialize(NLog.Config.LoggingConfiguration)">
            <summary>
            Initializes this instance.
            </summary>
            <param name="configuration">The configuration.</param>
        </member>
        <member name="M:NLog.Targets.Target.Close">
            <summary>
            Closes this instance.
            </summary>
        </member>
        <member name="M:NLog.Targets.Target.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
            <param name="disposing">True to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="M:NLog.Targets.Target.InitializeTarget">
            <summary>
            Initializes the target. Can be used by inheriting classes
            to initialize logging.
            </summary>
        </member>
        <member name="M:NLog.Targets.Target.CloseTarget">
            <summary>
            Closes the target and releases any unmanaged resources.
            </summary>
        </member>
        <member name="M:NLog.Targets.Target.FlushAsync(NLog.Common.AsyncContinuation)">
            <summary>
            Flush any pending log messages asynchronously (in case of asynchronous targets).
            </summary>
            <param name="asyncContinuation">The asynchronous continuation.</param>
        </member>
        <member name="M:NLog.Targets.Target.Write(NLog.LogEventInfo)">
            <summary>
            Writes logging event to the log target.
            classes.
            </summary>
            <param name="logEvent">
            Logging event to be written out.
            </param>
        </member>
        <member name="M:NLog.Targets.Target.Write(NLog.Common.AsyncLogEventInfo)">
            <summary>
            Writes log event to the log target. Must be overridden in inheriting
            classes.
            </summary>
            <param name="logEvent">Log event to be written out.</param>
        </member>
        <member name="M:NLog.Targets.Target.Write(NLog.Common.AsyncLogEventInfo[])">
            <summary>
            Writes an array of logging events to the log target. By default it iterates on all
            events and passes them to "Write" method. Inheriting classes can use this method to
            optimize batch writes.
            </summary>
            <param name="logEvents">Logging events to be written out.</param>
        </member>
        <member name="P:NLog.Targets.Target.Name">
            <summary>
            Gets or sets the name of the target.
            </summary>
            <docgen category='General Options' order='10' />
        </member>
        <member name="P:NLog.Targets.Target.SyncRoot">
            <summary>
            Gets the object which can be used to synchronize asynchronous operations that must rely on the .
            </summary>
        </member>
        <member name="P:NLog.Targets.Target.LoggingConfiguration">
            <summary>
            Gets the logging configuration this target is part of.
            </summary>
        </member>
        <member name="P:NLog.Targets.Target.IsInitialized">
            <summary>
            Gets a value indicating whether the target has been initialized.
            </summary>
        </member>
        <member name="M:NLog.Targets.TargetWithLayout.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.TargetWithLayout"/> class.
            </summary>
            <remarks>
            The default value of the layout is: <code>${longdate}|${level:uppercase=true}|${logger}|${message}</code>
            </remarks>
        </member>
        <member name="P:NLog.Targets.TargetWithLayout.Layout">
            <summary>
            Gets or sets the layout used to format log messages.
            </summary>
            <docgen category='Layout Options' order='1' />
        </member>
        <member name="M:NLog.Targets.NetworkTarget.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.NetworkTarget"/> class.
            </summary>
            <remarks>
            The default value of the layout is: <code>${longdate}|${level:uppercase=true}|${logger}|${message}</code>
            </remarks>
        </member>
        <member name="M:NLog.Targets.NetworkTarget.FlushAsync(NLog.Common.AsyncContinuation)">
            <summary>
            Flush any pending log messages asynchronously (in case of asynchronous targets).
            </summary>
            <param name="asyncContinuation">The asynchronous continuation.</param>
        </member>
        <member name="M:NLog.Targets.NetworkTarget.CloseTarget">
            <summary>
            Closes the target.
            </summary>
        </member>
        <member name="M:NLog.Targets.NetworkTarget.Write(NLog.Common.AsyncLogEventInfo)">
            <summary>
            Sends the 
            rendered logging event over the network optionally concatenating it with a newline character.
            </summary>
            <param name="logEvent">The logging event.</param>
        </member>
        <member name="M:NLog.Targets.NetworkTarget.GetBytesToWrite(NLog.LogEventInfo)">
            <summary>
            Gets the bytes to be written.
            </summary>
            <param name="logEvent">Log event.</param>
            <returns>Byte array.</returns>
        </member>
        <member name="P:NLog.Targets.NetworkTarget.Address">
            <summary>
            Gets or sets the network address.
            </summary>
            <remarks>
            The network address can be:
            <ul>
            <li>tcp://host:port - TCP (auto select IPv4/IPv6) (not supported on Windows Phone 7.0)</li>
            <li>tcp4://host:port - force TCP/IPv4 (not supported on Windows Phone 7.0)</li>
            <li>tcp6://host:port - force TCP/IPv6 (not supported on Windows Phone 7.0)</li>
            <li>udp://host:port - UDP (auto select IPv4/IPv6, not supported on Silverlight and on Windows Phone 7.0)</li>
            <li>udp4://host:port - force UDP/IPv4 (not supported on Silverlight and on Windows Phone 7.0)</li>
            <li>udp6://host:port - force UDP/IPv6  (not supported on Silverlight and on Windows Phone 7.0)</li>
            <li>http://host:port/pageName - HTTP using POST verb</li>
            <li>https://host:port/pageName - HTTPS using POST verb</li>
            </ul>
            For SOAP-based webservice support over HTTP use WebService target.
            </remarks>
            <docgen category='Connection Options' order='10' />
        </member>
        <member name="P:NLog.Targets.NetworkTarget.KeepConnection">
            <summary>
            Gets or sets a value indicating whether to keep connection open whenever possible.
            </summary>
            <docgen category='Connection Options' order='10' />
        </member>
        <member name="P:NLog.Targets.NetworkTarget.NewLine">
            <summary>
            Gets or sets a value indicating whether to append newline at the end of log message.
            </summary>
            <docgen category='Layout Options' order='10' />
        </member>
        <member name="P:NLog.Targets.NetworkTarget.MaxMessageSize">
            <summary>
            Gets or sets the maximum message size in bytes.
            </summary>
            <docgen category='Layout Options' order='10' />
        </member>
        <member name="P:NLog.Targets.NetworkTarget.ConnectionCacheSize">
            <summary>
            Gets or sets the size of the connection cache (number of connections which are kept alive).
            </summary>
            <docgen category="Connection Options" order="10"/>
        </member>
        <member name="P:NLog.Targets.NetworkTarget.OnOverflow">
            <summary>
            Gets or sets the action that should be taken if the message is larger than
            maxMessageSize.
            </summary>
            <docgen category='Layout Options' order='10' />
        </member>
        <member name="P:NLog.Targets.NetworkTarget.Encoding">
            <summary>
            Gets or sets the encoding to be used.
            </summary>
            <docgen category='Layout Options' order='10' />
        </member>
        <member name="M:NLog.Targets.NLogViewerTarget.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.NLogViewerTarget"/> class.
            </summary>
            <remarks>
            The default value of the layout is: <code>${longdate}|${level:uppercase=true}|${logger}|${message}</code>
            </remarks>
        </member>
        <member name="P:NLog.Targets.NLogViewerTarget.IncludeNLogData">
            <summary>
            Gets or sets a value indicating whether to include NLog-specific extensions to log4j schema.
            </summary>
            <docgen category='Payload Options' order='10' />
        </member>
        <member name="P:NLog.Targets.NLogViewerTarget.AppInfo">
            <summary>
            Gets or sets the AppInfo field. By default it's the friendly name of the current AppDomain.
            </summary>
            <docgen category='Payload Options' order='10' />
        </member>
        <member name="P:NLog.Targets.NLogViewerTarget.IncludeCallSite">
            <summary>
            Gets or sets a value indicating whether to include call site (class and method name) in the information sent over the network.
            </summary>
            <docgen category='Payload Options' order='10' />
        </member>
        <member name="P:NLog.Targets.NLogViewerTarget.IncludeMdc">
            <summary>
            Gets or sets a value indicating whether to include <see cref="T:NLog.MappedDiagnosticsContext"/> dictionary contents.
            </summary>
            <docgen category="Payload Options" order="10"/>
        </member>
        <member name="P:NLog.Targets.NLogViewerTarget.IncludeNdc">
            <summary>
            Gets or sets a value indicating whether to include <see cref="T:NLog.NestedDiagnosticsContext"/> stack contents.
            </summary>
            <docgen category="Payload Options" order="10"/>
        </member>
        <member name="P:NLog.Targets.NLogViewerTarget.NdcItemSeparator">
            <summary>
            Gets or sets the NDC item separator.
            </summary>
            <docgen category='Payload Options' order='10' />
        </member>
        <member name="P:NLog.Targets.NLogViewerTarget.Parameters">
            <summary>
            Gets the collection of parameters. Each parameter contains a mapping
            between NLog layout and a named parameter.
            </summary>
            <docgen category='Payload Options' order='10' />
        </member>
        <member name="P:NLog.Targets.NLogViewerTarget.Renderer">
            <summary>
            Gets the layout renderer which produces Log4j-compatible XML events.
            </summary>
        </member>
        <member name="P:NLog.Targets.NLogViewerTarget.Layout">
            <summary>
            Gets or sets the instance of <see cref="T:NLog.Layouts.Log4JXmlEventLayout"/> that is used to format log messages.
            </summary>
            <docgen category="Layout Options" order="10"/>
        </member>
        <member name="M:NLog.Targets.ChainsawTarget.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.ChainsawTarget"/> class.
            </summary>
        </member>
        <member name="T:NLog.Targets.ConsoleTarget">
            <summary>
            Writes log messages to the console.
            </summary>
            <seealso href="http://nlog-project.org/wiki/Console_target">Documentation on NLog Wiki</seealso>
            <example>
            <p>
            To set up the target in the <a href="config.html">configuration file</a>, 
            use the following syntax:
            </p>
            <code lang="XML" source="examples/targets/Configuration File/Console/NLog.config" />
            <p>
            This assumes just one target and a single rule. More configuration
            options are described <a href="config.html">here</a>.
            </p>
            <p>
            To set up the log target programmatically use code like this:
            </p>
            <code lang="C#" source="examples/targets/Configuration API/Console/Simple/Example.cs" />
            </example>
        </member>
        <member name="T:NLog.Targets.TargetWithLayoutHeaderAndFooter">
            <summary>
            Represents target that supports string formatting using layouts.
            </summary>
        </member>
        <member name="M:NLog.Targets.TargetWithLayoutHeaderAndFooter.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.TargetWithLayoutHeaderAndFooter"/> class.
            </summary>
            <remarks>
            The default value of the layout is: <code>${longdate}|${level:uppercase=true}|${logger}|${message}</code>
            </remarks>
        </member>
        <member name="P:NLog.Targets.TargetWithLayoutHeaderAndFooter.Layout">
            <summary>
            Gets or sets the text to be rendered.
            </summary>
            <docgen category='Layout Options' order='1' />
        </member>
        <member name="P:NLog.Targets.TargetWithLayoutHeaderAndFooter.Footer">
            <summary>
            Gets or sets the footer.
            </summary>
            <docgen category='Layout Options' order='3' />
        </member>
        <member name="P:NLog.Targets.TargetWithLayoutHeaderAndFooter.Header">
            <summary>
            Gets or sets the header.
            </summary>
            <docgen category='Layout Options' order='2' />
        </member>
        <member name="P:NLog.Targets.TargetWithLayoutHeaderAndFooter.LHF">
            <summary>
            Gets or sets the layout with header and footer.
            </summary>
            <value>The layout with header and footer.</value>
        </member>
        <member name="M:NLog.Targets.ConsoleTarget.InitializeTarget">
            <summary>
            Initializes the target.
            </summary>
        </member>
        <member name="M:NLog.Targets.ConsoleTarget.CloseTarget">
            <summary>
            Closes the target and releases any unmanaged resources.
            </summary>
        </member>
        <member name="M:NLog.Targets.ConsoleTarget.Write(NLog.LogEventInfo)">
            <summary>
            Writes the specified logging event to the Console.Out or
            Console.Error depending on the value of the Error flag.
            </summary>
            <param name="logEvent">The logging event.</param>
            <remarks>
            Note that the Error option is not supported on .NET Compact Framework.
            </remarks>
        </member>
        <member name="P:NLog.Targets.ConsoleTarget.Error">
            <summary>
            Gets or sets a value indicating whether to send the log messages to the standard error instead of the standard output.
            </summary>
            <docgen category='Console Options' order='10' />
        </member>
        <member name="T:NLog.Targets.DebuggerTarget">
            <summary>
            Writes log messages to the attached managed debugger.
            </summary>
            <example>
            <p>
            To set up the target in the <a href="config.html">configuration file</a>, 
            use the following syntax:
            </p>
            <code lang="XML" source="examples/targets/Configuration File/Debugger/NLog.config" />
            <p>
            This assumes just one target and a single rule. More configuration
            options are described <a href="config.html">here</a>.
            </p>
            <p>
            To set up the log target programmatically use code like this:
            </p>
            <code lang="C#" source="examples/targets/Configuration API/Debugger/Simple/Example.cs" />
            </example>
        </member>
        <member name="M:NLog.Targets.DebuggerTarget.InitializeTarget">
            <summary>
            Initializes the target.
            </summary>
        </member>
        <member name="M:NLog.Targets.DebuggerTarget.CloseTarget">
            <summary>
            Closes the target and releases any unmanaged resources.
            </summary>
        </member>
        <member name="M:NLog.Targets.DebuggerTarget.Write(NLog.LogEventInfo)">
            <summary>
            Writes the specified logging event to the attached debugger.
            </summary>
            <param name="logEvent">The logging event.</param>
        </member>
        <member name="T:NLog.Targets.DebugTarget">
            <summary>
            Mock target - useful for testing.
            </summary>
            <seealso href="http://nlog-project.org/wiki/Debug_target">Documentation on NLog Wiki</seealso>
            <example>
            <p>
            To set up the target in the <a href="config.html">configuration file</a>, 
            use the following syntax:
            </p>
            <code lang="XML" source="examples/targets/Configuration File/Debug/NLog.config" />
            <p>
            This assumes just one target and a single rule. More configuration
            options are described <a href="config.html">here</a>.
            </p>
            <p>
            To set up the log target programmatically use code like this:
            </p>
            <code lang="C#" source="examples/targets/Configuration API/Debug/Simple/Example.cs" />
            </example>
        </member>
        <member name="M:NLog.Targets.DebugTarget.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.DebugTarget"/> class.
            </summary>
            <remarks>
            The default value of the layout is: <code>${longdate}|${level:uppercase=true}|${logger}|${message}</code>
            </remarks>
        </member>
        <member name="M:NLog.Targets.DebugTarget.Write(NLog.LogEventInfo)">
            <summary>
            Increases the number of messages.
            </summary>
            <param name="logEvent">The logging event.</param>
        </member>
        <member name="P:NLog.Targets.DebugTarget.Counter">
            <summary>
            Gets the number of times this target has been called.
            </summary>
            <docgen category='Debugging Options' order='10' />
        </member>
        <member name="P:NLog.Targets.DebugTarget.LastMessage">
            <summary>
            Gets the last message rendered by this target.
            </summary>
            <docgen category='Debugging Options' order='10' />
        </member>
        <member name="T:NLog.Targets.LogReceiverWebServiceTarget">
            <summary>
            Sends log messages to a NLog Receiver Service (using WCF or Web Services).
            </summary>
            <seealso href="http://nlog-project.org/wiki/LogReceiverService_target">Documentation on NLog Wiki</seealso>
        </member>
        <member name="M:NLog.Targets.LogReceiverWebServiceTarget.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.LogReceiverWebServiceTarget"/> class.
            </summary>
        </member>
        <member name="M:NLog.Targets.LogReceiverWebServiceTarget.OnSend(NLog.LogReceiverService.NLogEvents,System.Collections.Generic.IEnumerable{NLog.Common.AsyncLogEventInfo})">
            <summary>
            Called when log events are being sent (test hook).
            </summary>
            <param name="events">The events.</param>
            <param name="asyncContinuations">The async continuations.</param>
            <returns>True if events should be sent, false to stop processing them.</returns>
        </member>
        <member name="M:NLog.Targets.LogReceiverWebServiceTarget.Write(NLog.Common.AsyncLogEventInfo)">
            <summary>
            Writes logging event to the log target. Must be overridden in inheriting
            classes.
            </summary>
            <param name="logEvent">Logging event to be written out.</param>
        </member>
        <member name="M:NLog.Targets.LogReceiverWebServiceTarget.Write(NLog.Common.AsyncLogEventInfo[])">
            <summary>
            Writes an array of logging events to the log target. By default it iterates on all
            events and passes them to "Append" method. Inheriting classes can use this method to
            optimize batch writes.
            </summary>
            <param name="logEvents">Logging events to be written out.</param>
        </member>
        <member name="P:NLog.Targets.LogReceiverWebServiceTarget.EndpointAddress">
            <summary>
            Gets or sets the endpoint address.
            </summary>
            <value>The endpoint address.</value>
            <docgen category='Connection Options' order='10' />
        </member>
        <member name="P:NLog.Targets.LogReceiverWebServiceTarget.EndpointConfigurationName">
            <summary>
            Gets or sets the name of the endpoint configuration in WCF configuration file.
            </summary>
            <value>The name of the endpoint configuration.</value>
            <docgen category='Connection Options' order='10' />
        </member>
        <member name="P:NLog.Targets.LogReceiverWebServiceTarget.UseBinaryEncoding">
            <summary>
            Gets or sets a value indicating whether to use binary message encoding.
            </summary>
            <docgen category='Payload Options' order='10' />
        </member>
        <member name="P:NLog.Targets.LogReceiverWebServiceTarget.ClientId">
            <summary>
            Gets or sets the client ID.
            </summary>
            <value>The client ID.</value>
            <docgen category='Payload Options' order='10' />
        </member>
        <member name="P:NLog.Targets.LogReceiverWebServiceTarget.Parameters">
            <summary>
            Gets the list of parameters.
            </summary>
            <value>The parameters.</value>
            <docgen category='Payload Options' order='10' />
        </member>
        <member name="P:NLog.Targets.LogReceiverWebServiceTarget.IncludeEventProperties">
            <summary>
            Gets or sets a value indicating whether to include per-event properties in the payload sent to the server.
            </summary>
            <docgen category='Payload Options' order='10' />
        </member>
        <member name="T:NLog.Targets.MemoryTarget">
            <summary>
            Writes log messages to an ArrayList in memory for programmatic retrieval.
            </summary>
            <seealso href="http://nlog-project.org/wiki/Memory_target">Documentation on NLog Wiki</seealso>
            <example>
            <p>
            To set up the target in the <a href="config.html">configuration file</a>, 
            use the following syntax:
            </p>
            <code lang="XML" source="examples/targets/Configuration File/Memory/NLog.config" />
            <p>
            This assumes just one target and a single rule. More configuration
            options are described <a href="config.html">here</a>.
            </p>
            <p>
            To set up the log target programmatically use code like this:
            </p>
            <code lang="C#" source="examples/targets/Configuration API/Memory/Simple/Example.cs" />
            </example>
        </member>
        <member name="M:NLog.Targets.MemoryTarget.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.MemoryTarget"/> class.
            </summary>
            <remarks>
            The default value of the layout is: <code>${longdate}|${level:uppercase=true}|${logger}|${message}</code>
            </remarks>
        </member>
        <member name="M:NLog.Targets.MemoryTarget.Write(NLog.LogEventInfo)">
            <summary>
            Renders the logging event message and adds it to the internal ArrayList of log messages.
            </summary>
            <param name="logEvent">The logging event.</param>
        </member>
        <member name="P:NLog.Targets.MemoryTarget.Logs">
            <summary>
            Gets the list of logs gathered in the <see cref="T:NLog.Targets.MemoryTarget"/>.
            </summary>
        </member>
        <member name="T:NLog.Targets.MessageBoxTarget">
            <summary>
            Pops up log messages as message boxes.
            </summary>
            <seealso href="http://nlog-project.org/wiki/MessageBox_target">Documentation on NLog Wiki</seealso>
            <example>
            <p>
            To set up the target in the <a href="config.html">configuration file</a>, 
            use the following syntax:
            </p>
            <code lang="XML" source="examples/targets/Configuration File/MessageBox/NLog.config" />
            <p>
            This assumes just one target and a single rule. More configuration
            options are described <a href="config.html">here</a>.
            </p>
            <p>
            The result is a message box:
            </p>
            <img src="examples/targets/Screenshots/MessageBox/MessageBoxTarget.gif" />
            <p>
            To set up the log target programmatically use code like this:
            </p>
            <code lang="C#" source="examples/targets/Configuration API/MessageBox/Simple/Example.cs" />
            </example>
        </member>
        <member name="M:NLog.Targets.MessageBoxTarget.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.MessageBoxTarget"/> class.
            </summary>
            <remarks>
            The default value of the layout is: <code>${longdate}|${level:uppercase=true}|${logger}|${message}</code>
            </remarks>
        </member>
        <member name="M:NLog.Targets.MessageBoxTarget.Write(NLog.LogEventInfo)">
            <summary>
            Displays the message box with the log message and caption specified in the Caption
            parameter.
            </summary>
            <param name="logEvent">The logging event.</param>
        </member>
        <member name="M:NLog.Targets.MessageBoxTarget.Write(NLog.Common.AsyncLogEventInfo[])">
            <summary>
            Displays the message box with the array of rendered logs messages and caption specified in the Caption
            parameter.
            </summary>
            <param name="logEvents">The array of logging events.</param>
        </member>
        <member name="P:NLog.Targets.MessageBoxTarget.Caption">
            <summary>
            Gets or sets the message box title.
            </summary>
            <docgen category='UI Options' order='10' />
        </member>
        <member name="T:NLog.Targets.MethodCallParameter">
            <summary>
            A parameter to MethodCall.
            </summary>
        </member>
        <member name="M:NLog.Targets.MethodCallParameter.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.MethodCallParameter"/> class.
            </summary>
        </member>
        <member name="M:NLog.Targets.MethodCallParameter.#ctor(NLog.Layouts.Layout)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.MethodCallParameter"/> class.
            </summary>
            <param name="layout">The layout to use for parameter value.</param>
        </member>
        <member name="M:NLog.Targets.MethodCallParameter.#ctor(System.String,NLog.Layouts.Layout)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.MethodCallParameter"/> class.
            </summary>
            <param name="parameterName">Name of the parameter.</param>
            <param name="layout">The layout.</param>
        </member>
        <member name="M:NLog.Targets.MethodCallParameter.#ctor(System.String,NLog.Layouts.Layout,System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.MethodCallParameter"/> class.
            </summary>
            <param name="name">The name of the parameter.</param>
            <param name="layout">The layout.</param>
            <param name="type">The type of the parameter.</param>
        </member>
        <member name="P:NLog.Targets.MethodCallParameter.Name">
            <summary>
            Gets or sets the name of the parameter.
            </summary>
            <docgen category='Parameter Options' order='10' />
        </member>
        <member name="P:NLog.Targets.MethodCallParameter.Type">
            <summary>
            Gets or sets the type of the parameter.
            </summary>
            <docgen category='Parameter Options' order='10' />
        </member>
        <member name="P:NLog.Targets.MethodCallParameter.Layout">
            <summary>
            Gets or sets the layout that should be use to calcuate the value for the parameter.
            </summary>
            <docgen category='Parameter Options' order='10' />
        </member>
        <member name="T:NLog.Targets.MethodCallTarget">
            <summary>
            Calls the specified static method on each log message and passes contextual parameters to it.
            </summary>
            <seealso href="http://nlog-project.org/wiki/MethodCall_target">Documentation on NLog Wiki</seealso>
            <example>
            <p>
            To set up the target in the <a href="config.html">configuration file</a>, 
            use the following syntax:
            </p>
            <code lang="XML" source="examples/targets/Configuration File/MethodCall/NLog.config" />
            <p>
            This assumes just one target and a single rule. More configuration
            options are described <a href="config.html">here</a>.
            </p>
            <p>
            To set up the log target programmatically use code like this:
            </p>
            <code lang="C#" source="examples/targets/Configuration API/MethodCall/Simple/Example.cs" />
            </example>
        </member>
        <member name="T:NLog.Targets.MethodCallTargetBase">
            <summary>
            The base class for all targets which call methods (local or remote). 
            Manages parameters and type coercion.
            </summary>
        </member>
        <member name="M:NLog.Targets.MethodCallTargetBase.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.MethodCallTargetBase"/> class.
            </summary>
        </member>
        <member name="M:NLog.Targets.MethodCallTargetBase.Write(NLog.Common.AsyncLogEventInfo)">
            <summary>
            Prepares an array of parameters to be passed based on the logging event and calls DoInvoke().
            </summary>
            <param name="logEvent">
            The logging event.
            </param>
        </member>
        <member name="M:NLog.Targets.MethodCallTargetBase.DoInvoke(System.Object[],NLog.Common.AsyncContinuation)">
            <summary>
            Calls the target method. Must be implemented in concrete classes.
            </summary>
            <param name="parameters">Method call parameters.</param>
            <param name="continuation">The continuation.</param>
        </member>
        <member name="M:NLog.Targets.MethodCallTargetBase.DoInvoke(System.Object[])">
            <summary>
            Calls the target method. Must be implemented in concrete classes.
            </summary>
            <param name="parameters">Method call parameters.</param>
        </member>
        <member name="P:NLog.Targets.MethodCallTargetBase.Parameters">
            <summary>
            Gets the array of parameters to be passed.
            </summary>
            <docgen category='Parameter Options' order='10' />
        </member>
        <member name="M:NLog.Targets.MethodCallTarget.InitializeTarget">
            <summary>
            Initializes the target.
            </summary>
        </member>
        <member name="M:NLog.Targets.MethodCallTarget.DoInvoke(System.Object[])">
            <summary>
            Calls the specified Method.
            </summary>
            <param name="parameters">Method parameters.</param>
        </member>
        <member name="P:NLog.Targets.MethodCallTarget.ClassName">
            <summary>
            Gets or sets the class name.
            </summary>
            <docgen category='Invocation Options' order='10' />
        </member>
        <member name="P:NLog.Targets.MethodCallTarget.MethodName">
            <summary>
            Gets or sets the method name. The method must be public and static.
            </summary>
            <docgen category='Invocation Options' order='10' />
        </member>
        <member name="T:NLog.Targets.NetworkTargetOverflowAction">
            <summary>
            Action that should be taken if the message overflows.
            </summary>
        </member>
        <member name="F:NLog.Targets.NetworkTargetOverflowAction.Error">
            <summary>
            Report an error.
            </summary>
        </member>
        <member name="F:NLog.Targets.NetworkTargetOverflowAction.Split">
            <summary>
            Split the message into smaller pieces.
            </summary>
        </member>
        <member name="F:NLog.Targets.NetworkTargetOverflowAction.Discard">
            <summary>
            Discard the entire message.
            </summary>
        </member>
        <member name="T:NLog.Targets.NLogViewerParameterInfo">
            <summary>
            Represents a parameter to a NLogViewer target.
            </summary>
        </member>
        <member name="M:NLog.Targets.NLogViewerParameterInfo.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.NLogViewerParameterInfo"/> class.
            </summary>
        </member>
        <member name="P:NLog.Targets.NLogViewerParameterInfo.Name">
            <summary>
            Gets or sets viewer parameter name.
            </summary>
            <docgen category='Parameter Options' order='10' />
        </member>
        <member name="P:NLog.Targets.NLogViewerParameterInfo.Layout">
            <summary>
            Gets or sets the layout that should be use to calcuate the value for the parameter.
            </summary>
            <docgen category='Parameter Options' order='10' />
        </member>
        <member name="T:NLog.Targets.NullTarget">
            <summary>
            Discards log messages. Used mainly for debugging and benchmarking.
            </summary>
            <seealso href="http://nlog-project.org/wiki/Null_target">Documentation on NLog Wiki</seealso>
            <example>
            <p>
            To set up the target in the <a href="config.html">configuration file</a>, 
            use the following syntax:
            </p>
            <code lang="XML" source="examples/targets/Configuration File/Null/NLog.config" />
            <p>
            This assumes just one target and a single rule. More configuration
            options are described <a href="config.html">here</a>.
            </p>
            <p>
            To set up the log target programmatically use code like this:
            </p>
            <code lang="C#" source="examples/targets/Configuration API/Null/Simple/Example.cs" />
            </example>
        </member>
        <member name="M:NLog.Targets.NullTarget.Write(NLog.LogEventInfo)">
            <summary>
            Does nothing. Optionally it calculates the layout text but
            discards the results.
            </summary>
            <param name="logEvent">The logging event.</param>
        </member>
        <member name="P:NLog.Targets.NullTarget.FormatMessage">
            <summary>
            Gets or sets a value indicating whether to perform layout calculation.
            </summary>
            <docgen category='Layout Options' order='10' />
        </member>
        <member name="T:NLog.Targets.TargetAttribute">
            <summary>
            Marks class as a logging target and assigns a name to it.
            </summary>
        </member>
        <member name="M:NLog.Targets.TargetAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.TargetAttribute"/> class.
            </summary>
            <param name="name">Name of the target.</param>
        </member>
        <member name="P:NLog.Targets.TargetAttribute.IsWrapper">
            <summary>
            Gets or sets a value indicating whether to the target is a wrapper target (used to generate the target summary documentation page).
            </summary>
        </member>
        <member name="P:NLog.Targets.TargetAttribute.IsCompound">
            <summary>
            Gets or sets a value indicating whether to the target is a compound target (used to generate the target summary documentation page).
            </summary>
        </member>
        <member name="T:NLog.Targets.WebServiceProtocol">
            <summary>
            Web service protocol.
            </summary>
        </member>
        <member name="F:NLog.Targets.WebServiceProtocol.Soap11">
            <summary>
            Use SOAP 1.1 Protocol.
            </summary>
        </member>
        <member name="F:NLog.Targets.WebServiceProtocol.Soap12">
            <summary>
            Use SOAP 1.2 Protocol.
            </summary>
        </member>
        <member name="F:NLog.Targets.WebServiceProtocol.HttpPost">
            <summary>
            Use HTTP POST Protocol.
            </summary>
        </member>
        <member name="F:NLog.Targets.WebServiceProtocol.HttpGet">
            <summary>
            Use HTTP GET Protocol.
            </summary>
        </member>
        <member name="T:NLog.Targets.WebServiceTarget">
            <summary>
            Calls the specified web service on each log message.
            </summary>
            <seealso href="http://nlog-project.org/wiki/WebService_target">Documentation on NLog Wiki</seealso>
            <remarks>
            The web service must implement a method that accepts a number of string parameters.
            </remarks>
            <example>
            <p>
            To set up the target in the <a href="config.html">configuration file</a>, 
            use the following syntax:
            </p>
            <code lang="XML" source="examples/targets/Configuration File/WebService/NLog.config" />
            <p>
            This assumes just one target and a single rule. More configuration
            options are described <a href="config.html">here</a>.
            </p>
            <p>
            To set up the log target programmatically use code like this:
            </p>
            <code lang="C#" source="examples/targets/Configuration API/WebService/Simple/Example.cs" />
            <p>The example web service that works with this example is shown below</p>
            <code lang="C#" source="examples/targets/Configuration API/WebService/Simple/WebService1/Service1.asmx.cs" />
            </example>
        </member>
        <member name="M:NLog.Targets.WebServiceTarget.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.WebServiceTarget"/> class.
            </summary>
        </member>
        <member name="M:NLog.Targets.WebServiceTarget.DoInvoke(System.Object[])">
            <summary>
            Calls the target method. Must be implemented in concrete classes.
            </summary>
            <param name="parameters">Method call parameters.</param>
        </member>
        <member name="M:NLog.Targets.WebServiceTarget.DoInvoke(System.Object[],NLog.Common.AsyncContinuation)">
            <summary>
            Invokes the web service method.
            </summary>
            <param name="parameters">Parameters to be passed.</param>
            <param name="continuation">The continuation.</param>
        </member>
        <member name="P:NLog.Targets.WebServiceTarget.Url">
            <summary>
            Gets or sets the web service URL.
            </summary>
            <docgen category='Web Service Options' order='10' />
        </member>
        <member name="P:NLog.Targets.WebServiceTarget.MethodName">
            <summary>
            Gets or sets the Web service method name.
            </summary>
            <docgen category='Web Service Options' order='10' />
        </member>
        <member name="P:NLog.Targets.WebServiceTarget.Namespace">
            <summary>
            Gets or sets the Web service namespace.
            </summary>
            <docgen category='Web Service Options' order='10' />
        </member>
        <member name="P:NLog.Targets.WebServiceTarget.Protocol">
            <summary>
            Gets or sets the protocol to be used when calling web service.
            </summary>
            <docgen category='Web Service Options' order='10' />
        </member>
        <member name="P:NLog.Targets.WebServiceTarget.Encoding">
            <summary>
            Gets or sets the encoding.
            </summary>
            <docgen category='Web Service Options' order='10' />
        </member>
        <member name="T:NLog.Targets.Wrappers.AsyncRequestQueue">
            <summary>
            Asynchronous request queue.
            </summary>
        </member>
        <member name="M:NLog.Targets.Wrappers.AsyncRequestQueue.#ctor(System.Int32,NLog.Targets.Wrappers.AsyncTargetWrapperOverflowAction)">
            <summary>
            Initializes a new instance of the AsyncRequestQueue class.
            </summary>
            <param name="requestLimit">Request limit.</param>
            <param name="overflowAction">The overflow action.</param>
        </member>
        <member name="M:NLog.Targets.Wrappers.AsyncRequestQueue.Enqueue(NLog.Common.AsyncLogEventInfo)">
            <summary>
            Enqueues another item. If the queue is overflown the appropriate
            action is taken as specified by <see cref="P:NLog.Targets.Wrappers.AsyncRequestQueue.OnOverflow"/>.
            </summary>
            <param name="logEventInfo">The log event info.</param>
        </member>
        <member name="M:NLog.Targets.Wrappers.AsyncRequestQueue.DequeueBatch(System.Int32)">
            <summary>
            Dequeues a maximum of <c>count</c> items from the queue
            and adds returns the list containing them.
            </summary>
            <param name="count">Maximum number of items to be dequeued.</param>
            <returns>The array of log events.</returns>
        </member>
        <member name="M:NLog.Targets.Wrappers.AsyncRequestQueue.Clear">
            <summary>
            Clears the queue.
            </summary>
        </member>
        <member name="P:NLog.Targets.Wrappers.AsyncRequestQueue.RequestLimit">
            <summary>
            Gets or sets the request limit.
            </summary>
        </member>
        <member name="P:NLog.Targets.Wrappers.AsyncRequestQueue.OnOverflow">
            <summary>
            Gets or sets the action to be taken when there's no more room in
            the queue and another request is enqueued.
            </summary>
        </member>
        <member name="P:NLog.Targets.Wrappers.AsyncRequestQueue.RequestCount">
            <summary>
            Gets the number of requests currently in the queue.
            </summary>
        </member>
        <member name="T:NLog.Targets.Wrappers.AsyncTargetWrapper">
            <summary>
            Provides asynchronous, buffered execution of target writes.
            </summary>
            <seealso href="http://nlog-project.org/wiki/AsyncWrapper_target">Documentation on NLog Wiki</seealso>
            <remarks>
            <p>
            Asynchronous target wrapper allows the logger code to execute more quickly, by queueing
            messages and processing them in a separate thread. You should wrap targets
            that spend a non-trivial amount of time in their Write() method with asynchronous
            target to speed up logging.
            </p>
            <p>
            Because asynchronous logging is quite a common scenario, NLog supports a
            shorthand notation for wrapping all targets with AsyncWrapper. Just add async="true" to
            the &lt;targets/&gt; element in the configuration file.
            </p>
            <code lang="XML">
            <![CDATA[
            <targets async="true">
               ... your targets go here ...
            </targets>
            ]]></code>
            </remarks>
            <example>
            <p>
            To set up the target in the <a href="config.html">configuration file</a>, 
            use the following syntax:
            </p>
            <code lang="XML" source="examples/targets/Configuration File/AsyncWrapper/NLog.config" />
            <p>
            The above examples assume just one target and a single rule. See below for
            a programmatic configuration that's equivalent to the above config file:
            </p>
            <code lang="C#" source="examples/targets/Configuration API/AsyncWrapper/Wrapping File/Example.cs" />
            </example>
        </member>
        <member name="T:NLog.Targets.Wrappers.WrapperTargetBase">
            <summary>
            Base class for targets wrap other (single) targets.
            </summary>
        </member>
        <member name="M:NLog.Targets.Wrappers.WrapperTargetBase.ToString">
            <summary>
            Returns the text representation of the object. Used for diagnostics.
            </summary>
            <returns>A string that describes the target.</returns>
        </member>
        <member name="M:NLog.Targets.Wrappers.WrapperTargetBase.FlushAsync(NLog.Common.AsyncContinuation)">
            <summary>
            Flush any pending log messages (in case of asynchronous targets).
            </summary>
            <param name="asyncContinuation">The asynchronous continuation.</param>
        </member>
        <member name="M:NLog.Targets.Wrappers.WrapperTargetBase.Write(NLog.LogEventInfo)">
            <summary>
            Writes logging event to the log target. Must be overridden in inheriting
            classes.
            </summary>
            <param name="logEvent">Logging event to be written out.</param>
        </member>
        <member name="P:NLog.Targets.Wrappers.WrapperTargetBase.WrappedTarget">
            <summary>
            Gets or sets the target that is wrapped by this target.
            </summary>
            <docgen category='General Options' order='11' />
        </member>
        <member name="M:NLog.Targets.Wrappers.AsyncTargetWrapper.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.Wrappers.AsyncTargetWrapper"/> class.
            </summary>
        </member>
        <member name="M:NLog.Targets.Wrappers.AsyncTargetWrapper.#ctor(NLog.Targets.Target)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.Wrappers.AsyncTargetWrapper"/> class.
            </summary>
            <param name="wrappedTarget">The wrapped target.</param>
        </member>
        <member name="M:NLog.Targets.Wrappers.AsyncTargetWrapper.#ctor(NLog.Targets.Target,System.Int32,NLog.Targets.Wrappers.AsyncTargetWrapperOverflowAction)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.Wrappers.AsyncTargetWrapper"/> class.
            </summary>
            <param name="wrappedTarget">The wrapped target.</param>
            <param name="queueLimit">Maximum number of requests in the queue.</param>
            <param name="overflowAction">The action to be taken when the queue overflows.</param>
        </member>
        <member name="M:NLog.Targets.Wrappers.AsyncTargetWrapper.FlushAsync(NLog.Common.AsyncContinuation)">
            <summary>
            Waits for the lazy writer thread to finish writing messages.
            </summary>
            <param name="asyncContinuation">The asynchronous continuation.</param>
        </member>
        <member name="M:NLog.Targets.Wrappers.AsyncTargetWrapper.InitializeTarget">
            <summary>
            Initializes the target by starting the lazy writer timer.
            </summary>
        </member>
        <member name="M:NLog.Targets.Wrappers.AsyncTargetWrapper.CloseTarget">
            <summary>
            Shuts down the lazy writer timer.
            </summary>
        </member>
        <member name="M:NLog.Targets.Wrappers.AsyncTargetWrapper.StartLazyWriterTimer">
            <summary>
            Starts the lazy writer thread which periodically writes
            queued log messages.
            </summary>
        </member>
        <member name="M:NLog.Targets.Wrappers.AsyncTargetWrapper.StopLazyWriterThread">
            <summary>
            Starts the lazy writer thread.
            </summary>
        </member>
        <member name="M:NLog.Targets.Wrappers.AsyncTargetWrapper.Write(NLog.Common.AsyncLogEventInfo)">
            <summary>
            Adds the log event to asynchronous queue to be processed by
            the lazy writer thread.
            </summary>
            <param name="logEvent">The log event.</param>
            <remarks>
            The <see cref="M:NLog.Targets.Target.PrecalculateVolatileLayouts(NLog.LogEventInfo)"/> is called
            to ensure that the log event can be processed in another thread.
            </remarks>
        </member>
        <member name="P:NLog.Targets.Wrappers.AsyncTargetWrapper.BatchSize">
            <summary>
            Gets or sets the number of log events that should be processed in a batch
            by the lazy writer thread.
            </summary>
            <docgen category='Buffering Options' order='100' />
        </member>
        <member name="P:NLog.Targets.Wrappers.AsyncTargetWrapper.TimeToSleepBetweenBatches">
            <summary>
            Gets or sets the time in milliseconds to sleep between batches.
            </summary>
            <docgen category='Buffering Options' order='100' />
        </member>
        <member name="P:NLog.Targets.Wrappers.AsyncTargetWrapper.OverflowAction">
            <summary>
            Gets or sets the action to be taken when the lazy writer thread request queue count
            exceeds the set limit.
            </summary>
            <docgen category='Buffering Options' order='100' />
        </member>
        <member name="P:NLog.Targets.Wrappers.AsyncTargetWrapper.QueueLimit">
            <summary>
            Gets or sets the limit on the number of requests in the lazy writer thread request queue.
            </summary>
            <docgen category='Buffering Options' order='100' />
        </member>
        <member name="P:NLog.Targets.Wrappers.AsyncTargetWrapper.RequestQueue">
            <summary>
            Gets the queue of lazy writer thread requests.
            </summary>
        </member>
        <member name="T:NLog.Targets.Wrappers.AsyncTargetWrapperOverflowAction">
            <summary>
            The action to be taken when the queue overflows.
            </summary>
        </member>
        <member name="F:NLog.Targets.Wrappers.AsyncTargetWrapperOverflowAction.Grow">
            <summary>
            Grow the queue.
            </summary>
        </member>
        <member name="F:NLog.Targets.Wrappers.AsyncTargetWrapperOverflowAction.Discard">
            <summary>
            Discard the overflowing item.
            </summary>
        </member>
        <member name="F:NLog.Targets.Wrappers.AsyncTargetWrapperOverflowAction.Block">
            <summary>
            Block until there's more room in the queue.
            </summary>
        </member>
        <member name="T:NLog.Targets.Wrappers.AutoFlushTargetWrapper">
            <summary>
            Causes a flush after each write on a wrapped target.
            </summary>
            <seealso href="http://nlog-project.org/wiki/AutoFlushWrapper_target">Documentation on NLog Wiki</seealso>
            <example>
            <p>
            To set up the target in the <a href="config.html">configuration file</a>, 
            use the following syntax:
            </p>
            <code lang="XML" source="examples/targets/Configuration File/AutoFlushWrapper/NLog.config" />
            <p>
            The above examples assume just one target and a single rule. See below for
            a programmatic configuration that's equivalent to the above config file:
            </p>
            <code lang="C#" source="examples/targets/Configuration API/AutoFlushWrapper/Simple/Example.cs" />
            </example>
        </member>
        <member name="M:NLog.Targets.Wrappers.AutoFlushTargetWrapper.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.Wrappers.AutoFlushTargetWrapper"/> class.
            </summary>
            <remarks>
            The default value of the layout is: <code>${longdate}|${level:uppercase=true}|${logger}|${message}</code>
            </remarks>
        </member>
        <member name="M:NLog.Targets.Wrappers.AutoFlushTargetWrapper.#ctor(NLog.Targets.Target)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.Wrappers.AutoFlushTargetWrapper"/> class.
            </summary>
            <param name="wrappedTarget">The wrapped target.</param>
        </member>
        <member name="M:NLog.Targets.Wrappers.AutoFlushTargetWrapper.Write(NLog.Common.AsyncLogEventInfo)">
            <summary>
            Forwards the call to the <see cref="P:NLog.Targets.Wrappers.WrapperTargetBase.WrappedTarget"/>.Write()
            and calls <see cref="M:NLog.Targets.Target.Flush(NLog.Common.AsyncContinuation)"/> on it.
            </summary>
            <param name="logEvent">Logging event to be written out.</param>
        </member>
        <member name="T:NLog.Targets.Wrappers.BufferingTargetWrapper">
            <summary>
            A target that buffers log events and sends them in batches to the wrapped target.
            </summary>
            <seealso href="http://nlog-project.org/wiki/BufferingWrapper_target">Documentation on NLog Wiki</seealso>
        </member>
        <member name="M:NLog.Targets.Wrappers.BufferingTargetWrapper.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.Wrappers.BufferingTargetWrapper"/> class.
            </summary>
        </member>
        <member name="M:NLog.Targets.Wrappers.BufferingTargetWrapper.#ctor(NLog.Targets.Target)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.Wrappers.BufferingTargetWrapper"/> class.
            </summary>
            <param name="wrappedTarget">The wrapped target.</param>
        </member>
        <member name="M:NLog.Targets.Wrappers.BufferingTargetWrapper.#ctor(NLog.Targets.Target,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.Wrappers.BufferingTargetWrapper"/> class.
            </summary>
            <param name="wrappedTarget">The wrapped target.</param>
            <param name="bufferSize">Size of the buffer.</param>
        </member>
        <member name="M:NLog.Targets.Wrappers.BufferingTargetWrapper.#ctor(NLog.Targets.Target,System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.Wrappers.BufferingTargetWrapper"/> class.
            </summary>
            <param name="wrappedTarget">The wrapped target.</param>
            <param name="bufferSize">Size of the buffer.</param>
            <param name="flushTimeout">The flush timeout.</param>
        </member>
        <member name="M:NLog.Targets.Wrappers.BufferingTargetWrapper.FlushAsync(NLog.Common.AsyncContinuation)">
            <summary>
            Flushes pending events in the buffer (if any).
            </summary>
            <param name="asyncContinuation">The asynchronous continuation.</param>
        </member>
        <member name="M:NLog.Targets.Wrappers.BufferingTargetWrapper.InitializeTarget">
            <summary>
            Initializes the target.
            </summary>
        </member>
        <member name="M:NLog.Targets.Wrappers.BufferingTargetWrapper.CloseTarget">
            <summary>
            Closes the target by flushing pending events in the buffer (if any).
            </summary>
        </member>
        <member name="M:NLog.Targets.Wrappers.BufferingTargetWrapper.Write(NLog.Common.AsyncLogEventInfo)">
            <summary>
            Adds the specified log event to the buffer and flushes
            the buffer in case the buffer gets full.
            </summary>
            <param name="logEvent">The log event.</param>
        </member>
        <member name="P:NLog.Targets.Wrappers.BufferingTargetWrapper.BufferSize">
            <summary>
            Gets or sets the number of log events to be buffered.
            </summary>
            <docgen category='Buffering Options' order='100' />
        </member>
        <member name="P:NLog.Targets.Wrappers.BufferingTargetWrapper.FlushTimeout">
            <summary>
            Gets or sets the timeout (in milliseconds) after which the contents of buffer will be flushed 
            if there's no write in the specified period of time. Use -1 to disable timed flushes.
            </summary>
            <docgen category='Buffering Options' order='100' />
        </member>
        <member name="P:NLog.Targets.Wrappers.BufferingTargetWrapper.SlidingTimeout">
            <summary>
            Gets or sets a value indicating whether to use sliding timeout.
            </summary>
            <remarks>
            This value determines how the inactivity period is determined. If sliding timeout is enabled,
            the inactivity timer is reset after each write, if it is disabled - inactivity timer will 
            count from the first event written to the buffer. 
            </remarks>
            <docgen category='Buffering Options' order='100' />
        </member>
        <member name="T:NLog.Targets.Wrappers.CompoundTargetBase">
            <summary>
            A base class for targets which wrap other (multiple) targets
            and provide various forms of target routing.
            </summary>
        </member>
        <member name="M:NLog.Targets.Wrappers.CompoundTargetBase.#ctor(NLog.Targets.Target[])">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.Wrappers.CompoundTargetBase"/> class.
            </summary>
            <param name="targets">The targets.</param>
        </member>
        <member name="M:NLog.Targets.Wrappers.CompoundTargetBase.ToString">
            <summary>
            Returns the text representation of the object. Used for diagnostics.
            </summary>
            <returns>A string that describes the target.</returns>
        </member>
        <member name="M:NLog.Targets.Wrappers.CompoundTargetBase.Write(NLog.LogEventInfo)">
            <summary>
            Writes logging event to the log target.
            </summary>
            <param name="logEvent">Logging event to be written out.</param>
        </member>
        <member name="M:NLog.Targets.Wrappers.CompoundTargetBase.FlushAsync(NLog.Common.AsyncContinuation)">
            <summary>
            Flush any pending log messages for all wrapped targets.
            </summary>
            <param name="asyncContinuation">The asynchronous continuation.</param>
        </member>
        <member name="P:NLog.Targets.Wrappers.CompoundTargetBase.Targets">
            <summary>
            Gets the collection of targets managed by this compound target.
            </summary>
        </member>
        <member name="T:NLog.Targets.Wrappers.FallbackGroupTarget">
            <summary>
            Provides fallback-on-error.
            </summary>
            <seealso href="http://nlog-project.org/wiki/FallbackGroup_target">Documentation on NLog Wiki</seealso>
            <example>
            <p>This example causes the messages to be written to server1, 
            and if it fails, messages go to server2.</p>
            <p>
            To set up the target in the <a href="config.html">configuration file</a>, 
            use the following syntax:
            </p>
            <code lang="XML" source="examples/targets/Configuration File/FallbackGroup/NLog.config" />
            <p>
            The above examples assume just one target and a single rule. See below for
            a programmatic configuration that's equivalent to the above config file:
            </p>
            <code lang="C#" source="examples/targets/Configuration API/FallbackGroup/Simple/Example.cs" />
            </example>
        </member>
        <member name="M:NLog.Targets.Wrappers.FallbackGroupTarget.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.Wrappers.FallbackGroupTarget"/> class.
            </summary>
        </member>
        <member name="M:NLog.Targets.Wrappers.FallbackGroupTarget.#ctor(NLog.Targets.Target[])">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.Wrappers.FallbackGroupTarget"/> class.
            </summary>
            <param name="targets">The targets.</param>
        </member>
        <member name="M:NLog.Targets.Wrappers.FallbackGroupTarget.Write(NLog.Common.AsyncLogEventInfo)">
            <summary>
            Forwards the log event to the sub-targets until one of them succeeds.
            </summary>
            <param name="logEvent">The log event.</param>
            <remarks>
            The method remembers the last-known-successful target
            and starts the iteration from it.
            If <see cref="P:NLog.Targets.Wrappers.FallbackGroupTarget.ReturnToFirstOnSuccess"/> is set, the method
            resets the target to the first target
            stored in <see cref="N:NLog.Targets"/>.
            </remarks>
        </member>
        <member name="P:NLog.Targets.Wrappers.FallbackGroupTarget.ReturnToFirstOnSuccess">
            <summary>
            Gets or sets a value indicating whether to return to the first target after any successful write.
            </summary>
            <docgen category='Fallback Options' order='10' />
        </member>
        <member name="T:NLog.Targets.Wrappers.FilteringRule">
            <summary>
            Filtering rule for <see cref="T:NLog.Targets.Wrappers.PostFilteringTargetWrapper"/>.
            </summary>
        </member>
        <member name="M:NLog.Targets.Wrappers.FilteringRule.#ctor">
            <summary>
            Initializes a new instance of the FilteringRule class.
            </summary>
        </member>
        <member name="M:NLog.Targets.Wrappers.FilteringRule.#ctor(NLog.Conditions.ConditionExpression,NLog.Conditions.ConditionExpression)">
            <summary>
            Initializes a new instance of the FilteringRule class.
            </summary>
            <param name="whenExistsExpression">Condition to be tested against all events.</param>
            <param name="filterToApply">Filter to apply to all log events when the first condition matches any of them.</param>
        </member>
        <member name="P:NLog.Targets.Wrappers.FilteringRule.Exists">
            <summary>
            Gets or sets the condition to be tested.
            </summary>
            <docgen category='Filtering Options' order='10' />
        </member>
        <member name="P:NLog.Targets.Wrappers.FilteringRule.Filter">
            <summary>
            Gets or sets the resulting filter to be applied when the condition matches.
            </summary>
            <docgen category='Filtering Options' order='10' />
        </member>
        <member name="T:NLog.Targets.Wrappers.FilteringTargetWrapper">
            <summary>
            Filters log entries based on a condition.
            </summary>
            <seealso href="http://nlog-project.org/wiki/FilteringWrapper_target">Documentation on NLog Wiki</seealso>
            <example>
            <p>This example causes the messages not contains the string '1' to be ignored.</p>
            <p>
            To set up the target in the <a href="config.html">configuration file</a>, 
            use the following syntax:
            </p>
            <code lang="XML" source="examples/targets/Configuration File/FilteringWrapper/NLog.config" />
            <p>
            The above examples assume just one target and a single rule. See below for
            a programmatic configuration that's equivalent to the above config file:
            </p>
            <code lang="C#" source="examples/targets/Configuration API/FilteringWrapper/Simple/Example.cs" />
            </example>
        </member>
        <member name="M:NLog.Targets.Wrappers.FilteringTargetWrapper.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.Wrappers.FilteringTargetWrapper"/> class.
            </summary>
        </member>
        <member name="M:NLog.Targets.Wrappers.FilteringTargetWrapper.#ctor(NLog.Targets.Target,NLog.Conditions.ConditionExpression)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.Wrappers.FilteringTargetWrapper"/> class.
            </summary>
            <param name="wrappedTarget">The wrapped target.</param>
            <param name="condition">The condition.</param>
        </member>
        <member name="M:NLog.Targets.Wrappers.FilteringTargetWrapper.Write(NLog.Common.AsyncLogEventInfo)">
            <summary>
            Checks the condition against the passed log event.
            If the condition is met, the log event is forwarded to
            the wrapped target.
            </summary>
            <param name="logEvent">Log event.</param>
        </member>
        <member name="P:NLog.Targets.Wrappers.FilteringTargetWrapper.Condition">
            <summary>
            Gets or sets the condition expression. Log events who meet this condition will be forwarded 
            to the wrapped target.
            </summary>
            <docgen category='Filtering Options' order='10' />
        </member>
        <member name="T:NLog.Targets.Wrappers.PostFilteringTargetWrapper">
            <summary>
            Filters buffered log entries based on a set of conditions that are evaluated on a group of events.
            </summary>
            <seealso href="http://nlog-project.org/wiki/PostFilteringWrapper_target">Documentation on NLog Wiki</seealso>
            <remarks>
            PostFilteringWrapper must be used with some type of buffering target or wrapper, such as
            AsyncTargetWrapper, BufferingWrapper or ASPNetBufferingWrapper.
            </remarks>
            <example>
            <p>
            This example works like this. If there are no Warn,Error or Fatal messages in the buffer
            only Info messages are written to the file, but if there are any warnings or errors, 
            the output includes detailed trace (levels &gt;= Debug). You can plug in a different type
            of buffering wrapper (such as ASPNetBufferingWrapper) to achieve different
            functionality.
            </p>
            <p>
            To set up the target in the <a href="config.html">configuration file</a>, 
            use the following syntax:
            </p>
            <code lang="XML" source="examples/targets/Configuration File/PostFilteringWrapper/NLog.config" />
            <p>
            The above examples assume just one target and a single rule. See below for
            a programmatic configuration that's equivalent to the above config file:
            </p>
            <code lang="C#" source="examples/targets/Configuration API/PostFilteringWrapper/Simple/Example.cs" />
            </example>
        </member>
        <member name="M:NLog.Targets.Wrappers.PostFilteringTargetWrapper.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.Wrappers.PostFilteringTargetWrapper"/> class.
            </summary>
        </member>
        <member name="M:NLog.Targets.Wrappers.PostFilteringTargetWrapper.Write(NLog.Common.AsyncLogEventInfo[])">
            <summary>
            Evaluates all filtering rules to find the first one that matches.
            The matching rule determines the filtering condition to be applied
            to all items in a buffer. If no condition matches, default filter
            is applied to the array of log events.
            </summary>
            <param name="logEvents">Array of log events to be post-filtered.</param>
        </member>
        <member name="P:NLog.Targets.Wrappers.PostFilteringTargetWrapper.DefaultFilter">
            <summary>
            Gets or sets the default filter to be applied when no specific rule matches.
            </summary>
            <docgen category='Filtering Options' order='10' />
        </member>
        <member name="P:NLog.Targets.Wrappers.PostFilteringTargetWrapper.Rules">
            <summary>
            Gets the collection of filtering rules. The rules are processed top-down
            and the first rule that matches determines the filtering condition to
            be applied to log events.
            </summary>
            <docgen category='Filtering Rules' order='10' />
        </member>
        <member name="T:NLog.Targets.Wrappers.RandomizeGroupTarget">
            <summary>
            Sends log messages to a randomly selected target.
            </summary>
            <seealso href="http://nlog-project.org/wiki/RandomizeGroup_target">Documentation on NLog Wiki</seealso>
            <example>
            <p>This example causes the messages to be written to either file1.txt or file2.txt 
            chosen randomly on a per-message basis.
            </p>
            <p>
            To set up the target in the <a href="config.html">configuration file</a>, 
            use the following syntax:
            </p>
            <code lang="XML" source="examples/targets/Configuration File/RandomizeGroup/NLog.config" />
            <p>
            The above examples assume just one target and a single rule. See below for
            a programmatic configuration that's equivalent to the above config file:
            </p>
            <code lang="C#" source="examples/targets/Configuration API/RandomizeGroup/Simple/Example.cs" />
            </example>
        </member>
        <member name="M:NLog.Targets.Wrappers.RandomizeGroupTarget.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.Wrappers.RandomizeGroupTarget"/> class.
            </summary>
        </member>
        <member name="M:NLog.Targets.Wrappers.RandomizeGroupTarget.#ctor(NLog.Targets.Target[])">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.Wrappers.RandomizeGroupTarget"/> class.
            </summary>
            <param name="targets">The targets.</param>
        </member>
        <member name="M:NLog.Targets.Wrappers.RandomizeGroupTarget.Write(NLog.Common.AsyncLogEventInfo)">
            <summary>
            Forwards the log event to one of the sub-targets.
            The sub-target is randomly chosen.
            </summary>
            <param name="logEvent">The log event.</param>
        </member>
        <member name="T:NLog.Targets.Wrappers.RepeatingTargetWrapper">
            <summary>
            Repeats each log event the specified number of times.
            </summary>
            <seealso href="http://nlog-project.org/wiki/RepeatingWrapper_target">Documentation on NLog Wiki</seealso>
            <example>
            <p>This example causes each log message to be repeated 3 times.</p>
            <p>
            To set up the target in the <a href="config.html">configuration file</a>, 
            use the following syntax:
            </p>
            <code lang="XML" source="examples/targets/Configuration File/RepeatingWrapper/NLog.config" />
            <p>
            The above examples assume just one target and a single rule. See below for
            a programmatic configuration that's equivalent to the above config file:
            </p>
            <code lang="C#" source="examples/targets/Configuration API/RepeatingWrapper/Simple/Example.cs" />
            </example>
        </member>
        <member name="M:NLog.Targets.Wrappers.RepeatingTargetWrapper.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.Wrappers.RepeatingTargetWrapper"/> class.
            </summary>
        </member>
        <member name="M:NLog.Targets.Wrappers.RepeatingTargetWrapper.#ctor(NLog.Targets.Target,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.Wrappers.RepeatingTargetWrapper"/> class.
            </summary>
            <param name="wrappedTarget">The wrapped target.</param>
            <param name="repeatCount">The repeat count.</param>
        </member>
        <member name="M:NLog.Targets.Wrappers.RepeatingTargetWrapper.Write(NLog.Common.AsyncLogEventInfo)">
            <summary>
            Forwards the log message to the <see cref="P:NLog.Targets.Wrappers.WrapperTargetBase.WrappedTarget"/> by calling the <see cref="M:NLog.Targets.Target.Write(NLog.LogEventInfo)"/> method <see cref="P:NLog.Targets.Wrappers.RepeatingTargetWrapper.RepeatCount"/> times.
            </summary>
            <param name="logEvent">The log event.</param>
        </member>
        <member name="P:NLog.Targets.Wrappers.RepeatingTargetWrapper.RepeatCount">
            <summary>
            Gets or sets the number of times to repeat each log message.
            </summary>
            <docgen category='Repeating Options' order='10' />
        </member>
        <member name="T:NLog.Targets.Wrappers.RetryingTargetWrapper">
            <summary>
            Retries in case of write error.
            </summary>
            <seealso href="http://nlog-project.org/wiki/RetryingWrapper_target">Documentation on NLog Wiki</seealso>
            <example>
            <p>This example causes each write attempt to be repeated 3 times, 
            sleeping 1 second between attempts if first one fails.</p>
            <p>
            To set up the target in the <a href="config.html">configuration file</a>, 
            use the following syntax:
            </p>
            <code lang="XML" source="examples/targets/Configuration File/RetryingWrapper/NLog.config" />
            <p>
            The above examples assume just one target and a single rule. See below for
            a programmatic configuration that's equivalent to the above config file:
            </p>
            <code lang="C#" source="examples/targets/Configuration API/RetryingWrapper/Simple/Example.cs" />
            </example>
        </member>
        <member name="M:NLog.Targets.Wrappers.RetryingTargetWrapper.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.Wrappers.RetryingTargetWrapper"/> class.
            </summary>
        </member>
        <member name="M:NLog.Targets.Wrappers.RetryingTargetWrapper.#ctor(NLog.Targets.Target,System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.Wrappers.RetryingTargetWrapper"/> class.
            </summary>
            <param name="wrappedTarget">The wrapped target.</param>
            <param name="retryCount">The retry count.</param>
            <param name="retryDelayMilliseconds">The retry delay milliseconds.</param>
        </member>
        <member name="M:NLog.Targets.Wrappers.RetryingTargetWrapper.Write(NLog.Common.AsyncLogEventInfo)">
            <summary>
            Writes the specified log event to the wrapped target, retrying and pausing in case of an error.
            </summary>
            <param name="logEvent">The log event.</param>
        </member>
        <member name="P:NLog.Targets.Wrappers.RetryingTargetWrapper.RetryCount">
            <summary>
            Gets or sets the number of retries that should be attempted on the wrapped target in case of a failure.
            </summary>
            <docgen category='Retrying Options' order='10' />
        </member>
        <member name="P:NLog.Targets.Wrappers.RetryingTargetWrapper.RetryDelayMilliseconds">
            <summary>
            Gets or sets the time to wait between retries in milliseconds.
            </summary>
            <docgen category='Retrying Options' order='10' />
        </member>
        <member name="T:NLog.Targets.Wrappers.RoundRobinGroupTarget">
            <summary>
            Distributes log events to targets in a round-robin fashion.
            </summary>
            <seealso href="http://nlog-project.org/wiki/RoundRobinGroup_target">Documentation on NLog Wiki</seealso>
            <example>
            <p>This example causes the messages to be written to either file1.txt or file2.txt.
            Each odd message is written to file2.txt, each even message goes to file1.txt.
            </p>
            <p>
            To set up the target in the <a href="config.html">configuration file</a>, 
            use the following syntax:
            </p>
            <code lang="XML" source="examples/targets/Configuration File/RoundRobinGroup/NLog.config" />
            <p>
            The above examples assume just one target and a single rule. See below for
            a programmatic configuration that's equivalent to the above config file:
            </p>
            <code lang="C#" source="examples/targets/Configuration API/RoundRobinGroup/Simple/Example.cs" />
            </example>
        </member>
        <member name="M:NLog.Targets.Wrappers.RoundRobinGroupTarget.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.Wrappers.RoundRobinGroupTarget"/> class.
            </summary>
        </member>
        <member name="M:NLog.Targets.Wrappers.RoundRobinGroupTarget.#ctor(NLog.Targets.Target[])">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.Wrappers.RoundRobinGroupTarget"/> class.
            </summary>
            <param name="targets">The targets.</param>
        </member>
        <member name="M:NLog.Targets.Wrappers.RoundRobinGroupTarget.Write(NLog.Common.AsyncLogEventInfo)">
            <summary>
            Forwards the write to one of the targets from
            the <see cref="N:NLog.Targets"/> collection.
            </summary>
            <param name="logEvent">The log event.</param>
            <remarks>
            The writes are routed in a round-robin fashion.
            The first log event goes to the first target, the second
            one goes to the second target and so on looping to the
            first target when there are no more targets available.
            In general request N goes to Targets[N % Targets.Count].
            </remarks>
        </member>
        <member name="T:NLog.Targets.Wrappers.SplitGroupTarget">
            <summary>
            Writes log events to all targets.
            </summary>
            <seealso href="http://nlog-project.org/wiki/SplitGroup_target">Documentation on NLog Wiki</seealso>
            <example>
            <p>This example causes the messages to be written to both file1.txt or file2.txt 
            </p>
            <p>
            To set up the target in the <a href="config.html">configuration file</a>, 
            use the following syntax:
            </p>
            <code lang="XML" source="examples/targets/Configuration File/SplitGroup/NLog.config" />
            <p>
            The above examples assume just one target and a single rule. See below for
            a programmatic configuration that's equivalent to the above config file:
            </p>
            <code lang="C#" source="examples/targets/Configuration API/SplitGroup/Simple/Example.cs" />
            </example>
        </member>
        <member name="M:NLog.Targets.Wrappers.SplitGroupTarget.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.Wrappers.SplitGroupTarget"/> class.
            </summary>
        </member>
        <member name="M:NLog.Targets.Wrappers.SplitGroupTarget.#ctor(NLog.Targets.Target[])">
            <summary>
            Initializes a new instance of the <see cref="T:NLog.Targets.Wrappers.SplitGroupTarget"/> class.
            </summary>
            <param name="targets">The targets.</param>
        </member>
        <member name="M:NLog.Targets.Wrappers.SplitGroupTarget.Write(NLog.Common.AsyncLogEventInfo)">
            <summary>
            Forwards the specified log event to all sub-targets.
            </summary>
            <param name="logEvent">The log event.</param>
        </member>
        <member name="M:NLog.Targets.Wrappers.SplitGroupTarget.Write(NLog.Common.AsyncLogEventInfo[])">
            <summary>
            Writes an array of logging events to the log target. By default it iterates on all
            events and passes them to "Write" method. Inheriting classes can use this method to
            optimize batch writes.
            </summary>
            <param name="logEvents">Logging events to be written out.</param>
        </member>
    </members>
</doc>
