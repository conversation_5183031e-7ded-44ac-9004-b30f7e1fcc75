﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.IO.FileSystem</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Win32.SafeHandles.SafeFileHandle">
      <summary>Stellt eine Wrapperklasse für ein Dateihandle dar. </summary>
    </member>
    <member name="M:Microsoft.Win32.SafeHandles.SafeFileHandle.#ctor(System.IntPtr,System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:Microsoft.Win32.SafeHandles.SafeFileHandle" />-Klasse. </summary>
      <param name="preexistingHandle">Ein <see cref="T:System.IntPtr" />-Objekt, das das zu verwendende, bereits vorhandene Handle darstellt.</param>
      <param name="ownsHandle">true, um das Handle während der Finalisierungsphase zuverlässig freizugeben, und false, um eine zuverlässige Freigabe zu verhindern (nicht empfohlen).</param>
    </member>
    <member name="P:Microsoft.Win32.SafeHandles.SafeFileHandle.IsInvalid"></member>
    <member name="T:System.IO.Directory">
      <summary>Macht statische Methoden zum Erstellen, Verschieben und Auflisten in Verzeichnissen und Unterverzeichnissen verfügbar.Diese Klasse kann nicht vererbt werden.Um den .NET Framework-Quellcode für diesen Typ zu durchsuchen, rufen Sie die Verweisquelle auf.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Directory.CreateDirectory(System.String)">
      <summary>Erstellt alle Verzeichnisse und Unterverzeichnisse im angegebenen Pfad, es sei denn, sie sind bereits vorhanden.</summary>
      <returns>Ein Objekt, das das Verzeichnis im angegebenen Pfad darstellt.Dieses Objekt wird unabhängig davon zurückgegeben, ob ein Verzeichnis unter dem angegebenen Pfad bereits vorhanden ist.</returns>
      <param name="path">Das zu erstellende Verzeichnis. </param>
      <exception cref="T:System.IO.IOException">Das von <paramref name="path" /> angegebene Verzeichnis ist eine Datei.- oder - Der Netzwerkname ist nicht bekannt.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ein oder mehrere ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mithilfe der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode ausführen.- oder - <paramref name="path" /> ist ein Doppelpunkt (:) vorangestellt bzw. enthält nur einen Doppelpunkt.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> enthält ein Doppelpunktzeichen (:), das nicht Bestandteil einer Laufwerksbezeichnung ("C:\") ist.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.Delete(System.String)">
      <summary>Löscht ein leeres Verzeichnis aus einem angegebenen Pfad.</summary>
      <param name="path">Der Name des zu entfernenden leeren Verzeichnisses.Dieses Verzeichnis muss schreibbar und leer sein.</param>
      <exception cref="T:System.IO.IOException">Eine Datei mit dem gleichen Namen und Speicherort (beide mit <paramref name="path" /> angegeben) ist vorhanden.- oder - Das Verzeichnis ist das aktuelle Arbeitsverzeichnis der Anwendung.- oder - Das durch <paramref name="path" /> angegebene Verzeichnis ist nicht leer.- oder - Das Verzeichnis ist schreibgeschützt oder enthält eine schreibgeschützte Datei.- oder - Das Verzeichnis wird von einem anderen Prozess verwendet.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ein oder mehrere ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mithilfe der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode ausführen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> ist nicht vorhanden oder konnte nicht gefunden werden.- oder - Der angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.Delete(System.String,System.Boolean)">
      <summary>Löscht das angegebene Verzeichnis und, sofern angegeben, alle Unterverzeichnisse und Dateien im Verzeichnis. </summary>
      <param name="path">Der Name des zu entfernenden Verzeichnisses. </param>
      <param name="recursive">true zum Entfernen von Verzeichnissen, Unterverzeichnissen und Dateien in <paramref name="path" />, andernfalls false. </param>
      <exception cref="T:System.IO.IOException">Eine Datei mit dem gleichen Namen und Speicherort (beide mit <paramref name="path" /> angegeben) ist vorhanden.- oder - Das durch <paramref name="path" /> angegebene Verzeichnis ist schreibgeschützt oder <paramref name="recursive" /> ist false und <paramref name="path" /> ist kein leeres Verzeichnis. - oder - Das Verzeichnis ist das aktuelle Arbeitsverzeichnis der Anwendung. - oder - Das Verzeichnis enthält eine schreibgeschützte Datei.- oder - Das Verzeichnis wird von einem anderen Prozess verwendet.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ein oder mehrere ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mithilfe der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode ausführen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> ist nicht vorhanden oder konnte nicht gefunden werden.- oder - Der angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String)">
      <summary>Gibt eine aufzählbare Auflistung von Verzeichnisnamen in einem angegebenen Pfad zurück.</summary>
      <returns>Eine aufzählbare Auflistung der vollständigen Namen (einschließlich Pfade) für die Verzeichnisse im Verzeichnis, das von <paramref name="path" /> angegeben wird.</returns>
      <param name="path">Der relative oder absolute Pfad zum Verzeichnis, das durchsucht werden soll.Bei dieser Zeichenfolge wird die Groß-/Kleinschreibung nicht berücksichtigt.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mithilfe der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode ausführen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> ist ungültig. Dies ist z. B. der Fall, wenn auf ein nicht zugeordnetes Laufwerk verwiesen wird. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> ist ein Dateiname.</exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad, Dateiname oder die Kombination aus diesen überschreitet die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String,System.String)">
      <summary>Gibt eine aufzählbare Auflistung von Verzeichnisnamen zurück, die einem Suchmuster in einem angegebenen Pfad entsprechen.</summary>
      <returns>Eine aufzählbare Auflistung der vollständigen Namen (einschließlich Pfade) für die Verzeichnisse im Verzeichnis, das von <paramref name="path" /> angegebenen wird und der angegebene Suchmuster entsprechen.</returns>
      <param name="path">Der relative oder absolute Pfad zum Verzeichnis, das durchsucht werden soll.Bei dieser Zeichenfolge wird die Groß-/Kleinschreibung nicht berücksichtigt.</param>
      <param name="searchPattern">Die Suchzeichenfolge für die Überprüfung der Namen von Verzeichnissen in <paramref name="path" /> auf Übereinstimmungen.Dieser Parameter kann eine Kombination aus gültigen Literalpfad- und Platzhalterzeichen (* und ?, siehe Hinweise) enthalten, unterstützt jedoch keine regulären Ausdrücke.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mit der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode durchführen.- oder - <paramref name="searchPattern" /> enthält kein gültiges Muster.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null.- oder - <paramref name="searchPattern" /> ist null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> ist ungültig. Dies ist z. B. der Fall, wenn auf ein nicht zugeordnetes Laufwerk verwiesen wird. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> ist ein Dateiname.</exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad, Dateiname oder die Kombination aus diesen überschreitet die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String,System.String,System.IO.SearchOption)">
      <summary>Gibt eine aufzählbare Auflistung von Verzeichnisnamen zurück, die einem Suchmuster in einem angegebenen Pfad entsprechen. Optional werden Unterverzeichnisse durchsucht.</summary>
      <returns>Eine aufzählbare Auflistung der vollständigen Namen (einschließlich Pfade) für die Verzeichnisse im Verzeichnis, die von <paramref name="path" /> angegebenen werden und dem angegebenen Suchmuster und der angegebenen Option entsprechen.</returns>
      <param name="path">Der relative oder absolute Pfad zum Verzeichnis, das durchsucht werden soll.Bei dieser Zeichenfolge wird die Groß-/Kleinschreibung nicht berücksichtigt.</param>
      <param name="searchPattern">Die Suchzeichenfolge für die Überprüfung der Namen von Verzeichnissen in <paramref name="path" /> auf Übereinstimmungen.Dieser Parameter kann eine Kombination aus gültigen Literalpfad- und Platzhalterzeichen (* und ?, siehe Hinweise) enthalten, unterstützt jedoch keine regulären Ausdrücke.</param>
      <param name="searchOption">Einer der Enumerationswerte, der angibt, ob nur das aktuelle Verzeichnis oder auch alle Unterverzeichnisse durchsucht werden sollen.Der Standardwert ist <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mithilfe der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode ausführen.- oder - <paramref name="searchPattern" /> enthält kein gültiges Muster.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null.- oder - <paramref name="searchPattern" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> ist kein gültiger <see cref="T:System.IO.SearchOption" />-Wert.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> ist ungültig. Dies ist z. B. der Fall, wenn auf ein nicht zugeordnetes Laufwerk verwiesen wird. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> ist ein Dateiname.</exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad, Dateiname oder die Kombination aus diesen überschreitet die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String)">
      <summary>Gibt eine aufzählbare Auflistung von Dateinamen in einem angegebenen Pfad zurück.</summary>
      <returns>Eine aufzählbare Auflistung der vollständigen Namen (einschließlich Pfade) für die Verzeichnisse im Verzeichnis, das von <paramref name="path" /> angegeben wird.</returns>
      <param name="path">Der relative oder absolute Pfad zum Verzeichnis, das durchsucht werden soll.Bei dieser Zeichenfolge wird die Groß-/Kleinschreibung nicht berücksichtigt.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mithilfe der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode ausführen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> ist ungültig. Dies ist z. B. der Fall, wenn auf ein nicht zugeordnetes Laufwerk verwiesen wird. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> ist ein Dateiname.</exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad, Dateiname oder die Kombination aus diesen überschreitet die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String,System.String)">
      <summary>Gibt eine aufzählbare Auflistung von Dateinamen zurück, die einem Suchmuster in einem angegebenen Pfad entsprechen.</summary>
      <returns>Eine aufzählbare Auflistung der vollständigen Namen (einschließlich Pfade) für die Dateien im Verzeichnis, das von <paramref name="path" /> angegebenen wird und dem angegebenen Suchmuster entsprechen.</returns>
      <param name="path">Der relative oder absolute Pfad zum Verzeichnis, das durchsucht werden soll.Bei dieser Zeichenfolge wird die Groß-/Kleinschreibung nicht berücksichtigt.</param>
      <param name="searchPattern">Die Suchzeichenfolge für die Überprüfung der Namen von Dateien in <paramref name="path" /> auf Übereinstimmungen.Dieser Parameter kann eine Kombination aus gültigen Literalpfad- und Platzhalterzeichen (* und ?, siehe Hinweise) enthalten, unterstützt jedoch keine regulären Ausdrücke.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mithilfe der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode ausführen.- oder - <paramref name="searchPattern" /> enthält kein gültiges Muster.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null.- oder - <paramref name="searchPattern" /> ist null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> ist ungültig. Dies ist z. B. der Fall, wenn auf ein nicht zugeordnetes Laufwerk verwiesen wird. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> ist ein Dateiname.</exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad, Dateiname oder die Kombination aus diesen überschreitet die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String,System.String,System.IO.SearchOption)">
      <summary>Gibt eine aufzählbare Auflistung von Dateinamen zurück, die einem Suchmuster in einem angegebenen Pfad entsprechen. Optional werden Unterverzeichnisse durchsucht.</summary>
      <returns>Eine aufzählbare Auflistung der vollständigen Namen (einschließlich Pfade) für die Dateien im Verzeichnis, die von <paramref name="path" /> angegebenen werden und dem angegebenen Suchmuster und der angegebenen Option entsprechen.</returns>
      <param name="path">Der relative oder absolute Pfad zum Verzeichnis, das durchsucht werden soll.Bei dieser Zeichenfolge wird die Groß-/Kleinschreibung nicht berücksichtigt.</param>
      <param name="searchPattern">Die Suchzeichenfolge für die Überprüfung der Namen von Dateien in <paramref name="path" /> auf Übereinstimmungen.Dieser Parameter kann eine Kombination aus gültigen Literalpfad- und Platzhalterzeichen (* und ?, siehe Hinweise) enthalten, unterstützt jedoch keine regulären Ausdrücke.</param>
      <param name="searchOption">Einer der Enumerationswerte, der angibt, ob nur das aktuelle Verzeichnis oder auch alle Unterverzeichnisse durchsucht werden sollen.Der Standardwert ist <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mithilfe der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode ausführen.- oder - <paramref name="searchPattern" /> enthält kein gültiges Muster.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null.- oder - <paramref name="searchPattern" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> ist kein gültiger <see cref="T:System.IO.SearchOption" />-Wert.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> ist ungültig. Dies ist z. B. der Fall, wenn auf ein nicht zugeordnetes Laufwerk verwiesen wird. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> ist ein Dateiname.</exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad, Dateiname oder die Kombination aus diesen überschreitet die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String)">
      <summary>Gibt eine aufzählbare Auflistung von Dateinamen und Verzeichnisnamen in einem angegebenen Pfad zurück. </summary>
      <returns>Eine aufzählbare Auflistung von Dateisystemeinträgen im von <paramref name="path" /> angegebenen Verzeichnis.</returns>
      <param name="path">Der relative oder absolute Pfad zum Verzeichnis, das durchsucht werden soll.Bei dieser Zeichenfolge wird die Groß-/Kleinschreibung nicht berücksichtigt.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mithilfe der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode ausführen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> ist ungültig. Dies ist z. B. der Fall, wenn auf ein nicht zugeordnetes Laufwerk verwiesen wird. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> ist ein Dateiname.</exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad, Dateiname oder die Kombination aus diesen überschreitet die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String,System.String)">
      <summary>Gibt eine aufzählbare Auflistung von Dateinamen und Verzeichnisnamen zurück, die einem Suchmuster in einem angegebenen Pfad entsprechen.</summary>
      <returns>Eine aufzählbare Auflistung von Dateisystemeinträgen, die sich in dem von <paramref name="path" /> angegebenen Verzeichnis befinden und dem angegebenen Suchmuster entsprechen.</returns>
      <param name="path">Der relative oder absolute Pfad zum Verzeichnis, das durchsucht werden soll.Bei dieser Zeichenfolge wird die Groß-/Kleinschreibung nicht berücksichtigt.</param>
      <param name="searchPattern">Die Suchzeichenfolge für die Überprüfung der Namen von Dateisystemeinträgen in <paramref name="path" /> auf Übereinstimmungen.Dieser Parameter kann eine Kombination aus gültigen Literalpfad- und Platzhalterzeichen (* und ?, siehe Hinweise) enthalten, unterstützt jedoch keine regulären Ausdrücke.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mithilfe der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode ausführen.- oder - <paramref name="searchPattern" /> enthält kein gültiges Muster.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null.- oder - <paramref name="searchPattern" /> ist null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> ist ungültig. Dies ist z. B. der Fall, wenn auf ein nicht zugeordnetes Laufwerk verwiesen wird. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> ist ein Dateiname.</exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad, Dateiname oder die Kombination aus diesen überschreitet die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String,System.String,System.IO.SearchOption)">
      <summary>Gibt eine aufzählbare Auflistung von Dateinamen und Verzeichnisnamen zurück, die einem Suchmuster in einem angegebenen Pfad entsprechen. Optional werden Unterverzeichnisse durchsucht.</summary>
      <returns>Eine aufzählbare Auflistung von Dateisystemeinträgen, die sich in dem von <paramref name="path" /> angegebenen Verzeichnis befinden und dem angegebenen Suchmuster und der angegebenen Option entsprechen.</returns>
      <param name="path">Der relative oder absolute Pfad zum Verzeichnis, das durchsucht werden soll.Bei dieser Zeichenfolge wird die Groß-/Kleinschreibung nicht berücksichtigt.</param>
      <param name="searchPattern">Die Suchzeichenfolge für die Überprüfung von Dateisystemeinträgen in <paramref name="path" /> auf Übereinstimmungen.Dieser Parameter kann eine Kombination aus gültigen Literalpfad- und Platzhalterzeichen (* und ?, siehe Hinweise) enthalten, unterstützt jedoch keine regulären Ausdrücke.</param>
      <param name="searchOption">Einer der Enumerationswerte, der angibt, ob nur das aktuelle Verzeichnis oder auch alle Unterverzeichnisse durchsucht werden sollen.Der Standardwert ist <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mithilfe der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode ausführen.- oder - <paramref name="searchPattern" /> enthält kein gültiges Muster.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null.- oder - <paramref name="searchPattern" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> ist kein gültiger <see cref="T:System.IO.SearchOption" />-Wert.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> ist ungültig. Dies ist z. B. der Fall, wenn auf ein nicht zugeordnetes Laufwerk verwiesen wird. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> ist ein Dateiname.</exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad, Dateiname oder die Kombination aus diesen überschreitet die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
    </member>
    <member name="M:System.IO.Directory.Exists(System.String)">
      <summary>Bestimmt, ob der angegebene Pfad auf ein vorhandenes Verzeichnis auf einem Datenträger verweist.</summary>
      <returns>true, wenn sich <paramref name="path" /> auf ein vorhandenes Verzeichnis bezieht; false, wenn ein Verzeichnis nicht vorhanden ist, oder wenn beim Versuch festzustellen, ob eine angegebene Datei vorhanden ist, ein Fehler auftritt.</returns>
      <param name="path">Der zu testende Pfad. </param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetCreationTime(System.String)">
      <summary>Ruft Datum und Zeit der Erstellung eines Verzeichnisses ab.</summary>
      <returns>Eine Struktur, die auf das Erstellungsdatum und den Erstellungszeitpunkt für das angegebene Verzeichnis festgelegt wird.Dieser Wert wird in Ortszeit angegeben.</returns>
      <param name="path">Der Pfad des Verzeichnisses. </param>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ein oder mehrere ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mithilfe der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode ausführen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetCreationTimeUtc(System.String)">
      <summary>Ruft das Erstellungsdatum und den Erstellungszeitpunkt im UTC-Format (Coordinated Universal Time, koordinierte Weltzeit) eines Verzeichnisses ab.</summary>
      <returns>Eine Struktur, die auf das Erstellungsdatum und den Erstellungszeitpunkt für das angegebene Verzeichnis festgelegt wird.Der Wert wird in UTC-Zeit angegeben.</returns>
      <param name="path">Der Pfad des Verzeichnisses. </param>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ein oder mehrere ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mithilfe der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode ausführen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetCurrentDirectory">
      <summary>Ruft das aktuelle Arbeitsverzeichnis der Anwendung ab.</summary>
      <returns>Eine Zeichenfolge, die den Pfad des aktuellen Arbeitsverzeichnisses enthält und nicht mit einem umgekehrten Schrägstrich (\) endet.</returns>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.NotSupportedException">Das Betriebssystem ist Windows CE, das keine Funktion für ein aktuelles Verzeichnis aufweist.Diese Methode ist in .NET Compact Framework verfügbar, wird gegenwärtig jedoch nicht unterstützt.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String)">
      <summary>Gibt die Namen der Unterverzeichnisse (einschließlich der Pfade) im angegebenen Verzeichnis zurück.</summary>
      <returns>Ein Array der vollständigen Namen (einschließlich der Pfade) der Unterverzeichnisse im angegebenen Pfad, oder ein leeres Array, wenn keine Verzeichnisse gefunden werden.</returns>
      <param name="path">Der relative oder absolute Pfad zum Verzeichnis, das durchsucht werden soll.Bei dieser Zeichenfolge wird die Groß-/Kleinschreibung nicht berücksichtigt.</param>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ein oder mehrere ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mithilfe der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode ausführen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> ist ein Dateiname. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String,System.String)">
      <summary>Gibt die Namen von Unterverzeichnissen (einschließlich der zugehörigen Pfade) zurück, die dem angegebenen Suchmuster im angegebenen Verzeichnis entsprechen.</summary>
      <returns>Ein Array der vollständigen Namen (einschließlich der Pfade) der Unterverzeichnisse, die dem Suchmuster im angegebenen Verzeichnis entsprechen, oder ein leeres Array, wenn keine Verzeichnisse gefunden werden.</returns>
      <param name="path">Der relative oder absolute Pfad zum Verzeichnis, das durchsucht werden soll.Bei dieser Zeichenfolge wird die Groß-/Kleinschreibung nicht berücksichtigt.</param>
      <param name="searchPattern">Die Suchzeichenfolge für die Überprüfung der Namen von Unterverzeichnissen in <paramref name="path" /> auf Übereinstimmungen.Dieser Parameter kann eine Kombination aus gültigen Literal- und Platzhalterzeichen (siehe Hinweise) enthalten, unterstützt jedoch keine regulären Ausdrücke.</param>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ein oder mehrere ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mit <see cref="M:System.IO.Path.GetInvalidPathChars" /> durchführen.- oder -  <paramref name="searchPattern" /> enthält kein gültiges Muster. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> oder <paramref name="searchPattern" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> ist ein Dateiname. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String,System.String,System.IO.SearchOption)">
      <summary>Gibt die Namen der Unterverzeichnisse (einschließlich der Pfade) zurück, die dem angegebenen Suchmuster im aktuellen Verzeichnis entsprechen. Optional werden Unterverzeichnisse durchsucht.</summary>
      <returns>Ein Array der vollständigen Namen (einschließlich der Pfade) der Unterverzeichnisse, die den angegebenen Kriterien entsprechen, oder ein leeres Array, wenn keine Verzeichnisse gefunden werden.</returns>
      <param name="path">Der relative oder absolute Pfad zum Verzeichnis, das durchsucht werden soll.Bei dieser Zeichenfolge wird die Groß-/Kleinschreibung nicht berücksichtigt.</param>
      <param name="searchPattern">Die Suchzeichenfolge für die Überprüfung der Namen von Unterverzeichnissen in <paramref name="path" /> auf Übereinstimmungen.Dieser Parameter kann eine Kombination aus gültigen Literal- und Platzhalterzeichen (siehe Hinweise) enthalten, unterstützt jedoch keine regulären Ausdrücke.</param>
      <param name="searchOption">Einer der Enumerationswerte, der angibt, ob nur das aktuelle Verzeichnis oder auch alle Unterverzeichnisse durchsucht werden sollen. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ein oder mehrere ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mithilfe der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode ausführen.- oder -  <paramref name="searchPattern" /> enthält kein gültiges Muster. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> oder <paramref name="searchPattern" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> ist kein gültiger <see cref="T:System.IO.SearchOption" />-Wert.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> ist ein Dateiname. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
    </member>
    <member name="M:System.IO.Directory.GetDirectoryRoot(System.String)">
      <summary>Gibt für den angegebenen Pfad die Informationen über Volume, Stammverzeichnis oder beides zurück.</summary>
      <returns>Eine Zeichenfolge, die für den angegebenen Pfad die Informationen über Volume, Stammverzeichnis oder beides enthält.</returns>
      <param name="path">Der Pfad einer Datei oder eines Verzeichnisses. </param>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ein oder mehrere ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mit <see cref="M:System.IO.Path.GetInvalidPathChars" /> durchführen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String)">
      <summary>Gibt die Namen aller Dateien (einschließlich des Pfads) im angegebenen Verzeichnis zurück.</summary>
      <returns>Ein Array der vollständigen Namen (einschließlich der Pfade) für die Dateien im angegebenen Verzeichnis, oder ein leeres Array, wenn keine Dateien gefunden werden.</returns>
      <param name="path">Der relative oder absolute Pfad zum Verzeichnis, das durchsucht werden soll.Bei dieser Zeichenfolge wird die Groß-/Kleinschreibung nicht berücksichtigt.</param>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> ist ein Dateiname.- oder - Ein Netzwerkfehler ist aufgetreten. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ein oder mehrere ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mithilfe der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode ausführen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad wird nicht gefunden oder ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String,System.String)">
      <summary>Gibt die Namen der Dateien (einschließlich der zugehörigen Pfade) zurück, die dem angegebenen Suchmuster im angegebenen Verzeichnis entsprechen.</summary>
      <returns>Ein Array der vollständigen Namen der Dateien im angegebenen Verzeichnis (einschließlich der zugehörigen Pfade), die dem angegebenen Suchmuster entsprechen, oder ein leeres Array, wenn keine Dateien gefunden werden.</returns>
      <param name="path">Der relative oder absolute Pfad zum Verzeichnis, das durchsucht werden soll.Bei dieser Zeichenfolge wird die Groß-/Kleinschreibung nicht berücksichtigt.</param>
      <param name="searchPattern">Die Suchzeichenfolge für die Überprüfung der Namen von Dateien in <paramref name="path" /> auf Übereinstimmungen.Dieser Parameter kann eine Kombination aus gültigen Literalpfad- und Platzhalterzeichen (* und ?, siehe Hinweise) enthalten, unterstützt jedoch keine regulären Ausdrücke.</param>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> ist ein Dateiname.- oder - Ein Netzwerkfehler ist aufgetreten. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ein oder mehrere ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mit <see cref="M:System.IO.Path.GetInvalidPathChars" /> durchführen.- oder -  <paramref name="searchPattern" /> enthält kein gültiges Muster. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> oder <paramref name="searchPattern" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad wird nicht gefunden oder ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String,System.String,System.IO.SearchOption)">
      <summary>Gibt die Namen der Dateien (einschließlich der zugehörigen Pfade) zurück, die dem angegebenen Suchmuster im angegebenen Verzeichnis entsprechen. Anhand eines Werts wird bestimmt, ob Unterverzeichnisse durchsucht werden sollen.</summary>
      <returns>Ein Array der vollständigen Namen der Dateien im angegebenen Verzeichnis (einschließlich der Pfade), die dem angegebenen Suchmuster und der Option entsprechen, oder ein leeres Array, wenn keine Dateien gefunden werden.</returns>
      <param name="path">Der relative oder absolute Pfad zum Verzeichnis, das durchsucht werden soll.Bei dieser Zeichenfolge wird die Groß-/Kleinschreibung nicht berücksichtigt.</param>
      <param name="searchPattern">Die Suchzeichenfolge für die Überprüfung der Namen von Dateien in <paramref name="path" /> auf Übereinstimmungen.Dieser Parameter kann eine Kombination aus gültigen Literalpfad- und Platzhalterzeichen (* und ?, siehe Hinweise) enthalten, unterstützt jedoch keine regulären Ausdrücke.</param>
      <param name="searchOption">Einer der Enumerationswerte, der angibt, ob nur das aktuelle Verzeichnis oder auch alle Unterverzeichnisse durchsucht werden sollen. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ein oder mehrere ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mit der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode durchführen.- oder -  <paramref name="searchPattern" /> enthält kein gültiges Muster.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> oder <paramref name="searchpattern" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> ist kein gültiger <see cref="T:System.IO.SearchOption" />-Wert.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad wird nicht gefunden oder ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> ist ein Dateiname.- oder - Ein Netzwerkfehler ist aufgetreten. </exception>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String)">
      <summary>Gibt die Namen aller Dateien und Unterverzeichnisse in einem angegebenen Pfad zurück.</summary>
      <returns>Ein Array mit den Namen der Dateien und Unterverzeichnisse im angegebenen Verzeichnis, oder ein leeres Array, wenn keine Dateien oder Unterverzeichnisse gefunden werden.</returns>
      <param name="path">Der relative oder absolute Pfad zum Verzeichnis, das durchsucht werden soll.Bei dieser Zeichenfolge wird die Groß-/Kleinschreibung nicht berücksichtigt.</param>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ein oder mehrere ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mit <see cref="M:System.IO.Path.GetInvalidPathChars" /> durchführen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> ist ein Dateiname. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String,System.String)">
      <summary>Gibt ein Array von Dateinamen und Verzeichnisnamen zurück, die einem Suchmuster in einem angegebenen Pfad entsprechen.</summary>
      <returns>Ein Array von Dateinamen und Verzeichnisnamen, die den angegebenen Suchkriterien entsprechen, oder ein leeres Array, wenn keine Dateien oder Verzeichnisse gefunden werden.</returns>
      <param name="path">Der relative oder absolute Pfad zum Verzeichnis, das durchsucht werden soll.Bei dieser Zeichenfolge wird die Groß-/Kleinschreibung nicht berücksichtigt.</param>
      <param name="searchPattern">Die Suchzeichenfolge für die Überprüfung der Namen von Dateien und Verzeichnissen in <paramref name="path" /> auf Übereinstimmungen.Dieser Parameter kann eine Kombination aus gültigen Literalpfad- und Platzhalterzeichen (* und ?, siehe Hinweise) enthalten, unterstützt jedoch keine regulären Ausdrücke.</param>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ein oder mehrere ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mit der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode durchführen.- oder -  <paramref name="searchPattern" /> enthält kein gültiges Muster. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> oder <paramref name="searchPattern" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> ist ein Dateiname. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String,System.String,System.IO.SearchOption)">
      <summary>Gibt ein Array aller Dateinamen und Verzeichnisnamen zurück, die einem Suchmuster in einem angegebenen Pfad entsprechen. Optional werden Unterverzeichnisse durchsucht.</summary>
      <returns>Ein Array von Dateinamen und Verzeichnisnamen, die den angegebenen Suchkriterien entsprechen, oder ein leeres Array, wenn keine Dateien oder Verzeichnisse gefunden werden.</returns>
      <param name="path">Der relative oder absolute Pfad zum Verzeichnis, das durchsucht werden soll.Bei dieser Zeichenfolge wird die Groß-/Kleinschreibung nicht berücksichtigt.</param>
      <param name="searchPattern">Die Suchzeichenfolge für die Überprüfung der Namen von Dateien und Verzeichnissen in <paramref name="path" /> auf Übereinstimmungen.Dieser Parameter kann eine Kombination aus gültigen Literalpfad- und Platzhalterzeichen (* und ?, siehe Hinweise) enthalten, unterstützt jedoch keine regulären Ausdrücke.</param>
      <param name="searchOption">Einer der Enumerationswerte, der angibt, ob nur das aktuelle Verzeichnis oder auch alle Unterverzeichnisse durchsucht werden sollen.Der Standardwert ist <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mithilfe der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode ausführen.- oder - <paramref name="searchPattern" /> enthält kein gültiges Muster.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null.- oder - <paramref name="searchPattern" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> ist kein gültiger <see cref="T:System.IO.SearchOption" />-Wert.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> ist ungültig. Dies ist z. B. der Fall, wenn auf ein nicht zugeordnetes Laufwerk verwiesen wird. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> ist ein Dateiname.</exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad, Dateiname oder die Kombination aus diesen überschreitet die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
    </member>
    <member name="M:System.IO.Directory.GetLastAccessTime(System.String)">
      <summary>Gibt das Datum und die Uhrzeit des letzten Zugriffs auf die angegebene Datei bzw. das angegebene Verzeichnis zurück.</summary>
      <returns>Eine Struktur, die auf das Datum und die Uhrzeit des letzten Zugriffs auf die angegebene Datei bzw. das angegebene Verzeichnis festgelegt wird.Dieser Wert wird in Ortszeit angegeben.</returns>
      <param name="path">Die Datei oder das Verzeichnis, für die bzw. das die Informationen über Zugriffsdatum und -zeitpunkt abgerufen werden sollen. </param>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ein oder mehrere ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mit der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode durchführen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.NotSupportedException">Der <paramref name="path" />-Parameter hat ein ungültiges Format. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetLastAccessTimeUtc(System.String)">
      <summary>Gibt das Datum und den Zeitpunkt im UTC-Format (Coordinated Universal Time, koordinierte Weltzeit) des letzten Zugriffs auf die angegebene Datei bzw. das angegebene Verzeichnis zurück.</summary>
      <returns>Eine Struktur, die auf das Datum und die Uhrzeit des letzten Zugriffs auf die angegebene Datei bzw. das angegebene Verzeichnis festgelegt wird.Der Wert wird in UTC-Zeit angegeben.</returns>
      <param name="path">Die Datei oder das Verzeichnis, für die bzw. das die Informationen über Zugriffsdatum und -zeitpunkt abgerufen werden sollen. </param>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ein oder mehrere ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mit der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode durchführen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.NotSupportedException">Der <paramref name="path" />-Parameter hat ein ungültiges Format. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetLastWriteTime(System.String)">
      <summary>Gibt das Datum und die Uhrzeit des letzten Schreibvorgangs in die angegebene Datei bzw. das angegebene Verzeichnis zurück.</summary>
      <returns>Eine Struktur, die auf das Datum und die Uhrzeit des letzten Schreibvorgangs in die angegebene Datei bzw. das angegebene Verzeichnis festgelegt wird.Dieser Wert wird in Ortszeit angegeben.</returns>
      <param name="path">Die Datei oder das Verzeichnis, für die bzw. für das die Informationen über Datum und Uhrzeit der letzten Änderung abgerufen werden sollen. </param>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ein oder mehrere ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mit der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode durchführen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetLastWriteTimeUtc(System.String)">
      <summary>Gibt das Datum und den Zeitpunkt des letzten Schreibzugriffs im UTC-Format (Coordinated Universal Time, koordinierte Weltzeit) auf die angegebenen Datei bzw. das angegebene Verzeichnis zurück.</summary>
      <returns>Eine Struktur, die auf das Datum und die Uhrzeit des letzten Schreibvorgangs in die angegebene Datei bzw. das angegebene Verzeichnis festgelegt wird.Der Wert wird in UTC-Zeit angegeben.</returns>
      <param name="path">Die Datei oder das Verzeichnis, für die bzw. für das die Informationen über Datum und Uhrzeit der letzten Änderung abgerufen werden sollen. </param>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ein oder mehrere ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mit der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode durchführen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetParent(System.String)">
      <summary>Ruft das übergeordnete Verzeichnis des angegebenen Pfads ab, sowohl für absolute als auch für relative Pfade.</summary>
      <returns>	Das übergeordnete Verzeichnis oder null, wenn <paramref name="path" />das Stammverzeichnis ist, einschließlich des Stammverzeichnisses eines UNC-Server- oder Freigabenamens.</returns>
      <param name="path">Der Pfad, dessen übergeordnetes Verzeichnis abgerufen werden soll. </param>
      <exception cref="T:System.IO.IOException">Das durch <paramref name="path" /> angegebene Verzeichnis ist schreibgeschützt. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ein oder mehrere ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mit der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode durchführen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad wurde nicht gefunden. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.Move(System.String,System.String)">
      <summary>Verschiebt eine Datei oder ein Verzeichnis und dessen Inhalt an einen neuen Speicherort.</summary>
      <param name="sourceDirName">Der Pfad der Datei oder des Verzeichnisses, das verschoben werden soll. </param>
      <param name="destDirName">Der Pfad zum neuen Speicherort für <paramref name="sourceDirName" />.Wenn <paramref name="sourceDirName" /> eine Datei ist, muss <paramref name="destDirName" /> auch ein Dateiname sein.</param>
      <exception cref="T:System.IO.IOException">Es wurde versucht, ein Verzeichnis auf ein anderes Volume zu verschieben. - oder -  <paramref name="destDirName" /> ist bereits vorhanden. - oder -  Der <paramref name="sourceDirName" />-Parameter und der <paramref name="destDirName" />-Parameter verweisen auf dieselbe Datei oder dasselbe Verzeichnis. - oder - Das Verzeichnis oder eine Datei in ihr wird von einem anderen Prozess verwendet.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceDirName" /> oder <paramref name="destDirName" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ein oder mehrere ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mit der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode durchführen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceDirName" /> oder <paramref name="destDirName" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der durch <paramref name="sourceDirName" /> angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetCreationTime(System.String,System.DateTime)">
      <summary>Legt das Erstellungsdatum und den Erstellungszeitpunkt für die angegebene Datei bzw. das angegebene Verzeichnis fest.</summary>
      <param name="path">Die Datei oder das Verzeichnis, für das die Informationen über Erstellungsdatum und -zeitpunkt festgelegt werden sollen. </param>
      <param name="creationTime">Datum und Zeitpunkt des letzten Schreibvorgangs in einer Datei oder einem Verzeichnis.Dieser Wert wird in Ortszeit angegeben.</param>
      <exception cref="T:System.IO.FileNotFoundException">Der angegebene Pfad wurde nicht gefunden. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ein oder mehrere ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mit der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode durchführen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> gibt einen Wert an, der außerhalb des für diesen Vorgang zulässigen Bereichs für Datum oder Zeit liegt. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Das aktuelle Betriebssystem ist nicht Windows NT oder höher.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetCreationTimeUtc(System.String,System.DateTime)">
      <summary>Legt das Erstellungsdatum und den Erstellungszeitpunkt im UTC-Format (Coordinated Universal Time, koordinierte Weltzeit) für die angegebene Datei oder das angegebene Verzeichnis fest.</summary>
      <param name="path">Die Datei oder das Verzeichnis, für das die Informationen über Erstellungsdatum und -zeitpunkt festgelegt werden sollen. </param>
      <param name="creationTimeUtc">Datum und Uhrzeit der Erstellung des Verzeichnisses oder der Datei.Dieser Wert wird in Ortszeit angegeben.</param>
      <exception cref="T:System.IO.FileNotFoundException">Der angegebene Pfad wurde nicht gefunden. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ein oder mehrere ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mit der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode durchführen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> gibt einen Wert an, der außerhalb des für diesen Vorgang zulässigen Bereichs für Datum oder Zeit liegt. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Das aktuelle Betriebssystem ist nicht Windows NT oder höher.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetCurrentDirectory(System.String)">
      <summary>Legt das aktuelle Arbeitsverzeichnis der Anwendung auf das angegebene Verzeichnis fest.</summary>
      <param name="path">Der Pfad, auf den das aktuelle Arbeitsverzeichnis festgelegt ist. </param>
      <exception cref="T:System.IO.IOException">Ein E/A-Fehler ist aufgetreten. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ein oder mehrere ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mit der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode durchführen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung für den Zugriff auf nicht verwalteten Code. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Der angegebene Pfad wurde nicht gefunden. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Das angegebene Verzeichnis wurde nicht gefunden.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastAccessTime(System.String,System.DateTime)">
      <summary>Legt das Datum und die Uhrzeit des letzten Zugriffs auf die angegebene Datei bzw. das angegebene Verzeichnis fest.</summary>
      <param name="path">Die Datei oder das Verzeichnis, für die bzw. für das die Informationen über Zugriffsdatum und -zeitpunkt festgelegt werden sollen. </param>
      <param name="lastAccessTime">Eine Objekt, das den festzulegenden Wert für Zugriffsdatum und -zeitpunkt von <paramref name="path" /> enthält.Dieser Wert wird in Ortszeit angegeben.</param>
      <exception cref="T:System.IO.FileNotFoundException">Der angegebene Pfad wurde nicht gefunden. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ein oder mehrere ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mit der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode durchführen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Das aktuelle Betriebssystem ist nicht Windows NT oder höher.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTime" /> gibt einen Wert an, der außerhalb des für diesen Vorgang zulässigen Bereichs für Datum oder Zeit liegt.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastAccessTimeUtc(System.String,System.DateTime)">
      <summary>Legt das Datum und den Zeitpunkt im UTC-Format (Coordinated Universal Time, koordinierte Weltzeit) des letzten Zugriffs auf die angegebene Datei bzw. das angegebene Verzeichnis fest.</summary>
      <param name="path">Die Datei oder das Verzeichnis, für die bzw. für das die Informationen über Zugriffsdatum und -zeitpunkt festgelegt werden sollen. </param>
      <param name="lastAccessTimeUtc">Eine Objekt, das den festzulegenden Wert für Zugriffsdatum und -zeitpunkt von <paramref name="path" /> enthält.Der Wert wird in UTC-Zeit angegeben.</param>
      <exception cref="T:System.IO.FileNotFoundException">Der angegebene Pfad wurde nicht gefunden. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ein oder mehrere ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mit der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode durchführen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Das aktuelle Betriebssystem ist nicht Windows NT oder höher.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTimeUtc" /> gibt einen Wert an, der außerhalb des für diesen Vorgang zulässigen Bereichs für Datum oder Zeit liegt.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastWriteTime(System.String,System.DateTime)">
      <summary>Legt Datum und Zeit des letzten Schreibvorgangs in einem Verzeichnis fest.</summary>
      <param name="path">Der Pfad des Verzeichnisses. </param>
      <param name="lastWriteTime">Datum und Zeitpunkt des letzten Schreibvorgangs in einem Verzeichnis.Dieser Wert wird in Ortszeit angegeben.</param>
      <exception cref="T:System.IO.FileNotFoundException">Der angegebene Pfad wurde nicht gefunden. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ein oder mehrere ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mit der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode durchführen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Das aktuelle Betriebssystem ist nicht Windows NT oder höher.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTime" /> gibt einen Wert an, der außerhalb des für diesen Vorgang zulässigen Bereichs für Datum oder Zeit liegt.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastWriteTimeUtc(System.String,System.DateTime)">
      <summary>Legt das Datum und den Zeitpunkt im UTC-Format (Coordinated Universal Time, koordinierte Weltzeit) des letzten Schreibzugriffs auf ein Verzeichnis fest.</summary>
      <param name="path">Der Pfad des Verzeichnisses. </param>
      <param name="lastWriteTimeUtc">Datum und Zeitpunkt des letzten Schreibvorgangs in einem Verzeichnis.Der Wert wird in UTC-Zeit angegeben.</param>
      <exception cref="T:System.IO.FileNotFoundException">Der angegebene Pfad wurde nicht gefunden. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ein oder mehrere ungültige Zeichen.Sie können Abfragen für ungültige Zeichen mit der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode durchführen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Das aktuelle Betriebssystem ist nicht Windows NT oder höher.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTimeUtc" /> gibt einen Wert an, der außerhalb des für diesen Vorgang zulässigen Bereichs für Datum oder Zeit liegt.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.IO.DirectoryInfo">
      <summary>Macht Instanzmethoden zum Erstellen, Verschieben und Auflisten in Verzeichnissen und Unterverzeichnissen verfügbar.Diese Klasse kann nicht vererbt werden.Um den .NET Framework-Quellcode für diesen Typ zu durchsuchen, finden Sie in der
                                Referenzquelle.
                            </summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.DirectoryInfo.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der
                                <see cref="T:System.IO.DirectoryInfo" />eine Klasse für den angegebenen Pfad.
                            </summary>
      <param name="path">Eine Zeichenfolge, die den Pfad für das Erstellen der
                                    DirectoryInfo.
                                </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />ist
                                        null.
                                    </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> enthält ungültige Zeichen wie z. B ", &lt;, &gt; oder |
                                    </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.Der angegebene Pfad und/oder Dateiname ist zu lang.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.Create">
      <summary>Erstellt ein Verzeichnis.</summary>
      <exception cref="T:System.IO.IOException">Das Verzeichnis kann nicht erstellt werden.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.CreateSubdirectory(System.String)">
      <summary>Erstellt ein oder mehrere Unterverzeichnisse im angegebenen Pfad.Der angegebene Pfad kann relativ zu dieser Instanz von der
                            <see cref="T:System.IO.DirectoryInfo" />Klasse.
                        </summary>
      <returns>Im letzten im angegebenen Verzeichnis
                                <paramref name="path" />.
                            </returns>
      <param name="path">Der angegebene Pfad.Dieser kann nicht ein anderer Datenträgervolumename oder ein anderer UNC-Name (Universal Naming Convention) sein.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />Gibt keinen gültigen Dateipfad oder enthält ungültige
                                        DirectoryInfoZeichen.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />ist
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig. Dies ist z. B. der Fall, wenn das Laufwerk des Pfads nicht zugeordnet ist.</exception>
      <exception cref="T:System.IO.IOException">Das Unterverzeichnis kann nicht erstellt werden.- oder - Eine Datei oder ein Verzeichnis hat bereits angegebene Namen
                                        <paramref name="path" />.
                                    </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.Der angegebene Pfad und/oder Dateiname ist zu lang.</exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer hat keine Codezugriffsberechtigung zum Erstellen des Verzeichnisses.- oder - Der Aufrufer hat keinen Codezugriffsberechtigung zum Lesen des Verzeichnisses durch die zurückgegebene beschrieben
                                    <see cref="T:System.IO.DirectoryInfo" />-Objekt
                                Dies kann auftreten, wenn die
                                    <paramref name="path" />-Parameter beschreibt ein vorhandenes Verzeichnis.
                                </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> enthält ein Doppelpunktzeichen (:), das nicht Bestandteil einer Laufwerksbezeichnung ("C:\") ist.
                                    </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.Delete">
      <summary>Löscht das
                                <see cref="T:System.IO.DirectoryInfo" />Wenn sie leer ist.
                            </summary>
      <exception cref="T:System.UnauthorizedAccessException">Das Verzeichnis enthält eine schreibgeschützte Datei.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Das Verzeichnis, das von diesem beschrieben
                                        <see cref="T:System.IO.DirectoryInfo" />Objekt ist nicht vorhanden oder konnte nicht gefunden werden.
                                    </exception>
      <exception cref="T:System.IO.IOException">Das Verzeichnis ist nicht leer.- oder - Das Verzeichnis ist das aktuelle Arbeitsverzeichnis der Anwendung.- oder - Für das Verzeichnis ist ein geöffnetes Handle vorhanden, und das Betriebssystem ist Windows XP oder früher.Dieses geöffnete Handle kann aus der Auflistung von Verzeichnissen entstanden sein.Weitere Informationen finden Sie unter
                                    Gewusst wie: Auflisten von Verzeichnissen und Dateien.
                                </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.Delete(System.Boolean)">
      <summary>Löscht diese Instanz von einem
                                <see cref="T:System.IO.DirectoryInfo" />, gibt an, ob Unterverzeichnisse und Dateien gelöscht.
                            </summary>
      <param name="recursive">trueUm dieses Verzeichnis, seine Unterverzeichnisse und alle Dateien zu löschen. andernfalls
                                    false.
                                </param>
      <exception cref="T:System.UnauthorizedAccessException">Das Verzeichnis enthält eine schreibgeschützte Datei.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Das Verzeichnis, das von diesem beschrieben
                                        <see cref="T:System.IO.DirectoryInfo" />Objekt ist nicht vorhanden oder konnte nicht gefunden werden.
                                    </exception>
      <exception cref="T:System.IO.IOException">Das Verzeichnis ist schreibgeschützt.- oder - Das Verzeichnis enthält eine oder mehrere Dateien oder Unterverzeichnisse und
                                        <paramref name="recursive" />ist
                                        false.
                                    - oder - Das Verzeichnis ist das aktuelle Arbeitsverzeichnis der Anwendung.- oder - Für das Verzeichnis oder eine der Dateien darin ist ein geöffnetes Handle vorhanden, und das Betriebssystem ist Windows XP oder früher.Dieses geöffnete Handle kann aus der Auflistung von Verzeichnissen und Dateien entstanden sein.Weitere Informationen finden Sie unter
                                    Gewusst wie: Auflisten von Verzeichnissen und Dateien.
                                </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories">
      <summary>Gibt eine aufzählbare Auflistung von Verzeichnisinformationen im aktuellen Verzeichnis zurück.</summary>
      <returns>Eine aufzählbare Auflistung der Verzeichnisse im aktuellen Verzeichnis.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">Gekapselte Pfad der
                                        <see cref="T:System.IO.DirectoryInfo" />das Objekt ist ungültig (z. B. die befindet sich auf einem nicht zugeordneten Laufwerk).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories(System.String)">
      <summary>Gibt eine aufzählbare Auflistung von Verzeichnisinformationen zurück, die einem angegebenen Suchmuster entsprechen.</summary>
      <returns>Eine aufzählbare Auflistung von Verzeichnissen, die entspricht
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">Die Suchzeichenfolge für die Überprüfung der Namen von Verzeichnissen auf Übereinstimmungen.Dieser Parameter kann eine Kombination aus gültigen Literalpfad- und Platzhalterzeichen (* und ?, siehe Hinweise) enthalten, unterstützt jedoch keine regulären Ausdrücke.Das Standardmuster ist "*", wobei alle Dateien zurückgegeben werden.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />ist
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Gekapselte Pfad der
                                        <see cref="T:System.IO.DirectoryInfo" />das Objekt ist ungültig (z. B. die befindet sich auf einem nicht zugeordneten Laufwerk).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories(System.String,System.IO.SearchOption)">
      <summary>Gibt eine aufzählbare Auflistung von Verzeichnisinformationen zurück, die einem angegebenen Suchmuster und einer angegebenen Option zum Durchsuchen von Unterverzeichnissen entspricht.</summary>
      <returns>Eine aufzählbare Auflistung von Verzeichnissen, die entspricht
                                <paramref name="searchPattern" /> und
                                <paramref name="searchOption" />.
                            </returns>
      <param name="searchPattern">Die Suchzeichenfolge für die Überprüfung der Namen von Verzeichnissen auf Übereinstimmungen.Dieser Parameter kann eine Kombination aus gültigen Literalpfad- und Platzhalterzeichen (* und ?, siehe Hinweise) enthalten, unterstützt jedoch keine regulären Ausdrücke.Das Standardmuster ist "*", wobei alle Dateien zurückgegeben werden.</param>
      <param name="searchOption">Einer der Enumerationswerte, der angibt, ob nur das aktuelle Verzeichnis oder auch alle Unterverzeichnisse durchsucht werden sollen.Der Standardwert ist
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />ist
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />ist kein gültiger
                                        <see cref="T:System.IO.SearchOption" />-Wert.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Gekapselte Pfad der
                                        <see cref="T:System.IO.DirectoryInfo" />das Objekt ist ungültig (z. B. die befindet sich auf einem nicht zugeordneten Laufwerk).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles">
      <summary>Gibt eine aufzählbare Auflistung von Dateiinformationen im aktuellen Verzeichnis zurück.</summary>
      <returns>Eine aufzählbare Auflistung der Dateien im aktuellen Verzeichnis.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">Gekapselte Pfad der
                                        <see cref="T:System.IO.DirectoryInfo" />das Objekt ist ungültig (z. B. die befindet sich auf einem nicht zugeordneten Laufwerk).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles(System.String)">
      <summary>Gibt eine aufzählbare Auflistung von Dateiinformationen zurück, die einem Suchmuster entsprechen.</summary>
      <returns>Eine aufzählbare Auflistung von Dateien, die entspricht
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">Die Suchzeichenfolge für die Überprüfung der Namen von Dateien auf Übereinstimmungen.Dieser Parameter kann eine Kombination aus gültigen Literalpfad- und Platzhalterzeichen (* und ?, siehe Hinweise) enthalten, unterstützt jedoch keine regulären Ausdrücke.Das Standardmuster ist "*", wobei alle Dateien zurückgegeben werden.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />ist
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Gekapselte Pfad der
                                        <see cref="T:System.IO.DirectoryInfo" />das Objekt ist ungültig, (z. B. auf einem nicht zugeordneten Laufwerk).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles(System.String,System.IO.SearchOption)">
      <summary>Gibt eine aufzählbare Auflistung von Dateiinformationen zurück, die einem angegebenen Suchmuster und einer angegebenen Option zum Durchsuchen von Unterverzeichnissen entspricht.</summary>
      <returns>Eine aufzählbare Auflistung von Dateien, die entspricht
                                <paramref name="searchPattern" /> und
                                <paramref name="searchOption" />.
                            </returns>
      <param name="searchPattern">Die Suchzeichenfolge für die Überprüfung der Namen von Dateien auf Übereinstimmungen.Dieser Parameter kann eine Kombination aus gültigen Literalpfad- und Platzhalterzeichen (* und ?, siehe Hinweise) enthalten, unterstützt jedoch keine regulären Ausdrücke.Das Standardmuster ist "*", wobei alle Dateien zurückgegeben werden.</param>
      <param name="searchOption">Einer der Enumerationswerte, der angibt, ob nur das aktuelle Verzeichnis oder auch alle Unterverzeichnisse durchsucht werden sollen.Der Standardwert ist
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />ist
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />ist kein gültiger
                                        <see cref="T:System.IO.SearchOption" />-Wert.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Gekapselte Pfad der
                                        <see cref="T:System.IO.DirectoryInfo" />das Objekt ist ungültig (z. B. die befindet sich auf einem nicht zugeordneten Laufwerk).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos">
      <summary>Gibt eine aufzählbare Auflistung von Systeminformationen im aktuellen Verzeichnis zurück.</summary>
      <returns>Eine aufzählbare Auflistung von Systeminformationen im aktuellen Verzeichnis.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">Gekapselte Pfad der
                                        <see cref="T:System.IO.DirectoryInfo" />das Objekt ist ungültig (z. B. die befindet sich auf einem nicht zugeordneten Laufwerk).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos(System.String)">
      <summary>Gibt eine aufzählbare Auflistung von Dateisysteminformationen zurück, die einem angegebenen Suchmuster entsprechen.</summary>
      <returns>Eine aufzählbare Auflistung von Informationsobjekten, die entspricht
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">Die Suchzeichenfolge für die Überprüfung der Namen von Verzeichnissen auf Übereinstimmungen.Dieser Parameter kann eine Kombination aus gültigen Literalpfad- und Platzhalterzeichen (* und ?, siehe Hinweise) enthalten, unterstützt jedoch keine regulären Ausdrücke.Das Standardmuster ist "*", wobei alle Dateien zurückgegeben werden.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />ist
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Gekapselte Pfad der
                                        <see cref="T:System.IO.DirectoryInfo" />das Objekt ist ungültig (z. B. die befindet sich auf einem nicht zugeordneten Laufwerk).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos(System.String,System.IO.SearchOption)">
      <summary>Gibt eine aufzählbare Auflistung von Dateisysteminformationen zurück, die einem angegebenen Suchmuster und einer angegebenen Option zum Durchsuchen von Unterverzeichnissen entspricht.</summary>
      <returns>Eine aufzählbare Auflistung von Informationsobjekten, die entspricht
                                <paramref name="searchPattern" /> und
                                <paramref name="searchOption" />.
                            </returns>
      <param name="searchPattern">Die Suchzeichenfolge für die Überprüfung der Namen von Verzeichnissen auf Übereinstimmungen.Dieser Parameter kann eine Kombination aus gültigen Literalpfad- und Platzhalterzeichen (* und ?, siehe Hinweise) enthalten, unterstützt jedoch keine regulären Ausdrücke.Das Standardmuster ist "*", wobei alle Dateien zurückgegeben werden.</param>
      <param name="searchOption">Einer der Enumerationswerte, der angibt, ob nur das aktuelle Verzeichnis oder auch alle Unterverzeichnisse durchsucht werden sollen.Der Standardwert ist
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />ist
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />ist kein gültiger
                                        <see cref="T:System.IO.SearchOption" />-Wert.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Gekapselte Pfad der
                                        <see cref="T:System.IO.DirectoryInfo" />das Objekt ist ungültig (z. B. die befindet sich auf einem nicht zugeordneten Laufwerk).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
    </member>
    <member name="P:System.IO.DirectoryInfo.Exists">
      <summary>Ruft einen Wert ab, der angibt, ob das Verzeichnis vorhanden ist.</summary>
      <returns>trueWenn das Verzeichnis vorhanden ist. andernfalls
                                false.
                            </returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories">
      <summary>Gibt die Unterverzeichnisse des aktuellen Verzeichnisses zurück.</summary>
      <returns>Ein Array von
                                <see cref="T:System.IO.DirectoryInfo" />-Objekte
                            </returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">Gekapselte Pfad der
                                        <see cref="T:System.IO.DirectoryInfo" />das Objekt ist ungültig, z. B. auf einem nicht zugeordneten Laufwerk.
                                    </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories(System.String)">
      <summary>Gibt ein Array von Verzeichnissen im aktuellen
                                <see cref="T:System.IO.DirectoryInfo" />die angegebenen Suchkriterien entsprechen.
                            </summary>
      <returns>Ein Array des Typs
                                DirectoryInfoAbgleich
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">Die Suchzeichenfolge für die Überprüfung der Namen von Verzeichnissen auf Übereinstimmungen.Dieser Parameter kann eine Kombination aus gültigen Literalpfad- und Platzhalterzeichen (* und ?, siehe Hinweise) enthalten, unterstützt jedoch keine regulären Ausdrücke.Das Standardmuster ist "*", wobei alle Dateien zurückgegeben werden.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />enthält mindestens ein ungültiges Zeichen definiert, indem Sie die
                                        <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />ist
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Gekapselte Pfad der
                                        DirectoryInfodas Objekt ist ungültig (z. B. die befindet sich auf einem nicht zugeordneten Laufwerk).
                                    </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories(System.String,System.IO.SearchOption)">
      <summary>Gibt ein Array von Verzeichnissen im aktuellen
                                <see cref="T:System.IO.DirectoryInfo" />die angegebenen Suchkriterien entsprechen, und verwenden einen Wert zu bestimmen, ob Unterverzeichnisse durchsucht.
                            </summary>
      <returns>Ein Array des Typs
                                DirectoryInfoAbgleich
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">Die Suchzeichenfolge für die Überprüfung der Namen von Verzeichnissen auf Übereinstimmungen.Dieser Parameter kann eine Kombination aus gültigen Literalpfad- und Platzhalterzeichen (* und ?, siehe Hinweise) enthalten, unterstützt jedoch keine regulären Ausdrücke.Das Standardmuster ist "*", wobei alle Dateien zurückgegeben werden.</param>
      <param name="searchOption">Einer der Enumerationswerte, der angibt, ob nur das aktuelle Verzeichnis oder auch alle Unterverzeichnisse durchsucht werden sollen.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />enthält mindestens ein ungültiges Zeichen definiert, indem Sie die
                                        <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />ist
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />ist kein gültiger
                                        <see cref="T:System.IO.SearchOption" />-Wert.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Gekapselte Pfad der
                                        DirectoryInfodas Objekt ist ungültig (z. B. die befindet sich auf einem nicht zugeordneten Laufwerk).
                                    </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles">
      <summary>Gibt eine Dateiliste des aktuellen Verzeichnisses zurück.</summary>
      <returns>Ein Array des Typs
                                <see cref="T:System.IO.FileInfo" />.
                            </returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der Pfad ist ungültig. Dies ist z. B. der Fall, wenn das Laufwerk des Pfads nicht zugeordnet ist.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles(System.String)">
      <summary>Gibt eine Dateiliste des aktuellen Verzeichnisses zurück, das dem angegebenen Suchmuster entspricht.</summary>
      <returns>Ein Array des Typs
                                <see cref="T:System.IO.FileInfo" />.
                            </returns>
      <param name="searchPattern">Die Suchzeichenfolge für die Überprüfung der Namen von Dateien auf Übereinstimmungen.Dieser Parameter kann eine Kombination aus gültigen Literalpfad- und Platzhalterzeichen (* und ?, siehe Hinweise) enthalten, unterstützt jedoch keine regulären Ausdrücke.Das Standardmuster ist "*", wobei alle Dateien zurückgegeben werden.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />enthält mindestens ein ungültiges Zeichen definiert, indem Sie die
                                        <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />ist
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk).</exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles(System.String,System.IO.SearchOption)">
      <summary>Gibt eine Dateiliste des aktuellen Verzeichnisses zurück, das dem angegebenen Suchmuster entspricht. Anhand eines Werts wird bestimmt, ob in Unterverzeichnissen gesucht wird.</summary>
      <returns>Ein Array des Typs
                                <see cref="T:System.IO.FileInfo" />.
                            </returns>
      <param name="searchPattern">Die Suchzeichenfolge für die Überprüfung der Namen von Dateien auf Übereinstimmungen.Dieser Parameter kann eine Kombination aus gültigen Literalpfad- und Platzhalterzeichen (* und ?, siehe Hinweise) enthalten, unterstützt jedoch keine regulären Ausdrücke.Das Standardmuster ist "*", wobei alle Dateien zurückgegeben werden.</param>
      <param name="searchOption">Einer der Enumerationswerte, der angibt, ob nur das aktuelle Verzeichnis oder auch alle Unterverzeichnisse durchsucht werden sollen.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />enthält mindestens ein ungültiges Zeichen definiert, indem Sie die
                                        <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />ist
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />ist kein gültiger
                                        <see cref="T:System.IO.SearchOption" />-Wert.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk).</exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos">
      <summary>Gibt ein Array von stark typisierten
                                <see cref="T:System.IO.FileSystemInfo" />Einträge, die alle Dateien und Unterverzeichnisse in einem Verzeichnis darstellt.
                            </summary>
      <returns>Ein Array von stark typisierten
                                <see cref="T:System.IO.FileSystemInfo" />Einträge.
                            </returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk).</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos(System.String)">
      <summary>Ruft ein Array von stark typisierten
                                <see cref="T:System.IO.FileSystemInfo" />Objekte darstellt, die Dateien und Unterverzeichnisse, die die angegebenen Suchkriterien entsprechen.
                            </summary>
      <returns>Ein Array von stark typisierten
                                FileSystemInfoObjekte, die den Suchkriterien entsprechen.
                            </returns>
      <param name="searchPattern">Die Suchzeichenfolge für die Überprüfung der Namen von Verzeichnissen und Dateien auf Übereinstimmungen.Dieser Parameter kann eine Kombination aus gültigen Literalpfad- und Platzhalterzeichen (* und ?, siehe Hinweise) enthalten, unterstützt jedoch keine regulären Ausdrücke.Das Standardmuster ist "*", wobei alle Dateien zurückgegeben werden.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />enthält mindestens ein ungültiges Zeichen definiert, indem Sie die
                                        <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />ist
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk).</exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos(System.String,System.IO.SearchOption)">
      <summary>Ruft ein Array von
                                <see cref="T:System.IO.FileSystemInfo" />Objekte, die Dateien und Unterverzeichnisse, die mit den angegebenen Suchkriterien darstellen.
                            </summary>
      <returns>Ein Array von Dateisystemeinträgen, die den Suchkriterien entsprechen.</returns>
      <param name="searchPattern">Die Suchzeichenfolge für die Überprüfung der Namen von Verzeichnissen und Dateien auf Übereinstimmungen.Dieser Parameter kann eine Kombination aus gültigen Literalpfad- und Platzhalterzeichen (* und ?, siehe Hinweise) enthalten, unterstützt jedoch keine regulären Ausdrücke.Das Standardmuster ist "*", wobei alle Dateien zurückgegeben werden.</param>
      <param name="searchOption">Einer der Enumerationswerte, der angibt, ob nur das aktuelle Verzeichnis oder auch alle Unterverzeichnisse durchsucht werden sollen.Der Standardwert ist
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />enthält mindestens ein ungültiges Zeichen definiert, indem Sie die
                                        <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />ist
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />ist kein gültiger
                                        <see cref="T:System.IO.SearchOption" />-Wert.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk).</exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.MoveTo(System.String)">
      <summary>Verschiebt ein
                                <see cref="T:System.IO.DirectoryInfo" />-Instanz und deren Inhalt in einen neuen Pfad.
                            </summary>
      <param name="destDirName">Der Name und Pfad des Verzeichnisses, in das das Verzeichnis verschoben werden soll.Das Ziel darf kein anderes Datenträgervolume und kein Verzeichnis mit dem gleichen Namen sein.Es kann ein vorhandenes Verzeichnis sein, dem dieses Verzeichnis als Unterverzeichnis hinzugefügt werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destDirName" />ist
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="destDirName" /> ist eine leere Zeichenfolge (''").
                                    </exception>
      <exception cref="T:System.IO.IOException">Es wurde versucht, ein Verzeichnis auf ein anderes Volume zu verschieben.- oder - <paramref name="destDirName" /> ist bereits vorhanden.
                                    - oder - Sie sind nicht autorisiert, auf diesen Pfad zuzugreifen.- oder - Das verschobene Verzeichnis und das Zielverzeichnis haben denselben Namen.</exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Das Zielverzeichnis kann nicht gefunden werden.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.DirectoryInfo.Name">
      <summary>Ruft den Namen dieses
                                <see cref="T:System.IO.DirectoryInfo" />Instanz.
                            </summary>
      <returns>Der Name des Verzeichnisses.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.DirectoryInfo.Parent">
      <summary>Ruft das übergeordnete Verzeichnis eines angegebenen Unterverzeichnisses ab.</summary>
      <returns>Das übergeordnete Verzeichnis, oder
                                nullWenn der Pfad null ist oder der Dateipfad ein Stammverzeichnis angibt (z. B. "\", "C:" oder * "\\server\share").
                            </returns>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.DirectoryInfo.Root">
      <summary>Ruft den Stammteil des Verzeichnisses ab.</summary>
      <returns>Ein Objekt, das den Stamm des Verzeichnisses darstellt.</returns>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.ToString">
      <summary>Gibt den ursprünglichen Pfad zurück, der vom Benutzer übergeben wurde.</summary>
      <returns>Gibt den ursprünglichen Pfad zurück, der vom Benutzer übergeben wurde.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.IO.File">
      <summary>Stellt statische Methoden zum Erstellen, Kopieren, Löschen, Verschieben und Öffnen einer Datei bereit und unterstützt das Erstellen von <see cref="T:System.IO.FileStream" />-Objekten.Um den .NET Framework-Quellcode für diesen Typ zu durchsuchen, finden Sie unter der Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.File.AppendAllLines(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>Fügt Zeilen an eine Datei an und schließt dann die Datei.Wenn die angegebene Datei nicht vorhanden ist, erstellt diese Methode eine Datei, schreibt die angegebenen Zeilen in die Datei und schließt die Datei dann.</summary>
      <param name="path">Die Datei, an die Zeilen angefügt werden sollen.Wenn die Datei nicht bereits vorhanden ist, wird sie erstellt.</param>
      <param name="contents">Die Zeilen, die an die Datei angefügt werden sollen.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält mindestens eines der von der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode definierten ungültigen Zeichen.</exception>
      <exception cref="T:System.ArgumentNullException">Entweder<paramref name=" path " />oder <paramref name="contents" /> ist null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" />ist ungültig (z. B. ist das Verzeichnis nicht vorhanden oder es befindet sich auf einem nicht zugeordneten Laufwerk).</exception>
      <exception cref="T:System.IO.FileNotFoundException">Die von <paramref name="path" /> angegebene Datei wurde nicht gefunden.</exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler beim Öffnen der Datei.</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> überschreitet die im System definierte maximale Länge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig.</exception>
      <exception cref="T:System.Security.SecurityException">Der Anrufer verfügt nicht über eine Berechtigung zum Schreiben der Datei.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> gibt eine schreibgeschützte Datei an.- oder - Dieser Vorgang wird von der aktuellen Plattform nicht unterstützt.- oder - "<paramref name="path" />" ist ein Verzeichnis.</exception>
    </member>
    <member name="M:System.IO.File.AppendAllLines(System.String,System.Collections.Generic.IEnumerable{System.String},System.Text.Encoding)">
      <summary>Fügt Zeilen unter Verwendung einer angegebenen Codierung an eine Datei an und schließt dann die Datei.Wenn die angegebene Datei nicht vorhanden ist, erstellt diese Methode eine Datei, schreibt die angegebenen Zeilen in die Datei und schließt die Datei dann.</summary>
      <param name="path">Die Datei, an die Zeilen angefügt werden sollen.Wenn die Datei nicht bereits vorhanden ist, wird sie erstellt.</param>
      <param name="contents">Die Zeilen, die an die Datei angefügt werden sollen.</param>
      <param name="encoding">Die zu verwendende Zeichencodierung.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält mindestens eines der von der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode definierten ungültigen Zeichen.</exception>
      <exception cref="T:System.ArgumentNullException">Entweder<paramref name=" path" />, <paramref name="contents" /> oder <paramref name="encoding" /> ist null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" />ist ungültig (z. B. ist das Verzeichnis nicht vorhanden oder es befindet sich auf einem nicht zugeordneten Laufwerk).</exception>
      <exception cref="T:System.IO.FileNotFoundException">Die von <paramref name="path" /> angegebene Datei wurde nicht gefunden.</exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler beim Öffnen der Datei.</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> überschreitet die im System definierte maximale Länge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig.</exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> gibt eine schreibgeschützte Datei an.- oder - Dieser Vorgang wird von der aktuellen Plattform nicht unterstützt.- oder - "<paramref name="path" />" ist ein Verzeichnis.- oder - Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
    </member>
    <member name="M:System.IO.File.AppendAllText(System.String,System.String)">
      <summary>Öffnet eine Datei, fügt die angegebene Zeichenfolge an die Datei an und schließt dann die Datei.Wenn die Datei nicht vorhanden ist, erstellt diese Methode eine Datei, schreibt die angegebene Zeichenfolge in die Datei und schließt die Datei dann.</summary>
      <param name="path">Die Datei, an die die angegebene Zeichenfolge angefügt werden soll. </param>
      <param name="contents">Die Zeichenfolge, die an die Datei angefügt werden soll. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfads ist ungültig (z. B. ist das Verzeichnis nicht vorhanden oder es befindet sich auf einem nicht zugeordneten Laufwerk). </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler beim Öffnen der Datei. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> gibt eine schreibgeschützte Datei an.- oder -  Dieser Vorgang wird von der aktuellen Plattform nicht unterstützt.- oder -  <paramref name="path" /> gibt ein Verzeichnis an.- oder -  Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.AppendAllText(System.String,System.String,System.Text.Encoding)">
      <summary>Fügt die angegebene Zeichenfolge an die Datei an und erstellt die Datei, wenn sie nicht bereits vorhanden ist.</summary>
      <param name="path">Die Datei, an die die angegebene Zeichenfolge angefügt werden soll. </param>
      <param name="contents">Die Zeichenfolge, die an die Datei angefügt werden soll. </param>
      <param name="encoding">Die zu verwendende Zeichencodierung. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfads ist ungültig (z. B. ist das Verzeichnis nicht vorhanden oder es befindet sich auf einem nicht zugeordneten Laufwerk). </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler beim Öffnen der Datei. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> gibt eine schreibgeschützte Datei an.- oder -  Dieser Vorgang wird von der aktuellen Plattform nicht unterstützt.- oder -  <paramref name="path" /> gibt ein Verzeichnis an.- oder -  Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.AppendText(System.String)">
      <summary>Erstellt einen <see cref="T:System.IO.StreamWriter" />, der UTF-8 codierten Text an eine vorhandene Datei anfügt oder an eine neue Datei, wenn die angegebene Datei nicht vorhanden ist.</summary>
      <returns>Ein Streamwriter, der UTF-8 codierten Text an die angegebene Datei oder eine neue Datei anfügt.</returns>
      <param name="path">Der Pfad zu der Datei, an die angefügt wird. </param>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfads ist ungültig (z. B. ist das Verzeichnis nicht vorhanden oder es befindet sich auf einem nicht zugeordneten Laufwerk). </exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Copy(System.String,System.String)">
      <summary>Kopiert eine vorhandene Datei in eine neue Datei.Das Überschreiben einer gleichnamigen Datei ist nicht zulässig.</summary>
      <param name="sourceFileName">Die zu kopierende Datei. </param>
      <param name="destFileName">Der Name der Zieldatei.Dies darf kein Verzeichnis und keine vorhandene Datei sein.</param>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> oder <paramref name="destFileName" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ein oder mehr durch <see cref="F:System.IO.Path.InvalidPathChars" /> definierte ungültige Zeichen.- oder -  <paramref name="sourceFileName" /> oder <paramref name="destFileName" /> gibt ein Verzeichnis an. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> oder <paramref name="destFileName" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der in <paramref name="sourceFileName" /> oder <paramref name="destFileName" /> angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="sourceFileName" /> wurde nicht gefunden. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destFileName" /> ist vorhanden.- oder -  Ein E/A-Fehler ist aufgetreten. </exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="sourceFileName" /> oder <paramref name="destFileName" /> ist ungültig. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Copy(System.String,System.String,System.Boolean)">
      <summary>Kopiert eine vorhandene Datei in eine neue Datei.Das Überschreiben einer gleichnamigen Datei ist zulässig.</summary>
      <param name="sourceFileName">Die zu kopierende Datei. </param>
      <param name="destFileName">Der Name der Zieldatei.Dabei darf es sich nicht um ein Verzeichnis handeln.</param>
      <param name="overwrite">true, wenn die Zieldatei überschrieben werden kann, andernfalls false. </param>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. - oder - <paramref name="destFileName" /> ist schreibgeschützt.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> oder <paramref name="destFileName" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält ein oder mehr durch <see cref="F:System.IO.Path.InvalidPathChars" /> definierte ungültige Zeichen.- oder -  <paramref name="sourceFileName" /> oder <paramref name="destFileName" /> gibt ein Verzeichnis an. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> oder <paramref name="destFileName" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der in <paramref name="sourceFileName" /> oder <paramref name="destFileName" /> angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="sourceFileName" /> wurde nicht gefunden. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destFileName" /> ist vorhanden, und <paramref name="overwrite" /> lautet false.- oder -  Ein E/A-Fehler ist aufgetreten. </exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="sourceFileName" /> oder <paramref name="destFileName" /> ist ungültig. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Create(System.String)">
      <summary>Erstellt oder überschreibt eine Datei im angegebenen Pfad.</summary>
      <returns>Ein <see cref="T:System.IO.FileStream" />, der Lese- und Schreibzugriff auf die in <paramref name="path" /> angegebene Datei bereitstellt.</returns>
      <param name="path">Der Pfad und der Name der zu erstellenden Datei. </param>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.- oder -  <paramref name="path" /> gibt eine schreibgeschützte Datei an. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler beim Erstellen der Datei. </exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Create(System.String,System.Int32)">
      <summary>Erstellt oder überschreibt die angegebene Datei.</summary>
      <returns>Ein <see cref="T:System.IO.FileStream" /> mit der angegebenen Puffergröße, der Lese-/Schreibzugriff auf die in <paramref name="path" /> angegebene Datei bereitstellt.</returns>
      <param name="path">Der Name der Datei. </param>
      <param name="bufferSize">Die Anzahl der für Lese- und Schreibvorgänge in die Datei gepufferten Bytes. </param>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.- oder -  <paramref name="path" /> gibt eine schreibgeschützte Datei an. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler beim Erstellen der Datei. </exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Create(System.String,System.Int32,System.IO.FileOptions)">
      <summary>Erstellt oder überschreibt die angegebene Datei und gibt eine Puffergröße sowie einen <see cref="T:System.IO.FileOptions" />-Wert an, der beschreibt, wie die Datei erstellt oder überschrieben werden soll.</summary>
      <returns>Eine neue Datei mit der angegebenen Puffergröße.</returns>
      <param name="path">Der Name der Datei. </param>
      <param name="bufferSize">Die Anzahl der für Lese- und Schreibvorgänge in die Datei gepufferten Bytes. </param>
      <param name="options">Einer der <see cref="T:System.IO.FileOptions" />-Werte, der beschreibt, wie die Datei erstellt oder überschrieben werden soll.</param>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.- oder -  <paramref name="path" /> gibt eine schreibgeschützte Datei an. - oder - <see cref="F:System.IO.FileOptions.Encrypted" /> wurde für <paramref name="options" /> angegeben, und die Dateiverschlüsselung wird auf der aktuellen Plattform nicht unterstützt.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler beim Erstellen der Datei. </exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.- oder -  <paramref name="path" /> gibt eine schreibgeschützte Datei an. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.- oder -  <paramref name="path" /> gibt eine schreibgeschützte Datei an. </exception>
    </member>
    <member name="M:System.IO.File.CreateText(System.String)">
      <summary>Erstellt oder öffnet eine Datei zum Schreiben von UTF-8 codiertem Text.</summary>
      <returns>Ein <see cref="T:System.IO.StreamWriter" />, der unter Verwendung der UTF-8-Codierung in die angegebene Datei schreibt.</returns>
      <param name="path">Die Datei, die zum Schreiben geöffnet werden soll. </param>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Delete(System.String)">
      <summary>Löscht die angegebene Datei. </summary>
      <param name="path">Der Name der zu löschenden Datei.Platzhalterzeichen werden nicht unterstützt.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <exception cref="T:System.IO.IOException">Die angegebene Datei wird verwendet. - oder - Für die Datei ist ein geöffnetes Handle vorhanden, und das Betriebssystem ist Windows XP oder früher.Dieses geöffnete Handle kann aus der Auflistung von Verzeichnissen und Dateien entstanden sein.Weitere Informationen finden Sie unter Gewusst wie: Auflisten von Verzeichnissen und Dateien.</exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.- oder -  Die Datei ist eine ausführbare Datei, die verwendet wird.- oder -  "<paramref name="path" />" ist ein Verzeichnis.- oder -  <paramref name="path" /> gibt eine schreibgeschützte Datei an. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Exists(System.String)">
      <summary>Bestimmt, ob die angegebene Datei vorhanden ist.</summary>
      <returns>true, wenn der Aufrufer über die erforderlichen Berechtigungen verfügt und <paramref name="path" /> den Namen einer vorhandenen Datei enthält, andernfalls false.Diese Methode gibt auch false zurück, wenn für <paramref name="path" />null, ein ungültiger Pfad oder eine Zeichenfolge der Länge 0 (null) festgelegt ist.Wenn ein Aufrufer nicht über ausreichende Berechtigungen zum Lesen der angegebenen Datei verfügt, wird keine Ausnahme ausgelöst, und die Methode gibt false zurück, unabhängig vom Vorhandensein von <paramref name="path" />.</returns>
      <param name="path">Die zu überprüfende Datei. </param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetAttributes(System.String)">
      <summary>Ruft die <see cref="T:System.IO.FileAttributes" /> der Datei in dem Pfad ab.</summary>
      <returns>Die <see cref="T:System.IO.FileAttributes" /> der Datei in dem Pfad.</returns>
      <param name="path">Der Pfad zur Datei. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist leer oder enthält nur Leerräume oder ungültige Zeichen. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" /> stellt eine Datei dar und ist ungültig (er befindet sich z. B. auf einem nicht zugeordneten Laufwerk), oder die Datei kann nicht gefunden werden. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> stellt eine Datei dar und ist ungültig (er befindet sich z. B. auf einem nicht zugeordneten Laufwerk), oder die Datei kann nicht gefunden werden.</exception>
      <exception cref="T:System.IO.IOException">Diese Datei wird von einem anderen Prozess verwendet.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetCreationTime(System.String)">
      <summary>Gibt das Erstellungsdatum und den Erstellungszeitpunkt für die angegebene Datei bzw. das angegebene Verzeichnis zurück.</summary>
      <returns>Eine <see cref="T:System.DateTime" />-Struktur, die auf das Erstellungsdatum und den Erstellungszeitpunkt für das angegebene Verzeichnis oder die angegebene Datei festgelegt wird.Dieser Wert wird in Ortszeit angegeben.</returns>
      <param name="path">Die Datei oder das Verzeichnis, für die bzw. für das die Informationen über Erstellungsdatum und -zeitpunkt abgerufen werden sollen. </param>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetCreationTimeUtc(System.String)">
      <summary>Gibt das Erstellungsdatum und den Erstellungszeitpunkt der angegebenen Datei bzw. des angegebenen Verzeichnisses im UTC-Format (Coordinated Universal Time) zurück.</summary>
      <returns>Eine <see cref="T:System.DateTime" />-Struktur, die auf das Erstellungsdatum und den Erstellungszeitpunkt für das angegebene Verzeichnis oder die angegebene Datei festgelegt wird.Der Wert wird in UTC-Zeit angegeben.</returns>
      <param name="path">Die Datei oder das Verzeichnis, für die bzw. für das die Informationen über Erstellungsdatum und -zeitpunkt abgerufen werden sollen. </param>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastAccessTime(System.String)">
      <summary>Gibt das Datum und die Uhrzeit des letzten Zugriffs auf die angegebene Datei bzw. das angegebene Verzeichnis zurück.</summary>
      <returns>Eine <see cref="T:System.DateTime" />-Struktur, die auf das Datum und die Uhrzeit des letzten Zugriffs auf die angegebene Datei bzw. das angegebene Verzeichnis festgelegt wird.Dieser Wert wird in Ortszeit angegeben.</returns>
      <param name="path">Die Datei oder das Verzeichnis, für die bzw. das die Informationen über Zugriffsdatum und -zeitpunkt abgerufen werden sollen. </param>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastAccessTimeUtc(System.String)">
      <summary>Gibt das Datum und den Zeitpunkt im UTC-Format (Coordinated Universal Time) des letzten Zugriffs auf die angegebene Datei bzw. das angegebene Verzeichnis zurück.</summary>
      <returns>Eine <see cref="T:System.DateTime" />-Struktur, die auf das Datum und die Uhrzeit des letzten Zugriffs auf die angegebene Datei bzw. das angegebene Verzeichnis festgelegt wird.Der Wert wird in UTC-Zeit angegeben.</returns>
      <param name="path">Die Datei oder das Verzeichnis, für die bzw. das die Informationen über Zugriffsdatum und -zeitpunkt abgerufen werden sollen. </param>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastWriteTime(System.String)">
      <summary>Gibt das Datum und die Uhrzeit des letzten Schreibvorgangs in die angegebene Datei bzw. das angegebene Verzeichnis zurück.</summary>
      <returns>Eine <see cref="T:System.DateTime" />-Struktur, die auf das Datum und die Uhrzeit des letzten Schreibvorgangs in die angegebene Datei bzw. das angegebene Verzeichnis festgelegt wird.Dieser Wert wird in Ortszeit angegeben.</returns>
      <param name="path">Die Datei oder das Verzeichnis, für die bzw. für das die Informationen über Schreibdatum und -zeitpunkt abgerufen werden sollen. </param>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastWriteTimeUtc(System.String)">
      <summary>Gibt das Datum und den Zeitpunkt des letzten Schreibzugriffs auf die angegebenen Datei bzw. das angegebene Verzeichnis im UTC-Format (Coordinated Universal Time) zurück.</summary>
      <returns>Eine <see cref="T:System.DateTime" />-Struktur, die auf das Datum und die Uhrzeit des letzten Schreibvorgangs in die angegebene Datei bzw. das angegebene Verzeichnis festgelegt wird.Der Wert wird in UTC-Zeit angegeben.</returns>
      <param name="path">Die Datei oder das Verzeichnis, für die bzw. für das die Informationen über Schreibdatum und -zeitpunkt abgerufen werden sollen. </param>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Move(System.String,System.String)">
      <summary>Verschiebt eine angegebene Datei an einen neuen Speicherort und ermöglicht das Angeben eines neuen Dateinamens.</summary>
      <param name="sourceFileName">Der Name der zu verschiebenden Datei.Kann einen absoluten oder relativen Pfad enthalten.</param>
      <param name="destFileName">Der neue Pfad und Name für die Datei.</param>
      <exception cref="T:System.IO.IOException">Die Zieldatei ist bereits vorhanden.- oder - <paramref name="sourceFileName" /> wurde nicht gefunden. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> oder <paramref name="destFileName" /> ist null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> oder <paramref name="destFileName" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält durch <see cref="F:System.IO.Path.InvalidPathChars" /> definierte ungültige Zeichen. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der in <paramref name="sourceFileName" /> oder <paramref name="destFileName" /> angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="sourceFileName" /> oder <paramref name="destFileName" /> ist ungültig. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode)">
      <summary>Öffnet einen <see cref="T:System.IO.FileStream" /> für den angegebenen Pfad mit Lese- und Schreibzugriff.</summary>
      <returns>Ein im angegebenen Modus und Pfad geöffneter, nicht freigegebener <see cref="T:System.IO.FileStream" /> mit Lese- und Schreibzugriff.</returns>
      <param name="path">Die zu öffnende Datei. </param>
      <param name="mode">Ein <see cref="T:System.IO.FileMode" />-Wert, der angibt, ob eine Datei erstellt wird, wenn sie nicht vorhanden ist, und bestimmt, ob der Inhalt vorhandener Dateien beibehalten oder überschrieben wird. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler beim Öffnen der Datei. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> gibt eine schreibgeschützte Datei an.- oder -  Dieser Vorgang wird von der aktuellen Plattform nicht unterstützt.- oder -  <paramref name="path" /> gibt ein Verzeichnis an.- oder -  Der Aufrufer verfügt nicht über die erforderliche Berechtigung. - oder - <paramref name="mode" /> ist <see cref="F:System.IO.FileMode.Create" />, und bei der angegebenen Datei handelt es sich um eine verborgene Datei.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> gibt einen ungültigen Wert an. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Die in <paramref name="path" /> angegebene Datei wurde nicht gefunden. </exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode,System.IO.FileAccess)">
      <summary>Öffnet einen <see cref="T:System.IO.FileStream" /> unter dem angegebenen Pfad mit dem angegebenen Modus und Zugriff.</summary>
      <returns>Ein <see cref="T:System.IO.FileStream" /> ohne Freigabe, der Zugriff im angegebenen Modus mit angegebenem Zugriff auf die angegebene Datei bereitstellt.</returns>
      <param name="path">Die zu öffnende Datei. </param>
      <param name="mode">Ein <see cref="T:System.IO.FileMode" />-Wert, der angibt, ob eine Datei erstellt wird, wenn sie nicht vorhanden ist, und bestimmt, ob der Inhalt vorhandener Dateien beibehalten oder überschrieben wird. </param>
      <param name="access">Ein <see cref="T:System.IO.FileAccess" />-Wert, der die Vorgänge angibt, die für die Datei ausgeführt werden können. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen.- oder -  <paramref name="access" /> gibt Read an, und <paramref name="mode" /> gibt Create, CreateNew, Truncate oder Append an. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler beim Öffnen der Datei. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> gibt eine schreibgeschützte Datei an, und <paramref name="access" /> ist nicht Read.- oder -  <paramref name="path" /> gibt ein Verzeichnis an.- oder -  Der Aufrufer verfügt nicht über die erforderliche Berechtigung. - oder - <paramref name="mode" /> ist <see cref="F:System.IO.FileMode.Create" />, und bei der angegebenen Datei handelt es sich um eine verborgene Datei.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> oder <paramref name="access" /> geben einen ungültigen Wert an. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Die in <paramref name="path" /> angegebene Datei wurde nicht gefunden. </exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>Öffnet einen <see cref="T:System.IO.FileStream" /> auf dem angegebenen Pfad, der über den angegebenen Modus mit Lese-, Schreib- oder Lese-/Schreibzugriff und die angegebene Freigabeoption verfügt.</summary>
      <returns>Ein <see cref="T:System.IO.FileStream" /> auf dem angegebenen Pfad, der über den angegebenen Modus mit Lese-, Schreib- oder Lese-/Schreibzugriff und die angegebene Freigabeoption verfügt.</returns>
      <param name="path">Die zu öffnende Datei. </param>
      <param name="mode">Ein <see cref="T:System.IO.FileMode" />-Wert, der angibt, ob eine Datei erstellt wird, wenn sie nicht vorhanden ist, und bestimmt, ob der Inhalt vorhandener Dateien beibehalten oder überschrieben wird. </param>
      <param name="access">Ein <see cref="T:System.IO.FileAccess" />-Wert, der die Vorgänge angibt, die für die Datei ausgeführt werden können. </param>
      <param name="share">Ein <see cref="T:System.IO.FileShare" />-Wert, der die Art des Zugriffs angibt, die andere Threads auf die Datei haben. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen.- oder -  <paramref name="access" /> gibt Read an, und <paramref name="mode" /> gibt Create, CreateNew, Truncate oder Append an. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler beim Öffnen der Datei. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> gibt eine schreibgeschützte Datei an, und <paramref name="access" /> ist nicht Read.- oder -  <paramref name="path" /> gibt ein Verzeichnis an.- oder -  Der Aufrufer verfügt nicht über die erforderliche Berechtigung. - oder - <paramref name="mode" /> ist <see cref="F:System.IO.FileMode.Create" />, und bei der angegebenen Datei handelt es sich um eine verborgene Datei.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Für <paramref name="mode" />, <paramref name="access" /> oder <paramref name="share" /> ist ein ungültiger Wert angegeben. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Die in <paramref name="path" /> angegebene Datei wurde nicht gefunden. </exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.OpenRead(System.String)">
      <summary>Öffnet eine vorhandene Datei zum Lesen.</summary>
      <returns>Ein schreibgeschützter <see cref="T:System.IO.FileStream" /> für den angegebenen Pfad.</returns>
      <param name="path">Die Datei, die zum Lesen geöffnet werden soll. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> gibt ein Verzeichnis an.- oder -  Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Die in <paramref name="path" /> angegebene Datei wurde nicht gefunden. </exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler beim Öffnen der Datei. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.OpenText(System.String)">
      <summary>Öffnet eine vorhandene UTF-8-codierte Textdatei zum Lesen.</summary>
      <returns>Ein <see cref="T:System.IO.StreamReader" /> für den angegebenen Pfad.</returns>
      <param name="path">Die Datei, die zum Lesen geöffnet werden soll. </param>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <exception cref="T:System.IO.FileNotFoundException">Die in <paramref name="path" /> angegebene Datei wurde nicht gefunden. </exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.OpenWrite(System.String)">
      <summary>Öffnet eine vorhandene Datei oder erstellt eine neue Datei zum Schreiben.</summary>
      <returns>Ein nicht freigegebenes <see cref="T:System.IO.FileStream" />-Objekt am angegebenen Pfad mit <see cref="F:System.IO.FileAccess.Write" />-Zugriff.</returns>
      <param name="path">Die Datei, die zum Schreiben geöffnet werden soll. </param>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.- oder -  <paramref name="path" /> gibt eine schreibgeschützte Datei oder ein schreibgeschütztes Verzeichnis an. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllBytes(System.String)">
      <summary>Öffnet eine Binärdatei, liest den Inhalt der Datei in ein Bytearray ein und schließt dann die Datei.</summary>
      <returns>Ein Bytearray mit dem Inhalt der Datei.</returns>
      <param name="path">Die Datei, die zum Lesen geöffnet werden soll. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler beim Öffnen der Datei. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Dieser Vorgang wird von der aktuellen Plattform nicht unterstützt.- oder -  <paramref name="path" /> gibt ein Verzeichnis an.- oder -  Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Die in <paramref name="path" /> angegebene Datei wurde nicht gefunden. </exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllLines(System.String)">
      <summary>Öffnet eine Textdatei, liest alle Zeilen der Datei und schließt dann die Datei.</summary>
      <returns>Ein Zeichenfolgenarray, das alle Zeilen der Datei enthält.</returns>
      <param name="path">Die Datei, die zum Lesen geöffnet werden soll. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler beim Öffnen der Datei. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> gibt eine schreibgeschützte Datei an.- oder -  Dieser Vorgang wird von der aktuellen Plattform nicht unterstützt.- oder -  <paramref name="path" /> gibt ein Verzeichnis an.- oder -  Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Die in <paramref name="path" /> angegebene Datei wurde nicht gefunden. </exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllLines(System.String,System.Text.Encoding)">
      <summary>Öffnet eine Datei, liest alle Zeilen der Datei mit der angegebenen Codierung und schließt dann die Datei.</summary>
      <returns>Ein Zeichenfolgenarray, das alle Zeilen der Datei enthält.</returns>
      <param name="path">Die Datei, die zum Lesen geöffnet werden soll. </param>
      <param name="encoding">Die auf den Inhalt der Datei angewendete Codierung. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler beim Öffnen der Datei. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> gibt eine schreibgeschützte Datei an.- oder -  Dieser Vorgang wird von der aktuellen Plattform nicht unterstützt.- oder -  <paramref name="path" /> gibt ein Verzeichnis an.- oder -  Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Die in <paramref name="path" /> angegebene Datei wurde nicht gefunden. </exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllText(System.String)">
      <summary>Öffnet eine Textdatei, liest alle Zeilen der Datei und schließt dann die Datei.</summary>
      <returns>Eine Zeichenfolge, die alle Zeilen der Datei enthält.</returns>
      <param name="path">Die Datei, die zum Lesen geöffnet werden soll. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler beim Öffnen der Datei. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> gibt eine schreibgeschützte Datei an.- oder -  Dieser Vorgang wird von der aktuellen Plattform nicht unterstützt.- oder -  <paramref name="path" /> gibt ein Verzeichnis an.- oder -  Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Die in <paramref name="path" /> angegebene Datei wurde nicht gefunden. </exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllText(System.String,System.Text.Encoding)">
      <summary>Öffnet eine Datei, liest alle Zeilen der Datei mit der angegebenen Codierung und schließt dann die Datei.</summary>
      <returns>Eine Zeichenfolge, die alle Zeilen der Datei enthält.</returns>
      <param name="path">Die Datei, die zum Lesen geöffnet werden soll. </param>
      <param name="encoding">Die auf den Inhalt der Datei angewendete Codierung. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler beim Öffnen der Datei. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> gibt eine schreibgeschützte Datei an.- oder -  Dieser Vorgang wird von der aktuellen Plattform nicht unterstützt.- oder -  <paramref name="path" /> gibt ein Verzeichnis an.- oder -  Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Die in <paramref name="path" /> angegebene Datei wurde nicht gefunden. </exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadLines(System.String)">
      <summary>Liest die Zeilen einer Datei.</summary>
      <returns>Alle Zeilen der Datei oder die Zeilen, die das Ergebnis einer Abfrage sind.</returns>
      <param name="path">Die zu lesende Datei.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält mindestens eines der von der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode definierten ungültigen Zeichen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk).</exception>
      <exception cref="T:System.IO.FileNotFoundException">Die von <paramref name="path" /> angegebene Datei wurde nicht gefunden.</exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler beim Öffnen der Datei.</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> überschreitet die im System definierte maximale Länge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> gibt eine schreibgeschützte Datei an.- oder - Dieser Vorgang wird von der aktuellen Plattform nicht unterstützt.- oder - "<paramref name="path" />" ist ein Verzeichnis.- oder - Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
    </member>
    <member name="M:System.IO.File.ReadLines(System.String,System.Text.Encoding)">
      <summary>Liest die Zeilen einer Datei mit einer angegebenen Codierung.</summary>
      <returns>Alle Zeilen der Datei oder die Zeilen, die das Ergebnis einer Abfrage sind.</returns>
      <param name="path">Die zu lesende Datei.</param>
      <param name="encoding">Die auf den Inhalt der Datei angewendete Codierung. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält mindestens eines der von der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode definierten ungültige Zeichen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk).</exception>
      <exception cref="T:System.IO.FileNotFoundException">Die von <paramref name="path" /> angegebene Datei wurde nicht gefunden.</exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler beim Öffnen der Datei.</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> überschreitet die im System definierte maximale Länge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> gibt eine schreibgeschützte Datei an.- oder - Dieser Vorgang wird von der aktuellen Plattform nicht unterstützt.- oder - "<paramref name="path" />" ist ein Verzeichnis.- oder - Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
    </member>
    <member name="M:System.IO.File.SetAttributes(System.String,System.IO.FileAttributes)">
      <summary>Legt die angegebenen <see cref="T:System.IO.FileAttributes" /> der Datei im angegebenen Pfad fest.</summary>
      <param name="path">Der Pfad zur Datei. </param>
      <param name="fileAttributes">Eine bitweise Kombination der Enumerationswerte. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist leer, besteht nur aus Leerraum, enthält ungültige Zeichen, oder das Dateiattribut ist ungültig. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <exception cref="T:System.IO.FileNotFoundException">Die Datei kann nicht gefunden werden.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> gibt eine schreibgeschützte Datei an.- oder -  Dieser Vorgang wird von der aktuellen Plattform nicht unterstützt.- oder -  <paramref name="path" /> gibt ein Verzeichnis an.- oder -  Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetCreationTime(System.String,System.DateTime)">
      <summary>Legt das Datum und die Uhrzeit fest, zu der die Datei erstellt wurde.</summary>
      <param name="path">Die Datei, für die die Informationen über Erstellungsdatum und -zeitpunkt festgelegt werden sollen. </param>
      <param name="creationTime">Eine <see cref="T:System.DateTime" />, die den festzulegenden Wert für Erstellungsdatum und -zeitpunkt von <paramref name="path" /> enthält.Dieser Wert wird in Ortszeit angegeben.</param>
      <exception cref="T:System.IO.FileNotFoundException">Der angegebene Pfad wurde nicht gefunden. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler beim Ausführen des Vorgangs. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> gibt einen Wert an, der außerhalb des für diesen Vorgang zulässigen Bereichs für Datum oder Zeit oder beidem liegt. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetCreationTimeUtc(System.String,System.DateTime)">
      <summary>Legt das Datum und die Uhrzeit der Erstellung der Datei im UTC-Format (Coordinated Universal Time) fest.</summary>
      <param name="path">Die Datei, für die die Informationen über Erstellungsdatum und -zeitpunkt festgelegt werden sollen. </param>
      <param name="creationTimeUtc">Eine <see cref="T:System.DateTime" />, die den festzulegenden Wert für Erstellungsdatum und -zeitpunkt von <paramref name="path" /> enthält.Der Wert wird in UTC-Zeit angegeben.</param>
      <exception cref="T:System.IO.FileNotFoundException">Der angegebene Pfad wurde nicht gefunden. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler beim Ausführen des Vorgangs. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> gibt einen Wert an, der außerhalb des für diesen Vorgang zulässigen Bereichs für Datum oder Zeit oder beidem liegt. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastAccessTime(System.String,System.DateTime)">
      <summary>Legt das Datum und die Uhrzeit des letzten Zugriffs auf die angegebene Datei fest.</summary>
      <param name="path">Die Datei, für die die Informationen über Zugriffsdatum und -zeitpunkt festgelegt werden sollen. </param>
      <param name="lastAccessTime">Eine <see cref="T:System.DateTime" />, die den festzulegenden Wert für Datum und Zeitpunkt des letzten Zugriffs auf <paramref name="path" /> enthält.Dieser Wert wird in Ortszeit angegeben.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Der angegebene Pfad wurde nicht gefunden. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTime" /> gibt einen Wert an, der außerhalb des für diesen Vorgang zulässigen Bereichs für Datum oder Zeit liegt.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastAccessTimeUtc(System.String,System.DateTime)">
      <summary>Legt das Datum und den Zeitpunkt des letzten Zugriffs auf die angegebene Datei im UTC-Format (Coordinated Universal Time) fest.</summary>
      <param name="path">Die Datei, für die die Informationen über Zugriffsdatum und -zeitpunkt festgelegt werden sollen. </param>
      <param name="lastAccessTimeUtc">Eine <see cref="T:System.DateTime" />, die den festzulegenden Wert für Datum und Zeitpunkt des letzten Zugriffs auf <paramref name="path" /> enthält.Der Wert wird in UTC-Zeit angegeben.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Der angegebene Pfad wurde nicht gefunden. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTimeUtc" /> gibt einen Wert an, der außerhalb des für diesen Vorgang zulässigen Bereichs für Datum oder Zeit liegt.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastWriteTime(System.String,System.DateTime)">
      <summary>Legt das Datum und die Uhrzeit des letzten Schreibvorgangs in die angegebene Datei fest.</summary>
      <param name="path">Die Datei, für die die Informationen über Datum und Uhrzeit festgelegt werden sollen. </param>
      <param name="lastWriteTime">Eine <see cref="T:System.DateTime" />, die den festzulegenden Wert für Datum und Uhrzeit des letzten Schreibvorgangs von <paramref name="path" /> enthält.Dieser Wert wird in Ortszeit angegeben.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Der angegebene Pfad wurde nicht gefunden. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTime" /> gibt einen Wert an, der außerhalb des für diesen Vorgang zulässigen Bereichs für Datum oder Zeit liegt.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastWriteTimeUtc(System.String,System.DateTime)">
      <summary>Legt das Datum und den Zeitpunkt des letzten Schreibzugriffs auf die angegebene Datei im UTC-Format (Coordinated Universal Time) fest.</summary>
      <param name="path">Die Datei, für die die Informationen über Datum und Uhrzeit festgelegt werden sollen. </param>
      <param name="lastWriteTimeUtc">Eine <see cref="T:System.DateTime" />, die den festzulegenden Wert für Datum und Uhrzeit des letzten Schreibvorgangs von <paramref name="path" /> enthält.Der Wert wird in UTC-Zeit angegeben.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Der angegebene Pfad wurde nicht gefunden. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTimeUtc" /> gibt einen Wert an, der außerhalb des für diesen Vorgang zulässigen Bereichs für Datum oder Zeit liegt.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.WriteAllBytes(System.String,System.Byte[])">
      <summary>Erstellt eine neue Datei, schreibt das angegebene Bytearray in die Datei und schließt die Datei dann.Ist die Zieldatei bereits vorhanden, wird sie überschrieben.</summary>
      <param name="path">Die Datei, in die geschrieben werden soll. </param>
      <param name="bytes">Die Bytes, die in die Datei geschrieben werden sollen. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null, oder das Bytearray ist leer. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler beim Öffnen der Datei. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> gibt eine schreibgeschützte Datei an.- oder -  Dieser Vorgang wird von der aktuellen Plattform nicht unterstützt.- oder -  <paramref name="path" /> gibt ein Verzeichnis an.- oder -  Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.WriteAllLines(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>Erstellt eine neue Datei, schreibt eine Auflistung von Zeichenfolgen in die Datei und schließt dann die Datei.</summary>
      <param name="path">Die Datei, in die geschrieben werden soll.</param>
      <param name="contents">Die Zeilen, die in die Datei geschrieben werden sollen.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält mindestens eines der von der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode definierten ungültigen Zeichen.</exception>
      <exception cref="T:System.ArgumentNullException">Entweder<paramref name=" path " />oder <paramref name="contents" /> ist null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk).</exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler beim Öffnen der Datei.</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> überschreitet die im System definierte maximale Länge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig.</exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> gibt eine schreibgeschützte Datei an.- oder - Dieser Vorgang wird von der aktuellen Plattform nicht unterstützt.- oder - "<paramref name="path" />" ist ein Verzeichnis.- oder - Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
    </member>
    <member name="M:System.IO.File.WriteAllLines(System.String,System.Collections.Generic.IEnumerable{System.String},System.Text.Encoding)">
      <summary>Erstellt eine neue Datei unter Verwendung der angegebenen Codierung, schreibt eine Auflistung von Zeichenfolgen in die Datei und schließt dann die Datei.</summary>
      <param name="path">Die Datei, in die geschrieben werden soll.</param>
      <param name="contents">Die Zeilen, die in die Datei geschrieben werden sollen.</param>
      <param name="encoding">Die zu verwendende Zeichencodierung.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält mindestens eines der von der <see cref="M:System.IO.Path.GetInvalidPathChars" />-Methode definierten ungültigen Zeichen.</exception>
      <exception cref="T:System.ArgumentNullException">Entweder<paramref name=" path" />,<paramref name=" contents" />, oder <paramref name="encoding" /> ist null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk).</exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler beim Öffnen der Datei.</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> überschreitet die im System definierte maximale Länge.Beispielsweise dürfen auf Windows-Plattformen Pfade nicht länger als 247 Zeichen und Dateinamen nicht länger als 259 Zeichen sein.</exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig.</exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> gibt eine schreibgeschützte Datei an.- oder - Dieser Vorgang wird von der aktuellen Plattform nicht unterstützt.- oder - "<paramref name="path" />" ist ein Verzeichnis.- oder - Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
    </member>
    <member name="M:System.IO.File.WriteAllText(System.String,System.String)">
      <summary>Erstellt eine neue Datei, schreibt die angegebene Zeichenfolge in die Datei und schließt die Datei dann.Ist die Zieldatei bereits vorhanden, wird sie überschrieben.</summary>
      <param name="path">Die Datei, in die geschrieben werden soll. </param>
      <param name="contents">Die Zeichenfolge, die in die Datei geschrieben werden soll. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null oder <paramref name="contents" /> ist leer.  </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler beim Öffnen der Datei. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> gibt eine schreibgeschützte Datei an.- oder -  Dieser Vorgang wird von der aktuellen Plattform nicht unterstützt.- oder -  <paramref name="path" /> gibt ein Verzeichnis an.- oder -  Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.WriteAllText(System.String,System.String,System.Text.Encoding)">
      <summary>Erstellt eine neue Datei, schreibt die angegebene Zeichenfolge mit der angegebenen Codierung in die Datei und schließt die Datei dann.Ist die Zieldatei bereits vorhanden, wird sie überschrieben.</summary>
      <param name="path">Die Datei, in die geschrieben werden soll. </param>
      <param name="contents">Die Zeichenfolge, die in die Datei geschrieben werden soll. </param>
      <param name="encoding">Die auf die Zeichenfolge anzuwendende Codierung.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0, besteht nur aus Leerraum oder enthält mindestens ein durch <see cref="F:System.IO.Path.InvalidPathChars" /> definiertes ungültiges Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null oder <paramref name="contents" /> ist leer. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig (z. B. befindet er sich auf einem nicht zugeordneten Laufwerk). </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler beim Öffnen der Datei. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> gibt eine schreibgeschützte Datei an.- oder -  Dieser Vorgang wird von der aktuellen Plattform nicht unterstützt.- oder -  <paramref name="path" /> gibt ein Verzeichnis an.- oder -  Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.NotSupportedException">Das Format von <paramref name="path" /> ist ungültig. </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.IO.FileInfo">
      <summary>Stellt Eigenschaften und Instanzmethoden zum Erstellen, Kopieren, Löschen, Verschieben und Öffnen von Dateien bereit und unterstützt das Erstellen von <see cref="T:System.IO.FileStream" />-Objekten.Diese Klasse kann nicht vererbt werden.Um den .NET Framework-Quellcode für diesen Typ zu durchsuchen, finden Sie unter der Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileInfo.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.FileInfo" />-Klasse, die als Wrapper für einen Dateipfad fungiert.</summary>
      <param name="fileName">Der vollqualifizierte Name der neuen Datei oder der relative Dateiname.Der Pfad darf nicht mit dem Verzeichnistrennzeichen enden.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="fileName" /> ist null. </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentException">Der Dateiname ist leer, oder er enthält nur Leerräume oder ungültige Zeichen. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Zugriff auf <paramref name="fileName" /> wird verweigert. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="fileName" /> enthält einen Doppelpunkt (:) innerhalb der Zeichenfolge. </exception>
    </member>
    <member name="M:System.IO.FileInfo.AppendText">
      <summary>Erstellt einen <see cref="T:System.IO.StreamWriter" />, der der Datei Text hinzufügt, die von dieser Instanz von <see cref="T:System.IO.FileInfo" /> dargestellt wird.</summary>
      <returns>Ein neuer StreamWriter.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.CopyTo(System.String)">
      <summary>Kopiert eine vorhandene Datei in eine neue Datei, ohne das Überschreiben einer vorhandenen Datei zuzulassen.</summary>
      <returns>Eine neue Datei mit einem vollqualifizierten Pfad.</returns>
      <param name="destFileName">Der Name der neuen Datei, in die kopiert werden soll. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destFileName" /> ist leer oder enthält nur Leerräume oder ungültige Zeichen. </exception>
      <exception cref="T:System.IO.IOException">Es tritt ein Fehler auf, oder die Zieldatei ist bereits vorhanden. </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destFileName" /> ist null. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Ein Verzeichnispfad wird übergeben, oder die Datei wird auf ein anderes Laufwerk verschoben. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Das in <paramref name="destFileName" /> angegebene Verzeichnis ist nicht vorhanden.</exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.NotSupportedException">Die Zeichenfolge von <paramref name="destFileName" /> enthält einen Doppelpunkt (:), aber das Volume ist nicht angegeben. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.CopyTo(System.String,System.Boolean)">
      <summary>Kopiert eine vorhandene Datei in eine neue Datei und lässt das Überschreiben einer vorhandenen Datei zu.</summary>
      <returns>Eine neue Datei oder eine Überschreibung einer vorhandenen Datei, wenn <paramref name="overwrite" />true ist.Wenn die Datei vorhanden und <paramref name="overwrite" />false ist, wird eine <see cref="T:System.IO.IOException" /> ausgelöst.</returns>
      <param name="destFileName">Der Name der neuen Datei, in die kopiert werden soll. </param>
      <param name="overwrite">true, um das Überschreiben einer vorhandenen Datei zuzulassen, andernfalls false. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destFileName" /> ist leer oder enthält nur Leerräume oder ungültige Zeichen. </exception>
      <exception cref="T:System.IO.IOException">Es tritt ein Fehler auf, oder die Zieldatei ist bereits vorhanden, und <paramref name="overwrite" /> ist false. </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destFileName" /> ist null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Das in <paramref name="destFileName" /> angegebene Verzeichnis ist nicht vorhanden.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Ein Verzeichnispfad wird übergeben, oder die Datei wird auf ein anderes Laufwerk verschoben. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destFileName" /> enthält einen Doppelpunkt (:) innerhalb der Zeichenfolge. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Create">
      <summary>Erstellt eine Datei.</summary>
      <returns>Eine neue Datei.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.CreateText">
      <summary>Erstellt einen <see cref="T:System.IO.StreamWriter" />, der eine neue Textdatei erstellt und in diese schreibt.</summary>
      <returns>Ein neuer StreamWriter.</returns>
      <exception cref="T:System.UnauthorizedAccessException">Der Dateiname ist ein Verzeichnis. </exception>
      <exception cref="T:System.IO.IOException">Der Datenträger ist schreibgeschützt. </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Delete">
      <summary>Löscht eine Datei unwiderruflich.</summary>
      <exception cref="T:System.IO.IOException">Die Zieldatei ist geöffnet, oder es handelt sich um eine Datei mit Speicherzuordnung (Memory-Mapped File) auf einem Computer, auf dem Microsoft Windows NT ausgeführt ist.- oder - Für die Datei ist ein geöffnetes Handle vorhanden, und das Betriebssystem ist Windows XP oder früher.Dieses geöffnete Handle kann aus der Auflistung von Verzeichnissen und Dateien entstanden sein.Weitere Informationen finden Sie unter Gewusst wie: Auflisten von Verzeichnissen und Dateien.</exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der Pfad ist ein Verzeichnis. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.Directory">
      <summary>Ruft eine Instanz des übergeordneten Verzeichnisses ab.</summary>
      <returns>Ein <see cref="T:System.IO.DirectoryInfo" />-Objekt, das das übergeordnete Verzeichnis dieser Datei darstellt.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig. Dies ist z. B. der Fall, wenn das Laufwerk des Pfads nicht zugeordnet ist. </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.DirectoryName">
      <summary>Ruft eine Zeichenfolge ab, die den vollständigen Pfad des Verzeichnisses darstellt.</summary>
      <returns>Eine Zeichenfolge, die den vollständigen Pfad des Verzeichnisses darstellt.</returns>
      <exception cref="T:System.ArgumentNullException">Als Verzeichnisname wurde null übergeben. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der vollqualifizierte Pfad ist 260 oder mehr Zeichen.</exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.Exists">
      <summary>Ruft einen Wert ab, der angibt, ob eine Datei vorhanden ist.</summary>
      <returns>true, wenn die Datei vorhanden ist; false, wenn die Datei nicht vorhanden ist oder es sich um ein Verzeichnis handelt.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileInfo.IsReadOnly">
      <summary>Ruft einen Wert ab, der bestimmt, ob die aktuelle Datei schreibgeschützt ist, oder legt diesen Wert fest.</summary>
      <returns>true, wenn die aktuelle Datei schreibgeschützt ist, andernfalls false.</returns>
      <exception cref="T:System.IO.FileNotFoundException">Die durch das aktuelle <see cref="T:System.IO.FileInfo" />-Objekt beschriebene Datei konnte nicht gefunden werden.</exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler beim Öffnen der Datei.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Dieser Vorgang wird von der aktuellen Plattform nicht unterstützt.- oder - Der Aufrufer verfügt nicht über die erforderliche Berechtigung.</exception>
      <exception cref="T:System.ArgumentException">Der Benutzer verfügt nicht über die Schreibberechtigung, hat jedoch versucht, diese Eigenschaft auf false festzulegen.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileInfo.Length">
      <summary>Ruft die Größe der aktuellen Datei in Byte ab.</summary>
      <returns>Die Größe der aktuellen Datei in Bytes.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> kann den Zustand der Datei oder des Verzeichnisses nicht aktualisieren. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Die Datei ist nicht vorhanden.- oder -  Die Length-Eigenschaft wird für ein Verzeichnis aufgerufen. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileInfo.MoveTo(System.String)">
      <summary>Verschiebt eine angegebene Datei an einen neuen Speicherort und ermöglicht das Angeben eines neuen Dateinamens.</summary>
      <param name="destFileName">Der Pfad, in den die Datei verschoben werden soll. Dadurch kann ein anderer Dateiname angegeben werden. </param>
      <exception cref="T:System.IO.IOException">Es ist ein E/A-Fehler aufgetreten, beispielsweise ist die Zieldatei bereits vorhanden, oder das Zielgerät ist nicht bereit. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destFileName" /> ist null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="destFileName" /> ist leer oder enthält nur Leerräume oder ungültige Zeichen. </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="destFileName" /> ist schreibgeschützt oder ein Verzeichnis. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Die Datei wurde nicht gefunden. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig. Dies ist z. B. der Fall, wenn das Laufwerk des Pfads nicht zugeordnet ist. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destFileName" /> enthält einen Doppelpunkt (:) innerhalb der Zeichenfolge. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.Name">
      <summary>Ruft den Namen der Datei ab.</summary>
      <returns>Der Name der Datei.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode)">
      <summary>Öffnet eine Datei im angegebenen Modus.</summary>
      <returns>Eine im angegebenen Modus geöffnete Datei mit Lese-/Schreibzugriff und ohne Freigabe.</returns>
      <param name="mode">Eine <see cref="T:System.IO.FileMode" />-Konstante, die den Modus angibt, in dem die Datei geöffnet werden soll (z. B. Open oder Append). </param>
      <exception cref="T:System.IO.FileNotFoundException">Die Datei wurde nicht gefunden. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Die Datei ist schreibgeschützt oder ein Verzeichnis. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig. Dies ist z. B. der Fall, wenn das Laufwerk des Pfads nicht zugeordnet ist. </exception>
      <exception cref="T:System.IO.IOException">Die Datei ist bereits geöffnet. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode,System.IO.FileAccess)">
      <summary>Öffnet eine Datei im angegebenen Modus mit Lese-, Schreib- oder Lese-/Schreibzugriff.</summary>
      <returns>Ein im angegebenen Modus und mit dem angegebenem Zugriff geöffnetes <see cref="T:System.IO.FileStream" />-Objekt ohne Freigabe.</returns>
      <param name="mode">Eine <see cref="T:System.IO.FileMode" />-Konstante, die den Modus angibt, in dem die Datei geöffnet werden soll (z. B. Open oder Append). </param>
      <param name="access">Eine <see cref="T:System.IO.FileAccess" />-Konstante, die angibt, ob die Datei mit Read-Zugriff, Write-Zugriff oder ReadWrite-Zugriff geöffnet werden soll. </param>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Die Datei wurde nicht gefunden. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> ist schreibgeschützt oder ein Verzeichnis. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig. Dies ist z. B. der Fall, wenn das Laufwerk des Pfads nicht zugeordnet ist. </exception>
      <exception cref="T:System.IO.IOException">Die Datei ist bereits geöffnet. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>Öffnet eine Datei im angegebenen Modus mit Lese-, Schreib- oder Lese-/Schreibzugriff und der angegebenen Freigabeoption.</summary>
      <returns>Ein mit dem angegebenen Modus, dem angegebenen Zugriff und den angegebenen Freigabeoptionen geöffnetes <see cref="T:System.IO.FileStream" />-Objekt.</returns>
      <param name="mode">Eine <see cref="T:System.IO.FileMode" />-Konstante, die den Modus angibt, in dem die Datei geöffnet werden soll (z. B. Open oder Append). </param>
      <param name="access">Eine <see cref="T:System.IO.FileAccess" />-Konstante, die angibt, ob die Datei mit Read-Zugriff, Write-Zugriff oder ReadWrite-Zugriff geöffnet werden soll. </param>
      <param name="share">Eine <see cref="T:System.IO.FileShare" />-Konstante, die die Art des Zugriffs angibt, die andere FileStream-Objekte auf diese Datei haben. </param>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Die Datei wurde nicht gefunden. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> ist schreibgeschützt oder ein Verzeichnis. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig. Dies ist z. B. der Fall, wenn das Laufwerk des Pfads nicht zugeordnet ist. </exception>
      <exception cref="T:System.IO.IOException">Die Datei ist bereits geöffnet. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.OpenRead">
      <summary>Erstellt einen schreibgeschützten <see cref="T:System.IO.FileStream" />.</summary>
      <returns>Ein neues schreibgeschütztes <see cref="T:System.IO.FileStream" />-Objekt.</returns>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> ist schreibgeschützt oder ein Verzeichnis. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig. Dies ist z. B. der Fall, wenn das Laufwerk des Pfads nicht zugeordnet ist. </exception>
      <exception cref="T:System.IO.IOException">Die Datei ist bereits geöffnet. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.OpenText">
      <summary>Erstellt einen <see cref="T:System.IO.StreamReader" /> mit UTF8-Codierung, der aus einer vorhandenen Textdatei liest.</summary>
      <returns>Ein neuer StreamReader mit UTF8-Codierung.</returns>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Die Datei wurde nicht gefunden. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> ist schreibgeschützt oder ein Verzeichnis. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig. Dies ist z. B. der Fall, wenn das Laufwerk des Pfads nicht zugeordnet ist. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.OpenWrite">
      <summary>Erstellt einen lesegeschützten <see cref="T:System.IO.FileStream" />.</summary>
      <returns>Ein lesegeschütztes nicht freigegebenes <see cref="T:System.IO.FileStream" />-Objekt für eine neue oder existierende Datei.</returns>
      <exception cref="T:System.UnauthorizedAccessException">Der beim Erstellen einer Instanz des <see cref="T:System.IO.FileInfo" />-Objekts angegebene Pfad ist schreibgeschützt oder ein Verzeichnis.  </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der Pfad beim Erstellen eines <see cref="T:System.IO.FileInfo" />-Objekts angegebene Pfad ist ungültig. Dies ist z. B. der Fall, wenn das Laufwerk des Pfades nicht zugeordnet ist. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.ToString">
      <summary>Gibt den Pfad als Zeichenfolge zurück.</summary>
      <returns>Eine Zeichenfolge, die den Pfad darstellt.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.FileOptions">
      <summary>Stellt zusätzliche Optionen für das Erstellen eines <see cref="T:System.IO.FileStream" />-Objekts dar.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.FileOptions.Asynchronous">
      <summary>Gibt an, dass eine Datei für asynchrone Lese- und Schreibvorgänge verwendet werden kann. </summary>
    </member>
    <member name="F:System.IO.FileOptions.DeleteOnClose">
      <summary>Gibt an, dass eine Datei automatisch gelöscht wird, wenn sie nicht mehr verwendet wird.</summary>
    </member>
    <member name="F:System.IO.FileOptions.Encrypted">
      <summary>Gibt an, dass eine Datei verschlüsselt ist und nur mit dem Benutzerkonto entschlüsselt werden kann, das für die Verschlüsselung verwendet wurde.</summary>
    </member>
    <member name="F:System.IO.FileOptions.None">
      <summary>Gibt an, dass keine zusätzlichen Optionen verwendet werden sollen, wenn ein <see cref="T:System.IO.FileStream" />-Objekt erstellt wird.</summary>
    </member>
    <member name="F:System.IO.FileOptions.RandomAccess">
      <summary>Gibt an, dass auf die Datei willkürlich zugegriffen wird.Das System kann dies als Hinweis zur Optimierung der Zwischenspeicherung von Dateien verwenden.</summary>
    </member>
    <member name="F:System.IO.FileOptions.SequentialScan">
      <summary>Gibt an, dass auf die Datei sequenziell vom Anfang bis zum Ende zugegriffen wird.Das System kann dies als Hinweis zur Optimierung der Zwischenspeicherung von Dateien verwenden.Wenn eine Anwendung den Dateizeiger für den zufälligen Zugriff verschiebt, erfolgt möglicherweise keine optimale Zwischenspeicherung. Die ordnungsgemäße Ausführung ist jedoch weiterhin gewährleistet.</summary>
    </member>
    <member name="F:System.IO.FileOptions.WriteThrough">
      <summary>Gibt an, dass das System durch jeden Zwischencache schreiben und direkt zum Datenträger wechseln soll.</summary>
    </member>
    <member name="T:System.IO.FileStream">
      <summary>Stellt einen <see cref="T:System.IO.Stream" /> für eine Datei bereit, wobei synchrone und asynchrone Lese- und Schreibvorgänge unterstützt werden.Um den .NET Framework-Quellcode für diesen Typ zu durchsuchen, finden Sie unter der Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.#ctor(Microsoft.Win32.SafeHandles.SafeFileHandle,System.IO.FileAccess)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.FileStream" />-Klasse für das angegebene Dateihandle und mit der angegebenen Lese- und Schreibberechtigung. </summary>
      <param name="handle">Ein Dateihandle für die Datei, die vom aktuellen FileStream-Objekt gekapselt wird. </param>
      <param name="access">Eine Konstante, die die <see cref="P:System.IO.FileStream.CanRead" />-Eigenschaft und die <see cref="P:System.IO.FileStream.CanWrite" />-Eigenschaft des FileStream-Objekts festlegt. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="access" /> ist kein Feld von <see cref="T:System.IO.FileAccess" />. </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.IO.IOException">Es ist ein E/A-Fehler aufgetreten, z. B. ein Datenträgerfehler.- oder - Der Stream wurde geschlossen. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der angeforderte <paramref name="access" /> für das angegebene Dateihandle wird durch das Betriebssystem nicht zugelassen. Dies ist z. B. der Fall, wenn <paramref name="access" /> oder Write für ReadWrite festgelegt sind und das Dateihandle auf schreibgeschützten Zugriff festgelegt ist. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(Microsoft.Win32.SafeHandles.SafeFileHandle,System.IO.FileAccess,System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.FileStream" />-Klasse für das angegebene Dateihandle mit den Angaben für die Lese-/Schreibberechtigung und die Puffergröße.</summary>
      <param name="handle">Ein Dateihandle für die Datei, die vom aktuellen FileStream-Objekt gekapselt wird. </param>
      <param name="access">Eine <see cref="T:System.IO.FileAccess" />-Konstante, die die <see cref="P:System.IO.FileStream.CanRead" />- und <see cref="P:System.IO.FileStream.CanWrite" />-Eigenschaften des FileStream-Objekts festlegt. </param>
      <param name="bufferSize">Ein positiver <see cref="T:System.Int32" />-Wert größer als 0 (null), der die Puffergröße angibt.Die Standardpuffergröße ist 4096.</param>
      <exception cref="T:System.ArgumentException">Der <paramref name="handle" />-Parameter ist ein ungültiges Handle.- oder - Der <paramref name="handle" />-Parameter ist ein synchrones Handle und wurde asynchron verwendet. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der <paramref name="bufferSize" />-Parameter ist negativ. </exception>
      <exception cref="T:System.IO.IOException">Es ist ein E/A-Fehler aufgetreten, z. B. ein Datenträgerfehler.- oder - Der Stream wurde geschlossen.  </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der angeforderte <paramref name="access" /> für das angegebene Dateihandle wird durch das Betriebssystem nicht zugelassen. Dies ist z. B. der Fall, wenn <paramref name="access" /> oder Write für ReadWrite festgelegt sind und das Dateihandle auf schreibgeschützten Zugriff festgelegt ist. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(Microsoft.Win32.SafeHandles.SafeFileHandle,System.IO.FileAccess,System.Int32,System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.FileStream" />-Klasse für das angegebene Dateihandle mit den Angaben für die Lese-/Schreibberechtigung, die Puffergröße und den synchronen bzw. asynchronen Zustand.</summary>
      <param name="handle">Ein Dateihandle für die Datei, die von diesem FileStream-Objekt gekapselt wird. </param>
      <param name="access">Eine Konstante, die die <see cref="P:System.IO.FileStream.CanRead" />-Eigenschaft und die <see cref="P:System.IO.FileStream.CanWrite" />-Eigenschaft des FileStream-Objekts festlegt. </param>
      <param name="bufferSize">Ein positiver <see cref="T:System.Int32" />-Wert größer als 0 (null), der die Puffergröße angibt.Die Standardpuffergröße ist 4096.</param>
      <param name="isAsync">true, wenn das Handle asynchron geöffnet wurde (im überlappenden E/A-Modus), andernfalls false. </param>
      <exception cref="T:System.ArgumentException">Der <paramref name="handle" />-Parameter ist ein ungültiges Handle.- oder - Der <paramref name="handle" />-Parameter ist ein synchrones Handle und wurde asynchron verwendet. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der <paramref name="bufferSize" />-Parameter ist negativ. </exception>
      <exception cref="T:System.IO.IOException">Es ist ein E/A-Fehler aufgetreten, z. B. ein Datenträgerfehler.- oder - Der Stream wurde geschlossen.  </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der angeforderte <paramref name="access" /> für das angegebene Dateihandle wird durch das Betriebssystem nicht zugelassen. Dies ist z. B. der Fall, wenn <paramref name="access" /> oder Write für ReadWrite festgelegt sind und das Dateihandle auf schreibgeschützten Zugriff festgelegt ist. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.FileStream" />-Klasse mit dem angegebenen Pfad und dem angegebenen Erstellungsmodus.</summary>
      <param name="path">Ein relativer oder absoluter Pfad zu der Datei, die vom aktuellen FileStream-Objekt gekapselt wird. </param>
      <param name="mode">Eine Konstante, die bestimmt, auf welche Weise die Datei geöffnet oder erstellt werden soll. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine leere Zeichenfolge (""), die nur Leerraum oder mindestens ein ungültiges Zeichen enthält. - oder - <paramref name="path" /> verweist auf eine nicht-Datei-Gerät, wie z. B. "con:", "com1:", "lpt1:" usw..in einer NTFS-Umgebung.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> verweist auf eine nicht-Datei-Gerät, wie z. B. "con:", "com1:", "lpt1:" usw..in einer nicht-NTFS-Umgebung.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Die Datei kann nicht gefunden werden. Dies ist z. B. der Fall, wenn <paramref name="mode" /> auf FileMode.Truncate oder FileMode.Open festgelegt wurde und die durch <paramref name="path" /> angegebene Datei nicht vorhanden ist.Die Datei muss bereits in diesen Modi vorhanden sein.</exception>
      <exception cref="T:System.IO.IOException">Es ist ein E/A-Fehler aufgetreten, beispielsweise wurde FileMode.CreateNew angegeben, und die durch <paramref name="path" /> angegebene Datei ist bereits vorhanden.- oder - Der Stream wurde geschlossen. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig. Dies ist z. B. der Fall, wenn das Laufwerk des Pfads nicht zugeordnet ist. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> enthält einen ungültigen Wert. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.FileStream" />-Klasse mit den Angaben für den Pfad, den Erstellungsmodus und die Lese-/Schreibberechtigung.</summary>
      <param name="path">Ein relativer oder absoluter Pfad zu der Datei, die vom aktuellen FileStream-Objekt gekapselt wird. </param>
      <param name="mode">Eine Konstante, die bestimmt, auf welche Weise die Datei geöffnet oder erstellt werden soll. </param>
      <param name="access">Eine Konstante, die bestimmt, welcher Zugriff auf die Datei für das FileStream-Objekt zulässig ist.Dies bestimmt ebenfalls die von den <see cref="P:System.IO.FileStream.CanRead" />- und <see cref="P:System.IO.FileStream.CanWrite" />-Eigenschaften des FileStream-Objekts zurückgegebenen Werte.<see cref="P:System.IO.FileStream.CanSeek" /> ist true, wenn <paramref name="path" /> eine Datei auf einem Datenträger darstellt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine leere Zeichenfolge (""), die nur Leerraum oder mindestens ein ungültiges Zeichen enthält. - oder - <paramref name="path" /> verweist auf eine nicht-Datei-Gerät, wie z. B. "con:", "com1:", "lpt1:" usw..in einer NTFS-Umgebung.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> verweist auf eine nicht-Datei-Gerät, wie z. B. "con:", "com1:", "lpt1:" usw..in einer nicht-NTFS-Umgebung.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Die Datei kann nicht gefunden werden. Dies ist z. B. der Fall, wenn <paramref name="mode" /> auf FileMode.Truncate oder FileMode.Open festgelegt wurde und die durch <paramref name="path" /> angegebene Datei nicht vorhanden ist.Die Datei muss bereits in diesen Modi vorhanden sein.</exception>
      <exception cref="T:System.IO.IOException">Es ist ein E/A-Fehler aufgetreten, beispielsweise wurde FileMode.CreateNew angegeben, und die durch <paramref name="path" /> angegebene Datei ist bereits vorhanden. - oder - Der Stream wurde geschlossen.</exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig. Dies ist z. B. der Fall, wenn das Laufwerk des Pfads nicht zugeordnet ist. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der angeforderte <paramref name="access" /> für den angegebenen <paramref name="path" /> wird durch das Betriebssystem nicht zugelassen. Dies ist z. B. der Fall, wenn <paramref name="access" /> oder Write für ReadWrite festgelegt ist, die Datei bzw. das Verzeichnis jedoch schreibgeschützt ist. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> enthält einen ungültigen Wert. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.FileStream" />-Klasse mit den Angaben für den Pfad, den Erstellungsmodus, die Lese-/Schreib- und Freigabeberechtigung.</summary>
      <param name="path">Ein relativer oder absoluter Pfad zu der Datei, die vom aktuellen FileStream-Objekt gekapselt wird. </param>
      <param name="mode">Eine Konstante, die bestimmt, auf welche Weise die Datei geöffnet oder erstellt werden soll. </param>
      <param name="access">Eine Konstante, die bestimmt, welcher Zugriff auf die Datei für das FileStream-Objekt zulässig ist.Dies bestimmt ebenfalls die von den <see cref="P:System.IO.FileStream.CanRead" />- und <see cref="P:System.IO.FileStream.CanWrite" />-Eigenschaften des FileStream-Objekts zurückgegebenen Werte.<see cref="P:System.IO.FileStream.CanSeek" /> ist true, wenn <paramref name="path" /> eine Datei auf einem Datenträger darstellt.</param>
      <param name="share">Eine Konstante zur Bestimmung der Art, in der Prozesse gemeinsam auf die Datei zugreifen. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine leere Zeichenfolge (""), die nur Leerraum oder mindestens ein ungültiges Zeichen enthält. - oder - <paramref name="path" /> verweist auf eine nicht-Datei-Gerät, wie z. B. "con:", "com1:", "lpt1:" usw..in einer NTFS-Umgebung.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> verweist auf eine nicht-Datei-Gerät, wie z. B. "con:", "com1:", "lpt1:" usw..in einer nicht-NTFS-Umgebung.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Die Datei kann nicht gefunden werden. Dies ist z. B. der Fall, wenn <paramref name="mode" /> auf FileMode.Truncate oder FileMode.Open festgelegt wurde und die durch <paramref name="path" /> angegebene Datei nicht vorhanden ist.Die Datei muss bereits in diesen Modi vorhanden sein.</exception>
      <exception cref="T:System.IO.IOException">Es ist ein E/A-Fehler aufgetreten, beispielsweise wurde FileMode.CreateNew angegeben, und die durch <paramref name="path" /> angegebene Datei ist bereits vorhanden. - oder - Das System führt Windows 98 oder Windows 98 SE aus, und <paramref name="share" /> ist auf FileShare.Delete festgelegt.- oder - Der Stream wurde geschlossen.</exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig. Dies ist z. B. der Fall, wenn das Laufwerk des Pfads nicht zugeordnet ist. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der angeforderte <paramref name="access" /> für den angegebenen <paramref name="path" /> wird durch das Betriebssystem nicht zugelassen. Dies ist z. B. der Fall, wenn <paramref name="access" /> oder Write für ReadWrite festgelegt ist, die Datei bzw. das Verzeichnis jedoch schreibgeschützt ist. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> enthält einen ungültigen Wert. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.FileStream" />-Klasse mit den Angaben für den Pfad, den Erstellungsmodus, die Lese-/Schreib- und Freigabeberechtigung sowie die Puffergröße.</summary>
      <param name="path">Ein relativer oder absoluter Pfad zu der Datei, die vom aktuellen FileStream-Objekt gekapselt wird. </param>
      <param name="mode">Eine Konstante, die bestimmt, auf welche Weise die Datei geöffnet oder erstellt werden soll. </param>
      <param name="access">Eine Konstante, die bestimmt, welcher Zugriff auf die Datei für das FileStream-Objekt zulässig ist.Dies bestimmt ebenfalls die von den <see cref="P:System.IO.FileStream.CanRead" />- und <see cref="P:System.IO.FileStream.CanWrite" />-Eigenschaften des FileStream-Objekts zurückgegebenen Werte.<see cref="P:System.IO.FileStream.CanSeek" /> ist true, wenn <paramref name="path" /> eine Datei auf einem Datenträger darstellt.</param>
      <param name="share">Eine Konstante zur Bestimmung der Art, in der Prozesse gemeinsam auf die Datei zugreifen. </param>
      <param name="bufferSize">Ein positiver <see cref="T:System.Int32" />-Wert größer als 0 (null), der die Puffergröße angibt.Die Standardpuffergröße ist 4096.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine leere Zeichenfolge (""), die nur Leerraum oder mindestens ein ungültiges Zeichen enthält. - oder - <paramref name="path" /> verweist auf eine nicht-Datei-Gerät, wie z. B. "con:", "com1:", "lpt1:" usw..in einer NTFS-Umgebung.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> verweist auf eine nicht-Datei-Gerät, wie z. B. "con:", "com1:", "lpt1:" usw..in einer nicht-NTFS-Umgebung.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> ist negativ oder 0 (null).- oder -  <paramref name="mode" />, <paramref name="access" /> oder <paramref name="share" /> enthalten einen ungültigen Wert. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Die Datei kann nicht gefunden werden. Dies ist z. B. der Fall, wenn <paramref name="mode" /> auf FileMode.Truncate oder FileMode.Open festgelegt wurde und die durch <paramref name="path" /> angegebene Datei nicht vorhanden ist.Die Datei muss bereits in diesen Modi vorhanden sein.</exception>
      <exception cref="T:System.IO.IOException">Es ist ein E/A-Fehler aufgetreten, beispielsweise wurde FileMode.CreateNew angegeben, und die durch <paramref name="path" /> angegebene Datei ist bereits vorhanden. - oder - Das System führt Windows 98 oder Windows 98 SE aus, und <paramref name="share" /> ist auf FileShare.Delete festgelegt.- oder - Der Stream wurde geschlossen.</exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig. Dies ist z. B. der Fall, wenn das Laufwerk des Pfads nicht zugeordnet ist. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der angeforderte <paramref name="access" /> für den angegebenen <paramref name="path" /> wird durch das Betriebssystem nicht zugelassen. Dies ist z. B. der Fall, wenn <paramref name="access" /> oder Write für ReadWrite festgelegt ist, die Datei bzw. das Verzeichnis jedoch schreibgeschützt ist. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Int32,System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.FileStream" />-Klasse mit den Angaben für den Pfad, den Erstellungsmodus, die Lese-/Schreib- und Freigabeberechtigung, die Puffergröße und den synchronen bzw. asynchronen Zustand.</summary>
      <param name="path">Ein relativer oder absoluter Pfad zu der Datei, die vom aktuellen FileStream-Objekt gekapselt wird. </param>
      <param name="mode">Eine Konstante, die bestimmt, auf welche Weise die Datei geöffnet oder erstellt werden soll. </param>
      <param name="access">Eine Konstante, die bestimmt, welcher Zugriff auf die Datei für das FileStream-Objekt zulässig ist.Dies bestimmt ebenfalls die von den <see cref="P:System.IO.FileStream.CanRead" />- und <see cref="P:System.IO.FileStream.CanWrite" />-Eigenschaften des FileStream-Objekts zurückgegebenen Werte.<see cref="P:System.IO.FileStream.CanSeek" /> ist true, wenn <paramref name="path" /> eine Datei auf einem Datenträger darstellt.</param>
      <param name="share">Eine Konstante zur Bestimmung der Art, in der Prozesse gemeinsam auf die Datei zugreifen. </param>
      <param name="bufferSize">Ein positiver <see cref="T:System.Int32" />-Wert größer als 0 (null), der die Puffergröße angibt.Die Standardpuffergröße ist 4096.</param>
      <param name="useAsync">Gibt an, ob eine asynchrone E/A oder eine synchrone E/A verwendet wird.Beachten Sie jedoch, dass das zugrunde liegende Betriebssystem möglicherweise keine asynchrone E/A unterstützt, sodass das Handle je nach Plattform auch bei der Angabe von true möglicherweise synchron geöffnet wird.Bei asynchronem Öffnen liefern die <see cref="M:System.IO.FileStream.BeginRead(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)" />-Methode und die <see cref="M:System.IO.FileStream.BeginWrite(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)" />-Methode bei umfangreichen Lese- oder Schreibvorgängen eine höhere Leistung, sind aber möglicherweise bei kleinen Lese- und Schreibvorgängen wesentlich langsamer.Wenn die Anwendung die Vorteile der asynchronen E/A nutzen kann, legen Sie den <paramref name="useAsync" />-Parameter auf true fest.Wird die asynchrone E/A richtig eingesetzt, können Anwendungen um ein Zehnfaches beschleunigt werden. Wenn die Anwendung jedoch nicht an eine asynchrone E/A angepasst wurde, kann dies die Leistung auch um das Zehnfache verringern.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine leere Zeichenfolge (""), die nur Leerraum oder mindestens ein ungültiges Zeichen enthält. - oder - <paramref name="path" /> verweist auf eine nicht-Datei-Gerät, wie z. B. "con:", "com1:", "lpt1:" usw..in einer NTFS-Umgebung.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> verweist auf eine nicht-Datei-Gerät, wie z. B. "con:", "com1:", "lpt1:" usw..in einer nicht-NTFS-Umgebung.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> ist negativ oder 0 (null).- oder -  <paramref name="mode" />, <paramref name="access" /> oder <paramref name="share" /> enthalten einen ungültigen Wert. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Die Datei kann nicht gefunden werden. Dies ist z. B. der Fall, wenn <paramref name="mode" /> auf FileMode.Truncate oder FileMode.Open festgelegt wurde und die durch <paramref name="path" /> angegebene Datei nicht vorhanden ist.Die Datei muss bereits in diesen Modi vorhanden sein.</exception>
      <exception cref="T:System.IO.IOException">Es ist ein E/A-Fehler aufgetreten, beispielsweise wurde FileMode.CreateNew angegeben, und die durch <paramref name="path" /> angegebene Datei ist bereits vorhanden.- oder -  Das System führt Windows 98 oder Windows 98 SE aus, und <paramref name="share" /> ist auf FileShare.Delete festgelegt.- oder - Der Stream wurde geschlossen.</exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig. Dies ist z. B. der Fall, wenn das Laufwerk des Pfads nicht zugeordnet ist. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der angeforderte <paramref name="access" /> für den angegebenen <paramref name="path" /> wird durch das Betriebssystem nicht zugelassen. Dies ist z. B. der Fall, wenn <paramref name="access" /> oder Write für ReadWrite festgelegt ist, die Datei bzw. das Verzeichnis jedoch schreibgeschützt ist. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Int32,System.IO.FileOptions)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.FileStream" />-Klasse mit den Angaben für den Pfad, den Erstellungsmodus, die Lese-/Schreib- und Freigabeberechtigung, die Zugriffsmöglichkeiten anderer FileStreams auf die gleiche Datei, die Puffergröße und zusätzliche Dateioptionen.</summary>
      <param name="path">Ein relativer oder absoluter Pfad zu der Datei, die vom aktuellen FileStream-Objekt gekapselt wird. </param>
      <param name="mode">Eine Konstante, die bestimmt, auf welche Weise die Datei geöffnet oder erstellt werden soll. </param>
      <param name="access">Eine Konstante, die bestimmt, welcher Zugriff auf die Datei für das FileStream-Objekt zulässig ist.Dies bestimmt ebenfalls die von den <see cref="P:System.IO.FileStream.CanRead" />- und <see cref="P:System.IO.FileStream.CanWrite" />-Eigenschaften des FileStream-Objekts zurückgegebenen Werte.<see cref="P:System.IO.FileStream.CanSeek" /> ist true, wenn <paramref name="path" /> eine Datei auf einem Datenträger darstellt.</param>
      <param name="share">Eine Konstante zur Bestimmung der Art, in der Prozesse gemeinsam auf die Datei zugreifen. </param>
      <param name="bufferSize">Ein positiver <see cref="T:System.Int32" />-Wert größer als 0 (null), der die Puffergröße angibt.Die Standardpuffergröße ist 4096.</param>
      <param name="options">Ein Wert, der zusätzliche Dateioptionen angibt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine leere Zeichenfolge (""), die nur Leerraum oder mindestens ein ungültiges Zeichen enthält. - oder - <paramref name="path" /> verweist auf eine nicht-Datei-Gerät, wie z. B. "con:", "com1:", "lpt1:" usw..in einer NTFS-Umgebung.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> verweist auf eine nicht-Datei-Gerät, wie z. B. "con:", "com1:", "lpt1:" usw..in einer nicht-NTFS-Umgebung.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> ist negativ oder 0 (null).- oder -  <paramref name="mode" />, <paramref name="access" /> oder <paramref name="share" /> enthalten einen ungültigen Wert. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Die Datei kann nicht gefunden werden. Dies ist z. B. der Fall, wenn <paramref name="mode" /> auf FileMode.Truncate oder FileMode.Open festgelegt wurde und die durch <paramref name="path" /> angegebene Datei nicht vorhanden ist.Die Datei muss bereits in diesen Modi vorhanden sein.</exception>
      <exception cref="T:System.IO.IOException">Es ist ein E/A-Fehler aufgetreten, beispielsweise wurde FileMode.CreateNew angegeben, und die durch <paramref name="path" /> angegebene Datei ist bereits vorhanden.- oder - Der Stream wurde geschlossen.</exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig. Dies ist z. B. der Fall, wenn das Laufwerk des Pfads nicht zugeordnet ist. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Der angeforderte <paramref name="access" /> für den angegebenen <paramref name="path" /> wird durch das Betriebssystem nicht zugelassen. Dies ist z. B. der Fall, wenn <paramref name="access" /> oder Write für ReadWrite festgelegt ist, die Datei bzw. das Verzeichnis jedoch schreibgeschützt ist. - oder - <see cref="F:System.IO.FileOptions.Encrypted" /> wurde für <paramref name="options" /> angegeben, aber die Dateiverschlüsselung wird auf der aktuellen Plattform nicht unterstützt.</exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
    </member>
    <member name="P:System.IO.FileStream.CanRead">
      <summary>Ruft einen Wert ab, der angibt, ob der aktuelle Stream Lesevorgänge unterstützt.</summary>
      <returns>true, wenn der Stream Lesevorgänge unterstützt, false, wenn der Stream geschlossen ist oder ausschließlich mit Schreibzugriff geöffnet wurde.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.CanSeek">
      <summary>Ruft einen Wert ab, der angibt, ob der aktuelle Stream Suchvorgänge unterstützt.</summary>
      <returns>true, wenn der Stream Suchvorgänge unterstützt, false, wenn der Stream geschlossen ist oder der FileStream von einem Betriebssystemhandle, z. B. einer Pipe oder einer Ausgabe in der Konsole, erstellt wurde.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.CanWrite">
      <summary>Ruft einen Wert ab, der angibt, ob der aktuelle Stream Schreibvorgänge unterstützt.</summary>
      <returns>true, wenn der Stream Schreibvorgänge unterstützt, false, wenn der Stream geschlossen ist oder mit schreibgeschütztem Zugriff geöffnet wurde.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Dispose(System.Boolean)">
      <summary>Gibt die von <see cref="T:System.IO.FileStream" /> verwendeten nicht verwalteten Ressourcen und optional die verwalteten Ressourcen frei.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben, false, um ausschließlich nicht verwaltete Ressourcen freizugeben. </param>
    </member>
    <member name="M:System.IO.FileStream.Finalize">
      <summary>Stellt das Freigeben von Ressourcen und das Ausführen anderer Bereinigungsvorgänge sicher, wenn der Garbage Collector den FileStream verarbeitet.</summary>
    </member>
    <member name="M:System.IO.FileStream.Flush">
      <summary>Löscht die Puffer für diesen Datenstrom und veranlasst die Ausgabe aller gepufferten Daten in die Datei.</summary>
      <exception cref="T:System.IO.IOException">Ein E/A-Fehler ist aufgetreten. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Flush(System.Boolean)">
      <summary>Löscht die Puffer für diesen Datenstrom, veranlasst die Ausgabe aller gepufferten Daten in die Datei und löscht zudem alle Zwischendateipuffer.</summary>
      <param name="flushToDisk">true, um alle Zwischendateipuffer zu leeren, andernfalls false. </param>
    </member>
    <member name="M:System.IO.FileStream.FlushAsync(System.Threading.CancellationToken)">
      <summary>Löscht alle Puffer für diesen Stream asynchron, veranlasst die Ausgabe aller gepufferten Daten an das zugrunde liegende Gerät und überwacht Abbruchanforderungen. </summary>
      <returns>Eine Aufgabe, die die asynchrone Leerung darstellt. </returns>
      <param name="cancellationToken">Das Token zum überwachen von Abbruchanforderungen .</param>
      <exception cref="T:System.ObjectDisposedException">Der Stream wurde freigegeben.</exception>
    </member>
    <member name="P:System.IO.FileStream.IsAsync">
      <summary>Ruft einen Wert ab, der angibt, ob der FileStream asynchron oder synchron geöffnet wurde.</summary>
      <returns>true, wenn FileStream asynchron geöffnet wurde, andernfalls false.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.Length">
      <summary>Ruft die Länge des Streams in Bytes ab.</summary>
      <returns>Ein Long-Wert, der die Länge des Streams in Bytes darstellt.</returns>
      <exception cref="T:System.NotSupportedException">
        <see cref="P:System.IO.FileStream.CanSeek" /> ist für diesen Stream false. </exception>
      <exception cref="T:System.IO.IOException">Es ist ein E/A-Fehler aufgetreten, beispielsweise das Schließen der Datei. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.Name">
      <summary>Ruft den Namen des FileStream ab, der an den Konstruktor übergeben wurde.</summary>
      <returns>Eine Zeichenfolge, die den Namen des FileStream darstellt.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileStream.Position">
      <summary>Ruft die aktuelle Position dieses Streams ab oder legt diese fest.</summary>
      <returns>Die aktuelle Position dieses Streams.</returns>
      <exception cref="T:System.NotSupportedException">Der Stream unterstützt keine Suchvorgänge. </exception>
      <exception cref="T:System.IO.IOException">Ein E/A-Fehler ist aufgetreten. - oder -In Windows 98 oder früher wurde die Position auf einen sehr großen Wert nach dem Ende des Streams festgelegt.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Es wurde versucht, die Position auf einen negativen Wert festzulegen. </exception>
      <exception cref="T:System.IO.EndOfStreamException">Es wurde versucht, eine Suche über ein Ende eines Streams hinaus auszuführen, der diesen Vorgang nicht unterstützt. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>Liest einen Byteblock aus dem Stream und schreibt die Daten in einen angegebenen Puffer.</summary>
      <returns>Die Gesamtanzahl der in den Puffer gelesenen Bytes.Dies kann weniger als die Anzahl der angeforderten Bytes sein, wenn diese Anzahl an Bytes derzeit nicht verfügbar ist, oder 0, wenn das Streamende erreicht ist.</returns>
      <param name="array">Enthält nach dem Beenden dieser Methode das angegebene Bytearray mit den Werten zwischen <paramref name="offset" /> und (<paramref name="offset" /> + <paramref name="count" /> - 1<paramref name=")" />, die durch die aus der aktuellen Quelle gelesenen Bytes ersetzt wurden. </param>
      <param name="offset">Das Byteoffset in <paramref name="array" />, an dem die gelesenen Bytes platziert werden. </param>
      <param name="count">Die maximale Anzahl der zu lesenden Bytes. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> oder <paramref name="count" /> ist negativ. </exception>
      <exception cref="T:System.NotSupportedException">Lesevorgänge werden vom Stream nicht unterstützt. </exception>
      <exception cref="T:System.IO.IOException">Ein E/A-Fehler ist aufgetreten. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> und <paramref name="count" /> bezeichnen einen ungültigen Bereich in <paramref name="array" />. </exception>
      <exception cref="T:System.ObjectDisposedException">Es wurden Methoden aufgerufen, nachdem der Stream geschlossen wurde. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.ReadAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>Liest eine Folge von Bytes asynchron aus dem aktuellen Stream, erhöht die Position im Stream um die Anzahl der gelesenen Bytes und überwacht Abbruchanfragen.</summary>
      <returns>Eine Aufgabe, die den asynchronen Lesevorgang darstellt.Der Wert des <paramref name="TResult" />-Parameters enthält die Gesamtzahl der Bytes, die in den Puffer gelesen werden.Der Ergebniswert kann niedriger als die Anzahl der angeforderten Bytes sein, wenn die Anzahl an derzeit verfügbaren Bytes kleiner ist als die angeforderte Anzahl, oder sie kann 0 (null) sein, wenn das Datenstromende erreicht ist.</returns>
      <param name="buffer">Der Puffer, in den die Daten geschrieben werden sollen.</param>
      <param name="offset">Der Byteoffset im <paramref name="buffer" />, ab dem Daten aus dem Stream geschrieben werden.</param>
      <param name="count">Die maximale Anzahl der zu lesenden Bytes.</param>
      <param name="cancellationToken">Das Token zum überwachen von Abbruchanforderungen .</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> oder <paramref name="count" /> ist negativ.</exception>
      <exception cref="T:System.ArgumentException">Die Summe aus <paramref name="offset" /> und <paramref name="count" /> ist größer als die Pufferlänge.</exception>
      <exception cref="T:System.NotSupportedException">Lesevorgänge werden vom Stream nicht unterstützt.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream wurde freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Stream wird gerade durch einen früheren Lesevorgang. </exception>
    </member>
    <member name="M:System.IO.FileStream.ReadByte">
      <summary>Liest ein Byte aus der Datei und erhöht die Leseposition um ein Byte.</summary>
      <returns>Das Byte, das in <see cref="T:System.Int32" /> umgewandelt wurde, oder -1, wenn das Ende des Streams erreicht wurde.</returns>
      <exception cref="T:System.NotSupportedException">Der aktuelle Stream unterstützt keine Lesevorgänge. </exception>
      <exception cref="T:System.ObjectDisposedException">Der aktuelle Stream ist geschlossen. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.SafeFileHandle">
      <summary>Ruft ein <see cref="T:Microsoft.Win32.SafeHandles.SafeFileHandle" />-Objekt ab, das das Dateihandle des Betriebssystems für die Datei darstellt, die vom aktuellen <see cref="T:System.IO.FileStream" />-Objekt gekapselt wird.</summary>
      <returns>Ein Objekt, das das Dateihandle des Betriebssystems für die Datei darstellt, von der das aktuelle <see cref="T:System.IO.FileStream" />-Objekt gekapselt wird.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Seek(System.Int64,System.IO.SeekOrigin)">
      <summary>Legt die aktuelle Position dieses Streams auf den angegebenen Wert fest.</summary>
      <returns>Die neue Position im Stream.</returns>
      <param name="offset">Der Punkt relativ zu <paramref name="origin" />, ab dem gesucht werden soll. </param>
      <param name="origin">Bestimmt den Anfang, das Ende oder die aktuelle Position als Bezugspunkt für <paramref name="offset" /> unter Verwendung eines Werts vom Typ <see cref="T:System.IO.SeekOrigin" />. </param>
      <exception cref="T:System.IO.IOException">Ein E/A-Fehler ist aufgetreten. </exception>
      <exception cref="T:System.NotSupportedException">Der Stream unterstützt keine Suchvorgänge. Dies ist beispielsweise der Fall, wenn FileStream aus einer Pipe- oder Konsolenausgabe erstellt wird. </exception>
      <exception cref="T:System.ArgumentException">Es wurde versucht, eine Suche vor dem Anfang des Streams auszuführen. </exception>
      <exception cref="T:System.ObjectDisposedException">Es wurden Methoden aufgerufen, nachdem der Stream geschlossen wurde. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.SetLength(System.Int64)">
      <summary>Legt die Länge dieses Streams auf den angegebenen Wert fest.</summary>
      <param name="value">Die neue Länge des Streams. </param>
      <exception cref="T:System.IO.IOException">Ein E/A-Fehler ist aufgetreten. </exception>
      <exception cref="T:System.NotSupportedException">Der Stream unterstützt weder Schreib- noch Suchvorgänge. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Es wurde versucht, den <paramref name="value" />-Parameter auf einen kleineren Wert als 0 festzulegen. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>Schreibt einen Block von Bytes in den Dateistream.</summary>
      <param name="array">Der Puffer mit den Daten, die in den Stream geschrieben werden sollen.</param>
      <param name="offset">Der nullbasierte Byteoffset im <paramref name="array" />, ab dem Bytes in den Stream kopiert werden. </param>
      <param name="count">Die maximale Anzahl der zu schreibenden Bytes. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> und <paramref name="count" /> bezeichnen einen ungültigen Bereich in <paramref name="array" />. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> oder <paramref name="count" /> ist negativ. </exception>
      <exception cref="T:System.IO.IOException">Ein E/A-Fehler ist aufgetreten. - oder -Ein anderer Thread hat ggf. eine unerwartete Änderung an der Position des vom Betriebssystem verwendeten Dateihandles bewirkt. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <exception cref="T:System.NotSupportedException">Die aktuelle Instanz des Streams unterstützt keine Schreibvorgänge. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.WriteAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>Schreibt beim Überschreiben in einer abgeleiteten Klasse eine Folge von Bytes asynchron in den aktuellen Stream und erhöht die aktuelle Position im Stream um die Anzahl der geschriebenen Bytes und überwacht Abbruchanforderungen. </summary>
      <returns>Eine Aufgabe, die den asynchronen Schreibvorgang darstellt.</returns>
      <param name="buffer">Der Puffer, aus dem Daten geschrieben werden sollen. </param>
      <param name="offset">Der nullbasierte Byteoffset im <paramref name="buffer" />, ab dem Bytes in den Stream kopiert werden.</param>
      <param name="count">Die maximale Anzahl der zu schreibenden Bytes.</param>
      <param name="cancellationToken">Das Token zum überwachen von Abbruchanforderungen .</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> oder <paramref name="count" /> ist negativ.</exception>
      <exception cref="T:System.ArgumentException">Die Summe aus <paramref name="offset" /> und <paramref name="count" /> ist größer als die Pufferlänge.</exception>
      <exception cref="T:System.NotSupportedException">Der Stream unterstützt das Schreiben nicht.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream wurde freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Stream wird derzeit von einem vorherigen Schreibvorgang verwendet. </exception>
    </member>
    <member name="M:System.IO.FileStream.WriteByte(System.Byte)">
      <summary>Schreibt ein Byte an die aktuelle Position im Dateistream.</summary>
      <param name="value">Ein Byte, das in den Stream geschrieben werden soll. </param>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <exception cref="T:System.NotSupportedException">Der Stream unterstützt das Schreiben nicht. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.FileSystemInfo">
      <summary>Stellt die Basisklasse sowohl für <see cref="T:System.IO.FileInfo" />-Objekte als auch für <see cref="T:System.IO.DirectoryInfo" />-Objekte bereit.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.FileSystemInfo.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.FileSystemInfo" />-Klasse.</summary>
    </member>
    <member name="P:System.IO.FileSystemInfo.Attributes">
      <summary>Ruft die Attribute für die aktuelle Datei oder das aktuelle Verzeichnis ab oder legt diese fest.</summary>
      <returns>
        <see cref="T:System.IO.FileAttributes" /> der aktuellen <see cref="T:System.IO.FileSystemInfo" />.</returns>
      <exception cref="T:System.IO.FileNotFoundException">Die angegebene Datei ist nicht vorhanden. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig. Zum Beispiel befindet er sich auf einem nicht zugeordneten Laufwerk. </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentException">Der Aufrufer versucht, ein ungültiges Dateiattribut festzulegen. - oder - Die Benutzer versucht, einen Attributwert festzulegen, verfügt jedoch nicht über Schreibberechtigungen.</exception>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> kann die Daten nicht initialisieren. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.CreationTime">
      <summary>Ruft den Erstellungszeitpunkt der aktuellen Datei oder des aktuellen Verzeichnisses ab oder legt diesen fest.</summary>
      <returns>Das Erstellungsdatum und die Erstellungszeit des aktuellen <see cref="T:System.IO.FileSystemInfo" />-Objekts.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> kann die Daten nicht initialisieren. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig. Zum Beispiel befindet er sich auf einem nicht zugeordneten Laufwerk.</exception>
      <exception cref="T:System.PlatformNotSupportedException">Das aktuelle Betriebssystem ist nicht Windows NT oder höher.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der Aufrufer versucht, eine ungültige Erstellungszeit festzulegen.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.CreationTimeUtc">
      <summary>Ruft die Erstellungszeit der aktuellen Datei oder des aktuellen Verzeichnisses im UTC-Format (Coordinated Universal Time) ab oder legt diese fest.</summary>
      <returns>Das Erstellungsdatum und die Erstellungszeit (im UTC-Format) des aktuellen <see cref="T:System.IO.FileSystemInfo" />-Objekts.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> kann die Daten nicht initialisieren. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig. Zum Beispiel befindet er sich auf einem nicht zugeordneten Laufwerk.</exception>
      <exception cref="T:System.PlatformNotSupportedException">Das aktuelle Betriebssystem ist nicht Windows NT oder höher.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der Aufrufer versucht, eine ungültige Zugriffszeit festzulegen.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileSystemInfo.Delete">
      <summary>Löscht eine Datei oder ein Verzeichnis.</summary>
      <exception cref="T:System.IO.DirectoryNotFoundException">Der angegebene Pfad ist ungültig. Zum Beispiel befindet er sich auf einem nicht zugeordneten Laufwerk.</exception>
      <exception cref="T:System.IO.IOException">Für die Datei oder das Verzeichnis ist ein geöffnetes Handle vorhanden, und das Betriebssystem ist Windows XP oder früher.Dieses geöffnete Handle kann aus der Auflistung von Verzeichnissen und Dateien entstanden sein.Weitere Informationen finden Sie unter Gewusst wie: Auflisten von Verzeichnissen und Dateien.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.FileSystemInfo.Exists">
      <summary>Ruft einen Wert ab, der angibt, ob die Datei oder das Verzeichnis vorhanden ist.</summary>
      <returns>true, wenn die Datei oder das Verzeichnis vorhanden ist, andernfalls false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileSystemInfo.Extension">
      <summary>Ruft die Zeichenfolge ab, die den Erweiterungsteil der Datei darstellt.</summary>
      <returns>Eine Zeichenfolge, die die <see cref="T:System.IO.FileSystemInfo" />-Erweiterung enthält.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileSystemInfo.FullName">
      <summary>Ruft den vollständigen Pfad des Verzeichnisses oder der Datei ab.</summary>
      <returns>Eine Zeichenfolge mit dem vollständigen Pfad.</returns>
      <exception cref="T:System.IO.PathTooLongException">Der vollqualifizierte Pfad und der Dateiname sind mindestens 260 Zeichen lang.</exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="F:System.IO.FileSystemInfo.FullPath">
      <summary>Stellt den vollqualifizierten Pfad des Verzeichnisses oder der Datei dar.</summary>
      <exception cref="T:System.IO.PathTooLongException">Der vollqualifizierte Pfad ist 260 oder mehr Zeichen.</exception>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastAccessTime">
      <summary>Ruft den Zeitpunkt des letzten Zugriffs auf die aktuelle Datei oder das aktuelle Verzeichnis ab oder legt diesen fest.</summary>
      <returns>Der Zeitpunkt des letzten Zugriffs auf die aktuelle Datei oder das aktuelle Verzeichnis.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> kann die Daten nicht initialisieren. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Das aktuelle Betriebssystem ist nicht Windows NT oder höher.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der Aufrufer versucht, eine ungültige Zugriffszeit festzulegen.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastAccessTimeUtc">
      <summary>Ruft den Zeitpunkt des letzten Zugriffs auf die aktuelle Datei bzw. das aktuelle Verzeichnis im UTC-Format (Coordinated Universal Time) ab oder legt diesen fest.</summary>
      <returns>Die UTC-Zeit des letzten Zugriffs auf die aktuelle Datei oder das aktuelle Verzeichnis.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> kann die Daten nicht initialisieren. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Das aktuelle Betriebssystem ist nicht Windows NT oder höher.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der Aufrufer versucht, eine ungültige Zugriffszeit festzulegen.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastWriteTime">
      <summary>Ruft den Zeitpunkt des letzten Schreibzugriffs auf die aktuelle Datei oder das aktuelle Verzeichnis ab oder legt diesen fest.</summary>
      <returns>Der Zeitpunkt des letzten Schreibzugriffs auf die Datei.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> kann die Daten nicht initialisieren. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Das aktuelle Betriebssystem ist nicht Windows NT oder höher.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der Aufrufer versucht, eine ungültige Zeit für den Schreibvorgang festzulegen.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastWriteTimeUtc">
      <summary>Ruft den Zeitpunkt des letzten Schreibens in die aktuelle Datei oder das aktuelle Verzeichnis im UTC-Format (Coordinated Universal Time) ab oder legt diesen fest.</summary>
      <returns>Die UTC-Zeit des letzten Schreibzugriffs auf die aktuelle Datei.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> kann die Daten nicht initialisieren. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Das aktuelle Betriebssystem ist nicht Windows NT oder höher.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der Aufrufer versucht, eine ungültige Zeit für den Schreibvorgang festzulegen.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.Name">
      <summary>Ruft bei Dateien den Namen der Datei ab.Ruft bei Verzeichnissen den Namen des letzten Verzeichnisses in der Hierarchie ab, sofern eine Hierarchie vorhanden ist.Andernfalls ruft die Name-Eigenschaft den Namen des Verzeichnisses ab.</summary>
      <returns>Eine Zeichenfolge, die den Namen des übergeordneten Verzeichnisses, den Namen des letzten Verzeichnisses in der Hierarchie oder den Namen einer Datei, einschließlich der Dateinamenerweiterung, darstellt.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.FileSystemInfo.OriginalPath">
      <summary>Der ursprünglich vom Benutzer angegebene Pfad, relativ oder absolut.</summary>
    </member>
    <member name="M:System.IO.FileSystemInfo.Refresh">
      <summary>Aktualisiert den Zustand des Objekts.</summary>
      <exception cref="T:System.IO.IOException">Ein Gerät, z. B. ein Laufwerk, ist nicht verfügbar. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.SearchOption">
      <summary>Gibt an, ob das aktuelle Verzeichnis oder das aktuelle Verzeichnis und alle Unterverzeichnisse durchsucht werden sollen. </summary>
    </member>
    <member name="F:System.IO.SearchOption.AllDirectories">
      <summary>Schließt das aktuelle Verzeichnis und alle Unterverzeichnisse in einen Suchvorgang ein.Diese Option schließt Punkte für die erneute Analyse wie bereitgestellte Laufwerke und symbolische Links in die Suche ein.</summary>
    </member>
    <member name="F:System.IO.SearchOption.TopDirectoryOnly">
      <summary>Schließt nur das aktuelle Verzeichnis in den Suchevorgang ein.</summary>
    </member>
  </members>
</doc>