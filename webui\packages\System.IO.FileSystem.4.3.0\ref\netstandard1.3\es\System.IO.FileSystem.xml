﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.IO.FileSystem</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Win32.SafeHandles.SafeFileHandle">
      <summary>Representa una clase contenedora de un identificador de archivo. </summary>
    </member>
    <member name="M:Microsoft.Win32.SafeHandles.SafeFileHandle.#ctor(System.IntPtr,System.Boolean)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:Microsoft.Win32.SafeHandles.SafeFileHandle" />. </summary>
      <param name="preexistingHandle">Objeto <see cref="T:System.IntPtr" /> que representa el identificador preexistente que se va a utilizar.</param>
      <param name="ownsHandle">Se establece en true para liberar de forma confiable el identificador durante la fase de finalización; se establece en false para impedir que se libere de forma confiable (no se recomienda).</param>
    </member>
    <member name="P:Microsoft.Win32.SafeHandles.SafeFileHandle.IsInvalid"></member>
    <member name="T:System.IO.Directory">
      <summary>Expone métodos estáticos para crear, mover y enumerar archivos en directorios y subdirectorios.Esta clase no puede heredarse.Para examinar el código fuente de .NET Framework para este tipo, consulte el fuente de referencia de.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Directory.CreateDirectory(System.String)">
      <summary>Crea todos los directorios y subdirectorios en la ruta de acceso especificada, a menos que ya existan.</summary>
      <returns>Un objeto que representa el directorio en la ruta de acceso especificada.Este objeto se devuelve sin importar si ya existe un directorio en la ruta especificada.</returns>
      <param name="path">Directorio que se va a crear. </param>
      <exception cref="T:System.IO.IOException">El directorio especificado por <paramref name="path" /> es un archivo.o bienEl nombre de la red no se conoce.</exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero que contiene solo espacios en blanco o uno o más caracteres no válidos.Puede consultar caracteres no válidos usando el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.o bien<paramref name="path" /> lleva como prefijo, o contiene, solo un carácter de dos puntos (:).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> contiene un carácter de dos puntos (:) que no forma parte de una etiqueta de la unidad de disco ("C:\\").</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.Delete(System.String)">
      <summary>Elimina un directorio vacío de una ruta de acceso especificada.</summary>
      <param name="path">Nombre del directorio vacío que se va a quitar.El directorio debe permitir la escritura y estar vacío.</param>
      <exception cref="T:System.IO.IOException">Ya existe un archivo con el mismo nombre y ubicación especificados por <paramref name="path" />.o bienEl directorio es el directorio de trabajo actual de la aplicación.o bienEl directorio especificado por <paramref name="path" /> no está vacío.o bienEl directorio es de solo lectura o contiene un archivo de solo lectura.o bienOtro proceso está utilizando el directorio.</exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero que contiene solo espacios en blanco o uno o más caracteres no válidos.Puede consultar caracteres no válidos usando el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> no existe o no se encuentra.o bienLa ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.Delete(System.String,System.Boolean)">
      <summary>Elimina el directorio especificado y, si está indicado, los subdirectorios y archivos que contiene. </summary>
      <param name="path">Nombre del directorio que se va a quitar. </param>
      <param name="recursive">true para quitar directorios, subdirectorios y archivos de <paramref name="path" />; de lo contrario, false. </param>
      <exception cref="T:System.IO.IOException">Ya existe un archivo con el mismo nombre y ubicación especificados por <paramref name="path" />.o bienEl directorio especificado por <paramref name="path" /> es de solo lectura o <paramref name="recursive" /> es false y <paramref name="path" /> no es un directorio vacío. o bienEl directorio es el directorio de trabajo actual de la aplicación. o bienEl directorio contiene un archivo de solo lectura.o bienOtro proceso está utilizando el directorio.</exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero que contiene solo espacios en blanco o uno o más caracteres no válidos.Puede consultar caracteres no válidos usando el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> no existe o no se encuentra.o bienLa ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String)">
      <summary>Devuelve una colección enumerable de nombres de directorio en una ruta de acceso especificada.</summary>
      <returns>Una colección enumerable de nombres completos (con sus rutas de acceso) para los directorios en el directorio especificado por <paramref name="path" />.</returns>
      <param name="path">La ruta de acceso absoluta o relativa al directorio que se va a buscar.Esta cadena no distingue entre mayúsculas y minúsculas.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " /> es una cadena de longitud cero, que contiene solo un espacio en blanco, o que contiene uno o más caracteres no válidos.Puede consultar caracteres no válidos usando el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">El valor de <paramref name="path" /> no es válido; por ejemplo, hace referencia a una unidad no asignada. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> es un nombre de archivo.</exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo especificado o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String,System.String)">
      <summary>Devuelve una colección enumerable de nombres de directorio que coinciden con un patrón de búsqueda en una ruta de acceso especificada.</summary>
      <returns>Una colección enumerable de nombres completos (con sus rutas de acceso) para los directorios en el directorio especificado por <paramref name="path" /> y que coinciden con el patrón de búsqueda especificado.</returns>
      <param name="path">La ruta de acceso absoluta o relativa al directorio que se va a buscar.Esta cadena no distingue entre mayúsculas y minúsculas.</param>
      <param name="searchPattern">Cadena de búsqueda que debe coincidir con los nombres de los directorios de <paramref name="path" />.Este parámetro puede contener una combinación de ruta de acceso literal y caracteres comodín (* y ?) válidos (vea Comentarios), pero no es compatible con las expresiones regulares.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " /> es una cadena de longitud cero, que contiene solo un espacio en blanco, o que contiene uno o más caracteres no válidos.Puede consultar los caracteres no válidos con el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.o bien<paramref name="searchPattern" /> no contiene un modelo válido.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null.o bien<paramref name="searchPattern" />is null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">El valor de <paramref name="path" /> no es válido; por ejemplo, hace referencia a una unidad no asignada. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> es un nombre de archivo.</exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo especificado o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String,System.String,System.IO.SearchOption)">
      <summary>Devuelve una colección enumerable de nombres de directorio que coinciden con un patrón de búsqueda en una ruta de acceso especificada y, opcionalmente, busca en subdirectorios.</summary>
      <returns>Una colección enumerable de nombres completos (con sus rutas de acceso) para los directorios en el directorio especificado por <paramref name="path" /> y que coinciden con el patrón de búsqueda y opción especificados.</returns>
      <param name="path">La ruta de acceso absoluta o relativa al directorio que se va a buscar.Esta cadena no distingue entre mayúsculas y minúsculas.</param>
      <param name="searchPattern">Cadena de búsqueda que debe coincidir con los nombres de los directorios de <paramref name="path" />.Este parámetro puede contener una combinación de ruta de acceso literal y caracteres comodín (* y ?) válidos (vea Comentarios), pero no es compatible con las expresiones regulares.</param>
      <param name="searchOption">Uno de los valores de enumeración que especifica si la operación de búsqueda debe incluir solo el directorio actual o debe incluir todos los subdirectorios.El valor predeterminado es <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " /> es una cadena de longitud cero, que contiene solo un espacio en blanco, o que contiene uno o más caracteres no válidos.Puede consultar caracteres no válidos usando el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.o bien<paramref name="searchPattern" /> no contiene un modelo válido.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null.o bien<paramref name="searchPattern" />is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> no es un valor válido de <see cref="T:System.IO.SearchOption" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">El valor de <paramref name="path" /> no es válido; por ejemplo, hace referencia a una unidad no asignada. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> es un nombre de archivo.</exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo especificado o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String)">
      <summary>Devuelve una colección enumerable de nombres de archivo en una ruta de acceso especificada.</summary>
      <returns>Una colección enumerable de nombres completos (con sus rutas de acceso) para los archivos en el directorio especificado por <paramref name="path" />.</returns>
      <param name="path">La ruta de acceso absoluta o relativa al directorio que se va a buscar.Esta cadena no distingue entre mayúsculas y minúsculas.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " /> es una cadena de longitud cero, que contiene solo un espacio en blanco, o que contiene uno o más caracteres no válidos.Puede consultar caracteres no válidos usando el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">El valor de <paramref name="path" /> no es válido; por ejemplo, hace referencia a una unidad no asignada. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> es un nombre de archivo.</exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo especificado o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String,System.String)">
      <summary>Devuelve una colección enumerable de nombres de archivo que coinciden con un patrón de búsqueda en una ruta de acceso especificada.</summary>
      <returns>Una colección enumerable de nombres completos (con sus rutas de acceso) para los archivos en el directorio especificado por <paramref name="path" /> y que coinciden con el patrón de búsqueda especificado.</returns>
      <param name="path">La ruta de acceso absoluta o relativa al directorio que se va a buscar.Esta cadena no distingue entre mayúsculas y minúsculas.</param>
      <param name="searchPattern">Cadena de búsqueda que debe coincidir con los nombres de los archivos de <paramref name="path" />.Este parámetro puede contener una combinación de ruta de acceso literal y caracteres comodín (* y ?) válidos (vea Comentarios), pero no es compatible con las expresiones regulares.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " /> es una cadena de longitud cero, que contiene solo un espacio en blanco, o que contiene uno o más caracteres no válidos.Puede consultar caracteres no válidos usando el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.o bien<paramref name="searchPattern" /> no contiene un modelo válido.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null.o bien<paramref name="searchPattern" />is null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">El valor de <paramref name="path" /> no es válido; por ejemplo, hace referencia a una unidad no asignada. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> es un nombre de archivo.</exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo especificado o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String,System.String,System.IO.SearchOption)">
      <summary>Devuelve una colección enumerable de nombres de archivo que coinciden con un patrón de búsqueda en una ruta de acceso especificada y, opcionalmente, busca en subdirectorios.</summary>
      <returns>Una colección enumerable de nombres completos (con sus rutas de acceso) para los archivos en el directorio especificado por <paramref name="path" /> y que coinciden con el patrón de búsqueda y opción especificados.</returns>
      <param name="path">La ruta de acceso absoluta o relativa al directorio que se va a buscar.Esta cadena no distingue entre mayúsculas y minúsculas.</param>
      <param name="searchPattern">Cadena de búsqueda que debe coincidir con los nombres de los archivos de <paramref name="path" />.Este parámetro puede contener una combinación de ruta de acceso literal y caracteres comodín (* y ?) válidos (vea Comentarios), pero no es compatible con las expresiones regulares.</param>
      <param name="searchOption">Uno de los valores de enumeración que especifica si la operación de búsqueda debe incluir solo el directorio actual o debe incluir todos los subdirectorios.El valor predeterminado es <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " /> es una cadena de longitud cero, que contiene solo un espacio en blanco, o que contiene uno o más caracteres no válidos.Puede consultar caracteres no válidos usando el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.o bien<paramref name="searchPattern" /> no contiene un modelo válido.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null.o bien<paramref name="searchPattern" />is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> no es un valor válido de <see cref="T:System.IO.SearchOption" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">El valor de <paramref name="path" /> no es válido; por ejemplo, hace referencia a una unidad no asignada. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> es un nombre de archivo.</exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo especificado o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String)">
      <summary>Devuelve una colección enumerable de nombres de archivo y nombres de directorio en una ruta de acceso especificada. </summary>
      <returns>Colección enumerable de entradas del sistema de archivos en el directorio especificado por <paramref name="path" />.</returns>
      <param name="path">La ruta de acceso absoluta o relativa al directorio que se va a buscar.Esta cadena no distingue entre mayúsculas y minúsculas.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " /> es una cadena de longitud cero, que contiene solo un espacio en blanco, o que contiene uno o más caracteres no válidos.Puede consultar caracteres no válidos usando el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">El valor de <paramref name="path" /> no es válido; por ejemplo, hace referencia a una unidad no asignada. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> es un nombre de archivo.</exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo especificado o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String,System.String)">
      <summary>Devuelve una colección enumerable de nombres de archivo y nombres de directorio que coinciden con un patrón de búsqueda en una ruta de acceso especificada.</summary>
      <returns>Colección enumerable de entradas del sistema de archivos en el directorio especificado por <paramref name="path" />, y que coinciden con el patrón de búsqueda especificado.</returns>
      <param name="path">La ruta de acceso absoluta o relativa al directorio que se va a buscar.Esta cadena no distingue entre mayúsculas y minúsculas.</param>
      <param name="searchPattern">Cadena de búsqueda que debe coincidir con los nombres de las entradas del sistema de archivos de <paramref name="path" />.Este parámetro puede contener una combinación de ruta de acceso literal y caracteres comodín (* y ?) válidos (vea Comentarios), pero no es compatible con las expresiones regulares.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " /> es una cadena de longitud cero, que contiene solo un espacio en blanco, o que contiene uno o más caracteres no válidos.Puede consultar caracteres no válidos usando el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.o bien<paramref name="searchPattern" /> no contiene un modelo válido.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null.o bien<paramref name="searchPattern" />is null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">El valor de <paramref name="path" /> no es válido; por ejemplo, hace referencia a una unidad no asignada. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> es un nombre de archivo.</exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo especificado o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String,System.String,System.IO.SearchOption)">
      <summary>Devuelve una colección enumerable de nombres de archivo y de directorio que coinciden con un patrón de búsqueda en una ruta de acceso especificada y, opcionalmente, busca en subdirectorios.</summary>
      <returns>Colección enumerable de entradas del sistema de archivos en el directorio especificado por <paramref name="path" />, y que coinciden con el patrón de búsqueda y opción especificados.</returns>
      <param name="path">La ruta de acceso absoluta o relativa al directorio que se va a buscar.Esta cadena no distingue entre mayúsculas y minúsculas.</param>
      <param name="searchPattern">Cadena de búsqueda que debe coincidir con entradas del sistema de archivos de <paramref name="path" />.Este parámetro puede contener una combinación de ruta de acceso literal y caracteres comodín (* y ?) válidos (vea Comentarios), pero no es compatible con las expresiones regulares.</param>
      <param name="searchOption">Uno de los valores de enumeración que especifica si la operación de búsqueda debe incluir solo el directorio actual o debe incluir todos los subdirectorios.El valor predeterminado es <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " /> es una cadena de longitud cero, que contiene solo un espacio en blanco, o que contiene uno o más caracteres no válidos.Puede consultar caracteres no válidos usando el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.o bien<paramref name="searchPattern" /> no contiene un modelo válido.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null.o bien<paramref name="searchPattern" />is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> no es un valor válido de <see cref="T:System.IO.SearchOption" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">El valor de <paramref name="path" /> no es válido; por ejemplo, hace referencia a una unidad no asignada. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> es un nombre de archivo.</exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo especificado o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido.</exception>
    </member>
    <member name="M:System.IO.Directory.Exists(System.String)">
      <summary>Determina si la ruta de acceso dada hace referencia a un directorio existente en el disco.</summary>
      <returns>true si <paramref name="path" /> hace referencia a un directorio existente; false si el directorio no existe o se produce un error al intentar determinar si existe el archivo especificado.</returns>
      <param name="path">Ruta de acceso que se va a probar. </param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetCreationTime(System.String)">
      <summary>Obtiene la fecha y hora de creación de un directorio.</summary>
      <returns>Estructura que se establece en la fecha y hora de creación para el directorio especificado.Este valor se expresa en hora local.</returns>
      <param name="path">Ruta de acceso del directorio. </param>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero que contiene solo espacios en blanco o uno o más caracteres no válidos.Puede consultar caracteres no válidos usando el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetCreationTimeUtc(System.String)">
      <summary>Obtiene la fecha y hora de creación, en formato de Hora universal coordinada (UTC), de un directorio.</summary>
      <returns>Estructura que se establece en la fecha y hora de creación para el directorio especificado.Este valor se expresa en hora UTC.</returns>
      <param name="path">Ruta de acceso del directorio. </param>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero que contiene solo espacios en blanco o uno o más caracteres no válidos.Puede consultar caracteres no válidos usando el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetCurrentDirectory">
      <summary>Obtiene el directorio de trabajo actual de la aplicación.</summary>
      <returns>Cadena que contiene la ruta de acceso del directorio de trabajo actual y no finaliza con una barra diagonal inversa (\).</returns>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.NotSupportedException">El sistema operativo es Windows CE, que no dispone de funcionalidad de directorio actual.Este método está disponible en .NET Compact Framework, pero no se admite actualmente.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String)">
      <summary>Devuelve los nombres de los subdirectorios (con sus rutas de acceso) del directorio especificado.</summary>
      <returns>Una matriz de los nombres completos (con sus rutas de acceso) de los subdirectorios de la ruta de acceso especificada, o una matriz vacía si no se encuentran directorios.</returns>
      <param name="path">La ruta de acceso absoluta o relativa al directorio que se va a buscar.Esta cadena no distingue entre mayúsculas y minúsculas.</param>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero que contiene solo espacios en blanco o uno o más caracteres no válidos.Puede consultar caracteres no válidos usando el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> es un nombre de archivo. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String,System.String)">
      <summary>Devuelve los nombres de los subdirectorios (con sus rutas de acceso) que coinciden con el patrón de búsqueda especificado en el directorio especificado.</summary>
      <returns>Una matriz de los nombres completos (con sus rutas de acceso) de los subdirectorios que coinciden con el patrón de búsqueda en el directorio especificado, o una matriz vacía si no se encuentran directorios.</returns>
      <param name="path">La ruta de acceso absoluta o relativa al directorio que se va a buscar.Esta cadena no distingue entre mayúsculas y minúsculas.</param>
      <param name="searchPattern">Cadena de búsqueda que debe coincidir con los nombres de los subdirectorios de <paramref name="path" />.Este parámetro puede contener una combinación de caracteres literales y caracteres comodín válidos (vea Comentarios), pero no es compatible con las expresiones regulares.</param>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero que contiene solo espacios en blanco o uno o más caracteres no válidos.Puede consultar los caracteres no válidos mediante <see cref="M:System.IO.Path.GetInvalidPathChars" />.o bien <paramref name="searchPattern" /> no contiene un patrón válido. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> o <paramref name="searchPattern" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> es un nombre de archivo. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String,System.String,System.IO.SearchOption)">
      <summary>Devuelve los nombres de los subdirectorios (incluidas las rutas de acceso) que coinciden con el patrón de búsqueda especificado en el directorio especificado y, opcionalmente, busca en subdirectorios.</summary>
      <returns>Una matriz de los nombres completos (con sus rutas de acceso) de los subdirectorios que coinciden con los criterios especificados, o una matriz vacía si no se encuentran directorios.</returns>
      <param name="path">La ruta de acceso absoluta o relativa al directorio que se va a buscar.Esta cadena no distingue entre mayúsculas y minúsculas.</param>
      <param name="searchPattern">Cadena de búsqueda que debe coincidir con los nombres de los subdirectorios de <paramref name="path" />.Este parámetro puede contener una combinación de caracteres literales y caracteres comodín válidos (vea Comentarios), pero no es compatible con las expresiones regulares.</param>
      <param name="searchOption">Uno de los valores de enumeración que especifica si la operación de búsqueda debe incluir todos los subdirectorios o solo el directorio actual. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero que contiene solo espacios en blanco o uno o más caracteres no válidos.Puede consultar caracteres no válidos usando el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.o bien <paramref name="searchPattern" /> no contiene un modelo válido. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> o <paramref name="searchPattern" /> es null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> no es un valor válido de <see cref="T:System.IO.SearchOption" />.</exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> es un nombre de archivo. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
    </member>
    <member name="M:System.IO.Directory.GetDirectoryRoot(System.String)">
      <summary>Devuelve la información del volumen, la información de raíz o ambas para la ruta de acceso especificada.</summary>
      <returns>Cadena que contiene la información del volumen, la información de raíz o ambas para la ruta de acceso especificada.</returns>
      <param name="path">Ruta de acceso de un archivo o directorio. </param>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero que contiene solo espacios en blanco o uno o más caracteres no válidos.Puede consultar los caracteres no válidos con <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String)">
      <summary>Devuelve los nombres de archivo (con sus rutas de acceso) del directorio especificado.</summary>
      <returns>Una matriz de los nombres completos (con sus rutas de acceso) para los archivos en el directorio especificado, o una matriz vacía si no se encuentran archivos.</returns>
      <param name="path">La ruta de acceso absoluta o relativa al directorio que se va a buscar.Esta cadena no distingue entre mayúsculas y minúsculas.</param>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> es un nombre de archivo.o bienSe ha producido un error de red. </exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero que contiene solo espacios en blanco o uno o más caracteres no válidos.Puede consultar caracteres no válidos usando el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no se encuentra o no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String,System.String)">
      <summary>Devuelve los nombres de los archivos (con sus rutas de acceso) que coinciden con el patrón de búsqueda especificado en el directorio especificado.</summary>
      <returns>Una matriz de nombres completos (con sus rutas de acceso) para los archivos del directorio especificado que coinciden con el patrón de búsqueda especificado, o una matriz vacía si no se encuentra ningún archivo.</returns>
      <param name="path">La ruta de acceso absoluta o relativa al directorio que se va a buscar.Esta cadena no distingue entre mayúsculas y minúsculas.</param>
      <param name="searchPattern">Cadena de búsqueda que debe coincidir con los nombres de los archivos de <paramref name="path" />.Este parámetro puede contener una combinación de ruta de acceso literal y caracteres comodín (* y ?) válidos (vea Comentarios), pero no es compatible con las expresiones regulares.</param>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> es un nombre de archivo.o bienSe ha producido un error de red. </exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero que contiene solo espacios en blanco o uno o más caracteres no válidos.Puede consultar los caracteres no válidos mediante <see cref="M:System.IO.Path.GetInvalidPathChars" />.o bien <paramref name="searchPattern" /> no contiene un patrón válido. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> o <paramref name="searchPattern" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no se encuentra o no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String,System.String,System.IO.SearchOption)">
      <summary>Devuelve los nombres de los archivos (con sus rutas de acceso) que coincidan con el patrón de búsqueda especificado en el directorio especificado, utilizando un valor para determinar si se debe buscar en los subdirectorios.</summary>
      <returns>Una matriz de nombres completos (con sus rutas de acceso) para los archivos del directorio especificado que coinciden con el patrón de búsqueda y opción especificados, o una matriz vacía si no se encuentra ningún archivo.</returns>
      <param name="path">La ruta de acceso absoluta o relativa al directorio que se va a buscar.Esta cadena no distingue entre mayúsculas y minúsculas.</param>
      <param name="searchPattern">Cadena de búsqueda que debe coincidir con los nombres de los archivos de <paramref name="path" />.Este parámetro puede contener una combinación de ruta de acceso literal y caracteres comodín (* y ?) válidos (vea Comentarios), pero no es compatible con las expresiones regulares.</param>
      <param name="searchOption">Uno de los valores de enumeración que especifica si la operación de búsqueda debe incluir todos los subdirectorios o solo el directorio actual. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero que contiene solo espacios en blanco o uno o más caracteres no válidos.Puede consultar los caracteres no válidos con el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.o bien <paramref name="searchPattern" /> no contiene un modelo válido.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> o <paramref name="searchpattern" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> no es un valor válido de <see cref="T:System.IO.SearchOption" />.</exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no se encuentra o no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> es un nombre de archivo.o bienSe ha producido un error de red. </exception>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String)">
      <summary>Devuelve los nombres de todos los archivos y subdirectorios de una ruta de acceso especificada.</summary>
      <returns>Una matriz de los nombres de los archivos y subdirectorios en el directorio especificado, o una matriz vacía si no se encuentran archivos o subdirectorios.</returns>
      <param name="path">La ruta de acceso absoluta o relativa al directorio que se va a buscar.Esta cadena no distingue entre mayúsculas y minúsculas.</param>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero que contiene solo espacios en blanco o uno o más caracteres no válidos.Puede consultar los caracteres no válidos con <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> es un nombre de archivo. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String,System.String)">
      <summary>Devuelve una matriz o nombres de archivo y nombres de directorio que coinciden con un patrón de búsqueda en una ruta de acceso especificada.</summary>
      <returns>Una matriz de nombres de archivo y de directorio que coinciden con los criterios de búsqueda especificados, o una matriz vacía si no se encuentran archivos o directorios.</returns>
      <param name="path">La ruta de acceso absoluta o relativa al directorio que se va a buscar.Esta cadena no distingue entre mayúsculas y minúsculas.</param>
      <param name="searchPattern">Cadena de búsqueda que debe coincidir con los nombres de los directorios y archivos de <paramref name="path" />.Este parámetro puede contener una combinación de ruta de acceso literal y caracteres comodín (* y ?) válidos (vea Comentarios), pero no es compatible con las expresiones regulares.</param>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero que contiene solo espacios en blanco o uno o más caracteres no válidos.Puede consultar los caracteres no válidos con el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.o bien <paramref name="searchPattern" /> no contiene un modelo válido. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> o <paramref name="searchPattern" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> es un nombre de archivo. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String,System.String,System.IO.SearchOption)">
      <summary>Devuelve una matriz de todos los nombres de archivo y de directorio que coinciden con un patrón de búsqueda en una ruta de acceso especificada y, opcionalmente, busca en subdirectorios.</summary>
      <returns>Una matriz de nombres de archivo y nombres de directorio que coincide con los criterios de búsqueda especificados, o una matriz vacía si no se encuentran archivos o directorios.</returns>
      <param name="path">La ruta de acceso absoluta o relativa al directorio que se va a buscar.Esta cadena no distingue entre mayúsculas y minúsculas.</param>
      <param name="searchPattern">Cadena de búsqueda que debe coincidir con los nombres de los directorios y archivos de <paramref name="path" />.Este parámetro puede contener una combinación de ruta de acceso literal y caracteres comodín (* y ?) válidos (vea Comentarios), pero no es compatible con las expresiones regulares.</param>
      <param name="searchOption">Uno de los valores de enumeración que especifica si la operación de búsqueda debe incluir solo el directorio actual o debe incluir todos los subdirectorios.El valor predeterminado es <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " /> es una cadena de longitud cero, que contiene solo un espacio en blanco, o que contiene uno o más caracteres no válidos.Puede consultar caracteres no válidos usando el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.o bien<paramref name="searchPattern" /> no contiene un modelo válido.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null.o bien<paramref name="searchPattern" />is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> no es un valor válido de <see cref="T:System.IO.SearchOption" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">El valor de <paramref name="path" /> no es válido; por ejemplo, hace referencia a una unidad no asignada. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> es un nombre de archivo.</exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo especificado o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido.</exception>
    </member>
    <member name="M:System.IO.Directory.GetLastAccessTime(System.String)">
      <summary>Devuelve la fecha y hora a la que se produjo el último acceso al archivo o directorio especificado.</summary>
      <returns>Estructura que se establece en la fecha y hora a la que se produjo el último acceso al archivo o directorio especificado.Este valor se expresa en hora local.</returns>
      <param name="path">Archivo o directorio para el que se va a obtener información de fecha y hora de acceso. </param>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero que contiene solo espacios en blanco o uno o más caracteres no válidos.Puede consultar los caracteres no válidos con el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.NotSupportedException">El parámetro <paramref name="path" /> no tiene un formato válido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetLastAccessTimeUtc(System.String)">
      <summary>Devuelve la fecha y la hora, en formato de Hora universal coordinada (UTC), a la que se produjo el último acceso al archivo o directorio especificado.</summary>
      <returns>Estructura que se establece en la fecha y hora a la que se produjo el último acceso al archivo o directorio especificado.Este valor se expresa en hora UTC.</returns>
      <param name="path">Archivo o directorio para el que se va a obtener información de fecha y hora de acceso. </param>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero que contiene solo espacios en blanco o uno o más caracteres no válidos.Puede consultar los caracteres no válidos con el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.NotSupportedException">El parámetro <paramref name="path" /> no tiene un formato válido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetLastWriteTime(System.String)">
      <summary>Devuelve la fecha y hora a la que se escribió por última vez en el archivo o directorio especificado.</summary>
      <returns>Estructura que se establece en la fecha y hora a la que se escribió por última vez en el archivo o directorio especificado.Este valor se expresa en hora local.</returns>
      <param name="path">Archivo o directorio para el que se va a obtener información de fecha y hora de modificación. </param>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero que contiene solo espacios en blanco o uno o más caracteres no válidos.Puede consultar los caracteres no válidos con el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetLastWriteTimeUtc(System.String)">
      <summary>Devuelve la fecha y la hora, en formato de Hora universal coordinada (UTC), a la que se escribió por última vez en el archivo o directorio especificado.</summary>
      <returns>Estructura que se establece en la fecha y hora a la que se escribió por última vez en el archivo o directorio especificado.Este valor se expresa en hora UTC.</returns>
      <param name="path">Archivo o directorio para el que se va a obtener información de fecha y hora de modificación. </param>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero que contiene solo espacios en blanco o uno o más caracteres no válidos.Puede consultar los caracteres no válidos con el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetParent(System.String)">
      <summary>Recupera el directorio principal de la ruta especificada, incluidas tanto las rutas de acceso absolutas como las relativas.</summary>
      <returns>El directorio principal o null si <paramref name="path" /> es el directorio raíz, incluida la raíz de un servidor UNC o un nombre de uso compartido.</returns>
      <param name="path">Ruta de acceso para la cual se va a recuperar el directorio principal. </param>
      <exception cref="T:System.IO.IOException">El directorio que especifica <paramref name="path" /> es de solo lectura. </exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero que contiene solo espacios en blanco o uno o más caracteres no válidos.Puede consultar los caracteres no válidos con el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">No se encontró la ruta de acceso especificada. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.Move(System.String,System.String)">
      <summary>Mueve un archivo o directorio y su contenido a una nueva ubicación.</summary>
      <param name="sourceDirName">Ruta de acceso del archivo o del directorio que se va a mover. </param>
      <param name="destDirName">Ruta de acceso a la nueva ubicación para <paramref name="sourceDirName" />.Si <paramref name="sourceDirName" /> es un archivo, <paramref name="destDirName" /> debe ser también un nombre de archivo.</param>
      <exception cref="T:System.IO.IOException">Se intentó mover un directorio a un volumen diferente. o bien <paramref name="destDirName" /> ya existe. o bien Los parámetros <paramref name="sourceDirName" /> y <paramref name="destDirName" /> hacen referencia al mismo archivo o directorio. o bienEl directorio o un archivo en el está utilizando otro proceso.</exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceDirName" /> o <paramref name="destDirName" /> es una cadena de longitud cero, contiene solo espacios en blanco o contiene uno o más caracteres no válidos.Puede consultar los caracteres no válidos con el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceDirName" /> o <paramref name="destDirName" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada por <paramref name="sourceDirName" /> no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetCreationTime(System.String,System.DateTime)">
      <summary>Establece la fecha y hora de creación del archivo o la carpeta especificados.</summary>
      <param name="path">El archivo o directorio para el que se va a establecer información de fecha y hora de creación. </param>
      <param name="creationTime">Fecha y hora en que escribió en el archivo o directorio por última vez.Este valor se expresa en hora local.</param>
      <exception cref="T:System.IO.FileNotFoundException">No se encontró la ruta de acceso especificada. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero que contiene solo espacios en blanco o uno o más caracteres no válidos.Puede consultar los caracteres no válidos con el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> especifica un valor fuera del intervalo de fechas u horas permitido para esta operación. </exception>
      <exception cref="T:System.PlatformNotSupportedException">El sistema operativo actual no es Windows NT o una versión posterior.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetCreationTimeUtc(System.String,System.DateTime)">
      <summary>Establece la fecha y hora de creación, en formato de Hora universal coordinada (UTC), del archivo o directorio especificado.</summary>
      <param name="path">El archivo o directorio para el que se va a establecer información de fecha y hora de creación. </param>
      <param name="creationTimeUtc">La fecha y hora en que se creó el directorio o archivo.Este valor se expresa en hora local.</param>
      <exception cref="T:System.IO.FileNotFoundException">No se encontró la ruta de acceso especificada. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero que contiene solo espacios en blanco o uno o más caracteres no válidos.Puede consultar los caracteres no válidos con el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> especifica un valor fuera del intervalo de fechas u horas permitido para esta operación. </exception>
      <exception cref="T:System.PlatformNotSupportedException">El sistema operativo actual no es Windows NT o una versión posterior.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetCurrentDirectory(System.String)">
      <summary>Establece el directorio de trabajo actual de la aplicación en el directorio especificado.</summary>
      <param name="path">Ruta de acceso en la que se establece el directorio de trabajo actual. </param>
      <exception cref="T:System.IO.IOException">Error de E/S. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero que contiene solo espacios en blanco o uno o más caracteres no válidos.Puede consultar los caracteres no válidos con el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.Security.SecurityException">El llamador no tiene el permiso necesario para obtener acceso a código no administrado. </exception>
      <exception cref="T:System.IO.FileNotFoundException">No se encontró la ruta de acceso especificada. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">No se encontró el directorio especificado.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastAccessTime(System.String,System.DateTime)">
      <summary>Establece la fecha y hora a la que se produjo el último acceso al archivo o directorio especificado.</summary>
      <param name="path">El archivo o directorio para el que se va a establecer información de fecha y hora de acceso. </param>
      <param name="lastAccessTime">Objeto que contiene el valor que se va a establecer para la fecha y hora de acceso de <paramref name="path" />.Este valor se expresa en hora local.</param>
      <exception cref="T:System.IO.FileNotFoundException">No se encontró la ruta de acceso especificada. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero que contiene solo espacios en blanco o uno o más caracteres no válidos.Puede consultar los caracteres no válidos con el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.PlatformNotSupportedException">El sistema operativo actual no es Windows NT o una versión posterior.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTime" /> especifica un valor fuera del intervalo de fechas u horas permitido para esta operación.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastAccessTimeUtc(System.String,System.DateTime)">
      <summary>Establece la fecha y la hora, en formato de Hora universal coordinada (UTC), a la que se produjo el último acceso al archivo o directorio especificado.</summary>
      <param name="path">El archivo o directorio para el que se va a establecer información de fecha y hora de acceso. </param>
      <param name="lastAccessTimeUtc">Objeto que contiene el valor que se va a establecer para la fecha y hora de acceso de <paramref name="path" />.Este valor se expresa en hora UTC.</param>
      <exception cref="T:System.IO.FileNotFoundException">No se encontró la ruta de acceso especificada. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero que contiene solo espacios en blanco o uno o más caracteres no válidos.Puede consultar los caracteres no válidos con el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.PlatformNotSupportedException">El sistema operativo actual no es Windows NT o una versión posterior.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTimeUtc" /> especifica un valor fuera del intervalo de fechas u horas permitido para esta operación.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastWriteTime(System.String,System.DateTime)">
      <summary>Establece la fecha y la hora en que escribió en un directorio por última vez.</summary>
      <param name="path">Ruta de acceso del directorio. </param>
      <param name="lastWriteTime">Fecha y hora en que escribió en el directorio por última vez.Este valor se expresa en hora local.</param>
      <exception cref="T:System.IO.FileNotFoundException">No se encontró la ruta de acceso especificada. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero que contiene solo espacios en blanco o uno o más caracteres no válidos.Puede consultar los caracteres no válidos con el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.PlatformNotSupportedException">El sistema operativo actual no es Windows NT o una versión posterior.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTime" /> especifica un valor fuera del intervalo de fechas u horas permitido para esta operación.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastWriteTimeUtc(System.String,System.DateTime)">
      <summary>Establece la fecha y la hora, en formato de Hora universal coordinada (UTC), a la que se escribió en el directorio por última vez.</summary>
      <param name="path">Ruta de acceso del directorio. </param>
      <param name="lastWriteTimeUtc">Fecha y hora en que escribió en el directorio por última vez.Este valor se expresa en hora UTC.</param>
      <exception cref="T:System.IO.FileNotFoundException">No se encontró la ruta de acceso especificada. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero que contiene solo espacios en blanco o uno o más caracteres no válidos.Puede consultar los caracteres no válidos con el método <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.PlatformNotSupportedException">El sistema operativo actual no es Windows NT o una versión posterior.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTimeUtc" /> especifica un valor fuera del intervalo de fechas u horas permitido para esta operación.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.IO.DirectoryInfo">
      <summary>Expone métodos de instancia para crear, mover y enumerar archivos en directorios y subdirectorios.Esta clase no puede heredarse.Para examinar el código fuente de .NET Framework para este tipo, consulte el
                                Origen de referencia.
                            </summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.DirectoryInfo.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase 
                                <see cref="T:System.IO.DirectoryInfo" />clase en la ruta de acceso especificada.
                            </summary>
      <param name="path">Una cadena que especifica la ruta de acceso en la que se va a crear el
                                    DirectoryInfo.
                                </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />es
                                        null.
                                    </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contiene caracteres no válidos, como ", &lt;, &gt; o |
                                    </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.La ruta de acceso especificada o el nombre de archivo (o ambos) son demasiado largos.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.Create">
      <summary>Crea un directorio.</summary>
      <exception cref="T:System.IO.IOException">No se puede crear el directorio.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.CreateSubdirectory(System.String)">
      <summary>Crea uno o varios subdirectorios en la ruta de acceso especificada.La ruta de acceso especificada puede ser relativa a esta instancia de la
                            <see cref="T:System.IO.DirectoryInfo" />clase.
                        </summary>
      <returns>El último directorio especificado en
                                <paramref name="path" />.
                            </returns>
      <param name="path">La ruta de acceso especificada.No puede ser un volumen de disco diferente ni un nombre de convención de nomenclatura universal (Universal Naming Convention, UNC).</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />no se especifica una ruta de acceso de archivo válida o no contiene
                                        DirectoryInfocaracteres.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />es
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida como, por ejemplo, una ruta de una unidad no asignada.</exception>
      <exception cref="T:System.IO.IOException">No se puede crear el subdirectorio.o bienUn archivo o directorio ya tiene el nombre especificado por
                                        <paramref name="path" />.
                                    </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.La ruta de acceso especificada o el nombre de archivo (o ambos) son demasiado largos.</exception>
      <exception cref="T:System.Security.SecurityException">El llamador no tiene permiso de acceso a código para crear el directorio.o bienEl llamador no tiene permiso de acceso a código para leer el directorio descrito por el valor devuelto
                                    Objeto <see cref="T:System.IO.DirectoryInfo" />.
                                Esto puede ocurrir cuando el
                                    <paramref name="path" />parámetro describe un directorio existente.
                                </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> contiene un carácter de dos puntos (:) que no forma parte de una etiqueta de la unidad de disco ("C:\\").
                                    </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.Delete">
      <summary>Esto elimina
                                <see cref="T:System.IO.DirectoryInfo" />Si está vacío.
                            </summary>
      <exception cref="T:System.UnauthorizedAccessException">El directorio contiene un archivo de solo lectura.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">El directorio descrito por este
                                        <see cref="T:System.IO.DirectoryInfo" />objeto no existe o no se pudo encontrar.
                                    </exception>
      <exception cref="T:System.IO.IOException">El directorio no está vacío.o bienEl directorio es el directorio de trabajo actual de la aplicación.o bienHay un identificador abierto en el directorio, y el sistema operativo es Windows XP o anterior.Este identificador abierto puede ser el resultado de la enumeración de directorios.Para obtener más información, vea
                                    Cómo: Enumerar directorios y archivos.
                                </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.Delete(System.Boolean)">
      <summary>Elimina esta instancia de un
                                <see cref="T:System.IO.DirectoryInfo" />, especificar si se va a eliminar los subdirectorios y archivos.
                            </summary>
      <param name="recursive">truePara eliminar este directorio, sus subdirectorios y todos los archivos; de lo contrario,
                                    false.
                                </param>
      <exception cref="T:System.UnauthorizedAccessException">El directorio contiene un archivo de solo lectura.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">El directorio descrito por este
                                        <see cref="T:System.IO.DirectoryInfo" />objeto no existe o no se pudo encontrar.
                                    </exception>
      <exception cref="T:System.IO.IOException">El directorio es de solo lectura.o bienEl directorio contiene uno o más archivos o subdirectorios y
                                        <paramref name="recursive" />es
                                        false.
                                    o bienEl directorio es el directorio de trabajo actual de la aplicación.o bienHay un identificador abierto en el directorio o en uno de sus archivos, y el sistema operativo es Windows XP o anterior.Este identificador abierto puede ser el resultado de la enumeración de directorios y archivos.Para obtener más información, vea
                                    Cómo: Enumerar directorios y archivos.
                                </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories">
      <summary>Devuelve una colección enumerable de información de directorios del directorio actual.</summary>
      <returns>Colección enumerable de directorios del directorio actual.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso encapsulada en el
                                        <see cref="T:System.IO.DirectoryInfo" />objeto no es válido (por ejemplo, está en una unidad no asignada).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories(System.String)">
      <summary>Devuelve una colección enumerable de información de directorios que coincide con un modelo de búsqueda especificado.</summary>
      <returns>Una colección enumerable de directorios que coincide con
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">Cadena de búsqueda que debe coincidir con los nombres de los directorios.Este parámetro puede contener una combinación de ruta de acceso literal y caracteres comodín (* y ?) válidos (vea Comentarios), pero no es compatible con las expresiones regulares.El modelo predeterminado es "*", que devuelve todos los archivos.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />es
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso encapsulada en el
                                        <see cref="T:System.IO.DirectoryInfo" />objeto no es válido (por ejemplo, está en una unidad no asignada).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories(System.String,System.IO.SearchOption)">
      <summary>Devuelve una colección enumerable de información de directorios que coincide con un modelo de búsqueda y una opción de búsqueda en subdirectorios especificados.</summary>
      <returns>Una colección enumerable de directorios que coincide con
                                <paramref name="searchPattern" />y
                                <paramref name="searchOption" />.
                            </returns>
      <param name="searchPattern">Cadena de búsqueda que debe coincidir con los nombres de los directorios.Este parámetro puede contener una combinación de ruta de acceso literal y caracteres comodín (* y ?) válidos (vea Comentarios), pero no es compatible con las expresiones regulares.El modelo predeterminado es "*", que devuelve todos los archivos.</param>
      <param name="searchOption">Uno de los valores de enumeración que especifica si la operación de búsqueda debe incluir solo el directorio actual o todos los subdirectorios.El valor predeterminado es
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />es
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />no es válido
                                        Valor de <see cref="T:System.IO.SearchOption" />.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso encapsulada en el
                                        <see cref="T:System.IO.DirectoryInfo" />objeto no es válido (por ejemplo, está en una unidad no asignada).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles">
      <summary>Devuelve una colección enumerable de información de archivos del directorio actual.</summary>
      <returns>Colección enumerable de los archivos del directorio actual.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso encapsulada en el
                                        <see cref="T:System.IO.DirectoryInfo" />objeto no es válido (por ejemplo, está en una unidad no asignada).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles(System.String)">
      <summary>Devuelve una colección enumerable de información de archivos que coincide con un modelo de búsqueda.</summary>
      <returns>Una colección enumerable de archivos que coincide con
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">Cadena de búsqueda que debe coincidir con los nombres de los archivos.Este parámetro puede contener una combinación de ruta de acceso literal y caracteres comodín (* y ?) válidos (vea Comentarios), pero no es compatible con las expresiones regulares.El modelo predeterminado es "*", que devuelve todos los archivos.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />es
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso encapsulada en el
                                        <see cref="T:System.IO.DirectoryInfo" />objeto no es válido, (por ejemplo, está en una unidad no asignada).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles(System.String,System.IO.SearchOption)">
      <summary>Devuelve una colección enumerable de información de archivos que coincide con un modelo de búsqueda y una opción de búsqueda en subdirectorios especificados.</summary>
      <returns>Una colección enumerable de archivos que coincide con
                                <paramref name="searchPattern" />y
                                <paramref name="searchOption" />.
                            </returns>
      <param name="searchPattern">Cadena de búsqueda que debe coincidir con los nombres de los archivos.Este parámetro puede contener una combinación de ruta de acceso literal y caracteres comodín (* y ?) válidos (vea Comentarios), pero no es compatible con las expresiones regulares.El modelo predeterminado es "*", que devuelve todos los archivos.</param>
      <param name="searchOption">Uno de los valores de enumeración que especifica si la operación de búsqueda debe incluir solo el directorio actual o todos los subdirectorios.El valor predeterminado es
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />es
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />no es válido
                                        Valor de <see cref="T:System.IO.SearchOption" />.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso encapsulada en el
                                        <see cref="T:System.IO.DirectoryInfo" />objeto no es válido (por ejemplo, está en una unidad no asignada).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos">
      <summary>Devuelve una colección enumerable de información del sistema de archivos del directorio actual.</summary>
      <returns>Colección enumerable de información del sistema de archivos del directorio actual.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso encapsulada en el
                                        <see cref="T:System.IO.DirectoryInfo" />objeto no es válido (por ejemplo, está en una unidad no asignada).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos(System.String)">
      <summary>Devuelve una colección enumerable de información del sistema de archivos que coincide con un modelo de búsqueda especificado.</summary>
      <returns>Una colección enumerable de objetos de información del sistema de archivos que coincide con
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">Cadena de búsqueda que debe coincidir con los nombres de los directorios.Este parámetro puede contener una combinación de ruta de acceso literal y caracteres comodín (* y ?) válidos (vea Comentarios), pero no es compatible con las expresiones regulares.El modelo predeterminado es "*", que devuelve todos los archivos.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />es
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso encapsulada en el
                                        <see cref="T:System.IO.DirectoryInfo" />objeto no es válido (por ejemplo, está en una unidad no asignada).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos(System.String,System.IO.SearchOption)">
      <summary>Devuelve una colección enumerable de información del sistema de archivos que coincide con un modelo de búsqueda y una opción de búsqueda en subdirectorios especificados.</summary>
      <returns>Una colección enumerable de objetos de información del sistema de archivos que coincide con
                                <paramref name="searchPattern" />y
                                <paramref name="searchOption" />.
                            </returns>
      <param name="searchPattern">Cadena de búsqueda que debe coincidir con los nombres de los directorios.Este parámetro puede contener una combinación de ruta de acceso literal y caracteres comodín (* y ?) válidos (vea Comentarios), pero no es compatible con las expresiones regulares.El modelo predeterminado es "*", que devuelve todos los archivos.</param>
      <param name="searchOption">Uno de los valores de enumeración que especifica si la operación de búsqueda debe incluir solo el directorio actual o todos los subdirectorios.El valor predeterminado es
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />es
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />no es válido
                                        Valor de <see cref="T:System.IO.SearchOption" />.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso encapsulada en el
                                        <see cref="T:System.IO.DirectoryInfo" />objeto no es válido (por ejemplo, está en una unidad no asignada).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido.</exception>
    </member>
    <member name="P:System.IO.DirectoryInfo.Exists">
      <summary>Obtiene un valor que indica si existe el directorio.</summary>
      <returns>trueSi el directorio existe; de lo contrario,
                                false.
                            </returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories">
      <summary>Devuelve los subdirectorios del directorio actual.</summary>
      <returns>Una matriz de
                                Objetos <see cref="T:System.IO.DirectoryInfo" />.
                            </returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso encapsulada en el
                                        <see cref="T:System.IO.DirectoryInfo" />objeto es válida, por ejemplo en una unidad no asignada.
                                    </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido.</exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories(System.String)">
      <summary>Devuelve una matriz de directorios en la actual
                                <see cref="T:System.IO.DirectoryInfo" />que cumplan los criterios de búsqueda determinada.
                            </summary>
      <returns>Matriz de tipo
                                DirectoryInfobúsqueda de coincidencias
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">Cadena de búsqueda que debe coincidir con los nombres de los directorios.Este parámetro puede contener una combinación de ruta de acceso literal y caracteres comodín (* y ?) válidos (vea Comentarios), pero no es compatible con las expresiones regulares.El modelo predeterminado es "*", que devuelve todos los archivos.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />contiene uno o más caracteres no válidos definidos por el
                                        Método <see cref="M:System.IO.Path.GetInvalidPathChars" />.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />es
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso encapsulada en el
                                        DirectoryInfoobjeto no es válido (por ejemplo, está en una unidad no asignada).
                                    </exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories(System.String,System.IO.SearchOption)">
      <summary>Devuelve una matriz de directorios en la actual
                                <see cref="T:System.IO.DirectoryInfo" />que cumplan los criterios de búsqueda dados y utilizando un valor para determinar si se va a buscar en los subdirectorios.
                            </summary>
      <returns>Matriz de tipo
                                DirectoryInfobúsqueda de coincidencias
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">Cadena de búsqueda que debe coincidir con los nombres de los directorios.Este parámetro puede contener una combinación de ruta de acceso literal y caracteres comodín (* y ?) válidos (vea Comentarios), pero no es compatible con las expresiones regulares.El modelo predeterminado es "*", que devuelve todos los archivos.</param>
      <param name="searchOption">Uno de los valores de enumeración que especifica si la operación de búsqueda debe incluir solo el directorio actual o todos los subdirectorios.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />contiene uno o más caracteres no válidos definidos por el
                                        Método <see cref="M:System.IO.Path.GetInvalidPathChars" />.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />es
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />no es válido
                                        Valor de <see cref="T:System.IO.SearchOption" />.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso encapsulada en el
                                        DirectoryInfoobjeto no es válido (por ejemplo, está en una unidad no asignada).
                                    </exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles">
      <summary>Devuelve una lista de archivos del directorio actual.</summary>
      <returns>Matriz de tipo
                                <see cref="T:System.IO.FileInfo" />.
                            </returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso no es válida como, por ejemplo, una ruta de una unidad no asignada.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles(System.String)">
      <summary>Devuelve una lista de archivos del directorio actual que coinciden con el modelo de búsqueda.</summary>
      <returns>Matriz de tipo
                                <see cref="T:System.IO.FileInfo" />.
                            </returns>
      <param name="searchPattern">Cadena de búsqueda que debe coincidir con los nombres de los archivos.Este parámetro puede contener una combinación de ruta de acceso literal y caracteres comodín (* y ?) válidos (vea Comentarios), pero no es compatible con las expresiones regulares.El modelo predeterminado es "*", que devuelve todos los archivos.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />contiene uno o más caracteres no válidos definidos por el
                                        Método <see cref="M:System.IO.Path.GetInvalidPathChars" />.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />es
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso no es válida (por ejemplo, se encuentra en una unidad de red no asignada).</exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles(System.String,System.IO.SearchOption)">
      <summary>Devuelve una lista de archivos del directorio actual que coinciden con el modelo de búsqueda dado y utiliza un valor para determinar si se va a buscar en los subdirectorios.</summary>
      <returns>Matriz de tipo
                                <see cref="T:System.IO.FileInfo" />.
                            </returns>
      <param name="searchPattern">Cadena de búsqueda que debe coincidir con los nombres de los archivos.Este parámetro puede contener una combinación de ruta de acceso literal y caracteres comodín (* y ?) válidos (vea Comentarios), pero no es compatible con las expresiones regulares.El modelo predeterminado es "*", que devuelve todos los archivos.</param>
      <param name="searchOption">Uno de los valores de enumeración que especifica si la operación de búsqueda debe incluir solo el directorio actual o todos los subdirectorios.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />contiene uno o más caracteres no válidos definidos por el
                                        Método <see cref="M:System.IO.Path.GetInvalidPathChars" />.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />es
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />no es válido
                                        Valor de <see cref="T:System.IO.SearchOption" />.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso no es válida (por ejemplo, se encuentra en una unidad de red no asignada).</exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos">
      <summary>Devuelve una matriz de establecimiento inflexible de tipos
                                <see cref="T:System.IO.FileSystemInfo" />entradas que representa todos los archivos y subdirectorios de un directorio.
                            </summary>
      <returns>Una matriz de establecimiento inflexible de tipos
                                <see cref="T:System.IO.FileSystemInfo" />entradas.
                            </returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso no es válida (por ejemplo, se encuentra en una unidad de red no asignada).</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos(System.String)">
      <summary>Recupera una matriz de establecimiento inflexible de tipos
                                <see cref="T:System.IO.FileSystemInfo" />objetos que representan los archivos y subdirectorios que coinciden con los criterios de búsqueda especificados.
                            </summary>
      <returns>Una matriz de establecimiento inflexible de tipos
                                FileSystemInfoobjetos que coinciden con los criterios de búsqueda.
                            </returns>
      <param name="searchPattern">Cadena de búsqueda que debe coincidir con los nombres de los directorios y archivos.Este parámetro puede contener una combinación de ruta de acceso literal y caracteres comodín (* y ?) válidos (vea Comentarios), pero no es compatible con las expresiones regulares.El modelo predeterminado es "*", que devuelve todos los archivos.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />contiene uno o más caracteres no válidos definidos por el
                                        Método <see cref="M:System.IO.Path.GetInvalidPathChars" />.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />es
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada).</exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos(System.String,System.IO.SearchOption)">
      <summary>Recupera una matriz de
                                <see cref="T:System.IO.FileSystemInfo" />objetos que representan los archivos y subdirectorios que coinciden con los criterios de búsqueda especificados.
                            </summary>
      <returns>Matriz de entradas del sistema de archivos que coinciden con los criterios de búsqueda.</returns>
      <param name="searchPattern">Cadena de búsqueda que debe coincidir con los nombres de los directorios y archivos.Este parámetro puede contener una combinación de ruta de acceso literal y caracteres comodín (* y ?) válidos (vea Comentarios), pero no es compatible con las expresiones regulares.El modelo predeterminado es "*", que devuelve todos los archivos.</param>
      <param name="searchOption">Uno de los valores de enumeración que especifica si la operación de búsqueda debe incluir solo el directorio actual o todos los subdirectorios.El valor predeterminado es
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />contiene uno o más caracteres no válidos definidos por el
                                        Método <see cref="M:System.IO.Path.GetInvalidPathChars" />.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />es
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />no es válido
                                        Valor de <see cref="T:System.IO.SearchOption" />.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada).</exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.MoveTo(System.String)">
      <summary>Mueve un
                                <see cref="T:System.IO.DirectoryInfo" />instancia y su contenido a una nueva ruta de acceso.
                            </summary>
      <param name="destDirName">Nombre y ruta de acceso a la que se va a mover este directorio.El destino no puede ser otro volumen de disco ni un directorio con el mismo nombre.Puede ser un directorio existente al que desee agregar este directorio como subdirectorio.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destDirName" />es
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="destDirName" /> es una cadena vacía ("").
                                    </exception>
      <exception cref="T:System.IO.IOException">Se intentó mover un directorio a un volumen diferente.o bien<paramref name="destDirName" /> ya existe.
                                    o bienNo tiene autorización para esta ruta de acceso.o bienEl directorio que se está moviendo y el directorio de destino tienen el mismo nombre.</exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">No se puede encontrar el directorio de destino.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.DirectoryInfo.Name">
      <summary>Obtiene el nombre de este
                                <see cref="T:System.IO.DirectoryInfo" />instancia.
                            </summary>
      <returns>El nombre del directorio.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.DirectoryInfo.Parent">
      <summary>Obtiene el directorio principal de un subdirectorio especificado.</summary>
      <returns>El directorio principal, o
                                nullSi la ruta de acceso es null o si la ruta de acceso del archivo indica un directorio raíz (por ejemplo, "\", "C:" o * "\\server\share").
                            </returns>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.DirectoryInfo.Root">
      <summary>Obtiene la parte de la raíz del directorio.</summary>
      <returns>Un objeto que representa la raíz del directorio.</returns>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.ToString">
      <summary>Devuelve la ruta de acceso original que pasó el usuario.</summary>
      <returns>Devuelve la ruta de acceso original que pasó el usuario.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.IO.File">
      <summary>Proporciona métodos estáticos para crear, copiar, eliminar, mover y abrir un solo archivo, y contribuye a la creación de objetos <see cref="T:System.IO.FileStream" />.Para examinar el código fuente de .NET Framework para este tipo, consulte el Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.File.AppendAllLines(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>Anexa líneas a un archivo y, a continuación, cierra el archivo.Si el archivo especificado no existe, este método crea un archivo, escribe las líneas especificadas en él y, a continuación, lo cierra.</summary>
      <param name="path">El archivo al que se van a anexar líneas.Si el archivo no existe, se creará.</param>
      <param name="contents">Las líneas que se van a anexar al archivo.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene caracteres no válidos más de uno definidos por el <see cref="M:System.IO.Path.GetInvalidPathChars" /> (método).</exception>
      <exception cref="T:System.ArgumentNullException">Ya sea<paramref name=" path " />o <paramref name="contents" /> es null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> no es válido (por ejemplo, el directorio no existe o está en una unidad no asignada).</exception>
      <exception cref="T:System.IO.FileNotFoundException">El archivo especificado por <paramref name="path" /> no se encontró.</exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de E/S al abrir el archivo.</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> supera la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido.</exception>
      <exception cref="T:System.Security.SecurityException">El llamador no tiene permiso para escribir en el archivo.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> Especifica un archivo que es de solo lectura.o bienEsta operación no es compatible con la plataforma actual.o bien<paramref name="path" /> es un directorio.</exception>
    </member>
    <member name="M:System.IO.File.AppendAllLines(System.String,System.Collections.Generic.IEnumerable{System.String},System.Text.Encoding)">
      <summary>Anexa líneas a un archivo usando la codificación especificada y, a continuación, lo cierra.Si el archivo especificado no existe, este método crea un archivo, escribe las líneas especificadas en él y, a continuación, lo cierra.</summary>
      <param name="path">El archivo al que se van a anexar líneas.Si el archivo no existe, se creará.</param>
      <param name="contents">Las líneas que se van a anexar al archivo.</param>
      <param name="encoding">Codificación de caracteres que se utilizará.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene caracteres no válidos más de uno definidos por el <see cref="M:System.IO.Path.GetInvalidPathChars" /> (método).</exception>
      <exception cref="T:System.ArgumentNullException">Ya sea<paramref name=" path" />, <paramref name="contents" />, o <paramref name="encoding" /> es null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> no es válido (por ejemplo, el directorio no existe o está en una unidad no asignada).</exception>
      <exception cref="T:System.IO.FileNotFoundException">El archivo especificado por <paramref name="path" /> no se encontró.</exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de E/S al abrir el archivo.</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> supera la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido.</exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> Especifica un archivo que es de solo lectura.o bienEsta operación no es compatible con la plataforma actual.o bien<paramref name="path" /> es un directorio.o bienEl llamador no dispone del permiso requerido.</exception>
    </member>
    <member name="M:System.IO.File.AppendAllText(System.String,System.String)">
      <summary>Abre un archivo, le anexa la cadena especificada y, a continuación, cierra el archivo.Si el archivo no existe, este método crea un archivo, escribe la cadena especificada en él y, a continuación, lo cierra.</summary>
      <param name="path">Archivo al que se va a anexar la cadena especificada. </param>
      <param name="contents">Cadena que se va a anexar al archivo. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, el directorio no existe o se encuentra en una unidad de red no asignada). </exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de E/S al abrir el archivo. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> Especifica un archivo que es de solo lectura.o bien Esta operación no es compatible con la plataforma actual.o bien <paramref name="path" /> Especifica un directorio.o bien El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.AppendAllText(System.String,System.String,System.Text.Encoding)">
      <summary>Anexa la cadena especificada al archivo y crea el archivo si aún no existe.</summary>
      <param name="path">Archivo al que se va a anexar la cadena especificada. </param>
      <param name="contents">Cadena que se va a anexar al archivo. </param>
      <param name="encoding">Codificación de caracteres que se utilizará. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, el directorio no existe o se encuentra en una unidad de red no asignada). </exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de E/S al abrir el archivo. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> Especifica un archivo que es de solo lectura.o bien Esta operación no es compatible con la plataforma actual.o bien <paramref name="path" /> Especifica un directorio.o bien El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.AppendText(System.String)">
      <summary>Crea un <see cref="T:System.IO.StreamWriter" /> que anexa texto codificado UTF-8 a un archivo existente o a un nuevo archivo si el archivo especificado no existe.</summary>
      <returns>Un escritor de secuencias que anexa el texto con codificación UTF-8 al archivo especificado o a un nuevo archivo.</returns>
      <param name="path">Ruta de acceso del archivo al que se va a anexar. </param>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, el directorio no existe o se encuentra en una unidad de red no asignada). </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Copy(System.String,System.String)">
      <summary>Copia un archivo existente en un archivo nuevo.No se permite sobrescribir un archivo del mismo nombre.</summary>
      <param name="sourceFileName">Archivo que se va a copiar. </param>
      <param name="destFileName">Nombre del archivo de destino.No puede ser un directorio o un archivo existente.</param>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> o <paramref name="destFileName" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />.o bien <paramref name="sourceFileName" /> o <paramref name="destFileName" /> especifica un directorio. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="sourceFileName" /> o <paramref name="destFileName" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta especificada en <paramref name="sourceFileName" /> o <paramref name="destFileName" /> no es válido (por ejemplo, está en una unidad no asignada). </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="sourceFileName" /> no se encontró. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destFileName" /> existe.o bien Se produjo un error de E/S. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceFileName" /> o <paramref name="destFileName" /> está en un formato no válido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Copy(System.String,System.String,System.Boolean)">
      <summary>Copia un archivo existente en un archivo nuevo.Se permite sobrescribir un archivo del mismo nombre.</summary>
      <param name="sourceFileName">Archivo que se va a copiar. </param>
      <param name="destFileName">Nombre del archivo de destino.No puede ser un directorio.</param>
      <param name="overwrite">Es true si se puede sobrescribir el archivo de destino; en caso contrario, es false. </param>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. o bien<paramref name="destFileName" /> es de sólo lectura.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> o <paramref name="destFileName" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />.o bien <paramref name="sourceFileName" /> o <paramref name="destFileName" /> especifica un directorio. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="sourceFileName" /> o <paramref name="destFileName" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta especificada en <paramref name="sourceFileName" /> o <paramref name="destFileName" /> no es válido (por ejemplo, está en una unidad no asignada). </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="sourceFileName" /> no se encontró. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destFileName" /> existe y <paramref name="overwrite" /> es false.o bien Se produjo un error de E/S. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceFileName" /> o <paramref name="destFileName" /> está en un formato no válido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Create(System.String)">
      <summary>Crea o sobrescribe un archivo en la ruta de acceso especificada.</summary>
      <returns>
        <see cref="T:System.IO.FileStream" /> que proporciona acceso de lectura y escritura al archivo especificado en <paramref name="path" />.</returns>
      <param name="path">Ruta de acceso y nombre del archivo que se va a crear. </param>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido.o bien <paramref name="path" /> Especifica un archivo que es de solo lectura. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <exception cref="T:System.IO.IOException">Error de E/S al crear el archivo. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Create(System.String,System.Int32)">
      <summary>Crea o sobrescribe el archivo especificado.</summary>
      <returns>
        <see cref="T:System.IO.FileStream" /> con el tamaño de búfer especificado que proporciona acceso de lectura y escritura al archivo especificado en <paramref name="path" />.</returns>
      <param name="path">Nombre del archivo. </param>
      <param name="bufferSize">Número de bytes almacenados en el búfer para leer y escribir en el archivo. </param>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido.o bien <paramref name="path" /> Especifica un archivo que es de solo lectura. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <exception cref="T:System.IO.IOException">Error de E/S al crear el archivo. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Create(System.String,System.Int32,System.IO.FileOptions)">
      <summary>Crea o sobrescribe el archivo especificado, especificando un tamaño de búfer y un valor de <see cref="T:System.IO.FileOptions" /> que describe cómo crear o sobrescribir el archivo.</summary>
      <returns>Un archivo nuevo con el tamaño de búfer especificado.</returns>
      <param name="path">Nombre del archivo. </param>
      <param name="bufferSize">Número de bytes almacenados en el búfer para leer y escribir en el archivo. </param>
      <param name="options">Uno de los valores de <see cref="T:System.IO.FileOptions" /> que describe cómo crear o sobrescribir el archivo.</param>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido.o bien <paramref name="path" /> Especifica un archivo que es de solo lectura. o bien<see cref="F:System.IO.FileOptions.Encrypted" /> se ha especificado para <paramref name="options" /> y cifrado de archivos no es compatible con la plataforma actual.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <exception cref="T:System.IO.IOException">Error de E/S al crear el archivo. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido.o bien <paramref name="path" /> Especifica un archivo que es de solo lectura. </exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido.o bien <paramref name="path" /> Especifica un archivo que es de solo lectura. </exception>
    </member>
    <member name="M:System.IO.File.CreateText(System.String)">
      <summary>Crea o abre un archivo para escribir texto con codificación UTF-8.</summary>
      <returns>
        <see cref="T:System.IO.StreamWriter" /> que escribe en el archivo especificado con codificación UTF-8.</returns>
      <param name="path">Archivo que se va a abrir para escritura. </param>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Delete(System.String)">
      <summary>Elimina el archivo especificado. </summary>
      <param name="path">Nombre del archivo que se va a eliminar.No se admiten los caracteres comodín.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <exception cref="T:System.IO.IOException">El archivo especificado se está utilizando. o bienHay un identificador abierto en el archivo, y el sistema operativo es Windows XP o anterior.Este identificador abierto puede ser el resultado de la enumeración de directorios y archivos.Para obtener más información, vea Cómo: Enumerar directorios y archivos.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido.o bien El archivo es un archivo ejecutable que está en uso.o bien <paramref name="path" /> es un directorio.o bien <paramref name="path" /> Especifica un archivo de sólo lectura. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Exists(System.String)">
      <summary>Determina si existe el archivo especificado.</summary>
      <returns>Es true si el llamador tiene los permisos necesarios y <paramref name="path" /> contiene el nombre de un archivo existente; de lo contrario, es false.Este método también devuelve false si <paramref name="path" /> es null, una ruta de acceso no válida o una cadena de longitud cero.Si el llamador no tiene permisos suficientes para leer el archivo especificado, no se produce ninguna excepción y el método devuelve false, independientemente de la existencia de <paramref name="path" />.</returns>
      <param name="path">Archivo que se va a comprobar. </param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetAttributes(System.String)">
      <summary>Obtiene el <see cref="T:System.IO.FileAttributes" /> del archivo en la ruta de acceso.</summary>
      <returns>
        <see cref="T:System.IO.FileAttributes" /> del archivo en la ruta de acceso.</returns>
      <param name="path">Ruta de acceso al archivo. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> está vacío, contiene solamente espacios en blanco o contiene caracteres no válidos. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" /> Representa un archivo y no es válido, como en una unidad no asignada o el archivo no se encuentra. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> Representa un directorio y no es válido, como en una unidad no asignada o el directorio no se encuentra.</exception>
      <exception cref="T:System.IO.IOException">Este archivo lo está utilizando otro proceso.</exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetCreationTime(System.String)">
      <summary>Devuelve la fecha y hora de creación del archivo o el directorio especificados.</summary>
      <returns>Estructura <see cref="T:System.DateTime" /> que se establece en la fecha y hora de creación para el archivo o directorio especificado.Este valor se expresa en hora local.</returns>
      <param name="path">Archivo o directorio para el que se va a obtener información de fecha y hora de creación. </param>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetCreationTimeUtc(System.String)">
      <summary>Devuelve la fecha y la hora de creación, en formato de hora universal coordinada (UTC), del archivo o directorio especificado.</summary>
      <returns>Estructura <see cref="T:System.DateTime" /> que se establece en la fecha y hora de creación para el archivo o directorio especificado.Este valor se expresa en hora UTC.</returns>
      <param name="path">Archivo o directorio para el que se va a obtener información de fecha y hora de creación. </param>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastAccessTime(System.String)">
      <summary>Devuelve la fecha y hora a la que se produjo el último acceso al archivo o directorio especificado.</summary>
      <returns>Estructura <see cref="T:System.DateTime" /> que se establece en la fecha y hora a la que se produjo el último acceso al archivo o directorio especificado.Este valor se expresa en hora local.</returns>
      <param name="path">Archivo o directorio para el que se va a obtener información de fecha y hora de acceso. </param>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastAccessTimeUtc(System.String)">
      <summary>Devuelve la fecha y la hora, en formato de hora universal coordinada (UTC), a la que se produjo el último acceso al archivo o directorio especificado.</summary>
      <returns>Estructura <see cref="T:System.DateTime" /> que se establece en la fecha y hora a la que se produjo el último acceso al archivo o directorio especificado.Este valor se expresa en hora UTC.</returns>
      <param name="path">Archivo o directorio para el que se va a obtener información de fecha y hora de acceso. </param>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastWriteTime(System.String)">
      <summary>Devuelve la fecha y hora a la que se escribió por última vez en el archivo o directorio especificado.</summary>
      <returns>Estructura <see cref="T:System.DateTime" /> que se establece en la fecha y hora a la que se escribió por última vez en el archivo o directorio especificado.Este valor se expresa en hora local.</returns>
      <param name="path">Archivo o directorio para el que se va a obtener información de fecha y hora de escritura. </param>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastWriteTimeUtc(System.String)">
      <summary>Devuelve la fecha y la hora, en formato de hora universal coordinada (UTC), a la que se escribió por última vez en el archivo o directorio especificado.</summary>
      <returns>Estructura <see cref="T:System.DateTime" /> que se establece en la fecha y hora a la que se escribió por última vez en el archivo o directorio especificado.Este valor se expresa en hora UTC.</returns>
      <param name="path">Archivo o directorio para el que se va a obtener información de fecha y hora de escritura. </param>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Move(System.String,System.String)">
      <summary>Mueve un archivo especificado a una nueva ubicación, proporcionando la opción para indicar un nuevo nombre de archivo.</summary>
      <param name="sourceFileName">Nombre del archivo que se va a mover.Puede incluir una ruta de acceso relativa o absoluta.</param>
      <param name="destFileName">Nueva ruta de acceso y nombre del archivo.</param>
      <exception cref="T:System.IO.IOException">El archivo de destino ya existe.o bien<paramref name="sourceFileName" /> no se encontró. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="sourceFileName" /> o <paramref name="destFileName" /> es null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> o <paramref name="destFileName" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene caracteres no válidos como se define en <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta especificada en <paramref name="sourceFileName" /> o <paramref name="destFileName" /> no es válido, (por ejemplo, está en una unidad no asignada). </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceFileName" /> o <paramref name="destFileName" /> está en un formato no válido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode)">
      <summary>Abre un <see cref="T:System.IO.FileStream" /> en la ruta de acceso especificada con acceso de lectura y escritura.</summary>
      <returns>
        <see cref="T:System.IO.FileStream" /> abierto en el modo y ruta de acceso especificados, con acceso de lectura y escritura y de uso no compartido.</returns>
      <param name="path">Archivo que se va a abrir. </param>
      <param name="mode">Valor <see cref="T:System.IO.FileMode" /> que especifica si se crea un archivo si no existe uno y determina si el contenido de los archivos existentes se conserva o se sobrescribe. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de E/S al abrir el archivo. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> Especifica un archivo que es de solo lectura.o bien Esta operación no es compatible con la plataforma actual.o bien <paramref name="path" /> Especifica un directorio.o bien El llamador no dispone del permiso requerido. o bien<paramref name="mode" /> es <see cref="F:System.IO.FileMode.Create" /> y el archivo especificado es un archivo oculto.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> Especifica un valor no válido. </exception>
      <exception cref="T:System.IO.FileNotFoundException">El archivo especificado en <paramref name="path" /> no se encontró. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode,System.IO.FileAccess)">
      <summary>Abre un <see cref="T:System.IO.FileStream" /> en la ruta de acceso especificada, con el modo y acceso especificados.</summary>
      <returns>
        <see cref="T:System.IO.FileStream" /> de uso no compartido que proporciona acceso al archivo especificado, con el modo y el acceso especificados.</returns>
      <param name="path">Archivo que se va a abrir. </param>
      <param name="mode">Valor <see cref="T:System.IO.FileMode" /> que especifica si se crea un archivo si no existe uno y determina si el contenido de los archivos existentes se conserva o se sobrescribe. </param>
      <param name="access">Valor <see cref="T:System.IO.FileAccess" /> que especifica las operaciones que se pueden realizar en el archivo. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />.o bien <paramref name="access" /> especificado Read y <paramref name="mode" /> especificada Create, CreateNew, Truncate, o Append. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de E/S al abrir el archivo. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> Especifica un archivo que es de solo lectura y <paramref name="access" /> no es Read.o bien <paramref name="path" /> Especifica un directorio.o bien El llamador no dispone del permiso requerido. o bien<paramref name="mode" /> es <see cref="F:System.IO.FileMode.Create" /> y el archivo especificado es un archivo oculto.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> o <paramref name="access" /> especifica un valor no válido. </exception>
      <exception cref="T:System.IO.FileNotFoundException">El archivo especificado en <paramref name="path" /> no se encontró. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>Abre un <see cref="T:System.IO.FileStream" /> en la ruta de acceso especificada con el modo especificado, con acceso de lectura, escritura o ambos, y la opción de uso compartido especificada.</summary>
      <returns>
        <see cref="T:System.IO.FileStream" /> en la ruta de acceso especificada con el modo especificado, con acceso de lectura, escritura o ambos, y la opción de uso compartido especificada.</returns>
      <param name="path">Archivo que se va a abrir. </param>
      <param name="mode">Valor <see cref="T:System.IO.FileMode" /> que especifica si se crea un archivo si no existe uno y determina si el contenido de los archivos existentes se conserva o se sobrescribe. </param>
      <param name="access">Valor <see cref="T:System.IO.FileAccess" /> que especifica las operaciones que se pueden realizar en el archivo. </param>
      <param name="share">Valor <see cref="T:System.IO.FileShare" /> que especifica el tipo de acceso que otros subprocesos tienen en este archivo. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />.o bien <paramref name="access" /> especificado Read y <paramref name="mode" /> especificada Create, CreateNew, Truncate, o Append. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de E/S al abrir el archivo. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> Especifica un archivo que es de solo lectura y <paramref name="access" /> no es Read.o bien <paramref name="path" /> Especifica un directorio.o bien El llamador no dispone del permiso requerido. o bien<paramref name="mode" /> es <see cref="F:System.IO.FileMode.Create" /> y el archivo especificado es un archivo oculto.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" />, <paramref name="access" />, o <paramref name="share" /> especifica un valor no válido. </exception>
      <exception cref="T:System.IO.FileNotFoundException">El archivo especificado en <paramref name="path" /> no se encontró. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.OpenRead(System.String)">
      <summary>Abre un archivo existente para lectura.</summary>
      <returns>
        <see cref="T:System.IO.FileStream" /> de solo lectura en la ruta de acceso especificada.</returns>
      <param name="path">Archivo que se va a abrir para la lectura. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> Especifica un directorio.o bien El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.IO.FileNotFoundException">El archivo especificado en <paramref name="path" /> no se encontró. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de E/S al abrir el archivo. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.OpenText(System.String)">
      <summary>Abre un archivo de texto existente con codificación UTF-8 para lectura.</summary>
      <returns>
        <see cref="T:System.IO.StreamReader" /> en la ruta de acceso especificada.</returns>
      <param name="path">Archivo que se va a abrir para la lectura. </param>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <exception cref="T:System.IO.FileNotFoundException">El archivo especificado en <paramref name="path" /> no se encontró. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.OpenWrite(System.String)">
      <summary>Abre un archivo existente o crea un nuevo archivo para escribir en él.</summary>
      <returns>Objeto <see cref="T:System.IO.FileStream" /> no compartido en la ruta de acceso especificada con acceso <see cref="F:System.IO.FileAccess.Write" />.</returns>
      <param name="path">Archivo que se va a abrir para escritura. </param>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido.o bien <paramref name="path" /> Especifica un archivo de sólo lectura o un directorio. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllBytes(System.String)">
      <summary>Abre un archivo binario, lee su contenido, lo introduce en una matriz de bytes y, a continuación, cierra el archivo.</summary>
      <returns>Una matriz de bytes con el contenido del archivo.</returns>
      <param name="path">Archivo que se abre para la lectura. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de E/S al abrir el archivo. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Esta operación no es compatible con la plataforma actual.o bien <paramref name="path" /> Especifica un directorio.o bien El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.IO.FileNotFoundException">El archivo especificado en <paramref name="path" /> no se encontró. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllLines(System.String)">
      <summary>Abre un archivo de texto, lee todas sus líneas y, a continuación, cierra el archivo.</summary>
      <returns>Una matriz de cadenas que contiene todas las líneas del archivo.</returns>
      <param name="path">Archivo que se abre para la lectura. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de E/S al abrir el archivo. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> Especifica un archivo que es de solo lectura.o bien Esta operación no es compatible con la plataforma actual.o bien <paramref name="path" /> Especifica un directorio.o bien El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.IO.FileNotFoundException">El archivo especificado en <paramref name="path" /> no se encontró. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllLines(System.String,System.Text.Encoding)">
      <summary>Abre un archivo, lee todas sus líneas con la codificación especificada y, a continuación, cierra el archivo.</summary>
      <returns>Una matriz de cadenas que contiene todas las líneas del archivo.</returns>
      <param name="path">Archivo que se abre para la lectura. </param>
      <param name="encoding">Codificación aplicada al contenido del archivo. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de E/S al abrir el archivo. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> Especifica un archivo que es de solo lectura.o bien Esta operación no es compatible con la plataforma actual.o bien <paramref name="path" /> Especifica un directorio.o bien El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.IO.FileNotFoundException">El archivo especificado en <paramref name="path" /> no se encontró. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllText(System.String)">
      <summary>Abre un archivo de texto, lee todas sus líneas y, a continuación, cierra el archivo.</summary>
      <returns>Cadena que contiene todas las líneas del archivo.</returns>
      <param name="path">Archivo que se abre para la lectura. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de E/S al abrir el archivo. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> Especifica un archivo que es de solo lectura.o bien Esta operación no es compatible con la plataforma actual.o bien <paramref name="path" /> Especifica un directorio.o bien El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.IO.FileNotFoundException">El archivo especificado en <paramref name="path" /> no se encontró. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllText(System.String,System.Text.Encoding)">
      <summary>Abre un archivo, lee todas sus líneas con la codificación especificada y, a continuación, cierra el archivo.</summary>
      <returns>Cadena que contiene todas las líneas del archivo.</returns>
      <param name="path">Archivo que se abre para la lectura. </param>
      <param name="encoding">Codificación aplicada al contenido del archivo. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de E/S al abrir el archivo. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> Especifica un archivo que es de solo lectura.o bien Esta operación no es compatible con la plataforma actual.o bien <paramref name="path" /> Especifica un directorio.o bien El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.IO.FileNotFoundException">El archivo especificado en <paramref name="path" /> no se encontró. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadLines(System.String)">
      <summary>Lee las líneas de un archivo.</summary>
      <returns>Todas las líneas del archivo o las líneas que son el resultado de una consulta.</returns>
      <param name="path">Archivo que se va a leer.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos definidos por el <see cref="M:System.IO.Path.GetInvalidPathChars" /> (método).</exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> no es válido (por ejemplo, está en una unidad no asignada).</exception>
      <exception cref="T:System.IO.FileNotFoundException">El archivo especificado por <paramref name="path" /> no se encontró.</exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de E/S al abrir el archivo.</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> supera la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> Especifica un archivo que es de solo lectura.o bienEsta operación no es compatible con la plataforma actual.o bien<paramref name="path" /> es un directorio.o bienEl llamador no dispone del permiso requerido.</exception>
    </member>
    <member name="M:System.IO.File.ReadLines(System.String,System.Text.Encoding)">
      <summary>Lee las líneas de un archivo que tiene una codificación especificada.</summary>
      <returns>Todas las líneas del archivo o las líneas que son el resultado de una consulta.</returns>
      <param name="path">Archivo que se va a leer.</param>
      <param name="encoding">Codificación aplicada al contenido del archivo. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por la <see cref="M:System.IO.Path.GetInvalidPathChars" /> (método).</exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> no es válido (por ejemplo, está en una unidad no asignada).</exception>
      <exception cref="T:System.IO.FileNotFoundException">El archivo especificado por <paramref name="path" /> no se encontró.</exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de E/S al abrir el archivo.</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> supera la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> Especifica un archivo que es de solo lectura.o bienEsta operación no es compatible con la plataforma actual.o bien<paramref name="path" /> es un directorio.o bienEl llamador no dispone del permiso requerido.</exception>
    </member>
    <member name="M:System.IO.File.SetAttributes(System.String,System.IO.FileAttributes)">
      <summary>Establece el <see cref="T:System.IO.FileAttributes" /> especificado del archivo en la ruta de acceso especificada.</summary>
      <param name="path">Ruta de acceso al archivo. </param>
      <param name="fileAttributes">Combinación bit a bit de los valores de la enumeración. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> está vacío, contiene solamente espacios en blanco, contiene caracteres no válidos, o el atributo de archivo no es válido. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <exception cref="T:System.IO.FileNotFoundException">No se puede encontrar el archivo.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> Especifica un archivo que es de solo lectura.o bien Esta operación no es compatible con la plataforma actual.o bien <paramref name="path" /> Especifica un directorio.o bien El llamador no dispone del permiso requerido.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetCreationTime(System.String,System.DateTime)">
      <summary>Establece la fecha y la hora a la que se creó el archivo.</summary>
      <param name="path">Archivo para el que se va a establecer información de fecha y hora de creación. </param>
      <param name="creationTime">
        <see cref="T:System.DateTime" /> que contiene el valor que se va a establecer para la fecha y hora de creación de <paramref name="path" />.Este valor se expresa en hora local.</param>
      <exception cref="T:System.IO.FileNotFoundException">No se encontró la ruta de acceso especificada. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de E/S al realizar la operación. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> Especifica un valor fuera del intervalo de fechas, horas o ambas permitido para esta operación. </exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetCreationTimeUtc(System.String,System.DateTime)">
      <summary>Establece la fecha y la hora, en formato de hora universal coordinada (UTC), a la que se creó el archivo.</summary>
      <param name="path">Archivo para el que se va a establecer información de fecha y hora de creación. </param>
      <param name="creationTimeUtc">
        <see cref="T:System.DateTime" /> que contiene el valor que se va a establecer para la fecha y hora de creación de <paramref name="path" />.Este valor se expresa en hora UTC.</param>
      <exception cref="T:System.IO.FileNotFoundException">No se encontró la ruta de acceso especificada. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de E/S al realizar la operación. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> Especifica un valor fuera del intervalo de fechas, horas o ambas permitido para esta operación. </exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastAccessTime(System.String,System.DateTime)">
      <summary>Establece la fecha y la hora en que se obtuvo acceso por última vez al archivo especificado.</summary>
      <param name="path">Archivo para el que se va a establecer información de fecha y hora de acceso. </param>
      <param name="lastAccessTime">
        <see cref="T:System.DateTime" /> que contiene el valor que se va a establecer para la fecha y hora de último acceso de <paramref name="path" />.Este valor se expresa en hora local.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.FileNotFoundException">No se encontró la ruta de acceso especificada. </exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTime" /> Especifica un valor fuera del intervalo de fechas u horas permitidos para esta operación.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastAccessTimeUtc(System.String,System.DateTime)">
      <summary>Establece la fecha y la hora, en formato de hora universal coordinada (UTC), a la que se produjo el último acceso al archivo especificado.</summary>
      <param name="path">Archivo para el que se va a establecer información de fecha y hora de acceso. </param>
      <param name="lastAccessTimeUtc">
        <see cref="T:System.DateTime" /> que contiene el valor que se va a establecer para la fecha y hora de último acceso de <paramref name="path" />.Este valor se expresa en hora UTC.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.FileNotFoundException">No se encontró la ruta de acceso especificada. </exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTimeUtc" /> Especifica un valor fuera del intervalo de fechas u horas permitidos para esta operación.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastWriteTime(System.String,System.DateTime)">
      <summary>Establece la fecha y la hora en que escribió por última vez en el archivo especificado.</summary>
      <param name="path">Archivo para el que se va a establecer información de fecha y hora. </param>
      <param name="lastWriteTime">
        <see cref="T:System.DateTime" /> que contiene el valor que se va a establecer para la fecha y hora de última escritura de <paramref name="path" />.Este valor se expresa en hora local.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.FileNotFoundException">No se encontró la ruta de acceso especificada. </exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTime" /> Especifica un valor fuera del intervalo de fechas u horas permitidos para esta operación.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastWriteTimeUtc(System.String,System.DateTime)">
      <summary>Establece la fecha y la hora, en formato de hora universal coordinada (UTC), a la que se escribió por última vez en el archivo especificado.</summary>
      <param name="path">Archivo para el que se va a establecer información de fecha y hora. </param>
      <param name="lastWriteTimeUtc">
        <see cref="T:System.DateTime" /> que contiene el valor que se va a establecer para la fecha y hora de última escritura de <paramref name="path" />.Este valor se expresa en hora UTC.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.FileNotFoundException">No se encontró la ruta de acceso especificada. </exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTimeUtc" /> Especifica un valor fuera del intervalo de fechas u horas permitidos para esta operación.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.WriteAllBytes(System.String,System.Byte[])">
      <summary>Crea un archivo nuevo, escribe en él la matriz de bytes especificada y, a continuación, lo cierra.Si el archivo de destino ya existe, se sobrescribe.</summary>
      <param name="path">Archivo en el que se va a escribir. </param>
      <param name="bytes">Bytes que se van a escribir en el archivo. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> es null o la matriz de bytes está vacía. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de E/S al abrir el archivo. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> Especifica un archivo que es de solo lectura.o bien Esta operación no es compatible con la plataforma actual.o bien <paramref name="path" /> Especifica un directorio.o bien El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.WriteAllLines(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>Crea un archivo nuevo, escribe una colección de cadenas en él y lo cierra.</summary>
      <param name="path">Archivo en el que se va a escribir.</param>
      <param name="contents">Líneas que se van a escribir en el archivo.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos definidos por el <see cref="M:System.IO.Path.GetInvalidPathChars" /> (método).</exception>
      <exception cref="T:System.ArgumentNullException">Ya sea<paramref name=" path " />o <paramref name="contents" /> es null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> no es válido (por ejemplo, está en una unidad no asignada).</exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de E/S al abrir el archivo.</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> supera la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido.</exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> Especifica un archivo que es de solo lectura.o bienEsta operación no es compatible con la plataforma actual.o bien<paramref name="path" /> es un directorio.o bienEl llamador no dispone del permiso requerido.</exception>
    </member>
    <member name="M:System.IO.File.WriteAllLines(System.String,System.Collections.Generic.IEnumerable{System.String},System.Text.Encoding)">
      <summary>Crea un archivo nuevo usando la codificación especificada, escribe en él la colección de cadenas especificada y, a continuación, lo cierra.</summary>
      <param name="path">Archivo en el que se va a escribir.</param>
      <param name="contents">Líneas que se van a escribir en el archivo.</param>
      <param name="encoding">Codificación de caracteres que se utilizará.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos definidos por el <see cref="M:System.IO.Path.GetInvalidPathChars" /> (método).</exception>
      <exception cref="T:System.ArgumentNullException">Ya sea<paramref name=" path" />,<paramref name=" contents" />, o <paramref name="encoding" /> es null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> no es válido (por ejemplo, está en una unidad no asignada).</exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de E/S al abrir el archivo.</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> supera la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido.</exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> Especifica un archivo que es de solo lectura.o bienEsta operación no es compatible con la plataforma actual.o bien<paramref name="path" /> es un directorio.o bienEl llamador no dispone del permiso requerido.</exception>
    </member>
    <member name="M:System.IO.File.WriteAllText(System.String,System.String)">
      <summary>Crea un archivo nuevo, escribe la cadena especificada en él y, a continuación, lo cierra.Si el archivo de destino ya existe, se sobrescribe.</summary>
      <param name="path">Archivo en el que se va a escribir. </param>
      <param name="contents">Cadena que se va a escribir en el archivo. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> es null o <paramref name="contents" /> está vacía.  </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de E/S al abrir el archivo. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> Especifica un archivo que es de solo lectura.o bien Esta operación no es compatible con la plataforma actual.o bien <paramref name="path" /> Especifica un directorio.o bien El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.WriteAllText(System.String,System.String,System.Text.Encoding)">
      <summary>Crea un archivo nuevo, escribe en él la cadena especificada con la codificación especificada y, a continuación, lo cierra.Si el archivo de destino ya existe, se sobrescribe.</summary>
      <param name="path">Archivo en el que se va a escribir. </param>
      <param name="contents">Cadena que se va a escribir en el archivo. </param>
      <param name="encoding">La codificación que se aplica a la cadena.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o más caracteres no válidos, tal como se define por <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> es null o <paramref name="contents" /> está vacía. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de E/S al abrir el archivo. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> Especifica un archivo que es de solo lectura.o bien Esta operación no es compatible con la plataforma actual.o bien <paramref name="path" /> Especifica un directorio.o bien El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> está en un formato no válido. </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.IO.FileInfo">
      <summary>Proporciona propiedades y métodos de instancia para crear, copiar, eliminar, mover y abrir archivos y contribuye a la creación de objetos <see cref="T:System.IO.FileStream" />.Esta clase no puede heredarse.Para examinar el código fuente de .NET Framework para este tipo, consulte el Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileInfo.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.IO.FileInfo" />, que actúa como contenedor de una ruta de archivos.</summary>
      <param name="fileName">El nombre completo del nuevo archivo o el nombre de archivo relativo.No finalice la ruta de acceso con el carácter separador de directorios.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="fileName" /> es null. </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentException">El nombre de archivo está vacío, contiene únicamente espacios en blanco o contiene caracteres no válidos. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Acceso a <paramref name="fileName" /> denegado. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="fileName" /> contiene dos puntos (:) dentro de la cadena. </exception>
    </member>
    <member name="M:System.IO.FileInfo.AppendText">
      <summary>Crea un <see cref="T:System.IO.StreamWriter" /> que agrega texto al archivo representado por esta instancia de <see cref="T:System.IO.FileInfo" />.</summary>
      <returns>Un nuevo objeto StreamWriter.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.CopyTo(System.String)">
      <summary>Copia un archivo existente en un archivo nuevo, impidiendo que se sobrescriba el archivo existente.</summary>
      <returns>Nuevo archivo con una ruta de acceso completa.</returns>
      <param name="destFileName">El nombre del nuevo archivo destino de la copia. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destFileName" /> está vacío, contiene solamente espacios en blanco o contiene caracteres no válidos. </exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error o ya existe el archivo de destino. </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="destFileName" /> es null. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Se pasa una ruta de directorio o el archivo se mueve a una unidad diferente. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">El directorio especificado en <paramref name="destFileName" /> no existe.</exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destFileName" /> contiene dos puntos (:) dentro de la cadena pero no especifica el volumen. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.CopyTo(System.String,System.Boolean)">
      <summary>Copia un archivo existente en un archivo nuevo, permitiendo que se sobrescriba el archivo existente.</summary>
      <returns>Un nuevo archivo, o una copia sobrescrita de un archivo existente si <paramref name="overwrite" /> es true.Si existe el archivo y <paramref name="overwrite" /> es false, se produce una excepción <see cref="T:System.IO.IOException" />.</returns>
      <param name="destFileName">El nombre del nuevo archivo destino de la copia. </param>
      <param name="overwrite">true si se permite que se sobrescriba el archivo existente; en caso contrario, false. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destFileName" /> está vacío, contiene solamente espacios en blanco o contiene caracteres no válidos. </exception>
      <exception cref="T:System.IO.IOException">Se produce un error o ya existe el archivo de destino y <paramref name="overwrite" /> es false. </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="destFileName" /> es null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">El directorio especificado en <paramref name="destFileName" /> no existe.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Se pasa una ruta de directorio o el archivo se mueve a una unidad diferente. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destFileName" /> contiene dos puntos (:) dentro de la cadena. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Create">
      <summary>Crea un archivo.</summary>
      <returns>Un nuevo archivo.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.CreateText">
      <summary>Crea un <see cref="T:System.IO.StreamWriter" /> que escribe un nuevo archivo de texto.</summary>
      <returns>Un nuevo objeto StreamWriter.</returns>
      <exception cref="T:System.UnauthorizedAccessException">El nombre de archivo es un directorio. </exception>
      <exception cref="T:System.IO.IOException">El disco es de solo lectura. </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Delete">
      <summary>Elimina de forma permanente un archivo.</summary>
      <exception cref="T:System.IO.IOException">El archivo de destino se abre o se asigna a memoria en un equipo con Microsoft Windows NT.o bienHay un identificador abierto en el archivo, y el sistema operativo es Windows XP o anterior.Este identificador abierto puede ser el resultado de la enumeración de directorios y archivos.Para obtener más información, vea Cómo: Enumerar directorios y archivos.</exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.UnauthorizedAccessException">La ruta de acceso es un directorio. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.Directory">
      <summary>Obtiene una instancia del directorio principal.</summary>
      <returns>Objeto <see cref="T:System.IO.DirectoryInfo" /> que representa el directorio principal de este archivo.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida como, por ejemplo, una ruta de una unidad no asignada. </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.DirectoryName">
      <summary>Obtiene una cadena que representa la ruta de acceso completa del directorio.</summary>
      <returns>Cadena que representa la ruta de acceso completa del directorio.</returns>
      <exception cref="T:System.ArgumentNullException">null se pasó en el nombre del directorio. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso completa tiene 260 o más caracteres.</exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.Exists">
      <summary>Obtiene un valor que indica si existe un archivo.</summary>
      <returns>true si existe el archivo; false si no existe el archivo o es un directorio.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileInfo.IsReadOnly">
      <summary>Obtiene o establece un valor que determina si el archivo actual es de solo lectura.</summary>
      <returns>Es true si el archivo actual es de solo lectura; en caso contrario, es false.</returns>
      <exception cref="T:System.IO.FileNotFoundException">El archivo descrito por el actual <see cref="T:System.IO.FileInfo" /> no se encontró el objeto.</exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de E/S al abrir el archivo.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Esta operación no es compatible con la plataforma actual.o bien El llamador no dispone del permiso requerido.</exception>
      <exception cref="T:System.ArgumentException">El usuario no tiene permiso de escritura, pero se ha intentado establecer esta propiedad en false.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileInfo.Length">
      <summary>Obtiene el tamaño, en bytes, del archivo actual.</summary>
      <returns>Tamaño del archivo actual en bytes.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> no se puede actualizar el estado del archivo o directorio. </exception>
      <exception cref="T:System.IO.FileNotFoundException">El archivo no existe.o bien El Length propiedad se llama para un directorio. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileInfo.MoveTo(System.String)">
      <summary>Mueve un archivo especificado a una nueva ubicación, proporcionando la opción para indicar un nuevo nombre de archivo.</summary>
      <param name="destFileName">La ruta de acceso a la que se mueve el archivo, que puede especificar un nombre de archivo distinto. </param>
      <exception cref="T:System.IO.IOException">Se produce un error de E/S, como por ejemplo, que el archivo de destino ya existe o que el dispositivo de destino no está listo. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="destFileName" /> es null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="destFileName" /> está vacío, contiene solamente espacios en blanco o contiene caracteres no válidos. </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="destFileName" /> es de sólo lectura o es un directorio. </exception>
      <exception cref="T:System.IO.FileNotFoundException">No se encuentra el archivo. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida como, por ejemplo, una ruta de una unidad no asignada. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destFileName" /> contiene dos puntos (:) dentro de la cadena. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.Name">
      <summary>Obtiene el nombre del archivo.</summary>
      <returns>Nombre del archivo.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode)">
      <summary>Abre un archivo en el modo especificado.</summary>
      <returns>Archivo abierto en el modo especificado, con acceso de lectura y escritura y de uso no compartido.</returns>
      <param name="mode">Constante <see cref="T:System.IO.FileMode" /> que especifica el modo (por ejemplo, Open o Append) en que se abre el archivo. </param>
      <exception cref="T:System.IO.FileNotFoundException">No se encuentra el archivo. </exception>
      <exception cref="T:System.UnauthorizedAccessException">El archivo es de solo lectura o es un directorio. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida como, por ejemplo, una ruta de una unidad no asignada. </exception>
      <exception cref="T:System.IO.IOException">El archivo ya está abierto. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode,System.IO.FileAccess)">
      <summary>Abre un archivo en el modo especificado, con acceso de lectura o escritura (o ambos).</summary>
      <returns>Objeto <see cref="T:System.IO.FileStream" /> abierto en el modo y con el acceso especificados y de uso no compartido.</returns>
      <param name="mode">Constante <see cref="T:System.IO.FileMode" /> que especifica el modo (por ejemplo, Open o Append) en que se abre el archivo. </param>
      <param name="access">Constante <see cref="T:System.IO.FileAccess" /> que especifica si el archivo se abre con el acceso de archivo Read, Write o ReadWrite. </param>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.IO.FileNotFoundException">No se encuentra el archivo. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> es de sólo lectura o es un directorio. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida como, por ejemplo, una ruta de una unidad no asignada. </exception>
      <exception cref="T:System.IO.IOException">El archivo ya está abierto. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>Abre un archivo en el modo especificado, con acceso de lectura o escritura (o ambos) y la opción de uso compartido especificada.</summary>
      <returns>Objeto <see cref="T:System.IO.FileStream" /> abierto en el modo y con el acceso especificados y con opciones de uso compartido.</returns>
      <param name="mode">Constante <see cref="T:System.IO.FileMode" /> que especifica el modo (por ejemplo, Open o Append) en que se abre el archivo. </param>
      <param name="access">Constante <see cref="T:System.IO.FileAccess" /> que especifica si el archivo se abre con el acceso de archivo Read, Write o ReadWrite. </param>
      <param name="share">Constante <see cref="T:System.IO.FileShare" /> que especifica el tipo de acceso que otros objetos FileStream tienen en este archivo. </param>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.IO.FileNotFoundException">No se encuentra el archivo. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> es de sólo lectura o es un directorio. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida como, por ejemplo, una ruta de una unidad no asignada. </exception>
      <exception cref="T:System.IO.IOException">El archivo ya está abierto. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.OpenRead">
      <summary>Crea un objeto <see cref="T:System.IO.FileStream" /> de solo lectura.</summary>
      <returns>Nuevo objeto <see cref="T:System.IO.FileStream" /> de solo lectura.</returns>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> es de sólo lectura o es un directorio. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida como, por ejemplo, una ruta de una unidad no asignada. </exception>
      <exception cref="T:System.IO.IOException">El archivo ya está abierto. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.OpenText">
      <summary>Crea un <see cref="T:System.IO.StreamReader" /> con la codificación UTF8 que lee de un archivo de texto existente.</summary>
      <returns>Nuevo StreamReader con la codificación UTF8.</returns>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.IO.FileNotFoundException">No se encuentra el archivo. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> es de sólo lectura o es un directorio. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida como, por ejemplo, una ruta de una unidad no asignada. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.OpenWrite">
      <summary>Crea un <see cref="T:System.IO.FileStream" /> de solo escritura.</summary>
      <returns>Objeto no compartido de solo escritura <see cref="T:System.IO.FileStream" /> para un archivo nuevo o existente.</returns>
      <exception cref="T:System.UnauthorizedAccessException">La ruta de acceso especificada al crear una instancia de la <see cref="T:System.IO.FileInfo" /> objeto es de solo lectura o es un directorio.  </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada al crear una instancia de la <see cref="T:System.IO.FileInfo" /> objeto es válida, como en una unidad no asignada. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.ToString">
      <summary>Devuelve la ruta de acceso como una cadena.</summary>
      <returns>Cadena que representa la ruta de acceso.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.FileOptions">
      <summary>Representa opciones avanzadas para crear un objeto <see cref="T:System.IO.FileStream" />.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.FileOptions.Asynchronous">
      <summary>Indica que un archivo puede utilizarse para las operaciones de lectura y escritura asincrónicas. </summary>
    </member>
    <member name="F:System.IO.FileOptions.DeleteOnClose">
      <summary>Indica que un archivo se elimina automáticamente cuando ya no está en uso.</summary>
    </member>
    <member name="F:System.IO.FileOptions.Encrypted">
      <summary>Indica que un archivo está cifrado y que sólo se puede descifrar utilizando la misma cuenta de usuario que la utilizada para el cifrado.</summary>
    </member>
    <member name="F:System.IO.FileOptions.None">
      <summary>Indica que no se deben usar opciones adicionales al crear un objeto <see cref="T:System.IO.FileStream" /> .</summary>
    </member>
    <member name="F:System.IO.FileOptions.RandomAccess">
      <summary>Indica que el acceso al archivo se realiza aleatoriamente.El sistema puede considerar que esto es una sugerencia para optimizar el almacenamiento en caché del archivo.</summary>
    </member>
    <member name="F:System.IO.FileOptions.SequentialScan">
      <summary>Indica que el acceso al archivo debe ser secuencial de principio a fin.El sistema puede considerar que esto es una sugerencia para optimizar el almacenamiento en caché del archivo.Si una aplicación mueve el puntero de archivo para obtener acceso aleatorio, puede que no se produzca un almacenamiento en caché óptimo; no obstante, la operación correcta sigue garantizada.</summary>
    </member>
    <member name="F:System.IO.FileOptions.WriteThrough">
      <summary>Indica que el sistema debe escribir en una caché intermedia e ir directamente al disco.</summary>
    </member>
    <member name="T:System.IO.FileStream">
      <summary>Proporciona un <see cref="T:System.IO.Stream" /> para un archivo, lo que permite operaciones de lectura y escritura sincrónica y asincrónica.Para examinar el código fuente de .NET Framework para este tipo, consulte el Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.#ctor(Microsoft.Win32.SafeHandles.SafeFileHandle,System.IO.FileAccess)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.IO.FileStream" /> para el identificador de archivo especificado, con el permiso de lectura y escritura especificado. </summary>
      <param name="handle">Identificador de archivo para el archivo que el objeto FileStream actual va a encapsular. </param>
      <param name="access">Constante que establece las propiedades <see cref="P:System.IO.FileStream.CanRead" /> y <see cref="P:System.IO.FileStream.CanWrite" /> del objeto FileStream. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="access" /> no es un campo de <see cref="T:System.IO.FileAccess" />. </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de E/S, como un error de disco.o bienSe ha cerrado la secuencia. </exception>
      <exception cref="T:System.UnauthorizedAccessException">El <paramref name="access" /> solicitado no está permitido por el sistema operativo para el identificador de archivo especificado, como cuando <paramref name="access" /> es Write o ReadWrite y se establece el identificador de archivo para acceso de sólo lectura. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(Microsoft.Win32.SafeHandles.SafeFileHandle,System.IO.FileAccess,System.Int32)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.IO.FileStream" /> para el identificador de archivo especificado, con el tamaño de búfer y el permiso de lectura y escritura especificados.</summary>
      <param name="handle">Identificador de archivo para el archivo que el objeto FileStream actual va a encapsular. </param>
      <param name="access">Constante <see cref="T:System.IO.FileAccess" /> que establece las propiedades <see cref="P:System.IO.FileStream.CanRead" /> y <see cref="P:System.IO.FileStream.CanWrite" /> del objeto FileStream. </param>
      <param name="bufferSize">Valor <see cref="T:System.Int32" /> positivo mayor que 0 que indica el tamaño del búfer.El tamaño de búfer predeterminado es 4096.</param>
      <exception cref="T:System.ArgumentException">El <paramref name="handle" /> parámetro es un identificador no válido.o bienEl <paramref name="handle" /> parámetro es un identificador sincrónico y se utilizó de forma asincrónica. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El <paramref name="bufferSize" /> parámetro es negativo. </exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de E/S, como un error de disco.o bienSe ha cerrado la secuencia.  </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.UnauthorizedAccessException">El <paramref name="access" /> solicitado no está permitido por el sistema operativo para el identificador de archivo especificado, como cuando <paramref name="access" /> es Write o ReadWrite y se establece el identificador de archivo para acceso de sólo lectura. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(Microsoft.Win32.SafeHandles.SafeFileHandle,System.IO.FileAccess,System.Int32,System.Boolean)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.IO.FileStream" /> para el identificador de archivo especificado, y con el permiso de lectura y escritura, el tamaño de búfer y el estado sincrónico o asincrónico especificados.</summary>
      <param name="handle">Identificador de archivo para el archivo que este objeto FileStream va a encapsular. </param>
      <param name="access">Constante que establece las propiedades <see cref="P:System.IO.FileStream.CanRead" /> y <see cref="P:System.IO.FileStream.CanWrite" /> del objeto FileStream. </param>
      <param name="bufferSize">Valor <see cref="T:System.Int32" /> positivo mayor que 0 que indica el tamaño del búfer.El tamaño de búfer predeterminado es 4096.</param>
      <param name="isAsync">Es true si se abrió el identificador de forma asincrónica (es decir, en modo de E/S superpuesta); de lo contrario, es false. </param>
      <exception cref="T:System.ArgumentException">El <paramref name="handle" /> parámetro es un identificador no válido.o bienEl <paramref name="handle" /> parámetro es un identificador sincrónico y se utilizó de forma asincrónica. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El <paramref name="bufferSize" /> parámetro es negativo. </exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de E/S, como un error de disco.o bienSe ha cerrado la secuencia.  </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.UnauthorizedAccessException">El <paramref name="access" /> solicitado no está permitido por el sistema operativo para el identificador de archivo especificado, como cuando <paramref name="access" /> es Write o ReadWrite y se establece el identificador de archivo para acceso de sólo lectura. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.IO.FileStream" /> con el modo de creación y la ruta de acceso especificados.</summary>
      <param name="path">Ruta de acceso relativa o absoluta del archivo que va a encapsular el objeto FileStream actual. </param>
      <param name="mode">Constante que determina cómo abrir o crear el archivo. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena vacía (""), contiene sólo espacios en blanco o contiene uno o más caracteres no válidos. o bien<paramref name="path" /> hace referencia a un dispositivo no es un archivo, como "figurar:", "com1:", "lpt1:", etc..en un entorno NTFS.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> hace referencia a un dispositivo no es un archivo, como "figurar:", "com1:", "lpt1:", etc..en un entorno distinto de NTFS.</exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.IO.FileNotFoundException">No se encuentra el archivo, como cuando <paramref name="mode" /> es FileMode.Truncate o FileMode.Open, y el archivo especificado por <paramref name="path" /> no existe.El archivo ya debe existir en estos modos.</exception>
      <exception cref="T:System.IO.IOException">Error de E/S, como cuando se especifica FileMode.CreateNew cuando el archivo especificado por <paramref name="path" /> ya existe, se ha producido.o bienSe ha cerrado la secuencia. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida como, por ejemplo, una ruta de una unidad no asignada. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> contiene un valor no válido. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.IO.FileStream" /> con el permiso de lectura y escritura, el modo de creación y la ruta de acceso especificados.</summary>
      <param name="path">Ruta de acceso relativa o absoluta del archivo que va a encapsular el objeto FileStream actual. </param>
      <param name="mode">Constante que determina cómo abrir o crear el archivo. </param>
      <param name="access">Constante que determina cómo puede obtener acceso al archivo el objeto FileStream.Esto también determina los valores que devuelven las propiedades <see cref="P:System.IO.FileStream.CanRead" /> y <see cref="P:System.IO.FileStream.CanWrite" /> del objeto FileStream.<see cref="P:System.IO.FileStream.CanSeek" /> es true si <paramref name="path" /> especifica un archivo de disco.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena vacía (""), contiene sólo espacios en blanco o contiene uno o más caracteres no válidos. o bien<paramref name="path" /> hace referencia a un dispositivo no es un archivo, como "figurar:", "com1:", "lpt1:", etc..en un entorno NTFS.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> hace referencia a un dispositivo no es un archivo, como "figurar:", "com1:", "lpt1:", etc..en un entorno distinto de NTFS.</exception>
      <exception cref="T:System.IO.FileNotFoundException">No se encuentra el archivo, como cuando <paramref name="mode" /> es FileMode.Truncate o FileMode.Open, y el archivo especificado por <paramref name="path" /> no existe.El archivo ya debe existir en estos modos.</exception>
      <exception cref="T:System.IO.IOException">Error de E/S, como cuando se especifica FileMode.CreateNew cuando el archivo especificado por <paramref name="path" /> ya existe, se ha producido. o bienSe ha cerrado la secuencia.</exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida como, por ejemplo, una ruta de una unidad no asignada. </exception>
      <exception cref="T:System.UnauthorizedAccessException">El <paramref name="access" /> solicitado no permitido por el sistema operativo para el objeto <paramref name="path" />, como cuando <paramref name="access" /> es Write o ReadWrite y el archivo o directorio está establecido para el acceso de solo lectura. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> contiene un valor no válido. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.IO.FileStream" /> con el permiso de uso compartido, el permiso de lectura y escritura, el modo de creación y la ruta de acceso especificados.</summary>
      <param name="path">Ruta de acceso relativa o absoluta del archivo que va a encapsular el objeto FileStream actual. </param>
      <param name="mode">Constante que determina cómo abrir o crear el archivo. </param>
      <param name="access">Constante que determina cómo puede obtener acceso al archivo el objeto FileStream.Esto también determina los valores que devuelven las propiedades <see cref="P:System.IO.FileStream.CanRead" /> y <see cref="P:System.IO.FileStream.CanWrite" /> del objeto FileStream.<see cref="P:System.IO.FileStream.CanSeek" /> es true si <paramref name="path" /> especifica un archivo de disco.</param>
      <param name="share">Constante que determina cómo compartirán el archivo los procesos. </param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena vacía (""), contiene sólo espacios en blanco o contiene uno o más caracteres no válidos. o bien<paramref name="path" /> hace referencia a un dispositivo no es un archivo, como "figurar:", "com1:", "lpt1:", etc..en un entorno NTFS.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> hace referencia a un dispositivo no es un archivo, como "figurar:", "com1:", "lpt1:", etc..en un entorno distinto de NTFS.</exception>
      <exception cref="T:System.IO.FileNotFoundException">No se encuentra el archivo, como cuando <paramref name="mode" /> es FileMode.Truncate o FileMode.Open, y el archivo especificado por <paramref name="path" /> no existe.El archivo ya debe existir en estos modos.</exception>
      <exception cref="T:System.IO.IOException">Error de E/S, como cuando se especifica FileMode.CreateNew cuando el archivo especificado por <paramref name="path" /> ya existe, se ha producido. o bienEl sistema está ejecutando Windows 98 o Windows 98 Second Edition y <paramref name="share" /> se establece en FileShare.Delete.o bienSe ha cerrado la secuencia.</exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida como, por ejemplo, una ruta de una unidad no asignada. </exception>
      <exception cref="T:System.UnauthorizedAccessException">El <paramref name="access" /> solicitado no permitido por el sistema operativo para el objeto <paramref name="path" />, como cuando <paramref name="access" /> es Write o ReadWrite y el archivo o directorio está establecido para el acceso de solo lectura. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> contiene un valor no válido. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Int32)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.IO.FileStream" /> con el tamaño de búfer, el permiso de lectura y escritura y de uso compartido, el modo de creación y la ruta de acceso especificados.</summary>
      <param name="path">Ruta de acceso relativa o absoluta del archivo que va a encapsular el objeto FileStream actual. </param>
      <param name="mode">Constante que determina cómo abrir o crear el archivo. </param>
      <param name="access">Constante que determina cómo puede obtener acceso al archivo el objeto FileStream.Esto también determina los valores que devuelven las propiedades <see cref="P:System.IO.FileStream.CanRead" /> y <see cref="P:System.IO.FileStream.CanWrite" /> del objeto FileStream.<see cref="P:System.IO.FileStream.CanSeek" /> es true si <paramref name="path" /> especifica un archivo de disco.</param>
      <param name="share">Constante que determina cómo compartirán el archivo los procesos. </param>
      <param name="bufferSize">Valor <see cref="T:System.Int32" /> positivo mayor que 0 que indica el tamaño del búfer.El tamaño de búfer predeterminado es 4096.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena vacía (""), contiene sólo espacios en blanco o contiene uno o más caracteres no válidos. o bien<paramref name="path" /> hace referencia a un dispositivo no es un archivo, como "figurar:", "com1:", "lpt1:", etc..en un entorno NTFS.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> hace referencia a un dispositivo no es un archivo, como "figurar:", "com1:", "lpt1:", etc..en un entorno distinto de NTFS.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> es un valor negativo o es cero.o bien <paramref name="mode" />, <paramref name="access" />, o <paramref name="share" /> contienen un valor no válido. </exception>
      <exception cref="T:System.IO.FileNotFoundException">No se encuentra el archivo, como cuando <paramref name="mode" /> es FileMode.Truncate o FileMode.Open, y el archivo especificado por <paramref name="path" /> no existe.El archivo ya debe existir en estos modos.</exception>
      <exception cref="T:System.IO.IOException">Error de E/S, como cuando se especifica FileMode.CreateNew cuando el archivo especificado por <paramref name="path" /> ya existe, se ha producido. o bienEl sistema está ejecutando Windows 98 o Windows 98 Second Edition y <paramref name="share" /> se establece en FileShare.Delete.o bienSe ha cerrado la secuencia.</exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida como, por ejemplo, una ruta de una unidad no asignada. </exception>
      <exception cref="T:System.UnauthorizedAccessException">El <paramref name="access" /> solicitado no permitido por el sistema operativo para el objeto <paramref name="path" />, como cuando <paramref name="access" /> es Write o ReadWrite y el archivo o directorio está establecido para el acceso de solo lectura. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Int32,System.Boolean)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.IO.FileStream" /> con el estado sincrónico o asincrónico, el tamaño de búfer, el permiso de lectura y escritura y de uso compartido, el modo de creación y la ruta de acceso especificados.</summary>
      <param name="path">Ruta de acceso relativa o absoluta del archivo que va a encapsular el objeto FileStream actual. </param>
      <param name="mode">Constante que determina cómo abrir o crear el archivo. </param>
      <param name="access">Constante que determina cómo puede obtener acceso al archivo el objeto FileStream.Esto también determina los valores que devuelven las propiedades <see cref="P:System.IO.FileStream.CanRead" /> y <see cref="P:System.IO.FileStream.CanWrite" /> del objeto FileStream.<see cref="P:System.IO.FileStream.CanSeek" /> es true si <paramref name="path" /> especifica un archivo de disco.</param>
      <param name="share">Constante que determina cómo compartirán el archivo los procesos. </param>
      <param name="bufferSize">Valor <see cref="T:System.Int32" /> positivo mayor que 0 que indica el tamaño del búfer.El tamaño de búfer predeterminado es 4096.</param>
      <param name="useAsync">Especifica si se va a utilizar E/S asincrónica o sincrónica.Sin embargo, tenga en cuenta que el sistema operativo subyacente quizás no admita E/S asincrónica, por lo que cuando se especifica true, puede que el identificador se abra de forma sincrónica en función de la plataforma.Cuando se abre de forma asincrónica, los métodos <see cref="M:System.IO.FileStream.BeginRead(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)" /> y <see cref="M:System.IO.FileStream.BeginWrite(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)" /> proporcionan un rendimiento mejor en lecturas o escrituras grandes, pero es posible que sean mucho más lentos para lecturas o escrituras pequeñas.Si la aplicación se ha diseñado para aprovechar al máximo la E/S asincrónica, establezca el parámetro <paramref name="useAsync" /> en true.El uso de la E/S asincrónica de forma correcta puede agilizar las aplicaciones en hasta un factor de 10, pero su uso sin volver a diseñar la aplicación para la E/S asincrónica puede disminuir el rendimiento en hasta un factor de 10.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena vacía (""), contiene sólo espacios en blanco o contiene uno o más caracteres no válidos. o bien<paramref name="path" /> hace referencia a un dispositivo no es un archivo, como "figurar:", "com1:", "lpt1:", etc..en un entorno NTFS.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> hace referencia a un dispositivo no es un archivo, como "figurar:", "com1:", "lpt1:", etc..en un entorno distinto de NTFS.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> es un valor negativo o es cero.o bien <paramref name="mode" />, <paramref name="access" />, o <paramref name="share" /> contienen un valor no válido. </exception>
      <exception cref="T:System.IO.FileNotFoundException">No se encuentra el archivo, como cuando <paramref name="mode" /> es FileMode.Truncate o FileMode.Open, y el archivo especificado por <paramref name="path" /> no existe.El archivo ya debe existir en estos modos.</exception>
      <exception cref="T:System.IO.IOException">Error de E/S, como cuando se especifica FileMode.CreateNew cuando el archivo especificado por <paramref name="path" /> ya existe, se ha producido.o bien El sistema está ejecutando Windows 98 o Windows 98 Second Edition y <paramref name="share" /> se establece en FileShare.Delete.o bienSe ha cerrado la secuencia.</exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida como, por ejemplo, una ruta de una unidad no asignada. </exception>
      <exception cref="T:System.UnauthorizedAccessException">El <paramref name="access" /> solicitado no permitido por el sistema operativo para el objeto <paramref name="path" />, como cuando <paramref name="access" /> es Write o ReadWrite y el archivo o directorio está establecido para el acceso de solo lectura. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Int32,System.IO.FileOptions)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.IO.FileStream" /> con la ruta de acceso, el modo de creación, los permisos de lectura y escritura y de uso compartido, el acceso que otras secuencias de archivos pueden tener al mismo archivo, el tamaño del búfer y otras opciones de archivo que se hayan especificado.</summary>
      <param name="path">Ruta de acceso relativa o absoluta del archivo que va a encapsular el objeto FileStream actual. </param>
      <param name="mode">Constante que determina cómo abrir o crear el archivo. </param>
      <param name="access">Constante que determina cómo puede obtener acceso al archivo el objeto FileStream.Esto también determina los valores que devuelven las propiedades <see cref="P:System.IO.FileStream.CanRead" /> y <see cref="P:System.IO.FileStream.CanWrite" /> del objeto FileStream.<see cref="P:System.IO.FileStream.CanSeek" /> es true si <paramref name="path" /> especifica un archivo de disco.</param>
      <param name="share">Constante que determina cómo compartirán el archivo los procesos. </param>
      <param name="bufferSize">Valor <see cref="T:System.Int32" /> positivo mayor que 0 que indica el tamaño del búfer.El tamaño de búfer predeterminado es 4096.</param>
      <param name="options">Valor que especifica opciones de archivo adicionales.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena vacía (""), contiene sólo espacios en blanco o contiene uno o más caracteres no válidos. o bien<paramref name="path" /> hace referencia a un dispositivo no es un archivo, como "figurar:", "com1:", "lpt1:", etc..en un entorno NTFS.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> hace referencia a un dispositivo no es un archivo, como "figurar:", "com1:", "lpt1:", etc..en un entorno distinto de NTFS.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> es un valor negativo o es cero.o bien <paramref name="mode" />, <paramref name="access" />, o <paramref name="share" /> contienen un valor no válido. </exception>
      <exception cref="T:System.IO.FileNotFoundException">No se encuentra el archivo, como cuando <paramref name="mode" /> es FileMode.Truncate o FileMode.Open, y el archivo especificado por <paramref name="path" /> no existe.El archivo ya debe existir en estos modos.</exception>
      <exception cref="T:System.IO.IOException">Error de E/S, como cuando se especifica FileMode.CreateNew cuando el archivo especificado por <paramref name="path" /> ya existe, se ha producido.o bienSe ha cerrado la secuencia.</exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida como, por ejemplo, una ruta de una unidad no asignada. </exception>
      <exception cref="T:System.UnauthorizedAccessException">El <paramref name="access" /> solicitado no permitido por el sistema operativo para el objeto <paramref name="path" />, como cuando <paramref name="access" /> es Write o ReadWrite y el archivo o directorio está establecido para el acceso de solo lectura. o bien<see cref="F:System.IO.FileOptions.Encrypted" /> se ha especificado para <paramref name="options" />, pero no se admite el cifrado de archivos en la plataforma actual.</exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
    </member>
    <member name="P:System.IO.FileStream.CanRead">
      <summary>Obtiene un valor que indica si la secuencia actual admite lectura.</summary>
      <returns>Es true si la secuencia admite lectura; es false si la secuencia está cerrada o se abrió con acceso de solo escritura.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.CanSeek">
      <summary>Obtiene un valor que indica si la secuencia actual admite búsquedas.</summary>
      <returns>Es true si la secuencia admite búsquedas; es false si la secuencia está cerrada o si FileStream se construyó a partir de un identificador del sistema operativo, como una canalización o una salida a la consola.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.CanWrite">
      <summary>Obtiene un valor que indica si la secuencia actual admite escritura.</summary>
      <returns>Es true si la secuencia admite escritura; es false si la secuencia está cerrada o se abrió con acceso de solo lectura.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Dispose(System.Boolean)">
      <summary>Libera los recursos no administrados que usa <see cref="T:System.IO.FileStream" /> y libera los recursos administrados de forma opcional.</summary>
      <param name="disposing">Es true para liberar tanto recursos administrados como no administrados; es false para liberar únicamente recursos no administrados. </param>
    </member>
    <member name="M:System.IO.FileStream.Finalize">
      <summary>Garantiza que se liberen los recursos y se realicen otras operaciones de limpieza cuando el recolector de elementos no utilizados reclama FileStream.</summary>
    </member>
    <member name="M:System.IO.FileStream.Flush">
      <summary>Borra los búferes de esta secuencia y hace que todos los datos almacenados en los búferes se escriban en el archivo.</summary>
      <exception cref="T:System.IO.IOException">Error de E/S. </exception>
      <exception cref="T:System.ObjectDisposedException">La secuencia está cerrada. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Flush(System.Boolean)">
      <summary>Borra los búferes de esta secuencia, hace que todos los datos almacenados en los búferes se escriban en el archivo y borra también todos los búferes de archivos intermedios.</summary>
      <param name="flushToDisk">true para vaciar todos los búferes de archivos intermedios; de lo contrario, false. </param>
    </member>
    <member name="M:System.IO.FileStream.FlushAsync(System.Threading.CancellationToken)">
      <summary>Borra asincrónicamente todos los búferes del flujo actual, hace que todos los datos almacenados en el búfer se escriban en el dispositivo subyacente y supervisa las solicitudes de cancelación. </summary>
      <returns>Tarea que representa la operación de vaciado asincrónico. </returns>
      <param name="cancellationToken">Token para supervisar solicitudes de cancelación.</param>
      <exception cref="T:System.ObjectDisposedException">Se ha eliminado la secuencia.</exception>
    </member>
    <member name="P:System.IO.FileStream.IsAsync">
      <summary>Obtiene un valor que indica si se abrió FileStream de forma sincrónica o asincrónica.</summary>
      <returns>Es true si FileStream se abrió de forma asincrónica; de lo contrario, es false.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.Length">
      <summary>Devuelve la longitud en bytes del flujo.</summary>
      <returns>Un valor Long que representa la longitud de la secuencia en bytes.</returns>
      <exception cref="T:System.NotSupportedException">
        <see cref="P:System.IO.FileStream.CanSeek" /> para esta secuencia es false. </exception>
      <exception cref="T:System.IO.IOException">Se produce un error de E/S, como, por ejemplo, el cierre del archivo. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.Name">
      <summary>Obtiene el nombre del FileStream que se pasó al constructor.</summary>
      <returns>Cadena que es el nombre del FileStream.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileStream.Position">
      <summary>Obtiene o establece la posición actual de esta secuencia.</summary>
      <returns>Posición actual de esta secuencia.</returns>
      <exception cref="T:System.NotSupportedException">La secuencia no admite búsquedas. </exception>
      <exception cref="T:System.IO.IOException">Error de E/S. o bienLa posición se estableció en un valor muy grande, más allá del final de la secuencia, en Windows 98 o una versión anterior.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Se ha intentado establecer la posición en un valor negativo. </exception>
      <exception cref="T:System.IO.EndOfStreamException">Se intentó realizar una búsqueda más allá del final de una secuencia que no es compatible con ellas. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>Lee un bloque de bytes de la secuencia y escribe los datos en un búfer dado.</summary>
      <returns>Número total de bytes leídos en el búfer.Puede ser menor que el número de bytes solicitado si ese número de bytes no está disponible actualmente o cero si se ha alcanzado el final de la secuencia.</returns>
      <param name="array">Cuando este método devuelve un valor, contiene la matriz de bytes especificada con los valores entre <paramref name="offset" /> y (<paramref name="offset" /> + <paramref name="count" /> - 1<paramref name=")" /> reemplazados por los bytes leídos desde el origen actual. </param>
      <param name="offset">Desplazamiento de bytes en <paramref name="array" /> donde se colocarán los bytes leídos. </param>
      <param name="count">Número máximo de bytes que se pueden leer. </param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="array" /> es null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> o <paramref name="count" /> es negativo. </exception>
      <exception cref="T:System.NotSupportedException">La secuencia no admite lectura. </exception>
      <exception cref="T:System.IO.IOException">Error de E/S. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> y <paramref name="count" /> describen un intervalo no válido en <paramref name="array" />. </exception>
      <exception cref="T:System.ObjectDisposedException">Tras cerrar la secuencia, se llamó a algún método. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.ReadAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>Lee de forma asincrónica una secuencia de bytes en la secuencia actual, se hace avanzar la posición dentro de la secuencia el número de bytes leídos y controla las solicitudes de cancelación.</summary>
      <returns>Tarea que representa la operación de lectura asincrónica.El valor del parámetro <paramref name="TResult" /> contiene el número total de bytes leídos en el búfer.El valor del resultado puede ser menor que el número de bytes solicitados si el número de bytes disponibles actualmente es menor que el número solicitado o puede ser 0 (cero) si se ha llegado al final de la secuencia.</returns>
      <param name="buffer">Búfer en el que se escriben los datos.</param>
      <param name="offset">Posición de desplazamiento en bytes de <paramref name="buffer" /> donde se comienza a escribir los datos del flujo.</param>
      <param name="count">Número máximo de bytes que se pueden leer.</param>
      <param name="cancellationToken">Token para supervisar solicitudes de cancelación.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="buffer" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> o <paramref name="count" /> es negativo.</exception>
      <exception cref="T:System.ArgumentException">La suma de <paramref name="offset" /> y <paramref name="count" /> es mayor que la longitud del búfer.</exception>
      <exception cref="T:System.NotSupportedException">La secuencia no admite lectura.</exception>
      <exception cref="T:System.ObjectDisposedException">Se ha eliminado la secuencia.</exception>
      <exception cref="T:System.InvalidOperationException">La secuencia está actualmente en uso por una operación de lectura anterior. </exception>
    </member>
    <member name="M:System.IO.FileStream.ReadByte">
      <summary>Lee un byte del archivo y avanza la posición de lectura un byte.</summary>
      <returns>El byte, convertido en un <see cref="T:System.Int32" />, o -1 si se ha alcanzado el final de la secuencia.</returns>
      <exception cref="T:System.NotSupportedException">La secuencia actual no admite lectura. </exception>
      <exception cref="T:System.ObjectDisposedException">La secuencia actual está cerrada. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.SafeFileHandle">
      <summary>Obtiene un objeto <see cref="T:Microsoft.Win32.SafeHandles.SafeFileHandle" /> que representa el identificador de archivos del sistema operativo correspondiente al archivo que el objeto <see cref="T:System.IO.FileStream" /> actual encapsula.</summary>
      <returns>Objeto que representa el identificador de archivos del sistema operativo correspondiente al archivo que el objeto <see cref="T:System.IO.FileStream" /> actual encapsula.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Seek(System.Int64,System.IO.SeekOrigin)">
      <summary>Establece la posición actual de esta secuencia actual en el valor dado.</summary>
      <returns>Nueva posición en la secuencia.</returns>
      <param name="offset">El punto relativo a <paramref name="origin" /> desde el que comienza la operación Seek. </param>
      <param name="origin">Especifica el comienzo, el final o la posición actual como un punto de referencia para <paramref name="offset" />, mediante el uso de un valor de tipo <see cref="T:System.IO.SeekOrigin" />. </param>
      <exception cref="T:System.IO.IOException">Error de E/S. </exception>
      <exception cref="T:System.NotSupportedException">La secuencia no admite búsquedas, por ejemplo, si la FileStream se construye a partir de una canalización o consola de salida. </exception>
      <exception cref="T:System.ArgumentException">Se ha intentado realizar una búsqueda antes del inicio de la secuencia. </exception>
      <exception cref="T:System.ObjectDisposedException">Tras cerrar la secuencia, se llamó a algún método. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.SetLength(System.Int64)">
      <summary>Establece la longitud de esta secuencia en el valor dado.</summary>
      <param name="value">La nueva longitud de la secuencia. </param>
      <exception cref="T:System.IO.IOException">Se produjo un error de E/S. </exception>
      <exception cref="T:System.NotSupportedException">La secuencia no admite al mismo tiempo operaciones de escritura y búsquedas. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Se intentó establecer el <paramref name="value" /> parámetro en menos de 0. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>Escribe un bloque de bytes en la secuencia de archivo.</summary>
      <param name="array">Búfer que contiene los datos que se van a escribir en la secuencia.</param>
      <param name="offset">Desplazamiento en bytes de base cero de <paramref name="array" /> desde donde se comienzan a copiar los bytes en la secuencia. </param>
      <param name="count">Número máximo de bytes que se pueden escribir. </param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="array" /> es null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> y <paramref name="count" /> describen un intervalo no válido en <paramref name="array" />. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> o <paramref name="count" /> es negativo. </exception>
      <exception cref="T:System.IO.IOException">Error de E/S. o bienOtro subproceso puede haber producido un cambio inesperado en la posición del identificador de archivos del sistema operativo. </exception>
      <exception cref="T:System.ObjectDisposedException">La secuencia está cerrada. </exception>
      <exception cref="T:System.NotSupportedException">La instancia de la secuencia actual no admite escritura. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.WriteAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>Escribe de forma asincrónica una secuencia de bytes en la secuencia actual, se hace avanzar la posición actual dentro de la secuencia el número de bytes escritos y controla las solicitudes de cancelación. </summary>
      <returns>Tarea que representa la operación de escritura asincrónica.</returns>
      <param name="buffer">Búfer del que se van a escribir datos. </param>
      <param name="offset">Desplazamiento en bytes de base cero de <paramref name="buffer" /> desde donde se comienzan a copiar los bytes en la secuencia.</param>
      <param name="count">Número máximo de bytes que se pueden escribir.</param>
      <param name="cancellationToken">Token para supervisar solicitudes de cancelación.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="buffer" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> o <paramref name="count" /> es negativo.</exception>
      <exception cref="T:System.ArgumentException">La suma de <paramref name="offset" /> y <paramref name="count" /> es mayor que la longitud del búfer.</exception>
      <exception cref="T:System.NotSupportedException">La secuencia no es compatible con la escritura.</exception>
      <exception cref="T:System.ObjectDisposedException">Se ha eliminado la secuencia.</exception>
      <exception cref="T:System.InvalidOperationException">La secuencia está actualmente en uso por una operación de escritura anterior. </exception>
    </member>
    <member name="M:System.IO.FileStream.WriteByte(System.Byte)">
      <summary>Escribe un byte en la posición actual de la secuencia de archivo.</summary>
      <param name="value">Un byte que se va a escribir en la secuencia. </param>
      <exception cref="T:System.ObjectDisposedException">La secuencia está cerrada. </exception>
      <exception cref="T:System.NotSupportedException">La secuencia no es compatible con la escritura. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.FileSystemInfo">
      <summary>Proporciona la clase base para los objetos <see cref="T:System.IO.FileInfo" /> y <see cref="T:System.IO.DirectoryInfo" />.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.FileSystemInfo.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.IO.FileSystemInfo" />.</summary>
    </member>
    <member name="P:System.IO.FileSystemInfo.Attributes">
      <summary>Obtiene o establece los atributos del archivo o directorio actual.</summary>
      <returns>
        <see cref="T:System.IO.FileAttributes" /> del <see cref="T:System.IO.FileSystemInfo" /> actual.</returns>
      <exception cref="T:System.IO.FileNotFoundException">El archivo especificado no existe. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <exception cref="T:System.ArgumentException">El llamador intenta establecer un atributo de archivo no válido. o bienEl usuario intenta establecer un valor de atributo pero no tienen permiso de escritura.</exception>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> no puede inicializar los datos. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.CreationTime">
      <summary>Obtiene o establece la hora de creación del archivo o directorio actual.</summary>
      <returns>Fecha y hora de creación del objeto <see cref="T:System.IO.FileSystemInfo" /> actual.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> no puede inicializar los datos. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada).</exception>
      <exception cref="T:System.PlatformNotSupportedException">El sistema operativo actual no es Windows NT o una versión posterior.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El autor de llamada intenta establecer un tiempo de creación no válido.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.CreationTimeUtc">
      <summary>Obtiene o establece la hora de creación, en formato de hora universal coordinada (UTC), del archivo o directorio actual.</summary>
      <returns>Fecha y hora de creación en formato UTC del objeto <see cref="T:System.IO.FileSystemInfo" /> actual.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> no puede inicializar los datos. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada).</exception>
      <exception cref="T:System.PlatformNotSupportedException">El sistema operativo actual no es Windows NT o una versión posterior.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El autor de llamada intenta establecer un tiempo de acceso no válido.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileSystemInfo.Delete">
      <summary>Elimina un archivo o directorio.</summary>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada).</exception>
      <exception cref="T:System.IO.IOException">Hay un identificador abierto en el archivo o en el directorio, y el sistema operativo es Windows XP o anterior.Este identificador abierto puede ser el resultado de la enumeración de directorios y archivos.Para más información, vea Cómo: Enumerar directorios y archivos.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.FileSystemInfo.Exists">
      <summary>Obtiene un valor que indica si existe el archivo o directorio.</summary>
      <returns>Es true si existe el archivo o el directorio; de lo contrario, es false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileSystemInfo.Extension">
      <summary>Obtiene la cadena que representa la extensión del archivo.</summary>
      <returns>Cadena que contiene la extensión <see cref="T:System.IO.FileSystemInfo" />.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileSystemInfo.FullName">
      <summary>Obtiene la ruta de acceso completa del directorio o el archivo.</summary>
      <returns>Cadena que contiene la ruta de acceso completa.</returns>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso completa y el nombre de archivo tienen 260 o más caracteres.</exception>
      <exception cref="T:System.Security.SecurityException">El llamador no dispone del permiso requerido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="F:System.IO.FileSystemInfo.FullPath">
      <summary>Representa la ruta de acceso completa del directorio o el archivo.</summary>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso completa tiene 260 o más caracteres.</exception>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastAccessTime">
      <summary>Obtiene o establece la hora a la que se produjo el último acceso al archivo o directorio actual.</summary>
      <returns>La hora a la que se produjo el último acceso al archivo o directorio actual.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> no puede inicializar los datos. </exception>
      <exception cref="T:System.PlatformNotSupportedException">El sistema operativo actual no es Windows NT o una versión posterior.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El autor de llamada intenta establecer un tiempo de acceso no válido</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastAccessTimeUtc">
      <summary>Obtiene o establece la hora, en formato de hora universal coordinada (UTC), a la que se produjo el último acceso al archivo o directorio actual.</summary>
      <returns>Hora en formato UTC a la que se produjo el último acceso al archivo o directorio actual.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> no puede inicializar los datos. </exception>
      <exception cref="T:System.PlatformNotSupportedException">El sistema operativo actual no es Windows NT o una versión posterior.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El autor de llamada intenta establecer un tiempo de acceso no válido.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastWriteTime">
      <summary>Obtiene o establece la hora a la que se escribió por última vez en el archivo o directorio actual.</summary>
      <returns>La hora a la que se escribió por última vez en el archivo actual.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> no puede inicializar los datos. </exception>
      <exception cref="T:System.PlatformNotSupportedException">El sistema operativo actual no es Windows NT o una versión posterior.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El autor de llamada intenta establecer un tiempo de escritura no válido.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastWriteTimeUtc">
      <summary>Obtiene o establece la hora, en formato de hora universal coordinada (UTC), a la que se escribió por última vez en el archivo o directorio actual.</summary>
      <returns>Hora en formato UTC a la que se escribió por última vez en el archivo actual.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> no puede inicializar los datos. </exception>
      <exception cref="T:System.PlatformNotSupportedException">El sistema operativo actual no es Windows NT o una versión posterior.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El autor de llamada intenta establecer un tiempo de escritura no válido.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.Name">
      <summary>Para archivos, obtiene el nombre del archivo.Para directorios, obtiene el nombre del último directorio de la jerarquía, si existe tal jerarquía.En caso contrario, la propiedad Name obtiene el nombre del directorio.</summary>
      <returns>Cadena que representa el nombre del directorio principal, el nombre del último directorio de la jerarquía o el nombre de un archivo, incluida la extensión de nombre de archivo.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.FileSystemInfo.OriginalPath">
      <summary>La ruta de acceso especificada originalmente por el usuario, ya sea relativa o absoluta.</summary>
    </member>
    <member name="M:System.IO.FileSystemInfo.Refresh">
      <summary>Actualiza el estado del objeto.</summary>
      <exception cref="T:System.IO.IOException">El dispositivo, como la unidad de disco, no está preparado. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.SearchOption">
      <summary>Especifica si se va a buscar en el directorio actual, o bien, en el directorio actual y en todos los subdirectorios. </summary>
    </member>
    <member name="F:System.IO.SearchOption.AllDirectories">
      <summary>Incluye el directorio actual y todos sus subdirectorios en una operación de búsqueda.Esta opción incluye en la búsqueda los puntos de reanálisis, como las unidades montadas y los vínculos simbólicos.</summary>
    </member>
    <member name="F:System.IO.SearchOption.TopDirectoryOnly">
      <summary>Solo incluye el directorio actual en una operación de búsqueda.</summary>
    </member>
  </members>
</doc>