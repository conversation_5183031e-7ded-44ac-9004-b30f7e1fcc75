﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.IO.FileSystem</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Win32.SafeHandles.SafeFileHandle">
      <summary>Représente une classe wrapper pour un handle de fichier. </summary>
    </member>
    <member name="M:Microsoft.Win32.SafeHandles.SafeFileHandle.#ctor(System.IntPtr,System.Boolean)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:Microsoft.Win32.SafeHandles.SafeFileHandle" />. </summary>
      <param name="preexistingHandle">Objet <see cref="T:System.IntPtr" /> qui représente le handle préexistant à utiliser.</param>
      <param name="ownsHandle">true pour libérer de manière fiable le handle pendant la phase de finalisation ; false pour empêcher la libération fiable (déconseillé).</param>
    </member>
    <member name="P:Microsoft.Win32.SafeHandles.SafeFileHandle.IsInvalid"></member>
    <member name="T:System.IO.Directory">
      <summary>Expose des méthodes statiques pour créer, se déplacer dans et énumérer des répertoires et sous-répertoires.Cette classe ne peut pas être héritée.Pour parcourir le code source de .NET Framework pour ce type, consultez la Source de référence.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Directory.CreateDirectory(System.String)">
      <summary>Crée tous les répertoires et sous-répertoires dans le chemin d'accès spécifié, sauf s'ils existent déjà.</summary>
      <returns>Objet qui représente le répertoire dans le chemin d'accès spécifié.Cet objet est retourné, qu'un répertoire existe déjà ou non dans le chemin d'accès spécifié.</returns>
      <param name="path">Répertoire à créer. </param>
      <exception cref="T:System.IO.IOException">Le répertoire spécifié par <paramref name="path" /> est un fichier.ouLe nom de réseau n'est pas connu.</exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides à l'aide de la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.ou<paramref name="path" /> est préfixé avec, ou contient, uniquement un signe deux-points (:).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> contient un caractère deux-points (:) qui ne fait pas partie d'un volume de lecteur (« C:\ »).</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.Delete(System.String)">
      <summary>Supprime un répertoire vide dans un chemin d'accès spécifié.</summary>
      <param name="path">Nom du répertoire vide à supprimer.Ce répertoire doit être accessible en écriture et vide.</param>
      <exception cref="T:System.IO.IOException">Un fichier avec le même nom et le même emplacement spécifié par <paramref name="path" /> existe.ouLe répertoire est le répertoire de travail actif de l'application.ouLe répertoire spécifié par <paramref name="path" /> n'est pas vide.ouLe répertoire est en lecture seule ou contient est un répertoire en lecture seule.ouLe répertoire est utilisé par un autre processus.</exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides à l'aide de la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> n'existe pas ou n'a pas pu être trouvé.ouLe chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.Delete(System.String,System.Boolean)">
      <summary>Supprime le répertoire spécifié et, s'ils sont indiqués, tous les sous-répertoires et fichier qu'il contient. </summary>
      <param name="path">Nom du répertoire à supprimer. </param>
      <param name="recursive">true pour supprimer les répertoires, sous-répertoires et fichiers dans le chemin d'accès indiqué par <paramref name="path" /> ; sinon, false. </param>
      <exception cref="T:System.IO.IOException">Un fichier avec le même nom et le même emplacement spécifié par <paramref name="path" /> existe.ouLe répertoire spécifié par <paramref name="path" /> est en lecture seule ou <paramref name="recursive" /> est false et <paramref name="path" /> n'est pas un répertoire vide. ouLe répertoire est le répertoire de travail actif de l'application. ouLe répertoire contient un fichier en lecture seule.ouLe répertoire est utilisé par un autre processus.</exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides à l'aide de la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> n'existe pas ou n'a pas pu être trouvé.ouLe chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String)">
      <summary>Retourne une collection énumérable de noms de répertoires dans un chemin d'accès spécifié.</summary>
      <returns>Collection énumérable des noms complets (chemins compris) pour les répertoires du répertoire spécifiés par <paramref name="path" />.</returns>
      <param name="path">Le chemin d'accès relatif ou absolu du répertoire où effectuer la recherche.Cette chaîne n'est pas sensible à la casse.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides à l'aide de la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> n'est pas valide, notamment s'il fait référence à un lecteur non mappé. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> est un nom de fichier.</exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin et/ou le nom de fichier spécifiés dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String,System.String)">
      <summary>Retourne une collection énumérable des noms de répertoires qui correspondent à un modèle de recherche dans un chemin d'accès spécifié.</summary>
      <returns>Collection énumérable des noms complets (chemins d'accès compris) pour les répertoires du répertoire spécifié par <paramref name="path" /> et qui correspondent au modèle de recherche spécifié.</returns>
      <param name="path">Le chemin d'accès relatif ou absolu du répertoire où effectuer la recherche.Cette chaîne n'est pas sensible à la casse.</param>
      <param name="searchPattern">Chaîne recherchée à trouver parmi les noms de répertoires dans <paramref name="path" />.Ce paramètre peut contenir une combinaison de caractères littéraux et génériques * et ? (voir Remarques), mais ne prend pas en charge les expressions régulières.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides avec la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.ou<paramref name="searchPattern" /> ne contient pas un modèle valide.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null.ou<paramref name="searchPattern" /> a la valeur null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> n'est pas valide, notamment s'il fait référence à un lecteur non mappé. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> est un nom de fichier.</exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin et/ou le nom de fichier spécifiés dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String,System.String,System.IO.SearchOption)">
      <summary>Retourne une collection énumérable des noms de répertoires qui correspondent à un modèle de recherche dans un chemin d'accès spécifié, et effectue éventuellement la recherche dans les sous-répertoires.</summary>
      <returns>Collection énumérable des noms complets (chemins d'accès compris) pour les répertoires du répertoire spécifié par <paramref name="path" /> et qui correspondent au modèle et à l'option de recherche spécifiés.</returns>
      <param name="path">Le chemin d'accès relatif ou absolu du répertoire où effectuer la recherche.Cette chaîne n'est pas sensible à la casse.</param>
      <param name="searchPattern">Chaîne recherchée à trouver parmi les noms de répertoires dans <paramref name="path" />.Ce paramètre peut contenir une combinaison de caractères littéraux et génériques * et ? (voir Remarques), mais ne prend pas en charge les expressions régulières.</param>
      <param name="searchOption">Une des valeurs d'énumération qui spécifie si l'opération de recherche doit inclure seulement le répertoire actuel ou si elle doit inclure tous les sous-répertoires.La valeur par défaut est <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides à l'aide de la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.ou<paramref name="searchPattern" /> ne contient pas un modèle valide.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null.ou<paramref name="searchPattern" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> n'est pas une valeur <see cref="T:System.IO.SearchOption" /> valide.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> n'est pas valide, notamment s'il fait référence à un lecteur non mappé. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> est un nom de fichier.</exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin et/ou le nom de fichier spécifiés dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String)">
      <summary>Retourne une collection énumérable de noms de fichiers dans un chemin d'accès spécifié.</summary>
      <returns>Collection énumérable des noms complets (chemins compris) pour les fichiers du répertoire spécifiés par <paramref name="path" />.</returns>
      <param name="path">Le chemin d'accès relatif ou absolu du répertoire où effectuer la recherche.Cette chaîne n'est pas sensible à la casse.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides à l'aide de la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> n'est pas valide, notamment s'il fait référence à un lecteur non mappé. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> est un nom de fichier.</exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin et/ou le nom de fichier spécifiés dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String,System.String)">
      <summary>Retourne une collection énumérable des noms de fichiers qui correspondent à un modèle de recherche dans un chemin d'accès spécifié.</summary>
      <returns>Collection énumérable des noms complets (chemins d'accès compris) pour les fichiers du répertoire spécifié par <paramref name="path" /> et qui correspondent au modèle de recherche spécifié.</returns>
      <param name="path">Le chemin d'accès relatif ou absolu du répertoire où effectuer la recherche.Cette chaîne n'est pas sensible à la casse.</param>
      <param name="searchPattern">Chaîne recherchée à trouver parmi les noms de fichiers dans <paramref name="path" />.Ce paramètre peut contenir une combinaison de caractères littéraux et génériques * et ? (voir Remarques), mais ne prend pas en charge les expressions régulières.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides à l'aide de la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.ou<paramref name="searchPattern" /> ne contient pas un modèle valide.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null.ou<paramref name="searchPattern" /> a la valeur null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> n'est pas valide, notamment s'il fait référence à un lecteur non mappé. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> est un nom de fichier.</exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin et/ou le nom de fichier spécifiés dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String,System.String,System.IO.SearchOption)">
      <summary>Retourne une collection énumérable des noms de fichiers qui correspondent à un modèle de recherche dans un chemin d'accès spécifié, et effectue éventuellement des recherches dans les sous-répertoires.</summary>
      <returns>Collection énumérable des noms complets (chemins d'accès compris) pour les fichiers du répertoire spécifié par <paramref name="path" /> et qui correspondent au modèle et à l'option de recherche spécifiés.</returns>
      <param name="path">Le chemin d'accès relatif ou absolu du répertoire où effectuer la recherche.Cette chaîne n'est pas sensible à la casse.</param>
      <param name="searchPattern">Chaîne recherchée à trouver parmi les noms de fichiers dans <paramref name="path" />.Ce paramètre peut contenir une combinaison de caractères littéraux et génériques * et ? (voir Remarques), mais ne prend pas en charge les expressions régulières.</param>
      <param name="searchOption">Une des valeurs d'énumération qui spécifie si l'opération de recherche doit inclure seulement le répertoire actuel ou si elle doit inclure tous les sous-répertoires.La valeur par défaut est <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides à l'aide de la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.ou<paramref name="searchPattern" /> ne contient pas un modèle valide.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null.ou<paramref name="searchPattern" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> n'est pas une valeur <see cref="T:System.IO.SearchOption" /> valide.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> n'est pas valide, notamment s'il fait référence à un lecteur non mappé. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> est un nom de fichier.</exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin et/ou le nom de fichier spécifiés dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String)">
      <summary>Retourne une collection énumérable de noms de fichiers et de noms de répertoires dans un chemin d'accès spécifié. </summary>
      <returns>Collection énumérable des entrées de système de fichiers dans le répertoire spécifié par <paramref name="path" />.</returns>
      <param name="path">Le chemin d'accès relatif ou absolu du répertoire où effectuer la recherche.Cette chaîne n'est pas sensible à la casse.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides à l'aide de la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> n'est pas valide, notamment s'il fait référence à un lecteur non mappé. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> est un nom de fichier.</exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin et/ou le nom de fichier spécifiés dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String,System.String)">
      <summary>Retourne une collection énumérable des noms de fichiers et des noms de répertoires qui correspondent à un modèle de recherche dans un chemin d'accès spécifié.</summary>
      <returns>Collection énumérable des entrées de système de fichiers dans le répertoire spécifié par <paramref name="path" /> et qui correspondent au modèle de recherche spécifié.</returns>
      <param name="path">Le chemin d'accès relatif ou absolu du répertoire où effectuer la recherche.Cette chaîne n'est pas sensible à la casse.</param>
      <param name="searchPattern">Chaîne recherchée à trouver parmi les noms des entrées du système de fichiers dans <paramref name="path" />.Ce paramètre peut contenir une combinaison de caractères littéraux et génériques * et ? (voir Remarques), mais ne prend pas en charge les expressions régulières.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides à l'aide de la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.ou<paramref name="searchPattern" /> ne contient pas un modèle valide.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null.ou<paramref name="searchPattern" /> a la valeur null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> n'est pas valide, notamment s'il fait référence à un lecteur non mappé. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> est un nom de fichier.</exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin et/ou le nom de fichier spécifiés dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String,System.String,System.IO.SearchOption)">
      <summary>Retourne une collection énumérable des noms de fichiers et des noms de répertoires qui correspondent à un modèle de recherche dans un chemin d'accès spécifié, et effectue éventuellement des recherches dans les sous-répertoires.</summary>
      <returns>Collection énumérable des entrées de système de fichiers dans le répertoire spécifié par <paramref name="path" /> et qui correspondent au modèle et à l'option de recherche spécifiés.</returns>
      <param name="path">Le chemin d'accès relatif ou absolu du répertoire où effectuer la recherche.Cette chaîne n'est pas sensible à la casse.</param>
      <param name="searchPattern">Chaîne recherchée à trouver parmi les entrées du système de fichiers dans <paramref name="path" />.Ce paramètre peut contenir une combinaison de caractères littéraux et génériques * et ? (voir Remarques), mais ne prend pas en charge les expressions régulières.</param>
      <param name="searchOption">Une des valeurs d'énumération qui spécifie si l'opération de recherche doit inclure seulement le répertoire actuel ou si elle doit inclure tous les sous-répertoires.La valeur par défaut est <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides à l'aide de la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.ou<paramref name="searchPattern" /> ne contient pas un modèle valide.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null.ou<paramref name="searchPattern" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> n'est pas une valeur <see cref="T:System.IO.SearchOption" /> valide.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> n'est pas valide, notamment s'il fait référence à un lecteur non mappé. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> est un nom de fichier.</exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin et/ou le nom de fichier spécifiés dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise.</exception>
    </member>
    <member name="M:System.IO.Directory.Exists(System.String)">
      <summary>Détermine si le chemin d'accès donné référence un répertoire existant sur disque.</summary>
      <returns>true si le chemin <paramref name="path" /> fait référence à un répertoire existant ; false si le répertoire n'existe pas ou si une erreur se produit lorsque vous essayez de déterminer l'existence du fichier spécifié.</returns>
      <param name="path">Chemin d'accès à tester. </param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetCreationTime(System.String)">
      <summary>Obtient la date et l'heure de création d'un répertoire.</summary>
      <returns>Structure à laquelle est affectée la date/heure de création du répertoire spécifié.Cette valeur est exprimée en heure locale.</returns>
      <param name="path">Chemin d'accès du répertoire. </param>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides à l'aide de la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetCreationTimeUtc(System.String)">
      <summary>Obtient la date/heure de création d'un répertoire, au format UTC (Temps universel coordonné).</summary>
      <returns>Structure à laquelle est affectée la date/heure de création du répertoire spécifié.Cette valeur est exprimée en temps UTC.</returns>
      <param name="path">Chemin d'accès du répertoire. </param>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides à l'aide de la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetCurrentDirectory">
      <summary>Obtient le répertoire de travail en cours de l'application.</summary>
      <returns>Chaîne qui contient le chemin d'accès du répertoire de travail actuel et qui ne se termine pas par une barre oblique inverse (\).</returns>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.NotSupportedException">Le système d'exploitation est Windows CE, qui n'a pas la fonctionnalité du répertoire actif.Cette méthode est disponible dans le .NET Compact Framework, mais n'est pas actuellement prise en charge.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String)">
      <summary>Retourne les noms des sous-répertoires (y compris leurs chemins d'accès) dans le répertoire spécifié.</summary>
      <returns>Tableau des noms complets (y compris les chemins d'accès) des sous-répertoires dans le chemin d'accès spécifié, ou tableau vide si aucun répertoire n'est trouvé.</returns>
      <param name="path">Le chemin d'accès relatif ou absolu du répertoire où effectuer la recherche.Cette chaîne n'est pas sensible à la casse.</param>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides à l'aide de la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> est un nom de fichier. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String,System.String)">
      <summary>Retourne les noms des sous-répertoires (y compris leurs chemins d'accès) qui correspondent au modèle de recherche spécifié dans le répertoire spécifié.</summary>
      <returns>Tableau des noms complets (y compris les chemins d'accès) des sous-répertoires qui correspondent au modèle de recherche dans le répertoire spécifié, ou tableau vide si aucun répertoire n'est trouvé.</returns>
      <param name="path">Le chemin d'accès relatif ou absolu du répertoire où effectuer la recherche.Cette chaîne n'est pas sensible à la casse.</param>
      <param name="searchPattern">Chaîne recherchée à trouver parmi les noms de sous-répertoires dans <paramref name="path" />.Ce paramètre peut contenir une combinaison de caractères littéraux et de caractères génériques (voir Remarques), mais ne prend pas en charge les expressions régulières.</param>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides à l'aide de <see cref="M:System.IO.Path.GetInvalidPathChars" />.ou <paramref name="searchPattern" /> ne contient pas un modèle valide. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ou <paramref name="searchPattern" /> est null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> est un nom de fichier. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String,System.String,System.IO.SearchOption)">
      <summary>Retourne les noms des sous-répertoires (avec leurs chemins d'accès) qui correspondent au modèle de recherche spécifié dans le répertoire spécifié, et effectue éventuellement une recherche dans les sous-répertoires.</summary>
      <returns>Tableau des noms complets (y compris les chemins d'accès) des sous-répertoires qui correspondent aux critères spécifiés, ou tableau vide si aucun répertoire n'est trouvé.</returns>
      <param name="path">Le chemin d'accès relatif ou absolu du répertoire où effectuer la recherche.Cette chaîne n'est pas sensible à la casse.</param>
      <param name="searchPattern">Chaîne recherchée à trouver parmi les noms de sous-répertoires dans <paramref name="path" />.Ce paramètre peut contenir une combinaison de caractères littéraux et de caractères génériques (voir Remarques), mais ne prend pas en charge les expressions régulières.</param>
      <param name="searchOption">Une des valeurs d'énumération qui spécifie si l'opération de recherche doit inclure tous les sous-répertoires ou uniquement le répertoire actif. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides à l'aide de la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.ou <paramref name="searchPattern" /> ne contient pas un modèle valide. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ou <paramref name="searchPattern" /> est null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> n'est pas une valeur <see cref="T:System.IO.SearchOption" /> valide.</exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> est un nom de fichier. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
    </member>
    <member name="M:System.IO.Directory.GetDirectoryRoot(System.String)">
      <summary>Retourne les informations sur le volume, les informations sur la racine ou les deux, pour le chemin d'accès spécifié.</summary>
      <returns>Chaîne qui contient les informations sur le volume, les informations sur la racine ou les deux, pour le chemin d'accès spécifié.</returns>
      <param name="path">Chemin d'accès d'un fichier ou d'un répertoire. </param>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides avec <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String)">
      <summary>Retourne les noms des fichiers (y compris leur chemin d'accès) dans le répertoire spécifié.</summary>
      <returns>Tableau des noms complets (y compris les chemins d'accès) pour les fichiers du répertoire spécifié, ou tableau vide si aucun fichier n'est trouvé.</returns>
      <param name="path">Le chemin d'accès relatif ou absolu du répertoire où effectuer la recherche.Cette chaîne n'est pas sensible à la casse.</param>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> est un nom de fichier.ouUne erreur réseau s'est produite. </exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides à l'aide de la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié est introuvable ou n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String,System.String)">
      <summary>Retourne les noms des fichiers (y compris leurs chemins d'accès) qui correspondent au modèle de recherche spécifié dans le répertoire spécifié.</summary>
      <returns>Tableau des noms complets (y compris les chemins d'accès) pour les fichiers du répertoire spécifié qui correspondent au modèle de recherche spécifié, ou un tableau vide si aucun fichier n'est trouvé.</returns>
      <param name="path">Le chemin d'accès relatif ou absolu du répertoire où effectuer la recherche.Cette chaîne n'est pas sensible à la casse.</param>
      <param name="searchPattern">Chaîne recherchée à trouver parmi les noms de fichiers dans <paramref name="path" />.Ce paramètre peut contenir une combinaison de caractères littéraux et génériques * et ? (voir Remarques), mais ne prend pas en charge les expressions régulières.</param>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> est un nom de fichier.ouUne erreur réseau s'est produite. </exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides à l'aide de <see cref="M:System.IO.Path.GetInvalidPathChars" />.ou <paramref name="searchPattern" /> ne contient pas un modèle valide. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ou <paramref name="searchPattern" /> est null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié est introuvable ou n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String,System.String,System.IO.SearchOption)">
      <summary>Retourne les noms des fichiers (y compris leurs chemins d'accès) qui correspondent au modèle de recherche spécifié dans le répertoire spécifié, en utilisant une valeur pour déterminer s'il faut effectuer une recherche dans les sous-répertoires.</summary>
      <returns>Tableau des noms complets (y compris les chemins d'accès) pour les fichiers du répertoire spécifié qui correspondent au modèle et à l'option de recherche spécifiés, ou tableau vide si aucun fichier n'est trouvé.</returns>
      <param name="path">Le chemin d'accès relatif ou absolu du répertoire où effectuer la recherche.Cette chaîne n'est pas sensible à la casse.</param>
      <param name="searchPattern">Chaîne recherchée à trouver parmi les noms de fichiers dans <paramref name="path" />.Ce paramètre peut contenir une combinaison de caractères littéraux et génériques * et ? (voir Remarques), mais ne prend pas en charge les expressions régulières.</param>
      <param name="searchOption">Une des valeurs d'énumération qui spécifie si l'opération de recherche doit inclure tous les sous-répertoires ou uniquement le répertoire actif. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides avec la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.ou <paramref name="searchPattern" /> ne contient pas un modèle valide.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ou <paramref name="searchpattern" /> est null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> n'est pas une valeur <see cref="T:System.IO.SearchOption" /> valide.</exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié est introuvable ou n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> est un nom de fichier.ouUne erreur réseau s'est produite. </exception>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String)">
      <summary>Retourne les noms de tous les fichiers et sous-répertoires dans un chemin d'accès spécifié.</summary>
      <returns>Tableau des noms de fichiers et de sous-répertoires dans le répertoire spécifié, ou tableau vide si aucun fichier ni sous-répertoire n'est trouvé.</returns>
      <param name="path">Le chemin d'accès relatif ou absolu du répertoire où effectuer la recherche.Cette chaîne n'est pas sensible à la casse.</param>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides avec <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> est un nom de fichier. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String,System.String)">
      <summary>Retourne un tableau de noms de fichiers et noms de répertoires qui correspondent à un modèle de recherche dans un chemin d'accès spécifié.</summary>
      <returns>Tableau de noms de fichiers et de noms de répertoires qui correspondent aux critères de recherche spécifiés, ou tableau vide si aucun fichier ou répertoire n'est trouvé.</returns>
      <param name="path">Le chemin d'accès relatif ou absolu du répertoire où effectuer la recherche.Cette chaîne n'est pas sensible à la casse.</param>
      <param name="searchPattern">Chaîne recherchée à trouver parmi les noms de fichiers et les noms de répertoires dans <paramref name="path" />.Ce paramètre peut contenir une combinaison de caractères littéraux et génériques * et ? (voir Remarques), mais ne prend pas en charge les expressions régulières.</param>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides avec la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.ou <paramref name="searchPattern" /> ne contient pas un modèle valide. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ou <paramref name="searchPattern" /> est null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> est un nom de fichier. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String,System.String,System.IO.SearchOption)">
      <summary>Retourne un tableau de tous les noms de fichiers et noms de répertoires qui correspondent à un modèle de recherche dans un chemin d'accès spécifié, et effectue éventuellement des recherches dans les sous-répertoires.</summary>
      <returns>Tableau des noms de fichiers et des noms de répertoires qui correspondent aux critères de recherche spécifiés, ou tableau vide si aucun fichier ou répertoire n'est trouvé.</returns>
      <param name="path">Le chemin d'accès relatif ou absolu du répertoire où effectuer la recherche.Cette chaîne n'est pas sensible à la casse.</param>
      <param name="searchPattern">Chaîne recherchée à trouver parmi les noms de fichiers et les noms de répertoires dans <paramref name="path" />.Ce paramètre peut contenir une combinaison de caractères littéraux et génériques * et ? (voir Remarques), mais ne prend pas en charge les expressions régulières.</param>
      <param name="searchOption">Une des valeurs d'énumération qui spécifie si l'opération de recherche doit inclure seulement le répertoire actuel ou si elle doit inclure tous les sous-répertoires.La valeur par défaut est <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides à l'aide de la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.ou<paramref name="searchPattern" /> ne contient pas un modèle valide.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null.ou<paramref name="searchPattern" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> n'est pas une valeur <see cref="T:System.IO.SearchOption" /> valide.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> n'est pas valide, notamment s'il fait référence à un lecteur non mappé. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> est un nom de fichier.</exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin et/ou le nom de fichier spécifiés dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise.</exception>
    </member>
    <member name="M:System.IO.Directory.GetLastAccessTime(System.String)">
      <summary>Retourne la date/heure du dernier accès au fichier ou au répertoire spécifié.</summary>
      <returns>Structure définie avec la date/heure du dernier accès au fichier ou au répertoire spécifié.Cette valeur est exprimée en heure locale.</returns>
      <param name="path">Fichier ou répertoire pour lequel sont obtenues les informations relatives à la date et à l'heure. </param>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides avec la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.NotSupportedException">Le format du paramètre <paramref name="path" /> n'est pas valide. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetLastAccessTimeUtc(System.String)">
      <summary>Retourne la date/heure, au format UTC (Temps universel coordonné), du dernier accès au fichier ou au répertoire spécifié.</summary>
      <returns>Structure définie avec la date/heure du dernier accès au fichier ou au répertoire spécifié.Cette valeur est exprimée en temps UTC.</returns>
      <param name="path">Fichier ou répertoire pour lequel sont obtenues les informations relatives à la date et à l'heure. </param>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides avec la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.NotSupportedException">Le format du paramètre <paramref name="path" /> n'est pas valide. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetLastWriteTime(System.String)">
      <summary>Retourne la date/heure du dernier accès en écriture au fichier ou au répertoire spécifié.</summary>
      <returns>Structure définie avec la date/heure de la dernière écriture dans le fichier ou le répertoire spécifié.Cette valeur est exprimée en heure locale.</returns>
      <param name="path">Fichier ou répertoire pour lequel sont obtenues les informations relatives à la date et à l'heure de modification. </param>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides avec la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetLastWriteTimeUtc(System.String)">
      <summary>Retourne la date/heure, au format UTC (Temps universel coordonné), de la dernière écriture dans le fichier ou le répertoire spécifié.</summary>
      <returns>Structure définie avec la date/heure de la dernière écriture dans le fichier ou le répertoire spécifié.Cette valeur est exprimée en temps UTC.</returns>
      <param name="path">Fichier ou répertoire pour lequel sont obtenues les informations relatives à la date et à l'heure de modification. </param>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides avec la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetParent(System.String)">
      <summary>Récupère le répertoire parent du chemin d'accès spécifié, y compris les chemins d'accès absolus et relatifs.</summary>
      <returns>Répertoire parent, ou null si <paramref name="path" /> est le répertoire racine, y compris la racine d'un serveur UNC ou nom de partage.</returns>
      <param name="path">Chemin d'accès pour lequel est récupéré le répertoire parent. </param>
      <exception cref="T:System.IO.IOException">Le répertoire spécifié par <paramref name="path" /> est en lecture seule. </exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides avec la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié est introuvable. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.Move(System.String,System.String)">
      <summary>Déplace un fichier, ou un répertoire et son contenu, vers un nouvel emplacement.</summary>
      <param name="sourceDirName">Chemin d'accès du fichier ou du répertoire à déplacer. </param>
      <param name="destDirName">Chemin d'accès vers le nouvel emplacement pour <paramref name="sourceDirName" />.Si <paramref name="sourceDirName" /> est un fichier, <paramref name="destDirName" /> doit également être un nom de fichier.</param>
      <exception cref="T:System.IO.IOException">Une tentative pour déplacer un répertoire vers un autre volume a été effectuée. ou <paramref name="destDirName" /> existe déjà. ou Les paramètres <paramref name="sourceDirName" /> et <paramref name="destDirName" /> font référence au même fichier ou répertoire. ouLe répertoire ou un fichier qu'il est utilisé par un autre processus.</exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceDirName" /> ou <paramref name="destDirName" /> est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides avec la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceDirName" /> ou <paramref name="destDirName" /> est null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié par <paramref name="sourceDirName" /> n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetCreationTime(System.String,System.DateTime)">
      <summary>Définit la date/heure de création pour le fichier ou le répertoire spécifié.</summary>
      <param name="path">Fichier ou répertoire pour lequel sont définies les informations relatives à la date et à l'heure de création. </param>
      <param name="creationTime">Date/heure de la dernière écriture dans le fichier ou le répertoire.Cette valeur est exprimée en heure locale.</param>
      <exception cref="T:System.IO.FileNotFoundException">Le chemin d'accès spécifié est introuvable. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides avec la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> spécifie une valeur en dehors de la plage de dates ou d'heures autorisée pour cette opération. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Le système d'exploitation actif n'est pas Windows NT ni une version ultérieure.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetCreationTimeUtc(System.String,System.DateTime)">
      <summary>Définit la date et l'heure de création, au format de temps universel (UTC, Universal Coordinated Time) pour le fichier ou le répertoire spécifié.</summary>
      <param name="path">Fichier ou répertoire pour lequel sont définies les informations relatives à la date et à l'heure de création. </param>
      <param name="creationTimeUtc">Date/heure de création du répertoire ou du fichier.Cette valeur est exprimée en heure locale.</param>
      <exception cref="T:System.IO.FileNotFoundException">Le chemin d'accès spécifié est introuvable. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides avec la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> spécifie une valeur en dehors de la plage de dates ou d'heures autorisée pour cette opération. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Le système d'exploitation actif n'est pas Windows NT ni une version ultérieure.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetCurrentDirectory(System.String)">
      <summary>Définit le répertoire spécifié comme répertoire de travail actuel de l'application.</summary>
      <param name="path">Chemin d'accès assigné au répertoire de travail actif. </param>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides avec la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise pour accéder à du code non managé. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Le chemin d'accès spécifié est introuvable. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le répertoire spécifié est introuvable.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastAccessTime(System.String,System.DateTime)">
      <summary>Définit la date/heure du dernier accès au fichier ou au répertoire spécifié.</summary>
      <param name="path">Fichier ou répertoire pour lequel sont définies les informations relatives à la date et à l'heure d'accès. </param>
      <param name="lastAccessTime">Objet qui contient la valeur à assigner pour la date et l'heure d'accès de <paramref name="path" />.Cette valeur est exprimée en heure locale.</param>
      <exception cref="T:System.IO.FileNotFoundException">Le chemin d'accès spécifié est introuvable. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides avec la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Le système d'exploitation actif n'est pas Windows NT ni une version ultérieure.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTime" /> spécifie une valeur en dehors de la plage de dates ou d'heures autorisée pour cette opération.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastAccessTimeUtc(System.String,System.DateTime)">
      <summary>Définit la date/heure, au format UTC (Temps universel coordonné), du dernier accès au fichier ou au répertoire spécifié.</summary>
      <param name="path">Fichier ou répertoire pour lequel sont définies les informations relatives à la date et à l'heure d'accès. </param>
      <param name="lastAccessTimeUtc">Objet qui contient la valeur à assigner pour la date et l'heure d'accès de <paramref name="path" />.Cette valeur est exprimée en temps UTC.</param>
      <exception cref="T:System.IO.FileNotFoundException">Le chemin d'accès spécifié est introuvable. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides avec la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Le système d'exploitation actif n'est pas Windows NT ni une version ultérieure.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTimeUtc" /> spécifie une valeur en dehors de la plage de dates ou d'heures autorisée pour cette opération.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastWriteTime(System.String,System.DateTime)">
      <summary>Définit la date/heure du dernier accès en écriture au répertoire.</summary>
      <param name="path">Chemin d'accès du répertoire. </param>
      <param name="lastWriteTime">Date/heure de la dernière écriture dans le répertoire.Cette valeur est exprimée en heure locale.</param>
      <exception cref="T:System.IO.FileNotFoundException">Le chemin d'accès spécifié est introuvable. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides avec la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Le système d'exploitation actif n'est pas Windows NT ni une version ultérieure.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTime" /> spécifie une valeur en dehors de la plage de dates ou d'heures autorisée pour cette opération.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastWriteTimeUtc(System.String,System.DateTime)">
      <summary>Définit la date/heure, au format UTC (Temps universel coordonnée), de la dernière écriture dans un répertoire.</summary>
      <param name="path">Chemin d'accès du répertoire. </param>
      <param name="lastWriteTimeUtc">Date/heure de la dernière écriture dans le répertoire.Cette valeur est exprimée en temps UTC.</param>
      <exception cref="T:System.IO.FileNotFoundException">Le chemin d'accès spécifié est introuvable. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs, ou contient un ou plusieurs caractères non valides.Vous pouvez rechercher les caractères non valides avec la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Le système d'exploitation actif n'est pas Windows NT ni une version ultérieure.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTimeUtc" /> spécifie une valeur en dehors de la plage de dates ou d'heures autorisée pour cette opération.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.IO.DirectoryInfo">
      <summary>Expose des méthodes d'instance pour créer, se déplacer dans et énumérer des répertoires et sous-répertoires.Cette classe ne peut pas être héritée.Pour parcourir le code source de .NET Framework pour ce type, consultez la
                                Source de référence.
                            </summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.DirectoryInfo.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe
                                <see cref="T:System.IO.DirectoryInfo" />classe sur le chemin d'accès spécifié.
                            </summary>
      <param name="path">Chaîne spécifiant le chemin d'accès dans lequel créer le
                                    DirectoryInfo.
                                </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />est
                                        null.
                                    </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contient des caractères non valides tels que ", &lt;, &gt; ou |.
                                    </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.Le chemin d'accès spécifié, le nom de fichier ou les deux sont trop longs.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.Create">
      <summary>Crée un répertoire.</summary>
      <exception cref="T:System.IO.IOException">Le répertoire ne peut pas être créé.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.CreateSubdirectory(System.String)">
      <summary>Crée un ou plusieurs sous-répertoires dans le chemin d'accès spécifié.Le chemin d'accès spécifié peut être relatif à cette instance de la
                            <see cref="T:System.IO.DirectoryInfo" />classe.
                        </summary>
      <returns>Le dernier répertoire spécifié dans
                                <paramref name="path" />.
                            </returns>
      <param name="path">Chemin d'accès spécifié.Il ne peut pas s'agir d'un nom de volume de disque ou UNC différent.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />ne spécifiez pas un chemin d'accès de fichier valide ou n'est pas
                                        DirectoryInfocaractères.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />est
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide, il se trouve par exemple sur un lecteur non mappé.</exception>
      <exception cref="T:System.IO.IOException">Le sous-répertoire ne peut pas être créé.ouUn fichier ou un répertoire possède déjà le nom spécifié par
                                        <paramref name="path" />.
                                    </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.Le chemin d'accès spécifié, le nom de fichier ou les deux sont trop longs.</exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'est pas autorisé à accéder à du code pour créer le répertoire.ouL'appelant ne dispose pas d'autorisation d'accès au code pour lire le répertoire décrit par le texte retourné
                                    Objet <see cref="T:System.IO.DirectoryInfo" />.
                                Cela peut se produire lorsque le
                                    <paramref name="path" />Décrit un répertoire existant.
                                </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> contient un caractère deux-points (:) qui ne fait pas partie d'un volume de lecteur (« C:\ »).
                                    </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.Delete">
      <summary>Supprime cet
                                <see cref="T:System.IO.DirectoryInfo" />Si elle est vide.
                            </summary>
      <exception cref="T:System.UnauthorizedAccessException">Le répertoire contient un fichier en lecture seule.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le répertoire décrit par cet
                                        <see cref="T:System.IO.DirectoryInfo" />objet n'existe pas ou est introuvable.
                                    </exception>
      <exception cref="T:System.IO.IOException">Le répertoire n'est pas vide.ouLe répertoire est le répertoire de travail actif de l'application.ouUn handle est ouvert sur le répertoire et le système d'exploitation est Windows XP ou une version antérieure.Ce handle ouvert peut être le résultat d'une énumération de répertoires.Pour plus d'informations, voir
                                    Comment : énumérer des répertoires et des fichiers.
                                </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.Delete(System.Boolean)">
      <summary>Supprime cette instance d'un
                                <see cref="T:System.IO.DirectoryInfo" />, indiquant s'il faut supprimer les fichiers et sous-répertoires.
                            </summary>
      <param name="recursive">truePour supprimer ce répertoire, ses sous-répertoires et tous les fichiers. Sinon,
                                    false.
                                </param>
      <exception cref="T:System.UnauthorizedAccessException">Le répertoire contient un fichier en lecture seule.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le répertoire décrit par cet
                                        <see cref="T:System.IO.DirectoryInfo" />objet n'existe pas ou est introuvable.
                                    </exception>
      <exception cref="T:System.IO.IOException">Le répertoire est en lecture seule.ouLe répertoire contient un ou plusieurs fichiers ou sous-répertoires et
                                        <paramref name="recursive" />est
                                        false.
                                    ouLe répertoire est le répertoire de travail actif de l'application.ouUn handle est ouvert sur le répertoire ou sur l'un de ses fichiers, et le système d'exploitation est Windows XP ou une version antérieure.Ce handle ouvert peut être le résultat d'une énumération de répertoires et de fichiers.Pour plus d'informations, voir
                                    Comment : énumérer des répertoires et des fichiers.
                                </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories">
      <summary>Retourne une collection énumérable d'informations de répertoire dans le répertoire actuel.</summary>
      <returns>Collection énumérable des répertoires dans le répertoire actuel.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès encapsulé dans le
                                        <see cref="T:System.IO.DirectoryInfo" />objet n'est pas valide (par exemple, il est sur un lecteur non mappé).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories(System.String)">
      <summary>Retourne une collection énumérable d'informations de répertoire qui correspond à un modèle de recherche spécifié.</summary>
      <returns>Collection énumérable de répertoires qui correspond à
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">Chaîne recherchée à trouver parmi les noms de répertoires.Ce paramètre peut contenir une combinaison de caractères littéraux et génériques * et ? (voir Remarques), mais ne prend pas en charge les expressions régulières.Le modèle par défaut est "*", qui retourne tous les fichiers.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />est
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès encapsulé dans le
                                        <see cref="T:System.IO.DirectoryInfo" />objet n'est pas valide (par exemple, il est sur un lecteur non mappé).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories(System.String,System.IO.SearchOption)">
      <summary>Retourne une collection énumérable d'informations de répertoire qui correspond à un modèle de recherche spécifié et à une option de recherche de sous-répertoires.</summary>
      <returns>Collection énumérable de répertoires qui correspond à
                                <paramref name="searchPattern" />et
                                <paramref name="searchOption" />.
                            </returns>
      <param name="searchPattern">Chaîne recherchée à trouver parmi les noms de répertoires.Ce paramètre peut contenir une combinaison de caractères littéraux et génériques * et ? (voir Remarques), mais ne prend pas en charge les expressions régulières.Le modèle par défaut est "*", qui retourne tous les fichiers.</param>
      <param name="searchOption">Une des valeurs d'énumération qui spécifie si l'opération de recherche doit inclure seulement le répertoire actuel ou tous les sous-répertoires.La valeur par défaut
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />est
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />n'est pas valide
                                        Valeur <see cref="T:System.IO.SearchOption" /></exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès encapsulé dans le
                                        <see cref="T:System.IO.DirectoryInfo" />objet n'est pas valide (par exemple, il est sur un lecteur non mappé).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles">
      <summary>Retourne une collection énumérable d'informations de fichier dans le répertoire actuel.</summary>
      <returns>Collection énumérable des fichiers dans le répertoire actuel.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès encapsulé dans le
                                        <see cref="T:System.IO.DirectoryInfo" />objet n'est pas valide (par exemple, il est sur un lecteur non mappé).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles(System.String)">
      <summary>Retourne une collection énumérable d'informations de fichier qui correspond à un modèle de recherche.</summary>
      <returns>Collection énumérable de fichiers qui correspond à
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">Chaîne recherchée à trouver parmi les noms de fichiers.Ce paramètre peut contenir une combinaison de caractères littéraux et génériques * et ? (voir Remarques), mais ne prend pas en charge les expressions régulières.Le modèle par défaut est "*", qui retourne tous les fichiers.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />est
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès encapsulé dans le
                                        <see cref="T:System.IO.DirectoryInfo" />objet n'est pas valide, (par exemple, il est sur un lecteur non mappé).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles(System.String,System.IO.SearchOption)">
      <summary>Retourne une collection énumérable d'informations de fichier qui correspond à un modèle de recherche spécifié et à une option de recherche de sous-répertoires.</summary>
      <returns>Collection énumérable de fichiers qui correspond à
                                <paramref name="searchPattern" />et
                                <paramref name="searchOption" />.
                            </returns>
      <param name="searchPattern">Chaîne recherchée à trouver parmi les noms de fichiers.Ce paramètre peut contenir une combinaison de caractères littéraux et génériques * et ? (voir Remarques), mais ne prend pas en charge les expressions régulières.Le modèle par défaut est "*", qui retourne tous les fichiers.</param>
      <param name="searchOption">Une des valeurs d'énumération qui spécifie si l'opération de recherche doit inclure seulement le répertoire actuel ou tous les sous-répertoires.La valeur par défaut
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />est
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />n'est pas valide
                                        Valeur <see cref="T:System.IO.SearchOption" /></exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès encapsulé dans le
                                        <see cref="T:System.IO.DirectoryInfo" />objet n'est pas valide (par exemple, il est sur un lecteur non mappé).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos">
      <summary>Retourne une collection énumérable d'informations de système de fichiers dans le répertoire actuel.</summary>
      <returns>Collection énumérable d'informations de système de fichiers dans le répertoire actif.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès encapsulé dans le
                                        <see cref="T:System.IO.DirectoryInfo" />objet n'est pas valide (par exemple, il est sur un lecteur non mappé).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos(System.String)">
      <summary>Retourne une collection énumérable d'informations de système de fichiers qui correspond à un modèle de recherche spécifié.</summary>
      <returns>Une collection énumérable d'objets d'informations de système de fichiers qui correspond à
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">Chaîne recherchée à trouver parmi les noms de répertoires.Ce paramètre peut contenir une combinaison de caractères littéraux et génériques * et ? (voir Remarques), mais ne prend pas en charge les expressions régulières.Le modèle par défaut est "*", qui retourne tous les fichiers.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />est
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès encapsulé dans le
                                        <see cref="T:System.IO.DirectoryInfo" />objet n'est pas valide (par exemple, il est sur un lecteur non mappé).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos(System.String,System.IO.SearchOption)">
      <summary>Retourne une collection énumérable d'informations de système de fichiers qui correspond à un modèle de recherche spécifié et à une option de recherche de sous-répertoires.</summary>
      <returns>Une collection énumérable d'objets d'informations de système de fichiers qui correspond à
                                <paramref name="searchPattern" />et
                                <paramref name="searchOption" />.
                            </returns>
      <param name="searchPattern">Chaîne recherchée à trouver parmi les noms de répertoires.Ce paramètre peut contenir une combinaison de caractères littéraux et génériques * et ? (voir Remarques), mais ne prend pas en charge les expressions régulières.Le modèle par défaut est "*", qui retourne tous les fichiers.</param>
      <param name="searchOption">Une des valeurs d'énumération qui spécifie si l'opération de recherche doit inclure seulement le répertoire actuel ou tous les sous-répertoires.La valeur par défaut
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />est
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />n'est pas valide
                                        Valeur <see cref="T:System.IO.SearchOption" /></exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès encapsulé dans le
                                        <see cref="T:System.IO.DirectoryInfo" />objet n'est pas valide (par exemple, il est sur un lecteur non mappé).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise.</exception>
    </member>
    <member name="P:System.IO.DirectoryInfo.Exists">
      <summary>Obtient une valeur indiquant si le répertoire existe.</summary>
      <returns>trueSi le répertoire existe ; Sinon,
                                false.
                            </returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories">
      <summary>Retourne les sous-répertoires du répertoire actuel.</summary>
      <returns>Un tableau de
                                Objets <see cref="T:System.IO.DirectoryInfo" />.
                            </returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès encapsulé dans le
                                        <see cref="T:System.IO.DirectoryInfo" />objet n'est pas valide, par exemple sur un lecteur non mappé.
                                    </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise.</exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories(System.String)">
      <summary>Retourne un tableau de répertoires en cours
                                <see cref="T:System.IO.DirectoryInfo" />correspondant aux critères de recherche donnée.
                            </summary>
      <returns>Tableau de type
                                DirectoryInfomise en correspondance
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">Chaîne recherchée à trouver parmi les noms de répertoires.Ce paramètre peut contenir une combinaison de caractères littéraux et génériques * et ? (voir Remarques), mais ne prend pas en charge les expressions régulières.Le modèle par défaut est "*", qui retourne tous les fichiers.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />contient un ou plusieurs caractères non valides définis par la
                                        Méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />est
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès encapsulé dans le
                                        DirectoryInfoobjet n'est pas valide (par exemple, il est sur un lecteur non mappé).
                                    </exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories(System.String,System.IO.SearchOption)">
      <summary>Retourne un tableau de répertoires en cours
                                <see cref="T:System.IO.DirectoryInfo" />correspondant aux critères de recherche donnée et à l'aide d'une valeur pour déterminer s'il faut rechercher des sous-répertoires.
                            </summary>
      <returns>Tableau de type
                                DirectoryInfomise en correspondance
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">Chaîne recherchée à trouver parmi les noms de répertoires.Ce paramètre peut contenir une combinaison de caractères littéraux et génériques * et ? (voir Remarques), mais ne prend pas en charge les expressions régulières.Le modèle par défaut est "*", qui retourne tous les fichiers.</param>
      <param name="searchOption">Une des valeurs d'énumération qui spécifie si l'opération de recherche doit inclure seulement le répertoire actuel ou tous les sous-répertoires.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />contient un ou plusieurs caractères non valides définis par la
                                        Méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />est
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />n'est pas valide
                                        Valeur <see cref="T:System.IO.SearchOption" /></exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès encapsulé dans le
                                        DirectoryInfoobjet n'est pas valide (par exemple, il est sur un lecteur non mappé).
                                    </exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles">
      <summary>Retourne la liste des fichiers du répertoire actuel.</summary>
      <returns>Tableau de type
                                <see cref="T:System.IO.FileInfo" />.
                            </returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès n'est pas valide, il se trouve par exemple sur un lecteur non mappé.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles(System.String)">
      <summary>Retourne la liste des fichiers du répertoire actuel correspondant au modèle de recherche donné.</summary>
      <returns>Tableau de type
                                <see cref="T:System.IO.FileInfo" />.
                            </returns>
      <param name="searchPattern">Chaîne recherchée à trouver parmi les noms de fichiers.Ce paramètre peut contenir une combinaison de caractères littéraux et génériques * et ? (voir Remarques), mais ne prend pas en charge les expressions régulières.Le modèle par défaut est "*", qui retourne tous les fichiers.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />contient un ou plusieurs caractères non valides définis par la
                                        Méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />est
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé).</exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles(System.String,System.IO.SearchOption)">
      <summary>Retourne une liste des fichiers du répertoire actuel correspondant au modèle de recherche donné et en utilisant une valeur pour déterminer s'il faut effectuer une recherche dans les sous-répertoires.</summary>
      <returns>Tableau de type
                                <see cref="T:System.IO.FileInfo" />.
                            </returns>
      <param name="searchPattern">Chaîne recherchée à trouver parmi les noms de fichiers.Ce paramètre peut contenir une combinaison de caractères littéraux et génériques * et ? (voir Remarques), mais ne prend pas en charge les expressions régulières.Le modèle par défaut est "*", qui retourne tous les fichiers.</param>
      <param name="searchOption">Une des valeurs d'énumération qui spécifie si l'opération de recherche doit inclure seulement le répertoire actuel ou tous les sous-répertoires.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />contient un ou plusieurs caractères non valides définis par la
                                        Méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />est
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />n'est pas valide
                                        Valeur <see cref="T:System.IO.SearchOption" /></exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé).</exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos">
      <summary>Retourne un tableau de fortement typées
                                <see cref="T:System.IO.FileSystemInfo" />entrées qui représente tous les fichiers et sous-répertoires d'un répertoire.
                            </summary>
      <returns>Un tableau de fortement typées
                                <see cref="T:System.IO.FileSystemInfo" />entrées.
                            </returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé).</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos(System.String)">
      <summary>Récupère un tableau des fortement typées
                                <see cref="T:System.IO.FileSystemInfo" />objets qui représentent les fichiers et sous-répertoires qui correspondent aux critères de recherche spécifiés.
                            </summary>
      <returns>Un tableau de fortement typées
                                FileSystemInfoobjets correspondant aux critères de recherche.
                            </returns>
      <param name="searchPattern">Chaîne recherchée à trouver parmi les noms de répertoires et les noms de fichiers.Ce paramètre peut contenir une combinaison de caractères littéraux et génériques * et ? (voir Remarques), mais ne prend pas en charge les expressions régulières.Le modèle par défaut est "*", qui retourne tous les fichiers.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />contient un ou plusieurs caractères non valides définis par la
                                        Méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />est
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé).</exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos(System.String,System.IO.SearchOption)">
      <summary>Récupère un tableau des
                                <see cref="T:System.IO.FileSystemInfo" />objets qui représentent les fichiers et les sous-répertoires correspondant au critère de recherche spécifié.
                            </summary>
      <returns>Tableau d'entrées de système de fichiers qui satisfont aux critères de recherche.</returns>
      <param name="searchPattern">Chaîne recherchée à trouver parmi les noms de répertoires et les noms de fichiers.Ce paramètre peut contenir une combinaison de caractères littéraux et génériques * et ? (voir Remarques), mais ne prend pas en charge les expressions régulières.Le modèle par défaut est "*", qui retourne tous les fichiers.</param>
      <param name="searchOption">Une des valeurs d'énumération qui spécifie si l'opération de recherche doit inclure seulement le répertoire actuel ou tous les sous-répertoires.La valeur par défaut
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />contient un ou plusieurs caractères non valides définis par la
                                        Méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />est
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />n'est pas valide
                                        Valeur <see cref="T:System.IO.SearchOption" /></exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé).</exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.MoveTo(System.String)">
      <summary>Déplace un
                                <see cref="T:System.IO.DirectoryInfo" />instance et son contenu vers un nouveau chemin.
                            </summary>
      <param name="destDirName">Nom et chemin d'accès où déplacer ce répertoire.La destination ne peut pas être un autre volume de disque ou un répertoire du même nom.Il peut s'agir d'un répertoire existant où vous voulez ajouter ce répertoire comme sous-répertoire.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destDirName" />est
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="destDirName" /> est une chaîne vide (''").
                                    </exception>
      <exception cref="T:System.IO.IOException">Une tentative pour déplacer un répertoire vers un autre volume a été effectuée.ou<paramref name="destDirName" /> existe déjà.
                                    ouVous n'êtes pas autorisé à accéder à ce chemin d'accès.ouLe répertoire déplacé et le répertoire de destination ont le même nom.</exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le répertoire de destination est introuvable.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.DirectoryInfo.Name">
      <summary>Obtient le nom de ce
                                <see cref="T:System.IO.DirectoryInfo" />instance.
                            </summary>
      <returns>Nom du répertoire.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.DirectoryInfo.Parent">
      <summary>Obtient le répertoire parent d'un sous-répertoire spécifié.</summary>
      <returns>Le répertoire parent, ou
                                nullSi le chemin d'accès est null ou si le chemin d'accès du fichier désigne une racine (tel que « \ », « C: », ou * "\\server\share").
                            </returns>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.DirectoryInfo.Root">
      <summary>Obtient la partie racine du répertoire.</summary>
      <returns>Objet qui représente la racine du répertoire.</returns>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.ToString">
      <summary>Retourne le chemin d'accès d'origine passé par l'utilisateur.</summary>
      <returns>Retourne le chemin d'accès d'origine passé par l'utilisateur.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.IO.File">
      <summary>Fournit des méthodes statiques pour créer, copier, supprimer, déplacer et ouvrir un fichier unique, et facilite la création d'objets <see cref="T:System.IO.FileStream" />.Pour parcourir le code source de .NET Framework pour ce type, consultez la Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.File.AppendAllLines(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>Ajoute des lignes à un fichier, puis ferme le fichier.Si le fichier spécifié n'existe pas, cette méthode crée un fichier, écrit les lignes spécifiées dans le fichier, puis ferme le fichier.</summary>
      <param name="path">Fichier auquel ajouter les lignes.Le fichier est créé s'il n'existe pas.</param>
      <param name="contents">Lignes à ajouter au fichier.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient des caractères non valides définis par la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">Soit<paramref name=" path " />ou <paramref name="contents" /> est null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> n'est pas valide (par exemple, le répertoire n'existe pas ou il se trouve sur un lecteur non mappé).</exception>
      <exception cref="T:System.IO.FileNotFoundException">Le fichier spécifié par <paramref name="path" /> est introuvable.</exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite lors de l'ouverture du fichier.</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> dépasse la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide.</exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'est pas autorisé à écrire dans le fichier.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> spécifie un fichier qui est en lecture seule.ouCette opération n'est pas prise en charge sur la plateforme actuelle.ou<paramref name="path" /> est un répertoire.</exception>
    </member>
    <member name="M:System.IO.File.AppendAllLines(System.String,System.Collections.Generic.IEnumerable{System.String},System.Text.Encoding)">
      <summary>Ajoute des lignes à un fichier en utilisant un encodage spécifié, puis ferme le fichier.Si le fichier spécifié n'existe pas, cette méthode crée un fichier, écrit les lignes spécifiées dans le fichier, puis ferme le fichier.</summary>
      <param name="path">Fichier auquel ajouter les lignes.Le fichier est créé s'il n'existe pas.</param>
      <param name="contents">Lignes à ajouter au fichier.</param>
      <param name="encoding">Encodage des caractères à utiliser.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient des caractères non valides définis par la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name=" path" />, <paramref name="contents" /> ou <paramref name="encoding" /> est null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> n'est pas valide (par exemple, le répertoire n'existe pas ou il se trouve sur un lecteur non mappé).</exception>
      <exception cref="T:System.IO.FileNotFoundException">Le fichier spécifié par <paramref name="path" /> est introuvable.</exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite lors de l'ouverture du fichier.</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> dépasse la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide.</exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> spécifie un fichier qui est en lecture seule.ouCette opération n'est pas prise en charge sur la plateforme actuelle.ou<paramref name="path" /> est un répertoire.ouL'appelant n'a pas l'autorisation requise.</exception>
    </member>
    <member name="M:System.IO.File.AppendAllText(System.String,System.String)">
      <summary>Ouvre un fichier, ajoute la chaîne spécifiée au fichier, puis ferme le fichier.Si le fichier n'existe pas, cette méthode crée un fichier, écrit la chaîne spécifiée dans le fichier, puis ferme le fichier.</summary>
      <param name="path">Fichier auquel ajouter la chaîne spécifiée. </param>
      <param name="contents">Chaîne à ajouter au fichier. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (par exemple, le répertoire n'existe pas ou il se trouve sur un lecteur non mappé). </exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite lors de l'ouverture du fichier. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> a spécifié un fichier qui est en lecture seule.ou Cette opération n'est pas prise en charge sur la plateforme actuelle.ou <paramref name="path" /> a spécifié un répertoire.ou L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.AppendAllText(System.String,System.String,System.Text.Encoding)">
      <summary>Ajoute la chaîne spécifiée au fichier, en créant le fichier s'il n'existe pas.</summary>
      <param name="path">Fichier auquel ajouter la chaîne spécifiée. </param>
      <param name="contents">Chaîne à ajouter au fichier. </param>
      <param name="encoding">Encodage des caractères à utiliser. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (par exemple, le répertoire n'existe pas ou il se trouve sur un lecteur non mappé). </exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite lors de l'ouverture du fichier. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> a spécifié un fichier qui est en lecture seule.ou Cette opération n'est pas prise en charge sur la plateforme actuelle.ou <paramref name="path" /> a spécifié un répertoire.ou L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.AppendText(System.String)">
      <summary>Crée un élément <see cref="T:System.IO.StreamWriter" /> qui ajoute du texte encodé en UTF-8 à un fichier existant ou à un nouveau fichier si le fichier spécifié n'existe pas.</summary>
      <returns>Writer de flux qui ajoute du texte encodé en UTF-8 au fichier spécifié ou à un nouveau fichier.</returns>
      <param name="path">Chemin d'accès du fichier auquel le texte doit être ajouté. </param>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (par exemple, le répertoire n'existe pas ou il se trouve sur un lecteur non mappé). </exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Copy(System.String,System.String)">
      <summary>Copie un fichier existant vers un nouveau fichier.Le remplacement d'un fichier du même nom n'est pas autorisé.</summary>
      <param name="sourceFileName">Fichier à copier. </param>
      <param name="destFileName">Nom du fichier de destination.Celui-ci ne peut pas être un répertoire ou un fichier existant.</param>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> ou <paramref name="destFileName" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />.ou <paramref name="sourceFileName" /> ou <paramref name="destFileName" /> spécifie un répertoire. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> ou <paramref name="destFileName" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié dans <paramref name="sourceFileName" /> ou <paramref name="destFileName" /> n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <exception cref="T:System.IO.FileNotFoundException">Impossible de trouver <paramref name="sourceFileName" />. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destFileName" /> existe.ou Une erreur d'E/S s'est produite. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceFileName" /> ou <paramref name="destFileName" /> est dans un format non valide. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Copy(System.String,System.String,System.Boolean)">
      <summary>Copie un fichier existant vers un nouveau fichier.Le remplacement d'un fichier du même nom est autorisé.</summary>
      <param name="sourceFileName">Fichier à copier. </param>
      <param name="destFileName">Nom du fichier de destination.Il ne peut pas s'agir d'un répertoire.</param>
      <param name="overwrite">true si le fichier de destination peut être remplacé ; sinon, false. </param>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. ou<paramref name="destFileName" /> est en lecture seule.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> ou <paramref name="destFileName" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />.ou <paramref name="sourceFileName" /> ou <paramref name="destFileName" /> spécifie un répertoire. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> ou <paramref name="destFileName" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié dans <paramref name="sourceFileName" /> ou <paramref name="destFileName" /> n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <exception cref="T:System.IO.FileNotFoundException">Impossible de trouver <paramref name="sourceFileName" />. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destFileName" /> existe et <paramref name="overwrite" /> est false.ou Une erreur d'E/S s'est produite. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceFileName" /> ou <paramref name="destFileName" /> est dans un format non valide. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Create(System.String)">
      <summary>Crée ou remplace un fichier dans le chemin d'accès spécifié.</summary>
      <returns>Élément <see cref="T:System.IO.FileStream" /> qui fournit l'accès en lecture/écriture au fichier spécifié dans <paramref name="path" />.</returns>
      <param name="path">Chemin d'accès et nom du fichier à créer. </param>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise.ou <paramref name="path" /> a spécifié un fichier qui est en lecture seule. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite lors de la création du fichier. </exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Create(System.String,System.Int32)">
      <summary>Crée ou remplace le fichier spécifié.</summary>
      <returns>Élément <see cref="T:System.IO.FileStream" /> avec la taille de la mémoire tampon spécifiée qui fournit l'accès en lecture/écriture au fichier spécifié dans <paramref name="path" />.</returns>
      <param name="path">Nom du fichier. </param>
      <param name="bufferSize">Nombre d'octets mis en mémoire tampon pour les opérations de lecture et d'écriture dans le fichier. </param>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise.ou <paramref name="path" /> a spécifié un fichier qui est en lecture seule. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite lors de la création du fichier. </exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Create(System.String,System.Int32,System.IO.FileOptions)">
      <summary>Crée ou remplace le fichier spécifié en spécifiant une taille de mémoire tampon et une valeur de <see cref="T:System.IO.FileOptions" /> qui décrit comment créer ou remplacer le fichier.</summary>
      <returns>Nouveau fichier avec la taille de mémoire tampon spécifiée.</returns>
      <param name="path">Nom du fichier. </param>
      <param name="bufferSize">Nombre d'octets mis en mémoire tampon pour les opérations de lecture et d'écriture dans le fichier. </param>
      <param name="options">Une des valeurs <see cref="T:System.IO.FileOptions" /> qui décrit comment créer ou remplacer le fichier.</param>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise.ou <paramref name="path" /> a spécifié un fichier qui est en lecture seule. ou<see cref="F:System.IO.FileOptions.Encrypted" /> est spécifié pour <paramref name="options" /> et le chiffrement des fichiers n'est pas pris en charge sur la plateforme actuelle.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite lors de la création du fichier. </exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise.ou <paramref name="path" /> a spécifié un fichier qui est en lecture seule. </exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise.ou <paramref name="path" /> a spécifié un fichier qui est en lecture seule. </exception>
    </member>
    <member name="M:System.IO.File.CreateText(System.String)">
      <summary>Crée ou ouvre un fichier pour écrire du texte encodé en UTF-8.</summary>
      <returns>Élément <see cref="T:System.IO.StreamWriter" /> qui écrit dans le fichier spécifié en utilisant l'encodage UTF-8.</returns>
      <param name="path">Fichier à ouvrir pour écriture. </param>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Delete(System.String)">
      <summary>Supprime le fichier spécifié. </summary>
      <param name="path">Nom du fichier à supprimer.Les caractères génériques ne sont pas pris en charge.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <exception cref="T:System.IO.IOException">Le fichier spécifié est utilisé. ouUn handle est ouvert sur le fichier et le système d'exploitation est Windows XP ou une version antérieure.Ce handle ouvert peut être le résultat d'une énumération de répertoires et de fichiers.Pour plus d'informations, consultez Comment : énumérer des répertoires et des fichiers.</exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise.ou Le fichier est un fichier exécutable est en cours d'utilisation.ou <paramref name="path" /> est un répertoire.ou <paramref name="path" /> a spécifié un fichier en lecture seule. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Exists(System.String)">
      <summary>Détermine si le fichier spécifié existe.</summary>
      <returns>true si l'appelant a les autorisations requises et si <paramref name="path" /> contient le nom d'un fichier existant ; sinon, false.Cette méthode retourne également false si <paramref name="path" /> est null, un chemin d'accès non valide ou une chaîne de longueur nulle.Si l'appelant n'a pas les autorisations suffisantes pour lire le fichier spécifié, aucune exception n'est levée et la méthode retourne false, indépendamment de l'existence de <paramref name="path" />.</returns>
      <param name="path">Fichier à vérifier. </param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetAttributes(System.String)">
      <summary>Obtient l'élément <see cref="T:System.IO.FileAttributes" /> du fichier sur le chemin d'accès.</summary>
      <returns>Élément <see cref="T:System.IO.FileAttributes" /> du fichier sur le chemin d'accès.</returns>
      <param name="path">Chemin d'accès au fichier. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est vide, ne contient que des espaces blancs ou contient des caractères non valides. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" /> représente un fichier et n'est pas valide. Il se trouve par exemple sur un lecteur non mappé ou le fichier est introuvable. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> représente un répertoire et n'est pas valide. Il se trouve par exemple sur un lecteur non mappé ou le répertoire est introuvable.</exception>
      <exception cref="T:System.IO.IOException">Ce fichier est utilisé par un autre processus.</exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetCreationTime(System.String)">
      <summary>Retourne la date/heure de création du fichier ou du répertoire spécifié.</summary>
      <returns>Structure <see cref="T:System.DateTime" /> à laquelle sont assignées la date et l'heure de création du fichier ou du répertoire spécifié.Cette valeur est exprimée en heure locale.</returns>
      <param name="path">Fichier ou répertoire pour lequel obtenir les informations de date/heure de création. </param>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetCreationTimeUtc(System.String)">
      <summary>Retourne la date et l'heure de création au format UTC (Temps universel coordonné) du fichier ou du répertoire spécifié.</summary>
      <returns>Structure <see cref="T:System.DateTime" /> à laquelle sont assignées la date et l'heure de création du fichier ou du répertoire spécifié.Cette valeur est exprimée en temps UTC.</returns>
      <param name="path">Fichier ou répertoire pour lequel obtenir les informations de date/heure de création. </param>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastAccessTime(System.String)">
      <summary>Retourne la date/heure du dernier accès au fichier ou au répertoire spécifié.</summary>
      <returns>Structure <see cref="T:System.DateTime" /> définie avec la date et l'heure de dernier accès au fichier ou au répertoire spécifié.Cette valeur est exprimée en heure locale.</returns>
      <param name="path">Fichier ou répertoire pour lequel obtenir les informations de date/heure d'accès. </param>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastAccessTimeUtc(System.String)">
      <summary>Retourne la date/heure au format UTC (Temps universel coordonné) du dernier accès au fichier ou au répertoire spécifié.</summary>
      <returns>Structure <see cref="T:System.DateTime" /> définie avec la date et l'heure de dernier accès au fichier ou au répertoire spécifié.Cette valeur est exprimée en temps UTC.</returns>
      <param name="path">Fichier ou répertoire pour lequel obtenir les informations de date/heure d'accès. </param>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastWriteTime(System.String)">
      <summary>Retourne la date/heure du dernier accès en écriture au fichier ou au répertoire spécifié.</summary>
      <returns>Structure <see cref="T:System.DateTime" /> ayant pour valeur la date et l'heure de la dernière écriture dans le fichier ou le répertoire spécifié.Cette valeur est exprimée en heure locale.</returns>
      <param name="path">Fichier ou répertoire pour lequel obtenir les informations de date/heure d'accès en écriture. </param>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastWriteTimeUtc(System.String)">
      <summary>Retourne la date/heure au format UTC (Temps universel coordonné) de la dernière écriture dans le fichier ou le répertoire spécifié.</summary>
      <returns>Structure <see cref="T:System.DateTime" /> ayant pour valeur la date et l'heure de la dernière écriture dans le fichier ou le répertoire spécifié.Cette valeur est exprimée en temps UTC.</returns>
      <param name="path">Fichier ou répertoire pour lequel obtenir les informations de date/heure d'accès en écriture. </param>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Move(System.String,System.String)">
      <summary>Déplace un fichier spécifié à un nouvel emplacement, en permettant de spécifier un nouveau nom.</summary>
      <param name="sourceFileName">Nom du fichier à déplacer.Peut inclure un chemin d'accès absolu ou relatif.</param>
      <param name="destFileName">Nouveaux nom et chemin d'accès au fichier.</param>
      <exception cref="T:System.IO.IOException">Le fichier de destination existe déjà.ouImpossible de trouver <paramref name="sourceFileName" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> ou <paramref name="destFileName" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> ou <paramref name="destFileName" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient des caractères non valides comme défini dans <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié dans <paramref name="sourceFileName" /> ou <paramref name="destFileName" /> n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceFileName" /> ou <paramref name="destFileName" /> est dans un format non valide. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode)">
      <summary>Ouvre un élément <see cref="T:System.IO.FileStream" /> sur le chemin d'accès spécifié avec un accès en lecture/écriture.</summary>
      <returns>Élément <see cref="T:System.IO.FileStream" /> ouvert dans le mode et le chemin d'accès spécifiés, avec un accès en lecture/écriture et non partagé.</returns>
      <param name="path">Fichier à ouvrir. </param>
      <param name="mode">Valeur <see cref="T:System.IO.FileMode" /> qui spécifie si un fichier est créé s'il n'existe pas et détermine si le contenu des fichiers existants est conservé ou remplacé. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite lors de l'ouverture du fichier. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> a spécifié un fichier qui est en lecture seule.ou Cette opération n'est pas prise en charge sur la plateforme actuelle.ou <paramref name="path" /> a spécifié un répertoire.ou L'appelant n'a pas l'autorisation requise. ou<paramref name="mode" /> est <see cref="F:System.IO.FileMode.Create" /> et le fichier spécifié est un fichier masqué.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> a spécifié une valeur non valide. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Le fichier spécifié dans <paramref name="path" /> est introuvable. </exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode,System.IO.FileAccess)">
      <summary>Ouvre un élément <see cref="T:System.IO.FileStream" /> sur le chemin d'accès spécifié, avec le mode et l'accès spécifiés.</summary>
      <returns>Élément <see cref="T:System.IO.FileStream" /> non partagé qui fournit l'accès au fichier spécifié avec le mode et l'accès spécifiés.</returns>
      <param name="path">Fichier à ouvrir. </param>
      <param name="mode">Valeur <see cref="T:System.IO.FileMode" /> qui spécifie si un fichier est créé s'il n'existe pas et détermine si le contenu des fichiers existants est conservé ou remplacé. </param>
      <param name="access">Valeur de <see cref="T:System.IO.FileAccess" /> spécifiant les opérations qui peuvent être effectuées sur le fichier. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />.ou <paramref name="access" /> a spécifié Read et <paramref name="mode" /> a spécifié Create, CreateNew, Truncate ou Append. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite lors de l'ouverture du fichier. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> a spécifié un fichier qui est en lecture seule et <paramref name="access" /> n'est pas Read.ou <paramref name="path" /> a spécifié un répertoire.ou L'appelant n'a pas l'autorisation requise. ou<paramref name="mode" /> est <see cref="F:System.IO.FileMode.Create" /> et le fichier spécifié est un fichier masqué.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> ou <paramref name="access" /> a spécifié une valeur non valide. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Le fichier spécifié dans <paramref name="path" /> est introuvable. </exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>Ouvre un élément <see cref="T:System.IO.FileStream" /> sur le chemin d'accès spécifié, dans le mode spécifié avec accès en lecture, en écriture ou en lecture/écriture, et l'option de partage spécifiée.</summary>
      <returns>Ouvre un élément <see cref="T:System.IO.FileStream" /> sur le chemin d'accès spécifié, dans le mode spécifié avec accès en lecture, en écriture ou en lecture/écriture, et l'option de partage spécifiée.</returns>
      <param name="path">Fichier à ouvrir. </param>
      <param name="mode">Valeur <see cref="T:System.IO.FileMode" /> qui spécifie si un fichier est créé s'il n'existe pas et détermine si le contenu des fichiers existants est conservé ou remplacé. </param>
      <param name="access">Valeur de <see cref="T:System.IO.FileAccess" /> spécifiant les opérations qui peuvent être effectuées sur le fichier. </param>
      <param name="share">Valeur de <see cref="T:System.IO.FileShare" /> spécifiant le type d'accès que les autres threads ont sur le fichier. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />.ou <paramref name="access" /> a spécifié Read et <paramref name="mode" /> a spécifié Create, CreateNew, Truncate ou Append. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite lors de l'ouverture du fichier. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> a spécifié un fichier qui est en lecture seule et <paramref name="access" /> n'est pas Read.ou <paramref name="path" /> a spécifié un répertoire.ou L'appelant n'a pas l'autorisation requise. ou<paramref name="mode" /> est <see cref="F:System.IO.FileMode.Create" /> et le fichier spécifié est un fichier masqué.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" />, <paramref name="access" /> ou <paramref name="share" /> a spécifié une valeur non valide. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Le fichier spécifié dans <paramref name="path" /> est introuvable. </exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.OpenRead(System.String)">
      <summary>Ouvre un fichier existant pour y accéder en lecture.</summary>
      <returns>Élément <see cref="T:System.IO.FileStream" /> en lecture seule sur le chemin d'accès spécifié.</returns>
      <param name="path">Fichier à ouvrir pour lecture. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> a spécifié un répertoire.ou L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Le fichier spécifié dans <paramref name="path" /> est introuvable. </exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite lors de l'ouverture du fichier. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.OpenText(System.String)">
      <summary>Ouvre un fichier texte encodé en UTF-8 existant pour lecture.</summary>
      <returns>Élément <see cref="T:System.IO.StreamReader" /> sur le chemin d'accès spécifié.</returns>
      <param name="path">Fichier à ouvrir pour lecture. </param>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <exception cref="T:System.IO.FileNotFoundException">Le fichier spécifié dans <paramref name="path" /> est introuvable. </exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.OpenWrite(System.String)">
      <summary>Ouvre un fichier existant ou crée un nouveau fichier pour écriture.</summary>
      <returns>Objet <see cref="T:System.IO.FileStream" /> non partagé sur le chemin d'accès spécifié avec un accès <see cref="F:System.IO.FileAccess.Write" />.</returns>
      <param name="path">Fichier à ouvrir pour écriture. </param>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise.ou <paramref name="path" /> a spécifié un fichier ou un répertoire en lecture seule. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllBytes(System.String)">
      <summary>Ouvre un fichier binaire, lit le contenu du fichier dans un tableau d'octets, puis ferme le fichier.</summary>
      <returns>Tableau d'octets contenant le contenu du fichier.</returns>
      <param name="path">Fichier à ouvrir pour lecture. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite lors de l'ouverture du fichier. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Cette opération n'est pas prise en charge sur la plateforme actuelle.ou <paramref name="path" /> a spécifié un répertoire.ou L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Le fichier spécifié dans <paramref name="path" /> est introuvable. </exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllLines(System.String)">
      <summary>Ouvre un fichier texte, lit toutes les lignes du fichier, puis ferme le fichier.</summary>
      <returns>Tableau de chaînes contenant toutes les lignes du fichier.</returns>
      <param name="path">Fichier à ouvrir pour lecture. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite lors de l'ouverture du fichier. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> a spécifié un fichier qui est en lecture seule.ou Cette opération n'est pas prise en charge sur la plateforme actuelle.ou <paramref name="path" /> a spécifié un répertoire.ou L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Le fichier spécifié dans <paramref name="path" /> est introuvable. </exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllLines(System.String,System.Text.Encoding)">
      <summary>Ouvre un fichier, lit toutes les lignes du fichier avec l'encodage spécifié, puis ferme le fichier.</summary>
      <returns>Tableau de chaînes contenant toutes les lignes du fichier.</returns>
      <param name="path">Fichier à ouvrir pour lecture. </param>
      <param name="encoding">Encodage appliqué au contenu du fichier. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite lors de l'ouverture du fichier. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> a spécifié un fichier qui est en lecture seule.ou Cette opération n'est pas prise en charge sur la plateforme actuelle.ou <paramref name="path" /> a spécifié un répertoire.ou L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Le fichier spécifié dans <paramref name="path" /> est introuvable. </exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllText(System.String)">
      <summary>Ouvre un fichier texte, lit toutes les lignes du fichier, puis ferme le fichier.</summary>
      <returns>Chaîne contenant toutes les lignes du fichier.</returns>
      <param name="path">Fichier à ouvrir pour lecture. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite lors de l'ouverture du fichier. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> a spécifié un fichier qui est en lecture seule.ou Cette opération n'est pas prise en charge sur la plateforme actuelle.ou <paramref name="path" /> a spécifié un répertoire.ou L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Le fichier spécifié dans <paramref name="path" /> est introuvable. </exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllText(System.String,System.Text.Encoding)">
      <summary>Ouvre un fichier, lit toutes les lignes du fichier avec l'encodage spécifié, puis ferme le fichier.</summary>
      <returns>Chaîne contenant toutes les lignes du fichier.</returns>
      <param name="path">Fichier à ouvrir pour lecture. </param>
      <param name="encoding">Encodage appliqué au contenu du fichier. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite lors de l'ouverture du fichier. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> a spécifié un fichier qui est en lecture seule.ou Cette opération n'est pas prise en charge sur la plateforme actuelle.ou <paramref name="path" /> a spécifié un répertoire.ou L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Le fichier spécifié dans <paramref name="path" /> est introuvable. </exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadLines(System.String)">
      <summary>Lit les lignes d'un fichier.</summary>
      <returns>Toutes les lignes du fichier ou les lignes qui sont le résultat d'une requête.</returns>
      <param name="path">Fichier à lire.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient seulement des espaces blancs ou contient un ou plusieurs caractères non valides définis par la <see cref="M:System.IO.Path.GetInvalidPathChars" /> (méthode).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> n'est pas valide (par exemple, il se trouve sur un lecteur non mappé).</exception>
      <exception cref="T:System.IO.FileNotFoundException">Le fichier spécifié par <paramref name="path" /> est introuvable.</exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite lors de l'ouverture du fichier.</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> dépasse la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> spécifie un fichier qui est en lecture seule.ouCette opération n'est pas prise en charge sur la plateforme actuelle.ou<paramref name="path" /> est un répertoire.ouL'appelant n'a pas l'autorisation requise.</exception>
    </member>
    <member name="M:System.IO.File.ReadLines(System.String,System.Text.Encoding)">
      <summary>Lit les lignes d'un fichier qui a un encodage spécifié.</summary>
      <returns>Toutes les lignes du fichier ou les lignes qui sont le résultat d'une requête.</returns>
      <param name="path">Fichier à lire.</param>
      <param name="encoding">Encodage appliqué au contenu du fichier. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient des caractères non valides définis par la méthode <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> n'est pas valide (par exemple, il se trouve sur un lecteur non mappé).</exception>
      <exception cref="T:System.IO.FileNotFoundException">Le fichier spécifié par <paramref name="path" /> est introuvable.</exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite lors de l'ouverture du fichier.</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> dépasse la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> spécifie un fichier qui est en lecture seule.ouCette opération n'est pas prise en charge sur la plateforme actuelle.ou<paramref name="path" /> est un répertoire.ouL'appelant n'a pas l'autorisation requise.</exception>
    </member>
    <member name="M:System.IO.File.SetAttributes(System.String,System.IO.FileAttributes)">
      <summary>Définit l'élément <see cref="T:System.IO.FileAttributes" /> spécifié du fichier sur le chemin d'accès spécifié.</summary>
      <param name="path">Chemin d'accès au fichier. </param>
      <param name="fileAttributes">Combinaison d'opérations de bits des valeurs d'énumération. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est vide, ne contient que des espaces blancs, contient des caractères non valides ou l'attribut de fichier n'est pas valide. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <exception cref="T:System.IO.FileNotFoundException">Impossible de trouver le fichier.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> a spécifié un fichier qui est en lecture seule.ou Cette opération n'est pas prise en charge sur la plateforme actuelle.ou <paramref name="path" /> a spécifié un répertoire.ou L'appelant n'a pas l'autorisation requise.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetCreationTime(System.String,System.DateTime)">
      <summary>Définit la date/heure de création du fichier.</summary>
      <param name="path">Fichier pour lequel définir les informations de date/heure de création. </param>
      <param name="creationTime">Élément <see cref="T:System.DateTime" /> contenant la valeur à affecter pour la date/heure de création de <paramref name="path" />.Cette valeur est exprimée en heure locale.</param>
      <exception cref="T:System.IO.FileNotFoundException">Le chemin d'accès spécifié est introuvable. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite lors de l'exécution de l'opération. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> spécifie une valeur en dehors de la plage de dates, d'heures ou des deux, autorisée pour cette opération. </exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetCreationTimeUtc(System.String,System.DateTime)">
      <summary>Définit la date/heure au format UTC (Temps universel coordonné) de création du fichier.</summary>
      <param name="path">Fichier pour lequel définir les informations de date/heure de création. </param>
      <param name="creationTimeUtc">Élément <see cref="T:System.DateTime" /> contenant la valeur à affecter pour la date/heure de création de <paramref name="path" />.Cette valeur est exprimée en temps UTC.</param>
      <exception cref="T:System.IO.FileNotFoundException">Le chemin d'accès spécifié est introuvable. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite lors de l'exécution de l'opération. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> spécifie une valeur en dehors de la plage de dates, d'heures ou des deux, autorisée pour cette opération. </exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastAccessTime(System.String,System.DateTime)">
      <summary>Définit la date et l'heure du dernier accès au fichier spécifié.</summary>
      <param name="path">Fichier pour lequel définir les informations de date/heure d'accès. </param>
      <param name="lastAccessTime">Élément <see cref="T:System.DateTime" /> contenant la valeur à affecter pour la date/heure du dernier accès à <paramref name="path" />.Cette valeur est exprimée en heure locale.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Le chemin d'accès spécifié est introuvable. </exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTime" /> spécifie une valeur en dehors de la plage de dates ou d'heures autorisée pour cette opération.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastAccessTimeUtc(System.String,System.DateTime)">
      <summary>Définit la date/heure au format UTC (Temps universel coordonné) du dernier accès au fichier spécifié.</summary>
      <param name="path">Fichier pour lequel définir les informations de date/heure d'accès. </param>
      <param name="lastAccessTimeUtc">Élément <see cref="T:System.DateTime" /> contenant la valeur à affecter pour la date/heure du dernier accès à <paramref name="path" />.Cette valeur est exprimée en temps UTC.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Le chemin d'accès spécifié est introuvable. </exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTimeUtc" /> spécifie une valeur en dehors de la plage de dates ou d'heures autorisée pour cette opération.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastWriteTime(System.String,System.DateTime)">
      <summary>Définit la date/heure du dernier accès en écriture au fichier spécifié.</summary>
      <param name="path">Fichier pour lequel définir les informations de date/heure. </param>
      <param name="lastWriteTime">Élément <see cref="T:System.DateTime" /> contenant la valeur à affecter pour la date/heure du dernier accès en écriture à <paramref name="path" />.Cette valeur est exprimée en heure locale.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Le chemin d'accès spécifié est introuvable. </exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTime" /> spécifie une valeur en dehors de la plage de dates ou d'heures autorisée pour cette opération.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastWriteTimeUtc(System.String,System.DateTime)">
      <summary>Définit la date/heure au format UTC (Temps universel coordonné) de la dernière écriture dans le fichier spécifié.</summary>
      <param name="path">Fichier pour lequel définir les informations de date/heure. </param>
      <param name="lastWriteTimeUtc">Élément <see cref="T:System.DateTime" /> contenant la valeur à affecter pour la date/heure du dernier accès en écriture à <paramref name="path" />.Cette valeur est exprimée en temps UTC.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Le chemin d'accès spécifié est introuvable. </exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTimeUtc" /> spécifie une valeur en dehors de la plage de dates ou d'heures autorisée pour cette opération.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.WriteAllBytes(System.String,System.Byte[])">
      <summary>Crée un nouveau fichier, écrit le tableau d'octets spécifié dans le fichier, puis ferme le fichier.Si le fichier cible existe déjà, il est remplacé.</summary>
      <param name="path">Fichier dans lequel écrire. </param>
      <param name="bytes">Octets à écrire dans le fichier. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> est null ou le tableau d'octets est vide. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite lors de l'ouverture du fichier. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> a spécifié un fichier qui est en lecture seule.ou Cette opération n'est pas prise en charge sur la plateforme actuelle.ou <paramref name="path" /> a spécifié un répertoire.ou L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.WriteAllLines(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>Crée un fichier, écrit une collection de chaînes dans le fichier, puis ferme le fichier.</summary>
      <param name="path">Fichier dans lequel écrire.</param>
      <param name="contents">Lignes à écrire dans le fichier.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient seulement des espaces blancs ou contient un ou plusieurs caractères non valides définis par la <see cref="M:System.IO.Path.GetInvalidPathChars" /> (méthode).</exception>
      <exception cref="T:System.ArgumentNullException">Soit<paramref name=" path " />ou <paramref name="contents" /> est null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> n'est pas valide (par exemple, il se trouve sur un lecteur non mappé).</exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite lors de l'ouverture du fichier.</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> dépasse la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide.</exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> spécifie un fichier qui est en lecture seule.ouCette opération n'est pas prise en charge sur la plateforme actuelle.ou<paramref name="path" /> est un répertoire.ouL'appelant n'a pas l'autorisation requise.</exception>
    </member>
    <member name="M:System.IO.File.WriteAllLines(System.String,System.Collections.Generic.IEnumerable{System.String},System.Text.Encoding)">
      <summary>Crée un fichier en utilisant l'encodage spécifié, écrit une collection de chaînes dans le fichier, puis ferme le fichier.</summary>
      <param name="path">Fichier dans lequel écrire.</param>
      <param name="contents">Lignes à écrire dans le fichier.</param>
      <param name="encoding">Encodage des caractères à utiliser.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient seulement des espaces blancs ou contient un ou plusieurs caractères non valides définis par la <see cref="M:System.IO.Path.GetInvalidPathChars" /> (méthode).</exception>
      <exception cref="T:System.ArgumentNullException">Soit<paramref name=" path" />,<paramref name=" contents" />, ou <paramref name="encoding" /> est null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> n'est pas valide (par exemple, il se trouve sur un lecteur non mappé).</exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite lors de l'ouverture du fichier.</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> dépasse la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide.</exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> spécifie un fichier qui est en lecture seule.ouCette opération n'est pas prise en charge sur la plateforme actuelle.ou<paramref name="path" /> est un répertoire.ouL'appelant n'a pas l'autorisation requise.</exception>
    </member>
    <member name="M:System.IO.File.WriteAllText(System.String,System.String)">
      <summary>Crée un nouveau fichier, écrit la chaîne spécifiée dans le fichier, puis ferme le fichier.Si le fichier cible existe déjà, il est remplacé.</summary>
      <param name="path">Fichier dans lequel écrire. </param>
      <param name="contents">Chaîne à écrire dans le fichier. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> est null ou <paramref name="contents" /> est vide.  </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite lors de l'ouverture du fichier. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> a spécifié un fichier qui est en lecture seule.ou Cette opération n'est pas prise en charge sur la plateforme actuelle.ou <paramref name="path" /> a spécifié un répertoire.ou L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.WriteAllText(System.String,System.String,System.Text.Encoding)">
      <summary>Crée un nouveau fichier, écrit la chaîne spécifiée dans le fichier en utilisant l'encodage spécifié, puis ferme le fichier.Si le fichier cible existe déjà, il est remplacé.</summary>
      <param name="path">Fichier dans lequel écrire. </param>
      <param name="contents">Chaîne à écrire dans le fichier. </param>
      <param name="encoding">Encodage à appliquer à la chaîne.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> est null ou <paramref name="contents" /> est vide. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite lors de l'ouverture du fichier. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> a spécifié un fichier qui est en lecture seule.ou Cette opération n'est pas prise en charge sur la plateforme actuelle.ou <paramref name="path" /> a spécifié un répertoire.ou L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="path" /> n'est pas valide. </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.IO.FileInfo">
      <summary>Fournit des propriétés et des méthodes d'instance pour créer, copier, supprimer, déplacer et ouvrir des fichiers, et facilite la création d'objets <see cref="T:System.IO.FileStream" />.Cette classe ne peut pas être héritée.Pour parcourir le code source de .NET Framework pour ce type, consultez la Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileInfo.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.IO.FileInfo" />, qui sert de wrapper pour un chemin d'accès de fichier.</summary>
      <param name="fileName">Nom qualifié complet du nouveau fichier, ou nom de fichier relatif.Ne placez pas de caractère de séparation de répertoire à la fin du chemin d'accès.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="fileName" /> a la valeur null. </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentException">Le nom de fichier est vide, ne contient que des espaces blancs ou contient des caractères non valides. </exception>
      <exception cref="T:System.UnauthorizedAccessException">L'accès à <paramref name="fileName" /> est refusé. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="fileName" /> contient un signe deux-points (:) au milieu de la chaîne. </exception>
    </member>
    <member name="M:System.IO.FileInfo.AppendText">
      <summary>Crée un élément <see cref="T:System.IO.StreamWriter" /> qui ajoute du texte au fichier représenté par cette instance de <see cref="T:System.IO.FileInfo" />.</summary>
      <returns>Nouveau StreamWriter.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.CopyTo(System.String)">
      <summary>Copie un fichier existant vers un nouveau fichier, en interdisant le remplacement d'un fichier existant.</summary>
      <returns>Nouveau fichier avec un chemin d'accès qualifié complet.</returns>
      <param name="destFileName">Nom du nouveau fichier de destination de la copie. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destFileName" /> est vide, ne contient que des espaces blancs ou contient des caractères non valides. </exception>
      <exception cref="T:System.IO.IOException">Une erreur se produit, ou le fichier de destination existe déjà. </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destFileName" /> a la valeur null. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Un chemin d'accès de répertoire est passé, ou bien le fichier est déplacé vers un lecteur différent. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le répertoire spécifié dans <paramref name="destFileName" /> n'existe pas.</exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destFileName" /> contient un signe deux-points (:) dans la chaîne mais ne spécifie pas le volume. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.CopyTo(System.String,System.Boolean)">
      <summary>Copie un fichier existant vers un nouveau fichier, en autorisant le remplacement d'un fichier existant.</summary>
      <returns>Nouveau fichier ou remplacement d'un fichier existant si <paramref name="overwrite" /> est défini à true.Si le fichier existe et que <paramref name="overwrite" /> est défini à false, une exception <see cref="T:System.IO.IOException" /> est levée.</returns>
      <param name="destFileName">Nom du nouveau fichier de destination de la copie. </param>
      <param name="overwrite">true pour autoriser le remplacement d'un fichier existant ; sinon, false. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destFileName" /> est vide, ne contient que des espaces blancs ou contient des caractères non valides. </exception>
      <exception cref="T:System.IO.IOException">Une erreur s'est produite ou le fichier de destination existe déjà et <paramref name="overwrite" /> est false. </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destFileName" /> a la valeur null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le répertoire spécifié dans <paramref name="destFileName" /> n'existe pas.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Un chemin d'accès de répertoire est passé, ou bien le fichier est déplacé vers un lecteur différent. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destFileName" /> contient un signe deux-points (:) au milieu de la chaîne. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Create">
      <summary>Crée un fichier.</summary>
      <returns>Nouveau fichier.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.CreateText">
      <summary>Crée un élément <see cref="T:System.IO.StreamWriter" /> qui écrit un nouveau fichier texte.</summary>
      <returns>Nouveau StreamWriter.</returns>
      <exception cref="T:System.UnauthorizedAccessException">Le nom de fichier est un répertoire. </exception>
      <exception cref="T:System.IO.IOException">Le disque est en lecture seule. </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Delete">
      <summary>Supprime définitivement un fichier.</summary>
      <exception cref="T:System.IO.IOException">Le fichier cible est ouvert ou mappé en mémoire sur un ordinateur fonctionnant sous Microsoft Windows NT.ouUn handle est ouvert sur le fichier et le système d'exploitation est Windows XP ou une version antérieure.Ce handle ouvert peut être le résultat d'une énumération de répertoires et de fichiers.Pour plus d'informations, consultez Comment : énumérer des répertoires et des fichiers.</exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Le chemin d'accès est un répertoire. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.Directory">
      <summary>Obtient une instance du répertoire parent.</summary>
      <returns>Objet <see cref="T:System.IO.DirectoryInfo" /> représentant le répertoire parent de ce fichier.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide, il se trouve par exemple sur un lecteur non mappé. </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.DirectoryName">
      <summary>Obtient une chaîne représentant le chemin d'accès complet du répertoire.</summary>
      <returns>Chaîne représentant le chemin d'accès complet du répertoire.</returns>
      <exception cref="T:System.ArgumentNullException">null a été passé comme nom de répertoire. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès qualifié complet est de 260 caractères ou plus.</exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.Exists">
      <summary>Obtient une valeur indiquant si un fichier existe.</summary>
      <returns>true si le fichier existe ; false si le fichier n'existe pas ou si le fichier est un répertoire.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileInfo.IsReadOnly">
      <summary>Obtient ou définit une valeur qui détermine si le fichier actuel est en lecture seule.</summary>
      <returns>true si le fichier actuel est en lecture seule ; sinon, false.</returns>
      <exception cref="T:System.IO.FileNotFoundException">Le fichier décrit par l'objet <see cref="T:System.IO.FileInfo" /> en cours est introuvable.</exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite lors de l'ouverture du fichier.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Cette opération n'est pas prise en charge sur la plateforme actuelle.ou L'appelant n'a pas l'autorisation requise.</exception>
      <exception cref="T:System.ArgumentException">L'utilisateur ne dispose pas d'une autorisation d'écriture, mais a tenté d'attribuer à cette propriété la valeur false.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileInfo.Length">
      <summary>Obtient la taille en octets du fichier actuel.</summary>
      <returns>Taille du fichier actuel en octets.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> ne peut pas mettre à jour l'état du fichier ou du répertoire. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Le fichier n'existe pas.ou La propriété Length est appelée pour un répertoire. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileInfo.MoveTo(System.String)">
      <summary>Déplace un fichier spécifié à un nouvel emplacement, en permettant de spécifier un nouveau nom.</summary>
      <param name="destFileName">Chemin d'accès de destination du fichier, qui peut spécifier un autre nom de fichier. </param>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S se produit ; par exemple, le fichier de destination existe déjà ou le périphérique de destination n'est pas prêt. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destFileName" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="destFileName" /> est vide, ne contient que des espaces blancs ou contient des caractères non valides. </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="destFileName" /> est en lecture seule ou est un répertoire. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Le fichier est introuvable. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide, il se trouve par exemple sur un lecteur non mappé. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destFileName" /> contient un signe deux-points (:) au milieu de la chaîne. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.Name">
      <summary>Obtient le nom du fichier.</summary>
      <returns>Nom du fichier.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode)">
      <summary>Ouvre un fichier dans le mode spécifié.</summary>
      <returns>Fichier ouvert dans le mode spécifié, avec accès en lecture/écriture et non partagé.</returns>
      <param name="mode">Constante <see cref="T:System.IO.FileMode" /> spécifiant le mode (par exemple Open ou Append) dans lequel ouvrir le fichier. </param>
      <exception cref="T:System.IO.FileNotFoundException">Le fichier est introuvable. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Le fichier est en lecture seule ou est un répertoire. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide, il se trouve par exemple sur un lecteur non mappé. </exception>
      <exception cref="T:System.IO.IOException">Le fichier est déjà ouvert. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode,System.IO.FileAccess)">
      <summary>Ouvre un fichier dans le mode spécifié, avec accès en lecture, en écriture ou en lecture/écriture.</summary>
      <returns>Objet <see cref="T:System.IO.FileStream" /> ouvert dans le mode et l'accès spécifiés, et non partagé.</returns>
      <param name="mode">Constante <see cref="T:System.IO.FileMode" /> spécifiant le mode (par exemple Open ou Append) dans lequel ouvrir le fichier. </param>
      <param name="access">Constante <see cref="T:System.IO.FileAccess" /> spécifiant si le fichier doit être ouvert avec l'accès Read, Write ou ReadWrite. </param>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Le fichier est introuvable. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> est en lecture seule ou est un répertoire. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide, il se trouve par exemple sur un lecteur non mappé. </exception>
      <exception cref="T:System.IO.IOException">Le fichier est déjà ouvert. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>Ouvre un fichier dans le mode spécifié, avec accès en lecture, en écriture ou en lecture/écriture, et l'option de partage spécifiée.</summary>
      <returns>Objet <see cref="T:System.IO.FileStream" /> ouvert avec le mode, l'accès et les options de partage spécifiés.</returns>
      <param name="mode">Constante <see cref="T:System.IO.FileMode" /> spécifiant le mode (par exemple Open ou Append) dans lequel ouvrir le fichier. </param>
      <param name="access">Constante <see cref="T:System.IO.FileAccess" /> spécifiant si le fichier doit être ouvert avec l'accès Read, Write ou ReadWrite. </param>
      <param name="share">Constante <see cref="T:System.IO.FileShare" /> spécifiant le type d'accès qu'ont d'autres objets FileStream à ce fichier. </param>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Le fichier est introuvable. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> est en lecture seule ou est un répertoire. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide, il se trouve par exemple sur un lecteur non mappé. </exception>
      <exception cref="T:System.IO.IOException">Le fichier est déjà ouvert. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.OpenRead">
      <summary>Crée un élément <see cref="T:System.IO.FileStream" /> en lecture seule.</summary>
      <returns>Nouvel objet <see cref="T:System.IO.FileStream" /> en lecture seule.</returns>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> est en lecture seule ou est un répertoire. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide, il se trouve par exemple sur un lecteur non mappé. </exception>
      <exception cref="T:System.IO.IOException">Le fichier est déjà ouvert. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.OpenText">
      <summary>Crée un élément <see cref="T:System.IO.StreamReader" /> avec encodage UTF-8 qui lit un fichier texte existant.</summary>
      <returns>Nouvel élément StreamReader avec encodage UTF-8.</returns>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Le fichier est introuvable. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> est en lecture seule ou est un répertoire. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide, il se trouve par exemple sur un lecteur non mappé. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.OpenWrite">
      <summary>Crée un élément <see cref="T:System.IO.FileStream" /> en écriture seule.</summary>
      <returns>Objet <see cref="T:System.IO.FileStream" /> en écriture seule non partagé pour un fichier nouveau ou existant.</returns>
      <exception cref="T:System.UnauthorizedAccessException">Le chemin d'accès spécifié quand la création d'une instance de l'objet <see cref="T:System.IO.FileInfo" /> est en lecture seule ou est un répertoire.  </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié quand la création d'une instance de l'objet <see cref="T:System.IO.FileInfo" /> n'est pas valide, tel qu'un emplacement sur un lecteur non mappé. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.ToString">
      <summary>Retourne le chemin d'accès sous forme de chaîne.</summary>
      <returns>Chaîne représentant le chemin d'accès.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.FileOptions">
      <summary>Représente des options avancées pour créer un objet <see cref="T:System.IO.FileStream" />.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.FileOptions.Asynchronous">
      <summary>Indique qu'un fichier peut être utilisé pour la lecture et l'écriture asynchrones. </summary>
    </member>
    <member name="F:System.IO.FileOptions.DeleteOnClose">
      <summary>Indique qu'un fichier est automatiquement supprimé lorsqu'il n'est plus en cours d'utilisation.</summary>
    </member>
    <member name="F:System.IO.FileOptions.Encrypted">
      <summary>Indique qu'un fichier est chiffré et qu'il peut être déchiffré uniquement en utilisant le même compte d'utilisateur que celui utilisé pour le chiffrement.</summary>
    </member>
    <member name="F:System.IO.FileOptions.None">
      <summary>Indique qu'aucune option supplémentaire ne doit être utilisée lors de la création d'un objet <see cref="T:System.IO.FileStream" />.</summary>
    </member>
    <member name="F:System.IO.FileOptions.RandomAccess">
      <summary>Indique que le fichier est accédé aléatoirement.Le système peut utiliser cette indication pour optimiser la mise en cache du fichier.</summary>
    </member>
    <member name="F:System.IO.FileOptions.SequentialScan">
      <summary>Indique que le fichier est accessible séquentiellement du début à la fin.Le système peut utiliser cette indication pour optimiser la mise en cache du fichier.Si une application déplace le pointeur de fichier pour l'accès aléatoire, une mise en cache optimale peut ne pas se produire ; toutefois, une opération correcte est garantie.</summary>
    </member>
    <member name="F:System.IO.FileOptions.WriteThrough">
      <summary>Indique que le système doit écrire dans n'importe quel cache intermédiaire et aller directement au disque.</summary>
    </member>
    <member name="T:System.IO.FileStream">
      <summary>Fournit un élément <see cref="T:System.IO.Stream" /> pour un fichier, prenant en charge les opérations en lecture et en écriture aussi bien synchrones qu'asynchrones.Pour parcourir le code source de .NET Framework pour ce type, consultez la Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.#ctor(Microsoft.Win32.SafeHandles.SafeFileHandle,System.IO.FileAccess)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.IO.FileStream" /> pour le handle de fichier spécifié, avec l'autorisation d'accès en lecture/écriture spécifiée. </summary>
      <param name="handle">Handle de fichier pour le fichier que l'objet FileStream actuel doit encapsuler. </param>
      <param name="access">Constante qui définit les propriétés <see cref="P:System.IO.FileStream.CanRead" /> et <see cref="P:System.IO.FileStream.CanWrite" /> de l'objet FileStream. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="access" /> n'est pas un champ de <see cref="T:System.IO.FileAccess" />. </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S, telle qu'une erreur disque, s'est produite.ouLe flux a été fermé. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Le <paramref name="access" /> demandé n'est pas autorisé par le système d'exploitation pour le handle de fichier spécifié, par exemple si <paramref name="access" /> est Write ou ReadWrite alors que le handle de fichier est défini pour un accès en lecture seule. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(Microsoft.Win32.SafeHandles.SafeFileHandle,System.IO.FileAccess,System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.IO.FileStream" /> pour le handle de fichier spécifié, avec l'autorisation d'accès en lecture/écriture et la taille de mémoire tampon spécifiées.</summary>
      <param name="handle">Handle de fichier pour le fichier que l'objet FileStream actuel doit encapsuler. </param>
      <param name="access">Constante <see cref="T:System.IO.FileAccess" /> qui définit les propriétés <see cref="P:System.IO.FileStream.CanRead" /> et <see cref="P:System.IO.FileStream.CanWrite" /> de l'objet FileStream. </param>
      <param name="bufferSize">Valeur <see cref="T:System.Int32" /> positive supérieure à 0 indiquant la taille de la mémoire tampon.La taille par défaut de la mémoire tampon est 4096.</param>
      <exception cref="T:System.ArgumentException">Le paramètre <paramref name="handle" /> est un handle non valide.ouLe paramètre <paramref name="handle" /> est un handle synchrone et il a été utilisé de façon asynchrone. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Le paramètre <paramref name="bufferSize" /> est négatif. </exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S, telle qu'une erreur disque, s'est produite.ouLe flux a été fermé.  </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Le <paramref name="access" /> demandé n'est pas autorisé par le système d'exploitation pour le handle de fichier spécifié, par exemple si <paramref name="access" /> est Write ou ReadWrite alors que le handle de fichier est défini pour un accès en lecture seule. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(Microsoft.Win32.SafeHandles.SafeFileHandle,System.IO.FileAccess,System.Int32,System.Boolean)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.IO.FileStream" /> pour le handle de fichier spécifié, avec l'autorisation d'accès en lecture/écriture, la taille de mémoire tampon et l'état synchrone ou asynchrone spécifiés.</summary>
      <param name="handle">Handle de fichier pour le fichier que cet objet FileStream encapsulera. </param>
      <param name="access">Constante qui définit les propriétés <see cref="P:System.IO.FileStream.CanRead" /> et <see cref="P:System.IO.FileStream.CanWrite" /> de l'objet FileStream. </param>
      <param name="bufferSize">Valeur <see cref="T:System.Int32" /> positive supérieure à 0 indiquant la taille de la mémoire tampon.La taille par défaut de la mémoire tampon est 4096.</param>
      <param name="isAsync">true si le handle a été ouvert de façon asynchrone (c'est-à-dire en mode E/S avec chevauchement) ; sinon, false. </param>
      <exception cref="T:System.ArgumentException">Le paramètre <paramref name="handle" /> est un handle non valide.ouLe paramètre <paramref name="handle" /> est un handle synchrone et il a été utilisé de façon asynchrone. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Le paramètre <paramref name="bufferSize" /> est négatif. </exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S, telle qu'une erreur disque, s'est produite.ouLe flux a été fermé.  </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Le <paramref name="access" /> demandé n'est pas autorisé par le système d'exploitation pour le handle de fichier spécifié, par exemple si <paramref name="access" /> est Write ou ReadWrite alors que le handle de fichier est défini pour un accès en lecture seule. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.IO.FileStream" /> avec le chemin d'accès et le mode de création spécifiés.</summary>
      <param name="path">Chemin d'accès relatif ou absolu pour le fichier que l'objet FileStream actuel doit encapsuler. </param>
      <param name="mode">Constante qui détermine le mode d'ouverture ou de création du fichier. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne vide (""), ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides. ou<paramref name="path" /> fait référence à un périphérique ne traitant pas de fichiers, tel que « con: »:, « com1 »:, « lpt1 »:, etc.dans un environnement NTFS.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> fait référence à un périphérique ne traitant pas de fichiers, tel que « con: »:, « com1 »:, « lpt1 »:, etc.dans un environnement non NTFS.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Le fichier est introuvable, comme lorsque <paramref name="mode" /> est FileMode.Truncate ou FileMode.Open et que le fichier spécifié par <paramref name="path" /> n'existe pas.Le fichier doit déjà exister dans ces modes.</exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite, par exemple en spécifiant FileMode.CreateNew alors que le fichier spécifié par <paramref name="path" /> existe déjà.ouLe flux a été fermé. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide, il se trouve par exemple sur un lecteur non mappé. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> contient une valeur non valide. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.IO.FileStream" /> avec le chemin d'accès, le mode de création et les autorisations de lecture/écriture spécifiés.</summary>
      <param name="path">Chemin d'accès relatif ou absolu pour le fichier que l'objet FileStream actuel doit encapsuler. </param>
      <param name="mode">Constante qui détermine le mode d'ouverture ou de création du fichier. </param>
      <param name="access">Constante qui détermine le mode d'accès au fichier par l'objet FileStream.Détermine aussi les valeurs retournées par les propriétés <see cref="P:System.IO.FileStream.CanRead" /> et <see cref="P:System.IO.FileStream.CanWrite" /> de l'objet FileStream.<see cref="P:System.IO.FileStream.CanSeek" /> a la valeur true si <paramref name="path" /> spécifie un fichier sur disque.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne vide (""), ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides. ou<paramref name="path" /> fait référence à un périphérique ne traitant pas de fichiers, tel que « con: »:, « com1 »:, « lpt1 »:, etc.dans un environnement NTFS.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> fait référence à un périphérique ne traitant pas de fichiers, tel que « con: »:, « com1 »:, « lpt1 »:, etc.dans un environnement non NTFS.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Le fichier est introuvable, comme lorsque <paramref name="mode" /> est FileMode.Truncate ou FileMode.Open et que le fichier spécifié par <paramref name="path" /> n'existe pas.Le fichier doit déjà exister dans ces modes.</exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite, par exemple en spécifiant FileMode.CreateNew alors que le fichier spécifié par <paramref name="path" /> existe déjà. ouLe flux a été fermé.</exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide, il se trouve par exemple sur un lecteur non mappé. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Le <paramref name="access" /> demandé n'est pas autorisé par le système d'exploitation pour le <paramref name="path" /> spécifié, par exemple si <paramref name="access" /> est Write ou ReadWrite alors que le fichier ou le répertoire est défini pour l'accès en lecture seule. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> contient une valeur non valide. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.IO.FileStream" /> avec le chemin d'accès, le mode de création, l'autorisation de lecture/écriture et l'autorisation de partage spécifiés.</summary>
      <param name="path">Chemin d'accès relatif ou absolu pour le fichier que l'objet FileStream actuel doit encapsuler. </param>
      <param name="mode">Constante qui détermine le mode d'ouverture ou de création du fichier. </param>
      <param name="access">Constante qui détermine le mode d'accès au fichier par l'objet FileStream.Détermine aussi les valeurs retournées par les propriétés <see cref="P:System.IO.FileStream.CanRead" /> et <see cref="P:System.IO.FileStream.CanWrite" /> de l'objet FileStream.<see cref="P:System.IO.FileStream.CanSeek" /> a la valeur true si <paramref name="path" /> spécifie un fichier sur disque.</param>
      <param name="share">Constante qui détermine le mode de partage du fichier par des processus. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne vide (""), ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides. ou<paramref name="path" /> fait référence à un périphérique ne traitant pas de fichiers, tel que « con: »:, « com1 »:, « lpt1 »:, etc.dans un environnement NTFS.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> fait référence à un périphérique ne traitant pas de fichiers, tel que « con: »:, « com1 »:, « lpt1 »:, etc.dans un environnement non NTFS.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Le fichier est introuvable, comme lorsque <paramref name="mode" /> est FileMode.Truncate ou FileMode.Open et que le fichier spécifié par <paramref name="path" /> n'existe pas.Le fichier doit déjà exister dans ces modes.</exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite, par exemple en spécifiant FileMode.CreateNew alors que le fichier spécifié par <paramref name="path" /> existe déjà. ouLe système exécute Windows 98 ou Windows 98 Deuxième Édition et <paramref name="share" /> a la valeur FileShare.Delete.ouLe flux a été fermé.</exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide, il se trouve par exemple sur un lecteur non mappé. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Le <paramref name="access" /> demandé n'est pas autorisé par le système d'exploitation pour le <paramref name="path" /> spécifié, par exemple si <paramref name="access" /> est Write ou ReadWrite alors que le fichier ou le répertoire est défini pour l'accès en lecture seule. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> contient une valeur non valide. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.IO.FileStream" /> avec le chemin d'accès, le mode de création, l'autorisation de lecture/écriture, l'autorisation de partage et la taille de mémoire tampon spécifiés.</summary>
      <param name="path">Chemin d'accès relatif ou absolu pour le fichier que l'objet FileStream actuel doit encapsuler. </param>
      <param name="mode">Constante qui détermine le mode d'ouverture ou de création du fichier. </param>
      <param name="access">Constante qui détermine le mode d'accès au fichier par l'objet FileStream.Détermine aussi les valeurs retournées par les propriétés <see cref="P:System.IO.FileStream.CanRead" /> et <see cref="P:System.IO.FileStream.CanWrite" /> de l'objet FileStream.<see cref="P:System.IO.FileStream.CanSeek" /> a la valeur true si <paramref name="path" /> spécifie un fichier sur disque.</param>
      <param name="share">Constante qui détermine le mode de partage du fichier par des processus. </param>
      <param name="bufferSize">Valeur <see cref="T:System.Int32" /> positive supérieure à 0 indiquant la taille de la mémoire tampon.La taille par défaut de la mémoire tampon est 4096.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne vide (""), ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides. ou<paramref name="path" /> fait référence à un périphérique ne traitant pas de fichiers, tel que « con: »:, « com1 »:, « lpt1 »:, etc.dans un environnement NTFS.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> fait référence à un périphérique ne traitant pas de fichiers, tel que « con: »:, « com1 »:, « lpt1 »:, etc.dans un environnement non NTFS.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> est négatif ou égal à zéro.ou <paramref name="mode" />, <paramref name="access" /> ou <paramref name="share" /> contient une valeur non valide. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Le fichier est introuvable, comme lorsque <paramref name="mode" /> est FileMode.Truncate ou FileMode.Open et que le fichier spécifié par <paramref name="path" /> n'existe pas.Le fichier doit déjà exister dans ces modes.</exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite, par exemple en spécifiant FileMode.CreateNew alors que le fichier spécifié par <paramref name="path" /> existe déjà. ouLe système exécute Windows 98 ou Windows 98 Deuxième Édition et <paramref name="share" /> a la valeur FileShare.Delete.ouLe flux a été fermé.</exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide, il se trouve par exemple sur un lecteur non mappé. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Le <paramref name="access" /> demandé n'est pas autorisé par le système d'exploitation pour le <paramref name="path" /> spécifié, par exemple si <paramref name="access" /> est Write ou ReadWrite alors que le fichier ou le répertoire est défini pour l'accès en lecture seule. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Int32,System.Boolean)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.IO.FileStream" /> avec le chemin d'accès, le mode de création, l'autorisation de lecture/écriture, l'autorisation de partage, la taille de mémoire tampon et l'état synchrone ou asynchrone spécifiés.</summary>
      <param name="path">Chemin d'accès relatif ou absolu pour le fichier que l'objet FileStream actuel doit encapsuler. </param>
      <param name="mode">Constante qui détermine le mode d'ouverture ou de création du fichier. </param>
      <param name="access">Constante qui détermine le mode d'accès au fichier par l'objet FileStream.Détermine aussi les valeurs retournées par les propriétés <see cref="P:System.IO.FileStream.CanRead" /> et <see cref="P:System.IO.FileStream.CanWrite" /> de l'objet FileStream.<see cref="P:System.IO.FileStream.CanSeek" /> a la valeur true si <paramref name="path" /> spécifie un fichier sur disque.</param>
      <param name="share">Constante qui détermine le mode de partage du fichier par des processus. </param>
      <param name="bufferSize">Valeur <see cref="T:System.Int32" /> positive supérieure à 0 indiquant la taille de la mémoire tampon.La taille par défaut de la mémoire tampon est 4 096.</param>
      <param name="useAsync">Spécifie s'il faut utiliser des E/S asynchrones ou synchrones.Notez cependant qu'il est possible que le système d'exploitation sous-jacent ne prenne pas en charge les E/S asynchrones. Par conséquent, si vous spécifiez true, le handle peut être ouvert de façon synchrone en fonction de la plateforme.Quand le mode asynchrone est utilisé, les méthodes <see cref="M:System.IO.FileStream.BeginRead(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)" /> et <see cref="M:System.IO.FileStream.BeginWrite(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)" /> donnent de meilleurs résultats pour des lectures ou des écritures de données volumineuses, mais elles peuvent être beaucoup plus lentes quand il s'agit de lectures et d'écritures de données de petite taille.Si l'application est conçue pour tirer parti des E/S asynchrones, affectez au paramètre <paramref name="useAsync" /> la valeur true.Quand les E/S asynchrones sont utilisées correctement, elles peuvent rendre les applications jusqu'à 10 fois plus rapides. Toutefois, si vous les utilisez dans une application qui n'a pas été reconfigurée pour les E/S asynchrones, l'application peut être jusqu'à 10 fois moins performante.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne vide (""), ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides. ou<paramref name="path" /> fait référence à un périphérique ne traitant pas de fichiers, tel que « con: »:, « com1 »:, « lpt1 »:, etc.dans un environnement NTFS.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> fait référence à un périphérique ne traitant pas de fichiers, tel que « con: »:, « com1 »:, « lpt1 »:, etc.dans un environnement non NTFS.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> est négatif ou égal à zéro.ou <paramref name="mode" />, <paramref name="access" /> ou <paramref name="share" /> contient une valeur non valide. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Le fichier est introuvable, comme lorsque <paramref name="mode" /> est FileMode.Truncate ou FileMode.Open et que le fichier spécifié par <paramref name="path" /> n'existe pas.Le fichier doit déjà exister dans ces modes.</exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite, par exemple en spécifiant FileMode.CreateNew alors que le fichier spécifié par <paramref name="path" /> existe déjà.ou Le système exécute Windows 98 ou Windows 98 Deuxième Édition et <paramref name="share" /> a la valeur FileShare.Delete.ouLe flux a été fermé.</exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide, il se trouve par exemple sur un lecteur non mappé. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Le <paramref name="access" /> demandé n'est pas autorisé par le système d'exploitation pour le <paramref name="path" /> spécifié, par exemple si <paramref name="access" /> est Write ou ReadWrite alors que le fichier ou le répertoire est défini pour l'accès en lecture seule. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Int32,System.IO.FileOptions)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.IO.FileStream" /> avec le chemin d'accès, le mode de création, l'autorisation d'accès en lecture/écriture et de partage, l'accès que d'autres flux de fichiers peuvent avoir au même fichier, la taille de mémoire tampon et les options de fichiers supplémentaires spécifiés.</summary>
      <param name="path">Chemin d'accès relatif ou absolu pour le fichier que l'objet FileStream actuel doit encapsuler. </param>
      <param name="mode">Constante qui détermine le mode d'ouverture ou de création du fichier. </param>
      <param name="access">Constante qui détermine le mode d'accès au fichier par l'objet FileStream.Détermine aussi les valeurs retournées par les propriétés <see cref="P:System.IO.FileStream.CanRead" /> et <see cref="P:System.IO.FileStream.CanWrite" /> de l'objet FileStream.<see cref="P:System.IO.FileStream.CanSeek" /> a la valeur true si <paramref name="path" /> spécifie un fichier sur disque.</param>
      <param name="share">Constante qui détermine le mode de partage du fichier par des processus. </param>
      <param name="bufferSize">Valeur <see cref="T:System.Int32" /> positive supérieure à 0 indiquant la taille de la mémoire tampon.La taille par défaut de la mémoire tampon est 4096.</param>
      <param name="options">Valeur qui spécifie des options de fichiers supplémentaires.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne vide (""), ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides. ou<paramref name="path" /> fait référence à un périphérique ne traitant pas de fichiers, tel que « con: »:, « com1 »:, « lpt1 »:, etc.dans un environnement NTFS.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> fait référence à un périphérique ne traitant pas de fichiers, tel que « con: »:, « com1 »:, « lpt1 »:, etc.dans un environnement non NTFS.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> est négatif ou égal à zéro.ou <paramref name="mode" />, <paramref name="access" /> ou <paramref name="share" /> contient une valeur non valide. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Le fichier est introuvable, comme lorsque <paramref name="mode" /> est FileMode.Truncate ou FileMode.Open et que le fichier spécifié par <paramref name="path" /> n'existe pas.Le fichier doit déjà exister dans ces modes.</exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite, par exemple en spécifiant FileMode.CreateNew alors que le fichier spécifié par <paramref name="path" /> existe déjà.ouLe flux a été fermé.</exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide, il se trouve par exemple sur un lecteur non mappé. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Le <paramref name="access" /> demandé n'est pas autorisé par le système d'exploitation pour le <paramref name="path" /> spécifié, par exemple si <paramref name="access" /> est Write ou ReadWrite alors que le fichier ou le répertoire est défini pour l'accès en lecture seule. ou<see cref="F:System.IO.FileOptions.Encrypted" /> est spécifié pour <paramref name="options" /> mais le chiffrement des fichiers n'est pas pris en charge sur la plateforme actuelle.</exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
    </member>
    <member name="P:System.IO.FileStream.CanRead">
      <summary>Obtient une valeur indiquant si le flux actuel prend en charge la lecture.</summary>
      <returns>true si le flux prend en charge la lecture ; false si le flux est fermé ou a été ouvert avec un accès en écriture seule.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.CanSeek">
      <summary>Obtient une valeur indiquant si le flux actuel prend en charge la recherche.</summary>
      <returns>true si le flux prend en charge la recherche ; false si le flux est fermé ou si l'élément FileStream a été construit à partir d'un handle de système d'exploitation comme un canal ou une sortie console.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.CanWrite">
      <summary>Obtient une valeur indiquant si le flux actuel prend en charge l'écriture.</summary>
      <returns>true si le flux prend en charge l'écriture ; false si le flux est fermé ou a été ouvert avec un accès en lecture seule.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Dispose(System.Boolean)">
      <summary>Libère les ressources non managées utilisées par <see cref="T:System.IO.FileStream" /> et libère éventuellement les ressources managées.</summary>
      <param name="disposing">true pour libérer les ressources managées et non managées ; false pour ne libérer que les ressources non managées. </param>
    </member>
    <member name="M:System.IO.FileStream.Finalize">
      <summary>Vérifie que les ressources sont libérées et que toute autre opération de nettoyage est effectuée quand le garbage collector récupère l'élément FileStream.</summary>
    </member>
    <member name="M:System.IO.FileStream.Flush">
      <summary>Efface les mémoires tampons pour ce flux et provoque l'écriture dans le fichier des données mises en mémoire tampon.</summary>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite. </exception>
      <exception cref="T:System.ObjectDisposedException">Le flux est fermé. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Flush(System.Boolean)">
      <summary>Efface les mémoires tampons pour ce flux, provoque l'écriture des données mises en mémoire tampon dans le fichier et efface également toutes les mémoires tampons de fichiers intermédiaires.</summary>
      <param name="flushToDisk">true pour vider toutes les mémoires tampons de fichiers intermédiaires ; sinon, false. </param>
    </member>
    <member name="M:System.IO.FileStream.FlushAsync(System.Threading.CancellationToken)">
      <summary>Efface de façon asynchrone toutes les mémoires tampons pour ce flux, provoque l'écriture des données mises en mémoire tampon sur l'appareil sous-jacent et surveille les demandes d'annulation. </summary>
      <returns>Tâche qui représente l'opération de vidage asynchrone. </returns>
      <param name="cancellationToken">Jeton pour surveiller les demandes d'annulation.</param>
      <exception cref="T:System.ObjectDisposedException">Le flux a été supprimé.</exception>
    </member>
    <member name="P:System.IO.FileStream.IsAsync">
      <summary>Obtient une valeur indiquant si l'élément FileStream a été ouvert en mode asynchrone ou synchrone.</summary>
      <returns>true si FileStream a été ouvert de façon asynchrone ; sinon, false.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.Length">
      <summary>Obtient la longueur du flux en octets.</summary>
      <returns>Valeur de type long représentant la longueur du flux en octets.</returns>
      <exception cref="T:System.NotSupportedException">
        <see cref="P:System.IO.FileStream.CanSeek" /> de ce flux a la valeur false. </exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite, telle que la fermeture du fichier. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.Name">
      <summary>Obtient le nom de l'élément FileStream passé au constructeur.</summary>
      <returns>Chaîne qui définit le nom de l'élément FileStream.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileStream.Position">
      <summary>Obtient ou définit la position actuelle de ce flux.</summary>
      <returns>Position actuelle de ce flux.</returns>
      <exception cref="T:System.NotSupportedException">Le flux ne prend pas en charge la recherche. </exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite. ouUne valeur très importante allant au-delà de la fin du flux dans Windows 98 ou version antérieure a été affectée à la position.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Tentative d'assignation d'une valeur négative à la propriété. </exception>
      <exception cref="T:System.IO.EndOfStreamException">Tentative de recherche au-delà de la fin d'un flux qui ne prend pas en charge cette fonctionnalité. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>Lit un bloc d'octets dans le flux et écrit les données dans une mémoire tampon donnée.</summary>
      <returns>Nombre total d'octets lus dans la mémoire tampon.Le total peut être inférieur au nombre d'octets demandé si ce nombre d'octets n'est pas disponible actuellement, ou il peut être égal à zéro si la fin du flux est atteinte.</returns>
      <param name="array">Quand cette méthode retourne un résultat, contient un tableau d'octets spécifié dont les valeurs comprises entre <paramref name="offset" /> et (<paramref name="offset" /> + <paramref name="count" /> - 1<paramref name=")" /> sont remplacées par les octets lus dans la source actuelle. </param>
      <param name="offset">Dans <paramref name="array" />, décalage d'octet auquel les octets lus seront placés. </param>
      <param name="count">Nombre maximal d'octets à lire. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> ou <paramref name="count" /> est négatif. </exception>
      <exception cref="T:System.NotSupportedException">Le flux ne prend pas en charge la lecture. </exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> et <paramref name="count" /> décrivent une plage non valide dans <paramref name="array" />. </exception>
      <exception cref="T:System.ObjectDisposedException">Des méthodes ont été appelées après que le flux a été fermé. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.ReadAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>Lit de façon asynchrone une séquence d'octets dans le flux actuel, avance la position dans le flux du nombre d'octets lus et surveille les demandes d'annulation.</summary>
      <returns>Tâche qui représente l'opération de lecture asynchrone.La valeur du paramètre <paramref name="TResult" /> contient le nombre total d'octets lus dans la mémoire tampon.La valeur du résultat peut être inférieure au nombre d'octets demandés si le nombre d'octets actuellement disponibles est inférieur au nombre demandé, ou elle peut avoir la valeur 0 (zéro) si la fin du flux a été atteinte.</returns>
      <param name="buffer">Mémoire tampon dans laquelle les données sont écrites.</param>
      <param name="offset">Dans <paramref name="buffer" />, décalage d'octet auquel commencer l'écriture des données à partir du flux.</param>
      <param name="count">Nombre maximal d'octets à lire.</param>
      <param name="cancellationToken">Jeton pour surveiller les demandes d'annulation.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> ou <paramref name="count" /> est négatif.</exception>
      <exception cref="T:System.ArgumentException">La somme de <paramref name="offset" /> et de <paramref name="count" /> est supérieure à la longueur de la mémoire tampon.</exception>
      <exception cref="T:System.NotSupportedException">Le flux ne prend pas en charge la lecture.</exception>
      <exception cref="T:System.ObjectDisposedException">Le flux a été supprimé.</exception>
      <exception cref="T:System.InvalidOperationException">Le flux est en cours d'utilisation par une opération de lecture précédente. </exception>
    </member>
    <member name="M:System.IO.FileStream.ReadByte">
      <summary>Lit un octet du fichier et avance la position de lecture d'un octet.</summary>
      <returns>Octet, converti en type (transtypage) <see cref="T:System.Int32" />, ou -1 si la fin du flux a été atteinte.</returns>
      <exception cref="T:System.NotSupportedException">Le flux actuel ne prend pas en charge la lecture. </exception>
      <exception cref="T:System.ObjectDisposedException">Le flux actuel est fermé. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.SafeFileHandle">
      <summary>Obtient un objet <see cref="T:Microsoft.Win32.SafeHandles.SafeFileHandle" /> qui représente le handle de fichier du système d'exploitation pour le fichier encapsulé par l'objet <see cref="T:System.IO.FileStream" /> actuel.</summary>
      <returns>Objet qui représente le handle de fichier du système d'exploitation pour le fichier encapsulé par l'objet <see cref="T:System.IO.FileStream" /> actuel.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Seek(System.Int64,System.IO.SeekOrigin)">
      <summary>Définit la position actuelle de ce flux avec la valeur donnée.</summary>
      <returns>Nouvelle position dans le flux.</returns>
      <param name="offset">Point relatif à l'élément <paramref name="origin" /> où la recherche doit commencer. </param>
      <param name="origin">Spécifie le début, la fin ou la position actuelle comme point de référence pour <paramref name="offset" />, en utilisant une valeur de type <see cref="T:System.IO.SeekOrigin" />. </param>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite. </exception>
      <exception cref="T:System.NotSupportedException">Le flux ne prend pas en charge la recherche, comme si FileStream était construit à partir d'un canal ou d'une sortie console. </exception>
      <exception cref="T:System.ArgumentException">Tentative de recherche avant le début du flux. </exception>
      <exception cref="T:System.ObjectDisposedException">Des méthodes ont été appelées après que le flux a été fermé. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.SetLength(System.Int64)">
      <summary>Définit la longueur de ce flux à la valeur donnée.</summary>
      <param name="value">Nouvelle longueur du flux. </param>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite. </exception>
      <exception cref="T:System.NotSupportedException">Le flux ne prend pas en charge l'écriture et la recherche. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Tentative de définition du paramètre <paramref name="value" /> sur une valeur inférieure à 0. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>Écrit un bloc d'octets dans le flux de fichier.</summary>
      <param name="array">Mémoire tampon qui contient les données à écrire dans le flux.</param>
      <param name="offset">Dans <paramref name="array" />, décalage d'octet de base zéro à partir duquel commencer la copie des octets dans le flux. </param>
      <param name="count">Nombre maximal d'octets à écrire. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> et <paramref name="count" /> décrivent une plage non valide dans <paramref name="array" />. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> ou <paramref name="count" /> est négatif. </exception>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S s'est produite. ouUn autre thread a pu provoquer une modification inattendue de la position du handle de fichier du système d'exploitation. </exception>
      <exception cref="T:System.ObjectDisposedException">Le flux est fermé. </exception>
      <exception cref="T:System.NotSupportedException">L'instance du flux actuel ne prend pas en charge l'écriture. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.WriteAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>Écrit de façon asynchrone une séquence d'octets dans le flux actuel, avance la position actuelle dans ce flux du nombre d'octets écrits et surveille les demandes d'annulation. </summary>
      <returns>Tâche qui représente l'opération d'écriture asynchrone.</returns>
      <param name="buffer">Mémoire tampon dont sont issues les données à écrire. </param>
      <param name="offset">Dans <paramref name="buffer" />, décalage d'octet de base zéro à partir duquel commencer la copie des octets dans le flux.</param>
      <param name="count">Nombre maximal d'octets à écrire.</param>
      <param name="cancellationToken">Jeton pour surveiller les demandes d'annulation.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> ou <paramref name="count" /> est négatif.</exception>
      <exception cref="T:System.ArgumentException">La somme de <paramref name="offset" /> et de <paramref name="count" /> est supérieure à la longueur de la mémoire tampon.</exception>
      <exception cref="T:System.NotSupportedException">Le flux ne prend pas en charge l'écriture.</exception>
      <exception cref="T:System.ObjectDisposedException">Le flux a été supprimé.</exception>
      <exception cref="T:System.InvalidOperationException">Le flux est actuellement utilisé par une opération d'écriture précédente. </exception>
    </member>
    <member name="M:System.IO.FileStream.WriteByte(System.Byte)">
      <summary>Écrit un octet à la position actuelle dans le flux de fichier.</summary>
      <param name="value">Octet à écrire dans le flux. </param>
      <exception cref="T:System.ObjectDisposedException">Le flux est fermé. </exception>
      <exception cref="T:System.NotSupportedException">Le flux ne prend pas en charge l'écriture. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.FileSystemInfo">
      <summary>Fournit la classe de base pour les objets <see cref="T:System.IO.FileInfo" /> et <see cref="T:System.IO.DirectoryInfo" />.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.FileSystemInfo.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.IO.FileSystemInfo" />.</summary>
    </member>
    <member name="P:System.IO.FileSystemInfo.Attributes">
      <summary>Obtient ou définit les attributs pour le fichier ou le répertoire actif.</summary>
      <returns>
        <see cref="T:System.IO.FileAttributes" /> du <see cref="T:System.IO.FileSystemInfo" /> en cours.</returns>
      <exception cref="T:System.IO.FileNotFoundException">Le fichier spécifié n'existe pas. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide ; il se trouve, par exemple, sur un lecteur non mappé. </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <exception cref="T:System.ArgumentException">L'appelant tente de définir un attribut de fichier non valide. ouL'utilisateur tente de définir une valeur d'attribut mais ne dispose pas de l'autorisation en écriture.</exception>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> ne peut pas initialiser les données. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.CreationTime">
      <summary>Obtient ou définit l'heure de création du fichier ou du répertoire actif.</summary>
      <returns>Date et heure de création de l'objet <see cref="T:System.IO.FileSystemInfo" /> en cours.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> ne peut pas initialiser les données. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide ; il se trouve, par exemple, sur un lecteur non mappé.</exception>
      <exception cref="T:System.PlatformNotSupportedException">Le système d'exploitation actif n'est pas Windows NT ni une version ultérieure.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'appelant essaie de définir un temps de création non valide.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.CreationTimeUtc">
      <summary>Obtient ou définit l'heure de création, au format de temps universel (UTC, Coordinated Universal Time), du fichier ou du répertoire actif.</summary>
      <returns>Date et heure de création au format de temps universel de l'objet <see cref="T:System.IO.FileSystemInfo" /> en cours.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> ne peut pas initialiser les données. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide ; il se trouve, par exemple, sur un lecteur non mappé.</exception>
      <exception cref="T:System.PlatformNotSupportedException">Le système d'exploitation actif n'est pas Windows NT ni une version ultérieure.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'appelant essaie de définir un temps d'accès non valide.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileSystemInfo.Delete">
      <summary>Supprime un fichier ou un répertoire.</summary>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide ; il se trouve, par exemple, sur un lecteur non mappé.</exception>
      <exception cref="T:System.IO.IOException">Un handle est ouvert sur le fichier ou le répertoire et le système d'exploitation est Windows XP ou une version antérieure.Ce handle ouvert peut être le résultat d'une énumération de répertoires et de fichiers.Pour plus d'informations, consultez Comment : énumérer des répertoires et des fichiers.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.FileSystemInfo.Exists">
      <summary>Obtient une valeur indiquant si le fichier ou le répertoire existe.</summary>
      <returns>true si le fichier ou répertoire existe ; sinon, false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileSystemInfo.Extension">
      <summary>Obtient la chaîne représentant l'extension du fichier.</summary>
      <returns>Chaîne contenant l'extension de <see cref="T:System.IO.FileSystemInfo" />.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileSystemInfo.FullName">
      <summary>Obtient le chemin d'accès complet du répertoire ou fichier.</summary>
      <returns>Chaîne contenant le chemin d'accès complet.</returns>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès qualifié complet et le nom du fichier est de 260 caractères ou plus.</exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas l'autorisation requise. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="F:System.IO.FileSystemInfo.FullPath">
      <summary>Représente le chemin d'accès qualifié complet du répertoire ou fichier.</summary>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès qualifié complet est de 260 caractères ou plus.</exception>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastAccessTime">
      <summary>Obtient ou définit l'heure du dernier accès au fichier ou répertoire actif.</summary>
      <returns>Heure du dernier accès au fichier ou répertoire actif.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> ne peut pas initialiser les données. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Le système d'exploitation actif n'est pas Windows NT ni une version ultérieure.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'appelant essaie de définir un temps d'accès non valide</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastAccessTimeUtc">
      <summary>Obtient ou définit l'heure, au format de temps universel (UTC, Universal Coordinated Time), du dernier accès au fichier ou au répertoire actif.</summary>
      <returns>Heure UTC du dernier accès au fichier ou répertoire actif.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> ne peut pas initialiser les données. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Le système d'exploitation actif n'est pas Windows NT ni une version ultérieure.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'appelant essaie de définir un temps d'accès non valide.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastWriteTime">
      <summary>Obtient ou définit l'heure de la dernière écriture dans le fichier ou répertoire actif.</summary>
      <returns>Heure de la dernière écriture dans le fichier en cours.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> ne peut pas initialiser les données. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Le système d'exploitation actif n'est pas Windows NT ni une version ultérieure.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'appelant essaie de définir un temps d'écriture non valide.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastWriteTimeUtc">
      <summary>Obtient ou définit l'heure, au format de temps universel (UTC, Universal Coordinated Time), de la dernière écriture dans le fichier ou le répertoire actif.</summary>
      <returns>Heure UTC de la dernière écriture dans le fichier en cours.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> ne peut pas initialiser les données. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Le système d'exploitation actif n'est pas Windows NT ni une version ultérieure.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'appelant essaie de définir un temps d'écriture non valide.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.Name">
      <summary>Pour des fichiers, obtient le nom du fichier.Pour des répertoires, obtient le nom du dernier répertoire dans la hiérarchie, si elle existe.Sinon, la propriété Name obtient le nom du répertoire.</summary>
      <returns>Chaîne qui est le nom du répertoire parent, le nom du dernier répertoire dans la hiérarchie, ou le nom d'un fichier, comprenant l'extension du nom de fichier.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.FileSystemInfo.OriginalPath">
      <summary>Chemin d'accès initialement spécifié par l'utilisateur, relatif ou absolu.</summary>
    </member>
    <member name="M:System.IO.FileSystemInfo.Refresh">
      <summary>Actualise l'état de l'objet.</summary>
      <exception cref="T:System.IO.IOException">Un périphérique, un lecteur de disque, par exemple, n'est pas prêt. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.SearchOption">
      <summary>Spécifie s'il faut rechercher le répertoire actif, ou le répertoire actif et tous les sous-répertoires. </summary>
    </member>
    <member name="F:System.IO.SearchOption.AllDirectories">
      <summary>Inclut le répertoire actif et tous ses sous-répertoires dans une opération de recherche.Cette option inclut des points d'analyse, tels que des lecteurs montés et des liens symboliques dans la recherche.</summary>
    </member>
    <member name="F:System.IO.SearchOption.TopDirectoryOnly">
      <summary>Inclut uniquement le répertoire actif dans une opération de recherche.</summary>
    </member>
  </members>
</doc>