﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.IO.FileSystem</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Win32.SafeHandles.SafeFileHandle">
      <summary>Rappresenta una classe wrapper per un handle di file. </summary>
    </member>
    <member name="M:Microsoft.Win32.SafeHandles.SafeFileHandle.#ctor(System.IntPtr,System.Boolean)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:Microsoft.Win32.SafeHandles.SafeFileHandle" />. </summary>
      <param name="preexistingHandle">Oggetto <see cref="T:System.IntPtr" /> che rappresenta l'handle preesistente da utilizzare.</param>
      <param name="ownsHandle">true per rilasciare in modo affidabile l'handle durante la fase di finalizzazione; false per impedire il rilascio affidabile (non consigliato).</param>
    </member>
    <member name="P:Microsoft.Win32.SafeHandles.SafeFileHandle.IsInvalid"></member>
    <member name="T:System.IO.Directory">
      <summary>Espone i metodi statici per la creazione, lo spostamento e l'enumerazione nelle directory e sottodirectory.La classe non può essere ereditata.Per esaminare il codice sorgente .NET Framework per questo tipo, vedere Origine riferimento.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Directory.CreateDirectory(System.String)">
      <summary>Crea tutte le directory e le sottodirectory nel percorso specificato a meno che non esistano già.</summary>
      <returns>Oggetto che rappresenta la directory nel percorso specificato.L'oggetto viene restituito a prescindere dal fatto che esista già una directory nel percorso specificato.</returns>
      <param name="path">Directory da creare. </param>
      <exception cref="T:System.IO.IOException">La directory specificata da <paramref name="path" /> è un file.-oppure-Il nome della rete non è conosciuto.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi.È possibile eseguire una query per i caratteri non validi utilizzando il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.-oppure-<paramref name="path" /> è preceduto o contiene solo due punti (:).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> contiene due punti (:) che non fanno parte dell'etichetta di un'unità ("C:\").</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.Delete(System.String)">
      <summary>Elimina una directory vuota dal percorso specificato.</summary>
      <param name="path">Nome della directory vuota da rimuovere.La directory deve essere accessibile in scrittura e vuota.</param>
      <exception cref="T:System.IO.IOException">Un file con lo stesso nome e nella stessa posizione specificata dal parametro <paramref name="path" /> esiste già.-oppure-La directory è la directory di lavoro corrente dell'applicazione.-oppure-La directory specificata nel parametro <paramref name="path" /> non è vuota.-oppure-La directory è di sola lettura o contiene un file di sola lettura.-oppure-La directory è in uso in un altro processo.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi.È possibile eseguire una query per i caratteri non validi utilizzando il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> non esiste o non è possibile trovarlo.-oppure-Il percorso specificato non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.Delete(System.String,System.Boolean)">
      <summary>Elimina la directory specificata e, se indicate, le sottodirectory e i file in essa contenuti. </summary>
      <param name="path">Nome della directory da rimuovere. </param>
      <param name="recursive">true per rimuovere directory, sottodirectory e file in <paramref name="path" />; in caso contrario, false. </param>
      <exception cref="T:System.IO.IOException">Un file con lo stesso nome e nella stessa posizione specificata dal parametro <paramref name="path" /> esiste già.-oppure-La directory specificata da <paramref name="path" /> è in sola lettura, oppure <paramref name="recursive" /> è false e <paramref name="path" /> non è una directory vuota. -oppure-La directory è la directory di lavoro corrente dell'applicazione. -oppure-La directory contiene un file di sola lettura.-oppure-La directory è in uso in un altro processo.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi.È possibile eseguire una query per i caratteri non validi utilizzando il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> non esiste o non è possibile trovarlo.-oppure-Il percorso specificato non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String)">
      <summary>Restituisce una raccolta enumerabile dei nomi di directory in un percorso specificato.</summary>
      <returns>Raccolta enumerabile dei nomi completi (inclusi i percorsi) per le directory incluse nella directory specificata da <paramref name="path" />.</returns>
      <param name="path">Percorso relativo o assoluto della directory in cui eseguire la ricerca.Stringa senza distinzione tra maiuscole e minuscole.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />è una stringa di lunghezza zero, contiene solo spazi vuoti oppure caratteri non validi.È possibile eseguire una query per i caratteri non validi utilizzando il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> non è valido, poiché, ad esempio, fa riferimento a un'unità non mappata. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> è un nome di file.</exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String,System.String)">
      <summary>Restituisce una raccolta enumerabile di nomi di directory che corrispondono a un criterio di ricerca in un percorso specificato.</summary>
      <returns>Raccolta enumerabile dei nomi completi (inclusi i percorsi) per le directory incluse nella directory specificata da <paramref name="path" /> e corrispondenti al criterio di ricerca specificato.</returns>
      <param name="path">Percorso relativo o assoluto della directory in cui eseguire la ricerca.Stringa senza distinzione tra maiuscole e minuscole.</param>
      <param name="searchPattern">Stringa di ricerca in base alla quale confrontare i nomi delle directory in <paramref name="path" />.Questo parametro può contenere una combinazione di caratteri del percorso letterale e caratteri jolly (* e ?) validi (vedere la sezione Osservazioni), ma non supporta le espressioni regolari.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />è una stringa di lunghezza zero, contiene solo spazi vuoti oppure caratteri non validi.È possibile eseguire una query per i caratteri non validi con il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.-oppure-<paramref name="searchPattern" /> non contiene un criterio valido.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null.-oppure-<paramref name="searchPattern" /> è null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> non è valido, poiché, ad esempio, fa riferimento a un'unità non mappata. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> è un nome di file.</exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String,System.String,System.IO.SearchOption)">
      <summary>Restituisce una raccolta enumerabile di nomi di directory che corrispondono a un criterio di ricerca in un percorso specificato e con ricerca facoltativa nelle sottodirectory.</summary>
      <returns>Raccolta enumerabile dei nomi completi (inclusi i percorsi) per le directory nella directory specificata da <paramref name="path" /> e corrispondenti al criterio e all'opzione di ricerca specificati.</returns>
      <param name="path">Percorso relativo o assoluto della directory in cui eseguire la ricerca.Stringa senza distinzione tra maiuscole e minuscole.</param>
      <param name="searchPattern">Stringa di ricerca in base alla quale confrontare i nomi delle directory in <paramref name="path" />.Questo parametro può contenere una combinazione di caratteri del percorso letterale e caratteri jolly (* e ?) validi (vedere la sezione Osservazioni), ma non supporta le espressioni regolari.</param>
      <param name="searchOption">Uno dei valori di enumerazione che specifica se l'operazione di ricerca deve includere solo la directory corrente o tutte le sottodirectory.Il valore predefinito è <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />è una stringa di lunghezza zero, contiene solo spazi vuoti oppure caratteri non validi.È possibile eseguire una query per i caratteri non validi utilizzando il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.-oppure-<paramref name="searchPattern" /> non contiene un criterio valido.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null.-oppure-<paramref name="searchPattern" /> è null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> non è un valore di <see cref="T:System.IO.SearchOption" /> valido.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> non è valido, poiché, ad esempio, fa riferimento a un'unità non mappata. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> è un nome di file.</exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String)">
      <summary>Restituisce una raccolta enumerabile dei nomi di file in un percorso specificato.</summary>
      <returns>Raccolta enumerabile dei nomi completi (inclusi i percorsi) per i file inclusi nella directory specificata da <paramref name="path" />.</returns>
      <param name="path">Percorso relativo o assoluto della directory in cui eseguire la ricerca.Stringa senza distinzione tra maiuscole e minuscole.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />è una stringa di lunghezza zero, contiene solo spazi vuoti oppure caratteri non validi.È possibile eseguire una query per i caratteri non validi utilizzando il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> non è valido, poiché, ad esempio, fa riferimento a un'unità non mappata. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> è un nome di file.</exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String,System.String)">
      <summary>Restituisce una raccolta enumerabile di nomi di file che corrispondono a un criterio di ricerca in un percorso specificato.</summary>
      <returns>Raccolta enumerabile dei nomi completi (inclusi i percorsi) per i file inclusi nella directory specificata da <paramref name="path" /> e corrispondenti al criterio di ricerca specificato.</returns>
      <param name="path">Percorso relativo o assoluto della directory in cui eseguire la ricerca.Stringa senza distinzione tra maiuscole e minuscole.</param>
      <param name="searchPattern">Stringa di ricerca in base alla quale confrontare i nomi dei file in <paramref name="path" />.Questo parametro può contenere una combinazione di caratteri del percorso letterale e caratteri jolly (* e ?) validi (vedere la sezione Osservazioni), ma non supporta le espressioni regolari.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />è una stringa di lunghezza zero, contiene solo spazi vuoti oppure caratteri non validi.È possibile eseguire una query per i caratteri non validi utilizzando il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.-oppure-<paramref name="searchPattern" /> non contiene un criterio valido.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null.-oppure-<paramref name="searchPattern" /> è null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> non è valido, poiché, ad esempio, fa riferimento a un'unità non mappata. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> è un nome di file.</exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String,System.String,System.IO.SearchOption)">
      <summary>Restituisce una raccolta enumerabile di nomi di file che corrispondono a un criterio di ricerca in un percorso specificato e con ricerca facoltativa nelle sottodirectory.</summary>
      <returns>Raccolta enumerabile dei nomi completi (inclusi i percorsi) per i file inclusi nella directory specificata da <paramref name="path" /> e corrispondenti al criterio e all'opzione di ricerca specificati.</returns>
      <param name="path">Percorso relativo o assoluto della directory in cui eseguire la ricerca.Stringa senza distinzione tra maiuscole e minuscole.</param>
      <param name="searchPattern">Stringa di ricerca in base alla quale confrontare i nomi dei file in <paramref name="path" />.Questo parametro può contenere una combinazione di caratteri del percorso letterale e caratteri jolly (* e ?) validi (vedere la sezione Osservazioni), ma non supporta le espressioni regolari.</param>
      <param name="searchOption">Uno dei valori di enumerazione che specifica se l'operazione di ricerca deve includere solo la directory corrente o tutte le sottodirectory.Il valore predefinito è <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />è una stringa di lunghezza zero, contiene solo spazi vuoti oppure caratteri non validi.È possibile eseguire una query per i caratteri non validi utilizzando il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.-oppure-<paramref name="searchPattern" /> non contiene un criterio valido.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null.-oppure-<paramref name="searchPattern" /> è null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> non è un valore di <see cref="T:System.IO.SearchOption" /> valido.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> non è valido, poiché, ad esempio, fa riferimento a un'unità non mappata. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> è un nome di file.</exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String)">
      <summary>Restituisce una raccolta enumerabile di nomi di file e di directory in un percorso specificato. </summary>
      <returns>Raccolta enumerabile dei nomi di voci di file system nella directory specificata da <paramref name="path" />.</returns>
      <param name="path">Percorso relativo o assoluto della directory in cui eseguire la ricerca.Stringa senza distinzione tra maiuscole e minuscole.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />è una stringa di lunghezza zero, contiene solo spazi vuoti oppure caratteri non validi.È possibile eseguire una query per i caratteri non validi utilizzando il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> non è valido, poiché, ad esempio, fa riferimento a un'unità non mappata. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> è un nome di file.</exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String,System.String)">
      <summary>Restituisce una raccolta enumerabile di nomi di file e di directory che corrispondono a un criterio di ricerca in un percorso specificato.</summary>
      <returns>Raccolta enumerabile di voci di file system nella directory specificata da <paramref name="path" /> e corrispondenti al criterio di ricerca specificato.</returns>
      <param name="path">Percorso relativo o assoluto della directory in cui eseguire la ricerca.Stringa senza distinzione tra maiuscole e minuscole.</param>
      <param name="searchPattern">Stringa di ricerca in base alla quale confrontare i nomi delle voci di file system in <paramref name="path" />.Questo parametro può contenere una combinazione di caratteri del percorso letterale e caratteri jolly (* e ?) validi (vedere la sezione Osservazioni), ma non supporta le espressioni regolari.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />è una stringa di lunghezza zero, contiene solo spazi vuoti oppure caratteri non validi.È possibile eseguire una query per i caratteri non validi utilizzando il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.-oppure-<paramref name="searchPattern" /> non contiene un criterio valido.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null.-oppure-<paramref name="searchPattern" /> è null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> non è valido, poiché, ad esempio, fa riferimento a un'unità non mappata. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> è un nome di file.</exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String,System.String,System.IO.SearchOption)">
      <summary>Restituisce una raccolta enumerabile di nomi di file e di directory che corrispondono a un criterio di ricerca in un percorso specificato e con ricerca facoltativa nelle sottodirectory.</summary>
      <returns>Raccolta enumerabile di voci di file system nella directory specificata da <paramref name="path" /> e corrispondenti al criterio e all'opzione di ricerca specificata.</returns>
      <param name="path">Percorso relativo o assoluto della directory in cui eseguire la ricerca.Stringa senza distinzione tra maiuscole e minuscole.</param>
      <param name="searchPattern">Stringa di ricerca in base alla quale confrontare le voci di file system in <paramref name="path" />.Questo parametro può contenere una combinazione di caratteri del percorso letterale e caratteri jolly (* e ?) validi (vedere la sezione Osservazioni), ma non supporta le espressioni regolari.</param>
      <param name="searchOption">Uno dei valori di enumerazione che specifica se l'operazione di ricerca deve includere solo la directory corrente o tutte le sottodirectory.Il valore predefinito è <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />è una stringa di lunghezza zero, contiene solo spazi vuoti oppure caratteri non validi.È possibile eseguire una query per i caratteri non validi utilizzando il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.-oppure-<paramref name="searchPattern" /> non contiene un criterio valido.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null.-oppure-<paramref name="searchPattern" /> è null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> non è un valore di <see cref="T:System.IO.SearchOption" /> valido.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> non è valido, poiché, ad esempio, fa riferimento a un'unità non mappata. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> è un nome di file.</exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
    </member>
    <member name="M:System.IO.Directory.Exists(System.String)">
      <summary>Determina se il percorso specificato fa riferimento a una directory esistente sul disco.</summary>
      <returns>true se <paramref name="path" /> fa riferimento a una directory esistente; false se la directory non esiste o si verifica un errore durante il tentativo di determinare se il file specificato esiste.</returns>
      <param name="path">Percorso da testare. </param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetCreationTime(System.String)">
      <summary>Ottiene la data e l'ora di creazione di una directory.</summary>
      <returns>Struttura impostata sulla data e l'ora di creazione della directory specificata.Questo valore è espresso nell'ora locale.</returns>
      <param name="path">Percorso della directory. </param>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi.È possibile eseguire una query per i caratteri non validi utilizzando il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetCreationTimeUtc(System.String)">
      <summary>Ottiene la data e l'ora di creazione di una directory, con l'ora nel formato UTC (Coordinated Universal Time).</summary>
      <returns>Struttura impostata sulla data e l'ora di creazione della directory specificata.Questo valore è espresso nell'ora UTC.</returns>
      <param name="path">Percorso della directory. </param>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi.È possibile eseguire una query per i caratteri non validi utilizzando il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetCurrentDirectory">
      <summary>Ottiene la directory di lavoro corrente dell'applicazione.</summary>
      <returns>Stringa che contiene il percorso della directory di lavoro corrente e che non termina con una barra rovesciata (\).</returns>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.NotSupportedException">Il sistema operativo è Windows CE, che non dispone di una funzionalità di directory corrente.Questo metodo è disponibile in .NET Compact Framework, ma non è supportato in modo corrente.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String)">
      <summary>Restituisce i nomi delle sottodirectory, inclusi i relativi percorsi, nella directory specificata.</summary>
      <returns>Una matrice dei nomi completi (inclusi i percorsi) delle sottodirectory nel percorso specificato o una matrice vuota se non viene trovata alcuna directory.</returns>
      <param name="path">Percorso relativo o assoluto della directory in cui eseguire la ricerca.Stringa senza distinzione tra maiuscole e minuscole.</param>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi.È possibile eseguire una query per i caratteri non validi utilizzando il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> è un nome di file. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String,System.String)">
      <summary>Restituisce i nomi delle sottodirectory (inclusi i percorsi) corrispondenti al criterio di ricerca specificato nella directory specificata.</summary>
      <returns>Una matrice dei nomi completi (inclusi i percorsi) delle sottodirectory che soddisfano i criteri di ricerca nella directory specificata o una matrice vuota se non viene trovata alcuna directory.</returns>
      <param name="path">Percorso relativo o assoluto della directory in cui eseguire la ricerca.Stringa senza distinzione tra maiuscole e minuscole.</param>
      <param name="searchPattern">Stringa di ricerca in base alla quale confrontare i nomi delle sottodirectory in <paramref name="path" />.Questo parametro può contenere una combinazione di caratteri letterali e caratteri jolly (* e ?) validi (vedere la sezione Osservazioni), ma non supporta le espressioni regolari.</param>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi.È possibile eseguire una query per i caratteri non validi utilizzando il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.-oppure- <paramref name="searchPattern" /> non contiene un criterio valido. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> o <paramref name="searchPattern" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> è un nome di file. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String,System.String,System.IO.SearchOption)">
      <summary>Restituisce i nomi delle sottodirectory (incluso il relativo percorso) che corrispondono ai criteri di ricerca specificati nella directory specificata e con ricerca facoltativa nelle sottodirectory.</summary>
      <returns>Una matrice di nomi completi (inclusi i percorsi) delle sottodirectory che soddisfano i criteri di ricerca specificati o una matrice vuota se non viene trovata alcuna directory.</returns>
      <param name="path">Percorso relativo o assoluto della directory in cui eseguire la ricerca.Stringa senza distinzione tra maiuscole e minuscole.</param>
      <param name="searchPattern">Stringa di ricerca in base alla quale confrontare i nomi delle sottodirectory in <paramref name="path" />.Questo parametro può contenere una combinazione di caratteri letterali e caratteri jolly (* e ?) validi (vedere la sezione Osservazioni), ma non supporta le espressioni regolari.</param>
      <param name="searchOption">Uno dei valori di enumerazione che specifica se l'operazione di ricerca deve includere tutte le sottodirectory o la sottodirectory corrente. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi.È possibile eseguire una query per i caratteri non validi utilizzando il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.-oppure- <paramref name="searchPattern" /> non contiene un criterio valido. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> o <paramref name="searchPattern" /> è null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> non è un valore di <see cref="T:System.IO.SearchOption" /> valido.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> è un nome di file. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido (ad esempio, si trova su un'unità non connessa). </exception>
    </member>
    <member name="M:System.IO.Directory.GetDirectoryRoot(System.String)">
      <summary>Restituisce le informazioni sul volume, sulla radice o su entrambi per il percorso specificato.</summary>
      <returns>Stringa contenente le informazioni sul volume, sulla radice, o su entrambi, per il percorso specificato.</returns>
      <param name="path">Percorso di un file o di una directory. </param>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi.È possibile eseguire una query per i caratteri non validi con il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String)">
      <summary>Restituisce i nomi dei file, inclusi i relativi percorsi, nella directory specificata.</summary>
      <returns>Una matrice dei nomi completi (inclusi i percorsi) per i file nella directory specificata o una matrice vuota se non viene trovato alcun file.</returns>
      <param name="path">Percorso relativo o assoluto della directory in cui eseguire la ricerca.Stringa senza distinzione tra maiuscole e minuscole.</param>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> è un nome di file.-oppure-Si è verificato un errore di rete. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi.È possibile eseguire una query per i caratteri non validi utilizzando il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è stato trovato o non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String,System.String)">
      <summary>Restituisce i nomi dei file (inclusi i percorsi) corrispondenti al criterio di ricerca specificato nella directory specificata.</summary>
      <returns>Una matrice dei nomi completi (inclusi i percorsi) per i file nella directory specificata corrispondenti al criterio di ricerca specificato oppure una matrice vuota se non viene trovato alcun file.</returns>
      <param name="path">Percorso relativo o assoluto della directory in cui eseguire la ricerca.Stringa senza distinzione tra maiuscole e minuscole.</param>
      <param name="searchPattern">Stringa di ricerca in base alla quale confrontare i nomi dei file in <paramref name="path" />.Questo parametro può contenere una combinazione di caratteri del percorso letterale e caratteri jolly (* e ?) validi (vedere la sezione Osservazioni), ma non supporta le espressioni regolari.</param>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> è un nome di file.-oppure-Si è verificato un errore di rete. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi.È possibile eseguire una query per i caratteri non validi utilizzando il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.-oppure- <paramref name="searchPattern" /> non contiene un criterio valido. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> o <paramref name="searchPattern" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è stato trovato o non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String,System.String,System.IO.SearchOption)">
      <summary>Restituisce i nomi dei file (inclusi i percorsi) che corrispondono ai criteri di ricerca specificati nella directory specificata, usando un valore per determinare se eseguire la ricerca nelle sottodirectory.</summary>
      <returns>Una matrice dei nomi completi (inclusi i percorsi) per i file nella directory specificata corrispondenti al criterio e all'opzione di ricerca specificati oppure una matrice vuota se non viene trovato alcun file.</returns>
      <param name="path">Percorso relativo o assoluto della directory in cui eseguire la ricerca.Stringa senza distinzione tra maiuscole e minuscole.</param>
      <param name="searchPattern">Stringa di ricerca in base alla quale confrontare i nomi dei file in <paramref name="path" />.Questo parametro può contenere una combinazione di caratteri del percorso letterale e caratteri jolly (* e ?) validi (vedere la sezione Osservazioni), ma non supporta le espressioni regolari.</param>
      <param name="searchOption">Uno dei valori di enumerazione che specifica se l'operazione di ricerca deve includere tutte le sottodirectory o la sottodirectory corrente. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi.È possibile eseguire una query per i caratteri non validi con il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.-oppure- <paramref name="searchPattern" /> non contiene un criterio valido.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> o <paramref name="searchpattern" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> non è un valore di <see cref="T:System.IO.SearchOption" /> valido.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è stato trovato o non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> è un nome di file.-oppure-Si è verificato un errore di rete. </exception>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String)">
      <summary>Restituisce i nomi di tutti i file e di tutte le sottodirectory in un percorso specificato.</summary>
      <returns>Una matrice dei nomi dei file e delle sottodirectory nella directory specificata oppure una matrice vuota se non vengono trovati file o sottodirectory.</returns>
      <param name="path">Percorso relativo o assoluto della directory in cui eseguire la ricerca.Stringa senza distinzione tra maiuscole e minuscole.</param>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi.È possibile eseguire una query per i caratteri non validi con il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> è un nome di file. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String,System.String)">
      <summary>Restituisce una matrice di nomi file e di nomi di directory che corrispondono a un criterio di ricerca in un percorso specificato.</summary>
      <returns>Una matrice di nomi di file e di nomi di directory che soddisfano i criteri di ricerca specificati, oppure una matrice vuota se non vengono trovati file o directory.</returns>
      <param name="path">Percorso relativo o assoluto della directory in cui eseguire la ricerca.Stringa senza distinzione tra maiuscole e minuscole.</param>
      <param name="searchPattern">Stringa di ricerca in base alla quale confrontare i nomi dei file e delle directory in <paramref name="path" />.Questo parametro può contenere una combinazione di caratteri del percorso letterale e caratteri jolly (* e ?) validi (vedere la sezione Osservazioni), ma non supporta le espressioni regolari.</param>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi.È possibile eseguire una query per i caratteri non validi con il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.-oppure- <paramref name="searchPattern" /> non contiene un criterio valido. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> o <paramref name="searchPattern" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> è un nome di file. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String,System.String,System.IO.SearchOption)">
      <summary>Restituisce una matrice di tutti i nomi di file e di directory che corrispondono a un criterio di ricerca in un percorso specificato e con ricerca facoltativa nelle sottodirectory.</summary>
      <returns>Una matrice di nomi di file e di nomi di directory che soddisfano i criteri di ricerca specificati oppure una matrice vuota se non vengono trovati file o directory.</returns>
      <param name="path">Percorso relativo o assoluto della directory in cui eseguire la ricerca.Stringa senza distinzione tra maiuscole e minuscole.</param>
      <param name="searchPattern">Stringa di ricerca in base alla quale confrontare i nomi dei file e delle directory in <paramref name="path" />.Questo parametro può contenere una combinazione di caratteri del percorso letterale e caratteri jolly (* e ?) validi (vedere la sezione Osservazioni), ma non supporta le espressioni regolari.</param>
      <param name="searchOption">Uno dei valori di enumerazione che specifica se l'operazione di ricerca deve includere solo la directory corrente o tutte le sottodirectory.Il valore predefinito è <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />è una stringa di lunghezza zero, contiene solo spazi vuoti oppure caratteri non validi.È possibile eseguire una query per i caratteri non validi utilizzando il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.-oppure-<paramref name="searchPattern" /> non contiene un criterio valido.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null.-oppure-<paramref name="searchPattern" /> è null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> non è un valore di <see cref="T:System.IO.SearchOption" /> valido.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> non è valido, poiché, ad esempio, fa riferimento a un'unità non mappata. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> è un nome di file.</exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
    </member>
    <member name="M:System.IO.Directory.GetLastAccessTime(System.String)">
      <summary>Restituisce la data e l'ora dell'ultimo accesso al file o alla directory specificata.</summary>
      <returns>Struttura impostata sulla data e l'ora dell'ultimo accesso al file o alla directory specificata.Questo valore è espresso nell'ora locale.</returns>
      <param name="path">File o directory per cui ottenere informazioni sulla data e l'ora di accesso. </param>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi.È possibile eseguire una query per i caratteri non validi con il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.NotSupportedException">Il parametro <paramref name="path" /> è in un formato non valido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetLastAccessTimeUtc(System.String)">
      <summary>Restituisce la data e l'ora, nel formato dell'ora UTC (Coordinated Universal Time), dell'ultimo accesso al file o alla directory specificata.</summary>
      <returns>Struttura impostata sulla data e l'ora dell'ultimo accesso al file o alla directory specificata.Questo valore è espresso nell'ora UTC.</returns>
      <param name="path">File o directory per cui ottenere informazioni sulla data e l'ora di accesso. </param>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi.È possibile eseguire una query per i caratteri non validi con il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.NotSupportedException">Il parametro <paramref name="path" /> è in un formato non valido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetLastWriteTime(System.String)">
      <summary>Restituisce la data e l'ora dell'ultima scrittura nel file o nella directory specificata.</summary>
      <returns>Struttura impostata sulla data e l'ora dell'ultima scrittura nel file o nella directory specificata.Questo valore è espresso nell'ora locale.</returns>
      <param name="path">File o directory per cui ottenere informazioni sulla data e l'ora di modifica. </param>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi.È possibile eseguire una query per i caratteri non validi con il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetLastWriteTimeUtc(System.String)">
      <summary>Restituisce la data e l'ora, nel formato dell'ora UTC (Coordinated Universal Time), dell'ultima scrittura nel file o nella directory specificata.</summary>
      <returns>Struttura impostata sulla data e l'ora dell'ultima scrittura nel file o nella directory specificata.Questo valore è espresso nell'ora UTC.</returns>
      <param name="path">File o directory per cui ottenere informazioni sulla data e l'ora di modifica. </param>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi.È possibile eseguire una query per i caratteri non validi con il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetParent(System.String)">
      <summary>Recupera la directory padre del percorso specificato, inclusi il percorso assoluto e relativo.</summary>
      <returns>Directory padre oppure null se <paramref name="path" /> è la directory radice, inclusa la radice di un server UNC o di un nome di condivisione.</returns>
      <param name="path">Percorso per il quale recuperare la directory padre. </param>
      <exception cref="T:System.IO.IOException">La directory specificata da <paramref name="path" /> è in sola lettura. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi.È possibile eseguire una query per i caratteri non validi con il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è stato trovato. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.Move(System.String,System.String)">
      <summary>Sposta un file o una directory e il suo contenuto in un nuovo percorso.</summary>
      <param name="sourceDirName">Percorso del file o della directory da spostare. </param>
      <param name="destDirName">Nuovo percorso di <paramref name="sourceDirName" />.Se <paramref name="sourceDirName" /> è un file, anche <paramref name="destDirName" /> deve essere un nome file.</param>
      <exception cref="T:System.IO.IOException">Si è tentato di spostare una directory in un diverso volume. -oppure- <paramref name="destDirName" /> esiste già. -oppure- I parametri <paramref name="sourceDirName" /> e <paramref name="destDirName" /> fanno riferimento allo stesso file o alla stessa directory. -oppure-La directory o un file in esso è utilizzato da un altro processo.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceDirName" /> o <paramref name="destDirName" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi.È possibile eseguire una query per i caratteri non validi con il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceDirName" /> o <paramref name="destDirName" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato dal parametro <paramref name="sourceDirName" /> non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetCreationTime(System.String,System.DateTime)">
      <summary>Imposta la data e l'ora di creazione per il file o la directory specificata.</summary>
      <param name="path">File o directory per cui impostare le informazioni sulla data e l'ora di creazione. </param>
      <param name="creationTime">Data e ora in cui è stata eseguita l'ultima scrittura nel file o nella directory.Questo valore è espresso nell'ora locale.</param>
      <exception cref="T:System.IO.FileNotFoundException">Il percorso specificato non è stato trovato. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi.È possibile eseguire una query per i caratteri non validi con il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> specifica un valore esterno all'intervallo di date od ore consentite per l'operazione. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Il sistema operativo corrente non è Windows NT o versioni successive.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetCreationTimeUtc(System.String,System.DateTime)">
      <summary>Imposta la data e l'ora di creazione, nel formato dell'ora UTC (Coordinated Universal Time), per il file o la directory specificata.</summary>
      <param name="path">File o directory per cui impostare le informazioni sulla data e l'ora di creazione. </param>
      <param name="creationTimeUtc">La data e l'ora di creazione della directory o del file.Questo valore è espresso nell'ora locale.</param>
      <exception cref="T:System.IO.FileNotFoundException">Il percorso specificato non è stato trovato. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi.È possibile eseguire una query per i caratteri non validi con il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> specifica un valore esterno all'intervallo di date od ore consentite per l'operazione. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Il sistema operativo corrente non è Windows NT o versioni successive.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetCurrentDirectory(System.String)">
      <summary>Imposta la directory di lavoro corrente dell'applicazione sulla directory specificata.</summary>
      <param name="path">Percorso su cui è impostata la directory di lavoro corrente. </param>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi.È possibile eseguire una query per i caratteri non validi con il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta per accedere al codice non gestito. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Il percorso specificato non è stato trovato. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La directory specificata non è stata trovata.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastAccessTime(System.String,System.DateTime)">
      <summary>Imposta la data e l'ora dell'ultimo accesso al file o alla directory specificata.</summary>
      <param name="path">File o directory per cui impostare le informazioni sulla data e l'ora di accesso. </param>
      <param name="lastAccessTime">Oggetto che contiene il valore da impostare per la data e ora di accesso di <paramref name="path" />.Questo valore è espresso nell'ora locale.</param>
      <exception cref="T:System.IO.FileNotFoundException">Il percorso specificato non è stato trovato. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi.È possibile eseguire una query per i caratteri non validi con il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Il sistema operativo corrente non è Windows NT o versioni successive.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTime" /> specifica un valore esterno all'intervallo di date od ore consentite per l'operazione.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastAccessTimeUtc(System.String,System.DateTime)">
      <summary>Imposta la data e l'ora, nel formato dell'ora UTC (Coordinated Universal Time), dell'ultimo accesso al file o alla directory specificata.</summary>
      <param name="path">File o directory per cui impostare le informazioni sulla data e l'ora di accesso. </param>
      <param name="lastAccessTimeUtc">Oggetto che contiene il valore da impostare per la data e ora di accesso di <paramref name="path" />.Questo valore è espresso nell'ora UTC.</param>
      <exception cref="T:System.IO.FileNotFoundException">Il percorso specificato non è stato trovato. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi.È possibile eseguire una query per i caratteri non validi con il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Il sistema operativo corrente non è Windows NT o versioni successive.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTimeUtc" /> specifica un valore esterno all'intervallo di date od ore consentite per l'operazione.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastWriteTime(System.String,System.DateTime)">
      <summary>Imposta la data e l'ora in cui è stata eseguita l'ultima scrittura in una directory.</summary>
      <param name="path">Percorso della directory. </param>
      <param name="lastWriteTime">Data e ora in cui è stata eseguita l'ultima scrittura nella directory.Questo valore è espresso nell'ora locale.</param>
      <exception cref="T:System.IO.FileNotFoundException">Il percorso specificato non è stato trovato. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi.È possibile eseguire una query per i caratteri non validi con il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Il sistema operativo corrente non è Windows NT o versioni successive.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTime" /> specifica un valore esterno all'intervallo di date od ore consentite per l'operazione.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastWriteTimeUtc(System.String,System.DateTime)">
      <summary>Imposta la data e l'ora, nel formato dell'ora UTC (Coordinated Universal Time), dell'ultima scrittura in una directory.</summary>
      <param name="path">Percorso della directory. </param>
      <param name="lastWriteTimeUtc">Data e ora in cui è stata eseguita l'ultima scrittura nella directory.Questo valore è espresso nell'ora UTC.</param>
      <exception cref="T:System.IO.FileNotFoundException">Il percorso specificato non è stato trovato. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi.È possibile eseguire una query per i caratteri non validi con il metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Il sistema operativo corrente non è Windows NT o versioni successive.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTimeUtc" /> specifica un valore esterno all'intervallo di date od ore consentite per l'operazione.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.IO.DirectoryInfo">
      <summary>Espone i metodi dell'istanza per la creazione, lo spostamento e l'enumerazione tramite directory e sottodirectory.La classe non può essere ereditata.Per esaminare il codice sorgente di .NET Framework per questo tipo, vedere la
                                Origine di riferimento.
                            </summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.DirectoryInfo.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe
                                <see cref="T:System.IO.DirectoryInfo" />classe nel percorso specificato.
                            </summary>
      <param name="path">Una stringa che specifica il percorso in cui creare il
                                    DirectoryInfo.
                                </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />è
                                        null.
                                    </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contiene caratteri non validi, come ad esempio ", &lt;, &gt;, o |.
                                    </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.Percorso e/o nome di file specificato troppo lungo.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.Create">
      <summary>Crea una directory.</summary>
      <exception cref="T:System.IO.IOException">Non è possibile creare la directory.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.CreateSubdirectory(System.String)">
      <summary>Crea una o più sottodirectory nel percorso specificato.Il percorso specificato può essere relativo a questa istanza del
                            <see cref="T:System.IO.DirectoryInfo" />classe.
                        </summary>
      <returns>L'ultima directory specificate in
                                <paramref name="path" />.
                            </returns>
      <param name="path">Percorso specificato.Non può essere un volume del disco o un nome UNC (Universal Naming Convention) diverso.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />non specifica un percorso valido o contiene non valido
                                        DirectoryInfocaratteri.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />è
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido, poiché, ad esempio, si trova su un'unità non connessa.</exception>
      <exception cref="T:System.IO.IOException">Non è possibile creare la sottodirectory.-oppure-Un file o directory esiste già il nome specificato da
                                        <paramref name="path" />.
                                    </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.Percorso e/o nome di file specificato troppo lungo.</exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione di accesso al codice per la creazione della directory.-oppure-Il chiamante non dispone di autorizzazione di accesso di codice per la lettura della directory descritta dall'oggetto restituito
                                    Oggetto <see cref="T:System.IO.DirectoryInfo" />.
                                Questo problema può verificarsi quando il
                                    <paramref name="path" />parametro descrive una directory esistente.
                                </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> contiene due punti (:) che non fanno parte dell'etichetta di un'unità ("C:\").
                                    </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.Delete">
      <summary>Elimina questo
                                <see cref="T:System.IO.DirectoryInfo" />Se è vuota.
                            </summary>
      <exception cref="T:System.UnauthorizedAccessException">La directory contiene un file di sola lettura.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La directory descritta dall'oggetto
                                        <see cref="T:System.IO.DirectoryInfo" />oggetto non esiste o non è stato trovato.
                                    </exception>
      <exception cref="T:System.IO.IOException">La directory non è vuota.-oppure-La directory è la directory di lavoro corrente dell'applicazione.-oppure-Handle aperto sulla directory e il sistema operativo è Windows XP o versione precedente.Questo handle aperto può derivare da directory di enumerazione.Per altre informazioni, vedere
                                    Procedura: enumerare directory e file.
                                </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.Delete(System.Boolean)">
      <summary>Elimina questa istanza di un
                                <see cref="T:System.IO.DirectoryInfo" />, che specifica se eliminare le sottodirectory e i file.
                            </summary>
      <param name="recursive">truePer eliminare la directory, le relative sottodirectory e tutti i file; in caso contrario,
                                    false.
                                </param>
      <exception cref="T:System.UnauthorizedAccessException">La directory contiene un file di sola lettura.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La directory descritta dall'oggetto
                                        <see cref="T:System.IO.DirectoryInfo" />oggetto non esiste o non è stato trovato.
                                    </exception>
      <exception cref="T:System.IO.IOException">La directory è di sola lettura.-oppure-La directory contiene uno o più file o sottodirectory e
                                        <paramref name="recursive" />è
                                        false.
                                    -oppure-La directory è la directory di lavoro corrente dell'applicazione.-oppure-Handle aperto sulla directory o su uno dei file e il sistema operativo è Windows XP o versione precedente.Questo handle aperto può derivare da file e directory di enumerazione.Per altre informazioni, vedere
                                    Procedura: enumerare directory e file.
                                </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories">
      <summary>Restituisce una raccolta enumerabile di informazioni sulla directory nella directory corrente.</summary>
      <returns>Raccolta enumerabile di directory nella directory corrente.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso incapsulato nel
                                        <see cref="T:System.IO.DirectoryInfo" />oggetto non valido (ad esempio, si trova su un'unità non mappata).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories(System.String)">
      <summary>Restituisce una raccolta enumerabile di informazioni sulla directory che corrisponde a un criterio di ricerca specificato.</summary>
      <returns>Una raccolta enumerabile di directory corrispondente
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">Stringa di ricerca in base alla quale confrontare i nomi delle directory.Questo parametro può contenere una combinazione di caratteri del percorso letterale e caratteri jolly (* e ?) validi (vedere la sezione Osservazioni), ma non supporta le espressioni regolari.Il criterio predefinito è "*" che restituisce tutti i file.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />è
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso incapsulato nel
                                        <see cref="T:System.IO.DirectoryInfo" />oggetto non valido (ad esempio, si trova su un'unità non mappata).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories(System.String,System.IO.SearchOption)">
      <summary>Restituisce una raccolta enumerabile di informazioni sulla directory che corrisponde a un criterio di ricerca e all'opzione di ricerca subdirectory specificati.</summary>
      <returns>Una raccolta enumerabile di directory corrispondente
                                <paramref name="searchPattern" /> e
                                <paramref name="searchOption" />.
                            </returns>
      <param name="searchPattern">Stringa di ricerca in base alla quale confrontare i nomi delle directory.Questo parametro può contenere una combinazione di caratteri del percorso letterale e caratteri jolly (* e ?) validi (vedere la sezione Osservazioni), ma non supporta le espressioni regolari.Il criterio predefinito è "*" che restituisce tutti i file.</param>
      <param name="searchOption">Uno dei valori di enumerazione che specifica se l'operazione di ricerca deve includere solo la directory corrente o tutte le sottodirectory.Il valore predefinito è
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />è
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />non è valido
                                        Valore di <see cref="T:System.IO.SearchOption" />.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso incapsulato nel
                                        <see cref="T:System.IO.DirectoryInfo" />oggetto non valido (ad esempio, si trova su un'unità non mappata).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles">
      <summary>Restituisce una raccolta enumerabile di informazioni sui file nella directory corrente.</summary>
      <returns>Raccolta enumerabile dei file nella directory corrente.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso incapsulato nel
                                        <see cref="T:System.IO.DirectoryInfo" />oggetto non valido (ad esempio, si trova su un'unità non mappata).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles(System.String)">
      <summary>Restituisce una raccolta enumerabile di informazioni sui file che corrisponde a un criterio di ricerca.</summary>
      <returns>Una raccolta enumerabile di file corrispondente
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">Stringa di ricerca in base alla quale confrontare i nomi dei file.Questo parametro può contenere una combinazione di caratteri del percorso letterale e caratteri jolly (* e ?) validi (vedere la sezione Osservazioni), ma non supporta le espressioni regolari.Il criterio predefinito è "*" che restituisce tutti i file.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />è
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso incapsulato nel
                                        <see cref="T:System.IO.DirectoryInfo" />oggetto non è valido, (ad esempio, si trova su un'unità non mappata).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles(System.String,System.IO.SearchOption)">
      <summary>Restituisce una raccolta enumerabile di informazioni sui file che corrisponde a un criterio di ricerca e all'opzione di ricerca subdirectory specificati.</summary>
      <returns>Una raccolta enumerabile di file corrispondente
                                <paramref name="searchPattern" /> e
                                <paramref name="searchOption" />.
                            </returns>
      <param name="searchPattern">Stringa di ricerca in base alla quale confrontare i nomi dei file.Questo parametro può contenere una combinazione di caratteri del percorso letterale e caratteri jolly (* e ?) validi (vedere la sezione Osservazioni), ma non supporta le espressioni regolari.Il criterio predefinito è "*" che restituisce tutti i file.</param>
      <param name="searchOption">Uno dei valori di enumerazione che specifica se l'operazione di ricerca deve includere solo la directory corrente o tutte le sottodirectory.Il valore predefinito è
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />è
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />non è valido
                                        Valore di <see cref="T:System.IO.SearchOption" />.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso incapsulato nel
                                        <see cref="T:System.IO.DirectoryInfo" />oggetto non valido (ad esempio, si trova su un'unità non mappata).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos">
      <summary>Restituisce una raccolta enumerabile di informazioni sul file system nella directory corrente.</summary>
      <returns>Raccolta enumerabile di informazioni sul file system nella directory corrente.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso incapsulato nel
                                        <see cref="T:System.IO.DirectoryInfo" />oggetto non valido (ad esempio, si trova su un'unità non mappata).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos(System.String)">
      <summary>Restituisce una raccolta enumerabile di informazioni sul file system che corrisponde a un criterio di ricerca specificato.</summary>
      <returns>Una raccolta enumerabile di oggetti informazioni del file system corrispondente
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">Stringa di ricerca in base alla quale confrontare i nomi delle directory.Questo parametro può contenere una combinazione di caratteri del percorso letterale e caratteri jolly (* e ?) validi (vedere la sezione Osservazioni), ma non supporta le espressioni regolari.Il criterio predefinito è "*" che restituisce tutti i file.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />è
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso incapsulato nel
                                        <see cref="T:System.IO.DirectoryInfo" />oggetto non valido (ad esempio, si trova su un'unità non mappata).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos(System.String,System.IO.SearchOption)">
      <summary>Restituisce una raccolta enumerabile di informazioni sul file system che corrisponde a un criterio di ricerca e all'opzione di ricerca subdirectory specificati.</summary>
      <returns>Una raccolta enumerabile di oggetti informazioni del file system corrispondente
                                <paramref name="searchPattern" /> e
                                <paramref name="searchOption" />.
                            </returns>
      <param name="searchPattern">Stringa di ricerca in base alla quale confrontare i nomi delle directory.Questo parametro può contenere una combinazione di caratteri del percorso letterale e caratteri jolly (* e ?) validi (vedere la sezione Osservazioni), ma non supporta le espressioni regolari.Il criterio predefinito è "*" che restituisce tutti i file.</param>
      <param name="searchOption">Uno dei valori di enumerazione che specifica se l'operazione di ricerca deve includere solo la directory corrente o tutte le sottodirectory.Il valore predefinito è
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />è
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />non è valido
                                        Valore di <see cref="T:System.IO.SearchOption" />.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso incapsulato nel
                                        <see cref="T:System.IO.DirectoryInfo" />oggetto non valido (ad esempio, si trova su un'unità non mappata).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
    </member>
    <member name="P:System.IO.DirectoryInfo.Exists">
      <summary>Ottiene un valore che indica se la directory esiste.</summary>
      <returns>trueSe è presente nella directory. in caso contrario,
                                false.
                            </returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories">
      <summary>Restituisce le sottodirectory della directory corrente.</summary>
      <returns>Matrice di
                                Oggetti <see cref="T:System.IO.DirectoryInfo" />.
                            </returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso incapsulato nel
                                        <see cref="T:System.IO.DirectoryInfo" />oggetto non valido, ad esempio si trova su un'unità non connessa.
                                    </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories(System.String)">
      <summary>Restituisce una matrice di directory nell'attuale
                                <see cref="T:System.IO.DirectoryInfo" />corrispondono ai criteri di ricerca specificato.
                            </summary>
      <returns>Matrice di tipo
                                DirectoryInfocorrispondenza
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">Stringa di ricerca in base alla quale confrontare i nomi delle directory.Questo parametro può contenere una combinazione di caratteri del percorso letterale e caratteri jolly (* e ?) validi (vedere la sezione Osservazioni), ma non supporta le espressioni regolari.Il criterio predefinito è "*" che restituisce tutti i file.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />contiene uno o più caratteri non validi definiti per il
                                        Metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />è
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso incapsulato nel
                                        DirectoryInfooggetto non valido (ad esempio, si trova su un'unità non mappata).
                                    </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories(System.String,System.IO.SearchOption)">
      <summary>Restituisce una matrice di directory nell'attuale
                                <see cref="T:System.IO.DirectoryInfo" />corrispondono ai criteri di ricerca e utilizzo di un valore per determinare se eseguire la ricerca nelle sottodirectory.
                            </summary>
      <returns>Matrice di tipo
                                DirectoryInfocorrispondenza
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">Stringa di ricerca in base alla quale confrontare i nomi delle directory.Questo parametro può contenere una combinazione di caratteri del percorso letterale e caratteri jolly (* e ?) validi (vedere la sezione Osservazioni), ma non supporta le espressioni regolari.Il criterio predefinito è "*" che restituisce tutti i file.</param>
      <param name="searchOption">Uno dei valori di enumerazione che specifica se l'operazione di ricerca deve includere solo la directory corrente o tutte le sottodirectory.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />contiene uno o più caratteri non validi definiti per il
                                        Metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />è
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />non è valido
                                        Valore di <see cref="T:System.IO.SearchOption" />.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso incapsulato nel
                                        DirectoryInfooggetto non valido (ad esempio, si trova su un'unità non mappata).
                                    </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles">
      <summary>Restituisce un elenco di file della directory corrente.</summary>
      <returns>Matrice di tipo
                                <see cref="T:System.IO.FileInfo" />.
                            </returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso non è valido, poiché, ad esempio, si trova su un'unità non connessa.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles(System.String)">
      <summary>Restituisce un elenco di file della directory corrente corrispondente al criterio di ricerca specificato.</summary>
      <returns>Matrice di tipo
                                <see cref="T:System.IO.FileInfo" />.
                            </returns>
      <param name="searchPattern">Stringa di ricerca in base alla quale confrontare i nomi dei file.Questo parametro può contenere una combinazione di caratteri del percorso letterale e caratteri jolly (* e ?) validi (vedere la sezione Osservazioni), ma non supporta le espressioni regolari.Il criterio predefinito è "*" che restituisce tutti i file.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />contiene uno o più caratteri non validi definiti per il
                                        Metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />è
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso non è valido (ad esempio, si trova su un'unità non mappata).</exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles(System.String,System.IO.SearchOption)">
      <summary>Restituisce un elenco di file dalla directory corrente che corrisponde al criterio di ricerca specificato e usando un valore per determinare se eseguire la ricerca nelle sottodirectory.</summary>
      <returns>Matrice di tipo
                                <see cref="T:System.IO.FileInfo" />.
                            </returns>
      <param name="searchPattern">Stringa di ricerca in base alla quale confrontare i nomi dei file.Questo parametro può contenere una combinazione di caratteri del percorso letterale e caratteri jolly (* e ?) validi (vedere la sezione Osservazioni), ma non supporta le espressioni regolari.Il criterio predefinito è "*" che restituisce tutti i file.</param>
      <param name="searchOption">Uno dei valori di enumerazione che specifica se l'operazione di ricerca deve includere solo la directory corrente o tutte le sottodirectory.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />contiene uno o più caratteri non validi definiti per il
                                        Metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />è
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />non è valido
                                        Valore di <see cref="T:System.IO.SearchOption" />.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso non è valido (ad esempio, si trova su un'unità non mappata).</exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos">
      <summary>Restituisce una matrice di fortemente tipizzati
                                <see cref="T:System.IO.FileSystemInfo" />voci che rappresentano tutti i file e sottodirectory in una directory.
                            </summary>
      <returns>Matrice di fortemente tipizzata.
                                <see cref="T:System.IO.FileSystemInfo" />voci.
                            </returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso non è valido (ad esempio, si trova su un'unità non mappata).</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos(System.String)">
      <summary>Recupera una matrice di fortemente tipizzati
                                <see cref="T:System.IO.FileSystemInfo" />oggetti che rappresentano i file e sottodirectory che corrispondono ai criteri di ricerca specificati.
                            </summary>
      <returns>Matrice di fortemente tipizzata.
                                FileSystemInfooggetti che corrispondono ai criteri di ricerca.
                            </returns>
      <param name="searchPattern">Stringa di ricerca in base alla quale confrontare i nomi delle directory e dei file.Questo parametro può contenere una combinazione di caratteri del percorso letterale e caratteri jolly (* e ?) validi (vedere la sezione Osservazioni), ma non supporta le espressioni regolari.Il criterio predefinito è "*" che restituisce tutti i file.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />contiene uno o più caratteri non validi definiti per il
                                        Metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />è
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido (ad esempio, si trova su un'unità non connessa).</exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos(System.String,System.IO.SearchOption)">
      <summary>Recupera una matrice di
                                <see cref="T:System.IO.FileSystemInfo" />oggetti che rappresentano i file e le sottodirectory corrispondenti ai criteri di ricerca specificato.
                            </summary>
      <returns>Matrice di voci di file system corrispondenti ai criteri di ricerca.</returns>
      <param name="searchPattern">Stringa di ricerca in base alla quale confrontare i nomi delle directory e dei file.Questo parametro può contenere una combinazione di caratteri del percorso letterale e caratteri jolly (* e ?) validi (vedere la sezione Osservazioni), ma non supporta le espressioni regolari.Il criterio predefinito è "*" che restituisce tutti i file.</param>
      <param name="searchOption">Uno dei valori di enumerazione che specifica se l'operazione di ricerca deve includere solo la directory corrente o tutte le sottodirectory.Il valore predefinito è
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />contiene uno o più caratteri non validi definiti per il
                                        Metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />è
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />non è valido
                                        Valore di <see cref="T:System.IO.SearchOption" />.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido (ad esempio, si trova su un'unità non connessa).</exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.MoveTo(System.String)">
      <summary>Sposta un
                                <see cref="T:System.IO.DirectoryInfo" />istanza e il relativo contenuto in un nuovo percorso.
                            </summary>
      <param name="destDirName">Nome e percorso in cui spostare la directory.La destinazione non può essere un altro volume del disco o una directory con lo stesso nome.Può essere una directory esistente in cui si intende aggiungere la directory come sottodirectory.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destDirName" />è
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="destDirName" /> è una stringa vuota ("").
                                    </exception>
      <exception cref="T:System.IO.IOException">Si è tentato di spostare una directory in un diverso volume.-oppure-<paramref name="destDirName" /> esiste già.
                                    -oppure-Non si è autorizzati ad accedere a questo percorso.-oppure-La directory spostata e quella di destinazione hanno lo stesso nome.</exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Non è possibile trovare la directory di destinazione.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.DirectoryInfo.Name">
      <summary>Ottiene il nome di questo
                                <see cref="T:System.IO.DirectoryInfo" />istanza.
                            </summary>
      <returns>Nome della directory.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.DirectoryInfo.Parent">
      <summary>Ottiene la directory padre di una sottodirectory specificata.</summary>
      <returns>Directory padre, o
                                nullSe il percorso è null o se il percorso del file indica una directory radice (ad esempio "\", "C:", o * "\\server\share").
                            </returns>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.DirectoryInfo.Root">
      <summary>Ottiene la parte radice della directory.</summary>
      <returns>Oggetto che rappresenta la radice della directory.</returns>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.ToString">
      <summary>Restituisce il percorso originale passato dall'utente.</summary>
      <returns>Restituisce il percorso originale passato dall'utente.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.IO.File">
      <summary>Fornisce i metodi statici per creare, copiare, eliminare, spostare e aprire un singolo file, nonché supportare la creazione di oggetti <see cref="T:System.IO.FileStream" />.Per esaminare il codice sorgente di .NET Framework per questo tipo, vedere il Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.File.AppendAllLines(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>Aggiunge righe a un file e quindi lo chiude.Se il file specificato non esiste, questo metodo ne crea uno, scrive la riga specificata e quindi lo chiude.</summary>
      <param name="path">File in cui aggiungere righe.Se il file non esiste già, verrà creato.</param>
      <param name="contents">Righe da aggiungere al file.</param>
      <exception cref="T:System.ArgumentException">Il parametro <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti dal metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">Sia<paramref name=" path " />o <paramref name="contents" /> è null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> non è valido (ad esempio, la directory non esiste o si trova su un'unità non connessa).</exception>
      <exception cref="T:System.IO.FileNotFoundException">Il file specificato dal parametro <paramref name="path" /> non è stato trovato.</exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O durante l'apertura del file.</exception>
      <exception cref="T:System.IO.PathTooLongException">Il parametro <paramref name="path" /> supera la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido.</exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione per scrivere il file.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Il parametro <paramref name="path" /> consente di specificare un file di sola lettura.-oppure-L'operazione non è supportata sulla piattaforma corrente.-oppure-<paramref name="path" /> è una directory.</exception>
    </member>
    <member name="M:System.IO.File.AppendAllLines(System.String,System.Collections.Generic.IEnumerable{System.String},System.Text.Encoding)">
      <summary>Aggiunge righe a un file, usando una codifica specificata e quindi lo chiude.Se il file specificato non esiste, questo metodo ne crea uno, scrive la riga specificata e quindi lo chiude.</summary>
      <param name="path">File in cui aggiungere righe.Se il file non esiste già, verrà creato.</param>
      <param name="contents">Righe da aggiungere al file.</param>
      <param name="encoding">Codifica dei caratteri da usare.</param>
      <exception cref="T:System.ArgumentException">Il parametro <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti dal metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">Il parametro<paramref name=" path" />, <paramref name="contents" /> o <paramref name="encoding" /> è null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> non è valido (ad esempio, la directory non esiste o si trova su un'unità non connessa).</exception>
      <exception cref="T:System.IO.FileNotFoundException">Il file specificato dal parametro <paramref name="path" /> non è stato trovato.</exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O durante l'apertura del file.</exception>
      <exception cref="T:System.IO.PathTooLongException">Il parametro <paramref name="path" /> supera la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido.</exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Il parametro <paramref name="path" /> consente di specificare un file di sola lettura.-oppure-L'operazione non è supportata sulla piattaforma corrente.-oppure-<paramref name="path" /> è una directory.-oppure-Il chiamante non dispone dell'autorizzazione richiesta.</exception>
    </member>
    <member name="M:System.IO.File.AppendAllText(System.String,System.String)">
      <summary>Apre un file, aggiunge la stringa specificata e quindi lo chiude.Se il file non esiste, questo metodo ne crea uno, scrive la stringa specificata e quindi lo chiude.</summary>
      <param name="path">File a cui aggiungere la stringa specificata. </param>
      <param name="contents">Stringa da aggiungere al file. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido (ad esempio, la directory non esiste o si trova su un'unità non connessa). </exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O durante l'apertura del file. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> ha specificato un file che è in sola lettura.-oppure- L'operazione non è supportata sulla piattaforma corrente.-oppure- <paramref name="path" /> ha specificato una directory.-oppure- Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.AppendAllText(System.String,System.String,System.Text.Encoding)">
      <summary>Accoda la stringa specificata al file, creando il file nel caso in cui non esista.</summary>
      <param name="path">File a cui aggiungere la stringa specificata. </param>
      <param name="contents">Stringa da aggiungere al file. </param>
      <param name="encoding">Codifica dei caratteri da usare. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido (ad esempio, la directory non esiste o si trova su un'unità non connessa). </exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O durante l'apertura del file. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> ha specificato un file che è in sola lettura.-oppure- L'operazione non è supportata sulla piattaforma corrente.-oppure- <paramref name="path" /> ha specificato una directory.-oppure- Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.AppendText(System.String)">
      <summary>Crea un oggetto <see cref="T:System.IO.StreamWriter" /> che aggiunge testo con codifica UTF-8 a un file esistente o a un nuovo file se quello specificato non esiste.</summary>
      <returns>Writer di flusso che aggiunge testo con codifica UTF-8 al file specificato o a un nuovo file.</returns>
      <param name="path">Percorso del file al quale aggiungere testo. </param>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido (ad esempio, la directory non esiste o si trova su un'unità non connessa). </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Copy(System.String,System.String)">
      <summary>Copia un file esistente in un nuovo file.Non è consentito sovrascrivere un file con lo stesso nome.</summary>
      <param name="sourceFileName">File da copiare. </param>
      <param name="destFileName">Nome del file di destinazione.Non può essere una directory o un file esistente.</param>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> o <paramref name="destFileName" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti dal campo <see cref="F:System.IO.Path.InvalidPathChars" />.-oppure- <paramref name="sourceFileName" /> o <paramref name="destFileName" /> specifica una directory. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> o <paramref name="destFileName" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato in <paramref name="sourceFileName" /> o <paramref name="destFileName" /> non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <exception cref="T:System.IO.FileNotFoundException">Impossibile trovare <paramref name="sourceFileName" />. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destFileName" /> esiste.-oppure- Si è verificato un errore di I/O. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceFileName" /> o <paramref name="destFileName" /> è in un formato non valido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Copy(System.String,System.String,System.Boolean)">
      <summary>Copia un file esistente in un nuovo file.È consentito sovrascrivere un file con lo stesso nome.</summary>
      <param name="sourceFileName">File da copiare. </param>
      <param name="destFileName">Nome del file di destinazione.Non può essere una directory.</param>
      <param name="overwrite">true se il file di destinazione può essere sovrascritto; in caso contrario, false. </param>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. -oppure-<paramref name="destFileName" /> è di sola lettura.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> o <paramref name="destFileName" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti dal campo <see cref="F:System.IO.Path.InvalidPathChars" />.-oppure- <paramref name="sourceFileName" /> o <paramref name="destFileName" /> specifica una directory. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> o <paramref name="destFileName" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato in <paramref name="sourceFileName" /> o <paramref name="destFileName" /> non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <exception cref="T:System.IO.FileNotFoundException">Impossibile trovare <paramref name="sourceFileName" />. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destFileName" /> esiste e <paramref name="overwrite" /> è false.-oppure- Si è verificato un errore di I/O. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceFileName" /> o <paramref name="destFileName" /> è in un formato non valido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Create(System.String)">
      <summary>Crea o sovrascrive un file nel percorso specificato.</summary>
      <returns>Oggetto <see cref="T:System.IO.FileStream" /> che fornisce l'accesso in lettura/scrittura al file specificato in <paramref name="path" />.</returns>
      <param name="path">Percorso e nome del file da creare. </param>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta.-oppure- <paramref name="path" /> ha specificato un file che è in sola lettura. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O durante la creazione del file. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Create(System.String,System.Int32)">
      <summary>Crea o sovrascrive il file specificato.</summary>
      <returns>Oggetto <see cref="T:System.IO.FileStream" /> con le dimensioni del buffer specificate che fornisce l'accesso in lettura/scrittura al file specificato in <paramref name="path" />.</returns>
      <param name="path">Nome del file. </param>
      <param name="bufferSize">Numero di byte memorizzati nel buffer per letture e scritture nel file. </param>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta.-oppure- <paramref name="path" /> ha specificato un file che è in sola lettura. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O durante la creazione del file. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Create(System.String,System.Int32,System.IO.FileOptions)">
      <summary>Crea o sovrascrive il file indicato, specificando le dimensioni del buffer e un valore <see cref="T:System.IO.FileOptions" /> che descrive come creare o sovrascrivere il file.</summary>
      <returns>Nuovo file con le dimensioni del buffer specificate.</returns>
      <param name="path">Nome del file. </param>
      <param name="bufferSize">Numero di byte memorizzati nel buffer per letture e scritture nel file. </param>
      <param name="options">Uno dei valori di <see cref="T:System.IO.FileOptions" /> che descrive come creare o sovrascrivere il file.</param>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta.-oppure- <paramref name="path" /> ha specificato un file che è in sola lettura. -oppure-<see cref="F:System.IO.FileOptions.Encrypted" /> viene specificato per <paramref name="options" /> e la crittografia del file non è supportata sulla piattaforma corrente.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O durante la creazione del file. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta.-oppure- <paramref name="path" /> ha specificato un file che è in sola lettura. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta.-oppure- <paramref name="path" /> ha specificato un file che è in sola lettura. </exception>
    </member>
    <member name="M:System.IO.File.CreateText(System.String)">
      <summary>Crea o apre un file per la scrittura di testo con codifica UTF-8.</summary>
      <returns>Oggetto <see cref="T:System.IO.StreamWriter" /> che scrive nel file specificato usando la codifica UTF-8.</returns>
      <param name="path">File da aprire per la scrittura. </param>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Delete(System.String)">
      <summary>Elimina il file specificato. </summary>
      <param name="path">Nome del file da eliminare.Non è supportato l'uso di caratteri jolly.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <exception cref="T:System.IO.IOException">Il file specificato è in uso. -oppure-È presente un handle aperto sul file e il sistema operativo è Windows XP o versioni precedenti.Questo handle aperto può derivare da file e directory di enumerazione.Per altre informazioni, vedere Procedura: enumerare directory e file.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta.-oppure- Il file è un file eseguibile è in uso.-oppure- <paramref name="path" /> è una directory.-oppure- <paramref name="path" /> ha specificato un file in sola lettura. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Exists(System.String)">
      <summary>Determina se il file specificato esiste.</summary>
      <returns>true se il chiamante dispone delle autorizzazioni richieste e se <paramref name="path" /> contiene il nome di un file esistente; in caso contrario, false.Questo metodo restituisce anche false se <paramref name="path" /> è null, un percorso non valido o una stringa di lunghezza zero.Se il chiamante non dispone di autorizzazioni sufficienti per leggere il file specificato, non viene generata alcuna eccezione e il metodo restituisce false a prescindere dall'esistenza di <paramref name="path" />.</returns>
      <param name="path">File da controllare. </param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetAttributes(System.String)">
      <summary>Ottiene l'oggetto <see cref="T:System.IO.FileAttributes" /> del file nel percorso.</summary>
      <returns>Oggetto <see cref="T:System.IO.FileAttributes" /> del file nel percorso.</returns>
      <param name="path">Percorso del file. </param>
      <exception cref="T:System.ArgumentException">Il parametro <paramref name="path" /> è vuoto, contiene solo spazi vuoti oppure caratteri non validi. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" /> rappresenta un file e non è valido, ad esempio in quanto si trova su un'unità non connessa o è impossibile trovare il file. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> rappresenta una directory e non è valida, ad esempio in quanto si trova su un'unità non connessa o è impossibile trovare la directory.</exception>
      <exception cref="T:System.IO.IOException">Il file è in uso in un altro processo.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetCreationTime(System.String)">
      <summary>Restituisce la data e l'ora di creazione del file o della directory specificata.</summary>
      <returns>Struttura <see cref="T:System.DateTime" /> impostata sulla data e l'ora di creazione del file o della directory specificata.Questo valore è espresso nell'ora locale.</returns>
      <param name="path">File o directory per cui ottenere informazioni sulla data e l'ora di creazione. </param>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetCreationTimeUtc(System.String)">
      <summary>Restituisce la data e l'ora di creazione, nell'ora UTC (Universal Coordinated Time), del file o della directory specificata.</summary>
      <returns>Struttura <see cref="T:System.DateTime" /> impostata sulla data e l'ora di creazione del file o della directory specificata.Questo valore è espresso nell'ora UTC.</returns>
      <param name="path">File o directory per cui ottenere informazioni sulla data e l'ora di creazione. </param>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastAccessTime(System.String)">
      <summary>Restituisce la data e l'ora dell'ultimo accesso al file o alla directory specificata.</summary>
      <returns>Struttura <see cref="T:System.DateTime" /> impostata sulla data e l'ora dell'ultimo accesso al file o alla directory specificata.Questo valore è espresso nell'ora locale.</returns>
      <param name="path">File o directory per cui ottenere informazioni sulla data e l'ora di accesso. </param>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastAccessTimeUtc(System.String)">
      <summary>Restituisce la data e l'ora, nell'ora UTC (Universal Coordinated Time), dell'ultimo accesso al file o alla directory specificata.</summary>
      <returns>Struttura <see cref="T:System.DateTime" /> impostata sulla data e l'ora dell'ultimo accesso al file o alla directory specificata.Questo valore è espresso nell'ora UTC.</returns>
      <param name="path">File o directory per cui ottenere informazioni sulla data e l'ora di accesso. </param>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastWriteTime(System.String)">
      <summary>Restituisce la data e l'ora dell'ultima scrittura nel file o nella directory specificata.</summary>
      <returns>Struttura <see cref="T:System.DateTime" /> impostata sulla data e l'ora dell'ultima scrittura nel file o nella directory specificata.Questo valore è espresso nell'ora locale.</returns>
      <param name="path">File o directory per cui ottenere informazioni sulla data e l'ora di scrittura. </param>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastWriteTimeUtc(System.String)">
      <summary>Restituisce la data e l'ora, nell'ora UTC (Universal Coordinated Time), dell'ultima scrittura nel file o nella directory specificata.</summary>
      <returns>Struttura <see cref="T:System.DateTime" /> impostata sulla data e l'ora dell'ultima scrittura nel file o nella directory specificata.Questo valore è espresso nell'ora UTC.</returns>
      <param name="path">File o directory per cui ottenere informazioni sulla data e l'ora di scrittura. </param>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Move(System.String,System.String)">
      <summary>Sposta il file specificato in un nuovo percorso, consentendo di specificare per esso un nuovo nome.</summary>
      <param name="sourceFileName">Nome del file da spostare.Può includere un percorso relativo o assoluto.</param>
      <param name="destFileName">Nuovo percorso e nome del file.</param>
      <exception cref="T:System.IO.IOException">Il file di destinazione esiste già.-oppure-Impossibile trovare <paramref name="sourceFileName" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> o <paramref name="destFileName" /> è null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> o <paramref name="destFileName" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti in <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato in <paramref name="sourceFileName" /> o <paramref name="destFileName" /> non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceFileName" /> o <paramref name="destFileName" /> è in un formato non valido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode)">
      <summary>Apre un oggetto <see cref="T:System.IO.FileStream" /> nel percorso specificato con accesso in lettura/scrittura.</summary>
      <returns>Oggetto <see cref="T:System.IO.FileStream" /> aperto nella modalità e nel percorso specificati, con accesso in lettura/scrittura e non condiviso.</returns>
      <param name="path">File da aprire. </param>
      <param name="mode">Valore <see cref="T:System.IO.FileMode" /> che specifica se verrà creato un file qualora non ne esista già uno e determina se mantenere o sovrascrivere il contenuto dei file esistenti. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O durante l'apertura del file. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> ha specificato un file che è in sola lettura.-oppure- L'operazione non è supportata sulla piattaforma corrente.-oppure- <paramref name="path" /> ha specificato una directory.-oppure- Il chiamante non dispone dell'autorizzazione richiesta. -oppure-<paramref name="mode" /> è <see cref="F:System.IO.FileMode.Create" /> e il file specificato è un file nascosto.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> ha specificato un valore non valido. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Il file specificato in <paramref name="path" /> non è stato trovato. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode,System.IO.FileAccess)">
      <summary>Apre un oggetto <see cref="T:System.IO.FileStream" /> nel percorso specificato, con la modalità e l'accesso indicati.</summary>
      <returns>Oggetto <see cref="T:System.IO.FileStream" /> non condiviso che consente di accedere al file specificato, con la modalità e l'accesso indicati.</returns>
      <param name="path">File da aprire. </param>
      <param name="mode">Valore <see cref="T:System.IO.FileMode" /> che specifica se verrà creato un file qualora non ne esista già uno e determina se mantenere o sovrascrivere il contenuto dei file esistenti. </param>
      <param name="access">Valore <see cref="T:System.IO.FileAccess" /> che specifica le operazioni eseguibili sul file. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />.-oppure- <paramref name="access" /> ha specificato Read e <paramref name="mode" /> ha specificato Create, CreateNew, Truncate, oppure Append. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O durante l'apertura del file. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> ha specificato un file che è in sola lettura e <paramref name="access" /> non è Read.-oppure- <paramref name="path" /> ha specificato una directory.-oppure- Il chiamante non dispone dell'autorizzazione richiesta. -oppure-<paramref name="mode" /> è <see cref="F:System.IO.FileMode.Create" /> e il file specificato è un file nascosto.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> o <paramref name="access" /> ha specificato un valore non valido. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Il file specificato in <paramref name="path" /> non è stato trovato. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>Apre un oggetto <see cref="T:System.IO.FileStream" /> nel percorso specificato, con la modalità indicata con accesso in lettura, scrittura o lettura/scrittura e l'opzione di condivisione indicata.</summary>
      <returns>Oggetto <see cref="T:System.IO.FileStream" /> nel percorso specificato, con la modalità indicata con accesso in lettura, scrittura o lettura/scrittura e l'opzione di condivisione indicata.</returns>
      <param name="path">File da aprire. </param>
      <param name="mode">Valore <see cref="T:System.IO.FileMode" /> che specifica se verrà creato un file qualora non ne esista già uno e determina se mantenere o sovrascrivere il contenuto dei file esistenti. </param>
      <param name="access">Valore <see cref="T:System.IO.FileAccess" /> che specifica le operazioni eseguibili sul file. </param>
      <param name="share">Valore <see cref="T:System.IO.FileShare" /> che specifica il tipo di accesso al file di cui dispongono altri thread. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />.-oppure- <paramref name="access" /> ha specificato Read e <paramref name="mode" /> ha specificato Create, CreateNew, Truncate, oppure Append. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O durante l'apertura del file. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> ha specificato un file che è in sola lettura e <paramref name="access" /> non è Read.-oppure- <paramref name="path" /> ha specificato una directory.-oppure- Il chiamante non dispone dell'autorizzazione richiesta. -oppure-<paramref name="mode" /> è <see cref="F:System.IO.FileMode.Create" /> e il file specificato è un file nascosto.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> o <paramref name="access" /> o <paramref name="share" /> ha specificato un valore non valido. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Il file specificato in <paramref name="path" /> non è stato trovato. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.OpenRead(System.String)">
      <summary>Apre un file esistente per la lettura.</summary>
      <returns>Oggetto<see cref="T:System.IO.FileStream" /> di sola lettura nel percorso specificato.</returns>
      <param name="path">File da aprire per la lettura. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> ha specificato una directory.-oppure- Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Il file specificato in <paramref name="path" /> non è stato trovato. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O durante l'apertura del file. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.OpenText(System.String)">
      <summary>Apre un file di testo esistente con codifica UTF-8 per la lettura.</summary>
      <returns>Oggetto <see cref="T:System.IO.StreamReader" /> nel percorso specificato.</returns>
      <param name="path">File da aprire per la lettura. </param>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <exception cref="T:System.IO.FileNotFoundException">Il file specificato in <paramref name="path" /> non è stato trovato. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.OpenWrite(System.String)">
      <summary>Apre un file esistente o crea un nuovo file per la scrittura.</summary>
      <returns>Oggetto <see cref="T:System.IO.FileStream" /> non condiviso nel percorso specificato con accesso a <see cref="F:System.IO.FileAccess.Write" />.</returns>
      <param name="path">File da aprire per la scrittura. </param>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta.-oppure- <paramref name="path" /> ha specificato un file o una directory in sola lettura. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllBytes(System.String)">
      <summary>Apre un file binario, ne legge il contenuto in una matrice di byte e lo chiude.</summary>
      <returns>Matrice di byte con il contenuto del file.</returns>
      <param name="path">File da aprire per la lettura. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O durante l'apertura del file. </exception>
      <exception cref="T:System.UnauthorizedAccessException">L'operazione non è supportata sulla piattaforma corrente.-oppure- <paramref name="path" /> ha specificato una directory.-oppure- Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Il file specificato in <paramref name="path" /> non è stato trovato. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllLines(System.String)">
      <summary>Apre un file di testo, ne legge tutte le righe e quindi lo chiude.</summary>
      <returns>Matrice di stringhe contenente tutte le righe del file.</returns>
      <param name="path">File da aprire per la lettura. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O durante l'apertura del file. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> ha specificato un file che è in sola lettura.-oppure- L'operazione non è supportata sulla piattaforma corrente.-oppure- <paramref name="path" /> ha specificato una directory.-oppure- Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Il file specificato in <paramref name="path" /> non è stato trovato. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllLines(System.String,System.Text.Encoding)">
      <summary>Apre un file, ne legge tutte le righe con la codifica specificata e quindi lo chiude.</summary>
      <returns>Matrice di stringhe contenente tutte le righe del file.</returns>
      <param name="path">File da aprire per la lettura. </param>
      <param name="encoding">Codifica applicata al contenuto del file. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O durante l'apertura del file. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> ha specificato un file che è in sola lettura.-oppure- L'operazione non è supportata sulla piattaforma corrente.-oppure- <paramref name="path" /> ha specificato una directory.-oppure- Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Il file specificato in <paramref name="path" /> non è stato trovato. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllText(System.String)">
      <summary>Apre un file di testo, ne legge tutte le righe e quindi lo chiude.</summary>
      <returns>Stringa contenente tutte le righe del file.</returns>
      <param name="path">File da aprire per la lettura. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O durante l'apertura del file. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> ha specificato un file che è in sola lettura.-oppure- L'operazione non è supportata sulla piattaforma corrente.-oppure- <paramref name="path" /> ha specificato una directory.-oppure- Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Il file specificato in <paramref name="path" /> non è stato trovato. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllText(System.String,System.Text.Encoding)">
      <summary>Apre un file, ne legge tutte le righe con la codifica specificata e quindi lo chiude.</summary>
      <returns>Stringa contenente tutte le righe del file.</returns>
      <param name="path">File da aprire per la lettura. </param>
      <param name="encoding">Codifica applicata al contenuto del file. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O durante l'apertura del file. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> ha specificato un file che è in sola lettura.-oppure- L'operazione non è supportata sulla piattaforma corrente.-oppure- <paramref name="path" /> ha specificato una directory.-oppure- Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Il file specificato in <paramref name="path" /> non è stato trovato. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadLines(System.String)">
      <summary>Legge le righe di un file.</summary>
      <returns>Tutte le righe del file o le righe sono il risultato di una query.</returns>
      <param name="path">File da leggere.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti o contiene uno o più caratteri non validi definiti per il <see cref="M:System.IO.Path.GetInvalidPathChars" /> metodo.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> non è valido, poiché, ad esempio, si trova su un'unità non mappata.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Il file specificato dal parametro <paramref name="path" /> non è stato trovato.</exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O durante l'apertura del file.</exception>
      <exception cref="T:System.IO.PathTooLongException">Il parametro <paramref name="path" /> supera la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Il parametro <paramref name="path" /> consente di specificare un file di sola lettura.-oppure-L'operazione non è supportata sulla piattaforma corrente.-oppure-<paramref name="path" /> è una directory.-oppure-Il chiamante non dispone dell'autorizzazione richiesta.</exception>
    </member>
    <member name="M:System.IO.File.ReadLines(System.String,System.Text.Encoding)">
      <summary>Legge le righe di un file con una codifica specificata.</summary>
      <returns>Tutte le righe del file o le righe sono il risultato di una query.</returns>
      <param name="path">File da leggere.</param>
      <param name="encoding">Codifica applicata al contenuto del file. </param>
      <exception cref="T:System.ArgumentException">Il parametro <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti dal metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> non è valido, poiché, ad esempio, si trova su un'unità non mappata.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Il file specificato dal parametro <paramref name="path" /> non è stato trovato.</exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O durante l'apertura del file.</exception>
      <exception cref="T:System.IO.PathTooLongException">Il parametro <paramref name="path" /> supera la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Il parametro <paramref name="path" /> consente di specificare un file di sola lettura.-oppure-L'operazione non è supportata sulla piattaforma corrente.-oppure-<paramref name="path" /> è una directory.-oppure-Il chiamante non dispone dell'autorizzazione richiesta.</exception>
    </member>
    <member name="M:System.IO.File.SetAttributes(System.String,System.IO.FileAttributes)">
      <summary>Imposta l'enumerazione <see cref="T:System.IO.FileAttributes" /> del file nel percorso specificato.</summary>
      <param name="path">Percorso del file. </param>
      <param name="fileAttributes">Combinazione bit per bit dei valori di enumerazione. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è vuoto, contiene solo spazi vuoti, caratteri non validi, oppure l'attributo del file non è valido. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <exception cref="T:System.IO.FileNotFoundException">Impossibile trovare il file.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> ha specificato un file che è in sola lettura.-oppure- L'operazione non è supportata sulla piattaforma corrente.-oppure- <paramref name="path" /> ha specificato una directory.-oppure- Il chiamante non dispone dell'autorizzazione richiesta.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetCreationTime(System.String,System.DateTime)">
      <summary>Imposta la data e l'ora di creazione del file.</summary>
      <param name="path">File per cui impostare le informazioni sulla data e l'ora di creazione. </param>
      <param name="creationTime">Oggetto <see cref="T:System.DateTime" /> contenente il valore da impostare per la data e l'ora di creazione di <paramref name="path" />.Questo valore è espresso nell'ora locale.</param>
      <exception cref="T:System.IO.FileNotFoundException">Il percorso specificato non è stato trovato. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O durante l'esecuzione dell'operazione. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> specifica un valore al di fuori dell'intervallo di date, ore o di entrambe, consentite per l'operazione. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetCreationTimeUtc(System.String,System.DateTime)">
      <summary>Imposta la data e l'ora, nell'ora UTC (Universal Coordinated Time), della creazione del file.</summary>
      <param name="path">File per cui impostare le informazioni sulla data e l'ora di creazione. </param>
      <param name="creationTimeUtc">Oggetto <see cref="T:System.DateTime" /> contenente il valore da impostare per la data e l'ora di creazione di <paramref name="path" />.Questo valore è espresso nell'ora UTC.</param>
      <exception cref="T:System.IO.FileNotFoundException">Il percorso specificato non è stato trovato. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O durante l'esecuzione dell'operazione. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> specifica un valore al di fuori dell'intervallo di date, ore o di entrambe, consentite per l'operazione. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastAccessTime(System.String,System.DateTime)">
      <summary>Imposta la data e l'ora dell'ultimo accesso al file specificato.</summary>
      <param name="path">File per cui impostare le informazioni sulla data e l'ora di accesso. </param>
      <param name="lastAccessTime">Oggetto <see cref="T:System.DateTime" /> contenente il valore da impostare per la data e l'ora dell'ultimo accesso di <paramref name="path" />.Questo valore è espresso nell'ora locale.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Il percorso specificato non è stato trovato. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTime" /> specifica un valore esterno all'intervallo di date od ore consentite per l'operazione.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastAccessTimeUtc(System.String,System.DateTime)">
      <summary>Imposta la data e l'ora, nell'ora UTC (Universal Coordinated Time), dell'ultimo accesso al file specificato.</summary>
      <param name="path">File per cui impostare le informazioni sulla data e l'ora di accesso. </param>
      <param name="lastAccessTimeUtc">Oggetto <see cref="T:System.DateTime" /> contenente il valore da impostare per la data e l'ora dell'ultimo accesso di <paramref name="path" />.Questo valore è espresso nell'ora UTC.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Il percorso specificato non è stato trovato. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTimeUtc" /> specifica un valore esterno all'intervallo di date od ore consentite per l'operazione.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastWriteTime(System.String,System.DateTime)">
      <summary>Imposta la data e l'ora dell'ultima scrittura nel file specificato.</summary>
      <param name="path">File per cui impostare le informazioni sulla data e l'ora. </param>
      <param name="lastWriteTime">Oggetto <see cref="T:System.DateTime" /> contenente il valore da impostare per la data e l'ora dell'ultima scrittura di <paramref name="path" />.Questo valore è espresso nell'ora locale.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Il percorso specificato non è stato trovato. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTime" /> specifica un valore esterno all'intervallo di date od ore consentite per l'operazione.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastWriteTimeUtc(System.String,System.DateTime)">
      <summary>Imposta la data e l'ora, nell'ora UTC (Universal Coordinated Time), dell'ultima scrittura nel file specificato.</summary>
      <param name="path">File per cui impostare le informazioni sulla data e l'ora. </param>
      <param name="lastWriteTimeUtc">Oggetto <see cref="T:System.DateTime" /> contenente il valore da impostare per la data e l'ora dell'ultima scrittura di <paramref name="path" />.Questo valore è espresso nell'ora UTC.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Il percorso specificato non è stato trovato. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTimeUtc" /> specifica un valore esterno all'intervallo di date od ore consentite per l'operazione.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.WriteAllBytes(System.String,System.Byte[])">
      <summary>Crea un nuovo file, scrive la matrice di byte specificata e quindi lo chiude.Se il file di destinazione è già esistente, viene sovrascritto.</summary>
      <param name="path">File in cui scrivere. </param>
      <param name="bytes">Byte da scrivere nel file. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null o la matrice di byte è vuota. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O durante l'apertura del file. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> ha specificato un file che è in sola lettura.-oppure- L'operazione non è supportata sulla piattaforma corrente.-oppure- <paramref name="path" /> ha specificato una directory.-oppure- Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.WriteAllLines(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>Crea un nuovo file, vi scrive una raccolta di stringhe e quindi lo chiude.</summary>
      <param name="path">File in cui scrivere.</param>
      <param name="contents">Righe da scrivere nel file.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti o contiene uno o più caratteri non validi definiti per il <see cref="M:System.IO.Path.GetInvalidPathChars" /> metodo.</exception>
      <exception cref="T:System.ArgumentNullException">Sia<paramref name=" path " />o <paramref name="contents" /> è null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> non è valido, poiché, ad esempio, si trova su un'unità non mappata.</exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O durante l'apertura del file.</exception>
      <exception cref="T:System.IO.PathTooLongException">Il parametro <paramref name="path" /> supera la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido.</exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Il parametro <paramref name="path" /> consente di specificare un file di sola lettura.-oppure-L'operazione non è supportata sulla piattaforma corrente.-oppure-<paramref name="path" /> è una directory.-oppure-Il chiamante non dispone dell'autorizzazione richiesta.</exception>
    </member>
    <member name="M:System.IO.File.WriteAllLines(System.String,System.Collections.Generic.IEnumerable{System.String},System.Text.Encoding)">
      <summary>Crea un nuovo file usando la codifica specificata, vi scrive una raccolta di stringhe e quindi lo chiude.</summary>
      <param name="path">File in cui scrivere.</param>
      <param name="contents">Righe da scrivere nel file.</param>
      <param name="encoding">Codifica dei caratteri da usare.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti o contiene uno o più caratteri non validi definiti per il <see cref="M:System.IO.Path.GetInvalidPathChars" /> metodo.</exception>
      <exception cref="T:System.ArgumentNullException">Sia<paramref name=" path" />,<paramref name=" contents" />, o <paramref name="encoding" /> è null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> non è valido, poiché, ad esempio, si trova su un'unità non mappata.</exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O durante l'apertura del file.</exception>
      <exception cref="T:System.IO.PathTooLongException">Il parametro <paramref name="path" /> supera la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti da un numero di caratteri inferiore a 248 e i nomi file devono essere composti da un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido.</exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Il parametro <paramref name="path" /> consente di specificare un file di sola lettura.-oppure-L'operazione non è supportata sulla piattaforma corrente.-oppure-<paramref name="path" /> è una directory.-oppure-Il chiamante non dispone dell'autorizzazione richiesta.</exception>
    </member>
    <member name="M:System.IO.File.WriteAllText(System.String,System.String)">
      <summary>Crea un nuovo file, scrive la stringa specificata e quindi lo chiude.Se il file di destinazione è già esistente, viene sovrascritto.</summary>
      <param name="path">File in cui scrivere. </param>
      <param name="contents">Stringa da scrivere nel file. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Il parametro <paramref name="path" /> è null o il parametro <paramref name="contents" /> è vuoto.  </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O durante l'apertura del file. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> ha specificato un file che è in sola lettura.-oppure- L'operazione non è supportata sulla piattaforma corrente.-oppure- <paramref name="path" /> ha specificato una directory.-oppure- Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.WriteAllText(System.String,System.String,System.Text.Encoding)">
      <summary>Crea un nuovo file, scrive la stringa specificata usando la codifica specificata e quindi lo chiude.Se il file di destinazione è già esistente, viene sovrascritto.</summary>
      <param name="path">File in cui scrivere. </param>
      <param name="contents">Stringa da scrivere nel file. </param>
      <param name="encoding">Codifica da applicare alla stringa.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti da <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Il parametro <paramref name="path" /> è null o il parametro <paramref name="contents" /> è vuoto. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido (ad esempio, si trova su un'unità non connessa). </exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O durante l'apertura del file. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> ha specificato un file che è in sola lettura.-oppure- L'operazione non è supportata sulla piattaforma corrente.-oppure- <paramref name="path" /> ha specificato una directory.-oppure- Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> è in un formato non valido. </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.IO.FileInfo">
      <summary>Fornisce proprietà e metodi dell'istanza per la creazione, la copia, lo spostamento e l'apertura di file e facilita la creazione di oggetti <see cref="T:System.IO.FileStream" />.La classe non può essere ereditata.Per esaminare il codice sorgente di .NET Framework per questo tipo, vedere il Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileInfo.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.IO.FileInfo" />, che agisce da wrapper per un percorso di file.</summary>
      <param name="fileName">Nome completo del nuovo file oppure nome file relativo.Non terminare il percorso con il carattere di separazione directory.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="fileName" /> è null. </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentException">Il nome del file è vuoto, contiene solo spazi o contiene caratteri non validi. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Accesso a <paramref name="fileName" /> negato. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="fileName" /> contiene il carattere di due punti (:) all'interno della stringa. </exception>
    </member>
    <member name="M:System.IO.FileInfo.AppendText">
      <summary>Crea un oggetto <see cref="T:System.IO.StreamWriter" /> che aggiunge testo al file rappresentato da questa istanza di <see cref="T:System.IO.FileInfo" />.</summary>
      <returns>Nuovo oggetto StreamWriter.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.CopyTo(System.String)">
      <summary>Copia un file esistente in un nuovo file, non consentendo la sovrascrittura di un file esistente.</summary>
      <returns>Nuovo file con percorso completo.</returns>
      <param name="destFileName">Nome del nuovo file in cui copiare. </param>
      <exception cref="T:System.ArgumentException">Il parametro <paramref name="destFileName" /> è vuoto, contiene solo spazi vuoti oppure caratteri non validi. </exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore oppure il file di destinazione esiste già. </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destFileName" /> è null. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Viene passato un percorso di directory oppure è in corso lo spostamento del file in una diversa unità. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La directory specificata in <paramref name="destFileName" /> non esiste.</exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destFileName" /> contiene due punti (:) all'interno della stringa ma non specifica il volume. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.CopyTo(System.String,System.Boolean)">
      <summary>Copia un file esistente in un nuovo file, consentendo la sovrascrittura di un file esistente.</summary>
      <returns>Nuovo file oppure sovrascrittura di un file esistente se <paramref name="overwrite" /> è true.Se il file esiste e <paramref name="overwrite" /> è false, viene generata un'eccezione <see cref="T:System.IO.IOException" />.</returns>
      <param name="destFileName">Nome del nuovo file in cui copiare. </param>
      <param name="overwrite">true per consentire di sovrascrivere un file esistente; in caso contrario, false. </param>
      <exception cref="T:System.ArgumentException">Il parametro <paramref name="destFileName" /> è vuoto, contiene solo spazi vuoti oppure caratteri non validi. </exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore oppure il file di destinazione esiste già e <paramref name="overwrite" /> è false. </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destFileName" /> è null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La directory specificata in <paramref name="destFileName" /> non esiste.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Viene passato un percorso di directory oppure è in corso lo spostamento del file in una diversa unità. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destFileName" /> contiene il carattere di due punti (:) all'interno della stringa. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Create">
      <summary>Crea un file.</summary>
      <returns>Nuovo file.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.CreateText">
      <summary>Crea un oggetto <see cref="T:System.IO.StreamWriter" /> che scrive un nuovo file di testo.</summary>
      <returns>Nuovo oggetto StreamWriter.</returns>
      <exception cref="T:System.UnauthorizedAccessException">Il nome file è una directory. </exception>
      <exception cref="T:System.IO.IOException">Il disco è di sola lettura. </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Delete">
      <summary>Elimina un file in modo permanente.</summary>
      <exception cref="T:System.IO.IOException">Il file di destinazione è aperto o associato alla memoria su un computer con sistema operativo Microsoft Windows NT.-oppure-È presente un handle aperto sul file e il sistema operativo è Windows XP o versioni precedenti.Questo handle aperto può derivare da file e directory di enumerazione.Per altre informazioni, vedere Procedura: enumerare directory e file.</exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il percorso è una directory. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.Directory">
      <summary>Ottiene un'istanza della directory padre.</summary>
      <returns>Oggetto <see cref="T:System.IO.DirectoryInfo" /> che rappresenta la directory padre di questo file.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido, poiché, ad esempio, si trova su un'unità non connessa. </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.DirectoryName">
      <summary>Ottiene una stringa che rappresenta il percorso completo della directory.</summary>
      <returns>Stringa che rappresenta il percorso completo della directory.</returns>
      <exception cref="T:System.ArgumentNullException">null è stato passato per il nome della directory. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso completo è costituito da 260 o più caratteri.</exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.Exists">
      <summary>Ottiene un valore che indica se un file esiste.</summary>
      <returns>true se il file esiste; false se il file non esiste oppure è una directory.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileInfo.IsReadOnly">
      <summary>Ottiene o imposta un valore che determina se il file corrente è di sola lettura.</summary>
      <returns>true se l'oggetto corrente è di sola lettura; in caso contrario, false.</returns>
      <exception cref="T:System.IO.FileNotFoundException">Il file descritto dall'oggetto <see cref="T:System.IO.FileInfo" /> corrente non è stato trovato.</exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O durante l'apertura del file.</exception>
      <exception cref="T:System.UnauthorizedAccessException">L'operazione non è supportata sulla piattaforma corrente.-oppure- Il chiamante non dispone dell'autorizzazione richiesta.</exception>
      <exception cref="T:System.ArgumentException">L'utente non dispone dell'autorizzazione di scrittura, ma ha tentato di impostare questa proprietà su false.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileInfo.Length">
      <summary>Ottiene le dimensioni in byte del file corrente.</summary>
      <returns>Dimensione del file corrente espressa in byte.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> non può aggiornare lo stato del file o della directory. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Il file specificato non esiste.-oppure- La proprietà Length è chiamata per una directory. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileInfo.MoveTo(System.String)">
      <summary>Sposta il file specificato in un nuovo percorso, consentendo di specificare per esso un nuovo nome.</summary>
      <param name="destFileName">Percorso nel quale spostare il file, per il quale è possibile specificare un diverso nome file. </param>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O, ad esempio il file di destinazione esiste già o la periferica di destinazione non è pronta. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destFileName" /> è null. </exception>
      <exception cref="T:System.ArgumentException">Il parametro <paramref name="destFileName" /> è vuoto, contiene solo spazi vuoti oppure caratteri non validi. </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="destFileName" /> è in sola lettura o è una directory. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Il file non è stato individuato. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido, poiché, ad esempio, si trova su un'unità non connessa. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destFileName" /> contiene il carattere di due punti (:) all'interno della stringa. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.Name">
      <summary>Ottiene il nome del file.</summary>
      <returns>Nome del file.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode)">
      <summary>Apre un file nella modalità specificata.</summary>
      <returns>File aperto nella modalità specificata, con accesso in lettura/scrittura e non condiviso.</returns>
      <param name="mode">Costante <see cref="T:System.IO.FileMode" /> che specifica la modalità, ad esempio Open o Append, per l'apertura del file. </param>
      <exception cref="T:System.IO.FileNotFoundException">Il file non è stato individuato. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il file è in sola lettura o è una directory. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido, poiché, ad esempio, si trova su un'unità non connessa. </exception>
      <exception cref="T:System.IO.IOException">File già aperto. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode,System.IO.FileAccess)">
      <summary>Apre un file nella modalità specificata, con accesso in scrittura o lettura/scrittura.</summary>
      <returns>Oggetto <see cref="T:System.IO.FileStream" /> aperto con la modalità e l'accesso specificati e non condiviso.</returns>
      <param name="mode">Costante <see cref="T:System.IO.FileMode" /> che specifica la modalità, ad esempio Open o Append, per l'apertura del file. </param>
      <param name="access">Costante <see cref="T:System.IO.FileAccess" /> che specifica se aprire il file con accesso Read, Write o ReadWrite. </param>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Il file non è stato individuato. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> è in sola lettura o è una directory. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido, poiché, ad esempio, si trova su un'unità non connessa. </exception>
      <exception cref="T:System.IO.IOException">File già aperto. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>Apre un file nella modalità specificata, con accesso in lettura, in scrittura o in lettura/scrittura e l'opzione di condivisione specificata.</summary>
      <returns>Oggetto <see cref="T:System.IO.FileStream" /> aperto con la modalità, l'accesso e le opzioni di condivisione specificati.</returns>
      <param name="mode">Costante <see cref="T:System.IO.FileMode" /> che specifica la modalità, ad esempio Open o Append, per l'apertura del file. </param>
      <param name="access">Costante <see cref="T:System.IO.FileAccess" /> che specifica se aprire il file con accesso Read, Write o ReadWrite. </param>
      <param name="share">Costante <see cref="T:System.IO.FileShare" /> che specifica il tipo di accesso al file degli altri oggetti FileStream. </param>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Il file non è stato individuato. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> è in sola lettura o è una directory. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido, poiché, ad esempio, si trova su un'unità non connessa. </exception>
      <exception cref="T:System.IO.IOException">File già aperto. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.OpenRead">
      <summary>Crea un oggetto <see cref="T:System.IO.FileStream" /> di sola lettura.</summary>
      <returns>Nuovo oggetto <see cref="T:System.IO.FileStream" /> di sola lettura.</returns>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> è in sola lettura o è una directory. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido, poiché, ad esempio, si trova su un'unità non connessa. </exception>
      <exception cref="T:System.IO.IOException">File già aperto. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.OpenText">
      <summary>Crea un oggetto <see cref="T:System.IO.StreamReader" /> con codifica UTF8, che legge da un file di testo esistente.</summary>
      <returns>Nuovo oggetto StreamReader con codifica UTF8.</returns>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Il file non è stato individuato. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> è in sola lettura o è una directory. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido, poiché, ad esempio, si trova su un'unità non connessa. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.OpenWrite">
      <summary>Crea un oggetto <see cref="T:System.IO.FileStream" /> di sola scrittura.</summary>
      <returns>Oggetto <see cref="T:System.IO.FileStream" /> di sola scrittura non condiviso per un file nuovo o esistente.</returns>
      <exception cref="T:System.UnauthorizedAccessException">Il percorso specificato alla creazione di un'istanza dell'oggetto <see cref="T:System.IO.FileInfo" /> è di sola lettura o è una directory.  </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato alla creazione di un'istanza dell'oggetto <see cref="T:System.IO.FileInfo" /> non è valido, poiché, ad esempio, si trova su un'unità non mappata. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.ToString">
      <summary>Restituisce il percorso in forma di stringa.</summary>
      <returns>Stringa che rappresenta il percorso.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.FileOptions">
      <summary>Rappresenta le opzioni avanzate per la creazione di un oggetto <see cref="T:System.IO.FileStream" />.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.FileOptions.Asynchronous">
      <summary>Indica che è possibile utilizzare un file per la lettura e la scrittura asincrona. </summary>
    </member>
    <member name="F:System.IO.FileOptions.DeleteOnClose">
      <summary>Indica l'eliminazione automatica del file quando non è più in uso.</summary>
    </member>
    <member name="F:System.IO.FileOptions.Encrypted">
      <summary>Indica che un file è crittografato ed è possibile decrittografarlo solamente utilizzando lo stesso account utente utilizzato per la crittografia.</summary>
    </member>
    <member name="F:System.IO.FileOptions.None">
      <summary>Indica che non deve essere utilizzata alcuna opzione aggiuntiva quando crea un oggetto <see cref="T:System.IO.FileStream" />.</summary>
    </member>
    <member name="F:System.IO.FileOptions.RandomAccess">
      <summary>Indica l'accesso casuale al file.Il sistema può utilizzarlo come suggerimento per l'ottimizzazione della cache del file.</summary>
    </member>
    <member name="F:System.IO.FileOptions.SequentialScan">
      <summary>Indica che l'accesso del file deve avvenire in modo sequenziale dall'inizio alla fine.Il sistema può utilizzarlo come suggerimento per l'ottimizzazione della cache del file.Se un'applicazione sposta il puntatore a file per l'accesso casuale, la cache potrebbe non essere ottimale ma l'operazione viene comunque garantita.</summary>
    </member>
    <member name="F:System.IO.FileOptions.WriteThrough">
      <summary>Indica al sistema di ignorare le cache intermedie e di accedere direttamente al disco.</summary>
    </member>
    <member name="T:System.IO.FileStream">
      <summary>Fornisce un oggetto <see cref="T:System.IO.Stream" /> per un file, con il supporto di operazioni di lettura e scrittura sincrone e asincrone.Per esaminare il codice sorgente di .NET Framework per questo tipo, vedere il Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.#ctor(Microsoft.Win32.SafeHandles.SafeFileHandle,System.IO.FileAccess)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.IO.FileStream" /> per l'handle di file specificato, con l'autorizzazione di lettura/scrittura specificata. </summary>
      <param name="handle">Handle di file relativo al file che sarà incapsulato nell'oggetto FileStream corrente. </param>
      <param name="access">Costante che imposta le proprietà <see cref="P:System.IO.FileStream.CanRead" /> e <see cref="P:System.IO.FileStream.CanWrite" /> dell'oggetto FileStream. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="access" /> non è un campo di <see cref="T:System.IO.FileAccess" />. </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O, ad esempio un errore su disco.-oppure-Il flusso è stato chiuso. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il parametro <paramref name="access" /> richiesto non è consentito dal sistema operativo per l'handle di file specificato, ad esempio quando <paramref name="access" /> è Write o ReadWrite e l'handle di file è impostato per l'accesso in sola lettura. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(Microsoft.Win32.SafeHandles.SafeFileHandle,System.IO.FileAccess,System.Int32)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.IO.FileStream" /> per l'handle di file specificato, con l'autorizzazione di lettura/scrittura e la dimensione del buffer specificate.</summary>
      <param name="handle">Handle di file relativo al file che sarà incapsulato nell'oggetto FileStream corrente. </param>
      <param name="access">Costante <see cref="T:System.IO.FileAccess" /> che imposta le proprietà <see cref="P:System.IO.FileStream.CanRead" /> e <see cref="P:System.IO.FileStream.CanWrite" /> dell'oggetto FileStream. </param>
      <param name="bufferSize">Valore positivo <see cref="T:System.Int32" /> maggiore di 0 che indica la dimensione del buffer.La dimensione del buffer predefinita è 4096.</param>
      <exception cref="T:System.ArgumentException">Il parametro <paramref name="handle" /> è di un formato non valido.-oppure-Il parametro <paramref name="handle" /> rappresenta un handle sincrono che è stato utilizzato in modo asincrono. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Il parametro <paramref name="bufferSize" /> è negativo. </exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O, ad esempio un errore su disco.-oppure-Il flusso è stato chiuso.  </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il parametro <paramref name="access" /> richiesto non è consentito dal sistema operativo per l'handle di file specificato, ad esempio quando <paramref name="access" /> è Write o ReadWrite e l'handle di file è impostato per l'accesso in sola lettura. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(Microsoft.Win32.SafeHandles.SafeFileHandle,System.IO.FileAccess,System.Int32,System.Boolean)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.IO.FileStream" /> per l'handle di file specificato, con l'autorizzazione di lettura/scrittura, la dimensione del buffer e lo stato sincrono o asincrono specificati.</summary>
      <param name="handle">Handle di file relativo al file che sarà incapsulato nell'oggetto FileStream. </param>
      <param name="access">Costante che imposta le proprietà <see cref="P:System.IO.FileStream.CanRead" /> e <see cref="P:System.IO.FileStream.CanWrite" /> dell'oggetto FileStream. </param>
      <param name="bufferSize">Valore positivo <see cref="T:System.Int32" /> maggiore di 0 che indica la dimensione del buffer.La dimensione del buffer predefinita è 4096.</param>
      <param name="isAsync">true se l'handle è stato aperto in modalità asincrona, ovvero in modalità I/O sovrapposta; in caso contrario, false. </param>
      <exception cref="T:System.ArgumentException">Il parametro <paramref name="handle" /> è di un formato non valido.-oppure-Il parametro <paramref name="handle" /> rappresenta un handle sincrono che è stato utilizzato in modo asincrono. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Il parametro <paramref name="bufferSize" /> è negativo. </exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O, ad esempio un errore su disco.-oppure-Il flusso è stato chiuso.  </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il parametro <paramref name="access" /> richiesto non è consentito dal sistema operativo per l'handle di file specificato, ad esempio quando <paramref name="access" /> è Write o ReadWrite e l'handle di file è impostato per l'accesso in sola lettura. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.IO.FileStream" /> con il percorso e la modalità di creazione specificati.</summary>
      <param name="path">Percorso relativo o assoluto per il file che sarà incapsulato dall'oggetto FileStream corrente. </param>
      <param name="mode">Costante che determina la modalità di apertura o di creazione del file. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa vuota (""), contiene solo spazi oppure contiene uno o più caratteri non validi. -oppure-<paramref name="path" /> fa riferimento a dispositivi non basati su file come "con:", "com1:", "lpt1:" e così via.in un ambiente NTFS.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> fa riferimento a dispositivi non basati su file come "con:", "com1:", "lpt1:" e così via.in un ambiente non NTFS.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Impossibile trovare il file, ad esempio quando <paramref name="mode" /> è FileMode.Truncate o FileMode.Open e il file specificato da <paramref name="path" /> non è disponibile.È necessario che il file sia già disponibile in queste modalità.</exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O, ad esempio quando si specifica FileMode.CreateNew e il file specificato da <paramref name="path" /> è già presente.-oppure-Il flusso è stato chiuso. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido, poiché, ad esempio, si trova su un'unità non connessa. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> contiene un valore non valido. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.IO.FileStream" /> con il percorso, la modalità di creazione e l'autorizzazione di lettura/scrittura specificati.</summary>
      <param name="path">Percorso relativo o assoluto per il file che sarà incapsulato dall'oggetto FileStream corrente. </param>
      <param name="mode">Costante che determina la modalità di apertura o di creazione del file. </param>
      <param name="access">Costante che determina la modalità di accesso al file da parte dell'oggetto FileStream.Determina anche i valori restituiti dalle proprietà <see cref="P:System.IO.FileStream.CanRead" /> e <see cref="P:System.IO.FileStream.CanWrite" /> dell'oggetto FileStream.<see cref="P:System.IO.FileStream.CanSeek" /> è true se <paramref name="path" /> specifica un file su disco.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa vuota (""), contiene solo spazi oppure contiene uno o più caratteri non validi. -oppure-<paramref name="path" /> fa riferimento a dispositivi non basati su file come "con:", "com1:", "lpt1:" e così via.in un ambiente NTFS.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> fa riferimento a dispositivi non basati su file come "con:", "com1:", "lpt1:" e così via.in un ambiente non NTFS.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Impossibile trovare il file, ad esempio quando <paramref name="mode" /> è FileMode.Truncate o FileMode.Open e il file specificato da <paramref name="path" /> non è disponibile.È necessario che il file sia già disponibile in queste modalità.</exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O, ad esempio quando si specifica FileMode.CreateNew e il file specificato da <paramref name="path" /> è già presente. -oppure-Il flusso è stato chiuso.</exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido, poiché, ad esempio, si trova su un'unità non connessa. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il parametro <paramref name="access" /> richiesto non è consentito dal sistema operativo per il parametro <paramref name="path" /> specificato, ad esempio quando <paramref name="access" /> è Write o ReadWrite e il file o la directory è impostata per l'accesso in sola lettura. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> contiene un valore non valido. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.IO.FileStream" /> con il percorso, la modalità di creazione, l'autorizzazione di lettura/scrittura e l'autorizzazione di condivisione specificati.</summary>
      <param name="path">Percorso relativo o assoluto per il file che sarà incapsulato dall'oggetto FileStream corrente. </param>
      <param name="mode">Costante che determina la modalità di apertura o di creazione del file. </param>
      <param name="access">Costante che determina la modalità di accesso al file da parte dell'oggetto FileStream.Determina anche i valori restituiti dalle proprietà <see cref="P:System.IO.FileStream.CanRead" /> e <see cref="P:System.IO.FileStream.CanWrite" /> dell'oggetto FileStream.<see cref="P:System.IO.FileStream.CanSeek" /> è true se <paramref name="path" /> specifica un file su disco.</param>
      <param name="share">Costante che determina la modalità di condivisione del file da parte dei processi. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa vuota (""), contiene solo spazi oppure contiene uno o più caratteri non validi. -oppure-<paramref name="path" /> fa riferimento a dispositivi non basati su file come "con:", "com1:", "lpt1:" e così via.in un ambiente NTFS.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> fa riferimento a dispositivi non basati su file come "con:", "com1:", "lpt1:" e così via.in un ambiente non NTFS.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Impossibile trovare il file, ad esempio quando <paramref name="mode" /> è FileMode.Truncate o FileMode.Open e il file specificato da <paramref name="path" /> non è disponibile.È necessario che il file sia già disponibile in queste modalità.</exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O, ad esempio quando si specifica FileMode.CreateNew e il file specificato da <paramref name="path" /> è già presente. -oppure-Sul sistema è in esecuzione Windows 98 o Windows 98 Second Edition e <paramref name="share" /> è impostata su FileShare.Delete.-oppure-Il flusso è stato chiuso.</exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido, poiché, ad esempio, si trova su un'unità non connessa. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il parametro <paramref name="access" /> richiesto non è consentito dal sistema operativo per il parametro <paramref name="path" /> specificato, ad esempio quando <paramref name="access" /> è Write o ReadWrite e il file o la directory è impostata per l'accesso in sola lettura. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> contiene un valore non valido. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Int32)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.IO.FileStream" /> con il percorso, la modalità di creazione, l'autorizzazione di lettura/scrittura e condivisione e la dimensione del buffer specificati.</summary>
      <param name="path">Percorso relativo o assoluto per il file che sarà incapsulato dall'oggetto FileStream corrente. </param>
      <param name="mode">Costante che determina la modalità di apertura o di creazione del file. </param>
      <param name="access">Costante che determina la modalità di accesso al file da parte dell'oggetto FileStream.Determina anche i valori restituiti dalle proprietà <see cref="P:System.IO.FileStream.CanRead" /> e <see cref="P:System.IO.FileStream.CanWrite" /> dell'oggetto FileStream.<see cref="P:System.IO.FileStream.CanSeek" /> è true se <paramref name="path" /> specifica un file su disco.</param>
      <param name="share">Costante che determina la modalità di condivisione del file da parte dei processi. </param>
      <param name="bufferSize">Valore positivo <see cref="T:System.Int32" /> maggiore di 0 che indica la dimensione del buffer.La dimensione del buffer predefinita è 4096.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa vuota (""), contiene solo spazi oppure contiene uno o più caratteri non validi. -oppure-<paramref name="path" /> fa riferimento a dispositivi non basati su file come "con:", "com1:", "lpt1:" e così via.in un ambiente NTFS.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> fa riferimento a dispositivi non basati su file come "con:", "com1:", "lpt1:" e così via.in un ambiente non NTFS.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> è un valore negativo o zero.-oppure- <paramref name="mode" />, <paramref name="access" /> o <paramref name="share" /> contengono un valore non valido. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Impossibile trovare il file, ad esempio quando <paramref name="mode" /> è FileMode.Truncate o FileMode.Open e il file specificato da <paramref name="path" /> non è disponibile.È necessario che il file sia già disponibile in queste modalità.</exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O, ad esempio quando si specifica FileMode.CreateNew e il file specificato da <paramref name="path" /> è già presente. -oppure-Sul sistema è in esecuzione Windows 98 o Windows 98 Second Edition e <paramref name="share" /> è impostata su FileShare.Delete.-oppure-Il flusso è stato chiuso.</exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido, poiché, ad esempio, si trova su un'unità non connessa. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il parametro <paramref name="access" /> richiesto non è consentito dal sistema operativo per il parametro <paramref name="path" /> specificato, ad esempio quando <paramref name="access" /> è Write o ReadWrite e il file o la directory è impostata per l'accesso in sola lettura. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Int32,System.Boolean)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.IO.FileStream" /> con il percorso, la modalità di creazione, l'autorizzazione di lettura/scrittura e condivisione, la dimensione del buffer e lo stato sincrono o asincrono specificati.</summary>
      <param name="path">Percorso relativo o assoluto per il file che sarà incapsulato dall'oggetto FileStream corrente. </param>
      <param name="mode">Costante che determina la modalità di apertura o di creazione del file. </param>
      <param name="access">Costante che determina la modalità di accesso al file da parte dell'oggetto FileStream.Determina anche i valori restituiti dalle proprietà <see cref="P:System.IO.FileStream.CanRead" /> e <see cref="P:System.IO.FileStream.CanWrite" /> dell'oggetto FileStream.<see cref="P:System.IO.FileStream.CanSeek" /> è true se <paramref name="path" /> specifica un file su disco.</param>
      <param name="share">Costante che determina la modalità di condivisione del file da parte dei processi. </param>
      <param name="bufferSize">Valore positivo <see cref="T:System.Int32" /> maggiore di 0 che indica la dimensione del buffer.La dimensione del buffer predefinita è 4096.</param>
      <param name="useAsync">Specifica se usare I/O asincroni o sincroni.Tenere presente, tuttavia, che il sistema operativo usato potrebbe non supportare le operazioni di I/O asincrone. Quindi, se si specifica true, l'handle potrebbe essere aperto in modalità sincrona a seconda della piattaforma usata.Quando vengono aperti in modalità asincrona, i metodi <see cref="M:System.IO.FileStream.BeginRead(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)" /> e <see cref="M:System.IO.FileStream.BeginWrite(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)" /> offrono prestazioni migliori nelle operazioni di lettura o scrittura su dati di grandi dimensioni, ma potrebbero offrire prestazioni inferiori nelle operazioni di lettura o scrittura su dati di piccole dimensioni.Se l'applicazione è stata progettata per sfruttare le operazioni di I/O asincrone, impostare il parametro <paramref name="useAsync" /> su true.Se le operazioni di I/O asincrone vengono usate in modo corretto è possibile ottenere un incremento delle prestazioni delle applicazioni fino a 10 volte, ma se vengono usate senza riprogettare l'applicazione per le operazioni di I/O asincrone le prestazioni possono ridursi fino a 10 volte.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa vuota (""), contiene solo spazi oppure contiene uno o più caratteri non validi. -oppure-<paramref name="path" /> fa riferimento a dispositivi non basati su file come "con:", "com1:", "lpt1:" e così via.in un ambiente NTFS.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> fa riferimento a dispositivi non basati su file come "con:", "com1:", "lpt1:" e così via.in un ambiente non NTFS.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> è un valore negativo o zero.-oppure- <paramref name="mode" />, <paramref name="access" /> o <paramref name="share" /> contengono un valore non valido. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Impossibile trovare il file, ad esempio quando <paramref name="mode" /> è FileMode.Truncate o FileMode.Open e il file specificato da <paramref name="path" /> non è disponibile.È necessario che il file sia già disponibile in queste modalità.</exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O, ad esempio quando si specifica FileMode.CreateNew e il file specificato da <paramref name="path" /> è già presente.-oppure- Sul sistema è in esecuzione Windows 98 o Windows 98 Second Edition e <paramref name="share" /> è impostata su FileShare.Delete.-oppure-Il flusso è stato chiuso.</exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido, poiché, ad esempio, si trova su un'unità non connessa. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il parametro <paramref name="access" /> richiesto non è consentito dal sistema operativo per il parametro <paramref name="path" /> specificato, ad esempio quando <paramref name="access" /> è Write o ReadWrite e il file o la directory è impostata per l'accesso in sola lettura. </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Int32,System.IO.FileOptions)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.IO.FileStream" /> con il percorso, la modalità di creazione, l'autorizzazione di lettura/scrittura e condivisione, l'accesso consentito ad altri FileStream allo stesso file, la dimensione del buffer e le opzioni aggiuntive del file specificati.</summary>
      <param name="path">Percorso relativo o assoluto per il file che sarà incapsulato dall'oggetto FileStream corrente. </param>
      <param name="mode">Costante che determina la modalità di apertura o di creazione del file. </param>
      <param name="access">Costante che determina la modalità di accesso al file da parte dell'oggetto FileStream.Determina anche i valori restituiti dalle proprietà <see cref="P:System.IO.FileStream.CanRead" /> e <see cref="P:System.IO.FileStream.CanWrite" /> dell'oggetto FileStream.<see cref="P:System.IO.FileStream.CanSeek" /> è true se <paramref name="path" /> specifica un file su disco.</param>
      <param name="share">Costante che determina la modalità di condivisione del file da parte dei processi. </param>
      <param name="bufferSize">Valore positivo <see cref="T:System.Int32" /> maggiore di 0 che indica la dimensione del buffer.La dimensione del buffer predefinita è 4096.</param>
      <param name="options">Valore che specifica le opzioni aggiuntive del file.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa vuota (""), contiene solo spazi oppure contiene uno o più caratteri non validi. -oppure-<paramref name="path" /> fa riferimento a dispositivi non basati su file come "con:", "com1:", "lpt1:" e così via.in un ambiente NTFS.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> fa riferimento a dispositivi non basati su file come "con:", "com1:", "lpt1:" e così via.in un ambiente non NTFS.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> è un valore negativo o zero.-oppure- <paramref name="mode" />, <paramref name="access" /> o <paramref name="share" /> contengono un valore non valido. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Impossibile trovare il file, ad esempio quando <paramref name="mode" /> è FileMode.Truncate o FileMode.Open e il file specificato da <paramref name="path" /> non è disponibile.È necessario che il file sia già disponibile in queste modalità.</exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O, ad esempio quando si specifica FileMode.CreateNew e il file specificato da <paramref name="path" /> è già presente.-oppure-Il flusso è stato chiuso.</exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido, poiché, ad esempio, si trova su un'unità non connessa. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Il parametro <paramref name="access" /> richiesto non è consentito dal sistema operativo per il parametro <paramref name="path" /> specificato, ad esempio quando <paramref name="access" /> è Write o ReadWrite e il file o la directory è impostata per l'accesso in sola lettura. -oppure-<see cref="F:System.IO.FileOptions.Encrypted" /> viene specificato per <paramref name="options" />, ma la crittografia del file non è supportata sulla piattaforma corrente.</exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
    </member>
    <member name="P:System.IO.FileStream.CanRead">
      <summary>Ottiene un valore che indica se il flusso corrente supporta la lettura.</summary>
      <returns>true se il flusso supporta la lettura; false se il flusso è chiuso o è stato aperto con accesso in sola scrittura.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.CanSeek">
      <summary>Ottiene un valore che indica se il flusso corrente supporta la ricerca.</summary>
      <returns>true se il flusso supporta la ricerca; false se il flusso viene chiuso o se FileStream è stato costruito da un handle del sistema operativo, ad esempio un pipe o un output di console.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.CanWrite">
      <summary>Ottiene un valore che indica se il flusso corrente supporta la scrittura.</summary>
      <returns>true se il flusso supporta la scrittura; false se il flusso è chiuso o è stato aperto con accesso in sola lettura.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Dispose(System.Boolean)">
      <summary>Rilascia le risorse non gestite usate da <see cref="T:System.IO.FileStream" /> e, facoltativamente, le risorse gestite.</summary>
      <param name="disposing">true per rilasciare sia le risorse gestite sia quelle non gestite; false per rilasciare solo le risorse non gestite. </param>
    </member>
    <member name="M:System.IO.FileStream.Finalize">
      <summary>Garantisce che le risorse vengano liberate e le altre operazioni di pulizia vengano completate quando l'oggetto FileStream viene recuperato da Garbage Collector.</summary>
    </member>
    <member name="M:System.IO.FileStream.Flush">
      <summary>Cancella i buffer del flusso e fa sì che i dati memorizzati nel buffer vengano scritti nel file.</summary>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O. </exception>
      <exception cref="T:System.ObjectDisposedException">Il flusso è chiuso. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Flush(System.Boolean)">
      <summary>Cancella i buffer del flusso e fa sì che i dati memorizzati nei buffer vengano scritti nel file, cancellando anche tutti i buffer di file intermedi.</summary>
      <param name="flushToDisk">true per svuotare tutti i buffer di file intermedi; in caso contrario, false. </param>
    </member>
    <member name="M:System.IO.FileStream.FlushAsync(System.Threading.CancellationToken)">
      <summary>Cancella in modo asincrono i dati di tutti i buffer del flusso, determina la scrittura dei dati memorizzati nel buffer nel dispositivo sottostante e monitora le richieste di annullamento. </summary>
      <returns>Attività che rappresenta l'operazione di scaricamento asincrona. </returns>
      <param name="cancellationToken">Token da monitorare per le richieste di annullamento.</param>
      <exception cref="T:System.ObjectDisposedException">Il flusso è stato eliminato.</exception>
    </member>
    <member name="P:System.IO.FileStream.IsAsync">
      <summary>Ottiene un valore che indica se FileStream è stato aperto in modalità sincrona o asincrona.</summary>
      <returns>true se FileStream è stato aperto in modalità asincrona; in caso contrario, false.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.Length">
      <summary>Ottiene la lunghezza in byte del flusso.</summary>
      <returns>Valore long che rappresenta la lunghezza del flusso in byte.</returns>
      <exception cref="T:System.NotSupportedException">
        <see cref="P:System.IO.FileStream.CanSeek" /> per questo flusso è false. </exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O, come ad esempio la chiusura del file. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.Name">
      <summary>Ottiene il nome dell'oggetto FileStream passato al costruttore.</summary>
      <returns>Stringa che rappresenta il nome dell'oggetto FileStream.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileStream.Position">
      <summary>Ottiene o imposta la posizione corrente del flusso.</summary>
      <returns>Posizione corrente del flusso.</returns>
      <exception cref="T:System.NotSupportedException">Il flusso non supporta la ricerca. </exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O. -oppure-La posizione è stata impostata a un valore molto alto oltre la fine del flusso in Windows 98 o versioni precedenti.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Si è tentato di impostare la posizione su un valore negativo. </exception>
      <exception cref="T:System.IO.EndOfStreamException">Si è tentato di effettuare la ricerca oltre la fine di un flusso che non supporta tale operazione. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>Legge un blocco di byte dal flusso e scrive i dati in un determinato buffer.</summary>
      <returns>Numero complessivo di byte letti nel buffer.Può essere inferiore al numero dei byte richiesti se tale numero di byte non è disponibile oppure pari a zero se è stata raggiunta la fine del flusso.</returns>
      <param name="array">Quando questo metodo viene restituito, contiene la matrice di byte specificata in cui i valori compresi tra <paramref name="offset" /> e (<paramref name="offset" /> + <paramref name="count" /> - 1<paramref name=")" /> sono sostituiti dai byte letti dall'origine corrente. </param>
      <param name="offset">Offset dei byte in <paramref name="array" /> in corrispondenza del quale cui verranno inseriti i byte letti. </param>
      <param name="count">Numero massimo di byte da leggere. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Il valore di <paramref name="offset" /> o <paramref name="count" /> è negativo. </exception>
      <exception cref="T:System.NotSupportedException">Il flusso non supporta la lettura. </exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> e <paramref name="count" /> descrivono un intervallo non valido in <paramref name="array" />. </exception>
      <exception cref="T:System.ObjectDisposedException">Sono stati chiamati dei metodi dopo la chiusura del flusso. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.ReadAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>Legge in modo asincrono una sequenza di byte dal flusso corrente e passa alla posizione successiva all'interno del flusso corrente in base al numero di byte letti e monitora le richieste di annullamento.</summary>
      <returns>Attività che rappresenta l'operazione di lettura asincrona.Il valore del parametro di <paramref name="TResult" /> contiene il numero totale di byte letti nel buffer.Il valore del risultato può essere minore del numero di byte richiesti se il numero di byte attualmente disponibili è minore di quelli richiesti o può essere pari a 0 (zero) se è stata raggiunta la fine del flusso.</returns>
      <param name="buffer">Buffer in cui scrivere i dati.</param>
      <param name="offset">Offset dei byte in <paramref name="buffer" /> da cui iniziare la scrittura dei dati dal flusso.</param>
      <param name="count">Numero massimo di byte da leggere.</param>
      <param name="cancellationToken">Token da monitorare per le richieste di annullamento.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Il valore di <paramref name="offset" /> o <paramref name="count" /> è negativo.</exception>
      <exception cref="T:System.ArgumentException">La somma di <paramref name="offset" /> e <paramref name="count" /> è maggiore della lunghezza del buffer.</exception>
      <exception cref="T:System.NotSupportedException">Il flusso non supporta la lettura.</exception>
      <exception cref="T:System.ObjectDisposedException">Il flusso è stato eliminato.</exception>
      <exception cref="T:System.InvalidOperationException">Il flusso è al momento utilizzato da un'operazione di lettura precedente. </exception>
    </member>
    <member name="M:System.IO.FileStream.ReadByte">
      <summary>Legge un byte dal file e sposta in avanti di un byte la posizione di lettura.</summary>
      <returns>Byte, di cui è stato eseguito il cast a un oggetto <see cref="T:System.Int32" />, oppure -1 se è stata raggiunta la fine del flusso.</returns>
      <exception cref="T:System.NotSupportedException">Il flusso corrente non supporta la lettura. </exception>
      <exception cref="T:System.ObjectDisposedException">Il flusso corrente è chiuso. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.SafeFileHandle">
      <summary>Ottiene un oggetto <see cref="T:Microsoft.Win32.SafeHandles.SafeFileHandle" /> che rappresenta l'handle di file del sistema operativo per il file incapsulato dall'oggetto <see cref="T:System.IO.FileStream" /> corrente.</summary>
      <returns>Oggetto che rappresenta l'handle di file del sistema operativo per il file incapsulato dall'oggetto <see cref="T:System.IO.FileStream" /> corrente.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Seek(System.Int64,System.IO.SeekOrigin)">
      <summary>Imposta la posizione corrente del flusso sul valore dato.</summary>
      <returns>Nuova posizione all'interno del flusso.</returns>
      <param name="offset">Punto relativo a <paramref name="origin" /> da cui avviare la ricerca. </param>
      <param name="origin">Specifica l'inizio, la fine o la posizione corrente come punto di riferimento per <paramref name="offset" />, usando un valore di tipo <see cref="T:System.IO.SeekOrigin" />. </param>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O. </exception>
      <exception cref="T:System.NotSupportedException">Il flusso non supporta la ricerca, come nel caso in cui l'oggetto FileStream venga costruito da un pipe o da un output di console. </exception>
      <exception cref="T:System.ArgumentException">La ricerca viene tentata prima dell'inizio del flusso. </exception>
      <exception cref="T:System.ObjectDisposedException">Sono stati chiamati dei metodi dopo la chiusura del flusso. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.SetLength(System.Int64)">
      <summary>Imposta la lunghezza del flusso sul valore dato.</summary>
      <param name="value">Nuova lunghezza del flusso. </param>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O. </exception>
      <exception cref="T:System.NotSupportedException">Il flusso non supporta né la scrittura né la ricerca. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Si è tentato di impostare il parametro <paramref name="value" /> su un valore minore di 0. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>Scrive un blocco di byte nel flusso di file.</summary>
      <param name="array">Buffer contenente i dati da scrivere nel flusso.</param>
      <param name="offset">Offset dei byte in base zero in <paramref name="array" /> da cui iniziare la copia dei byte nel flusso. </param>
      <param name="count">Numero massimo di byte da scrivere. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> e <paramref name="count" /> descrivono un intervallo non valido in <paramref name="array" />. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Il valore di <paramref name="offset" /> o <paramref name="count" /> è negativo. </exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore di I/O. -oppure-Un altro thread può aver causato una modifica imprevista della posizione dell'handle di file del sistema operativo. </exception>
      <exception cref="T:System.ObjectDisposedException">Il flusso è chiuso. </exception>
      <exception cref="T:System.NotSupportedException">L'istanza del flusso corrente non supporta la scrittura. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.WriteAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>Scrive in modo asincrono una sequenza di byte nel flusso corrente e passa alla posizione successiva all'interno del flusso corrente in base al numero di byte scritti e monitora le richieste di annullamento. </summary>
      <returns>Attività che rappresenta l'operazione di scrittura asincrona.</returns>
      <param name="buffer">Buffer da cui scrivere i dati. </param>
      <param name="offset">Offset dei byte in base zero in <paramref name="buffer" /> da cui iniziare la copia dei byte nel flusso.</param>
      <param name="count">Numero massimo di byte da scrivere.</param>
      <param name="cancellationToken">Token da monitorare per le richieste di annullamento.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Il valore di <paramref name="offset" /> o <paramref name="count" /> è negativo.</exception>
      <exception cref="T:System.ArgumentException">La somma di <paramref name="offset" /> e <paramref name="count" /> è maggiore della lunghezza del buffer.</exception>
      <exception cref="T:System.NotSupportedException">Il flusso non supporta la scrittura.</exception>
      <exception cref="T:System.ObjectDisposedException">Il flusso è stato eliminato.</exception>
      <exception cref="T:System.InvalidOperationException">Il flusso è al momento utilizzato da un'operazione di scrittura precedente. </exception>
    </member>
    <member name="M:System.IO.FileStream.WriteByte(System.Byte)">
      <summary>Scrive un byte nella posizione corrente all'interno del flusso di file.</summary>
      <param name="value">Byte da scrivere nel flusso. </param>
      <exception cref="T:System.ObjectDisposedException">Il flusso è chiuso. </exception>
      <exception cref="T:System.NotSupportedException">Il flusso non supporta la scrittura. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.FileSystemInfo">
      <summary>Fornisce la classe base per gli oggetti <see cref="T:System.IO.FileInfo" /> e <see cref="T:System.IO.DirectoryInfo" />.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.FileSystemInfo.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.IO.FileSystemInfo" />.</summary>
    </member>
    <member name="P:System.IO.FileSystemInfo.Attributes">
      <summary>Ottiene o imposta gli attributi per il file o la directory corrente.</summary>
      <returns>
        <see cref="T:System.IO.FileAttributes" /> della classe <see cref="T:System.IO.FileSystemInfo" /> corrente.</returns>
      <exception cref="T:System.IO.FileNotFoundException">Il file specificato non esiste. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido, ad esempio, si trova in un'unità non mappata. </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <exception cref="T:System.ArgumentException">Il chiamante tenta di impostare un attributo di file non valido. -oppure-Tentativi dell'utente di impostare un valore di attributo senza che sia disponibile l'autorizzazione di scrittura.</exception>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> non è in grado di inizializzare i dati. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.CreationTime">
      <summary>Ottiene o imposta l'ora di creazione del file o della directory corrente.</summary>
      <returns>Data e ora di creazione dell'oggetto <see cref="T:System.IO.FileSystemInfo" /> corrente.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> non è in grado di inizializzare i dati. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido, ad esempio, si trova in un'unità non mappata.</exception>
      <exception cref="T:System.PlatformNotSupportedException">Il sistema operativo corrente non è Windows NT o versioni successive.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Il chiamante tenta di impostare un'ora di creazione non valida.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.CreationTimeUtc">
      <summary>Ottiene o imposta l'ora di creazione, nell'ora UTC (Coordinated Universal Time) del file o della directory corrente.</summary>
      <returns>Data e ora di creazione in formato UTC dell'oggetto <see cref="T:System.IO.FileSystemInfo" /> corrente.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> non è in grado di inizializzare i dati. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido, ad esempio, si trova in un'unità non mappata.</exception>
      <exception cref="T:System.PlatformNotSupportedException">Il sistema operativo corrente non è Windows NT o versioni successive.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Il chiamante tenta di impostare un'ora di accesso non valida.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileSystemInfo.Delete">
      <summary>Elimina un file o una directory.</summary>
      <exception cref="T:System.IO.DirectoryNotFoundException">Il percorso specificato non è valido, ad esempio, si trova in un'unità non mappata.</exception>
      <exception cref="T:System.IO.IOException">Handle aperto sulla directory o sul file e il sistema operativo è Windows XP o versione precedente.Questo handle aperto può derivare da file e directory di enumerazione.Per altre informazioni, vedere Procedura: enumerare directory e file.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.FileSystemInfo.Exists">
      <summary>Ottiene un valore che indica se il file o la directory esiste.</summary>
      <returns>true se il file o la directory esiste; in caso contrario, false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileSystemInfo.Extension">
      <summary>Ottiene la stringa che rappresenta l'estensione del file.</summary>
      <returns>Stringa contenente l'estensione dell'oggetto <see cref="T:System.IO.FileSystemInfo" />.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileSystemInfo.FullName">
      <summary>Ottiene il percorso completo del file o della directory.</summary>
      <returns>Stringa contenente il percorso completo.</returns>
      <exception cref="T:System.IO.PathTooLongException">Il percorso completo e il nome file sono costituiti da 260 o più caratteri.</exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone dell'autorizzazione richiesta. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="F:System.IO.FileSystemInfo.FullPath">
      <summary>Rappresenta il percorso completo del file o della directory.</summary>
      <exception cref="T:System.IO.PathTooLongException">Il percorso completo è costituito da 260 o più caratteri.</exception>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastAccessTime">
      <summary>Ottiene o imposta l'ora in cui è avvenuto l'ultimo accesso al file o alla directory corrente.</summary>
      <returns>Ora in cui è avvenuto l'ultimo accesso al file o alla directory corrente.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> non è in grado di inizializzare i dati. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Il sistema operativo corrente non è Windows NT o versioni successive.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Il chiamante tenta di impostare un'ora di accesso non valida</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastAccessTimeUtc">
      <summary>Ottiene o imposta l'ora, nell'ora UTC (Universal Coordinated Time), dell'ultimo accesso al file o alla directory corrente.</summary>
      <returns>Ora UTC in cui è avvenuto l'ultimo accesso al file o alla directory corrente.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> non è in grado di inizializzare i dati. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Il sistema operativo corrente non è Windows NT o versioni successive.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Il chiamante tenta di impostare un'ora di accesso non valida.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastWriteTime">
      <summary>Ottiene o imposta l'ora dell'ultima modifica del file o della directory corrente.</summary>
      <returns>Ora dell'ultima modifica del file o della directory corrente.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> non è in grado di inizializzare i dati. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Il sistema operativo corrente non è Windows NT o versioni successive.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Il chiamante tenta di impostare un'ora di scrittura non valida.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastWriteTimeUtc">
      <summary>Ottiene o imposta l'ora, nell'ora UTC (Universal Coordinated Time), dell'ultima scrittura al file o alla directory corrente.</summary>
      <returns>Ora UTC dell'ultima modifica nel file corrente.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> non è in grado di inizializzare i dati. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Il sistema operativo corrente non è Windows NT o versioni successive.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Il chiamante tenta di impostare un'ora di scrittura non valida.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.Name">
      <summary>Nel caso di file, ottiene il nome del file.Nel caso di directory, ottiene il nome dell'ultima directory nella gerarchia, se esiste una gerarchia.In caso contrario, la proprietà Name ottiene il nome della directory.</summary>
      <returns>Stringa che rappresenta il nome della directory padre, il nome dell'ultima directory nella gerarchia oppure il nome di un file, compresa l'estensione.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.FileSystemInfo.OriginalPath">
      <summary>Percorso, assoluto o relativo, originariamente specificato dall'utente.</summary>
    </member>
    <member name="M:System.IO.FileSystemInfo.Refresh">
      <summary>Aggiorna lo stato dell'oggetto.</summary>
      <exception cref="T:System.IO.IOException">Una periferica, ad esempio un'unità disco, non è pronta. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.SearchOption">
      <summary>Specifica se eseguire la ricerca solo nella directory corrente o anche nelle sottodirectory. </summary>
    </member>
    <member name="F:System.IO.SearchOption.AllDirectories">
      <summary>Include la directory corrente e tutte le relative sottodirectory nell'operazione di ricerca.Questa opzione include nella ricerca i punti di analisi come le unità montate e i collegamenti simbolici.</summary>
    </member>
    <member name="F:System.IO.SearchOption.TopDirectoryOnly">
      <summary>Include nell'operazione di ricerca solo la directory corrente.</summary>
    </member>
  </members>
</doc>