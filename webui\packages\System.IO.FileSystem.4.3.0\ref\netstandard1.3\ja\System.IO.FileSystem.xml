﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.IO.FileSystem</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Win32.SafeHandles.SafeFileHandle">
      <summary>ファイル ハンドルのラッパー クラスを表します。</summary>
    </member>
    <member name="M:Microsoft.Win32.SafeHandles.SafeFileHandle.#ctor(System.IntPtr,System.Boolean)">
      <summary>
        <see cref="T:Microsoft.Win32.SafeHandles.SafeFileHandle" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="preexistingHandle">使用する既存のハンドルを表す <see cref="T:System.IntPtr" /> オブジェクト。</param>
      <param name="ownsHandle">終了処理中にハンドルを安全に解放する場合は true。安全な解放を行わない場合は false (お勧めしません)。</param>
    </member>
    <member name="P:Microsoft.Win32.SafeHandles.SafeFileHandle.IsInvalid"></member>
    <member name="T:System.IO.Directory">
      <summary>ディレクトリやサブディレクトリを通じて、作成、移動、および列挙するための静的メソッドを公開します。このクラスは継承できません。この種類の .NET Framework ソース コードを参照して、次を参照してください。、参照ソースです。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Directory.CreateDirectory(System.String)">
      <summary>既に存在している場合以外は、指定したパスにすべてのディレクトリとサブディレクトリを作成します。</summary>
      <returns>指定したパスに存在するディレクトリを表すオブジェクト。指定したパスにおいてディレクトリが既に存在するかどうかにかかわりなく、このオブジェクトが返されます。</returns>
      <param name="path">作成するディレクトリ。</param>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> で指定したディレクトリはファイルです。またはネットワーク名がわかりません。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を 1 つ以上含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。または<paramref name="path" /> の先頭文字がコロン (:) か、またはコロン文字のみが含まれています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> に、ドライブのラベル ("C:\") の一部ではないコロン文字 (:) が含まれています。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.Delete(System.String)">
      <summary>指定したパスから空のディレクトリを削除します。</summary>
      <param name="path">削除する空のディレクトリの名前。このディレクトリは、書き込み可能で空である必要があります。</param>
      <exception cref="T:System.IO.IOException">名前と位置が <paramref name="path" /> で指定したのと同一のファイルが存在します。またはこのディレクトリは、アプリケーションの現在の作業ディレクトリです。または<paramref name="path" /> で指定したディレクトリは空ではありません。またはディレクトリが読み取り専用であるか、または読み取り専用ファイルを含んでいます。またはディレクトリは、別のプロセスによって使用されています。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を 1 つ以上含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> が存在しないか、または見つかりません。または指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.Delete(System.String,System.Boolean)">
      <summary>指定したディレクトリと、特に指定されている場合はディレクトリ内の任意のサブディレクトリおよびファイルを削除します。</summary>
      <param name="path">削除するディレクトリの名前。 </param>
      <param name="recursive">
        <paramref name="path" /> のディレクトリ、サブディレクトリ、およびファイルを削除する場合は true。それ以外の場合は false。</param>
      <exception cref="T:System.IO.IOException">名前と位置が <paramref name="path" /> で指定したのと同一のファイルが存在します。または<paramref name="path" /> で指定したディレクトリが読み取り専用か、または <paramref name="recursive" /> が false で、<paramref name="path" /> が空のディレクトリではありません。またはこのディレクトリは、アプリケーションの現在の作業ディレクトリです。またはディレクトリに読み取り専用のファイルが保存されています。またはディレクトリは、別のプロセスによって使用されています。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を 1 つ以上含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> が存在しないか、または見つかりません。または指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String)">
      <summary>指定されたパスに存在するディレクトリ名の列挙可能なコレクションを返します。</summary>
      <returns>
        <paramref name="path" /> で指定したディレクトリ内にあるディレクトリの完全名 (パスを含む) から成る列挙可能なコレクション。</returns>
      <param name="path">検索するディレクトリの相対パスまたは絶対パス。この文字列の大文字と小文字は区別されません。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> が無効です (割り当てられていないドライブを参照しているなど)。 </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> はファイル名です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその組み合わせが、システムで定義されている最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String,System.String)">
      <summary>指定されたパスから、検索パターンに一致するディレクトリ名の列挙可能なコレクションを返します。</summary>
      <returns>指定した検索パターンに一致し、<paramref name="path" /> で指定したディレクトリの中にあるディレクトリの完全名 (パスを含む) から成る列挙可能なコレクション。</returns>
      <param name="path">検索するディレクトリの相対パスまたは絶対パス。この文字列の大文字と小文字は区別されません。</param>
      <param name="searchPattern">
        <paramref name="path" /> 内のディレクトリの名前と照合する検索文字列。このパラメーターに、有効なリテラルのパスとワイルドカード (* と?) 文字の組み合わせを含めることができます (「解説」を参照) が、正規表現はサポートされていません。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。または<paramref name="searchPattern" /> に、有効なパターンが含まれていません。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。または<paramref name="searchPattern" /> は null です。 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> が無効です (割り当てられていないドライブを参照しているなど)。 </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> はファイル名です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその組み合わせが、システムで定義されている最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String,System.String,System.IO.SearchOption)">
      <summary>指定されたパスにあるディレクトリのうち、検索パターンに一致するディレクトリ名の列挙可能なコレクションを返します。オプションでサブディレクトリを検索対象にすることができます。</summary>
      <returns>指定した検索パターンおよびオプションに一致し、<paramref name="path" /> で指定したディレクトリの中にあるディレクトリの完全名 (パスを含む) から成る列挙可能なコレクション。</returns>
      <param name="path">検索するディレクトリの相対パスまたは絶対パス。この文字列の大文字と小文字は区別されません。</param>
      <param name="searchPattern">
        <paramref name="path" /> 内のディレクトリの名前と照合する検索文字列。このパラメーターに、有効なリテラルのパスとワイルドカード (* と?) 文字の組み合わせを含めることができます (「解説」を参照) が、正規表現はサポートされていません。</param>
      <param name="searchOption">検索操作に現在のディレクトリのみを含めるのか、またはすべてのサブディレクトリを含めるのかを指定する列挙値の 1 つ。既定値は <see cref="F:System.IO.SearchOption.TopDirectoryOnly" /> です。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。または<paramref name="searchPattern" /> に、有効なパターンが含まれていません。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。または<paramref name="searchPattern" /> は null です。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> が有効な <see cref="T:System.IO.SearchOption" /> 値ではありません。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> が無効です (割り当てられていないドライブを参照しているなど)。 </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> はファイル名です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその組み合わせが、システムで定義されている最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String)">
      <summary>指定されたパスに存在するファイル名の列挙可能なコレクションを返します。</summary>
      <returns>
        <paramref name="path" /> で指定したディレクトリ内にあるファイルの完全名 (パスを含む) から成る列挙可能なコレクション。</returns>
      <param name="path">検索するディレクトリの相対パスまたは絶対パス。この文字列の大文字と小文字は区別されません。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> が無効です (割り当てられていないドライブを参照しているなど)。 </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> はファイル名です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその組み合わせが、システムで定義されている最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String,System.String)">
      <summary>指定されたパスから、検索パターンに一致するファイル名の列挙可能なコレクションを返します。</summary>
      <returns>指定した検索パターンに一致し、<paramref name="path" /> で指定したディレクトリの中にあるファイルの完全名 (パスを含む) から成る列挙可能なコレクション。</returns>
      <param name="path">検索するディレクトリの相対パスまたは絶対パス。この文字列の大文字と小文字は区別されません。</param>
      <param name="searchPattern">
        <paramref name="path" /> 内のファイル名と対応させる検索文字列。このパラメーターに、有効なリテラルのパスとワイルドカード (* と?) 文字の組み合わせを含めることができます (「解説」を参照) が、正規表現はサポートされていません。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。または<paramref name="searchPattern" /> に、有効なパターンが含まれていません。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。または<paramref name="searchPattern" /> は null です。 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> が無効です (割り当てられていないドライブを参照しているなど)。 </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> はファイル名です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその組み合わせが、システムで定義されている最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String,System.String,System.IO.SearchOption)">
      <summary>指定されたパスにあるファイルのうち、検索パターンに一致するファイル名の列挙可能なコレクションを返します。オプションでサブディレクトリを検索対象にすることができます。</summary>
      <returns>指定した検索パターンおよびオプションに一致し、<paramref name="path" /> で指定したディレクトリの中にあるファイルの完全名 (パスを含む) から成る列挙可能なコレクション。</returns>
      <param name="path">検索するディレクトリの相対パスまたは絶対パス。この文字列の大文字と小文字は区別されません。</param>
      <param name="searchPattern">
        <paramref name="path" /> 内のファイル名と対応させる検索文字列。このパラメーターに、有効なリテラルのパスとワイルドカード (* と?) 文字の組み合わせを含めることができます (「解説」を参照) が、正規表現はサポートされていません。</param>
      <param name="searchOption">検索操作に現在のディレクトリのみを含めるのか、またはすべてのサブディレクトリを含めるのかを指定する列挙値の 1 つ。既定値は <see cref="F:System.IO.SearchOption.TopDirectoryOnly" /> です。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。または<paramref name="searchPattern" /> に、有効なパターンが含まれていません。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。または<paramref name="searchPattern" /> は null です。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> が有効な <see cref="T:System.IO.SearchOption" /> 値ではありません。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> が無効です (割り当てられていないドライブを参照しているなど)。 </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> はファイル名です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその組み合わせが、システムで定義されている最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String)">
      <summary>指定されたパスに存在するファイル名とディレクトリ名の列挙可能なコレクションを返します。</summary>
      <returns>
        <paramref name="path" /> で指定されたディレクトリ内のファイル システム エントリの列挙可能なコレクション。</returns>
      <param name="path">検索するディレクトリの相対パスまたは絶対パス。この文字列の大文字と小文字は区別されません。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> が無効です (割り当てられていないドライブを参照しているなど)。 </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> はファイル名です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその組み合わせが、システムで定義されている最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String,System.String)">
      <summary>指定されたパスから、検索パターンに一致するファイル名とディレクトリ名の列挙可能なコレクションを返します。</summary>
      <returns>
        <paramref name="path" /> で指定されたディレクトリに存在するファイル システム エントリのうち、指定した検索パターンに一致するファイル システム エントリの列挙可能なコレクション。</returns>
      <param name="path">検索するディレクトリの相対パスまたは絶対パス。この文字列の大文字と小文字は区別されません。</param>
      <param name="searchPattern">
        <paramref name="path" /> 内のファイル システム エントリの名前と照合する検索文字列。このパラメーターに、有効なリテラルのパスとワイルドカード (* と?) 文字の組み合わせを含めることができます (「解説」を参照) が、正規表現はサポートされていません。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。または<paramref name="searchPattern" /> に、有効なパターンが含まれていません。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。または<paramref name="searchPattern" /> は null です。 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> が無効です (割り当てられていないドライブを参照しているなど)。 </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> はファイル名です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその組み合わせが、システムで定義されている最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String,System.String,System.IO.SearchOption)">
      <summary>指定されたパスにあるディレクトリのうち、検索パターンに一致するファイル名およびディレクトリ名の列挙可能なコレクションを返します。オプションでサブディレクトリを検索対象にすることができます。</summary>
      <returns>
        <paramref name="path" /> で指定されたディレクトリに存在するファイル システム エントリのうち、指定した検索パターンおよびオプションに一致するファイル システム エントリの列挙可能なコレクション。</returns>
      <param name="path">検索するディレクトリの相対パスまたは絶対パス。この文字列の大文字と小文字は区別されません。</param>
      <param name="searchPattern">
        <paramref name="path" /> 内のファイル システム エントリと対応させる検索文字列。このパラメーターに、有効なリテラルのパスとワイルドカード (* と?) 文字の組み合わせを含めることができます (「解説」を参照) が、正規表現はサポートされていません。</param>
      <param name="searchOption">検索操作に現在のディレクトリのみを含めるのか、またはすべてのサブディレクトリを含めるのかを指定する列挙値の 1 つ。既定値は <see cref="F:System.IO.SearchOption.TopDirectoryOnly" /> です。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。または<paramref name="searchPattern" /> に、有効なパターンが含まれていません。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。または<paramref name="searchPattern" /> は null です。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> が有効な <see cref="T:System.IO.SearchOption" /> 値ではありません。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> が無効です (割り当てられていないドライブを参照しているなど)。 </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> はファイル名です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその組み合わせが、システムで定義されている最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
    </member>
    <member name="M:System.IO.Directory.Exists(System.String)">
      <summary>指定したパスがディスク上の既存のディレクトリを参照しているかどうかを確認します。</summary>
      <returns>
        <paramref name="path" /> が既存のディレクトリを参照している場合は true。ディレクトリが存在していない場合や、指定したファイルが存在するかどうかを判断しようとしたときにエラーが発生した場合は、false。</returns>
      <param name="path">テストするパス。 </param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetCreationTime(System.String)">
      <summary>ディレクトリの作成日時を取得します。</summary>
      <returns>指定したディレクトリの作成日時に設定された構造体。この値は現地時刻で表示されます。</returns>
      <param name="path">ディレクトリのパス。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を 1 つ以上含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetCreationTimeUtc(System.String)">
      <summary>世界協定時刻 (UTC: Coordinated Universal Time) 形式でのディレクトリの作成日時を取得します。</summary>
      <returns>指定したディレクトリの作成日時に設定された構造体。この値は UTC 時刻で表現されます。</returns>
      <param name="path">ディレクトリのパス。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を 1 つ以上含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetCurrentDirectory">
      <summary>アプリケーションの現在の作業ディレクトリを取得します。</summary>
      <returns>現在の作業ディレクトリのパスを示す文字列。この文字列の末尾には、円記号 (\) は付きません。</returns>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.NotSupportedException">オペレーティング システムが、"現在のディレクトリ" 機能を備えていない Windows CE です。このメソッドは .NET Compact Framework で使用できますが、現在はサポートされていません。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String)">
      <summary>指定したディレクトリ内のサブディレクトリの名前 (パスを含む) を返します。</summary>
      <returns>指定したパス内のサブディレクトリの完全名 (パスを含む) の配列。または、ディレクトリが見つからない場合は空の配列。</returns>
      <param name="path">検索するディレクトリの相対パスまたは絶対パス。この文字列の大文字と小文字は区別されません。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を 1 つ以上含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> はファイル名です。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String,System.String)">
      <summary>指定したディレクトリ内の指定した検索パターンに一致するサブディレクトリ名 (パスを含む) を返します。</summary>
      <returns>指定したディレクトリ内の検索パターンに一致するサブディレクトリの完全名 (パスを含む) の配列。または、ディレクトリが見つからない場合は空の配列。</returns>
      <param name="path">検索するディレクトリの相対パスまたは絶対パス。この文字列の大文字と小文字は区別されません。</param>
      <param name="searchPattern">
        <paramref name="path" /> 内のサブディレクトリの名前と照合する検索文字列。このパラメーターに、有効なリテラルのパスとワイルドカード文字の組み合わせを含めることができます (「解説」を参照) が、正規表現はサポートされていません。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を 1 つ以上含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> を使用します。または <paramref name="searchPattern" /> に、有効なパターンが含まれていません。 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> または <paramref name="searchPattern" /> が null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> はファイル名です。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String,System.String,System.IO.SearchOption)">
      <summary>現在のディレクトリ内で、指定された検索パターンに一致するサブディレクトリの名前 (パスを含む) を返します。オプションで、サブディレクトリを検索対象にすることができます。</summary>
      <returns>指定した条件に一致するサブディレクトリの完全名 (パスを含む) の配列。または、ディレクトリが見つからない場合は空の配列。</returns>
      <param name="path">検索するディレクトリの相対パスまたは絶対パス。この文字列の大文字と小文字は区別されません。</param>
      <param name="searchPattern">
        <paramref name="path" /> 内のサブディレクトリの名前と照合する検索文字列。このパラメーターに、有効なリテラルのパスとワイルドカード文字の組み合わせを含めることができます (「解説」を参照) が、正規表現はサポートされていません。</param>
      <param name="searchOption">検索操作にすべてのサブディレクトリを含めるのか、または現在のディレクトリのみを含めるのかを指定する列挙値の 1 つ。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を 1 つ以上含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。または <paramref name="searchPattern" /> に、有効なパターンが含まれていません。 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> または <paramref name="searchPattern" /> が null です。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> が有効な <see cref="T:System.IO.SearchOption" /> 値ではありません。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> はファイル名です。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
    </member>
    <member name="M:System.IO.Directory.GetDirectoryRoot(System.String)">
      <summary>指定したパスのボリューム情報またはルート情報、あるいはその両方を返します。</summary>
      <returns>指定したパスのボリューム情報またはルート情報、あるいはその両方を含む文字列。</returns>
      <param name="path">ファイルまたはディレクトリのパス。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を 1 つ以上含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> を使用します。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String)">
      <summary>指定したディレクトリ内のファイルの名前 (パスを含む) を返します。</summary>
      <returns>指定したディレクトリ内のファイルの完全名 (パスを含む) の配列。または、ファイルが見つからない場合は空の配列。</returns>
      <param name="path">検索するディレクトリの相対パスまたは絶対パス。この文字列の大文字と小文字は区別されません。</param>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> はファイル名です。またはネットワーク エラーが発生しました。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を 1 つ以上含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが見つからないか無効です (割り当てられていないドライブであるなど)。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String,System.String)">
      <summary>指定したディレクトリ内の指定した検索パターンに一致するファイル名 (パスを含む) を返します。</summary>
      <returns>指定したディレクトリ内の指定した検索パターンに一致するファイルの完全名 (パスを含む) の配列。または、ファイルが見つからない場合は空の配列。</returns>
      <param name="path">検索するディレクトリの相対パスまたは絶対パス。この文字列の大文字と小文字は区別されません。</param>
      <param name="searchPattern">
        <paramref name="path" /> 内のファイル名と対応させる検索文字列。このパラメーターに、有効なリテラルのパスとワイルドカード (* と?) 文字の組み合わせを含めることができます (「解説」を参照) が、正規表現はサポートされていません。</param>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> はファイル名です。またはネットワーク エラーが発生しました。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を 1 つ以上含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> を使用します。または <paramref name="searchPattern" /> に、有効なパターンが含まれていません。 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> または <paramref name="searchPattern" /> が null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが見つからないか無効です (割り当てられていないドライブであるなど)。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String,System.String,System.IO.SearchOption)">
      <summary>指定したディレクトリの中から、指定した検索パターンに一致し、サブディレクトリを検索するかどうかを決定する値を持つファイル名 (パスを含む) を返します。</summary>
      <returns>指定したディレクトリ内の指定した検索パターンおよびオプションに一致するファイルの完全名 (パスを含む) の配列。または、ファイルが見つからない場合は空の配列。</returns>
      <param name="path">検索するディレクトリの相対パスまたは絶対パス。この文字列の大文字と小文字は区別されません。</param>
      <param name="searchPattern">
        <paramref name="path" /> 内のファイル名と対応させる検索文字列。このパラメーターに、有効なリテラルのパスとワイルドカード (* と?) 文字の組み合わせを含めることができます (「解説」を参照) が、正規表現はサポートされていません。</param>
      <param name="searchOption">検索操作にすべてのサブディレクトリを含めるのか、または現在のディレクトリのみを含めるのかを指定する列挙値の 1 つ。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を 1 つ以上含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。または <paramref name="searchPattern" /> に、有効なパターンが含まれていません。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> または <paramref name="searchpattern" /> が null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> が有効な <see cref="T:System.IO.SearchOption" /> 値ではありません。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが見つからないか無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> はファイル名です。またはネットワーク エラーが発生しました。</exception>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String)">
      <summary>指定したパス内のすべてのファイル名とサブディレクトリ名を返します。</summary>
      <returns>指定したディレクトリ内ファイル名またはサブディレクトリ名の配列。ファイルやサブディレクトリが見つからない場合は、空の配列。</returns>
      <param name="path">検索するディレクトリの相対パスまたは絶対パス。この文字列の大文字と小文字は区別されません。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を 1 つ以上含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> を使用します。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> はファイル名です。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String,System.String)">
      <summary>指定されたパスから、検索パターンに一致するファイル名とディレクトリ名の配列を返します。</summary>
      <returns>指定した検索条件に一致するファイル名またはディレクトリ名の配列。ファイルやディレクトリが見つからない場合は、空の配列。</returns>
      <param name="path">検索するディレクトリの相対パスまたは絶対パス。この文字列の大文字と小文字は区別されません。</param>
      <param name="searchPattern">
        <paramref name="path" /> 内のファイルおよびディレクトリの名前と照合する検索文字列。このパラメーターに、有効なリテラルのパスとワイルドカード (* と?) 文字の組み合わせを含めることができます (「解説」を参照) が、正規表現はサポートされていません。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を 1 つ以上含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。または <paramref name="searchPattern" /> に、有効なパターンが含まれていません。 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> または <paramref name="searchPattern" /> が null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> はファイル名です。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String,System.String,System.IO.SearchOption)">
      <summary>指定されたパスにあるファイルおよびディレクトリのうち、検索パターンに一致するすべてのファイル名およびディレクトリ名の配列を返します。オプションでサブディレクトリを検索対象にすることができます。</summary>
      <returns>指定した検索条件に一致するファイル名またはディレクトリ名の配列。ファイルやディレクトリが見つからない場合は、空の配列。</returns>
      <param name="path">検索するディレクトリの相対パスまたは絶対パス。この文字列の大文字と小文字は区別されません。</param>
      <param name="searchPattern">
        <paramref name="path" /> 内のファイルおよびディレクトリの名前と照合する検索文字列。このパラメーターに、有効なリテラルのパスとワイルドカード (* と?) 文字の組み合わせを含めることができます (「解説」を参照) が、正規表現はサポートされていません。</param>
      <param name="searchOption">検索操作に現在のディレクトリのみを含めるのか、またはすべてのサブディレクトリを含めるのかを指定する列挙値の 1 つ。既定値は <see cref="F:System.IO.SearchOption.TopDirectoryOnly" /> です。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。または<paramref name="searchPattern" /> に、有効なパターンが含まれていません。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。または<paramref name="searchPattern" /> は null です。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> が有効な <see cref="T:System.IO.SearchOption" /> 値ではありません。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> が無効です (割り当てられていないドライブを参照しているなど)。 </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> はファイル名です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその組み合わせが、システムで定義されている最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
    </member>
    <member name="M:System.IO.Directory.GetLastAccessTime(System.String)">
      <summary>指定したファイルまたはディレクトリに最後にアクセスした日付と時刻を返します。</summary>
      <returns>指定したファイルまたはディレクトリに最後にアクセスした日時に設定された構造体。この値は現地時刻で表示されます。</returns>
      <param name="path">アクセス日時情報を取得する対象のファイルまたはディレクトリ。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を 1 つ以上含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> パラメーターの書式が無効です。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetLastAccessTimeUtc(System.String)">
      <summary>指定したファイルまたはディレクトリに最後にアクセスした日付と時刻を世界協定時刻 (UTC) 形式で返します。</summary>
      <returns>指定したファイルまたはディレクトリに最後にアクセスした日時に設定された構造体。この値は UTC 時刻で表現されます。</returns>
      <param name="path">アクセス日時情報を取得する対象のファイルまたはディレクトリ。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を 1 つ以上含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> パラメーターの書式が無効です。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetLastWriteTime(System.String)">
      <summary>指定したファイルまたはディレクトリに最後に書き込んだ日付と時刻を返します。</summary>
      <returns>指定したファイルまたはディレクトリに最後に書き込んだ日時に設定された構造体。この値は現地時刻で表示されます。</returns>
      <param name="path">変更日時情報を取得する対象のファイルまたはディレクトリ。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を 1 つ以上含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetLastWriteTimeUtc(System.String)">
      <summary>指定したファイルまたはディレクトリに最後に書き込んだ日付と時刻を世界協定時刻 (UTC) 形式で返します。</summary>
      <returns>指定したファイルまたはディレクトリに最後に書き込んだ日時に設定された構造体。この値は UTC 時刻で表現されます。</returns>
      <param name="path">変更日時情報を取得する対象のファイルまたはディレクトリ。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を 1 つ以上含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetParent(System.String)">
      <summary>絶対パスと相対パスの両方を含む指定したパスの親ディレクトリを取得します。</summary>
      <returns>
        <paramref name="path" /> が UNC サーバーや共有名のルートを含むルート ディレクトリの場合は、親ディレクトリまたは null。</returns>
      <param name="path">親ディレクトリを取得する対象のパス。</param>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> で指定したディレクトリは読み取り専用です。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を 1 つ以上含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが見つかりませんでした。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.Move(System.String,System.String)">
      <summary>ファイルまたはディレクトリ、およびその内容を新しい場所に移動します。</summary>
      <param name="sourceDirName">移動するファイルまたはディレクトリのパス。</param>
      <param name="destDirName">
        <paramref name="sourceDirName" /> の新しい位置へのパス。<paramref name="sourceDirName" /> がファイルの場合は、<paramref name="destDirName" /> もファイル名にする必要があります。</param>
      <exception cref="T:System.IO.IOException">ディレクトリを別のボリュームに移動しようとしました。または <paramref name="destDirName" /> は既に存在します。または<paramref name="sourceDirName" /> パラメーターおよび <paramref name="destDirName" /> パラメーターは、同じファイルまたはディレクトリを参照します。またはディレクトリまたはその中のファイルは、別のプロセスによって使用されています。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceDirName" /> または <paramref name="destDirName" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を 1 つ以上含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceDirName" /> または <paramref name="destDirName" /> が null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceDirName" /> によって指定されたパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetCreationTime(System.String,System.DateTime)">
      <summary>指定したファイルまたはディレクトリの作成日時を設定します。</summary>
      <param name="path">作成日時情報を設定する対象のファイルまたはディレクトリ。</param>
      <param name="creationTime">ファイルまたはディレクトリに最後に書き込んだ日付と時刻。この値は現地時刻で表示されます。</param>
      <exception cref="T:System.IO.FileNotFoundException">指定したパスが見つかりませんでした。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を 1 つ以上含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> は、この操作で許可される日付または時刻の範囲を超える値を指定します。</exception>
      <exception cref="T:System.PlatformNotSupportedException">現在のオペレーティング システムは Windows NT 以降ではありません。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetCreationTimeUtc(System.String,System.DateTime)">
      <summary>指定したファイルまたはディレクトリの作成日時を世界協定時刻 (UTC) 形式で設定します。</summary>
      <param name="path">作成日時情報を設定する対象のファイルまたはディレクトリ。</param>
      <param name="creationTimeUtc">ディレクトリまたはファイルが作成された日時。この値は現地時刻で表示されます。</param>
      <exception cref="T:System.IO.FileNotFoundException">指定したパスが見つかりませんでした。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を 1 つ以上含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> は、この操作で許可される日付または時刻の範囲を超える値を指定します。</exception>
      <exception cref="T:System.PlatformNotSupportedException">現在のオペレーティング システムは Windows NT 以降ではありません。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetCurrentDirectory(System.String)">
      <summary>アプリケーションの現在の作業ディレクトリを指定したディレクトリに設定します。</summary>
      <param name="path">現在の作業ディレクトリが設定されるパス。</param>
      <exception cref="T:System.IO.IOException">I/O エラーが発生しました。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を 1 つ以上含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.Security.SecurityException">アンマネージ コードにアクセスするために必要なアクセス許可が、呼び出し元にありません。</exception>
      <exception cref="T:System.IO.FileNotFoundException">指定したパスが見つかりませんでした。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したディレクトリが見つかりませんでした。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastAccessTime(System.String,System.DateTime)">
      <summary>指定したファイルまたはディレクトリに最後にアクセスした日付と時刻を設定します。</summary>
      <param name="path">アクセス日時情報を設定する対象のファイルまたはディレクトリ。</param>
      <param name="lastAccessTime">
        <paramref name="path" /> のアクセス日時の設定値を含むオブジェクト。この値は現地時刻で表示されます。</param>
      <exception cref="T:System.IO.FileNotFoundException">指定したパスが見つかりませんでした。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を 1 つ以上含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.PlatformNotSupportedException">現在のオペレーティング システムは Windows NT 以降ではありません。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTime" /> は、この操作で許可される日付または時刻の範囲を超える値を指定します。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastAccessTimeUtc(System.String,System.DateTime)">
      <summary>指定したファイルまたはディレクトリに最後にアクセスした日付と時刻を世界協定時刻 (UTC) 形式で設定します。</summary>
      <param name="path">アクセス日時情報を設定する対象のファイルまたはディレクトリ。</param>
      <param name="lastAccessTimeUtc">
        <paramref name="path" /> のアクセス日時の設定値を含むオブジェクト。この値は UTC 時刻で表現されます。</param>
      <exception cref="T:System.IO.FileNotFoundException">指定したパスが見つかりませんでした。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を 1 つ以上含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.PlatformNotSupportedException">現在のオペレーティング システムは Windows NT 以降ではありません。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTimeUtc" /> は、この操作で許可される日付または時刻の範囲を超える値を指定します。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastWriteTime(System.String,System.DateTime)">
      <summary>ディレクトリに最後に書き込んだ日付と時刻を設定します。</summary>
      <param name="path">ディレクトリのパス。</param>
      <param name="lastWriteTime">ディレクトリに最後に書き込んだ日付と時刻。この値は現地時刻で表示されます。</param>
      <exception cref="T:System.IO.FileNotFoundException">指定したパスが見つかりませんでした。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を 1 つ以上含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.PlatformNotSupportedException">現在のオペレーティング システムは Windows NT 以降ではありません。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTime" /> は、この操作で許可される日付または時刻の範囲を超える値を指定します。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastWriteTimeUtc(System.String,System.DateTime)">
      <summary>ディレクトリに最後に書き込んだ日付と時刻を世界協定時刻 (UTC) 形式で設定します。</summary>
      <param name="path">ディレクトリのパス。</param>
      <param name="lastWriteTimeUtc">ディレクトリに最後に書き込んだ日付と時刻。この値は UTC 時刻で表現されます。</param>
      <exception cref="T:System.IO.FileNotFoundException">指定したパスが見つかりませんでした。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または無効な文字を 1 つ以上含んでいます。無効な文字を照会するには、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドを使用します。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.PlatformNotSupportedException">現在のオペレーティング システムは Windows NT 以降ではありません。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTimeUtc" /> は、この操作で許可される日付または時刻の範囲を超える値を指定します。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.IO.DirectoryInfo">
      <summary>ディレクトリとサブディレクトリを作成、削除、および列挙するためのインスタンス メソッドを公開します。このクラスは継承できません。この種類の .NET Framework ソース コードを参照して、次を参照してください、。
                                ソースの参照.
                            </summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.DirectoryInfo.#ctor(System.String)">
      <summary>指定した接続文字列、serializeAsText パラメーター、および lockTimeout パラメーターで構成される、
<see cref="T:System.IO.DirectoryInfo" />指定されたパス上のクラスです。
                            </summary>
      <param name="path">作成するパスを指定する文字列、
                                    DirectoryInfo.
                                </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />が
                                        null.
                                    </exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> に、"、&lt;、&gt;、| などの無効な文字が含まれています。
                                    </exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。指定されたパス、ファイル名、またはその両方が長すぎます。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.Create">
      <summary>ディレクトリを作成します。</summary>
      <exception cref="T:System.IO.IOException">ディレクトリを作成できません。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.CreateSubdirectory(System.String)">
      <summary>指定したパスに 1 つ以上のサブディレクトリを作成します。このインスタンスを基準にして、指定されたパスを指定できます、
<see cref="T:System.IO.DirectoryInfo" />クラスです。
                        </summary>
      <returns>指定されている最後のディレクトリ
                                <paramref name="path" />.
                            </returns>
      <param name="path">指定するパス。異なるディスク ボリュームまたは UNC (Universal Naming Convention) 名は指定できません。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />有効なファイル パスを指定しないか、無効なが含まれています
DirectoryInfo文字。
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />が
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">割り当てられていないドライブであるなど、指定されたパスが無効です。</exception>
      <exception cref="T:System.IO.IOException">サブディレクトリを作成できません。またはファイルまたはディレクトリが既にで指定された名前
                                        <paramref name="path" />.
                                    </exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。指定されたパス、ファイル名、またはその両方が長すぎます。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、ディレクトリを作成するために必要なコード アクセス許可がありません。または呼び出し元が、返されたで説明されているディレクトリの読み取りアクセス許可のコード
<see cref="T:System.IO.DirectoryInfo" /> オブジェクト。
                                これは発生するときに、
<paramref name="path" />パラメーターには、既存のディレクトリについて説明します。
                                </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> に、ドライブのラベル ("C:\") の一部ではないコロン文字 (:) が含まれています。
                                    </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.Delete">
      <summary>これを削除します。
<see cref="T:System.IO.DirectoryInfo" />空の場合。
                            </summary>
      <exception cref="T:System.UnauthorizedAccessException">ディレクトリに読み取り専用のファイルが保存されています。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">これによって示されたディレクトリ
<see cref="T:System.IO.DirectoryInfo" />オブジェクトが存在しないか、見つかりませんでした。
                                    </exception>
      <exception cref="T:System.IO.IOException">ディレクトリが空ではありません。またはこのディレクトリは、アプリケーションの現在の作業ディレクトリです。または開いているハンドルがディレクトリに対して存在し、オペレーティング システムが Windows XP またはそれ以前のものです。開いているハンドルは、ディレクトリを列挙する際に発生した可能性があります。詳細については、次のトピックを参照してください。
                                    方法: ディレクトリとファイルを列挙する.
                                </exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.Delete(System.Boolean)">
      <summary>このインスタンスを削除します。
<see cref="T:System.IO.DirectoryInfo" />でのサブディレクトリおよびファイルを削除するかどうかを指定することです。
                            </summary>
      <param name="recursive">trueこのディレクトリやサブディレクトリでは、すべてのファイルを削除するにはそれ以外の場合、
                                    false.
                                </param>
      <exception cref="T:System.UnauthorizedAccessException">ディレクトリに読み取り専用のファイルが保存されています。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">これによって示されたディレクトリ
<see cref="T:System.IO.DirectoryInfo" />オブジェクトが存在しないか、見つかりませんでした。
                                    </exception>
      <exception cref="T:System.IO.IOException">ディレクトリが読み取り専用です。またはディレクトリには、1 つ以上のファイルまたはサブディレクトリが含まれていると
<paramref name="recursive" />が
                                        false.
                                    またはこのディレクトリは、アプリケーションの現在の作業ディレクトリです。または開いているハンドルがディレクトリまたはそのディレクトリ内のファイルに対して存在し、オペレーティング システムが Windows XP またはそれ以前のものです。開いているハンドルは、ディレクトリとファイルを列挙する際に発生した可能性があります。詳細については、次のトピックを参照してください。
                                    方法: ディレクトリとファイルを列挙する.
                                </exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories">
      <summary>現在のディレクトリの列挙可能なディレクトリ情報のコレクションを返します。</summary>
      <returns>現在のディレクトリ内の列挙可能なディレクトリのコレクション。</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">パス内にカプセル化します
<see cref="T:System.IO.DirectoryInfo" />オブジェクトは無効 (たとえば、上にある、マップされていないドライブ)。
                                    </exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories(System.String)">
      <summary>指定された検索パターンに一致する列挙可能なディレクトリ情報のコレクションを返します。</summary>
      <returns>列挙型と一致するディレクトリのコレクション
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">ディレクトリの名前と照合する検索文字列。このパラメーターに、有効なリテラルのパスとワイルドカード (* と?) 文字の組み合わせを含めることができます (「解説」を参照) が、正規表現はサポートされていません。既定のパターンは "*" で、すべてのファイルが返されます。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />が
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">パス内にカプセル化します
<see cref="T:System.IO.DirectoryInfo" />オブジェクトは無効 (たとえば、上にある、マップされていないドライブ)。
                                    </exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories(System.String,System.IO.SearchOption)">
      <summary>指定された検索パターンと、サブディレクトリを検索するかどうかを指定するオプションの設定に一致する列挙可能なディレクトリ情報のコレクションを返します。</summary>
      <returns>列挙型と一致するディレクトリのコレクション
<paramref name="searchPattern" />と
                                <paramref name="searchOption" />.
                            </returns>
      <param name="searchPattern">ディレクトリの名前と照合する検索文字列。このパラメーターに、有効なリテラルのパスとワイルドカード (* と?) 文字の組み合わせを含めることができます (「解説」を参照) が、正規表現はサポートされていません。既定のパターンは "*" で、すべてのファイルが返されます。</param>
      <param name="searchOption">検索操作に現在のディレクトリのみを含めるのか、またはすべてのサブディレクトリを含めるのかを指定する列挙値の 1 つ。既定値は
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />が
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />無効
<see cref="T:System.IO.SearchOption" /> 値。
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">パス内にカプセル化します
<see cref="T:System.IO.DirectoryInfo" />オブジェクトは無効 (たとえば、上にある、マップされていないドライブ)。
                                    </exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles">
      <summary>現在のディレクトリに存在するファイル情報の列挙可能なコレクションを返します。</summary>
      <returns>現在のディレクトリ内の列挙可能なファイルのコレクション。</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">パス内にカプセル化します
<see cref="T:System.IO.DirectoryInfo" />オブジェクトは無効 (たとえば、上にある、マップされていないドライブ)。
                                    </exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles(System.String)">
      <summary>検索パターンに一致する列挙可能なファイル情報のコレクションを返します。</summary>
      <returns>列挙型と一致するファイルのコレクション
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">ファイル名と照合する検索文字列。このパラメーターに、有効なリテラルのパスとワイルドカード (* と?) 文字の組み合わせを含めることができます (「解説」を参照) が、正規表現はサポートされていません。既定のパターンは "*" で、すべてのファイルが返されます。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />が
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">パス内にカプセル化します
<see cref="T:System.IO.DirectoryInfo" />オブジェクトが有効でない (たとえば、上にある、マップされていないドライブ)。
                                    </exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles(System.String,System.IO.SearchOption)">
      <summary>指定された検索パターンと、サブディレクトリを検索するかどうかを指定するオプションの設定に一致する列挙可能なファイル情報のコレクションを返します。</summary>
      <returns>列挙型と一致するファイルのコレクション
<paramref name="searchPattern" />と
                                <paramref name="searchOption" />.
                            </returns>
      <param name="searchPattern">ファイル名と照合する検索文字列。このパラメーターに、有効なリテラルのパスとワイルドカード (* と?) 文字の組み合わせを含めることができます (「解説」を参照) が、正規表現はサポートされていません。既定のパターンは "*" で、すべてのファイルが返されます。</param>
      <param name="searchOption">検索操作に現在のディレクトリのみを含めるのか、またはすべてのサブディレクトリを含めるのかを指定する列挙値の 1 つ。既定値は
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />が
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />無効
<see cref="T:System.IO.SearchOption" /> 値。
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">パス内にカプセル化します
<see cref="T:System.IO.DirectoryInfo" />オブジェクトは無効 (たとえば、上にある、マップされていないドライブ)。
                                    </exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos">
      <summary>現在のディレクトリ内の列挙可能なファイル システム情報のコレクションを返します。</summary>
      <returns>現在のディレクトリ内の列挙可能なファイル システム情報のコレクション。</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">パス内にカプセル化します
<see cref="T:System.IO.DirectoryInfo" />オブジェクトは無効 (たとえば、上にある、マップされていないドライブ)。
                                    </exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos(System.String)">
      <summary>指定された検索パターンに一致する列挙可能なファイル システム情報のコレクションを返します。</summary>
      <returns>列挙型と一致するファイル システムの情報のオブジェクトのコレクション
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">ディレクトリの名前と照合する検索文字列。このパラメーターに、有効なリテラルのパスとワイルドカード (* と?) 文字の組み合わせを含めることができます (「解説」を参照) が、正規表現はサポートされていません。既定のパターンは "*" で、すべてのファイルが返されます。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />が
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">パス内にカプセル化します
<see cref="T:System.IO.DirectoryInfo" />オブジェクトは無効 (たとえば、上にある、マップされていないドライブ)。
                                    </exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos(System.String,System.IO.SearchOption)">
      <summary>指定された検索パターンと、サブディレクトリを検索するかどうかを指定するオプションの設定に一致する列挙可能なファイル システム情報のコレクションを返します。</summary>
      <returns>列挙型と一致するファイル システムの情報のオブジェクトのコレクション
<paramref name="searchPattern" />と
                                <paramref name="searchOption" />.
                            </returns>
      <param name="searchPattern">ディレクトリの名前と照合する検索文字列。このパラメーターに、有効なリテラルのパスとワイルドカード (* と?) 文字の組み合わせを含めることができます (「解説」を参照) が、正規表現はサポートされていません。既定のパターンは "*" で、すべてのファイルが返されます。</param>
      <param name="searchOption">検索操作に現在のディレクトリのみを含めるのか、またはすべてのサブディレクトリを含めるのかを指定する列挙値の 1 つ。既定値は
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />が
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />無効
<see cref="T:System.IO.SearchOption" /> 値。
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">パス内にカプセル化します
<see cref="T:System.IO.DirectoryInfo" />オブジェクトは無効 (たとえば、上にある、マップされていないドライブ)。
                                    </exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
    </member>
    <member name="P:System.IO.DirectoryInfo.Exists">
      <summary>ディレクトリが存在するかどうかを示す値を取得します。</summary>
      <returns>trueディレクトリが存在する場合それ以外の場合、
                                false.
                            </returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories">
      <summary>現在のディレクトリのサブディレクトリを返します。</summary>
      <returns>配列
<see cref="T:System.IO.DirectoryInfo" /> オブジェクト
                            </returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">パス内にカプセル化します
<see cref="T:System.IO.DirectoryInfo" />オブジェクトは、マップされていないドライブを使用しているなど、無効です。
                                    </exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories(System.String)">
      <summary>現在のディレクトリの配列を返します
<see cref="T:System.IO.DirectoryInfo" />特定の検索条件に一致します。
                            </summary>
      <returns>型の配列
DirectoryInfo一致します。
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">ディレクトリの名前と照合する検索文字列。このパラメーターに、有効なリテラルのパスとワイルドカード (* と?) 文字の組み合わせを含めることができます (「解説」を参照) が、正規表現はサポートされていません。既定のパターンは "*" で、すべてのファイルが返されます。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />定義されている 1 つ以上の無効な文字が含まれています、。
<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッド
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />が
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">パス内にカプセル化します
DirectoryInfoオブジェクトは無効 (たとえば、上にある、マップされていないドライブ)。
                                    </exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories(System.String,System.IO.SearchOption)">
      <summary>現在のディレクトリの配列を返します
<see cref="T:System.IO.DirectoryInfo" />特定の検索条件に一致して、サブディレクトリを検索するかどうかを決定する値を使用します。
                            </summary>
      <returns>型の配列
DirectoryInfo一致します。
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">ディレクトリの名前と照合する検索文字列。このパラメーターに、有効なリテラルのパスとワイルドカード (* と?) 文字の組み合わせを含めることができます (「解説」を参照) が、正規表現はサポートされていません。既定のパターンは "*" で、すべてのファイルが返されます。</param>
      <param name="searchOption">検索操作に現在のディレクトリのみを含めるのか、またはすべてのサブディレクトリを含めるのかを指定する列挙値の 1 つ。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />定義されている 1 つ以上の無効な文字が含まれています、。
<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッド
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />が
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />無効
<see cref="T:System.IO.SearchOption" /> 値。
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">パス内にカプセル化します
DirectoryInfoオブジェクトは無効 (たとえば、上にある、マップされていないドライブ)。
                                    </exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles">
      <summary>現在のディレクトリからファイル一覧を返します。</summary>
      <returns>型の配列
                                <see cref="T:System.IO.FileInfo" />.
                            </returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">割り当てられていないドライブであるなど、パスが無効です。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles(System.String)">
      <summary>現在のディレクトリから、指定した検索パターンに一致するファイル一覧を返します。</summary>
      <returns>型の配列
                                <see cref="T:System.IO.FileInfo" />.
                            </returns>
      <param name="searchPattern">ファイル名と照合する検索文字列。このパラメーターに、有効なリテラルのパスとワイルドカード (* と?) 文字の組み合わせを含めることができます (「解説」を参照) が、正規表現はサポートされていません。既定のパターンは "*" で、すべてのファイルが返されます。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />定義されている 1 つ以上の無効な文字が含まれています、。
<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッド
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />が
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">パスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles(System.String,System.IO.SearchOption)">
      <summary>現在のディレクトリから、指定した検索パターンに一致し、サブディレクトリを検索するかどうかを決定する値を持つファイル一覧を返します。</summary>
      <returns>型の配列
                                <see cref="T:System.IO.FileInfo" />.
                            </returns>
      <param name="searchPattern">ファイル名と照合する検索文字列。このパラメーターに、有効なリテラルのパスとワイルドカード (* と?) 文字の組み合わせを含めることができます (「解説」を参照) が、正規表現はサポートされていません。既定のパターンは "*" で、すべてのファイルが返されます。</param>
      <param name="searchOption">検索操作に現在のディレクトリのみを含めるのか、またはすべてのサブディレクトリを含めるのかを指定する列挙値の 1 つ。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />定義されている 1 つ以上の無効な文字が含まれています、。
<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッド
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />が
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />無効
<see cref="T:System.IO.SearchOption" /> 値。
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">パスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos">
      <summary>厳密に型指定の配列を返します。
<see cref="T:System.IO.FileSystemInfo" />すべてのファイルおよびディレクトリ内にあるサブディレクトリを表すエントリ。
                            </summary>
      <returns>配列が厳密に型指定
<see cref="T:System.IO.FileSystemInfo" />エントリ。
                            </returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">パスが無効です (割り当てられていないドライブであるなど)。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos(System.String)">
      <summary>厳密に型指定の配列を取得します
<see cref="T:System.IO.FileSystemInfo" />ファイルと、指定した検索条件に一致するサブディレクトリを表すオブジェクトです。
                            </summary>
      <returns>配列が厳密に型指定
FileSystemInfo検索条件に一致するオブジェクトです。
                            </returns>
      <param name="searchPattern">ディレクトリおよびファイルの名前と照合する検索文字列。このパラメーターに、有効なリテラルのパスとワイルドカード (* と?) 文字の組み合わせを含めることができます (「解説」を参照) が、正規表現はサポートされていません。既定のパターンは "*" で、すべてのファイルが返されます。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />定義されている 1 つ以上の無効な文字が含まれています、。
<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッド
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />が
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos(System.String,System.IO.SearchOption)">
      <summary>配列を取得します。
<see cref="T:System.IO.FileSystemInfo" />ファイルと指定した検索条件に一致するサブディレクトリを表すオブジェクト。
                            </summary>
      <returns>検索条件に一致するファイル システム エントリの配列。</returns>
      <param name="searchPattern">ディレクトリおよびファイルの名前と照合する検索文字列。このパラメーターに、有効なリテラルのパスとワイルドカード (* と?) 文字の組み合わせを含めることができます (「解説」を参照) が、正規表現はサポートされていません。既定のパターンは "*" で、すべてのファイルが返されます。</param>
      <param name="searchOption">検索操作に現在のディレクトリのみを含めるのか、またはすべてのサブディレクトリを含めるのかを指定する列挙値の 1 つ。既定値は
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />定義されている 1 つ以上の無効な文字が含まれています、。
<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッド
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />が
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />無効
<see cref="T:System.IO.SearchOption" /> 値。
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.MoveTo(System.String)">
      <summary>移動します。
<see cref="T:System.IO.DirectoryInfo" />インスタンスとその内容を新しいパスにします。
                            </summary>
      <param name="destDirName">このディレクトリの移動先の名前とパス。別のディスク ボリュームまたは同じ名前のディレクトリは移動先として指定できません。このディレクトリをサブディレクトリとして追加する場合は、既存のディレクトリを指定できます。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destDirName" />が
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="destDirName" /> が空の文字列 (''") です。
                                    </exception>
      <exception cref="T:System.IO.IOException">ディレクトリを別のボリュームに移動しようとしました。または<paramref name="destDirName" /> は既に存在します。
                                    またはこのパスへのアクセスが承認されていません。または移動しようとしているディレクトリと移動先のディレクトリが同じ名前です。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">移動先のディレクトリが見つかりません。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.DirectoryInfo.Name">
      <summary>この名前を取得します。
<see cref="T:System.IO.DirectoryInfo" />インスタンス。
                            </summary>
      <returns>ディレクトリ名。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.DirectoryInfo.Parent">
      <summary>指定されたサブディレクトリの親ディレクトリを取得します。</summary>
      <returns>親ディレクトリ、または
nullパスが null の場合、またはファイルのパスをルートを表している場合 (など「\」、「c:」、または *"\\server\share")。
                            </returns>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.DirectoryInfo.Root">
      <summary>ディレクトリのルート部分を取得します。</summary>
      <returns>ディレクトリのルートを表すオブジェクト。</returns>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.ToString">
      <summary>ユーザーから渡された元のパスを返します。</summary>
      <returns>ユーザーから渡された元のパスを返します。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.IO.File">
      <summary>1 つのファイルの作成、コピー、削除、移動、オープンのための静的メソッドを提供し、<see cref="T:System.IO.FileStream" /> オブジェクトの作成を支援します。この種類の .NET Framework ソース コードを参照して、次を参照してください。、 Reference Sourceです。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.File.AppendAllLines(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>ファイルに行を追加してから、そのファイルを閉じます。指定したファイルが存在しない場合、このメソッドはファイルを作成し、指定した行をファイルに書き込んだ後、ファイルを閉じます。</summary>
      <param name="path">行を追加するファイル。ファイルがまだ存在しない場合は作成されます。</param>
      <param name="contents">ファイルに追加する行。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドで定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">いずれか<paramref name=" path " />または <paramref name="contents" /> は nullです。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> は無効です (たとえば、ディレクトリが存在しないか、割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" /> で指定されたファイルが見つかりませんでした。</exception>
      <exception cref="T:System.IO.IOException">ファイルを開くときに、I/O エラーが発生しました。</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> が、システムで定義されている最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、ファイルへの書き込みに必要なアクセス許可がありません。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> が、読み取り専用のファイルを指定しています。またはこの操作は、現在のプラットフォームではサポートされていません。または<paramref name="path" /> がディレクトリです。</exception>
    </member>
    <member name="M:System.IO.File.AppendAllLines(System.String,System.Collections.Generic.IEnumerable{System.String},System.Text.Encoding)">
      <summary>指定されたエンコーディングを使用してファイルに行を追加してから、そのファイルを閉じます。指定したファイルが存在しない場合、このメソッドはファイルを作成し、指定した行をファイルに書き込んだ後、ファイルを閉じます。</summary>
      <param name="path">行を追加するファイル。ファイルがまだ存在しない場合は作成されます。</param>
      <param name="contents">ファイルに追加する行。</param>
      <param name="encoding">使用する文字エンコーディング。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドで定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name=" path" />、<paramref name="contents" />、または <paramref name="encoding" /> が null です。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> は無効です (たとえば、ディレクトリが存在しないか、割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" /> で指定されたファイルが見つかりませんでした。</exception>
      <exception cref="T:System.IO.IOException">ファイルを開くときに、I/O エラーが発生しました。</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> が、システムで定義されている最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> が、読み取り専用のファイルを指定しています。またはこの操作は、現在のプラットフォームではサポートされていません。または<paramref name="path" /> がディレクトリです。または呼び出し元に、必要なアクセス許可がありません。</exception>
    </member>
    <member name="M:System.IO.File.AppendAllText(System.String,System.String)">
      <summary>ファイルを開き、指定した文字列をそのファイルに追加した後、ファイルを閉じます。ファイルが存在しない場合、このメソッドはファイルを作成し、指定した文字列をファイルに書き込んだ後、ファイルを閉じます。</summary>
      <param name="path">指定した文字列の追加先となるファイル。</param>
      <param name="contents">ファイルに追加する文字列。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定されたパスが無効です (たとえば、ディレクトリが存在しないか、割り当てられていないドライブ上にあります)。</exception>
      <exception cref="T:System.IO.IOException">ファイルを開くときに、I/O エラーが発生しました。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> によって、読み取り専用のファイルが指定されました。または この操作は、現在のプラットフォームではサポートされていません。または <paramref name="path" /> によってディレクトリが指定されました。または 呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.AppendAllText(System.String,System.String,System.Text.Encoding)">
      <summary>指定した文字列をファイルに追加します。ファイルがまだ存在しない場合は、ファイルを作成します。</summary>
      <param name="path">指定した文字列の追加先となるファイル。</param>
      <param name="contents">ファイルに追加する文字列。</param>
      <param name="encoding">使用する文字エンコーディング。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定されたパスが無効です (たとえば、ディレクトリが存在しないか、割り当てられていないドライブ上にあります)。</exception>
      <exception cref="T:System.IO.IOException">ファイルを開くときに、I/O エラーが発生しました。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> によって、読み取り専用のファイルが指定されました。または この操作は、現在のプラットフォームではサポートされていません。または <paramref name="path" /> によってディレクトリが指定されました。または 呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.AppendText(System.String)">
      <summary>
        <see cref="T:System.IO.StreamWriter" /> を作成します。これは、UTF-8 でエンコードされたテキストを既存のファイルに 追加するか、指定したファイルが存在しない場合は新しいファイルに追加します。</summary>
      <returns>指定したファイルまたは新しいファイルに、UTF-8 エンコードされたテキストを追加するストリーム ライター。</returns>
      <param name="path">追加先のファイル パス。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定されたパスが無効です (たとえば、ディレクトリが存在しないか、割り当てられていないドライブ上にあります)。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Copy(System.String,System.String)">
      <summary>既存のファイルを新しいファイルにコピーします。同じ名前のファイルを上書きできません。</summary>
      <param name="sourceFileName">コピーするファイル。</param>
      <param name="destFileName">コピー先ファイルの名前。ディレクトリや既存のファイルは使用できません。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> または <paramref name="destFileName" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。または <paramref name="sourceFileName" /> または <paramref name="destFileName" /> はディレクトリを指定します。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> または <paramref name="destFileName" /> が null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceFileName" /> または <paramref name="destFileName" /> で指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="sourceFileName" /> は見つかりませんでした。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destFileName" /> は存在します。または I/O エラーが発生しました。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceFileName" /> または <paramref name="destFileName" /> の形式が無効です。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Copy(System.String,System.String,System.Boolean)">
      <summary>既存のファイルを新しいファイルにコピーします。同じ名前のファイルの上書きが許可されます。</summary>
      <param name="sourceFileName">コピーするファイル。</param>
      <param name="destFileName">コピー先ファイルの名前。このパラメーターには、ディレクトリは指定できません。</param>
      <param name="overwrite">コピー先ファイルが上書きできる場合は true。それ以外の場合は false。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。または<paramref name="destFileName" /> は読み取り専用です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> または <paramref name="destFileName" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。または <paramref name="sourceFileName" /> または <paramref name="destFileName" /> はディレクトリを指定します。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> または <paramref name="destFileName" /> が null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceFileName" /> または <paramref name="destFileName" /> で指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="sourceFileName" /> は見つかりませんでした。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destFileName" /> は存在し、<paramref name="overwrite" /> は false です。または I/O エラーが発生しました。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceFileName" /> または <paramref name="destFileName" /> の形式が無効です。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Create(System.String)">
      <summary>指定したパスでファイルを作成または上書きします。</summary>
      <returns>
        <see cref="T:System.IO.FileStream" /> で指定したファイルへの読み取り/書き込みアクセスを提供する <paramref name="path" />。</returns>
      <param name="path">作成するファイルのパスと名前。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。または <paramref name="path" /> によって、読み取り専用のファイルが指定されました。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.IO.IOException">ファイルを作成しているときに、I/O エラーが発生しました。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Create(System.String,System.Int32)">
      <summary>指定したファイルを作成または上書きします。</summary>
      <returns>指定された大きさのバッファーを持ち、<see cref="T:System.IO.FileStream" /> で指定されたファイルへの読み取り/書き込みアクセスを提供する <paramref name="path" />。</returns>
      <param name="path">ファイルの名前です。</param>
      <param name="bufferSize">ファイルの読み取りおよび書き込み用にバッファリングされるバイト数。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。または <paramref name="path" /> によって、読み取り専用のファイルが指定されました。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.IO.IOException">ファイルを作成しているときに、I/O エラーが発生しました。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Create(System.String,System.Int32,System.IO.FileOptions)">
      <summary>バッファー サイズとファイルの作成または上書き方法を示す <see cref="T:System.IO.FileOptions" /> 値を指定して、指定のファイルを作成または上書きします。</summary>
      <returns>指定したバッファー サイズの新規ファイル。</returns>
      <param name="path">ファイルの名前です。</param>
      <param name="bufferSize">ファイルの読み取りおよび書き込み用にバッファリングされるバイト数。</param>
      <param name="options">ファイルを作成または上書きする方法を示す <see cref="T:System.IO.FileOptions" /> 値のいずれか。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。または <paramref name="path" /> によって、読み取り専用のファイルが指定されました。または<see cref="F:System.IO.FileOptions.Encrypted" /> が <paramref name="options" /> に対して指定されていますが、ファイル暗号化は現在のプラットフォームでサポートされていません。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.IO.IOException">ファイルを作成しているときに、I/O エラーが発生しました。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。または <paramref name="path" /> によって、読み取り専用のファイルが指定されました。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。または <paramref name="path" /> によって、読み取り専用のファイルが指定されました。</exception>
    </member>
    <member name="M:System.IO.File.CreateText(System.String)">
      <summary>UTF-8 エンコードされたテキストの書き込み用にファイルを作成または開きます。</summary>
      <returns>UTF-8 エンコーディングを使用して指定したファイルに書き込まれる <see cref="T:System.IO.StreamWriter" />。</returns>
      <param name="path">書き込み用に開かれるファイル。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Delete(System.String)">
      <summary>指定されたファイルを削除します。</summary>
      <param name="path">削除するファイルの名前。ワイルドカード文字はサポートされていません。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.IO.IOException">指定したファイルが使用中です。または開いているハンドルがファイルに対して存在し、オペレーティング システムが Windows XP またはそれ以前のものです。開いているハンドルは、ディレクトリとファイルを列挙する際に発生した可能性があります。詳細については、「方法: ディレクトリとファイルを列挙する」を参照してください。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。または ファイルが使用されている実行可能ファイルです。または <paramref name="path" /> がディレクトリです。または <paramref name="path" /> によって、読み取り専用のファイルが指定されました。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Exists(System.String)">
      <summary>指定したファイルが存在するかどうかを確認します。</summary>
      <returns>呼び出し元が必要なアクセス許可を持ち、true に既存のファイル名が格納されている場合は <paramref name="path" />。それ以外の場合は false。false が <paramref name="path" />、無効なパス、または長さ 0 の文字列の場合にも、このメソッドは null を返します。呼び出し元が指定したファイルを読み取るための十分なアクセス許可を持たない場合、例外はスローされず、このメソッドは、false の有無にかかわらず <paramref name="path" /> を返します。</returns>
      <param name="path">確認するファイル。</param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetAttributes(System.String)">
      <summary>パス上のファイルの <see cref="T:System.IO.FileAttributes" /> を取得します。</summary>
      <returns>パス上のファイルの <see cref="T:System.IO.FileAttributes" />。</returns>
      <param name="path">ファイルへのパス。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が空か、空白だけが含まれているか、または無効な文字が含まれています。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" /> はファイルを指定していますが、割り当てられていないドライブである、ファイルが見つからないなどの理由で無効です。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> はディレクトリを指定していますが、割り当てられていないドライブである、ファイルが見つからないなどの理由で無効です。</exception>
      <exception cref="T:System.IO.IOException">このファイルは、別のプロセスによって使用されています。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetCreationTime(System.String)">
      <summary>指定したファイルまたはディレクトリの作成日時を返します。</summary>
      <returns>指定したファイルまたはディレクトリの作成日時に設定された <see cref="T:System.DateTime" /> 構造体。この値は現地時刻で表示されます。</returns>
      <param name="path">作成日時情報を取得する対象のファイルまたはディレクトリ。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetCreationTimeUtc(System.String)">
      <summary>指定したファイルまたはディレクトリの作成日時を世界協定時刻 (UTC) で返します。</summary>
      <returns>指定したファイルまたはディレクトリの作成日時に設定された <see cref="T:System.DateTime" /> 構造体。この値は UTC 時刻で表現されます。</returns>
      <param name="path">作成日時情報を取得する対象のファイルまたはディレクトリ。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastAccessTime(System.String)">
      <summary>指定したファイルまたはディレクトリに最後にアクセスした日付と時刻を返します。</summary>
      <returns>指定したファイルまたはディレクトリに最後にアクセスした日付と時刻に設定された <see cref="T:System.DateTime" /> 構造体。この値は現地時刻で表示されます。</returns>
      <param name="path">アクセス日時情報を取得する対象のファイルまたはディレクトリ。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastAccessTimeUtc(System.String)">
      <summary>指定したファイルまたはディレクトリに最後にアクセスした日付と時刻を世界協定時刻 (UTC) で返します。</summary>
      <returns>指定したファイルまたはディレクトリに最後にアクセスした日付と時刻に設定された <see cref="T:System.DateTime" /> 構造体。この値は UTC 時刻で表現されます。</returns>
      <param name="path">アクセス日時情報を取得する対象のファイルまたはディレクトリ。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastWriteTime(System.String)">
      <summary>指定したファイルまたはディレクトリに最後に書き込んだ日付と時刻を返します。</summary>
      <returns>指定したファイルまたはディレクトリに最後に書き込んだ日付と時刻に設定された <see cref="T:System.DateTime" /> 構造体。この値は現地時刻で表示されます。</returns>
      <param name="path">書き込み日時情報を取得する対象のファイルまたはディレクトリ。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastWriteTimeUtc(System.String)">
      <summary>指定したファイルまたはディレクトリに最後に書き込んだ日付と時刻を世界協定時刻 (UTC) で返します。</summary>
      <returns>指定したファイルまたはディレクトリに最後に書き込んだ日付と時刻に設定された <see cref="T:System.DateTime" /> 構造体。この値は UTC 時刻で表現されます。</returns>
      <param name="path">書き込み日時情報を取得する対象のファイルまたはディレクトリ。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Move(System.String,System.String)">
      <summary>指定したファイルを新しい場所に移動します。オプションで新しいファイル名を指定することもできます。</summary>
      <param name="sourceFileName">移動するファイルの名前。相対パスまたは絶対パスを含めることができます。</param>
      <param name="destFileName">ファイルの新しいパスおよび名前。</param>
      <exception cref="T:System.IO.IOException">移動先のファイルは既に存在します。または<paramref name="sourceFileName" /> は見つかりませんでした。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> または <paramref name="destFileName" /> が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> または <paramref name="destFileName" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を含んでいます。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceFileName" /> または <paramref name="destFileName" /> で指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceFileName" /> または <paramref name="destFileName" /> の形式が無効です。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode)">
      <summary>指定したパスの <see cref="T:System.IO.FileStream" /> を読み取り/書き込みアクセスで開きます。</summary>
      <returns>共有モードではなく読み取り/書き込みアクセスで、指定したモードとパスで開いた <see cref="T:System.IO.FileStream" />。</returns>
      <param name="path">開くファイル。</param>
      <param name="mode">ファイルが存在しない場合にファイルを作成するかどうかを指定し、既存のファイルの内容を保持するか上書きするかを決定する <see cref="T:System.IO.FileMode" /> 値。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.IO.IOException">ファイルを開くときに、I/O エラーが発生しました。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> によって、読み取り専用のファイルが指定されました。または この操作は、現在のプラットフォームではサポートされていません。または <paramref name="path" /> によってディレクトリが指定されました。または 呼び出し元に、必要なアクセス許可がありません。または<paramref name="mode" /> は <see cref="F:System.IO.FileMode.Create" /> で、指定されたファイルは隠しファイルです。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> によって無効な値が指定されました。</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" /> で指定されたファイルが見つかりませんでした。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode,System.IO.FileAccess)">
      <summary>指定したモードとアクセスで、指定したパスの <see cref="T:System.IO.FileStream" /> を開きます。</summary>
      <returns>指定したモードとアクセスで、指定したファイルにアクセスする非共有 <see cref="T:System.IO.FileStream" />。</returns>
      <param name="path">開くファイル。</param>
      <param name="mode">ファイルが存在しない場合にファイルを作成するかどうかを指定し、既存のファイルの内容を保持するか上書きするかを決定する <see cref="T:System.IO.FileMode" /> 値。</param>
      <param name="access">ファイルで実行できる操作を指定する <see cref="T:System.IO.FileAccess" /> 値。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。または <paramref name="access" /> が Read を指定し、<paramref name="mode" /> が Create、CreateNew、Truncate、または Append を指定しました。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.IO.IOException">ファイルを開くときに、I/O エラーが発生しました。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> によって、読み取り専用のファイルが指定され、<paramref name="access" /> は Read ではありません。または <paramref name="path" /> によってディレクトリが指定されました。または 呼び出し元に、必要なアクセス許可がありません。または<paramref name="mode" /> は <see cref="F:System.IO.FileMode.Create" /> で、指定されたファイルは隠しファイルです。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> または <paramref name="access" /> によって無効な値が指定されました。</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" /> で指定されたファイルが見つかりませんでした。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>読み取り、書き込み、または読み取り/書き込みアクセスを持つ指定モードと指定した共有オプションで、指定したパスの <see cref="T:System.IO.FileStream" /> を開きます。</summary>
      <returns>読み取り、書き込み、または読み取り/書き込みアクセスを持つ指定モードと指定した共有オプションで、指定したパスの <see cref="T:System.IO.FileStream" />。</returns>
      <param name="path">開くファイル。</param>
      <param name="mode">ファイルが存在しない場合にファイルを作成するかどうかを指定し、既存のファイルの内容を保持するか上書きするかを決定する <see cref="T:System.IO.FileMode" /> 値。</param>
      <param name="access">ファイルで実行できる操作を指定する <see cref="T:System.IO.FileAccess" /> 値。</param>
      <param name="share">他のスレッドがファイルに対して持つアクセス タイプを指定する <see cref="T:System.IO.FileShare" /> 値。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。または <paramref name="access" /> が Read を指定し、<paramref name="mode" /> が Create、CreateNew、Truncate、または Append を指定しました。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.IO.IOException">ファイルを開くときに、I/O エラーが発生しました。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> によって、読み取り専用のファイルが指定され、<paramref name="access" /> は Read ではありません。または <paramref name="path" /> によってディレクトリが指定されました。または 呼び出し元に、必要なアクセス許可がありません。または<paramref name="mode" /> は <see cref="F:System.IO.FileMode.Create" /> で、指定されたファイルは隠しファイルです。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" />、<paramref name="access" />、または <paramref name="share" /> によって無効な値が指定されました。</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" /> で指定されたファイルが見つかりませんでした。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.OpenRead(System.String)">
      <summary>読み取り用の既存のファイルを開きます。</summary>
      <returns>指定したパスの読み取り専用 <see cref="T:System.IO.FileStream" />。</returns>
      <param name="path">読み取り用に開かれるファイル。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> によってディレクトリが指定されました。または 呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" /> で指定されたファイルが見つかりませんでした。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <exception cref="T:System.IO.IOException">ファイルを開くときに、I/O エラーが発生しました。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.OpenText(System.String)">
      <summary>読み取り用の既存の UTF-8 エンコードされたテキスト ファイルを開きます。</summary>
      <returns>指定したパスの <see cref="T:System.IO.StreamReader" />。</returns>
      <param name="path">読み取り用に開かれるファイル。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" /> で指定されたファイルが見つかりませんでした。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.OpenWrite(System.String)">
      <summary>書き込み用に、既存のファイルを開くか新しいファイルを作成します。</summary>
      <returns>指定されたパスに置かれている、非共有の <see cref="T:System.IO.FileStream" /> オブジェクト。アクセス許可は <see cref="F:System.IO.FileAccess.Write" /> です。</returns>
      <param name="path">書き込み用に開かれるファイル。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。または <paramref name="path" /> が、読み取り専用のファイルまたはディレクトリを指定しました。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllBytes(System.String)">
      <summary>バイナリ ファイルを開き、ファイルの内容をバイト配列に読み取った後、ファイルを閉じます。</summary>
      <returns>ファイルの内容を格納しているバイト配列。</returns>
      <param name="path">読み取り用に開かれるファイル。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.IO.IOException">ファイルを開くときに、I/O エラーが発生しました。</exception>
      <exception cref="T:System.UnauthorizedAccessException">この操作は、現在のプラットフォームではサポートされていません。または <paramref name="path" /> によってディレクトリが指定されました。または 呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" /> で指定されたファイルが見つかりませんでした。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllLines(System.String)">
      <summary>テキスト ファイルを開き、ファイルのすべての行を読み取った後、ファイルを閉じます。</summary>
      <returns>ファイルのすべての行を格納している文字列配列。</returns>
      <param name="path">読み取り用に開かれるファイル。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.IO.IOException">ファイルを開くときに、I/O エラーが発生しました。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> によって、読み取り専用のファイルが指定されました。または この操作は、現在のプラットフォームではサポートされていません。または <paramref name="path" /> によってディレクトリが指定されました。または 呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" /> で指定されたファイルが見つかりませんでした。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllLines(System.String,System.Text.Encoding)">
      <summary>ファイルを開き、指定したエンコーディングが適用されたファイルのすべての行を読み取った後、ファイルを閉じます。</summary>
      <returns>ファイルのすべての行を格納している文字列配列。</returns>
      <param name="path">読み取り用に開かれるファイル。</param>
      <param name="encoding">ファイルの内容に適用されるエンコーディング。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.IO.IOException">ファイルを開くときに、I/O エラーが発生しました。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> によって、読み取り専用のファイルが指定されました。または この操作は、現在のプラットフォームではサポートされていません。または <paramref name="path" /> によってディレクトリが指定されました。または 呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" /> で指定されたファイルが見つかりませんでした。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllText(System.String)">
      <summary>テキスト ファイルを開き、ファイルのすべての行を読み取った後、ファイルを閉じます。</summary>
      <returns>ファイルのすべての行を格納している文字列。</returns>
      <param name="path">読み取り用に開かれるファイル。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.IO.IOException">ファイルを開くときに、I/O エラーが発生しました。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> によって、読み取り専用のファイルが指定されました。または この操作は、現在のプラットフォームではサポートされていません。または <paramref name="path" /> によってディレクトリが指定されました。または 呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" /> で指定されたファイルが見つかりませんでした。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllText(System.String,System.Text.Encoding)">
      <summary>ファイルを開き、指定したエンコーディングが適用されたファイルのすべての行を読み取った後、ファイルを閉じます。</summary>
      <returns>ファイルのすべての行を格納している文字列。</returns>
      <param name="path">読み取り用に開かれるファイル。</param>
      <param name="encoding">ファイルの内容に適用されるエンコーディング。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.IO.IOException">ファイルを開くときに、I/O エラーが発生しました。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> によって、読み取り専用のファイルが指定されました。または この操作は、現在のプラットフォームではサポートされていません。または <paramref name="path" /> によってディレクトリが指定されました。または 呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" /> で指定されたファイルが見つかりませんでした。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadLines(System.String)">
      <summary>ファイルの行を読み取ります。</summary>
      <returns>ファイルのすべての行、またはクエリの結果の行。</returns>
      <param name="path">読み取るファイル。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 長さゼロの文字列は、空白文字だけが含まれています。 またはで定義されている 1 つ以上の無効な文字が含まれている、 <see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドです。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> が無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" /> で指定されたファイルが見つかりませんでした。</exception>
      <exception cref="T:System.IO.IOException">ファイルを開くときに、I/O エラーが発生しました。</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> が、システムで定義されている最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> が、読み取り専用のファイルを指定しています。またはこの操作は、現在のプラットフォームではサポートされていません。または<paramref name="path" /> がディレクトリです。または呼び出し元に、必要なアクセス許可がありません。</exception>
    </member>
    <member name="M:System.IO.File.ReadLines(System.String,System.Text.Encoding)">
      <summary>指定された方法でエンコーディングされたファイルの行を読み取ります。</summary>
      <returns>ファイルのすべての行、またはクエリの結果の行。</returns>
      <param name="path">読み取るファイル。</param>
      <param name="encoding">ファイルの内容に適用されるエンコーディング。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、<see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドで定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> が無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" /> で指定されたファイルが見つかりませんでした。</exception>
      <exception cref="T:System.IO.IOException">ファイルを開くときに、I/O エラーが発生しました。</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> が、システムで定義されている最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> が、読み取り専用のファイルを指定しています。またはこの操作は、現在のプラットフォームではサポートされていません。または<paramref name="path" /> がディレクトリです。または呼び出し元に、必要なアクセス許可がありません。</exception>
    </member>
    <member name="M:System.IO.File.SetAttributes(System.String,System.IO.FileAttributes)">
      <summary>指定したパスでファイルの指定された <see cref="T:System.IO.FileAttributes" /> を設定します。</summary>
      <param name="path">ファイルへのパス。</param>
      <param name="fileAttributes">列挙値のビットごとの組み合わせ。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が空か、空白だけが含まれているか、無効な文字が含まれているか、またはファイル属性が無効です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.IO.FileNotFoundException">ファイルが見つかりません。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> によって、読み取り専用のファイルが指定されました。または この操作は、現在のプラットフォームではサポートされていません。または <paramref name="path" /> によってディレクトリが指定されました。または 呼び出し元に、必要なアクセス許可がありません。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetCreationTime(System.String,System.DateTime)">
      <summary>ファイルが作成された日付と時刻を設定します。</summary>
      <param name="path">作成日時情報を設定する対象のファイル。</param>
      <param name="creationTime">
        <see cref="T:System.DateTime" /> の作成日時の設定値を含む <paramref name="path" />。この値は現地時刻で表示されます。</param>
      <exception cref="T:System.IO.FileNotFoundException">指定したパスが見つかりませんでした。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.IOException">操作の実行中に I/O エラーが発生しました。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> は、この操作で許可された日付または時刻、またはその両方の範囲を超える値を指定します。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetCreationTimeUtc(System.String,System.DateTime)">
      <summary>ファイルが作成された日付と時刻を世界協定時刻 (UTC) で設定します。</summary>
      <param name="path">作成日時情報を設定する対象のファイル。</param>
      <param name="creationTimeUtc">
        <see cref="T:System.DateTime" /> の作成日時の設定値を含む <paramref name="path" />。この値は UTC 時刻で表現されます。</param>
      <exception cref="T:System.IO.FileNotFoundException">指定したパスが見つかりませんでした。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.IOException">操作の実行中に I/O エラーが発生しました。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> は、この操作で許可された日付または時刻、またはその両方の範囲を超える値を指定します。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastAccessTime(System.String,System.DateTime)">
      <summary>指定したファイルに最後にアクセスした日付と時刻を設定します。</summary>
      <param name="path">アクセス日時情報を設定する対象のファイル。</param>
      <param name="lastAccessTime">
        <see cref="T:System.DateTime" /> に最後にアクセスした日付と時刻の設定値を含む <paramref name="path" />。この値は現地時刻で表示されます。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.FileNotFoundException">指定したパスが見つかりませんでした。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTime" /> は、この操作で許可される日付または時刻の範囲を超える値を指定します。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastAccessTimeUtc(System.String,System.DateTime)">
      <summary>指定したファイルに最後にアクセスした日付と時刻を世界協定時刻 (UTC) で設定します。</summary>
      <param name="path">アクセス日時情報を設定する対象のファイル。</param>
      <param name="lastAccessTimeUtc">
        <see cref="T:System.DateTime" /> に最後にアクセスした日付と時刻の設定値を含む <paramref name="path" />。この値は UTC 時刻で表現されます。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.FileNotFoundException">指定したパスが見つかりませんでした。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTimeUtc" /> は、この操作で許可される日付または時刻の範囲を超える値を指定します。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastWriteTime(System.String,System.DateTime)">
      <summary>指定したファイルに最後に書き込んだ日付と時刻を設定します。</summary>
      <param name="path">日時情報を設定する対象のファイル。</param>
      <param name="lastWriteTime">
        <see cref="T:System.DateTime" /> に最後に書き込んだ日付と時刻の設定値を含む <paramref name="path" />。この値は現地時刻で表示されます。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.FileNotFoundException">指定したパスが見つかりませんでした。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTime" /> は、この操作で許可される日付または時刻の範囲を超える値を指定します。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastWriteTimeUtc(System.String,System.DateTime)">
      <summary>指定したファイルに最後に書き込んだ日付と時刻を世界協定時刻 (UTC) で設定します。</summary>
      <param name="path">日時情報を設定する対象のファイル。</param>
      <param name="lastWriteTimeUtc">
        <see cref="T:System.DateTime" /> に最後に書き込んだ日付と時刻の設定値を含む <paramref name="path" />。この値は UTC 時刻で表現されます。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.FileNotFoundException">指定したパスが見つかりませんでした。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTimeUtc" /> は、この操作で許可される日付または時刻の範囲を超える値を指定します。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.WriteAllBytes(System.String,System.Byte[])">
      <summary>新しいファイルを作成し、指定したバイト配列をそのファイルに書き込んだ後、ファイルを閉じます。既存のターゲット ファイルは上書きされます。</summary>
      <param name="path">書き込み先のファイル。</param>
      <param name="bytes">ファイルに書き込むバイト。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> が null か、バイト配列が空です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.IO.IOException">ファイルを開くときに、I/O エラーが発生しました。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> によって、読み取り専用のファイルが指定されました。または この操作は、現在のプラットフォームではサポートされていません。または <paramref name="path" /> によってディレクトリが指定されました。または 呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.WriteAllLines(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>新しいファイルを作成し、文字列のコレクションをそのファイルに書き込んでから、そのファイルを閉じます。</summary>
      <param name="path">書き込み先のファイル。</param>
      <param name="contents">ファイルに書き込む行。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 長さゼロの文字列は、空白文字だけが含まれています。 またはで定義されている 1 つ以上の無効な文字が含まれている、 <see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドです。</exception>
      <exception cref="T:System.ArgumentNullException">いずれか<paramref name=" path " />または <paramref name="contents" /> は nullです。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> が無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.IO.IOException">ファイルを開くときに、I/O エラーが発生しました。</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> が、システムで定義されている最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> が、読み取り専用のファイルを指定しています。またはこの操作は、現在のプラットフォームではサポートされていません。または<paramref name="path" /> がディレクトリです。または呼び出し元に、必要なアクセス許可がありません。</exception>
    </member>
    <member name="M:System.IO.File.WriteAllLines(System.String,System.Collections.Generic.IEnumerable{System.String},System.Text.Encoding)">
      <summary>指定されたエンコーディングを使用して新しいファイルを作成し、文字列のコレクションをそのファイルに書き込んでから、そのファイルを閉じます。</summary>
      <param name="path">書き込み先のファイル。</param>
      <param name="contents">ファイルに書き込む行。</param>
      <param name="encoding">使用する文字エンコーディング。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 長さゼロの文字列は、空白文字だけが含まれています。 またはで定義されている 1 つ以上の無効な文字が含まれている、 <see cref="M:System.IO.Path.GetInvalidPathChars" /> メソッドです。</exception>
      <exception cref="T:System.ArgumentNullException">いずれか<paramref name=" path" />,、<paramref name=" contents" />, 、または <paramref name="encoding" /> は nullです。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> が無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.IO.IOException">ファイルを開くときに、I/O エラーが発生しました。</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> が、システムで定義されている最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> が、読み取り専用のファイルを指定しています。またはこの操作は、現在のプラットフォームではサポートされていません。または<paramref name="path" /> がディレクトリです。または呼び出し元に、必要なアクセス許可がありません。</exception>
    </member>
    <member name="M:System.IO.File.WriteAllText(System.String,System.String)">
      <summary>新しいファイルを作成し、指定した文字列をそのファイルに書き込んだ後、ファイルを閉じます。既存のターゲット ファイルは上書きされます。</summary>
      <param name="path">書き込み先のファイル。</param>
      <param name="contents">ファイルに書き込む文字列。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> が null であるか、<paramref name="contents" /> が空です。 </exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.IO.IOException">ファイルを開くときに、I/O エラーが発生しました。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> によって、読み取り専用のファイルが指定されました。または この操作は、現在のプラットフォームではサポートされていません。または <paramref name="path" /> によってディレクトリが指定されました。または 呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.WriteAllText(System.String,System.String,System.Text.Encoding)">
      <summary>新しいファイルを作成し、指定したエンコーディングで指定の文字列をそのファイルに書き込んだ後、ファイルを閉じます。既存のターゲット ファイルは上書きされます。</summary>
      <param name="path">書き込み先のファイル。</param>
      <param name="contents">ファイルに書き込む文字列。</param>
      <param name="encoding">文字列に適用するエンコーディング。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が長さ 0 の文字列であるか、空白しか含んでいないか、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> が null であるか、<paramref name="contents" /> が空です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.IO.IOException">ファイルを開くときに、I/O エラーが発生しました。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> によって、読み取り専用のファイルが指定されました。または この操作は、現在のプラットフォームではサポートされていません。または <paramref name="path" /> によってディレクトリが指定されました。または 呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> の形式が無効です。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.IO.FileInfo">
      <summary>ファイルを作成、コピー、削除、移動、および開くためのプロパティおよびインスタンス メソッドを提供し、<see cref="T:System.IO.FileStream" /> オブジェクトを作成できるようにします。このクラスは継承できません。この種類の .NET Framework ソース コードを参照して、次を参照してください。、 Reference Sourceです。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileInfo.#ctor(System.String)">
      <summary>ファイル パスのラッパーとして機能する、<see cref="T:System.IO.FileInfo" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="fileName">新しいファイルの完全修飾名または相対ファイル名。パスの末尾がディレクトリの区切り記号にならないようにしてください。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="fileName" /> は null です。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.ArgumentException">ファイル名が空か、空白文字だけを含んでいるか、無効な文字を含んでいます。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="fileName" /> へのアクセスが拒否されました。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="fileName" /> の文字列の中に、コロン (:) が含まれています。</exception>
    </member>
    <member name="M:System.IO.FileInfo.AppendText">
      <summary>
        <see cref="T:System.IO.StreamWriter" /> のインスタンスが表すファイルの末尾にテキストを追加する <see cref="T:System.IO.FileInfo" /> を作成します。</summary>
      <returns>新しい StreamWriter。</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.CopyTo(System.String)">
      <summary>既存のファイルを上書きできないようにして、既存のファイルを新しいファイルにコピーします。</summary>
      <returns>絶対パスで指定された新しいファイル。</returns>
      <param name="destFileName">コピー先の新しいファイルの名前。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destFileName" /> が空か、空白だけが含まれているか、または無効な文字が含まれています。</exception>
      <exception cref="T:System.IO.IOException">エラーが発生したか、コピー先のファイルが既に存在しています。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destFileName" /> は null です。</exception>
      <exception cref="T:System.UnauthorizedAccessException">ディレクトリ パスが渡されたか、ファイルを異なるドライブに移動しようとしています。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="destFileName" /> 内に指定されているディレクトリが存在しません。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destFileName" /> は文字列にコロン (:) が含まれていますが、ボリュームが指定されていません。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.CopyTo(System.String,System.Boolean)">
      <summary>既存のファイルを上書きできるようにして、既存のファイルを新しいファイルにコピーします。</summary>
      <returns>新しいファイル。または、<paramref name="overwrite" /> が true の場合は、既存のファイルを上書きしたファイル。ファイルが存在しており、<paramref name="overwrite" /> が false の場合は、<see cref="T:System.IO.IOException" /> がスローされます。</returns>
      <param name="destFileName">コピー先の新しいファイルの名前。</param>
      <param name="overwrite">既存のファイルを上書きできるようにする場合は true。それ以外の場合は false。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destFileName" /> が空か、空白だけが含まれているか、または無効な文字が含まれています。</exception>
      <exception cref="T:System.IO.IOException">エラーが発生したか、または、コピー先のファイルが既に存在しているけれども <paramref name="overwrite" /> が false です。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destFileName" /> は null です。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="destFileName" /> 内に指定されているディレクトリが存在しません。</exception>
      <exception cref="T:System.UnauthorizedAccessException">ディレクトリ パスが渡されたか、ファイルを異なるドライブに移動しようとしています。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destFileName" /> の文字列の中に、コロン (:) が含まれています。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Create">
      <summary>ファイルを作成します。</summary>
      <returns>新しいファイル。</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.CreateText">
      <summary>新しいテキスト ファイルに書き込みを行う <see cref="T:System.IO.StreamWriter" /> を作成します。</summary>
      <returns>新しい StreamWriter。</returns>
      <exception cref="T:System.UnauthorizedAccessException">指定したファイル名はディレクトリです。</exception>
      <exception cref="T:System.IO.IOException">ディスクが読み取り専用です。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Delete">
      <summary>ファイルを完全に削除します。</summary>
      <exception cref="T:System.IO.IOException">Microsoft Windows NT が稼動しているコンピューター上で、対象のファイルが開いているか、そのファイルにメモリが割り当てられています。または開いているハンドルがファイルに対して存在し、オペレーティング システムが Windows XP またはそれ以前のものです。開いているハンドルは、ディレクトリとファイルを列挙する際に発生した可能性があります。詳細については、「方法: ディレクトリとファイルを列挙する」を参照してください。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.UnauthorizedAccessException">指定したパスはディレクトリです。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.Directory">
      <summary>親ディレクトリのインスタンスを取得します。</summary>
      <returns>ファイルの親ディレクトリを表す <see cref="T:System.IO.DirectoryInfo" /> オブジェクト。</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">割り当てられていないドライブであるなど、指定されたパスが無効です。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.DirectoryName">
      <summary>ディレクトリの絶対パスを表す文字列を取得します。</summary>
      <returns>ディレクトリの絶対パスを表す文字列。</returns>
      <exception cref="T:System.ArgumentNullException">ディレクトリ名として null が渡されました。</exception>
      <exception cref="T:System.IO.PathTooLongException">ファイルの完全修飾パスが 260 文字以上です。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.Exists">
      <summary>ファイルが存在するかどうかを示す値を取得します。</summary>
      <returns>ファイルが存在する場合は true。ファイルが存在しないか、ファイルがディレクトリである場合は false。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileInfo.IsReadOnly">
      <summary>現在のファイルが読み取り専用であるかどうかを判断する値を取得または設定します。</summary>
      <returns>現在のファイルが読み取り専用の場合は true。それ以外の場合は false。</returns>
      <exception cref="T:System.IO.FileNotFoundException">現在の <see cref="T:System.IO.FileInfo" /> オブジェクトが示すファイルが見つかりませんでした。</exception>
      <exception cref="T:System.IO.IOException">ファイルを開くときに、I/O エラーが発生しました。</exception>
      <exception cref="T:System.UnauthorizedAccessException">この操作は、現在のプラットフォームではサポートされていません。または 呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.ArgumentException">ユーザーには書き込みの権限がありませんが、このプロパティを false に設定しようとしました。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileInfo.Length">
      <summary>現在のファイルのサイズをバイト単位で取得します。</summary>
      <returns>現在のファイルのサイズ (バイト単位)。</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> によってファイルまたはディレクトリの状態を更新できません。</exception>
      <exception cref="T:System.IO.FileNotFoundException">このファイルは存在しません。または Length プロパティはディレクトリに対して呼び出されます。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileInfo.MoveTo(System.String)">
      <summary>指定したファイルを新しい場所に移動します。オプションで新しいファイル名を指定することもできます。</summary>
      <param name="destFileName">ファイルの移動先のパス。異なるファイル名を指定できます。</param>
      <exception cref="T:System.IO.IOException">I/O エラーが発生しました。移動先のファイルが既に存在するか、移動先のデバイスの準備ができていない、などの原因が考えられます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destFileName" /> は null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="destFileName" /> が空か、空白だけが含まれているか、または無効な文字が含まれています。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="destFileName" /> が読み取り専用か、またはディレクトリです。</exception>
      <exception cref="T:System.IO.FileNotFoundException">ファイルが見つかりません。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">割り当てられていないドライブであるなど、指定されたパスが無効です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destFileName" /> の文字列の中に、コロン (:) が含まれています。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.Name">
      <summary>ファイルの名前を取得します。</summary>
      <returns>ファイルの名前です。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode)">
      <summary>指定したモードでファイルを開きます。</summary>
      <returns>読み書き可能、共有不可の権限を適用し、指定したモードで開くファイル。</returns>
      <param name="mode">ファイルを開くときのモード (<see cref="T:System.IO.FileMode" />、Open など) を指定する定数 Append。</param>
      <exception cref="T:System.IO.FileNotFoundException">ファイルが見つかりません。</exception>
      <exception cref="T:System.UnauthorizedAccessException">ファイルが読み取り専用か、ディレクトリです。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">割り当てられていないドライブであるなど、指定されたパスが無効です。</exception>
      <exception cref="T:System.IO.IOException">ファイルは既に開いています。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode,System.IO.FileAccess)">
      <summary>読み取り可、書き込み可、または読み書き込み可などのアクセス権を指定し、指定したモードでファイルを開きます。</summary>
      <returns>指定したモードとアクセス権、および非共有の権限で開く <see cref="T:System.IO.FileStream" /> オブジェクト。</returns>
      <param name="mode">ファイルを開くときのモード (<see cref="T:System.IO.FileMode" />、Open など) を指定する定数 Append。</param>
      <param name="access">ファイルを <see cref="T:System.IO.FileAccess" />、Read、または Write のいずれのアクセス権で開くかを指定する ReadWrite 定数。</param>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.IO.FileNotFoundException">ファイルが見つかりません。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> が読み取り専用か、またはディレクトリです。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">割り当てられていないドライブであるなど、指定されたパスが無効です。</exception>
      <exception cref="T:System.IO.IOException">ファイルは既に開いています。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>読み取り可、書き込み可、読み書き可などのアクセス権を指定し、指定したモードと共有オプションでファイルを開きます。</summary>
      <returns>指定したモード、アクセス権、および共有オプションで開く <see cref="T:System.IO.FileStream" /> オブジェクト。</returns>
      <param name="mode">ファイルを開くときのモード (<see cref="T:System.IO.FileMode" />、Open など) を指定する定数 Append。</param>
      <param name="access">ファイルを <see cref="T:System.IO.FileAccess" />、Read、または Write のいずれのアクセス権で開くかを指定する ReadWrite 定数。</param>
      <param name="share">ファイルに対して他の <see cref="T:System.IO.FileShare" /> オブジェクトが持つアクセスの種類を指定する FileStream 定数。</param>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.IO.FileNotFoundException">ファイルが見つかりません。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> が読み取り専用か、またはディレクトリです。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">割り当てられていないドライブであるなど、指定されたパスが無効です。</exception>
      <exception cref="T:System.IO.IOException">ファイルは既に開いています。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.OpenRead">
      <summary>読み取り専用の <see cref="T:System.IO.FileStream" /> を作成します。</summary>
      <returns>新しい読み取り専用の <see cref="T:System.IO.FileStream" /> オブジェクト。</returns>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> が読み取り専用か、またはディレクトリです。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">割り当てられていないドライブであるなど、指定されたパスが無効です。</exception>
      <exception cref="T:System.IO.IOException">ファイルは既に開いています。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.OpenText">
      <summary>既存のテキスト ファイルからの読み取りを行う、UTF8 エンコーディングの <see cref="T:System.IO.StreamReader" /> を作成します。</summary>
      <returns>UTF8 エンコーディングの新しい StreamReader。</returns>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.IO.FileNotFoundException">ファイルが見つかりません。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> が読み取り専用か、またはディレクトリです。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">割り当てられていないドライブであるなど、指定されたパスが無効です。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.OpenWrite">
      <summary>書き込み専用の <see cref="T:System.IO.FileStream" /> を作成します。</summary>
      <returns>新規または既存のファイルの書き込み専用の非共有 <see cref="T:System.IO.FileStream" /> オブジェクト。</returns>
      <exception cref="T:System.UnauthorizedAccessException">
        <see cref="T:System.IO.FileInfo" /> オブジェクトのインスタンス作成時に指定されたパスが、読み取り専用か、またはディレクトリです。 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <see cref="T:System.IO.FileInfo" /> オブジェクトのインスタンス作成時に指定されたパスが無効である (割り当てられていないドライブが指定されている場合など)。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.ToString">
      <summary>このパスを文字列として返します。</summary>
      <returns>パスを表す文字列。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.FileOptions">
      <summary>
        <see cref="T:System.IO.FileStream" /> オブジェクトを作成するための高度なオプションを表します。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.FileOptions.Asynchronous">
      <summary>ファイルを非同期の読み取り用および書き込み用に使用できることを示します。</summary>
    </member>
    <member name="F:System.IO.FileOptions.DeleteOnClose">
      <summary>ファイルは使用しなくなった時点で自動的に削除されることを示します。</summary>
    </member>
    <member name="F:System.IO.FileOptions.Encrypted">
      <summary>ファイルは暗号化され、暗号化に使用したのと同じユーザー アカウントを使用する場合のみ復号化できることを示します。</summary>
    </member>
    <member name="F:System.IO.FileOptions.None">
      <summary>
        <see cref="T:System.IO.FileStream" /> オブジェクトを作成するときに追加のオプションを使用する必要がないことを示します。</summary>
    </member>
    <member name="F:System.IO.FileOptions.RandomAccess">
      <summary>ファイルがランダムにアクセスされることを示します。システムは、これをファイル キャッシングを最適化するためのヒントとして使用できます。</summary>
    </member>
    <member name="F:System.IO.FileOptions.SequentialScan">
      <summary>ファイルは先頭から末尾まで順次アクセスされることを示します。システムは、これをファイル キャッシングを最適化するためのヒントとして使用できます。アプリケーションがランダム アクセスのファイル ポインターを移動させると、最適なキャッシングが行われない場合があります。ただし、操作は引き続き正常に実行されます。</summary>
    </member>
    <member name="F:System.IO.FileOptions.WriteThrough">
      <summary>システムが中間キャッシュを使用して書き込みを行い、直接ディスクに移動することを示します。</summary>
    </member>
    <member name="T:System.IO.FileStream">
      <summary>同期および非同期の読み取り操作と書き込み操作をサポートするファイル用の <see cref="T:System.IO.Stream" /> を提供します。この種類の .NET Framework ソース コードを参照して、次を参照してください。、 Reference Sourceです。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.#ctor(Microsoft.Win32.SafeHandles.SafeFileHandle,System.IO.FileAccess)">
      <summary>
        <see cref="T:System.IO.FileStream" /> クラスの新しいインスタンスを、指定した読み取り/書き込みアクセス許可を使用して、指定したファイル ハンドル用に初期化します。</summary>
      <param name="handle">現在の FileStream オブジェクトによってカプセル化されるファイルのファイル ハンドル。</param>
      <param name="access">FileStream オブジェクトの <see cref="P:System.IO.FileStream.CanRead" /> プロパティと <see cref="P:System.IO.FileStream.CanWrite" /> プロパティを設定する定数。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="access" /> が <see cref="T:System.IO.FileAccess" /> のフィールドではありません。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.IO.IOException">ディスク エラーなどの I/O エラーが発生しました。またはストリームは閉じられています。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="access" /> が <paramref name="access" /> または Write であるのに、ファイル ハンドルが読み取り専用に設定されているなど、指定したファイル ハンドルに対する ReadWrite 要求がオペレーティング システムで許可されません。</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(Microsoft.Win32.SafeHandles.SafeFileHandle,System.IO.FileAccess,System.Int32)">
      <summary>
        <see cref="T:System.IO.FileStream" /> クラスの新しいインスタンスを、指定した読み取り/書き込みアクセス許可、およびバッファー サイズを使用して、指定したファイル ハンドル用に初期化します。</summary>
      <param name="handle">現在の FileStream オブジェクトによってカプセル化されるファイルのファイル ハンドル。</param>
      <param name="access">FileStream オブジェクトの <see cref="P:System.IO.FileStream.CanRead" /> プロパティと <see cref="P:System.IO.FileStream.CanWrite" /> プロパティを設定する <see cref="T:System.IO.FileAccess" /> 定数。</param>
      <param name="bufferSize">バッファー サイズを示す 0 より大きな正の <see cref="T:System.Int32" /> 値。既定のバッファー サイズは、4096 です。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="handle" /> パラメーターが無効なハンドルです。または<paramref name="handle" /> パラメーターが同期ハンドルであるのに、非同期的に使用されました。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> パラメーターが負の値です。</exception>
      <exception cref="T:System.IO.IOException">ディスク エラーなどの I/O エラーが発生しました。またはストリームは閉じられています。 </exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="access" /> が <paramref name="access" /> または Write であるのに、ファイル ハンドルが読み取り専用に設定されているなど、指定したファイル ハンドルに対する ReadWrite 要求がオペレーティング システムで許可されません。</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(Microsoft.Win32.SafeHandles.SafeFileHandle,System.IO.FileAccess,System.Int32,System.Boolean)">
      <summary>
        <see cref="T:System.IO.FileStream" /> クラスの新しいインスタンスを、指定した読み取り/書き込みアクセス許可、バッファー サイズ、および同期状態または非同期状態を使用して、指定したファイル ハンドル用に初期化します。</summary>
      <param name="handle">この FileStream オブジェクトによってカプセル化されるファイルのファイル ハンドル。</param>
      <param name="access">FileStream オブジェクトの <see cref="P:System.IO.FileStream.CanRead" /> プロパティと <see cref="P:System.IO.FileStream.CanWrite" /> プロパティを設定する定数。</param>
      <param name="bufferSize">バッファー サイズを示す 0 より大きな正の <see cref="T:System.Int32" /> 値。既定のバッファー サイズは、4096 です。</param>
      <param name="isAsync">ハンドルが非同期的に開かれた場合 (重複 I/O モード用) は true。それ以外の場合は false。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="handle" /> パラメーターが無効なハンドルです。または<paramref name="handle" /> パラメーターが同期ハンドルであるのに、非同期的に使用されました。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> パラメーターが負の値です。</exception>
      <exception cref="T:System.IO.IOException">ディスク エラーなどの I/O エラーが発生しました。またはストリームは閉じられています。 </exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="access" /> が <paramref name="access" /> または Write であるのに、ファイル ハンドルが読み取り専用に設定されているなど、指定したファイル ハンドルに対する ReadWrite 要求がオペレーティング システムで許可されません。</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode)">
      <summary>
        <see cref="T:System.IO.FileStream" /> クラスの新しいインスタンスを、指定したパスと作成モードを使用して初期化します。</summary>
      <param name="path">現在の FileStream オブジェクトによってカプセル化されるファイルの相対パスまたは絶対パス。</param>
      <param name="mode">ファイルを開く方法または作成する方法を決定する定数。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が空の文字列 ("") か、空白しか含んでいないか、無効な文字を 1 つ以上含んでいます。または<paramref name="path" /> など、ファイル以外のデバイスへの参照"con:"、"com1:"、"lpt1:"などです。環境では、NTFS。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> など、ファイル以外のデバイスへの参照"con:"、"com1:"、"lpt1:"などです。環境では、NTFS 以外。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.IO.FileNotFoundException">ファイルが見つかりません。たとえば、<paramref name="mode" /> が FileMode.Truncate または FileMode.Open の場合に、<paramref name="path" /> で指定されたファイルが存在しません。これらのモードでは、ファイルが既に存在している必要があります。</exception>
      <exception cref="T:System.IO.IOException">FileMode.CreateNew で指定したファイルが既に存在している場合に <paramref name="path" /> が指定されているなどの I/O エラーが発生しました。またはストリームは閉じられています。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">割り当てられていないドライブであるなど、指定されたパスが無効です。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> に無効な値が含まれています。</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess)">
      <summary>
        <see cref="T:System.IO.FileStream" /> クラスの新しいインスタンスを、指定したパス、作成モード、および読み取り/書き込みアクセス許可を使用して初期化します。</summary>
      <param name="path">現在の FileStream オブジェクトによってカプセル化されるファイルの相対パスまたは絶対パス。</param>
      <param name="mode">ファイルを開く方法または作成する方法を決定する定数。</param>
      <param name="access">FileStream オブジェクトがファイルにアクセスできる方法を決定する定数。これにより、FileStream オブジェクトの <see cref="P:System.IO.FileStream.CanRead" /> および <see cref="P:System.IO.FileStream.CanWrite" /> プロパティによって返される値も決まります。<paramref name="path" /> がディスク ファイルを指定している場合、<see cref="P:System.IO.FileStream.CanSeek" /> は true になります。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が空の文字列 ("") か、空白しか含んでいないか、無効な文字を 1 つ以上含んでいます。または<paramref name="path" /> など、ファイル以外のデバイスへの参照"con:"、"com1:"、"lpt1:"などです。環境では、NTFS。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> など、ファイル以外のデバイスへの参照"con:"、"com1:"、"lpt1:"などです。環境では、NTFS 以外。</exception>
      <exception cref="T:System.IO.FileNotFoundException">ファイルが見つかりません。たとえば、<paramref name="mode" /> が FileMode.Truncate または FileMode.Open の場合に、<paramref name="path" /> で指定されたファイルが存在しません。これらのモードでは、ファイルが既に存在している必要があります。</exception>
      <exception cref="T:System.IO.IOException">FileMode.CreateNew で指定したファイルが既に存在している場合に <paramref name="path" /> が指定されているなどの I/O エラーが発生しました。またはストリームは閉じられています。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">割り当てられていないドライブであるなど、指定されたパスが無効です。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="access" /> が <paramref name="path" /> または <paramref name="access" /> であるのに、ファイルまたはディレクトリが読み取り専用に設定されているなど、指定した Write に対する ReadWrite 要求がオペレーティング システムで許可されません。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> に無効な値が含まれています。</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>
        <see cref="T:System.IO.FileStream" /> クラスの新しいインスタンスを、指定したパス、作成モード、読み取り/書き込みアクセス許可、および共有アクセス許可を使用して初期化します。</summary>
      <param name="path">現在の FileStream オブジェクトによってカプセル化されるファイルの相対パスまたは絶対パス。</param>
      <param name="mode">ファイルを開く方法または作成する方法を決定する定数。</param>
      <param name="access">FileStream オブジェクトがファイルにアクセスできる方法を決定する定数。これにより、FileStream オブジェクトの <see cref="P:System.IO.FileStream.CanRead" /> および <see cref="P:System.IO.FileStream.CanWrite" /> プロパティによって返される値も決まります。<paramref name="path" /> がディスク ファイルを指定している場合、<see cref="P:System.IO.FileStream.CanSeek" /> は true になります。</param>
      <param name="share">プロセスによるファイルの共有方法を決定する定数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が空の文字列 ("") か、空白しか含んでいないか、無効な文字を 1 つ以上含んでいます。または<paramref name="path" /> など、ファイル以外のデバイスへの参照"con:"、"com1:"、"lpt1:"などです。環境では、NTFS。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> など、ファイル以外のデバイスへの参照"con:"、"com1:"、"lpt1:"などです。環境では、NTFS 以外。</exception>
      <exception cref="T:System.IO.FileNotFoundException">ファイルが見つかりません。たとえば、<paramref name="mode" /> が FileMode.Truncate または FileMode.Open の場合に、<paramref name="path" /> で指定されたファイルが存在しません。これらのモードでは、ファイルが既に存在している必要があります。</exception>
      <exception cref="T:System.IO.IOException">FileMode.CreateNew で指定したファイルが既に存在している場合に <paramref name="path" /> が指定されているなどの I/O エラーが発生しました。またはシステムで Windows 98 または Windows 98 Second Edition を実行しており、<paramref name="share" /> が FileShare.Delete に設定されています。またはストリームは閉じられています。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">割り当てられていないドライブであるなど、指定されたパスが無効です。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="access" /> が <paramref name="path" /> または <paramref name="access" /> であるのに、ファイルまたはディレクトリが読み取り専用に設定されているなど、指定した Write に対する ReadWrite 要求がオペレーティング システムで許可されません。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> に無効な値が含まれています。</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Int32)">
      <summary>
        <see cref="T:System.IO.FileStream" /> クラスの新しいインスタンスを、指定したパス、作成モード、読み取り/書き込みアクセス許可、共有アクセス許可、およびバッファー サイズを使用して初期化します。</summary>
      <param name="path">現在の FileStream オブジェクトによってカプセル化されるファイルの相対パスまたは絶対パス。</param>
      <param name="mode">ファイルを開く方法または作成する方法を決定する定数。</param>
      <param name="access">FileStream オブジェクトがファイルにアクセスできる方法を決定する定数。これにより、FileStream オブジェクトの <see cref="P:System.IO.FileStream.CanRead" /> および <see cref="P:System.IO.FileStream.CanWrite" /> プロパティによって返される値も決まります。<paramref name="path" /> がディスク ファイルを指定している場合、<see cref="P:System.IO.FileStream.CanSeek" /> は true になります。</param>
      <param name="share">プロセスによるファイルの共有方法を決定する定数。</param>
      <param name="bufferSize">バッファー サイズを示す 0 より大きな正の <see cref="T:System.Int32" /> 値。既定のバッファー サイズは、4096 です。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が空の文字列 ("") か、空白しか含んでいないか、無効な文字を 1 つ以上含んでいます。または<paramref name="path" /> など、ファイル以外のデバイスへの参照"con:"、"com1:"、"lpt1:"などです。環境では、NTFS。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> など、ファイル以外のデバイスへの参照"con:"、"com1:"、"lpt1:"などです。環境では、NTFS 以外。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> が負の値またはゼロです。または <paramref name="mode" />、<paramref name="access" />、または <paramref name="share" /> に無効な値が含まれています。</exception>
      <exception cref="T:System.IO.FileNotFoundException">ファイルが見つかりません。たとえば、<paramref name="mode" /> が FileMode.Truncate または FileMode.Open の場合に、<paramref name="path" /> で指定されたファイルが存在しません。これらのモードでは、ファイルが既に存在している必要があります。</exception>
      <exception cref="T:System.IO.IOException">FileMode.CreateNew で指定したファイルが既に存在している場合に <paramref name="path" /> が指定されているなどの I/O エラーが発生しました。またはシステムで Windows 98 または Windows 98 Second Edition を実行しており、<paramref name="share" /> が FileShare.Delete に設定されています。またはストリームは閉じられています。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">割り当てられていないドライブであるなど、指定されたパスが無効です。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="access" /> が <paramref name="path" /> または <paramref name="access" /> であるのに、ファイルまたはディレクトリが読み取り専用に設定されているなど、指定した Write に対する ReadWrite 要求がオペレーティング システムで許可されません。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Int32,System.Boolean)">
      <summary>
        <see cref="T:System.IO.FileStream" /> クラスの新しいインスタンスを、指定したパス、作成モード、読み取り/書き込みアクセス許可、共有アクセス許可、バッファー サイズ、および同期状態または非同期状態を使用して初期化します。</summary>
      <param name="path">現在の FileStream オブジェクトによってカプセル化されるファイルの相対パスまたは絶対パス。</param>
      <param name="mode">ファイルを開く方法または作成する方法を決定する定数。</param>
      <param name="access">FileStream オブジェクトがファイルにアクセスできる方法を決定する定数。これにより、FileStream オブジェクトの <see cref="P:System.IO.FileStream.CanRead" /> および <see cref="P:System.IO.FileStream.CanWrite" /> プロパティによって返される値も決まります。<paramref name="path" /> がディスク ファイルを指定している場合、<see cref="P:System.IO.FileStream.CanSeek" /> は true になります。</param>
      <param name="share">プロセスによるファイルの共有方法を決定する定数。</param>
      <param name="bufferSize">バッファー サイズを示す 0 より大きな正の <see cref="T:System.Int32" /> 値。既定のバッファー サイズは、4096 です。</param>
      <param name="useAsync">非同期 I/O または同期 I/O のどちらを使用するかを指定します。ただし、基になるオペレーティング システムが非同期 I/O をサポートしていないことがあります。したがって、true を指定しても、プラットフォームによってはハンドルが同期的に開かれることがあります。非同期的に開いた場合、<see cref="M:System.IO.FileStream.BeginRead(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)" /> メソッドと <see cref="M:System.IO.FileStream.BeginWrite(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)" /> メソッドは、大量の読み取りまたは書き込み時にはパフォーマンスがより高くなりますが、少量の読み取りまたは書き込み時にはパフォーマンスが非常に低くなることがあります。アプリケーションが非同期 I/O を利用するように設計されている場合は、<paramref name="useAsync" /> パラメーターを true に設定します。非同期 I/O を正しく使用すると、アプリケーションが 10 倍ほど高速化することがあります。ただし、非同期 I/O 用にアプリケーションを再設計せずに非同期 I/O を使用すると、パフォーマンスが 10 分の 1 ほど低下することがあります。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が空の文字列 ("") か、空白しか含んでいないか、無効な文字を 1 つ以上含んでいます。または<paramref name="path" /> など、ファイル以外のデバイスへの参照"con:"、"com1:"、"lpt1:"などです。環境では、NTFS。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> など、ファイル以外のデバイスへの参照"con:"、"com1:"、"lpt1:"などです。環境では、NTFS 以外。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> が負の値またはゼロです。または <paramref name="mode" />、<paramref name="access" />、または <paramref name="share" /> に無効な値が含まれています。</exception>
      <exception cref="T:System.IO.FileNotFoundException">ファイルが見つかりません。たとえば、<paramref name="mode" /> が FileMode.Truncate または FileMode.Open の場合に、<paramref name="path" /> で指定されたファイルが存在しません。これらのモードでは、ファイルが既に存在している必要があります。</exception>
      <exception cref="T:System.IO.IOException">FileMode.CreateNew で指定したファイルが既に存在している場合に <paramref name="path" /> が指定されているなどの I/O エラーが発生しました。または システムで Windows 98 または Windows 98 Second Edition を実行しており、<paramref name="share" /> が FileShare.Delete に設定されています。またはストリームは閉じられています。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">割り当てられていないドライブであるなど、指定されたパスが無効です。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="access" /> が <paramref name="path" /> または <paramref name="access" /> であるのに、ファイルまたはディレクトリが読み取り専用に設定されているなど、指定した Write に対する ReadWrite 要求がオペレーティング システムで許可されません。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Int32,System.IO.FileOptions)">
      <summary>
        <see cref="T:System.IO.FileStream" /> クラスの新しいインスタンスを、指定したパス、作成モード、読み取り/書き込みアクセス許可、共有アクセス許可、同一のファイルに対して他の FileStream が保有できるアクセス、バッファー サイズ、および追加のファイル オプションを使用して初期化します。</summary>
      <param name="path">現在の FileStream オブジェクトによってカプセル化されるファイルの相対パスまたは絶対パス。</param>
      <param name="mode">ファイルを開く方法または作成する方法を決定する定数。</param>
      <param name="access">FileStream オブジェクトがファイルにアクセスできる方法を決定する定数。これにより、FileStream オブジェクトの <see cref="P:System.IO.FileStream.CanRead" /> および <see cref="P:System.IO.FileStream.CanWrite" /> プロパティによって返される値も決まります。<paramref name="path" /> がディスク ファイルを指定している場合、<see cref="P:System.IO.FileStream.CanSeek" /> は true になります。</param>
      <param name="share">プロセスによるファイルの共有方法を決定する定数。</param>
      <param name="bufferSize">バッファー サイズを示す 0 より大きな正の <see cref="T:System.Int32" /> 値。既定のバッファー サイズは、4096 です。</param>
      <param name="options">追加のファイル オプションを指定する値。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が空の文字列 ("") か、空白しか含んでいないか、無効な文字を 1 つ以上含んでいます。または<paramref name="path" /> など、ファイル以外のデバイスへの参照"con:"、"com1:"、"lpt1:"などです。環境では、NTFS。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> など、ファイル以外のデバイスへの参照"con:"、"com1:"、"lpt1:"などです。環境では、NTFS 以外。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> が負の値またはゼロです。または <paramref name="mode" />、<paramref name="access" />、または <paramref name="share" /> に無効な値が含まれています。</exception>
      <exception cref="T:System.IO.FileNotFoundException">ファイルが見つかりません。たとえば、<paramref name="mode" /> が FileMode.Truncate または FileMode.Open の場合に、<paramref name="path" /> で指定されたファイルが存在しません。これらのモードでは、ファイルが既に存在している必要があります。</exception>
      <exception cref="T:System.IO.IOException">FileMode.CreateNew で指定したファイルが既に存在している場合に <paramref name="path" /> が指定されているなどの I/O エラーが発生しました。またはストリームは閉じられています。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">割り当てられていないドライブであるなど、指定されたパスが無効です。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="access" /> が <paramref name="path" /> または <paramref name="access" /> であるのに、ファイルまたはディレクトリが読み取り専用に設定されているなど、指定した Write に対する ReadWrite 要求がオペレーティング システムで許可されません。または<see cref="F:System.IO.FileOptions.Encrypted" /> が <paramref name="options" /> に対して指定されていますが、ファイル暗号化は現在のプラットフォームでサポートされていません。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
    </member>
    <member name="P:System.IO.FileStream.CanRead">
      <summary>現在のストリームが読み取りをサポートしているかどうかを示す値を取得します。</summary>
      <returns>ストリームが読み取りをサポートしている場合は true。ストリームが閉じているか、書き込み専用アクセスで開かれた場合は false。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.CanSeek">
      <summary>現在のストリームがシークをサポートしているかどうかを示す値を取得します。</summary>
      <returns>ストリームがシークをサポートしている場合は true。ストリームが閉じているか、FileStream がパイプまたはコンソール出力などのオペレーティング システム ハンドルから構築された場合は false。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.CanWrite">
      <summary>現在のストリームが書き込みをサポートしているかどうかを示す値を取得します。</summary>
      <returns>ストリームが書き込みをサポートしている場合は true。ストリームが閉じているか、読み取り専用アクセスで開かれた場合は false。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.IO.FileStream" /> で使用されているアンマネージ リソースを解放し、オプションでマネージ リソースを解放します。</summary>
      <param name="disposing">マネージ リソースとアンマネージ リソースの両方を解放する場合は true。アンマネージ リソースだけを解放する場合は false。</param>
    </member>
    <member name="M:System.IO.FileStream.Finalize">
      <summary>ガベージ コレクターが FileStream を再利用するときに、リソースの解放およびその他のクリーンアップ操作を確実に実行するようにします。</summary>
    </member>
    <member name="M:System.IO.FileStream.Flush">
      <summary>このストリームのバッファーをクリアして、バッファー内のデータがファイルに書き込まれるようにします。</summary>
      <exception cref="T:System.IO.IOException">I/O エラーが発生しました。</exception>
      <exception cref="T:System.ObjectDisposedException">ストリームが閉じられました。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Flush(System.Boolean)">
      <summary>このストリームのバッファーをクリアして、バッファー内のデータがファイルに書き込まれるようにし、すべての中間ファイル バッファーもクリアします。</summary>
      <param name="flushToDisk">すべての中間ファイル バッファーをフラッシュする場合は true。それ以外の場合は false。</param>
    </member>
    <member name="M:System.IO.FileStream.FlushAsync(System.Threading.CancellationToken)">
      <summary>ストリームに対応するすべてのバッファーを非同期にクリアし、バッファー内のデータを基になるデバイスに書き込み、キャンセル要求を監視します。</summary>
      <returns>非同期のフラッシュ操作を表すタスク。</returns>
      <param name="cancellationToken">キャンセル要求を監視するためのトークン。</param>
      <exception cref="T:System.ObjectDisposedException">ストリームは破棄されています。</exception>
    </member>
    <member name="P:System.IO.FileStream.IsAsync">
      <summary>FileStream が非同期的に開かれたか、同期的に開かれたかを示す値を取得します。</summary>
      <returns>FileStream が非同期的に開かれた場合は true。それ以外の場合は false。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.Length">
      <summary>ストリーム長 (バイト単位) を取得します。</summary>
      <returns>ストリーム長 (バイト単位) を表す long 値。</returns>
      <exception cref="T:System.NotSupportedException">このストリーム用の <see cref="P:System.IO.FileStream.CanSeek" /> が false です。</exception>
      <exception cref="T:System.IO.IOException">ファイルが閉じられているなどの I/O エラーが発生しました。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.Name">
      <summary>コンストラクターに渡された FileStream の名前を取得します。</summary>
      <returns>FileStream の名前を示す文字列。</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileStream.Position">
      <summary>ストリームの現在位置を取得または設定します。</summary>
      <returns>ストリームの現在位置。</returns>
      <exception cref="T:System.NotSupportedException">ストリームがシークをサポートしていません。</exception>
      <exception cref="T:System.IO.IOException">I/O エラーが発生しました。またはWindows 98 以前で、位置がストリームの末尾を超えた非常に大きな値に設定されています。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">位置を負の値に設定しようとしました。</exception>
      <exception cref="T:System.IO.EndOfStreamException">シークをサポートしていないストリームの末尾を超える位置で、シークを試行しました。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>ストリームからバイトのブロックを読み取り、そのデータを特定のバッファーに書き込みます。</summary>
      <returns>バッファーに読み取られた合計バイト数。要求しただけのバイト数を読み取れなかった場合、この値は要求したバイト数より小さくなります。ストリームの末尾に到達した場合は 0 になることがあります。</returns>
      <param name="array">このメソッドが戻るとき、指定したバイト配列の <paramref name="offset" /> から (<paramref name="offset" /> + <paramref name="count" /> - 1<paramref name=")" />) までの値が、現在のソースから読み取られたバイトに置き換えられます。</param>
      <param name="offset">読み取られるバイトが配置される <paramref name="array" /> 内のバイト オフセット。</param>
      <param name="count">読み取る最大バイト数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> または <paramref name="count" /> が負の値です。</exception>
      <exception cref="T:System.NotSupportedException">ストリームは読み取りをサポートしません。</exception>
      <exception cref="T:System.IO.IOException">I/O エラーが発生しました。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> と <paramref name="count" /> が <paramref name="array" /> の無効な範囲を示しています。</exception>
      <exception cref="T:System.ObjectDisposedException">ストリームが閉じられた後でメソッドが呼び出されました。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.ReadAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>現在のストリームから非同期的にバイト シーケンスを非同期に読み取り、読み取ったバイト数だけストリーム内の位置を進め、キャンセル要求を監視します。</summary>
      <returns>非同期の読み取り操作を表すタスク。<paramref name="TResult" /> パラメーターの値には、バッファーに読み込まれるバイトの合計数が含まれます。現在使用できるバイト数が要求した数より小さい場合、結果の値は要求したバイト数より小さくなることがあります。また、ストリームの末尾に到達した場合は 0 になることがあります。</returns>
      <param name="buffer">データを書き込むバッファー。</param>
      <param name="offset">ストリームからのデータの書き込み開始位置を示す <paramref name="buffer" /> 内のバイト オフセット。</param>
      <param name="count">読み取る最大バイト数。</param>
      <param name="cancellationToken">キャンセル要求を監視するためのトークン。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> は null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> または <paramref name="count" /> が負の値です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> と <paramref name="count" /> の合計値が、バッファー長より大きい値です。</exception>
      <exception cref="T:System.NotSupportedException">ストリームは読み取りをサポートしません。</exception>
      <exception cref="T:System.ObjectDisposedException">ストリームは破棄されています。</exception>
      <exception cref="T:System.InvalidOperationException">ストリームは現在、前の読み取り操作で使用中です。</exception>
    </member>
    <member name="M:System.IO.FileStream.ReadByte">
      <summary>ファイルからバイトを読み取り、読み取り位置を 1 バイト進めます。</summary>
      <returns>
        <see cref="T:System.Int32" /> にキャストしたバイト。ストリームの末尾に達した場合は -1。</returns>
      <exception cref="T:System.NotSupportedException">現在のストリームは読み取りをサポートしていません。</exception>
      <exception cref="T:System.ObjectDisposedException">現在のストリームが閉じています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.SafeFileHandle">
      <summary>現在の <see cref="T:System.IO.FileStream" /> オブジェクトによってカプセル化されるファイルのオペレーティング システム ファイル ハンドルを表す <see cref="T:Microsoft.Win32.SafeHandles.SafeFileHandle" /> オブジェクトを取得します。</summary>
      <returns>現在の <see cref="T:System.IO.FileStream" /> オブジェクトによってカプセル化されるファイルのオペレーティング システム ファイル ハンドルを表すオブジェクト。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Seek(System.Int64,System.IO.SeekOrigin)">
      <summary>ストリームの現在位置を特定の値に設定します。</summary>
      <returns>ストリームの新しい位置。</returns>
      <param name="offset">シークの開始位置を示す、<paramref name="origin" /> に対する相対ポイント。</param>
      <param name="origin">
        <see cref="T:System.IO.SeekOrigin" /> 型の値を使用して、<paramref name="offset" /> の参照ポイントとして先頭、末尾、または現在位置を指定します。</param>
      <exception cref="T:System.IO.IOException">I/O エラーが発生しました。</exception>
      <exception cref="T:System.NotSupportedException">ストリームがシークをサポートしていません。たとえば、FileStream がパイプまたはコンソール出力から構築されました。</exception>
      <exception cref="T:System.ArgumentException">ストリームの先頭より前をシークしようとしました。</exception>
      <exception cref="T:System.ObjectDisposedException">ストリームが閉じられた後でメソッドが呼び出されました。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.SetLength(System.Int64)">
      <summary>ストリーム長を特定の値に設定します。</summary>
      <param name="value">ストリームの新しい長さ。</param>
      <exception cref="T:System.IO.IOException">I/O エラーが発生しました。</exception>
      <exception cref="T:System.NotSupportedException">ストリームが書き込みもシークもサポートしていません。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> パラメーターを 0 未満の値に設定しようとしました。</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>ファイル ストリームにバイトのブロックを書き込みます。</summary>
      <param name="array">ストリームに書き込むデータを格納しているバッファー。</param>
      <param name="offset">ストリームへのバイトのコピーを開始する位置を示す <paramref name="array" /> 内のバイト オフセット。インデックス番号は 0 から始まります。</param>
      <param name="count">書き込む最大バイト数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> と <paramref name="count" /> が <paramref name="array" /> の無効な範囲を示しています。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> または <paramref name="count" /> が負の値です。</exception>
      <exception cref="T:System.IO.IOException">I/O エラーが発生しました。または別のスレッドによって、オペレーティング システムのファイル ハンドルの位置に予期しない変更が行われている可能性があります。</exception>
      <exception cref="T:System.ObjectDisposedException">ストリームが閉じられました。</exception>
      <exception cref="T:System.NotSupportedException">現在のストリーム インスタンスは書き込みをサポートしていません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.WriteAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>現在のストリームにバイト シーケンスを非同期に書き込み、書き込んだバイト数だけストリーム内の現在位置を進め、キャンセル要求を監視します。</summary>
      <returns>非同期の書き込み操作を表すタスク。</returns>
      <param name="buffer">データの書き込み元となるバッファー。</param>
      <param name="offset">ストリームへのバイトのコピーを開始する位置を示す <paramref name="buffer" /> 内のバイト オフセット。インデックス番号は 0 から始まります。</param>
      <param name="count">書き込む最大バイト数。</param>
      <param name="cancellationToken">キャンセル要求を監視するためのトークン。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> は null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> または <paramref name="count" /> が負の値です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> と <paramref name="count" /> の合計値が、バッファー長より大きい値です。</exception>
      <exception cref="T:System.NotSupportedException">ストリームは書き込みをサポートしません。</exception>
      <exception cref="T:System.ObjectDisposedException">ストリームは破棄されています。</exception>
      <exception cref="T:System.InvalidOperationException">ストリームは現在、前の書き込み操作で使用中です。</exception>
    </member>
    <member name="M:System.IO.FileStream.WriteByte(System.Byte)">
      <summary>ファイル ストリームの現在位置にバイトを書き込みます。</summary>
      <param name="value">ストリームに書き込むバイト。</param>
      <exception cref="T:System.ObjectDisposedException">ストリームが閉じられました。</exception>
      <exception cref="T:System.NotSupportedException">ストリームは書き込みをサポートしません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.FileSystemInfo">
      <summary>
        <see cref="T:System.IO.FileInfo" /> オブジェクトと <see cref="T:System.IO.DirectoryInfo" /> オブジェクトの両方の基本クラスを提供します。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.FileSystemInfo.#ctor">
      <summary>
        <see cref="T:System.IO.FileSystemInfo" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="P:System.IO.FileSystemInfo.Attributes">
      <summary>現在のファイルまたはディレクトリの属性を取得または設定します。</summary>
      <returns>現在の <see cref="T:System.IO.FileSystemInfo" /> の <see cref="T:System.IO.FileAttributes" />。</returns>
      <exception cref="T:System.IO.FileNotFoundException">指定したファイルが存在しません。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.ArgumentException">呼び出し元が、無効なファイル属性を設定しようとしました。またはユーザーが属性値を設定しようとしていますが、書き込みの権限がありません。</exception>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> がデータを初期化できません。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.CreationTime">
      <summary>現在のファイルまたはディレクトリの作成日時を取得または設定します。</summary>
      <returns>現在の <see cref="T:System.IO.FileSystemInfo" /> オブジェクトの作成日時。</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> がデータを初期化できません。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.PlatformNotSupportedException">現在のオペレーティング システムは Windows NT 以降ではありません。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">呼び出し元が、無効な作成時間を設定しようとしています。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.CreationTimeUtc">
      <summary>現在のファイルまたはディレクトリの作成日時を世界協定時刻 (UTC) で取得または設定します。</summary>
      <returns>現在の <see cref="T:System.IO.FileSystemInfo" /> オブジェクトの UTC 形式での作成日時。</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> がデータを初期化できません。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.PlatformNotSupportedException">現在のオペレーティング システムは Windows NT 以降ではありません。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">呼び出し元が、無効なアクセス時間を設定しようとしています。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileSystemInfo.Delete">
      <summary>ファイルまたはディレクトリを削除します。</summary>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.IO.IOException">開いているハンドルがファイルまたはディレクトリに対して存在し、オペレーティング システムが Windows XP またはそれ以前のものです。開いているハンドルは、ディレクトリとファイルを列挙する際に発生した可能性があります。詳細については、「方法: ディレクトリとファイルを列挙する」を参照してください。</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.FileSystemInfo.Exists">
      <summary>ファイルまたはディレクトリが存在するかどうかを示す値を取得します。</summary>
      <returns>ファイルまたはディレクトリが存在する場合は true。それ以外の場合は false。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileSystemInfo.Extension">
      <summary>ファイルの拡張子部分を表す文字列を取得します。</summary>
      <returns>
        <see cref="T:System.IO.FileSystemInfo" /> 拡張子を格納している文字列。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileSystemInfo.FullName">
      <summary>ディレクトリまたはファイルの絶対パスを取得します。</summary>
      <returns>絶対パスを含んでいる文字列。</returns>
      <exception cref="T:System.IO.PathTooLongException">ファイルの完全修飾パスとファイル名が 260 文字以上です。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="F:System.IO.FileSystemInfo.FullPath">
      <summary>ディレクトリまたはファイルの絶対パスを表します。</summary>
      <exception cref="T:System.IO.PathTooLongException">ファイルの完全修飾パスが 260 文字以上です。</exception>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastAccessTime">
      <summary>現在のファイルまたはディレクトリに最後にアクセスした時刻を取得または設定します。</summary>
      <returns>現在のファイルまたはディレクトリに最後にアクセスした時刻。</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> がデータを初期化できません。</exception>
      <exception cref="T:System.PlatformNotSupportedException">現在のオペレーティング システムは Windows NT 以降ではありません。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">呼び出し元が、無効なアクセス時間を設定しようとしています。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastAccessTimeUtc">
      <summary>現在のファイルまたはディレクトリに最後にアクセスした時刻を世界協定時刻 (UTC) で取得または設定します。</summary>
      <returns>現在のファイルまたはディレクトリに最後にアクセスした UTC 時刻。</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> がデータを初期化できません。</exception>
      <exception cref="T:System.PlatformNotSupportedException">現在のオペレーティング システムは Windows NT 以降ではありません。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">呼び出し元が、無効なアクセス時間を設定しようとしています。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastWriteTime">
      <summary>現在のファイルまたはディレクトリに最後に書き込みがなされた時刻を取得または設定します。</summary>
      <returns>現在のファイルに最後に書き込みがなされた時刻。</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> がデータを初期化できません。</exception>
      <exception cref="T:System.PlatformNotSupportedException">現在のオペレーティング システムは Windows NT 以降ではありません。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">呼び出し元が、無効な書き込み時間を設定しようとしています。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastWriteTimeUtc">
      <summary>現在のファイルまたはディレクトリに最後に書き込みがなされた時刻を世界協定時刻 (UTC) で取得または設定します。</summary>
      <returns>現在のファイルに最後に書き込みがなされた UTC 時刻。</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> がデータを初期化できません。</exception>
      <exception cref="T:System.PlatformNotSupportedException">現在のオペレーティング システムは Windows NT 以降ではありません。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">呼び出し元が、無効な書き込み時間を設定しようとしています。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.Name">
      <summary>ファイルの場合は、そのファイルの名前を取得します。ディレクトリの場合は、階層が存在する場合は、その階層内にある最後のディレクトリの名前を取得します。それ以外の場合は、Name プロパティはそのディレクトリの名前を取得します。</summary>
      <returns>親ディレクトリの名前、階層内にある最後のディレクトリの名前、またはファイル名拡張子を含むファイル名を示す文字列。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.FileSystemInfo.OriginalPath">
      <summary>パスは、以前にユーザーが相対パスまたは絶対パスで指定したものです。</summary>
    </member>
    <member name="M:System.IO.FileSystemInfo.Refresh">
      <summary>オブジェクトの状態を更新します。</summary>
      <exception cref="T:System.IO.IOException">ディスク ドライブなどのデバイスの準備ができていません。 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.SearchOption">
      <summary>現在のディレクトリを検索するのか、または現在のディレクトリとすべてのサブディレクトリを検索するのかを指定します。</summary>
    </member>
    <member name="F:System.IO.SearchOption.AllDirectories">
      <summary>検索操作に現在のディレクトリとすべてのサブディレクトリを含めます。このオプションは、検索範囲に、マウントされたドライブやシンボリック リンクのようなリパース ポイントを含めます。</summary>
    </member>
    <member name="F:System.IO.SearchOption.TopDirectoryOnly">
      <summary>検索操作に現在のディレクトリのみを含めます。</summary>
    </member>
  </members>
</doc>