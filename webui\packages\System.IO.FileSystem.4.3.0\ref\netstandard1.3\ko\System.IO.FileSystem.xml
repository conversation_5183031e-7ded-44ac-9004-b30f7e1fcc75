﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.IO.FileSystem</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Win32.SafeHandles.SafeFileHandle">
      <summary>파일 핸들에 대한 래퍼 클래스를 나타냅니다. </summary>
    </member>
    <member name="M:Microsoft.Win32.SafeHandles.SafeFileHandle.#ctor(System.IntPtr,System.Boolean)">
      <summary>
        <see cref="T:Microsoft.Win32.SafeHandles.SafeFileHandle" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
      <param name="preexistingHandle">사용할 기존 핸들을 나타내는 <see cref="T:System.IntPtr" /> 개체입니다.</param>
      <param name="ownsHandle">종료 단계에서 핸들을 안정적으로 해제하려면 true이고, 안정적으로 해제할 수 없게 하려면 false(권장되지 않음)입니다.</param>
    </member>
    <member name="P:Microsoft.Win32.SafeHandles.SafeFileHandle.IsInvalid"></member>
    <member name="T:System.IO.Directory">
      <summary>디렉터리와 하위 디렉터리에서 만들기, 이동 및 열거를 위한 정적 메서드를 노출합니다.이 클래스는 상속될 수 없습니다.이 형식에 대 한.NET Framework 소스 코드를 찾아보려면 참조는 참조 원본.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Directory.CreateDirectory(System.String)">
      <summary>이미 존재하지 않는 한 지정된 경로에 모든 디렉터리와 하위 디렉터리를 만듭니다.</summary>
      <returns>지정된 경로에서 디렉터리를 나타내는 개체입니다.이 개체는 지정된 경로에 디렉터리가 이미 존재하는지 여부와 관계없이 반환됩니다.</returns>
      <param name="path">만들 디렉터리입니다. </param>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" />로 지정된 디렉터리가 파일입니다.또는네트워크 이름을 알 수 없습니다.</exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 하나 이상의 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드를 사용하여 잘못된 문자를 쿼리할 수 있습니다.또는<paramref name="path" />가 콜론 문자(:)로 시작되거나 이 문자만 포함하는 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />에 드라이브 레이블("C:\")의 일부가 아닌 콜론 문자(:)가 포함된 경우</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.Delete(System.String)">
      <summary>지정된 경로에서 빈 디렉터리를 삭제합니다.</summary>
      <param name="path">제거할 빈 디렉터리의 이름입니다.해당 디렉터리는 쓰기가 가능하고 비어 있어야 합니다.</param>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" />에 지정된 것과 이름 및 위치가 같은 파일이 있는 경우또는이 디렉터리는 응용 프로그램의 현재 작업 디렉터리입니다.또는<paramref name="path" />로 지정된 디렉터리가 비어 있지 않은 경우또는디렉터리가 읽기 전용이거나 읽기 전용 파일을 포함하고 있습니다.또는이 디렉터리는 다른 프로세스에서 사용되고 있습니다.</exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 하나 이상의 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드를 사용하여 잘못된 문자를 쿼리할 수 있습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" />가 없거나 찾지 못한 경우또는지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.Delete(System.String,System.Boolean)">
      <summary>지정된 디렉터리와 해당 디렉터리의 하위 디렉터리 및 파일을 삭제합니다. </summary>
      <param name="path">제거할 디렉터리의 이름입니다. </param>
      <param name="recursive">
        <paramref name="path" />의 디렉터리, 하위 디렉터리 및 파일을 제거하려면 true이고, 그렇지 않으면 false입니다. </param>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" />에 지정된 것과 이름 및 위치가 같은 파일이 있는 경우또는<paramref name="path" />로 지정된 디렉터리는 읽기 전용이거나 <paramref name="recursive" />가 false이고 <paramref name="path" />가 빈 디렉터리가 아닌 경우 또는이 디렉터리는 응용 프로그램의 현재 작업 디렉터리입니다. 또는디렉터리는 읽기 전용 파일을 포함합니다.또는이 디렉터리는 다른 프로세스에서 사용되고 있습니다.</exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 하나 이상의 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드를 사용하여 잘못된 문자를 쿼리할 수 있습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" />가 없거나 찾지 못한 경우또는지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String)">
      <summary>지정된 경로에 있는 디렉터리 이름의 열거 가능 컬렉션을 반환합니다.</summary>
      <returns>
        <paramref name="path" />로 지정된 디렉터리에서 디렉터리에 대한 전체 이름(경로 포함)의 열거 가능 컬렉션입니다.</returns>
      <param name="path">검색할 디렉터리에 대한 상대 또는 절대 경로입니다.이 문자열은 대/소문자를 구분하지 않습니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드를 사용하여 잘못된 문자를 쿼리할 수 있습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" />가 잘못된 경우(예: 매핑되지 않은 드라이브를 참조하는 경우) </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" />가 파일 이름인 경우</exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로나 파일 이름 또는 둘의 결합이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String,System.String)">
      <summary>지정된 경로에서 검색 패턴과 일치하는 디렉터리 이름의 열거 가능 컬렉션을 반환합니다.</summary>
      <returns>
        <paramref name="path" />로 지정되고 지정된 검색 패턴과 일치하는 디렉터리에서 디렉터리에 대한 전체 이름(경로 포함)의 열거 가능 컬렉션입니다.</returns>
      <param name="path">검색할 디렉터리에 대한 상대 또는 절대 경로입니다.이 문자열은 대/소문자를 구분하지 않습니다.</param>
      <param name="searchPattern">
        <paramref name="path" />에 있는 디렉터리 이름과 일치하는지 비교할 검색 문자열입니다.이 매개 변수는 유효한 리터럴 경로와 와일드카드(* 및 ?) 문자(설명 참조)로 된 조합을 포함하지만 정규식을 지원하지 않습니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드로 잘못된 문자를 쿼리할 수 있습니다.또는<paramref name="searchPattern" />이 유효한 패턴을 포함하지 않는 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우또는<paramref name="searchPattern" />가 null인 경우 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" />가 잘못된 경우(예: 매핑되지 않은 드라이브를 참조하는 경우) </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" />가 파일 이름인 경우</exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로나 파일 이름 또는 둘의 결합이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String,System.String,System.IO.SearchOption)">
      <summary>지정된 경로에서 검색 패턴과 일치하는 디렉터리 이름의 열거 가능 컬렉션을 반환하고 선택적으로 하위 디렉터리를 검색합니다.</summary>
      <returns>
        <paramref name="path" />로 지정되고 지정된 검색 패턴 및 옵션과 일치하는 디렉터리에 대한 전체 이름(경로 포함)의 열거 가능 컬렉션입니다.</returns>
      <param name="path">검색할 디렉터리에 대한 상대 또는 절대 경로입니다.이 문자열은 대/소문자를 구분하지 않습니다.</param>
      <param name="searchPattern">
        <paramref name="path" />에 있는 디렉터리 이름과 일치하는지 비교할 검색 문자열입니다.이 매개 변수는 유효한 리터럴 경로와 와일드카드(* 및 ?) 문자(설명 참조)로 된 조합을 포함하지만 정규식을 지원하지 않습니다.</param>
      <param name="searchOption">검색 작업에 현재 디렉터리만 포함할지 아니면 모든 하위 디렉터리를 포함할지를 지정하는 열거형 값 중 하나입니다.기본값은 <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드를 사용하여 잘못된 문자를 쿼리할 수 있습니다.또는<paramref name="searchPattern" />이 유효한 패턴을 포함하지 않는 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우또는<paramref name="searchPattern" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />은(는) 올바른 <see cref="T:System.IO.SearchOption" /> 값이 아닙니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" />가 잘못된 경우(예: 매핑되지 않은 드라이브를 참조하는 경우) </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" />가 파일 이름인 경우</exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로나 파일 이름 또는 둘의 결합이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String)">
      <summary>지정된 경로에 있는 파일 이름의 열거 가능 컬렉션을 반환합니다.</summary>
      <returns>
        <paramref name="path" />로 지정된 디렉터리에서 파일에 대한 전체 이름(경로 포함)의 열거 가능 컬렉션입니다.</returns>
      <param name="path">검색할 디렉터리에 대한 상대 또는 절대 경로입니다.이 문자열은 대/소문자를 구분하지 않습니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드를 사용하여 잘못된 문자를 쿼리할 수 있습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" />가 잘못된 경우(예: 매핑되지 않은 드라이브를 참조하는 경우) </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" />가 파일 이름인 경우</exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로나 파일 이름 또는 둘의 결합이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String,System.String)">
      <summary>지정된 경로에서 검색 패턴과 일치하는 파일 이름의 열거 가능 컬렉션을 반환합니다.</summary>
      <returns>
        <paramref name="path" />로 지정되고 지정된 검색 패턴과 일치하는 디렉터리에서 파일에 대한 전체 이름(경로 포함)의 열거 가능 컬렉션입니다.</returns>
      <param name="path">검색할 디렉터리에 대한 상대 또는 절대 경로입니다.이 문자열은 대/소문자를 구분하지 않습니다.</param>
      <param name="searchPattern">
        <paramref name="path" />에 있는 파일 이름과 일치하는지 비교할 검색 문자열입니다.이 매개 변수는 유효한 리터럴 경로와 와일드카드(* 및 ?) 문자(설명 참조)로 된 조합을 포함하지만 정규식을 지원하지 않습니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드를 사용하여 잘못된 문자를 쿼리할 수 있습니다.또는<paramref name="searchPattern" />이 유효한 패턴을 포함하지 않는 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우또는<paramref name="searchPattern" />가 null인 경우 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" />가 잘못된 경우(예: 매핑되지 않은 드라이브를 참조하는 경우) </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" />가 파일 이름인 경우</exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로나 파일 이름 또는 둘의 결합이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String,System.String,System.IO.SearchOption)">
      <summary>지정된 경로에서 검색 패턴과 일치하는 파일 이름의 열거 가능 컬렉션을 반환하고 선택적으로 하위 디렉터리를 검색합니다.</summary>
      <returns>
        <paramref name="path" />로 지정되고 지정된 검색 패턴 및 옵션과 일치하는 디렉터리에서 파일에 대한 전체 이름(경로 포함)의 열거 가능 컬렉션입니다.</returns>
      <param name="path">검색할 디렉터리에 대한 상대 또는 절대 경로입니다.이 문자열은 대/소문자를 구분하지 않습니다.</param>
      <param name="searchPattern">
        <paramref name="path" />에 있는 파일 이름과 일치하는지 비교할 검색 문자열입니다.이 매개 변수는 유효한 리터럴 경로와 와일드카드(* 및 ?) 문자(설명 참조)로 된 조합을 포함하지만 정규식을 지원하지 않습니다.</param>
      <param name="searchOption">검색 작업에 현재 디렉터리만 포함할지 아니면 모든 하위 디렉터리를 포함할지를 지정하는 열거형 값 중 하나입니다.기본값은 <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드를 사용하여 잘못된 문자를 쿼리할 수 있습니다.또는<paramref name="searchPattern" />이 유효한 패턴을 포함하지 않는 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우또는<paramref name="searchPattern" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />은(는) 올바른 <see cref="T:System.IO.SearchOption" /> 값이 아닙니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" />가 잘못된 경우(예: 매핑되지 않은 드라이브를 참조하는 경우) </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" />가 파일 이름인 경우</exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로나 파일 이름 또는 둘의 결합이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String)">
      <summary>지정된 경로에 있는 파일 이름 및 디렉터리 이름의 열거 가능 컬렉션을 반환합니다. </summary>
      <returns>
        <paramref name="path" />로 지정된 디렉터리에 있는 파일 시스템 항목의 열거 가능 컬렉션입니다.</returns>
      <param name="path">검색할 디렉터리에 대한 상대 또는 절대 경로입니다.이 문자열은 대/소문자를 구분하지 않습니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드를 사용하여 잘못된 문자를 쿼리할 수 있습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" />가 잘못된 경우(예: 매핑되지 않은 드라이브를 참조하는 경우) </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" />가 파일 이름인 경우</exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로나 파일 이름 또는 둘의 결합이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String,System.String)">
      <summary>지정된 경로에서 검색 패턴과 일치하는 파일 이름 및 디렉터리 이름의 열거 가능 컬렉션을 반환합니다.</summary>
      <returns>
        <paramref name="path" />로 지정된 디렉터리에서 지정된 검색 패턴과 일치하는 파일 시스템 항목의 열거 가능 컬렉션입니다.</returns>
      <param name="path">검색할 디렉터리에 대한 상대 또는 절대 경로입니다.이 문자열은 대/소문자를 구분하지 않습니다.</param>
      <param name="searchPattern">
        <paramref name="path" />에 있는 파일 시스템 항목 이름과 일치하는지 비교할 검색 문자열입니다.이 매개 변수는 유효한 리터럴 경로와 와일드카드(* 및 ?) 문자(설명 참조)로 된 조합을 포함하지만 정규식을 지원하지 않습니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드를 사용하여 잘못된 문자를 쿼리할 수 있습니다.또는<paramref name="searchPattern" />이 유효한 패턴을 포함하지 않는 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우또는<paramref name="searchPattern" />가 null인 경우 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" />가 잘못된 경우(예: 매핑되지 않은 드라이브를 참조하는 경우) </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" />가 파일 이름인 경우</exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로나 파일 이름 또는 둘의 결합이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String,System.String,System.IO.SearchOption)">
      <summary>지정된 경로에서 검색 패턴과 일치하는 파일 이름 및 디렉터리 이름의 열거 가능 컬렉션을 반환하고 선택적으로 하위 디렉터리를 검색합니다.</summary>
      <returns>
        <paramref name="path" />로 지정된 디렉터리에서 지정된 검색 패턴 및 옵션과 일치하는 파일 시스템 항목의 열거 가능 컬렉션입니다.</returns>
      <param name="path">검색할 디렉터리에 대한 상대 또는 절대 경로입니다.이 문자열은 대/소문자를 구분하지 않습니다.</param>
      <param name="searchPattern">
        <paramref name="path" />에 있는 파일 시스템 항목과 일치하는지 비교할 검색 문자열입니다.이 매개 변수는 유효한 리터럴 경로와 와일드카드(* 및 ?) 문자(설명 참조)로 된 조합을 포함하지만 정규식을 지원하지 않습니다.</param>
      <param name="searchOption">검색 작업에 현재 디렉터리만 포함할지 아니면 모든 하위 디렉터리를 포함할지를 지정하는 열거형 값 중 하나입니다.기본값은 <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드를 사용하여 잘못된 문자를 쿼리할 수 있습니다.또는<paramref name="searchPattern" />이 유효한 패턴을 포함하지 않는 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우또는<paramref name="searchPattern" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />은(는) 올바른 <see cref="T:System.IO.SearchOption" /> 값이 아닙니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" />가 잘못된 경우(예: 매핑되지 않은 드라이브를 참조하는 경우) </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" />가 파일 이름인 경우</exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로나 파일 이름 또는 둘의 결합이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우</exception>
    </member>
    <member name="M:System.IO.Directory.Exists(System.String)">
      <summary>지정된 경로가 디스크에 있는 기존 디렉터리를 참조하는지를 확인합니다.</summary>
      <returns>if <paramref name="path" />가 기존 디렉터리를 참조하면 true입니다. 디렉터리가 존재하지 않거나 지정된 파일이 존재하는지 확인할 때 오류가 발생하면 false입니다.</returns>
      <param name="path">테스트할 경로입니다. </param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetCreationTime(System.String)">
      <summary>디렉터리를 만든 날짜와 시간을 가져옵니다.</summary>
      <returns>지정된 디렉터리를 만든 날짜와 시간으로 설정된 구조체입니다.이 값은 현지 시간으로 표현됩니다.</returns>
      <param name="path">디렉터리 경로입니다. </param>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 하나 이상의 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드를 사용하여 잘못된 문자를 쿼리할 수 있습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetCreationTimeUtc(System.String)">
      <summary>디렉터리를 만든 날짜와 시간을 UTC(Coordinated Universal Time) 형식으로 가져옵니다.</summary>
      <returns>지정된 디렉터리를 만든 날짜와 시간으로 설정된 구조체입니다.이 값은 UTC 시간으로 표현됩니다.</returns>
      <param name="path">디렉터리 경로입니다. </param>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 하나 이상의 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드를 사용하여 잘못된 문자를 쿼리할 수 있습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetCurrentDirectory">
      <summary>응용 프로그램의 현재 작업 디렉터리를 가져옵니다.</summary>
      <returns>현재 작업 디렉터리의 경로를 포함하고 백슬래시(\)로 끝나지 않는 문자열입니다.</returns>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.NotSupportedException">운영 체제가 현재 디렉터리 기능이 없는 Windows CE인 경우이 메서드는 .NET Compact Framework에서 사용할 수 있지만 현재는 지원되지 않습니다.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String)">
      <summary>지정된 디렉터리에 있는 하위 디렉터리의 이름(경로 포함)을 반환합니다.</summary>
      <returns>지정된 경로에서 하위 디렉터리의 전체 이름(경로 포함)의 배열이거나 디렉터리가 없으면 빈 배열입니다.</returns>
      <param name="path">검색할 디렉터리에 대한 상대 또는 절대 경로입니다.이 문자열은 대/소문자를 구분하지 않습니다.</param>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 하나 이상의 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드를 사용하여 잘못된 문자를 쿼리할 수 있습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" />가 파일 이름인 경우 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String,System.String)">
      <summary>지정된 디렉터리에서 지정된 검색 패턴과 일치하는 하위 디렉터리 이름(파일 경로 포함)을 반환합니다.</summary>
      <returns>지정된 디렉터리에서 검색 패턴과 일치하는 하위 디렉터리의 전체 이름(경로 포함)의 배열이거나 디렉터리가 없으면 빈 배열입니다.</returns>
      <param name="path">검색할 디렉터리에 대한 상대 또는 절대 경로입니다.이 문자열은 대/소문자를 구분하지 않습니다.</param>
      <param name="searchPattern">
        <paramref name="path" />에 있는 하위 디렉터리 이름과 일치하는지 비교할 검색 문자열입니다.이 매개 변수는 유효한 리터럴 및 와일드카드 문자(설명 참조)로 된 조합을 포함하지만 정규식을 지원하지 않습니다.</param>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 하나 이상의 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" />를 사용하여 잘못된 문자를 쿼리할 수 있습니다.또는 <paramref name="searchPattern" />이 유효한 패턴을 포함하지 않는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 또는 <paramref name="searchPattern" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" />가 파일 이름인 경우 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String,System.String,System.IO.SearchOption)">
      <summary>지정된 디렉터리에서 지정된 검색 패턴과 일치하는 하위 디렉터리(해당 경로 포함)의 이름을 가져오고 선택적으로 하위 디렉터리를 반환합니다.</summary>
      <returns>지정된 조건과 일치하는 하위 디렉터리의 전체 이름(경로 포함)의 배열이거나 디렉터리가 없으면 빈 배열입니다.</returns>
      <param name="path">검색할 디렉터리에 대한 상대 또는 절대 경로입니다.이 문자열은 대/소문자를 구분하지 않습니다.</param>
      <param name="searchPattern">
        <paramref name="path" />에 있는 하위 디렉터리 이름과 일치하는지 비교할 검색 문자열입니다.이 매개 변수는 유효한 리터럴 및 와일드카드 문자(설명 참조)로 된 조합을 포함하지만 정규식을 지원하지 않습니다.</param>
      <param name="searchOption">검색 작업에 현재 디렉터리만 포함할지 아니면 모든 하위 디렉터리를 포함할지를 지정하는 열거형 값 중 하나입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 하나 이상의 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드를 사용하여 잘못된 문자를 쿼리할 수 있습니다.또는 <paramref name="searchPattern" />이 유효한 패턴을 포함하지 않는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 또는 <paramref name="searchPattern" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />은(는) 올바른 <see cref="T:System.IO.SearchOption" /> 값이 아닙니다.</exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" />가 파일 이름인 경우 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
    </member>
    <member name="M:System.IO.Directory.GetDirectoryRoot(System.String)">
      <summary>지정된 경로에 대한 볼륨 정보, 루트 정보 또는 두 정보 모두를 반환합니다.</summary>
      <returns>지정된 경로에 대한 볼륨 정보, 루트 정보 또는 두 정보 모두를 포함하는 문자열입니다.</returns>
      <param name="path">파일 또는 디렉터리의 경로입니다. </param>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 하나 이상의 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" />로 잘못된 문자를 쿼리할 수 있습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String)">
      <summary>지정된 디렉터리에 있는 파일의 이름(경로 포함)을 반환합니다.</summary>
      <returns>지정된 디렉터리에서 파일의 전체 이름(경로 포함)의 배열이거나 파일이 없으면 빈 배열입니다.</returns>
      <param name="path">검색할 디렉터리에 대한 상대 또는 절대 경로입니다.이 문자열은 대/소문자를 구분하지 않습니다.</param>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" />가 파일 이름인 경우또는네트워크 오류가 발생했습니다. </exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 하나 이상의 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드를 사용하여 잘못된 문자를 쿼리할 수 있습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 없거나 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String,System.String)">
      <summary>지정된 디렉터리에서 지정된 검색 패턴과 일치하는 파일 이름(파일 경로 포함)을 반환합니다.</summary>
      <returns>지정된 디렉터리에서 지정된 검색 패턴과 일치하는 파일의 전체 이름(경로 포함)의 배열이거나 파일이 없으면 빈 배열입니다.</returns>
      <param name="path">검색할 디렉터리에 대한 상대 또는 절대 경로입니다.이 문자열은 대/소문자를 구분하지 않습니다.</param>
      <param name="searchPattern">
        <paramref name="path" />에 있는 파일 이름과 일치하는지 비교할 검색 문자열입니다.이 매개 변수는 유효한 리터럴 경로와 와일드카드(* 및 ?) 문자(설명 참조)로 된 조합을 포함하지만 정규식을 지원하지 않습니다.</param>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" />가 파일 이름인 경우또는네트워크 오류가 발생했습니다. </exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 하나 이상의 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" />를 사용하여 잘못된 문자를 쿼리할 수 있습니다.또는 <paramref name="searchPattern" />이 유효한 패턴을 포함하지 않는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 또는 <paramref name="searchPattern" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 없거나 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String,System.String,System.IO.SearchOption)">
      <summary>하위 디렉터리를 검색할지를 나타내는 값을 사용하여 지정된 디렉터리에서 지정된 검색 패턴과 일치하는 파일 이름(파일 경로 포함)을 반환합니다.</summary>
      <returns>지정된 디렉터리에서 지정된 검색 패턴 및 옵션과 일치하는 파일의 전체 이름(경로 포함)의 배열이거나 파일이 없으면 빈 배열입니다.</returns>
      <param name="path">검색할 디렉터리에 대한 상대 또는 절대 경로입니다.이 문자열은 대/소문자를 구분하지 않습니다.</param>
      <param name="searchPattern">
        <paramref name="path" />에 있는 파일 이름과 일치하는지 비교할 검색 문자열입니다.이 매개 변수는 유효한 리터럴 경로와 와일드카드(* 및 ?) 문자(설명 참조)로 된 조합을 포함하지만 정규식을 지원하지 않습니다.</param>
      <param name="searchOption">검색 작업에 현재 디렉터리만 포함할지 아니면 모든 하위 디렉터리를 포함할지를 지정하는 열거형 값 중 하나입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 하나 이상의 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드로 잘못된 문자를 쿼리할 수 있습니다.또는 <paramref name="searchPattern" />이 유효한 패턴을 포함하지 않는 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 또는 <paramref name="searchpattern" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />은(는) 올바른 <see cref="T:System.IO.SearchOption" /> 값이 아닙니다.</exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 없거나 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" />가 파일 이름인 경우또는네트워크 오류가 발생했습니다. </exception>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String)">
      <summary>지정된 경로에 있는 모든 파일과 하위 디렉터리의 이름을 반환합니다.</summary>
      <returns>지정된 디렉터리에서 파일 및 하위 디렉터리 이름의 배열이거나 파일 또는 디렉터리가 없으면 빈 배열입니다.</returns>
      <param name="path">검색할 디렉터리에 대한 상대 또는 절대 경로입니다.이 문자열은 대/소문자를 구분하지 않습니다.</param>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 하나 이상의 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" />로 잘못된 문자를 쿼리할 수 있습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" />가 파일 이름인 경우 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String,System.String)">
      <summary>지정된 경로에서 검색 패턴과 일치하는 파일 및 디렉터리 이름 배열을 반환합니다.</summary>
      <returns>지정된 검색 조건과 일치하는 파일 이름 및 디렉터리 이름의 배열이거나 파일 또는 디렉터리가 없으면 빈 배열입니다.</returns>
      <param name="path">검색할 디렉터리에 대한 상대 또는 절대 경로입니다.이 문자열은 대/소문자를 구분하지 않습니다.</param>
      <param name="searchPattern">
        <paramref name="path" />에 있는 파일 및 디렉터리 이름과 일치하는지 비교할 검색 문자열입니다.이 매개 변수는 유효한 리터럴 경로와 와일드카드(* 및 ?) 문자(설명 참조)로 된 조합을 포함하지만 정규식을 지원하지 않습니다.</param>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 하나 이상의 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드로 잘못된 문자를 쿼리할 수 있습니다.또는 <paramref name="searchPattern" />이 유효한 패턴을 포함하지 않는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 또는 <paramref name="searchPattern" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" />가 파일 이름인 경우 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String,System.String,System.IO.SearchOption)">
      <summary>지정된 경로에서 검색 패턴과 일치하는 모든 파일 이름 및 디렉터리 이름의 배열을 가져오고 선택적으로 하위 디렉터리를 반환합니다.</summary>
      <returns>지정된 검색 조건과 일치하는 파일, 파일 이름 및 디렉터리 이름의 배열이거나 파일 또는 디렉터리가 없으면 빈 배열입니다.</returns>
      <param name="path">검색할 디렉터리에 대한 상대 또는 절대 경로입니다.이 문자열은 대/소문자를 구분하지 않습니다.</param>
      <param name="searchPattern">
        <paramref name="path" />에 있는 파일 및 디렉터리 이름과 일치하는지 비교할 검색 문자열입니다.이 매개 변수는 유효한 리터럴 경로와 와일드카드(* 및 ?) 문자(설명 참조)로 된 조합을 포함하지만 정규식을 지원하지 않습니다.</param>
      <param name="searchOption">검색 작업에 현재 디렉터리만 포함할지 아니면 모든 하위 디렉터리를 포함할지를 지정하는 열거형 값 중 하나입니다.기본값은 <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드를 사용하여 잘못된 문자를 쿼리할 수 있습니다.또는<paramref name="searchPattern" />이 유효한 패턴을 포함하지 않는 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우또는<paramref name="searchPattern" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />은(는) 올바른 <see cref="T:System.IO.SearchOption" /> 값이 아닙니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" />가 잘못된 경우(예: 매핑되지 않은 드라이브를 참조하는 경우) </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" />가 파일 이름인 경우</exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로나 파일 이름 또는 둘의 결합이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우</exception>
    </member>
    <member name="M:System.IO.Directory.GetLastAccessTime(System.String)">
      <summary>지정된 파일 또는 디렉터리를 마지막으로 액세스한 날짜와 시간을 반환합니다.</summary>
      <returns>지정된 파일 또는 디렉터리를 마지막으로 액세스한 날짜와 시간으로 설정된 구조체입니다.이 값은 현지 시간으로 표현됩니다.</returns>
      <param name="path">액세스 날짜와 시간 정보를 가져올 파일 또는 디렉터리입니다. </param>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 하나 이상의 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드로 잘못된 문자를 쿼리할 수 있습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 매개 변수가 잘못된 형식인 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetLastAccessTimeUtc(System.String)">
      <summary>지정된 파일 또는 디렉터리에 마지막으로 액세스한 날짜와 시간을 UTC(Coordinated Universal Time) 형식으로 반환합니다.</summary>
      <returns>지정된 파일 또는 디렉터리를 마지막으로 액세스한 날짜와 시간으로 설정된 구조체입니다.이 값은 UTC 시간으로 표현됩니다.</returns>
      <param name="path">액세스 날짜와 시간 정보를 가져올 파일 또는 디렉터리입니다. </param>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 하나 이상의 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드로 잘못된 문자를 쿼리할 수 있습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 매개 변수가 잘못된 형식인 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetLastWriteTime(System.String)">
      <summary>지정된 파일 또는 디렉터리를 마지막으로 쓴 날짜와 시간을 반환합니다.</summary>
      <returns>지정된 파일 또는 디렉터리를 마지막으로 쓴 날짜와 시간으로 설정된 구조체입니다.이 값은 현지 시간으로 표현됩니다.</returns>
      <param name="path">수정 날짜와 시간 정보를 가져올 파일 또는 디렉터리입니다. </param>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 하나 이상의 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드로 잘못된 문자를 쿼리할 수 있습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetLastWriteTimeUtc(System.String)">
      <summary>지정된 파일이나 디렉터리에 마지막으로 쓴 날짜와 시간을 UTC(Coordinated Universal Time) 형식으로 반환합니다.</summary>
      <returns>지정된 파일 또는 디렉터리를 마지막으로 쓴 날짜와 시간으로 설정된 구조체입니다.이 값은 UTC 시간으로 표현됩니다.</returns>
      <param name="path">수정 날짜와 시간 정보를 가져올 파일 또는 디렉터리입니다. </param>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 하나 이상의 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드로 잘못된 문자를 쿼리할 수 있습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetParent(System.String)">
      <summary>지정된 경로의 절대 경로와 상대 경로를 모두 포함한 부모 디렉터리를 검색합니다.</summary>
      <returns>부모 디렉터리이거나 <paramref name="path" />가 UNC 서버 또는 공유 이름의 루트를 포함한 루트 디렉터리일 경우 null입니다.</returns>
      <param name="path">부모 디렉터리를 검색할 경로입니다. </param>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" />로 지정된 디렉터리가 읽기 전용일 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 하나 이상의 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드로 잘못된 문자를 쿼리할 수 있습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로를 찾을 수 없는 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.Move(System.String,System.String)">
      <summary>파일 또는 디렉터리와 그 내용을 새 위치로 이동합니다.</summary>
      <param name="sourceDirName">이동할 파일 또는 디렉터리의 경로입니다. </param>
      <param name="destDirName">
        <paramref name="sourceDirName" />의 새 위치에 대한 경로입니다.<paramref name="sourceDirName" />이 파일이면 <paramref name="destDirName" />도 파일 이름이어야 합니다.</param>
      <exception cref="T:System.IO.IOException">디렉터리를 다른 볼륨으로 이동하려고 한 경우 또는 <paramref name="destDirName" />이(가) 이미 있습니다. 또는 <paramref name="sourceDirName" />과 <paramref name="destDirName" /> 매개 변수가 같은 파일이나 디렉터리를 참조하는 경우 또는디렉터리 또는 파일 내의 다른 프로세스에 의해 사용 중입니다.</exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceDirName" /> 또는 <paramref name="destDirName" />이 길이가 0인 문자열이거나, 공백만 포함하거나 또는 하나 이상의 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드로 잘못된 문자를 쿼리할 수 있습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceDirName" /> 또는 <paramref name="destDirName" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceDirName" />에서 지정한 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetCreationTime(System.String,System.DateTime)">
      <summary>지정된 파일 또는 디렉터리에 대한 만든 날짜와 시간을 설정합니다.</summary>
      <param name="path">만든 날짜와 시간 정보를 설정할 파일 또는 디렉터리입니다. </param>
      <param name="creationTime">파일 또는 디렉터리를 마지막으로 쓴 날짜와 시간입니다.이 값은 현지 시간으로 표현됩니다.</param>
      <exception cref="T:System.IO.FileNotFoundException">지정된 경로를 찾을 수 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 하나 이상의 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드로 잘못된 문자를 쿼리할 수 있습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" />에서 해당 작업에 허용된 날짜나 시간 범위를 벗어나는 값을 지정한 경우 </exception>
      <exception cref="T:System.PlatformNotSupportedException">현재 운영 체제가 Windows NT 이상이 아닌 경우</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetCreationTimeUtc(System.String,System.DateTime)">
      <summary>지정된 파일이나 디렉터리를 만든 날짜와 시간을 UTC(Coordinated Universal Time) 형식으로 설정합니다.</summary>
      <param name="path">만든 날짜와 시간 정보를 설정할 파일 또는 디렉터리입니다. </param>
      <param name="creationTimeUtc">디렉터리 또는 파일이 만들어진 날짜와 시간입니다.이 값은 현지 시간으로 표현됩니다.</param>
      <exception cref="T:System.IO.FileNotFoundException">지정된 경로를 찾을 수 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 하나 이상의 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드로 잘못된 문자를 쿼리할 수 있습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" />에서 해당 작업에 허용된 날짜나 시간 범위를 벗어나는 값을 지정한 경우 </exception>
      <exception cref="T:System.PlatformNotSupportedException">현재 운영 체제가 Windows NT 이상이 아닌 경우</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetCurrentDirectory(System.String)">
      <summary>응용 프로그램의 현재 작업 디렉터리를 지정된 디렉터리로 설정합니다.</summary>
      <param name="path">현재 작업 디렉터리로 설정될 경로입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 하나 이상의 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드로 잘못된 문자를 쿼리할 수 있습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.Security.SecurityException">비관리 코드에 액세스하는 데 필요한 권한이 호출자에게 없는 경우 </exception>
      <exception cref="T:System.IO.FileNotFoundException">지정된 경로를 찾을 수 없는 경우 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 디렉터리를 찾을 수 없는 경우</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastAccessTime(System.String,System.DateTime)">
      <summary>지정된 파일 또는 디렉터리를 마지막으로 액세스한 날짜와 시간을 설정합니다.</summary>
      <param name="path">액세스 날짜와 시간 정보를 설정할 파일 또는 디렉터리입니다. </param>
      <param name="lastAccessTime">
        <paramref name="path" />의 액세스 날짜와 시간을 설정할 값을 포함하는 개체입니다.이 값은 현지 시간으로 표현됩니다.</param>
      <exception cref="T:System.IO.FileNotFoundException">지정된 경로를 찾을 수 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 하나 이상의 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드로 잘못된 문자를 쿼리할 수 있습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.PlatformNotSupportedException">현재 운영 체제가 Windows NT 이상이 아닌 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTime" />에서 해당 작업에 허용된 날짜나 시간 범위를 벗어나는 값을 지정한 경우</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastAccessTimeUtc(System.String,System.DateTime)">
      <summary>지정된 파일 또는 디렉터리에 마지막으로 액세스한 날짜와 시간을 UTC(Coordinated Universal Time) 형식으로 설정합니다.</summary>
      <param name="path">액세스 날짜와 시간 정보를 설정할 파일 또는 디렉터리입니다. </param>
      <param name="lastAccessTimeUtc">
        <paramref name="path" />의 액세스 날짜와 시간을 설정할 값을 포함하는 개체입니다.이 값은 UTC 시간으로 표현됩니다.</param>
      <exception cref="T:System.IO.FileNotFoundException">지정된 경로를 찾을 수 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 하나 이상의 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드로 잘못된 문자를 쿼리할 수 있습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.PlatformNotSupportedException">현재 운영 체제가 Windows NT 이상이 아닌 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTimeUtc" />에서 해당 작업에 허용된 날짜나 시간 범위를 벗어나는 값을 지정한 경우</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastWriteTime(System.String,System.DateTime)">
      <summary>디렉터리를 마지막으로 쓴 날짜와 시간을 설정합니다.</summary>
      <param name="path">디렉터리 경로입니다. </param>
      <param name="lastWriteTime">디렉터리를 마지막으로 쓴 날짜와 시간입니다.이 값은 현지 시간으로 표현됩니다.</param>
      <exception cref="T:System.IO.FileNotFoundException">지정된 경로를 찾을 수 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 하나 이상의 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드로 잘못된 문자를 쿼리할 수 있습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.PlatformNotSupportedException">현재 운영 체제가 Windows NT 이상이 아닌 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTime" />에서 해당 작업에 허용된 날짜나 시간 범위를 벗어나는 값을 지정한 경우</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastWriteTimeUtc(System.String,System.DateTime)">
      <summary>디렉터리에 마지막으로 쓴 날짜와 시간을 UTC(Coordinated Universal Time) 형식으로 설정합니다.</summary>
      <param name="path">디렉터리 경로입니다. </param>
      <param name="lastWriteTimeUtc">디렉터리를 마지막으로 쓴 날짜와 시간입니다.이 값은 UTC 시간으로 표현됩니다.</param>
      <exception cref="T:System.IO.FileNotFoundException">지정된 경로를 찾을 수 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나 또는 하나 이상의 잘못된 문자를 포함하는 경우<see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드로 잘못된 문자를 쿼리할 수 있습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.PlatformNotSupportedException">현재 운영 체제가 Windows NT 이상이 아닌 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTimeUtc" />에서 해당 작업에 허용된 날짜나 시간 범위를 벗어나는 값을 지정한 경우</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.IO.DirectoryInfo">
      <summary>디렉터리 및 하위 디렉터리를 만들고, 이동하고, 열거하는 인스턴스 메서드를 노출합니다.이 클래스는 상속될 수 없습니다.이 형식에 대 한.NET Framework 소스 코드를 찾아보려면 참조는
                                참조 하십시오.
                            </summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.DirectoryInfo.#ctor(System.String)">
      <summary>새 인스턴스를 초기화합니다.
                                <see cref="T:System.IO.DirectoryInfo" />지정된 된 경로에 대 한 클래스입니다.
                            </summary>
      <param name="path">만들 경로 지정 하는 문자열은
                                    DirectoryInfo.
                                </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />은
                                        null.
                                    </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />에 ", &lt;, &gt;, 또는 | 같은 잘못된 문자가 있는 경우
                                    </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.지정한 경로 및/또는 파일 이름이 너무 깁니다.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.Create">
      <summary>디렉터리를 만듭니다.</summary>
      <exception cref="T:System.IO.IOException">디렉터리를 만들 수 없는 경우</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.CreateSubdirectory(System.String)">
      <summary>지정된 경로에 하위 디렉터리를 하나 이상 만듭니다.이 인스턴스를 기준으로 지정된 된 경로 수는
                            <see cref="T:System.IO.DirectoryInfo" />클래스입니다.
                        </summary>
      <returns>마지막에 지정 된 디렉터리
                                <paramref name="path" />.
                            </returns>
      <param name="path">지정된 경로입니다.이 경로는 다른 디스크 볼륨이나 UNC(Universal Naming Convention) 이름이 될 수 없습니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />올바른 파일 경로 지정 하지 않는 또는 잘못 된 포함
                                        DirectoryInfo문자 수입니다.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />은
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 유효하지 않은 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우)</exception>
      <exception cref="T:System.IO.IOException">하위 디렉터리를 만들 수 없는 경우또는에 지정 된 이름에 이미 파일 또는 디렉터리
                                        <paramref name="path" />.
                                    </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.지정한 경로 및/또는 파일 이름이 너무 깁니다.</exception>
      <exception cref="T:System.Security.SecurityException">디렉터리를 만드는 데 필요한 코드 액세스 권한이 호출자에게 없는 경우또는호출자에 의해 반환 된 설명 하는 디렉터리를 읽을 수 코드 액세스 권한이 없는
                                    <see cref="T:System.IO.DirectoryInfo" /> 개체
                                이 발생할 수 있습니다 때는
                                    <paramref name="path" />매개 변수는 기존 디렉터리를 설명합니다.
                                </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />에 드라이브 레이블("C:\")의 일부가 아닌 콜론 문자(:)가 포함된 경우
                                    </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.Delete">
      <summary>이 삭제합니다.
                                <see cref="T:System.IO.DirectoryInfo" />비어 있으면 됩니다.
                            </summary>
      <exception cref="T:System.UnauthorizedAccessException">디렉터리는 읽기 전용 파일을 포함합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">이 설명 하는 디렉터리
                                        <see cref="T:System.IO.DirectoryInfo" />개체가 없거나 찾을 수 없습니다.
                                    </exception>
      <exception cref="T:System.IO.IOException">디렉터리가 비어 있지 않은 경우또는이 디렉터리는 응용 프로그램의 현재 작업 디렉터리입니다.또는디렉터리에 대한 열린 핸들이 있고 운영 체제가 Windows XP 또는 그 이전 버전인 경우.이 열린 핸들은 디렉터리 열거로 인해 발생할 수 있습니다.詳細については、次のトピックを参照してください。
                                    방법: 디렉터리 및 파일 열거.
                                </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.Delete(System.Boolean)">
      <summary>이 인스턴스를 삭제 한
                                <see cref="T:System.IO.DirectoryInfo" />하위 디렉터리와 파일을 삭제할지 여부를 지정 합니다.
                            </summary>
      <param name="recursive">true이 디렉터리, 해당 하위 디렉터리 및 모든 파일을 삭제 하려면 그렇지 않은 경우
                                    false.
                                </param>
      <exception cref="T:System.UnauthorizedAccessException">디렉터리는 읽기 전용 파일을 포함합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">이 설명 하는 디렉터리
                                        <see cref="T:System.IO.DirectoryInfo" />개체가 없거나 찾을 수 없습니다.
                                    </exception>
      <exception cref="T:System.IO.IOException">디렉터리가 읽기 전용인 경우또는하나 이상의 파일 또는 하위 디렉터리의 디렉터리를 포함 하 고
                                        <paramref name="recursive" />은
                                        false.
                                    또는이 디렉터리는 응용 프로그램의 현재 작업 디렉터리입니다.또는디렉터리 또는 해당 파일 중 하나에 대한 열린 핸들이 있고 운영 체제가 Windows XP 또는 그 이전 버전인 경우.이 열린 핸들은 디렉터리 및 파일 열거로 인해 발생할 수 있습니다.詳細については、次のトピックを参照してください。
                                    방법: 디렉터리 및 파일 열거.
                                </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories">
      <summary>현재 디렉터리에 있는 디렉터리 정보의 열거 가능 컬렉션을 반환합니다.</summary>
      <returns>현재 디렉터리에 있는 디렉터리의 열거 가능 컬렉션입니다.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">에 캡슐화 된 경로
                                        <see cref="T:System.IO.DirectoryInfo" />개체가 유효 하지 않습니다 (예, 것은 매핑되지 않은 드라이브에).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories(System.String)">
      <summary>지정된 검색 패턴과 일치하는 디렉터리 정보의 열거 가능 컬렉션을 반환합니다.</summary>
      <returns>일치 하는 디렉터리의 열거 가능한 컬렉션
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">디렉터리 이름과 일치하는지 비교할 검색 문자열입니다.이 매개 변수는 유효한 리터럴 경로와 와일드카드(* 및 ?) 문자(설명 참조)로 된 조합을 포함하지만 정규식을 지원하지 않습니다.기본 패턴은 모든 파일을 반환하는 "*"입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />은
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">에 캡슐화 된 경로
                                        <see cref="T:System.IO.DirectoryInfo" />개체가 유효 하지 않습니다 (예, 것은 매핑되지 않은 드라이브에).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories(System.String,System.IO.SearchOption)">
      <summary>지정된 검색 패턴 및 하위 디렉터리 검색 옵션과 일치하는 디렉터리 정보의 열거 가능 컬렉션을 반환합니다.</summary>
      <returns>일치 하는 디렉터리의 열거 가능한 컬렉션
                                <paramref name="searchPattern" />및
                                <paramref name="searchOption" />.
                            </returns>
      <param name="searchPattern">디렉터리 이름과 일치하는지 비교할 검색 문자열입니다.이 매개 변수는 유효한 리터럴 경로와 와일드카드(* 및 ?) 문자(설명 참조)로 된 조합을 포함하지만 정규식을 지원하지 않습니다.기본 패턴은 모든 파일을 반환하는 "*"입니다.</param>
      <param name="searchOption">검색 작업에 현재 디렉터리만 포함할지 아니면 모든 하위 디렉터리를 포함할지를 지정하는 열거형 값 중 하나입니다.기본값은
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />은
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />유효 하지 않기
                                        <see cref="T:System.IO.SearchOption" /> 값입니다.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">에 캡슐화 된 경로
                                        <see cref="T:System.IO.DirectoryInfo" />개체가 유효 하지 않습니다 (예, 것은 매핑되지 않은 드라이브에).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles">
      <summary>현재 디렉터리에 있는 파일 정보의 열거 가능 컬렉션을 반환합니다.</summary>
      <returns>현재 디렉터리에 있는 파일의 열거 가능 컬렉션입니다.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">에 캡슐화 된 경로
                                        <see cref="T:System.IO.DirectoryInfo" />개체가 유효 하지 않습니다 (예, 것은 매핑되지 않은 드라이브에).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles(System.String)">
      <summary>검색 패턴과 일치하는 파일 정보의 열거 가능 컬렉션을 반환합니다.</summary>
      <returns>일치 하는 파일의 열거 가능한 컬렉션
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">파일 이름과 일치하는지 비교할 검색 문자열입니다.이 매개 변수는 유효한 리터럴 경로와 와일드카드(* 및 ?) 문자(설명 참조)로 된 조합을 포함하지만 정규식을 지원하지 않습니다.기본 패턴은 모든 파일을 반환하는 "*"입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />은
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">에 캡슐화 된 경로
                                        <see cref="T:System.IO.DirectoryInfo" />개체가 유효 하지 않습니다 (예, 역할 합니다 매핑되지 않은 드라이브에).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles(System.String,System.IO.SearchOption)">
      <summary>지정된 검색 패턴 및 하위 디렉터리 검색 옵션과 일치하는 파일 정보의 열거 가능 컬렉션을 반환합니다.</summary>
      <returns>일치 하는 파일의 열거 가능한 컬렉션
                                <paramref name="searchPattern" />및
                                <paramref name="searchOption" />.
                            </returns>
      <param name="searchPattern">파일 이름과 일치하는지 비교할 검색 문자열입니다.이 매개 변수는 유효한 리터럴 경로와 와일드카드(* 및 ?) 문자(설명 참조)로 된 조합을 포함하지만 정규식을 지원하지 않습니다.기본 패턴은 모든 파일을 반환하는 "*"입니다.</param>
      <param name="searchOption">검색 작업에 현재 디렉터리만 포함할지 아니면 모든 하위 디렉터리를 포함할지를 지정하는 열거형 값 중 하나입니다.기본값은
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />은
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />유효 하지 않기
                                        <see cref="T:System.IO.SearchOption" /> 값입니다.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">에 캡슐화 된 경로
                                        <see cref="T:System.IO.DirectoryInfo" />개체가 유효 하지 않습니다 (예, 것은 매핑되지 않은 드라이브에).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos">
      <summary>현재 디렉터리에 있는 파일 시스템 정보의 열거 가능 컬렉션을 반환합니다.</summary>
      <returns>현재 디렉터리에 있는 파일 시스템 정보의 열거 가능 컬렉션입니다.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">에 캡슐화 된 경로
                                        <see cref="T:System.IO.DirectoryInfo" />개체가 유효 하지 않습니다 (예, 것은 매핑되지 않은 드라이브에).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos(System.String)">
      <summary>지정된 검색 패턴과 일치하는 파일 시스템 정보의 열거 가능 컬렉션을 반환합니다.</summary>
      <returns>일치 하는 파일 시스템 정보 개체의 열거 가능한 컬렉션
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">디렉터리 이름과 일치하는지 비교할 검색 문자열입니다.이 매개 변수는 유효한 리터럴 경로와 와일드카드(* 및 ?) 문자(설명 참조)로 된 조합을 포함하지만 정규식을 지원하지 않습니다.기본 패턴은 모든 파일을 반환하는 "*"입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />은
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">에 캡슐화 된 경로
                                        <see cref="T:System.IO.DirectoryInfo" />개체가 유효 하지 않습니다 (예, 것은 매핑되지 않은 드라이브에).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos(System.String,System.IO.SearchOption)">
      <summary>지정된 검색 패턴 및 하위 디렉터리 검색 옵션과 일치하는 파일 시스템 정보의 열거 가능 컬렉션을 반환합니다.</summary>
      <returns>일치 하는 파일 시스템 정보 개체의 열거 가능한 컬렉션
                                <paramref name="searchPattern" />및
                                <paramref name="searchOption" />.
                            </returns>
      <param name="searchPattern">디렉터리 이름과 일치하는지 비교할 검색 문자열입니다.이 매개 변수는 유효한 리터럴 경로와 와일드카드(* 및 ?) 문자(설명 참조)로 된 조합을 포함하지만 정규식을 지원하지 않습니다.기본 패턴은 모든 파일을 반환하는 "*"입니다.</param>
      <param name="searchOption">검색 작업에 현재 디렉터리만 포함할지 아니면 모든 하위 디렉터리를 포함할지를 지정하는 열거형 값 중 하나입니다.기본값은
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />은
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />유효 하지 않기
                                        <see cref="T:System.IO.SearchOption" /> 값입니다.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">에 캡슐화 된 경로
                                        <see cref="T:System.IO.DirectoryInfo" />개체가 유효 하지 않습니다 (예, 것은 매핑되지 않은 드라이브에).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우</exception>
    </member>
    <member name="P:System.IO.DirectoryInfo.Exists">
      <summary>디렉터리가 있는지를 나타내는 값을 가져옵니다.</summary>
      <returns>true디렉터리가 존재 합니다. 그렇지 않은 경우
                                false.
                            </returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories">
      <summary>현재 디렉터리의 하위 디렉터리를 반환합니다.</summary>
      <returns>배열
                                <see cref="T:System.IO.DirectoryInfo" /> 개체
                            </returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">에 캡슐화 된 경로
                                        <see cref="T:System.IO.DirectoryInfo" />개체는 매핑되지 않은 드라이브와 같은 올바르지 않습니다.
                                    </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우</exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories(System.String)">
      <summary>현재에서 디렉터리의 배열을 반환 합니다.
                                <see cref="T:System.IO.DirectoryInfo" />지정 된 검색 조건과 일치 합니다.
                            </summary>
      <returns>형식의 배열
                                DirectoryInfo일치 하는
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">디렉터리 이름과 일치하는지 비교할 검색 문자열입니다.이 매개 변수는 유효한 리터럴 경로와 와일드카드(* 및 ?) 문자(설명 참조)로 된 조합을 포함하지만 정규식을 지원하지 않습니다.기본 패턴은 모든 파일을 반환하는 "*"입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />정의한 하나 이상의 잘못 된 문자가 포함 된
                                        <see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드를 호출하여 생성됩니다.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />은
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">에 캡슐화 된 경로
                                        DirectoryInfo개체가 유효 하지 않습니다 (예, 것은 매핑되지 않은 드라이브에).
                                    </exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories(System.String,System.IO.SearchOption)">
      <summary>현재에서 디렉터리의 배열을 반환 합니다.
                                <see cref="T:System.IO.DirectoryInfo" />지정 된 검색 조건과 일치 하는 및 값을 사용 하 여 하위 디렉터리를 검색할지 여부를 결정 합니다.
                            </summary>
      <returns>형식의 배열
                                DirectoryInfo일치 하는
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">디렉터리 이름과 일치하는지 비교할 검색 문자열입니다.이 매개 변수는 유효한 리터럴 경로와 와일드카드(* 및 ?) 문자(설명 참조)로 된 조합을 포함하지만 정규식을 지원하지 않습니다.기본 패턴은 모든 파일을 반환하는 "*"입니다.</param>
      <param name="searchOption">검색 작업에 현재 디렉터리만 포함할지 아니면 모든 하위 디렉터리를 포함할지를 지정하는 열거형 값 중 하나입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />정의한 하나 이상의 잘못 된 문자가 포함 된
                                        <see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드를 호출하여 생성됩니다.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />은
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />유효 하지 않기
                                        <see cref="T:System.IO.SearchOption" /> 값입니다.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">에 캡슐화 된 경로
                                        DirectoryInfo개체가 유효 하지 않습니다 (예, 것은 매핑되지 않은 드라이브에).
                                    </exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles">
      <summary>현재 디렉터리에서 파일 목록을 반환합니다.</summary>
      <returns>형식의 배열
                                <see cref="T:System.IO.FileInfo" />.
                            </returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">경로가 유효하지 않은 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우)</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles(System.String)">
      <summary>현재 디렉터리에서 지정된 검색 패턴과 일치하는 파일 목록을 반환합니다.</summary>
      <returns>형식의 배열
                                <see cref="T:System.IO.FileInfo" />.
                            </returns>
      <param name="searchPattern">파일 이름과 일치하는지 비교할 검색 문자열입니다.이 매개 변수는 유효한 리터럴 경로와 와일드카드(* 및 ?) 문자(설명 참조)로 된 조합을 포함하지만 정규식을 지원하지 않습니다.기본 패턴은 모든 파일을 반환하는 "*"입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />정의한 하나 이상의 잘못 된 문자가 포함 된
                                        <see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드를 호출하여 생성됩니다.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />은
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우)</exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles(System.String,System.IO.SearchOption)">
      <summary>하위 디렉터리를 검색할지를 나타내는 값을 사용하여 현재 디렉터리에서 지정된 검색 패턴과 일치하는 파일 목록을 반환합니다.</summary>
      <returns>형식의 배열
                                <see cref="T:System.IO.FileInfo" />.
                            </returns>
      <param name="searchPattern">파일 이름과 일치하는지 비교할 검색 문자열입니다.이 매개 변수는 유효한 리터럴 경로와 와일드카드(* 및 ?) 문자(설명 참조)로 된 조합을 포함하지만 정규식을 지원하지 않습니다.기본 패턴은 모든 파일을 반환하는 "*"입니다.</param>
      <param name="searchOption">검색 작업에 현재 디렉터리만 포함할지 아니면 모든 하위 디렉터리를 포함할지를 지정하는 열거형 값 중 하나입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />정의한 하나 이상의 잘못 된 문자가 포함 된
                                        <see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드를 호출하여 생성됩니다.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />은
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />유효 하지 않기
                                        <see cref="T:System.IO.SearchOption" /> 값입니다.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우)</exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos">
      <summary>강력한 형식의 배열을 반환 합니다.
                                <see cref="T:System.IO.FileSystemInfo" />모든 파일 및 디렉터리에 하위 디렉터리를 나타내는 항목입니다.
                            </summary>
      <returns>배열을 강력 하 게 형식화합니다.
                                <see cref="T:System.IO.FileSystemInfo" />항목입니다.
                            </returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우)</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos(System.String)">
      <summary>강력한 형식의 배열을 검색 합니다.
                                <see cref="T:System.IO.FileSystemInfo" />파일 및 지정 된 검색 조건과 일치 하는 하위 디렉터리를 나타내는 개체입니다.
                            </summary>
      <returns>배열을 강력 하 게 형식화합니다.
                                FileSystemInfo검색 조건과 일치 하는 개체입니다.
                            </returns>
      <param name="searchPattern">파일에 있는 디렉터리 이름과 일치하는지 비교할 검색 문자열입니다.이 매개 변수는 유효한 리터럴 경로와 와일드카드(* 및 ?) 문자(설명 참조)로 된 조합을 포함하지만 정규식을 지원하지 않습니다.기본 패턴은 모든 파일을 반환하는 "*"입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />정의한 하나 이상의 잘못 된 문자가 포함 된
                                        <see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드를 호출하여 생성됩니다.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />은
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우)</exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos(System.String,System.IO.SearchOption)">
      <summary>배열을 검색합니다.
                                <see cref="T:System.IO.FileSystemInfo" />파일 및 지정 된 검색 조건과 일치 하는 하위 디렉터리를 나타내는 개체입니다.
                            </summary>
      <returns>검색 조건과 일치하는 파일 시스템 항목의 배열입니다.</returns>
      <param name="searchPattern">디렉터리 및 파일 이름과 일치하는지 비교할 검색 문자열입니다.이 매개 변수는 유효한 리터럴 경로와 와일드카드(* 및 ?) 문자(설명 참조)로 된 조합을 포함하지만 정규식을 지원하지 않습니다.기본 패턴은 모든 파일을 반환하는 "*"입니다.</param>
      <param name="searchOption">검색 작업에 현재 디렉터리만 포함할지 아니면 모든 하위 디렉터리를 포함할지를 지정하는 열거형 값 중 하나입니다.기본값은
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />정의한 하나 이상의 잘못 된 문자가 포함 된
                                        <see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드를 호출하여 생성됩니다.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />은
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />유효 하지 않기
                                        <see cref="T:System.IO.SearchOption" /> 값입니다.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우)</exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.MoveTo(System.String)">
      <summary>이동 하는
                                <see cref="T:System.IO.DirectoryInfo" />인스턴스 및 해당 내용을 새 경로에 있습니다.
                            </summary>
      <param name="destDirName">이 디렉터리를 이동할 곳의 이름과 경로입니다.대상 디렉터리는 다른 디스크 볼륨이나 동일한 이름의 디렉터리가 될 수는 없지만,이 디렉터리를 하위 디렉터리로 추가할 기존 디렉터리가 될 수는 있습니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destDirName" />은
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="destDirName" />이 빈 문자열("")인 경우
                                    </exception>
      <exception cref="T:System.IO.IOException">디렉터리를 다른 볼륨으로 이동하려고 한 경우또는<paramref name="destDirName" />이(가) 이미 있습니다.
                                    또는이 경로에 액세스할 권한이 없는 경우또는이동되는 디렉터리와 대상 디렉터리의 이름이 같은 경우</exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">대상 디렉터리를 찾을 수 없는 경우</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.DirectoryInfo.Name">
      <summary>이 작업의 이름을 가져옵니다.
                                <see cref="T:System.IO.DirectoryInfo" />인스턴스입니다.
                            </summary>
      <returns>디렉터리 이름입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.DirectoryInfo.Parent">
      <summary>지정된 하위 디렉터리의 부모 디렉터리를 가져옵니다.</summary>
      <returns>부모 디렉터리 또는
                                null경로 null 또는 파일 경로 루트를 표시 하는 경우 ("\", "c:"와 같은 또는 * "\\server\share").
                            </returns>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.DirectoryInfo.Root">
      <summary>디렉터리의 루트 부분을 가져옵니다.</summary>
      <returns>디렉터리의 루트를 나타내는 개체입니다.</returns>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.ToString">
      <summary>사용자가 통과한 원래의 경로를 반환합니다.</summary>
      <returns>사용자가 통과한 원래의 경로를 반환합니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.IO.File">
      <summary>단일 파일에 대한 만들기, 복사, 삭제, 이동 및 열기를 위한 정적 메서드를 제공하고 <see cref="T:System.IO.FileStream" /> 개체 만들기를 지원합니다.이 형식에 대 한.NET Framework 소스 코드를 찾아보려면 참조는 Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.File.AppendAllLines(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>파일에 줄을 추가한 다음 파일을 닫습니다.지정한 파일이 없는 경우 이 메서드는 파일을 만들고 지정된 줄을 파일에 쓴 다음 파일을 닫습니다.</summary>
      <param name="path">줄을 추가할 파일입니다.파일이 아직 없으면 새로 만들어집니다.</param>
      <param name="contents">파일에 추가할 줄입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드에 정의된 하나 이상의 잘못된 문자를 포함하는 경우</exception>
      <exception cref="T:System.ArgumentNullException">두<paramref name=" path " />또는 <paramref name="contents" /> 은 null입니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" />가 잘못된 경우(예: 디렉터리가 존재하지 않거나 매핑되지 않은 드라이브의 경로를 지정한 경우)</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" />에 지정된 파일을 찾을 수 없는 경우</exception>
      <exception cref="T:System.IO.IOException">파일을 여는 동안 I/O 오류가 발생한 경우</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" />가 시스템 정의 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우</exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 해당 파일에 쓸 수 있는 권한이 없는 경우</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" />가 읽기 전용인 파일을 지정하는 경우또는현재 플랫폼이 해당 작업을 지원하지 않는 경우또는<paramref name="path" />은(는) 디렉터리입니다.</exception>
    </member>
    <member name="M:System.IO.File.AppendAllLines(System.String,System.Collections.Generic.IEnumerable{System.String},System.Text.Encoding)">
      <summary>지정된 인코딩을 사용하여 파일에 줄을 추가한 다음 파일을 닫습니다.지정한 파일이 없는 경우 이 메서드는 파일을 만들고 지정된 줄을 파일에 쓴 다음 파일을 닫습니다.</summary>
      <param name="path">줄을 추가할 파일입니다.파일이 아직 없으면 새로 만들어집니다.</param>
      <param name="contents">파일에 추가할 줄입니다.</param>
      <param name="encoding">사용할 문자 인코딩입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드에 정의된 하나 이상의 잘못된 문자를 포함하는 경우</exception>
      <exception cref="T:System.ArgumentNullException">Either<paramref name=" path" />, <paramref name="contents" /> 또는 <paramref name="encoding" />이 null인 경우</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" />가 잘못된 경우(예: 디렉터리가 존재하지 않거나 매핑되지 않은 드라이브의 경로를 지정한 경우)</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" />에 지정된 파일을 찾을 수 없는 경우</exception>
      <exception cref="T:System.IO.IOException">파일을 여는 동안 I/O 오류가 발생한 경우</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" />가 시스템 정의 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우</exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" />가 읽기 전용인 파일을 지정하는 경우또는현재 플랫폼이 해당 작업을 지원하지 않는 경우또는<paramref name="path" />은(는) 디렉터리입니다.또는호출자에게 필요한 권한이 없는 경우</exception>
    </member>
    <member name="M:System.IO.File.AppendAllText(System.String,System.String)">
      <summary>파일을 열고 지정된 문자열을 파일에 추가한 다음 파일을 닫습니다.파일이 없는 경우 이 메서드는 파일을 만들고 지정된 문자열을 파일에 쓴 다음 파일을 닫습니다.</summary>
      <param name="path">지정된 문자열을 추가할 파일입니다. </param>
      <param name="contents">파일에 추가할 문자열입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못된 경우(예: 디렉터리가 존재하지 않거나 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.IO.IOException">파일을 여는 동안 I/O 오류가 발생한 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" />가 읽기 전용인 파일을 지정하는 경우또는 현재 플랫폼이 해당 작업을 지원하지 않는 경우또는 <paramref name="path" />가 디렉터리를 지정한 경우또는 호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.AppendAllText(System.String,System.String,System.Text.Encoding)">
      <summary>지정된 문자열을 파일에 추가합니다. 파일이 아직 없으면 만듭니다.</summary>
      <param name="path">지정된 문자열을 추가할 파일입니다. </param>
      <param name="contents">파일에 추가할 문자열입니다. </param>
      <param name="encoding">사용할 문자 인코딩입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못된 경우(예: 디렉터리가 존재하지 않거나 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.IO.IOException">파일을 여는 동안 I/O 오류가 발생한 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" />가 읽기 전용인 파일을 지정하는 경우또는 현재 플랫폼이 해당 작업을 지원하지 않는 경우또는 <paramref name="path" />가 디렉터리를 지정한 경우또는 호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.AppendText(System.String)">
      <summary>기존 파일 또는 지정된 파일이 존재하지 않는 경우 새 파일에 UTF-8 인코딩된 텍스트를 추가하는 <see cref="T:System.IO.StreamWriter" />를 만듭니다.</summary>
      <returns>UTF-8로 인코딩된 텍스트를 지정된 파일 또는 새 파일에 추가하는 스트림 작성기입니다.</returns>
      <param name="path">추가 대상인 파일에 대한 경로입니다. </param>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못된 경우(예: 디렉터리가 존재하지 않거나 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Copy(System.String,System.String)">
      <summary>새 파일에 기존 파일을 복사합니다.같은 이름의 파일을 덮어쓸 수는 없습니다.</summary>
      <param name="sourceFileName">복사할 파일입니다. </param>
      <param name="destFileName">대상 파일 이름입니다.대상 파일 이름은 디렉터리나 기존 파일일 수 없습니다.</param>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> 또는 <paramref name="destFileName" />이 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />에 정의된 하나 이상의 잘못된 문자를 포함하는 경우또는 <paramref name="sourceFileName" /> 또는 <paramref name="destFileName" />이 디렉터리를 지정하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> 또는 <paramref name="destFileName" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceFileName" /> 또는 <paramref name="destFileName" />에 지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="sourceFileName" />을(를) 찾을 수 없습니다. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destFileName" />이(가) 있습니다.또는 I/O 오류가 발생한 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceFileName" /> 또는 <paramref name="destFileName" />의 형식이 잘못된 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Copy(System.String,System.String,System.Boolean)">
      <summary>새 파일에 기존 파일을 복사합니다.같은 이름의 파일을 덮어쓸 수 있습니다.</summary>
      <param name="sourceFileName">복사할 파일입니다. </param>
      <param name="destFileName">대상 파일 이름입니다.대상 파일 이름은 디렉터리가 될 수 없습니다.</param>
      <param name="overwrite">대상 파일을 덮어쓸 수 있으면 true이고, 그렇지 않으면 false입니다. </param>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 또는<paramref name="destFileName" />는 읽기 전용입니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> 또는 <paramref name="destFileName" />이 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />에 정의된 하나 이상의 잘못된 문자를 포함하는 경우또는 <paramref name="sourceFileName" /> 또는 <paramref name="destFileName" />이 디렉터리를 지정하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> 또는 <paramref name="destFileName" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceFileName" /> 또는 <paramref name="destFileName" />에 지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="sourceFileName" />을(를) 찾을 수 없습니다. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destFileName" />이 존재하고 <paramref name="overwrite" />는 false입니다.또는 I/O 오류가 발생한 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceFileName" /> 또는 <paramref name="destFileName" />의 형식이 잘못된 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Create(System.String)">
      <summary>지정된 경로에 파일을 만들거나 지정된 경로의 파일을 덮어씁니다.</summary>
      <returns>
        <see cref="T:System.IO.FileStream" />에 지정된 파일에 대한 읽기/쓰기 권한을 제공하는 <paramref name="path" />입니다.</returns>
      <param name="path">만들 파일의 경로와 이름입니다. </param>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우또는 <paramref name="path" />가 읽기 전용인 파일을 지정하는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.IO.IOException">파일을 만드는 동안 I/O 오류가 발생한 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Create(System.String,System.Int32)">
      <summary>지정된 파일을 만들거나 덮어씁니다.</summary>
      <returns>
        <see cref="T:System.IO.FileStream" />에 지정된 파일에 대한 읽기/쓰기 권한을 제공하는 지정된 버퍼 크기의 <paramref name="path" />입니다.</returns>
      <param name="path">파일의 이름입니다. </param>
      <param name="bufferSize">파일 읽기 및 쓰기를 위해 버퍼링할 바이트 수입니다. </param>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우또는 <paramref name="path" />가 읽기 전용인 파일을 지정하는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.IO.IOException">파일을 만드는 동안 I/O 오류가 발생한 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Create(System.String,System.Int32,System.IO.FileOptions)">
      <summary>버퍼 크기와 파일을 만들거나 덮어쓸 방식을 설명하는 <see cref="T:System.IO.FileOptions" /> 값을 지정하여 지정된 파일을 만들거나 덮어씁니다.</summary>
      <returns>지정된 버퍼 크기의 새 파일입니다.</returns>
      <param name="path">파일의 이름입니다. </param>
      <param name="bufferSize">파일 읽기 및 쓰기를 위해 버퍼링할 바이트 수입니다. </param>
      <param name="options">파일을 만들거나 덮어쓸 방식을 설명하는 <see cref="T:System.IO.FileOptions" /> 값 중 하나입니다.</param>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우또는 <paramref name="path" />가 읽기 전용인 파일을 지정하는 경우 또는<see cref="F:System.IO.FileOptions.Encrypted" />에 <paramref name="options" />가 지정되었고 현재 플랫폼에서 파일 암호화가 지원되지 않는 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.IO.IOException">파일을 만드는 동안 I/O 오류가 발생한 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우또는 <paramref name="path" />가 읽기 전용인 파일을 지정하는 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우또는 <paramref name="path" />가 읽기 전용인 파일을 지정하는 경우 </exception>
    </member>
    <member name="M:System.IO.File.CreateText(System.String)">
      <summary>UTF-8로 인코딩된 텍스트를 쓰기 위해 파일을 만들거나 엽니다.</summary>
      <returns>UTF-8 인코딩을 사용하여 지정된 파일에 쓸 <see cref="T:System.IO.StreamWriter" />입니다.</returns>
      <param name="path">쓰기용으로 열 파일입니다. </param>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Delete(System.String)">
      <summary>지정한 파일을 삭제합니다. </summary>
      <param name="path">삭제할 파일의 이름입니다.와일드카드 문자는 지원되지 않습니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.IO.IOException">지정된 파일이 사용되고 있는 경우 또는파일에 대한 열린 핸들이 있고 운영 체제가 Windows XP 또는 그 이전 버전인 경우.이 열린 핸들은 디렉터리 및 파일 열거로 인해 발생할 수 있습니다.자세한 내용은 방법: 디렉터리 및 파일 열거을 참조하십시오.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우또는 파일이 사용 중인 실행 파일입니다.또는 <paramref name="path" />은(는) 디렉터리입니다.또는 <paramref name="path" />에서 읽기 전용 파일을 지정한 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Exists(System.String)">
      <summary>지정된 파일이 있는지를 확인합니다.</summary>
      <returns>호출자에게 필요한 권한이 있고 true에 기존 파일의 이름이 포함되면 <paramref name="path" />이고, 그렇지 않으면 false입니다.또한 이 메서드는 false가 <paramref name="path" />이거나 잘못된 경로이거나 길이가 0인 문자열이면 null를 반환합니다.호출자에게 지정된 파일을 읽을 권한이 없는 경우 예외가 throw되지 않으며 false가 있는지 여부와 관계없이 <paramref name="path" />를 반환합니다.</returns>
      <param name="path">확인할 파일입니다. </param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetAttributes(System.String)">
      <summary>경로에 있는 파일의 <see cref="T:System.IO.FileAttributes" />를 가져옵니다.</summary>
      <returns>경로에 있는 파일의 <see cref="T:System.IO.FileAttributes" />입니다.</returns>
      <param name="path">파일의 경로입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />에 아무 것도 없거나, 공백만 있거나, 잘못된 문자가 포함되어 있는 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" />가 파일을 나타내며 유효하지 않은 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우나 파일을 찾을 수 없는 경우) </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" />가 디렉터리를 나타내며 유효하지 않은 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우나 디렉터리를 찾을 수 없는 경우)</exception>
      <exception cref="T:System.IO.IOException">이 파일은 다른 프로세스에서 사용되고 있습니다.</exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetCreationTime(System.String)">
      <summary>지정된 파일 또는 디렉터리의 만든 날짜와 시간을 반환합니다.</summary>
      <returns>지정된 파일이나 디렉터리를 만든 날짜와 시간으로 설정된 <see cref="T:System.DateTime" /> 구조체입니다.이 값은 현지 시간으로 표현됩니다.</returns>
      <param name="path">만든 날짜와 시간 정보를 가져올 파일 또는 디렉터리입니다. </param>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetCreationTimeUtc(System.String)">
      <summary>지정된 파일이나 디렉터리를 만든 날짜와 시간을 UTC 기준으로 반환합니다.</summary>
      <returns>지정된 파일이나 디렉터리를 만든 날짜와 시간으로 설정된 <see cref="T:System.DateTime" /> 구조체입니다.이 값은 UTC 시간으로 표현됩니다.</returns>
      <param name="path">만든 날짜와 시간 정보를 가져올 파일 또는 디렉터리입니다. </param>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastAccessTime(System.String)">
      <summary>지정된 파일 또는 디렉터리를 마지막으로 액세스한 날짜와 시간을 반환합니다.</summary>
      <returns>지정된 파일 또는 디렉터리를 마지막으로 액세스한 날짜와 시간으로 설정된 <see cref="T:System.DateTime" /> 구조체입니다.이 값은 현지 시간으로 표현됩니다.</returns>
      <param name="path">액세스 날짜와 시간 정보를 가져올 파일 또는 디렉터리입니다. </param>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastAccessTimeUtc(System.String)">
      <summary>지정된 파일 또는 디렉터리를 마지막으로 액세스한 날짜와 시간을 UTC 기준으로 반환합니다.</summary>
      <returns>지정된 파일 또는 디렉터리를 마지막으로 액세스한 날짜와 시간으로 설정된 <see cref="T:System.DateTime" /> 구조체입니다.이 값은 UTC 시간으로 표현됩니다.</returns>
      <param name="path">액세스 날짜와 시간 정보를 가져올 파일 또는 디렉터리입니다. </param>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastWriteTime(System.String)">
      <summary>지정된 파일 또는 디렉터리를 마지막으로 쓴 날짜와 시간을 반환합니다.</summary>
      <returns>지정된 파일 또는 디렉터리를 마지막으로 쓴 날짜와 시간으로 설정된 <see cref="T:System.DateTime" /> 구조체입니다.이 값은 현지 시간으로 표현됩니다.</returns>
      <param name="path">쓴 날짜와 시간 정보를 가져올 파일 또는 디렉터리입니다. </param>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastWriteTimeUtc(System.String)">
      <summary>지정된 파일 또는 디렉터리에 마지막으로 쓴 날짜와 시간을 UTC 기준으로 반환합니다.</summary>
      <returns>지정된 파일 또는 디렉터리를 마지막으로 쓴 날짜와 시간으로 설정된 <see cref="T:System.DateTime" /> 구조체입니다.이 값은 UTC 시간으로 표현됩니다.</returns>
      <param name="path">쓴 날짜와 시간 정보를 가져올 파일 또는 디렉터리입니다. </param>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Move(System.String,System.String)">
      <summary>지정된 파일을 새 위치로 이동하고 새 파일의 이름을 지정할 수 있는 옵션을 제공합니다.</summary>
      <param name="sourceFileName">이동할 파일의 이름입니다.상대 또는 절대 경로가 포함될 수 있습니다.</param>
      <param name="destFileName">파일에 대한 새 경로 및 이름입니다.</param>
      <exception cref="T:System.IO.IOException">대상 파일이 이미 있는 경우또는<paramref name="sourceFileName" />을(를) 찾을 수 없습니다. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> 또는 <paramref name="destFileName" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> 또는 <paramref name="destFileName" />이 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />에 정의된 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceFileName" /> 또는 <paramref name="destFileName" />에 지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceFileName" /> 또는 <paramref name="destFileName" />의 형식이 잘못된 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode)">
      <summary>지정된 경로에서 읽기/쓰기 권한으로 <see cref="T:System.IO.FileStream" />을 엽니다.</summary>
      <returns>읽기/쓰기 액세스 및 공유하지 않는 상태로 지정된 모드와 경로에서 열린 <see cref="T:System.IO.FileStream" />입니다.</returns>
      <param name="path">열 파일입니다. </param>
      <param name="mode">파일이 없는 경우 파일을 만들 것인지를 지정하고, 기존 파일 내용을 유지할 것인지 덮어쓸 것인지를 결정하는 <see cref="T:System.IO.FileMode" /> 값입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.IO.IOException">파일을 여는 동안 I/O 오류가 발생한 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" />가 읽기 전용인 파일을 지정하는 경우또는 현재 플랫폼이 해당 작업을 지원하지 않는 경우또는 <paramref name="path" />가 디렉터리를 지정한 경우또는 호출자에게 필요한 권한이 없는 경우 또는<paramref name="mode" />는 <see cref="F:System.IO.FileMode.Create" />이고 지정된 파일은 숨겨진 파일입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" />가 잘못된 값을 지정하는 경우 </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" />에 지정된 파일을 찾을 수 없는 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode,System.IO.FileAccess)">
      <summary>지정된 모드와 액세스 권한을 사용하여 지정된 경로에서 <see cref="T:System.IO.FileStream" />을 엽니다.</summary>
      <returns>지정된 모드와 액세스 권한으로 지정된 파일에 대한 액세스를 제공하는 공유되지 않는 <see cref="T:System.IO.FileStream" />입니다.</returns>
      <param name="path">열 파일입니다. </param>
      <param name="mode">파일이 없는 경우 파일을 만들 것인지를 지정하고, 기존 파일 내용을 유지할 것인지 덮어쓸 것인지를 결정하는 <see cref="T:System.IO.FileMode" /> 값입니다. </param>
      <param name="access">파일에 수행할 수 있는 작업을 지정하는 <see cref="T:System.IO.FileAccess" /> 값입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우또는 <paramref name="access" />가 Read로 지정되고 <paramref name="mode" />가 Create, CreateNew, Truncate 또는 Append로 지정된 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.IO.IOException">파일을 여는 동안 I/O 오류가 발생한 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" />가 읽기 전용인 파일을 지정하고 <paramref name="access" />가 Read가 아닌 경우또는 <paramref name="path" />가 디렉터리를 지정한 경우또는 호출자에게 필요한 권한이 없는 경우 또는<paramref name="mode" />는 <see cref="F:System.IO.FileMode.Create" />이고 지정된 파일은 숨겨진 파일입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> 또는 <paramref name="access" />가 잘못된 값을 지정하는 경우 </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" />에 지정된 파일을 찾을 수 없는 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>읽기, 쓰기 또는 읽기/쓰기 권한과 지정된 공유 옵션을 사용하여 지정된 경로에서 지정된 모드를 갖는 <see cref="T:System.IO.FileStream" />을 엽니다.</summary>
      <returns>읽기, 쓰기 또는 읽기/쓰기 권한과 지정된 공유 옵션을 사용하여 지정된 경로에서 지정된 모드를 갖는 <see cref="T:System.IO.FileStream" />을 엽니다.</returns>
      <param name="path">열 파일입니다. </param>
      <param name="mode">파일이 없는 경우 파일을 만들 것인지를 지정하고, 기존 파일 내용을 유지할 것인지 덮어쓸 것인지를 결정하는 <see cref="T:System.IO.FileMode" /> 값입니다. </param>
      <param name="access">파일에 수행할 수 있는 작업을 지정하는 <see cref="T:System.IO.FileAccess" /> 값입니다. </param>
      <param name="share">다른 스레드가 파일에 대해 가지는 액세스 형식을 지정하는 <see cref="T:System.IO.FileShare" /> 값입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우또는 <paramref name="access" />가 Read로 지정되고 <paramref name="mode" />가 Create, CreateNew, Truncate 또는 Append로 지정된 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.IO.IOException">파일을 여는 동안 I/O 오류가 발생한 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" />가 읽기 전용인 파일을 지정하고 <paramref name="access" />가 Read가 아닌 경우또는 <paramref name="path" />가 디렉터리를 지정한 경우또는 호출자에게 필요한 권한이 없는 경우 또는<paramref name="mode" />는 <see cref="F:System.IO.FileMode.Create" />이고 지정된 파일은 숨겨진 파일입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" />, <paramref name="access" /> 또는 <paramref name="share" />가 잘못된 값을 지정하는 경우 </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" />에 지정된 파일을 찾을 수 없는 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.OpenRead(System.String)">
      <summary>읽기용으로 기존 파일을 엽니다.</summary>
      <returns>지정된 경로에 있는 읽기 전용 <see cref="T:System.IO.FileStream" />입니다.</returns>
      <param name="path">읽기용으로 열 파일입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" />가 디렉터리를 지정한 경우또는 호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" />에 지정된 파일을 찾을 수 없는 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <exception cref="T:System.IO.IOException">파일을 여는 동안 I/O 오류가 발생한 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.OpenText(System.String)">
      <summary>UTF-8로 인코딩된 기존 텍스트 파일을 읽기용으로 엽니다.</summary>
      <returns>지정된 경로에 있는 <see cref="T:System.IO.StreamReader" />입니다.</returns>
      <param name="path">읽기용으로 열 파일입니다. </param>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" />에 지정된 파일을 찾을 수 없는 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.OpenWrite(System.String)">
      <summary>쓰기 위해 기존 파일을 열거나 새 파일을 만듭니다.</summary>
      <returns>
        <see cref="T:System.IO.FileStream" /> 액세스 권한이 있는 지정된 경로에서 공유되지 않은 <see cref="F:System.IO.FileAccess.Write" /> 개체입니다.</returns>
      <param name="path">쓰기용으로 열 파일입니다. </param>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우또는 <paramref name="path" />가 읽기 전용 파일이나 디렉터리를 지정하는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllBytes(System.String)">
      <summary>이진 파일을 열고 파일의 내용을 바이트 배열로 읽어 들인 다음 파일을 닫습니다.</summary>
      <returns>파일의 내용을 포함하는 바이트 배열입니다.</returns>
      <param name="path">읽기 위해 열 파일입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.IO.IOException">파일을 여는 동안 I/O 오류가 발생한 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">현재 플랫폼이 해당 작업을 지원하지 않는 경우또는 <paramref name="path" />가 디렉터리를 지정한 경우또는 호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" />에 지정된 파일을 찾을 수 없는 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllLines(System.String)">
      <summary>텍스트 파일을 열고 파일의 모든 줄을 읽은 다음 파일을 닫습니다.</summary>
      <returns>파일의 모든 줄을 포함하는 문자열 배열입니다.</returns>
      <param name="path">읽기 위해 열 파일입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.IO.IOException">파일을 여는 동안 I/O 오류가 발생한 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" />가 읽기 전용인 파일을 지정하는 경우또는 현재 플랫폼이 해당 작업을 지원하지 않는 경우또는 <paramref name="path" />가 디렉터리를 지정한 경우또는 호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" />에 지정된 파일을 찾을 수 없는 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllLines(System.String,System.Text.Encoding)">
      <summary>파일을 열고 지정된 인코딩을 사용하여 파일의 모든 줄을 읽은 다음 파일을 닫습니다.</summary>
      <returns>파일의 모든 줄을 포함하는 문자열 배열입니다.</returns>
      <param name="path">읽기 위해 열 파일입니다. </param>
      <param name="encoding">파일의 내용에 적용되는 인코딩입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.IO.IOException">파일을 여는 동안 I/O 오류가 발생한 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" />가 읽기 전용인 파일을 지정하는 경우또는 현재 플랫폼이 해당 작업을 지원하지 않는 경우또는 <paramref name="path" />가 디렉터리를 지정한 경우또는 호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" />에 지정된 파일을 찾을 수 없는 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllText(System.String)">
      <summary>텍스트 파일을 열고 파일의 모든 줄을 읽은 다음 파일을 닫습니다.</summary>
      <returns>파일의 모든 줄을 포함하는 문자열입니다.</returns>
      <param name="path">읽기 위해 열 파일입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.IO.IOException">파일을 여는 동안 I/O 오류가 발생한 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" />가 읽기 전용인 파일을 지정하는 경우또는 현재 플랫폼이 해당 작업을 지원하지 않는 경우또는 <paramref name="path" />가 디렉터리를 지정한 경우또는 호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" />에 지정된 파일을 찾을 수 없는 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllText(System.String,System.Text.Encoding)">
      <summary>파일을 열고 지정된 인코딩을 사용하여 파일의 모든 줄을 읽은 다음 파일을 닫습니다.</summary>
      <returns>파일의 모든 줄을 포함하는 문자열입니다.</returns>
      <param name="path">읽기 위해 열 파일입니다. </param>
      <param name="encoding">파일의 내용에 적용되는 인코딩입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.IO.IOException">파일을 여는 동안 I/O 오류가 발생한 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" />가 읽기 전용인 파일을 지정하는 경우또는 현재 플랫폼이 해당 작업을 지원하지 않는 경우또는 <paramref name="path" />가 디렉터리를 지정한 경우또는 호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" />에 지정된 파일을 찾을 수 없는 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadLines(System.String)">
      <summary>파일의 줄을 읽습니다.</summary>
      <returns>파일의 모든 줄 또는 쿼리 결과의 줄입니다.</returns>
      <param name="path">읽을 파일입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 길이가 0 인 문자열, 공백만, 또는 정의한 하나 이상의 잘못 된 문자가 포함 된 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" />가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우)</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" />에 지정된 파일을 찾을 수 없는 경우</exception>
      <exception cref="T:System.IO.IOException">파일을 여는 동안 I/O 오류가 발생한 경우</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" />가 시스템 정의 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" />가 읽기 전용인 파일을 지정하는 경우또는현재 플랫폼이 해당 작업을 지원하지 않는 경우또는<paramref name="path" />은(는) 디렉터리입니다.또는호출자에게 필요한 권한이 없는 경우</exception>
    </member>
    <member name="M:System.IO.File.ReadLines(System.String,System.Text.Encoding)">
      <summary>지정된 인코딩을 가진 파일의 줄을 읽습니다.</summary>
      <returns>파일의 모든 줄 또는 쿼리 결과의 줄입니다.</returns>
      <param name="path">읽을 파일입니다.</param>
      <param name="encoding">파일의 내용에 적용되는 인코딩입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드에 정의된 하나 이상의 잘못된 문자를 포함하는 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" />가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우)</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" />에 지정된 파일을 찾을 수 없는 경우</exception>
      <exception cref="T:System.IO.IOException">파일을 여는 동안 I/O 오류가 발생한 경우</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" />가 시스템 정의 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" />가 읽기 전용인 파일을 지정하는 경우또는현재 플랫폼이 해당 작업을 지원하지 않는 경우또는<paramref name="path" />은(는) 디렉터리입니다.또는호출자에게 필요한 권한이 없는 경우</exception>
    </member>
    <member name="M:System.IO.File.SetAttributes(System.String,System.IO.FileAttributes)">
      <summary>지정된 경로에 있는 파일의 지정된 <see cref="T:System.IO.FileAttributes" />를 가져옵니다.</summary>
      <param name="path">파일의 경로입니다. </param>
      <param name="fileAttributes">열거형 값의 비트 조합입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 비어 있거나, 공백만 포함하거나, 잘못된 문자를 포함하거나 또는 파일 특성이 잘못된 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.IO.FileNotFoundException">파일을 찾을 수 없는 경우</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" />가 읽기 전용인 파일을 지정하는 경우또는 현재 플랫폼이 해당 작업을 지원하지 않는 경우또는 <paramref name="path" />가 디렉터리를 지정한 경우또는 호출자에게 필요한 권한이 없는 경우</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetCreationTime(System.String,System.DateTime)">
      <summary>파일이 만들어진 날짜와 시간을 설정합니다.</summary>
      <param name="path">만든 날짜와 시간 정보를 설정할 파일입니다. </param>
      <param name="creationTime">
        <see cref="T:System.DateTime" />의 만든 날짜와 시간을 설정할 값이 포함된 <paramref name="path" />입니다.이 값은 현지 시간으로 표현됩니다.</param>
      <exception cref="T:System.IO.FileNotFoundException">지정된 경로를 찾을 수 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.IOException">작업을 수행하는 동안 I/O 오류가 발생한 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" />에서 해당 작업에 허용된 날짜나 시간 또는 둘 모두의 범위를 벋어나는 값을 지정한 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetCreationTimeUtc(System.String,System.DateTime)">
      <summary>파일을 만든 날짜와 시간을 UTC 기준으로 설정합니다.</summary>
      <param name="path">만든 날짜와 시간 정보를 설정할 파일입니다. </param>
      <param name="creationTimeUtc">
        <see cref="T:System.DateTime" />의 만든 날짜와 시간을 설정할 값이 포함된 <paramref name="path" />입니다.이 값은 UTC 시간으로 표현됩니다.</param>
      <exception cref="T:System.IO.FileNotFoundException">지정된 경로를 찾을 수 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.IOException">작업을 수행하는 동안 I/O 오류가 발생한 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" />에서 해당 작업에 허용된 날짜나 시간 또는 둘 모두의 범위를 벋어나는 값을 지정한 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastAccessTime(System.String,System.DateTime)">
      <summary>지정된 파일을 마지막으로 액세스한 날짜와 시간을 설정합니다.</summary>
      <param name="path">액세스 날짜와 시간 정보를 설정할 파일입니다. </param>
      <param name="lastAccessTime">
        <see cref="T:System.DateTime" />에 마지막으로 액세스한 날짜와 시간을 설정할 값이 포함된 <paramref name="path" />입니다.이 값은 현지 시간으로 표현됩니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.FileNotFoundException">지정된 경로를 찾을 수 없는 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTime" />에서 해당 작업에 허용된 날짜나 시간 범위를 벗어나는 값을 지정한 경우</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastAccessTimeUtc(System.String,System.DateTime)">
      <summary>지정된 파일을 마지막으로 액세스한 날짜와 시간을 UTC 기준으로 설정합니다.</summary>
      <param name="path">액세스 날짜와 시간 정보를 설정할 파일입니다. </param>
      <param name="lastAccessTimeUtc">
        <see cref="T:System.DateTime" />에 마지막으로 액세스한 날짜와 시간을 설정할 값이 포함된 <paramref name="path" />입니다.이 값은 UTC 시간으로 표현됩니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.FileNotFoundException">지정된 경로를 찾을 수 없는 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTimeUtc" />에서 해당 작업에 허용된 날짜나 시간 범위를 벗어나는 값을 지정한 경우</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastWriteTime(System.String,System.DateTime)">
      <summary>지정된 파일에 마지막으로 쓴 날짜와 시간을 설정합니다.</summary>
      <param name="path">날짜와 시간 정보를 설정할 파일입니다. </param>
      <param name="lastWriteTime">
        <see cref="T:System.DateTime" />에 마지막으로 쓴 날짜와 시간을 설정할 값이 포함된 <paramref name="path" />입니다.이 값은 현지 시간으로 표현됩니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.FileNotFoundException">지정된 경로를 찾을 수 없는 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTime" />에서 해당 작업에 허용된 날짜나 시간 범위를 벗어나는 값을 지정한 경우</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastWriteTimeUtc(System.String,System.DateTime)">
      <summary>지정된 파일에 마지막으로 쓴 날짜와 시간을 UTC 기준으로 설정합니다.</summary>
      <param name="path">날짜와 시간 정보를 설정할 파일입니다. </param>
      <param name="lastWriteTimeUtc">
        <see cref="T:System.DateTime" />에 마지막으로 쓴 날짜와 시간을 설정할 값이 포함된 <paramref name="path" />입니다.이 값은 UTC 시간으로 표현됩니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.FileNotFoundException">지정된 경로를 찾을 수 없는 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTimeUtc" />에서 해당 작업에 허용된 날짜나 시간 범위를 벗어나는 값을 지정한 경우</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.WriteAllBytes(System.String,System.Byte[])">
      <summary>새 파일을 만들고 지정된 바이트 배열을 파일에 쓴 다음 파일을 닫습니다.대상 파일이 이미 있으면 덮어씁니다.</summary>
      <param name="path">쓸 파일입니다. </param>
      <param name="bytes">파일에 쓸 바이트입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null이거나 바이트 배열이 비어 있는 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.IO.IOException">파일을 여는 동안 I/O 오류가 발생한 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" />가 읽기 전용인 파일을 지정하는 경우또는 현재 플랫폼이 해당 작업을 지원하지 않는 경우또는 <paramref name="path" />가 디렉터리를 지정한 경우또는 호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.WriteAllLines(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>새 파일을 만들고 문자열의 컬렉션을 파일에 쓴 다음 파일을 닫습니다.</summary>
      <param name="path">쓸 파일입니다.</param>
      <param name="contents">파일에 쓸 줄입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 길이가 0 인 문자열, 공백만, 또는 정의한 하나 이상의 잘못 된 문자가 포함 된 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드.</exception>
      <exception cref="T:System.ArgumentNullException">두<paramref name=" path " />또는 <paramref name="contents" /> 은 null입니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" />가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우)</exception>
      <exception cref="T:System.IO.IOException">파일을 여는 동안 I/O 오류가 발생한 경우</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" />가 시스템 정의 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우</exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" />가 읽기 전용인 파일을 지정하는 경우또는현재 플랫폼이 해당 작업을 지원하지 않는 경우또는<paramref name="path" />은(는) 디렉터리입니다.또는호출자에게 필요한 권한이 없는 경우</exception>
    </member>
    <member name="M:System.IO.File.WriteAllLines(System.String,System.Collections.Generic.IEnumerable{System.String},System.Text.Encoding)">
      <summary>지정된 인코딩을 사용하여 새 파일을 만들고 문자열의 컬렉션을 파일에 쓴 다음 파일을 닫습니다.</summary>
      <param name="path">쓸 파일입니다.</param>
      <param name="contents">파일에 쓸 줄입니다.</param>
      <param name="encoding">사용할 문자 인코딩입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 길이가 0 인 문자열, 공백만, 또는 정의한 하나 이상의 잘못 된 문자가 포함 된 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 메서드.</exception>
      <exception cref="T:System.ArgumentNullException">두<paramref name=" path" />,<paramref name=" contents" />, 또는 <paramref name="encoding" /> 은 null입니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" />가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우)</exception>
      <exception cref="T:System.IO.IOException">파일을 여는 동안 I/O 오류가 발생한 경우</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" />가 시스템 정의 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우</exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" />가 읽기 전용인 파일을 지정하는 경우또는현재 플랫폼이 해당 작업을 지원하지 않는 경우또는<paramref name="path" />은(는) 디렉터리입니다.또는호출자에게 필요한 권한이 없는 경우</exception>
    </member>
    <member name="M:System.IO.File.WriteAllText(System.String,System.String)">
      <summary>새 파일을 만들고 지정된 문자열을 파일에 쓴 다음 파일을 닫습니다.대상 파일이 이미 있으면 덮어씁니다.</summary>
      <param name="path">쓸 파일입니다. </param>
      <param name="contents">파일에 쓸 문자열입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null이거나 <paramref name="contents" />가 비어 있는 경우  </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.IO.IOException">파일을 여는 동안 I/O 오류가 발생한 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" />가 읽기 전용인 파일을 지정하는 경우또는 현재 플랫폼이 해당 작업을 지원하지 않는 경우또는 <paramref name="path" />가 디렉터리를 지정한 경우또는 호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.WriteAllText(System.String,System.String,System.Text.Encoding)">
      <summary>새 파일을 만들고 지정된 인코딩을 사용하여 지정된 문자열을 파일에 쓴 다음 파일을 닫습니다.대상 파일이 이미 있으면 덮어씁니다.</summary>
      <param name="path">쓸 파일입니다. </param>
      <param name="contents">파일에 쓸 문자열입니다. </param>
      <param name="encoding">문자열에 적용할 인코딩입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나, 공백만 포함하거나, <see cref="F:System.IO.Path.InvalidPathChars" />로 정의된 하나 이상의 잘못된 문자를 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null이거나 <paramref name="contents" />가 비어 있는 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못된 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.IO.IOException">파일을 여는 동안 I/O 오류가 발생한 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" />가 읽기 전용인 파일을 지정하는 경우또는 현재 플랫폼이 해당 작업을 지원하지 않는 경우또는 <paramref name="path" />가 디렉터리를 지정한 경우또는 호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />의 형식이 잘못된 경우 </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.IO.FileInfo">
      <summary>파일을 만들고, 복사하고, 삭제하고, 이동하고, 열기 위한 속성 및 인스턴스 메서드를 제공하고, <see cref="T:System.IO.FileStream" /> 개체를 만드는 것을 도와줍니다.이 클래스는 상속될 수 없습니다.이 형식에 대 한.NET Framework 소스 코드를 찾아보려면 참조는 Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileInfo.#ctor(System.String)">
      <summary>파일 경로의 래퍼 역할을 하는 <see cref="T:System.IO.FileInfo" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="fileName">새 파일의 정규화된 이름이거나 상대적인 파일 이름입니다.경로가 디렉터리 구분 기호 문자로 끝나서는 안 됩니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="fileName" />가 null인 경우 </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">파일 이름에 아무 것도 없거나, 공백만 있거나, 잘못된 문자가 포함되어 있는 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="fileName" />에 대한 액세스가 거부된 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="fileName" />이 문자열 중간에 콜론(:)을 포함하는 경우 </exception>
    </member>
    <member name="M:System.IO.FileInfo.AppendText">
      <summary>
        <see cref="T:System.IO.StreamWriter" />의 이 인스턴스가 나타내는 파일에 텍스트를 추가하는 <see cref="T:System.IO.FileInfo" />를 만듭니다.</summary>
      <returns>새 StreamWriter입니다.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.CopyTo(System.String)">
      <summary>새 파일에 기존 파일을 복사하고 기존 파일을 덮어쓸 수 없도록 합니다.</summary>
      <returns>정규화된 경로가 있는 새 파일입니다.</returns>
      <param name="destFileName">복사할 새 파일의 이름입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destFileName" />에 아무 것도 없거나, 공백만 있거나, 잘못된 문자가 포함되어 있는 경우 </exception>
      <exception cref="T:System.IO.IOException">오류가 발생하거나 대상 파일이 이미 있는 경우 </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destFileName" />가 null인 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">디렉터리 경로가 전달되거나 파일이 다른 드라이브로 이동하고 있는 경우 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="destFileName" />에 지정된 디렉터리가 없는 경우</exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destFileName" />에서 문자열에 콜론(:)이 들어있지만 볼륨이 지정되지 않는 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.CopyTo(System.String,System.Boolean)">
      <summary>새 파일에 기존 파일을 복사하고 기존 파일을 덮어쓸 수 있도록 합니다.</summary>
      <returns>새 파일이 반환되거나, <paramref name="overwrite" />가 true인 경우 기존 파일을 덮어씁니다.파일이 있고 <paramref name="overwrite" />가 false이면 <see cref="T:System.IO.IOException" />이 throw됩니다.</returns>
      <param name="destFileName">복사할 새 파일의 이름입니다. </param>
      <param name="overwrite">기존 파일을 덮어쓸 수 있도록 하려면 true이고, 그렇지 않으면 false입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destFileName" />에 아무 것도 없거나, 공백만 있거나, 잘못된 문자가 포함되어 있는 경우 </exception>
      <exception cref="T:System.IO.IOException">오류가 발생하거나 대상 파일이 이미 있으며 <paramref name="overwrite" />가 false인 경우 </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destFileName" />가 null인 경우 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="destFileName" />에 지정된 디렉터리가 없는 경우</exception>
      <exception cref="T:System.UnauthorizedAccessException">디렉터리 경로가 전달되거나 파일이 다른 드라이브로 이동하고 있는 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destFileName" />이 문자열 중간에 콜론(:)을 포함하는 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Create">
      <summary>파일을 만듭니다.</summary>
      <returns>새 파일입니다.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.CreateText">
      <summary>새 텍스트 파일을 쓰는 <see cref="T:System.IO.StreamWriter" />를 만듭니다.</summary>
      <returns>새 StreamWriter입니다.</returns>
      <exception cref="T:System.UnauthorizedAccessException">파일 이름이 디렉터리인 경우 </exception>
      <exception cref="T:System.IO.IOException">디스크가 읽기 전용인 경우 </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Delete">
      <summary>파일을 영구적으로 삭제합니다.</summary>
      <exception cref="T:System.IO.IOException">Microsoft Windows NT를 실행하는 컴퓨터에 대상 파일이 열려 있거나 메모리 매핑된 경우또는파일에 대한 열린 핸들이 있고 운영 체제가 Windows XP 또는 그 이전 버전인 경우.이 열린 핸들은 디렉터리 및 파일 열거로 인해 발생할 수 있습니다.자세한 내용은 방법: 디렉터리 및 파일 열거을 참조하십시오.</exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">경로가 디렉터리인 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.Directory">
      <summary>부모 디렉터리의 인스턴스를 가져옵니다.</summary>
      <returns>이 파일의 부모 디렉터리를 나타내는 <see cref="T:System.IO.DirectoryInfo" /> 개체입니다.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 유효하지 않은 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.DirectoryName">
      <summary>디렉터리의 전체 경로를 나타내는 문자열을 가져옵니다.</summary>
      <returns>디렉터리의 전체 경로를 나타내는 문자열입니다.</returns>
      <exception cref="T:System.ArgumentNullException">디렉터리 이름에 대해 null이 전달된 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">정규화된 경로는 260자 이상입니다.</exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.Exists">
      <summary>파일이 있는지를 나타내는 값을 가져옵니다.</summary>
      <returns>파일이 있으면 true이고, 파일이 없거나 파일이 디렉터리이면 false입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileInfo.IsReadOnly">
      <summary>현재 파일이 읽기 전용인지를 결정하는 값을 가져오거나 설정합니다.</summary>
      <returns>현재 파일이 읽기 전용이면 true이고, 그렇지 않으면 false입니다.</returns>
      <exception cref="T:System.IO.FileNotFoundException">현재 <see cref="T:System.IO.FileInfo" /> 개체에서 설명하는 파일을 찾을 수 없는 경우</exception>
      <exception cref="T:System.IO.IOException">파일을 여는 동안 I/O 오류가 발생한 경우</exception>
      <exception cref="T:System.UnauthorizedAccessException">현재 플랫폼이 해당 작업을 지원하지 않는 경우또는 호출자에게 필요한 권한이 없는 경우</exception>
      <exception cref="T:System.ArgumentException">사용자에게 쓰기 권한이 없지만 이 속성을 false로 설정하려고 한 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileInfo.Length">
      <summary>현재 파일의 크기(바이트)를 가져옵니다.</summary>
      <returns>현재 파일의 크기(바이트)입니다.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" />가 파일이나 디렉터리의 상태를 업데이트할 수 없는 경우 </exception>
      <exception cref="T:System.IO.FileNotFoundException">파일이 없습니다.또는 Length 속성이 디렉터리에 대해 호출되는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileInfo.MoveTo(System.String)">
      <summary>지정된 파일을 새 위치로 이동하고 새 파일의 이름을 지정할 수 있는 옵션을 제공합니다.</summary>
      <param name="destFileName">파일을 이동할 경로입니다. 이 경로에서 다른 파일 이름을 지정할 수 있습니다. </param>
      <exception cref="T:System.IO.IOException">대상 파일이 이미 있거나 대상 장치가 준비되어 있지 않는 상태 등의 I/O 오류가 발생한 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destFileName" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="destFileName" />에 아무 것도 없거나, 공백만 있거나, 잘못된 문자가 포함되어 있는 경우 </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="destFileName" />이 읽기 전용이거나 디렉터리인 경우 </exception>
      <exception cref="T:System.IO.FileNotFoundException">파일을 찾을 수 없는 경우 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 유효하지 않은 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destFileName" />이 문자열 중간에 콜론(:)을 포함하는 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.Name">
      <summary>파일 이름을 가져옵니다.</summary>
      <returns>파일의 이름입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode)">
      <summary>지정된 모드로 파일을 엽니다.</summary>
      <returns>공유 권한 없이 읽기/쓰기 권한을 사용하여 지정된 모드로 연 파일입니다.</returns>
      <param name="mode">파일을 여는 모드(예: <see cref="T:System.IO.FileMode" /> 또는 Open)를 지정하는 Append 상수입니다. </param>
      <exception cref="T:System.IO.FileNotFoundException">파일을 찾을 수 없는 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">파일이 읽기 전용이거나 디렉터리인 경우 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 유효하지 않은 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.IO.IOException">파일이 이미 열려 있습니다. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode,System.IO.FileAccess)">
      <summary>읽기, 쓰기 또는 읽기/쓰기 권한을 사용하여 지정된 모드로 파일을 엽니다.</summary>
      <returns>공유 권한 없이 지정된 액세스 권한 및 모드로 연 <see cref="T:System.IO.FileStream" /> 개체입니다.</returns>
      <param name="mode">파일을 여는 모드(예: <see cref="T:System.IO.FileMode" /> 또는 Open)를 지정하는 Append 상수입니다. </param>
      <param name="access">
        <see cref="T:System.IO.FileAccess" />, Read 또는 Write 파일 액세스 권한 중 어떤 권한으로 파일을 열지를 지정하는 ReadWrite 상수입니다. </param>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.IO.FileNotFoundException">파일을 찾을 수 없는 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" />이 읽기 전용이거나 디렉터리인 경우 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 유효하지 않은 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.IO.IOException">파일이 이미 열려 있습니다. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>읽기, 쓰기 또는 읽기/쓰기 권한과 지정된 공유 옵션을 사용하여 특정 모드로 파일을 엽니다.</summary>
      <returns>지정된 액세스 권한 및 공유 옵션을 사용하여 특정 모드로 연 <see cref="T:System.IO.FileStream" /> 개체입니다.</returns>
      <param name="mode">파일을 여는 모드(예: <see cref="T:System.IO.FileMode" /> 또는 Open)를 지정하는 Append 상수입니다. </param>
      <param name="access">
        <see cref="T:System.IO.FileAccess" />, Read 또는 Write 파일 액세스 권한 중 어떤 권한으로 파일을 열지를 지정하는 ReadWrite 상수입니다. </param>
      <param name="share">이 파일에 대해 다른 <see cref="T:System.IO.FileShare" /> 개체의 액세스 형식을 지정하는 FileStream 상수입니다. </param>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.IO.FileNotFoundException">파일을 찾을 수 없는 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" />이 읽기 전용이거나 디렉터리인 경우 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 유효하지 않은 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.IO.IOException">파일이 이미 열려 있습니다. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.OpenRead">
      <summary>읽기 전용 <see cref="T:System.IO.FileStream" />을 만듭니다.</summary>
      <returns>새 읽기 전용 <see cref="T:System.IO.FileStream" /> 개체입니다.</returns>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" />이 읽기 전용이거나 디렉터리인 경우 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 유효하지 않은 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.IO.IOException">파일이 이미 열려 있습니다. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.OpenText">
      <summary>UTF8 인코딩 방식으로 기존 텍스트 파일에서 읽는 <see cref="T:System.IO.StreamReader" />를 만듭니다.</summary>
      <returns>UTF8 인코딩 방식을 사용하는 새 StreamReader입니다.</returns>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.IO.FileNotFoundException">파일을 찾을 수 없는 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" />이 읽기 전용이거나 디렉터리인 경우 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 유효하지 않은 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.OpenWrite">
      <summary>쓰기 전용 <see cref="T:System.IO.FileStream" />을 만듭니다.</summary>
      <returns>새 파일 또는 기존 파일의 공유되지 않는 쓰기 전용 <see cref="T:System.IO.FileStream" /> 개체입니다.</returns>
      <exception cref="T:System.UnauthorizedAccessException">
        <see cref="T:System.IO.FileInfo" /> 개체의 인스턴스를 만들 때 지정된 경로는 읽기 전용이거나 디렉터리입니다.  </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <see cref="T:System.IO.FileInfo" /> 개체의 인스턴스를 만들 때 지정한 경로가 유효하지 않은 경우(예: 매핑되지 않은 드라이브의 경로에 해당하는 경우) </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.ToString">
      <summary>경로를 문자열로 반환합니다.</summary>
      <returns>경로를 나타내는 문자열입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.FileOptions">
      <summary>
        <see cref="T:System.IO.FileStream" /> 개체를 만들기 위한 고급 옵션을 나타냅니다.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.FileOptions.Asynchronous">
      <summary>파일을 비동기 읽기 및 쓰기에 사용할 수 있음을 나타냅니다. </summary>
    </member>
    <member name="F:System.IO.FileOptions.DeleteOnClose">
      <summary>파일이 더 이상 사용되지 않는 경우 자동으로 삭제됨을 나타냅니다.</summary>
    </member>
    <member name="F:System.IO.FileOptions.Encrypted">
      <summary>파일이 암호화되어 있고 암호화할 때 사용한 사용자 계정으로만 해독할 수 있음을 나타냅니다.</summary>
    </member>
    <member name="F:System.IO.FileOptions.None">
      <summary>
        <see cref="T:System.IO.FileStream" /> 개체를 만들 때 추가 옵션을 사용할 수 없음을 나타냅니다.</summary>
    </member>
    <member name="F:System.IO.FileOptions.RandomAccess">
      <summary>파일이 임의로 액세스됨을 나타냅니다.시스템에서는 이 필드를 힌트로 사용하여 파일 캐싱을 최적화할 수 있습니다.</summary>
    </member>
    <member name="F:System.IO.FileOptions.SequentialScan">
      <summary>파일이 처음부터 끝까지 순차적으로 액세스됨을 나타냅니다.시스템에서는 이 필드를 힌트로 사용하여 파일 캐싱을 최적화할 수 있습니다.응용 프로그램에서 임의 액세스를 위해 파일 포인터를 이동하는 경우 최적 캐싱이 수행되지 않을 수 있지만 올바른 작업은 보장됩니다.</summary>
    </member>
    <member name="F:System.IO.FileOptions.WriteThrough">
      <summary>시스템이 중간 캐시를 통해 쓰고 디스크로 직접 이동해야 함을 나타냅니다.</summary>
    </member>
    <member name="T:System.IO.FileStream">
      <summary>파일에 대해 <see cref="T:System.IO.Stream" />을 제공하여 동기 및 비동기 읽기/쓰기 작업을 모두 지원합니다.이 형식에 대 한.NET Framework 소스 코드를 찾아보려면 참조는 Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.#ctor(Microsoft.Win32.SafeHandles.SafeFileHandle,System.IO.FileAccess)">
      <summary>지정된 읽기/쓰기 권한을 사용하여 지정된 파일 핸들에 대한 <see cref="T:System.IO.FileStream" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
      <param name="handle">현재 FileStream 개체가 캡슐화할 파일에 대한 파일 핸들입니다. </param>
      <param name="access">FileStream 개체의 <see cref="P:System.IO.FileStream.CanRead" /> 및 <see cref="P:System.IO.FileStream.CanWrite" /> 속성을 설정하는 상수입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="access" />가 <see cref="T:System.IO.FileAccess" />의 필드가 아닌 경우 </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.IO.IOException">I/O 오류(예: 디스크 오류)가 발생한 경우또는스트림이 닫힌 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="access" />가 <paramref name="access" /> 또는 Write이고 파일 핸들이 읽기 전용 액세스로 설정된 경우처럼, 지정된 파일 핸들에 대해 운영 체제에서 ReadWrite 요청을 허용하지 않는 경우 </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(Microsoft.Win32.SafeHandles.SafeFileHandle,System.IO.FileAccess,System.Int32)">
      <summary>지정된 읽기/쓰기 권한 및 버퍼 크기를 사용하여 지정된 파일 핸들에 대해 <see cref="T:System.IO.FileStream" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="handle">현재 FileStream 개체가 캡슐화할 파일에 대한 파일 핸들입니다. </param>
      <param name="access">FileStream 개체의 <see cref="P:System.IO.FileStream.CanRead" /> 및 <see cref="P:System.IO.FileStream.CanWrite" /> 속성을 설정하는 <see cref="T:System.IO.FileAccess" /> 상수입니다. </param>
      <param name="bufferSize">버퍼 크기를 나타내는 0보다 큰 양의 <see cref="T:System.Int32" /> 값입니다.기본 버퍼 크기는 4,096입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="handle" /> 매개 변수가 잘못된 핸들인 경우또는<paramref name="handle" /> 매개 변수가 동기식 핸들인데 비동기식으로 사용된 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> 매개 변수가 음수인 경우 </exception>
      <exception cref="T:System.IO.IOException">I/O 오류(예: 디스크 오류)가 발생한 경우또는스트림이 닫힌 경우  </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="access" />가 <paramref name="access" /> 또는 Write이고 파일 핸들이 읽기 전용 액세스로 설정된 경우처럼, 지정된 파일 핸들에 대해 운영 체제에서 ReadWrite 요청을 허용하지 않는 경우 </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(Microsoft.Win32.SafeHandles.SafeFileHandle,System.IO.FileAccess,System.Int32,System.Boolean)">
      <summary>지정된 읽기/쓰기 권한, 버퍼 크기 및 동기/비동기 상태를 사용하여 지정된 파일 핸들에 대해 <see cref="T:System.IO.FileStream" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="handle">이 FileStream 개체가 캡슐화할 파일에 대한 파일 핸들입니다. </param>
      <param name="access">FileStream 개체의 <see cref="P:System.IO.FileStream.CanRead" /> 및 <see cref="P:System.IO.FileStream.CanWrite" /> 속성을 설정하는 상수입니다. </param>
      <param name="bufferSize">버퍼 크기를 나타내는 0보다 큰 양의 <see cref="T:System.Int32" /> 값입니다.기본 버퍼 크기는 4,096입니다.</param>
      <param name="isAsync">겹쳐진 I/O 모드에서 핸들이 비동기적으로 열렸으면 true이고, 그렇지 않으면 false입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="handle" /> 매개 변수가 잘못된 핸들인 경우또는<paramref name="handle" /> 매개 변수가 동기식 핸들인데 비동기식으로 사용된 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> 매개 변수가 음수인 경우 </exception>
      <exception cref="T:System.IO.IOException">I/O 오류(예: 디스크 오류)가 발생한 경우또는스트림이 닫힌 경우  </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="access" />가 <paramref name="access" /> 또는 Write이고 파일 핸들이 읽기 전용 액세스로 설정된 경우처럼, 지정된 파일 핸들에 대해 운영 체제에서 ReadWrite 요청을 허용하지 않는 경우 </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode)">
      <summary>지정된 경로 및 생성 모드들 사용하여 <see cref="T:System.IO.FileStream" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="path">현재 FileStream 개체가 캡슐화할 파일의 상대 또는 절대 경로입니다. </param>
      <param name="mode">파일을 열거나 만드는 방법을 결정하는 상수입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 빈 문자열("")이거나, 공백만 포함하거나, 잘못된 문자를 포함하고 있는 경우 또는<paramref name="path" /> 와 같은 파일이 아닌 장치를 가리키는 "con:", "com1:", "lpt1:" 등입니다.NTFS 환경입니다.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 와 같은 파일이 아닌 장치를 가리키는 "con:", "com1:", "lpt1:" 등입니다.에 NTFS가 아닌 환경입니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.IO.FileNotFoundException">예를 들어, <paramref name="mode" />가 FileMode.Truncate 또는 FileMode.Open인 경우 파일을 찾을 수 없으며 <paramref name="path" />로 지정된 파일이 없는 경우.파일이 이들 모드에 있어야 합니다.</exception>
      <exception cref="T:System.IO.IOException">FileMode.CreateNew로 지정한 파일이 이미 있는 경우에 <paramref name="path" />를 지정하는 등의 I/O 오류가 발생한 경우또는스트림이 닫힌 경우 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 유효하지 않은 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" />에 잘못된 값이 포함된 경우 </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess)">
      <summary>지정된 경로, 생성 모드 및 읽기/쓰기 권한을 사용하여 <see cref="T:System.IO.FileStream" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="path">현재 FileStream 개체가 캡슐화할 파일의 상대 또는 절대 경로입니다. </param>
      <param name="mode">파일을 열거나 만드는 방법을 결정하는 상수입니다. </param>
      <param name="access">FileStream 개체에서 파일에 액세스할 수 있는 방법을 결정하는 상수입니다.또한 FileStream 개체의 <see cref="P:System.IO.FileStream.CanRead" /> 및 <see cref="P:System.IO.FileStream.CanWrite" /> 속성에서 반환하는 값을 결정합니다.<paramref name="path" />가 디스크 파일을 지정하면 <see cref="P:System.IO.FileStream.CanSeek" />는 true입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 빈 문자열("")이거나, 공백만 포함하거나, 잘못된 문자를 포함하고 있는 경우 또는<paramref name="path" /> 와 같은 파일이 아닌 장치를 가리키는 "con:", "com1:", "lpt1:" 등입니다.NTFS 환경입니다.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 와 같은 파일이 아닌 장치를 가리키는 "con:", "com1:", "lpt1:" 등입니다.에 NTFS가 아닌 환경입니다.</exception>
      <exception cref="T:System.IO.FileNotFoundException">예를 들어, <paramref name="mode" />가 FileMode.Truncate 또는 FileMode.Open인 경우 파일을 찾을 수 없으며 <paramref name="path" />로 지정된 파일이 없는 경우.파일이 이들 모드에 있어야 합니다.</exception>
      <exception cref="T:System.IO.IOException">FileMode.CreateNew로 지정한 파일이 이미 있는 경우에 <paramref name="path" />를 지정하는 등의 I/O 오류가 발생한 경우 또는스트림이 닫힌 경우</exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 유효하지 않은 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="access" />가 <paramref name="path" /> 또는 <paramref name="access" />이고 파일 또는 디렉터리가 읽기 전용 액세스로 설정된 경우처럼, 지정된 Write에 대해 운영 체제에서 ReadWrite 요청을 허용하지 않는 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" />에 잘못된 값이 포함된 경우 </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>지정된 경로, 생성 모드, 읽기/쓰기 권한 및 공유 권한을 사용하여 <see cref="T:System.IO.FileStream" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="path">현재 FileStream 개체가 캡슐화할 파일의 상대 또는 절대 경로입니다. </param>
      <param name="mode">파일을 열거나 만드는 방법을 결정하는 상수입니다. </param>
      <param name="access">FileStream 개체에서 파일에 액세스할 수 있는 방법을 결정하는 상수입니다.또한 FileStream 개체의 <see cref="P:System.IO.FileStream.CanRead" /> 및 <see cref="P:System.IO.FileStream.CanWrite" /> 속성에서 반환하는 값을 결정합니다.<paramref name="path" />가 디스크 파일을 지정하면 <see cref="P:System.IO.FileStream.CanSeek" />는 true입니다.</param>
      <param name="share">프로세스에서 파일을 공유하는 방법을 결정하는 상수입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 빈 문자열("")이거나, 공백만 포함하거나, 잘못된 문자를 포함하고 있는 경우 또는<paramref name="path" /> 와 같은 파일이 아닌 장치를 가리키는 "con:", "com1:", "lpt1:" 등입니다.NTFS 환경입니다.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 와 같은 파일이 아닌 장치를 가리키는 "con:", "com1:", "lpt1:" 등입니다.에 NTFS가 아닌 환경입니다.</exception>
      <exception cref="T:System.IO.FileNotFoundException">예를 들어, <paramref name="mode" />가 FileMode.Truncate 또는 FileMode.Open인 경우 파일을 찾을 수 없으며 <paramref name="path" />로 지정된 파일이 없는 경우.파일이 이들 모드에 있어야 합니다.</exception>
      <exception cref="T:System.IO.IOException">FileMode.CreateNew로 지정한 파일이 이미 있는 경우에 <paramref name="path" />를 지정하는 등의 I/O 오류가 발생한 경우 또는시스템에서 Windows 98 또는 Windows 98 Second Edition을 실행 중이고 <paramref name="share" />가 FileShare.Delete로 설정된 경우또는스트림이 닫힌 경우</exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 유효하지 않은 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="access" />가 <paramref name="path" /> 또는 <paramref name="access" />이고 파일 또는 디렉터리가 읽기 전용 액세스로 설정된 경우처럼, 지정된 Write에 대해 운영 체제에서 ReadWrite 요청을 허용하지 않는 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" />에 잘못된 값이 포함된 경우 </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Int32)">
      <summary>지정된 경로, 생성 모드, 읽기/쓰기 및 공유 권한, 버퍼 크기를 사용하여 <see cref="T:System.IO.FileStream" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="path">현재 FileStream 개체가 캡슐화할 파일의 상대 또는 절대 경로입니다. </param>
      <param name="mode">파일을 열거나 만드는 방법을 결정하는 상수입니다. </param>
      <param name="access">FileStream 개체에서 파일에 액세스할 수 있는 방법을 결정하는 상수입니다.또한 FileStream 개체의 <see cref="P:System.IO.FileStream.CanRead" /> 및 <see cref="P:System.IO.FileStream.CanWrite" /> 속성에서 반환하는 값을 결정합니다.<paramref name="path" />가 디스크 파일을 지정하면 <see cref="P:System.IO.FileStream.CanSeek" />는 true입니다.</param>
      <param name="share">프로세스에서 파일을 공유하는 방법을 결정하는 상수입니다. </param>
      <param name="bufferSize">버퍼 크기를 나타내는 0보다 큰 양의 <see cref="T:System.Int32" /> 값입니다.기본 버퍼 크기는 4,096입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 빈 문자열("")이거나, 공백만 포함하거나, 잘못된 문자를 포함하고 있는 경우 또는<paramref name="path" /> 와 같은 파일이 아닌 장치를 가리키는 "con:", "com1:", "lpt1:" 등입니다.NTFS 환경입니다.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 와 같은 파일이 아닌 장치를 가리키는 "con:", "com1:", "lpt1:" 등입니다.에 NTFS가 아닌 환경입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" />가 음수이거나 0인 경우또는 <paramref name="mode" />, <paramref name="access" /> 또는 <paramref name="share" />에 잘못된 값이 포함된 경우 </exception>
      <exception cref="T:System.IO.FileNotFoundException">예를 들어, <paramref name="mode" />가 FileMode.Truncate 또는 FileMode.Open인 경우 파일을 찾을 수 없으며 <paramref name="path" />로 지정된 파일이 없는 경우.파일이 이들 모드에 있어야 합니다.</exception>
      <exception cref="T:System.IO.IOException">FileMode.CreateNew로 지정한 파일이 이미 있는 경우에 <paramref name="path" />를 지정하는 등의 I/O 오류가 발생한 경우 또는시스템에서 Windows 98 또는 Windows 98 Second Edition을 실행 중이고 <paramref name="share" />가 FileShare.Delete로 설정된 경우또는스트림이 닫힌 경우</exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 유효하지 않은 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="access" />가 <paramref name="path" /> 또는 <paramref name="access" />이고 파일 또는 디렉터리가 읽기 전용 액세스로 설정된 경우처럼, 지정된 Write에 대해 운영 체제에서 ReadWrite 요청을 허용하지 않는 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Int32,System.Boolean)">
      <summary>지정된 경로, 생성 모드, 읽기/쓰기 및 공유 권한, 버퍼 크기 및 동기/비동기 상태를 사용하여 <see cref="T:System.IO.FileStream" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="path">현재 FileStream 개체가 캡슐화할 파일의 상대 또는 절대 경로입니다. </param>
      <param name="mode">파일을 열거나 만드는 방법을 결정하는 상수입니다. </param>
      <param name="access">FileStream 개체에서 파일에 액세스할 수 있는 방법을 결정하는 상수입니다.또한 FileStream 개체의 <see cref="P:System.IO.FileStream.CanRead" /> 및 <see cref="P:System.IO.FileStream.CanWrite" /> 속성에서 반환하는 값을 결정합니다.<paramref name="path" />가 디스크 파일을 지정하면 <see cref="P:System.IO.FileStream.CanSeek" />는 true입니다.</param>
      <param name="share">프로세스에서 파일을 공유하는 방법을 결정하는 상수입니다. </param>
      <param name="bufferSize">버퍼 크기를 나타내는 0보다 큰 양의 <see cref="T:System.Int32" /> 값입니다.기본 버퍼 크기는 4,096입니다.</param>
      <param name="useAsync">비동기 I/O 또는 동기 I/O를 사용할지를 지정합니다.그러나 내부 운영 체제에서 비동기 I/O가 지원되지 않을 수도 있으므로 true를 지정해도 플랫폼에 따라 핸들이 동기적으로 열릴 수 있습니다.비동기적으로 열린 경우 <see cref="M:System.IO.FileStream.BeginRead(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)" /> 및 <see cref="M:System.IO.FileStream.BeginWrite(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)" /> 메서드는 대량의 데이터를 읽거나 쓸 때는 보다 효과적이지만 소량의 데이터를 읽거나 쓸 경우 작업 속도가 훨씬 느려질 수 있습니다.응용 프로그램이 비동기 I/O를 사용하도록 디자인된 경우 <paramref name="useAsync" /> 매개 변수를 true로 설정합니다.비동기 I/O를 올바르게 사용하면 응용 프로그램의 속도가 10배 정도 향상될 수 있지만 응용 프로그램을 비동기 I/O에 맞도록 다시 디자인하지 않고 사용하면 성능이 10배 정도 감소될 수도 있습니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 빈 문자열("")이거나, 공백만 포함하거나, 잘못된 문자를 포함하고 있는 경우 또는<paramref name="path" /> 와 같은 파일이 아닌 장치를 가리키는 "con:", "com1:", "lpt1:" 등입니다.NTFS 환경입니다.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 와 같은 파일이 아닌 장치를 가리키는 "con:", "com1:", "lpt1:" 등입니다.에 NTFS가 아닌 환경입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" />가 음수이거나 0인 경우또는 <paramref name="mode" />, <paramref name="access" /> 또는 <paramref name="share" />에 잘못된 값이 포함된 경우 </exception>
      <exception cref="T:System.IO.FileNotFoundException">예를 들어, <paramref name="mode" />가 FileMode.Truncate 또는 FileMode.Open인 경우 파일을 찾을 수 없으며 <paramref name="path" />로 지정된 파일이 없는 경우.파일이 이들 모드에 있어야 합니다.</exception>
      <exception cref="T:System.IO.IOException">FileMode.CreateNew로 지정한 파일이 이미 있는 경우에 <paramref name="path" />를 지정하는 등의 I/O 오류가 발생한 경우또는 시스템에서 Windows 98 또는 Windows 98 Second Edition을 실행 중이고 <paramref name="share" />가 FileShare.Delete로 설정된 경우또는스트림이 닫힌 경우</exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 유효하지 않은 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="access" />가 <paramref name="path" /> 또는 <paramref name="access" />이고 파일 또는 디렉터리가 읽기 전용 액세스로 설정된 경우처럼, 지정된 Write에 대해 운영 체제에서 ReadWrite 요청을 허용하지 않는 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Int32,System.IO.FileOptions)">
      <summary>지정된 경로, 생성 모드, 읽기/쓰기 및 공유 권한, 같은 파일에 대한 다른 FileStream의 액세스 권한, 버퍼 크기 및 추가 파일 옵션을 사용하여 <see cref="T:System.IO.FileStream" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="path">현재 FileStream 개체가 캡슐화할 파일의 상대 또는 절대 경로입니다. </param>
      <param name="mode">파일을 열거나 만드는 방법을 결정하는 상수입니다. </param>
      <param name="access">FileStream 개체에서 파일에 액세스할 수 있는 방법을 결정하는 상수입니다.또한 FileStream 개체의 <see cref="P:System.IO.FileStream.CanRead" /> 및 <see cref="P:System.IO.FileStream.CanWrite" /> 속성에서 반환하는 값을 결정합니다.<paramref name="path" />가 디스크 파일을 지정하면 <see cref="P:System.IO.FileStream.CanSeek" />는 true입니다.</param>
      <param name="share">프로세스에서 파일을 공유하는 방법을 결정하는 상수입니다. </param>
      <param name="bufferSize">버퍼 크기를 나타내는 0보다 큰 양의 <see cref="T:System.Int32" /> 값입니다.기본 버퍼 크기는 4,096입니다.</param>
      <param name="options">추가 파일 옵션을 지정하는 값입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 빈 문자열("")이거나, 공백만 포함하거나, 잘못된 문자를 포함하고 있는 경우 또는<paramref name="path" /> 와 같은 파일이 아닌 장치를 가리키는 "con:", "com1:", "lpt1:" 등입니다.NTFS 환경입니다.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 와 같은 파일이 아닌 장치를 가리키는 "con:", "com1:", "lpt1:" 등입니다.에 NTFS가 아닌 환경입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" />가 음수이거나 0인 경우또는 <paramref name="mode" />, <paramref name="access" /> 또는 <paramref name="share" />에 잘못된 값이 포함된 경우 </exception>
      <exception cref="T:System.IO.FileNotFoundException">예를 들어, <paramref name="mode" />가 FileMode.Truncate 또는 FileMode.Open인 경우 파일을 찾을 수 없으며 <paramref name="path" />로 지정된 파일이 없는 경우.파일이 이들 모드에 있어야 합니다.</exception>
      <exception cref="T:System.IO.IOException">FileMode.CreateNew로 지정한 파일이 이미 있는 경우에 <paramref name="path" />를 지정하는 등의 I/O 오류가 발생한 경우또는스트림이 닫힌 경우</exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 유효하지 않은 경우(예: 매핑되지 않은 드라이브의 경로를 지정한 경우) </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="access" />가 <paramref name="path" /> 또는 <paramref name="access" />이고 파일 또는 디렉터리가 읽기 전용 액세스로 설정된 경우처럼, 지정된 Write에 대해 운영 체제에서 ReadWrite 요청을 허용하지 않는 경우 또는<see cref="F:System.IO.FileOptions.Encrypted" />에 <paramref name="options" />가 지정되었고 현재 플랫폼에서 파일 암호화가 지원되지 않는 경우</exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
    </member>
    <member name="P:System.IO.FileStream.CanRead">
      <summary>현재 스트림이 읽기를 지원하는지를 나타내는 값을 가져옵니다.</summary>
      <returns>스트림이 읽기를 지원하면 true이고, 스트림이 닫혀 있거나 쓰기 전용 권한으로 열렸으면 false입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.CanSeek">
      <summary>현재 스트림이 검색을 지원하는지를 나타내는 값을 가져옵니다.</summary>
      <returns>스트림이 검색을 지원하면 true이고, 스트림이 닫혔거나 FileStream이 콘솔에 대한 출력 또는 파이프와 같은 운영 체제 핸들로부터 생성된 경우에는 false입니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.CanWrite">
      <summary>현재 스트림이 쓰기를 지원하는지를 나타내는 값을 가져옵니다.</summary>
      <returns>스트림이 쓰기를 지원하면 true이고, 스트림이 닫혀 있거나 읽기 전용 권한으로 열렸으면 false입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.IO.FileStream" />에서 사용하는 관리되지 않는 리소스를 해제하고, 관리되는 리소스를 선택적으로 해제할 수 있습니다.</summary>
      <param name="disposing">관리되는 리소스와 관리되지 않는 리소스를 모두 해제하려면 true로 설정하고, 관리되지 않는 리소스만 해제하려면 false로 설정합니다. </param>
    </member>
    <member name="M:System.IO.FileStream.Finalize">
      <summary>가비지 수집기에서 FileStream을 회수할 때 리소스가 해제되고 다른 정리 작업이 수행되도록 합니다.</summary>
    </member>
    <member name="M:System.IO.FileStream.Flush">
      <summary>이 스트림의 버퍼를 지우고 버퍼링된 모든 데이터가 파일에 쓰여지도록 합니다.</summary>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <exception cref="T:System.ObjectDisposedException">스트림이 닫혀 있는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Flush(System.Boolean)">
      <summary>이 스트림에 대한 버퍼를 지우고 버퍼링된 모든 데이터가 파일에 쓰여지도록 하며 모든 중간 파일 버퍼도 지웁니다.</summary>
      <param name="flushToDisk">모든 중간 파일 버퍼를 플러시하려면 true이고, 플러시하지 않으려면 false입니다. </param>
    </member>
    <member name="M:System.IO.FileStream.FlushAsync(System.Threading.CancellationToken)">
      <summary>이 스트림에 대해 모든 버퍼를 비동기적으로 지우고 버퍼링된 데이터가 내부 장치에 쓰여지도록 하고 취소 요청을 모니터링합니다. </summary>
      <returns>비동기 플러시 작업을 나타내는 작업입니다. </returns>
      <param name="cancellationToken">취소 요청을 모니터링할 토큰입니다.</param>
      <exception cref="T:System.ObjectDisposedException">스트림이 삭제된 경우</exception>
    </member>
    <member name="P:System.IO.FileStream.IsAsync">
      <summary>FileStream이 동기적으로 열렸는지 또는 비동기적으로 열렸는지를 나타내는 값을 가져옵니다.</summary>
      <returns>FileStream이 비동기적으로 열렸으면 true이고, 그렇지 않으면 false입니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.Length">
      <summary>스트림의 길이(바이트)를 가져옵니다.</summary>
      <returns>스트림 길이(바이트)를 나타내는 long 값입니다.</returns>
      <exception cref="T:System.NotSupportedException">이 스트림에 대한 <see cref="P:System.IO.FileStream.CanSeek" />가 false인 경우 </exception>
      <exception cref="T:System.IO.IOException">파일이 닫히는 등의 I/O 오류가 발생하는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.Name">
      <summary>생성자에 전달된 FileStream의 이름을 가져옵니다.</summary>
      <returns>FileStream의 이름인 문자열입니다.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileStream.Position">
      <summary>이 스트림의 현재 위치를 가져오거나 설정합니다.</summary>
      <returns>이 스트림의 현재 위치입니다.</returns>
      <exception cref="T:System.NotSupportedException">스트림이 검색을 지원하지 않는 경우 </exception>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 또는Windows 98 또는 이전 버전에서 스트림의 끝보다 큰 값을 위치로 설정한 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">위치를 음수 값으로 설정하려고 한 경우 </exception>
      <exception cref="T:System.IO.EndOfStreamException">끝 이후 검색을 지원하지 않는 스트림의 끝 이후를 검색하려 한 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>스트림에서 바이트 블록을 읽어서 해당 데이터를 제공된 버퍼에 씁니다.</summary>
      <returns>버퍼로 읽어온 총 바이트 수입니다.이 바이트 수는 사용 가능한 바이트 수가 부족한 경우 요청된 바이트 수보다 작을 수 있으며, 스트림의 끝에 도달하면 0이 됩니다.</returns>
      <param name="array">이 메서드는 지정된 바이트 배열의 값이 <paramref name="offset" />과 (<paramref name="offset" /> + <paramref name="count" /> - 1<paramref name=")" /> 사이에서 현재 원본으로부터 읽어온 바이트로 교체된 상태로 반환됩니다. </param>
      <param name="offset">읽은 바이트를 넣을 <paramref name="array" />의 바이트 오프셋입니다. </param>
      <param name="count">읽을 최대 바이트 수입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 또는 <paramref name="count" />가 음수인 경우 </exception>
      <exception cref="T:System.NotSupportedException">스트림이 읽기를 지원하지 않습니다. </exception>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> 및 <paramref name="count" />가 <paramref name="array" />에서 잘못된 범위를 설명하는 경우 </exception>
      <exception cref="T:System.ObjectDisposedException">스트림이 닫힌 후 메서드가 호출된 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.ReadAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>현재 스트림에서 바이트의 시퀀스를 비동기적으로 읽고 읽은 바이트 수만큼 스트림 내에서 앞으로 이동하며 취소 요청을 모니터링합니다.</summary>
      <returns>비동기 읽기 작업을 나타내는 작업입니다.<paramref name="TResult" /> 매개 변수의 값은 버퍼 안으로 읽어들인 총 바이트 수를 포함합니다.현재 사용할 수 있는 바이트 수가 요청된 수보다 작을 경우 결과 값이 요청된 바이트 수보다 작을 수 있으며 스트림의 끝에 도달한 경우에는 0이 될 수도 있습니다.</returns>
      <param name="buffer">데이터를 쓸 버퍼입니다.</param>
      <param name="offset">스트림의 데이터를 쓰기 시작할 <paramref name="buffer" />의 바이트 오프셋입니다.</param>
      <param name="count">읽을 최대 바이트 수입니다.</param>
      <param name="cancellationToken">취소 요청을 모니터링할 토큰입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 또는 <paramref name="count" />가 음수인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" />와 <paramref name="count" />의 합계가 버퍼 길이보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">스트림이 읽기를 지원하지 않습니다.</exception>
      <exception cref="T:System.ObjectDisposedException">스트림이 삭제된 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 읽기 작업에서 현재 스트림을 사용하고 있습니다. </exception>
    </member>
    <member name="M:System.IO.FileStream.ReadByte">
      <summary>파일에서 1바이트를 읽고 읽기 위치를 1바이트만큼 앞으로 이동합니다.</summary>
      <returns>
        <see cref="T:System.Int32" />로 캐스팅된 바이트이거나 스트림의 끝에 도달한 경우 -1입니다.</returns>
      <exception cref="T:System.NotSupportedException">현재 스트림이 읽기를 지원하지 않는 경우 </exception>
      <exception cref="T:System.ObjectDisposedException">현재 스트림이 닫혀 있는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.SafeFileHandle">
      <summary>현재 <see cref="T:System.IO.FileStream" /> 개체가 캡슐화하는 파일에 대한 운영 체제 파일 핸들을 나타내는 <see cref="T:Microsoft.Win32.SafeHandles.SafeFileHandle" /> 개체를 가져옵니다.</summary>
      <returns>현재 <see cref="T:System.IO.FileStream" /> 개체가 캡슐화하는 파일에 대한 운영 체제 파일 핸들을 나타내는 개체입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Seek(System.Int64,System.IO.SeekOrigin)">
      <summary>이 스트림의 현재 위치를 제공된 값으로 설정합니다.</summary>
      <returns>스트림 내의 새 위치입니다.</returns>
      <param name="offset">검색을 시작할 <paramref name="origin" />에 상대적인 위치입니다. </param>
      <param name="origin">
        <see cref="T:System.IO.SeekOrigin" /> 형식의 값을 사용하여 시작, 끝 또는 현재 위치를 <paramref name="offset" />에 대한 참조 지점으로 지정합니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <exception cref="T:System.NotSupportedException">FileStream이 파이프 또는 콘솔 출력에서 생성되는 경우와 같이 스트림이 검색을 지원하지 않는 경우 </exception>
      <exception cref="T:System.ArgumentException">스트림의 시작 전에 검색하려고 한 경우 </exception>
      <exception cref="T:System.ObjectDisposedException">스트림이 닫힌 후 메서드가 호출된 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.SetLength(System.Int64)">
      <summary>이 스트림의 길이를 제공된 값으로 설정합니다.</summary>
      <param name="value">스트림의 새 길이입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생한 경우 </exception>
      <exception cref="T:System.NotSupportedException">스트림이 쓰기 및 검색을 모두 지원하지 않는 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> 매개 변수를 0 미만으로 설정하려 한 경우 </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>바이트 블록을 파일 스트림에 씁니다.</summary>
      <param name="array">스트림에 쓸 데이터를 포함하는 버퍼입니다.</param>
      <param name="offset">스트림으로 바이트를 복사하기 시작할 <paramref name="array" />의 바이트 오프셋(0부터 시작)입니다. </param>
      <param name="count">쓸 최대 바이트 수입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> 및 <paramref name="count" />가 <paramref name="array" />에서 잘못된 범위를 설명하는 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 또는 <paramref name="count" />가 음수인 경우 </exception>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 또는다른 스레드에서 운영 체제의 파일 핸들 위치를 예기치 않게 변경한 경우 </exception>
      <exception cref="T:System.ObjectDisposedException">스트림이 닫혀 있는 경우 </exception>
      <exception cref="T:System.NotSupportedException">현재 스트림 인스턴스가 쓰기를 지원하지 않는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.WriteAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>바이트의 시퀀스를 현재 스트림에 비동기적으로 쓰고 쓰여진 바이트 수만큼 이 스트림 내의 현재 위치를 앞으로 이동한 후 취소 요청을 모니터링합니다. </summary>
      <returns>비동기 쓰기 작업을 나타내는 작업입니다.</returns>
      <param name="buffer">데이터를 쓸 버퍼입니다. </param>
      <param name="offset">스트림으로 바이트를 복사하기 시작할 <paramref name="buffer" />의 바이트 오프셋(0부터 시작)입니다.</param>
      <param name="count">쓸 최대 바이트 수입니다.</param>
      <param name="cancellationToken">취소 요청을 모니터링할 토큰입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 또는 <paramref name="count" />가 음수인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" />와 <paramref name="count" />의 합계가 버퍼 길이보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">스트림이 쓰기를 지원하지 않습니다.</exception>
      <exception cref="T:System.ObjectDisposedException">스트림이 삭제된 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 쓰기 작업에서 현재 스트림을 사용하고 있습니다. </exception>
    </member>
    <member name="M:System.IO.FileStream.WriteByte(System.Byte)">
      <summary>파일 스트림의 현재 위치에 바이트를 씁니다.</summary>
      <param name="value">스트림에 쓸 바이트입니다. </param>
      <exception cref="T:System.ObjectDisposedException">스트림이 닫혀 있는 경우 </exception>
      <exception cref="T:System.NotSupportedException">스트림이 쓰기를 지원하지 않습니다. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.FileSystemInfo">
      <summary>
        <see cref="T:System.IO.FileInfo" /> 및 <see cref="T:System.IO.DirectoryInfo" /> 개체에 대한 기본 클래스를 제공합니다.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.FileSystemInfo.#ctor">
      <summary>
        <see cref="T:System.IO.FileSystemInfo" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="P:System.IO.FileSystemInfo.Attributes">
      <summary>현재 파일 또는 디렉터리의 특성을 가져오거나 설정합니다.</summary>
      <returns>현재 <see cref="T:System.IO.FileSystemInfo" />의 <see cref="T:System.IO.FileAttributes" />입니다.</returns>
      <exception cref="T:System.IO.FileNotFoundException">지정된 파일이 없는 경우 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못되었습니다(예: 매핑되지 않은 드라이브의 경로를 지정한 경우). </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">호출자가 잘못된 파일 특성을 설정하려고 한 경우 또는사용자가 특성 값을 설정하려고 하지만 쓰기 권한이 없습니다.</exception>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" />가 데이터를 초기화할 수 없는 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.CreationTime">
      <summary>현재 파일 또는 디렉터리를 만든 시간을 가져오거나 설정합니다.</summary>
      <returns>현재 <see cref="T:System.IO.FileSystemInfo" /> 개체를 만든 날짜와 시간입니다.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" />가 데이터를 초기화할 수 없는 경우 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못되었습니다(예: 매핑되지 않은 드라이브의 경로를 지정한 경우).</exception>
      <exception cref="T:System.PlatformNotSupportedException">현재 운영 체제가 Windows NT 이상이 아닌 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">호출자가 잘못된 만든 시간을 설정하려고 시도합니다.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.CreationTimeUtc">
      <summary>현재 파일 또는 디렉터리를 만든 시간을 UTC(협정 세계시) 기준으로 가져오거나 설정합니다.</summary>
      <returns>현재 <see cref="T:System.IO.FileSystemInfo" /> 개체를 만든 UTC 형식의 날짜와 시간입니다.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" />가 데이터를 초기화할 수 없는 경우 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못되었습니다(예: 매핑되지 않은 드라이브의 경로를 지정한 경우).</exception>
      <exception cref="T:System.PlatformNotSupportedException">현재 운영 체제가 Windows NT 이상이 아닌 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">호출자가 잘못된 액세스 시간을 설정하려고 시도합니다.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileSystemInfo.Delete">
      <summary>파일이나 디렉터리를 삭제합니다.</summary>
      <exception cref="T:System.IO.DirectoryNotFoundException">지정된 경로가 잘못되었습니다(예: 매핑되지 않은 드라이브의 경로를 지정한 경우).</exception>
      <exception cref="T:System.IO.IOException">파일 또는 디렉터리에 대한 열린 핸들이 있고 운영 체제가 Windows XP 또는 그 이전 버전인 경우.이 열린 핸들은 디렉터리 및 파일 열거로 인해 발생할 수 있습니다.자세한 내용은 방법: 디렉터리 및 파일 열거을 참조하십시오.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.FileSystemInfo.Exists">
      <summary>파일이나 디렉터리가 있는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>파일이나 디렉터리가 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileSystemInfo.Extension">
      <summary>파일의 확장명 부분을 나타내는 문자열을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.IO.FileSystemInfo" /> 확장명을 포함하는 문자열입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileSystemInfo.FullName">
      <summary>파일이나 디렉터리의 전체 경로를 가져옵니다.</summary>
      <returns>전체 경로를 포함하는 문자열입니다.</returns>
      <exception cref="T:System.IO.PathTooLongException">정규화된 경로와 파일 이름은 260자 이상입니다.</exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="F:System.IO.FileSystemInfo.FullPath">
      <summary>파일이나 디렉터리의 정규화된 경로를 나타냅니다.</summary>
      <exception cref="T:System.IO.PathTooLongException">정규화된 경로는 260자 이상입니다.</exception>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastAccessTime">
      <summary>현재 파일이나 디렉터리에 마지막으로 액세스한 시간을 가져오거나 설정합니다.</summary>
      <returns>현재 파일이나 디렉터리에 마지막으로 액세스한 시간입니다.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" />가 데이터를 초기화할 수 없는 경우 </exception>
      <exception cref="T:System.PlatformNotSupportedException">현재 운영 체제가 Windows NT 이상이 아닌 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">호출자가 잘못된 액세스 시간을 설정하려고 시도합니다.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastAccessTimeUtc">
      <summary>현재 파일이나 디렉터리에 마지막으로 액세스한 시간을 UTC 기준으로 가져오거나 설정합니다.</summary>
      <returns>현재 파일이나 디렉터리에 마지막으로 액세스한 UTC 시간입니다.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" />가 데이터를 초기화할 수 없는 경우 </exception>
      <exception cref="T:System.PlatformNotSupportedException">현재 운영 체제가 Windows NT 이상이 아닌 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">호출자가 잘못된 액세스 시간을 설정하려고 시도합니다.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastWriteTime">
      <summary>현재 파일이나 디렉터리에 마지막으로 쓴 시간을 가져오거나 설정합니다.</summary>
      <returns>현재 파일에 마지막으로 쓴 시간입니다.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" />가 데이터를 초기화할 수 없는 경우 </exception>
      <exception cref="T:System.PlatformNotSupportedException">현재 운영 체제가 Windows NT 이상이 아닌 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">호출자가 잘못된 쓰기 시간을 설정하려고 시도합니다.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastWriteTimeUtc">
      <summary>현재 파일이나 디렉터리에 마지막으로 쓴 시간을 UTC 기준으로 가져오거나 설정합니다.</summary>
      <returns>현재 파일에 마지막으로 쓴 UTC 시간입니다.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" />가 데이터를 초기화할 수 없는 경우 </exception>
      <exception cref="T:System.PlatformNotSupportedException">현재 운영 체제가 Windows NT 이상이 아닌 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">호출자가 잘못된 쓰기 시간을 설정하려고 시도합니다.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.Name">
      <summary>파일의 경우 파일 이름을 가져옵니다.디렉터리의 경우 계층이 있으면 계층에 있는 마지막 디렉터리의 이름을 가져오고,계층이 없으면 Name 속성이 디렉터리 이름을 가져옵니다.</summary>
      <returns>부모 디렉터리의 이름, 계층에 있는 마지막 디렉터리의 이름 또는 파일 확장명을 포함한 파일의 이름을 나타내는 문자열입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.FileSystemInfo.OriginalPath">
      <summary>사용자가 원래 지정한 상대 또는 절대 경로입니다.</summary>
    </member>
    <member name="M:System.IO.FileSystemInfo.Refresh">
      <summary>개체의 상태를 새로 고칩니다.</summary>
      <exception cref="T:System.IO.IOException">디스크 드라이브 같은 장치가 준비되지 않은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.SearchOption">
      <summary>현재 디렉터리를 검색할 것인지 또는 현재 디렉터리와 모든 하위 디렉터리를 검색할 것인지 여부를 지정합니다. </summary>
    </member>
    <member name="F:System.IO.SearchOption.AllDirectories">
      <summary>검색 작업에 현재 디렉터리와 모든 하위 디렉터리를 포함합니다.이 옵션을 지정하면 탑재된 드라이브 및 심볼 링크와 같은 재분석 지점이 검색에 포함됩니다.</summary>
    </member>
    <member name="F:System.IO.SearchOption.TopDirectoryOnly">
      <summary>검색 작업에 현재 디렉터리만 포함합니다.</summary>
    </member>
  </members>
</doc>