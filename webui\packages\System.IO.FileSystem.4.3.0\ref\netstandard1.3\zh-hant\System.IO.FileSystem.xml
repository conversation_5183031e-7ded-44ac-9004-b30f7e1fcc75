﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.IO.FileSystem</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Win32.SafeHandles.SafeFileHandle">
      <summary>代表檔案控制代碼的包裝函式類別。</summary>
    </member>
    <member name="M:Microsoft.Win32.SafeHandles.SafeFileHandle.#ctor(System.IntPtr,System.Boolean)">
      <summary>初始化 <see cref="T:Microsoft.Win32.SafeHandles.SafeFileHandle" /> 類別的新執行個體。</summary>
      <param name="preexistingHandle">
        <see cref="T:System.IntPtr" /> 物件，表示要使用的既有控制代碼。</param>
      <param name="ownsHandle">true 表示在結束階段確實地釋放控制代碼，而 false 表示不要確實地釋放 (不建議)。</param>
    </member>
    <member name="P:Microsoft.Win32.SafeHandles.SafeFileHandle.IsInvalid"></member>
    <member name="T:System.IO.Directory">
      <summary>公開建立、移動和全面列舉目錄和子目錄的靜態方法。此類別無法被繼承。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Directory.CreateDirectory(System.String)">
      <summary>在指定的路徑建立所有目錄和子目錄 (如果這些目錄尚不存在)。</summary>
      <returns>表示指定路徑的目錄之物件。不論指定之路徑的目錄是否已經存在，都會傳回這個物件。</returns>
      <param name="path">要建立的目錄。</param>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 指定的目錄為檔案。-或-不知道網路名稱。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串，其中只包含泛空白字元，或包含一個或多個無效的字元。使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法，您可以查詢無效的字元。-或-<paramref name="path" /> 含有前置的冒號字元 (:)，或僅含有冒號字元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 包含冒號字元 (:)，此字元不屬於磁碟機標籤 ("C:\") 的一部分。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.Delete(System.String)">
      <summary>從指定的路徑刪除空目錄。</summary>
      <param name="path">要移除的空目錄名稱。這個目錄必須為可寫入的和空的。</param>
      <exception cref="T:System.IO.IOException">具有 <paramref name="path" /> 所指定之相同名稱和位置的檔案已存在。-或-此目錄是應用程式目前的工作目錄。-或-<paramref name="path" /> 指定的目錄不是空的。-或-目錄是唯讀的或包含唯讀檔案。-或-目錄正由另一個處理序使用。-或-此目錄具有開啟控制代碼，而且作業系統是 Windows XP 或先前的版本。這個開放控制代碼可能是目錄所產生的結果。如需詳細資訊，請參閱如何：列舉目錄和檔案。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串，其中只包含泛空白字元，或包含一個或多個無效的字元。使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法，您可以查詢無效的字元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 不存在或找不到。-或-<paramref name="path" /> 參考檔案，而不是目錄。-或-指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.Delete(System.String,System.Boolean)">
      <summary>刪除指定目錄，並且若有指示的話，刪除目錄中的任何子目錄和檔案。</summary>
      <param name="path">要移除的目錄名稱。</param>
      <param name="recursive">若要移除 <paramref name="path" /> 中的目錄、子目錄和檔案，則為 true，否則為 false。</param>
      <exception cref="T:System.IO.IOException">具有 <paramref name="path" /> 所指定之相同名稱和位置的檔案已存在。-或-<paramref name="path" /> 所指定的目錄是唯讀的，或者 <paramref name="recursive" /> 是 false 而且 <paramref name="path" /> 是空目錄。-或-此目錄是應用程式目前的工作目錄。-或-目錄包含唯讀檔案。-或-目錄正由另一個處理序使用。此目錄或其中一個檔案具有開啟控制代碼，而且作業系統是 Windows XP 或先前的版本。這個開啟控制代碼可能是列舉目錄和檔案所產生。如需詳細資訊，請參閱如何：列舉目錄和檔案。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串，其中只包含泛空白字元，或包含一個或多個無效的字元。使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法，您可以查詢無效的字元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 不存在或找不到。-或-<paramref name="path" /> 參考檔案，而不是目錄。-或-指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String)">
      <summary>傳回指定之路徑中目錄名稱的可列舉集合。</summary>
      <returns>
        <paramref name="path" /> 指定的目錄中，目錄完整名稱 (包括路徑) 的可列舉集合。</returns>
      <param name="path">要搜尋之目錄的相對或絕對路徑。這個字串不會區分大小寫。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " /> 是長度為零的字串，其中只包含泛空白字元，或包含無效的字元。使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法，您可以查詢無效的字元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 無效，例如參考未對應的磁碟機。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 為檔案名稱。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或組合都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String,System.String)">
      <summary>傳回指定之路徑中符合搜尋模式之目錄名稱的可列舉集合。</summary>
      <returns>
        <paramref name="path" /> 指定的目錄中，符合指定搜尋模式的目錄完整名稱 (包括路徑) 的可列舉集合。</returns>
      <param name="path">要搜尋之目錄的相對或絕對路徑。這個字串不會區分大小寫。</param>
      <param name="searchPattern">要比對 <paramref name="path" /> 中目錄名稱的搜尋字串。這個參數可以包含有效常值路徑與萬用 (* 和 ?) 字元 (請參閱＜備註＞) 的組合，但是不支援規則運算式。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " /> 是長度為零的字串，其中只包含泛空白字元，或包含無效的字元。您可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法來查詢無效的字元。-或-<paramref name="searchPattern" /> 不包含有效模式。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。-或-<paramref name="searchPattern" /> 為 null。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 無效，例如參考未對應的磁碟機。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 為檔案名稱。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或組合都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String,System.String,System.IO.SearchOption)">
      <summary>傳回指定之路徑中符合搜尋模式之目錄名稱的可列舉集合，並選擇性地搜尋子目錄。</summary>
      <returns>
        <paramref name="path" /> 指定的目錄中，符合指定搜尋模式和選項的目錄完整名稱 (包括路徑) 的可列舉集合。</returns>
      <param name="path">要搜尋之目錄的相對或絕對路徑。這個字串不會區分大小寫。</param>
      <param name="searchPattern">要比對 <paramref name="path" /> 中目錄名稱的搜尋字串。這個參數可以包含有效常值路徑與萬用 (* 和 ?) 字元 (請參閱＜備註＞) 的組合，但是不支援規則運算式。</param>
      <param name="searchOption">其中一個列舉值，該值會指定搜尋作業應該只包含目前目錄還是包含所有子目錄。預設值是 <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " /> 是長度為零的字串，其中只包含泛空白字元，或包含無效的字元。使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法，您可以查詢無效的字元。-或-<paramref name="searchPattern" /> 不包含有效模式。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。-或-<paramref name="searchPattern" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> 不是有效的 <see cref="T:System.IO.SearchOption" /> 值。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 無效，例如參考未對應的磁碟機。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 為檔案名稱。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或組合都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String)">
      <summary>傳回指定之路徑中檔案名稱的可列舉集合。</summary>
      <returns>
        <paramref name="path" /> 指定的目錄中，檔案完整名稱 (包括路徑) 的可列舉集合。</returns>
      <param name="path">要搜尋之目錄的相對或絕對路徑。這個字串不會區分大小寫。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " /> 是長度為零的字串，其中只包含泛空白字元，或包含無效的字元。使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法，您可以查詢無效的字元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 無效，例如參考未對應的磁碟機。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 為檔案名稱。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或組合都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String,System.String)">
      <summary>傳回指定之路徑中符合搜尋模式之檔名的可列舉集合。</summary>
      <returns>
        <paramref name="path" /> 指定的目錄中，符合指定搜尋模式的檔案完整名稱 (包括路徑) 的可列舉集合。</returns>
      <param name="path">要搜尋之目錄的相對或絕對路徑。這個字串不會區分大小寫。</param>
      <param name="searchPattern">要比對 <paramref name="path" /> 中檔案名稱的搜尋字串。這個參數可以包含有效常值路徑與萬用 (* 和 ?) 字元 (請參閱＜備註＞) 的組合，但是不支援規則運算式。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " /> 是長度為零的字串，其中只包含泛空白字元，或包含無效的字元。使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法，您可以查詢無效的字元。-或-<paramref name="searchPattern" /> 不包含有效模式。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。-或-<paramref name="searchPattern" /> 為 null。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 無效，例如參考未對應的磁碟機。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 為檔案名稱。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或組合都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String,System.String,System.IO.SearchOption)">
      <summary>傳回指定之路徑中符合搜尋模式之檔名的可列舉集合，並選擇性地搜尋子目錄。</summary>
      <returns>
        <paramref name="path" /> 指定的目錄中，符合指定搜尋模式和選項的檔案完整名稱 (包括路徑) 的可列舉集合。</returns>
      <param name="path">要搜尋之目錄的相對或絕對路徑。這個字串不會區分大小寫。</param>
      <param name="searchPattern">要比對 <paramref name="path" /> 中檔案名稱的搜尋字串。這個參數可以包含有效常值路徑與萬用 (* 和 ?) 字元 (請參閱＜備註＞) 的組合，但是不支援規則運算式。</param>
      <param name="searchOption">其中一個列舉值，該值會指定搜尋作業應該只包含目前目錄還是包含所有子目錄。預設值是 <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " /> 是長度為零的字串，其中只包含泛空白字元，或包含無效的字元。使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法，您可以查詢無效的字元。-或-<paramref name="searchPattern" /> 不包含有效模式。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。-或-<paramref name="searchPattern" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> 不是有效的 <see cref="T:System.IO.SearchOption" /> 值。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 無效，例如參考未對應的磁碟機。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 為檔案名稱。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或組合都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String)">
      <summary>傳回指定之路徑中檔案名稱及目錄名稱的可列舉集合。</summary>
      <returns>
        <paramref name="path" /> 所指定目錄中檔案系統項目的可列舉集合。</returns>
      <param name="path">要搜尋之目錄的相對或絕對路徑。這個字串不會區分大小寫。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " /> 是長度為零的字串，其中只包含泛空白字元，或包含無效的字元。使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法，您可以查詢無效的字元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 無效，例如參考未對應的磁碟機。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 為檔案名稱。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或組合都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String,System.String)">
      <summary>傳回指定之路徑中符合搜尋模式之檔案名稱及目錄名稱的可列舉集合。</summary>
      <returns>
        <paramref name="path" /> 所指定目錄中指定搜尋模式的檔案系統項目的可列舉集合。</returns>
      <param name="path">要搜尋之目錄的相對或絕對路徑。這個字串不會區分大小寫。</param>
      <param name="searchPattern">要比對 <paramref name="path" /> 中檔案系統項目名稱的搜尋字串。這個參數可以包含有效常值路徑與萬用 (* 和 ?) 字元 (請參閱＜備註＞) 的組合，但是不支援規則運算式。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " /> 是長度為零的字串，其中只包含泛空白字元，或包含無效的字元。使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法，您可以查詢無效的字元。-或-<paramref name="searchPattern" /> 不包含有效模式。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。-或-<paramref name="searchPattern" /> 為 null。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 無效，例如參考未對應的磁碟機。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 為檔案名稱。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或組合都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String,System.String,System.IO.SearchOption)">
      <summary>傳回指定之路徑中符合搜尋模式之檔名和目錄名稱的可列舉集合，並選擇性地搜尋子目錄。</summary>
      <returns>
        <paramref name="path" /> 所指定目錄中指定搜尋模式和選項的檔案系統項目的可列舉集合。</returns>
      <param name="path">要搜尋之目錄的相對或絕對路徑。這個字串不會區分大小寫。</param>
      <param name="searchPattern">要比對 <paramref name="path" /> 中檔案系統項目的搜尋字串。這個參數可以包含有效常值路徑與萬用 (* 和 ?) 字元 (請參閱＜備註＞) 的組合，但是不支援規則運算式。</param>
      <param name="searchOption">其中一個列舉值，該值會指定搜尋作業應該只包含目前目錄還是包含所有子目錄。預設值是 <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " /> 是長度為零的字串，其中只包含泛空白字元，或包含無效的字元。使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法，您可以查詢無效的字元。-或-<paramref name="searchPattern" /> 不包含有效模式。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。-或-<paramref name="searchPattern" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> 不是有效的 <see cref="T:System.IO.SearchOption" /> 值。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 無效，例如參考未對應的磁碟機。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 為檔案名稱。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或組合都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
    </member>
    <member name="M:System.IO.Directory.Exists(System.String)">
      <summary>判斷指定路徑是否參考磁碟上的現有目錄。</summary>
      <returns>如果 <paramref name="path" /> 參考現有的目錄，則為 true。如果目錄不存在，或嘗試判斷指定的檔案是否存在時發生錯誤，則為 false。如果 <paramref name="path" /> 參考現有目錄則為 true，否則為 false。</returns>
      <param name="path">要測試的路徑。</param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetCreationTime(System.String)">
      <summary>取得目錄的建立日期和時間。</summary>
      <returns>結構，設定為指定之目錄的建立日期和時間。這個值是以本地時間表示。</returns>
      <param name="path">目錄的路徑。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串，其中只包含泛空白字元，或包含一個或多個無效的字元。使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法，您可以查詢無效的字元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetCreationTimeUtc(System.String)">
      <summary>取得目錄的建立日期和時間，格式為 Coordinated Universal Time (UTC)。</summary>
      <returns>結構，設定為指定之目錄的建立日期和時間。這個值是以 UTC 時間表示的。</returns>
      <param name="path">目錄的路徑。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串，其中只包含泛空白字元，或包含一個或多個無效的字元。使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法，您可以查詢無效的字元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetCurrentDirectory">
      <summary>取得應用程式的目前工作目錄。</summary>
      <returns>含有目前工作目錄路徑且未以反斜線 (\) 結尾的字串。</returns>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.NotSupportedException">作業系統是沒有目前目錄功能的 Windows CE。這個方法在 .NET Compact Framework 中提供，但目前尚不支援。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String)">
      <summary>傳回指定之目錄中的子目錄名稱 (包括其路徑)。</summary>
      <returns>指定之路徑中的子目錄完整名稱 (包括路徑) 陣列，如果找不到任何目錄則為空陣列。</returns>
      <param name="path">要搜尋之目錄的相對或絕對路徑。這個字串不會區分大小寫。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串，其中只包含泛空白字元，或包含一個或多個無效的字元。使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法，您可以查詢無效的字元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 為檔案名稱。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String,System.String)">
      <summary>傳回指定目錄中符合指定搜尋模式的子目錄名稱 (包括檔案的路徑)。</summary>
      <returns>指定目錄中，符合搜尋模式的子目錄完整名稱 (包括路徑) 陣列，如果找不到任何目錄則為空陣列。</returns>
      <param name="path">要搜尋之目錄的相對或絕對路徑。這個字串不會區分大小寫。</param>
      <param name="searchPattern">要比對 <paramref name="path" /> 中子目錄名稱的搜尋字串。這個參數可以包含有效常值與萬用字元 (請參閱＜備註＞) 的組合，但是不支援規則運算式。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串，其中只包含泛空白字元，或包含一個或多個無效的字元。使用 <see cref="M:System.IO.Path.GetInvalidPathChars" />，您可以查詢無效的字元。-或-<paramref name="searchPattern" /> 不包含有效模式。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 或 <paramref name="searchPattern" /> 是 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 為檔案名稱。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String,System.String,System.IO.SearchOption)">
      <summary>傳回指定目錄中符合指定搜尋模式之子目錄 (包含其路徑) 的名稱，並且選擇性地搜尋子目錄。</summary>
      <returns>符合指定之準則的子目錄完整名稱 (包括路徑) 陣列，如果找不到任何目錄則為空陣列。</returns>
      <param name="path">要搜尋之目錄的相對或絕對路徑。這個字串不會區分大小寫。</param>
      <param name="searchPattern">要比對 <paramref name="path" /> 中子目錄名稱的搜尋字串。這個參數可以包含有效常值與萬用字元 (請參閱＜備註＞) 的組合，但是不支援規則運算式。</param>
      <param name="searchOption">其中一個列舉值，這個值會指定搜尋作業應該包含所有子目錄或只包含目前目錄。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串，其中只包含泛空白字元，或包含一個或多個無效的字元。使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法，您可以查詢無效的字元。-或-<paramref name="searchPattern" /> 不包含有效模式。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 或 <paramref name="searchPattern" /> 是 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> 不是有效的 <see cref="T:System.IO.SearchOption" /> 值。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 為檔案名稱。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
    </member>
    <member name="M:System.IO.Directory.GetDirectoryRoot(System.String)">
      <summary>傳回指定路徑的磁碟區資訊、根目錄資訊或兩者。</summary>
      <returns>含有指定路徑的磁碟區資訊、根目錄資訊或兩者的字串。</returns>
      <param name="path">檔案或目錄的路徑。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串，其中只包含泛空白字元，或包含一個或多個無效的字元。您可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 來查詢無效的字元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String)">
      <summary>傳回指定目錄中的檔案名稱 (包括路徑)。</summary>
      <returns>指定之目錄中的檔案完整名稱 (包括路徑) 陣列，如果找不到任何檔案則為空陣列。</returns>
      <param name="path">要搜尋之目錄的相對或絕對路徑。這個字串不會區分大小寫。</param>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 為檔案名稱。-或-發生網路錯誤。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串，其中只包含泛空白字元，或包含一個或多個無效的字元。使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法，您可以查詢無效的字元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑找不到或無效 (例如，位於未對應的磁碟上)。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String,System.String)">
      <summary>傳回指定目錄中符合指定搜尋模式的檔案名稱 (包括檔案的路徑)。</summary>
      <returns>指定目錄中，符合指定搜尋模式的檔案完整名稱 (包括路徑) 陣列，如果找不到任何檔案則為空陣列。</returns>
      <param name="path">要搜尋之目錄的相對或絕對路徑。這個字串不會區分大小寫。</param>
      <param name="searchPattern">要比對 <paramref name="path" /> 中檔案名稱的搜尋字串。這個參數可以包含有效常值路徑與萬用 (* 和 ?) 字元 (請參閱＜備註＞) 的組合，但是不支援規則運算式。</param>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 為檔案名稱。-或-發生網路錯誤。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串，其中只包含泛空白字元，或包含一個或多個無效的字元。使用 <see cref="M:System.IO.Path.GetInvalidPathChars" />，您可以查詢無效的字元。-或-<paramref name="searchPattern" /> 不包含有效模式。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 或 <paramref name="searchPattern" /> 是 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑找不到或無效 (例如，位於未對應的磁碟上)。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String,System.String,System.IO.SearchOption)">
      <summary>傳回指定目錄中符合指定搜尋模式的檔案名稱 (包括檔案的路徑)，並使用值判斷是否搜尋子目錄。</summary>
      <returns>指定目錄中，符合指定搜尋模式和選項的檔案完整名稱 (包括路徑) 陣列，如果找不到任何檔案則為空陣列。</returns>
      <param name="path">要搜尋之目錄的相對或絕對路徑。這個字串不會區分大小寫。</param>
      <param name="searchPattern">要比對 <paramref name="path" /> 中檔案名稱的搜尋字串。這個參數可以包含有效常值路徑與萬用 (* 和 ?) 字元 (請參閱＜備註＞) 的組合，但是不支援規則運算式。</param>
      <param name="searchOption">其中一個列舉值，這個值會指定搜尋作業應該包含所有子目錄或只包含目前目錄。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串，其中只包含泛空白字元，或包含一個或多個無效的字元。您可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法來查詢無效的字元。-或-<paramref name="searchPattern" /> 不包含有效模式。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 或 <paramref name="searchpattern" /> 是 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> 不是有效的 <see cref="T:System.IO.SearchOption" /> 值。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑找不到或無效 (例如，位於未對應的磁碟上)。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 為檔案名稱。-或-發生網路錯誤。</exception>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String)">
      <summary>傳回指定路徑中所有檔案和子目錄的名稱。</summary>
      <returns>指定之目錄中的檔案及子目錄名稱陣列，如果找不到檔案或子目錄則為空陣列。</returns>
      <param name="path">要搜尋之目錄的相對或絕對路徑。這個字串不會區分大小寫。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串，其中只包含泛空白字元，或包含一個或多個無效的字元。您可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 來查詢無效的字元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 為檔案名稱。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String,System.String)">
      <summary>傳回指定之路徑中符合搜尋模式之檔案名稱及目錄名稱陣列。</summary>
      <returns>符合指定搜尋條件的檔案名稱及目錄名稱陣列，如果找不到檔案或目錄則為空陣列。</returns>
      <param name="path">要搜尋之目錄的相對或絕對路徑。這個字串不會區分大小寫。</param>
      <param name="searchPattern">要比對 <paramref name="path" /> 中檔案及目錄名稱的搜尋字串。這個參數可以包含有效常值路徑與萬用 (* 和 ?) 字元 (請參閱＜備註＞) 的組合，但是不支援規則運算式。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串，其中只包含泛空白字元，或包含一個或多個無效的字元。您可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法來查詢無效的字元。-或-<paramref name="searchPattern" /> 不包含有效模式。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 或 <paramref name="searchPattern" /> 是 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 為檔案名稱。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String,System.String,System.IO.SearchOption)">
      <summary>傳回指定之路徑中符合搜尋模式之所有檔名和目錄名稱的陣列，並選擇性地搜尋子目錄。</summary>
      <returns>檔案名稱及目錄名稱符合指定搜尋條件的檔案陣列，如果找不到檔案或目錄則為空陣列。</returns>
      <param name="path">要搜尋之目錄的相對或絕對路徑。這個字串不會區分大小寫。</param>
      <param name="searchPattern">要比對 <paramref name="path" /> 中檔案及目錄名稱的搜尋字串。這個參數可以包含有效常值路徑與萬用 (* 和 ?) 字元 (請參閱＜備註＞) 的組合，但是不支援規則運算式。</param>
      <param name="searchOption">其中一個列舉值，該值會指定搜尋作業應該只包含目前目錄還是包含所有子目錄。預設值是 <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " /> 是長度為零的字串，其中只包含泛空白字元，或包含無效的字元。使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法，您可以查詢無效的字元。-或-<paramref name="searchPattern" /> 不包含有效模式。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。-或-<paramref name="searchPattern" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> 不是有效的 <see cref="T:System.IO.SearchOption" /> 值。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 無效，例如參考未對應的磁碟機。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 為檔案名稱。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或組合都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
    </member>
    <member name="M:System.IO.Directory.GetLastAccessTime(System.String)">
      <summary>傳回指定檔案或目錄上次被存取的日期和時間。</summary>
      <returns>結構，設定為指定之檔案或目錄上次被存取的日期和時間。這個值是以本地時間表示。</returns>
      <param name="path">要取得其存取日期和時間資訊的檔案或目錄。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串，其中只包含泛空白字元，或包含一個或多個無效的字元。您可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法來查詢無效的字元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 參數的格式無效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetLastAccessTimeUtc(System.String)">
      <summary>傳回上次存取指定檔案或目錄的日期和時間，格式為 Coordinated Universal Time (UTC)。</summary>
      <returns>結構，設定為指定之檔案或目錄上次被存取的日期和時間。這個值是以 UTC 時間表示的。</returns>
      <param name="path">要取得其存取日期和時間資訊的檔案或目錄。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串，其中只包含泛空白字元，或包含一個或多個無效的字元。您可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法來查詢無效的字元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 參數的格式無效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetLastWriteTime(System.String)">
      <summary>傳回指定檔案或目錄上次被寫入的日期和時間。</summary>
      <returns>結構，設定為指定之檔案或目錄上次被寫入的日期和時間。這個值是以本地時間表示。</returns>
      <param name="path">要取得其修改日期和時間資訊的檔案或目錄。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串，其中只包含泛空白字元，或包含一個或多個無效的字元。您可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法來查詢無效的字元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetLastWriteTimeUtc(System.String)">
      <summary>傳回上次寫入指定檔案或目錄的日期和時間，格式為 Coordinated Universal Time (UTC)。</summary>
      <returns>結構，設定為指定之檔案或目錄上次被寫入的日期和時間。這個值是以 UTC 時間表示的。</returns>
      <param name="path">要取得其修改日期和時間資訊的檔案或目錄。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串，其中只包含泛空白字元，或包含一個或多個無效的字元。您可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法來查詢無效的字元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetParent(System.String)">
      <summary>擷取指定路徑的父目錄，包括絕對和相對路徑兩者。</summary>
      <returns>父目錄，或者為 null，如果 <paramref name="path" /> 是根目錄，包括 UNC 伺機器或共用名稱的根 (Root)。</returns>
      <param name="path">要擷取其父目錄的路徑。</param>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 指定的目錄為唯讀。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串，其中只包含泛空白字元，或包含一個或多個無效的字元。您可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法來查詢無效的字元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">找不到指定的路徑。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.Move(System.String,System.String)">
      <summary>移動檔案或目錄和其內容到新位置。</summary>
      <param name="sourceDirName">要移動的檔案或目錄的路徑。</param>
      <param name="destDirName">
        <paramref name="sourceDirName" /> 的新位置的路徑。如果 <paramref name="sourceDirName" /> 是檔案，則 <paramref name="destDirName" /> 也必須是檔案名稱。</param>
      <exception cref="T:System.IO.IOException">已嘗試將目錄移動到不同磁碟區。-或-<paramref name="destDirName" /> 已存在。-或-<paramref name="sourceDirName" /> 和 <paramref name="destDirName" /> 參數參考到相同檔案或目錄。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceDirName" /> 或 <paramref name="destDirName" /> 是長度為零的字串，其中只包含泛空白字元，或包含一個或多個無效的字元。您可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法來查詢無效的字元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceDirName" /> 或 <paramref name="destDirName" /> 是 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">由 <paramref name="sourceDirName" /> 指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetCreationTime(System.String,System.DateTime)">
      <summary>設定指定檔案或目錄建立的日期和時間。</summary>
      <param name="path">要設定其日期和時間資訊的檔案或目錄。</param>
      <param name="creationTime">上次寫入檔案或目錄的日期和時間。這個值是以本地時間表示。</param>
      <exception cref="T:System.IO.FileNotFoundException">找不到指定的路徑。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串，其中只包含泛空白字元，或包含一個或多個無效的字元。您可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法來查詢無效的字元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> 指定這個作業允許的日期或時間範圍以外的值。</exception>
      <exception cref="T:System.PlatformNotSupportedException">目前的作業系統不是 Windows NT (含) 以後版本。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetCreationTimeUtc(System.String,System.DateTime)">
      <summary>設定指定檔案或目錄的建立日期和時間，格式為 Coordinated Universal Time (UTC)。</summary>
      <param name="path">要設定其日期和時間資訊的檔案或目錄。</param>
      <param name="creationTimeUtc">建立目錄或檔案的日期和時間。這個值是以本地時間表示。</param>
      <exception cref="T:System.IO.FileNotFoundException">找不到指定的路徑。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串，其中只包含泛空白字元，或包含一個或多個無效的字元。您可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法來查詢無效的字元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> 指定這個作業允許的日期或時間範圍以外的值。</exception>
      <exception cref="T:System.PlatformNotSupportedException">目前的作業系統不是 Windows NT (含) 以後版本。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetCurrentDirectory(System.String)">
      <summary>將應用程式的目前工作目錄設定為指定目錄。</summary>
      <param name="path">設定為目前工作目錄的路徑。</param>
      <exception cref="T:System.IO.IOException">發生 I/O 錯誤。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串，其中只包含泛空白字元，或包含一個或多個無效的字元。您可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法來查詢無效的字元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有所需的使用權限來存取 Unmanaged 程式碼。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到指定的路徑。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">找不到指定的目錄。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastAccessTime(System.String,System.DateTime)">
      <summary>設定指定檔案或目錄上次被存取的日期和時間。</summary>
      <param name="path">要設定其存取日期和時間資訊的檔案或目錄。</param>
      <param name="lastAccessTime">物件，含有要設定給 <paramref name="path" /> 的存取日期和時間的值。這個值是以本地時間表示。</param>
      <exception cref="T:System.IO.FileNotFoundException">找不到指定的路徑。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串，其中只包含泛空白字元，或包含一個或多個無效的字元。您可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法來查詢無效的字元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.PlatformNotSupportedException">目前的作業系統不是 Windows NT (含) 以後版本。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTime" /> 指定這個作業允許的日期或時間範圍以外的值。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastAccessTimeUtc(System.String,System.DateTime)">
      <summary>設定上次存取指定檔案或目錄的日期和時間，格式為 Coordinated Universal Time (UTC)。</summary>
      <param name="path">要設定其存取日期和時間資訊的檔案或目錄。</param>
      <param name="lastAccessTimeUtc">物件，含有要設定給 <paramref name="path" /> 的存取日期和時間的值。這個值是以 UTC 時間表示的。</param>
      <exception cref="T:System.IO.FileNotFoundException">找不到指定的路徑。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串，其中只包含泛空白字元，或包含一個或多個無效的字元。您可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法來查詢無效的字元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.PlatformNotSupportedException">目前的作業系統不是 Windows NT (含) 以後版本。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTimeUtc" /> 指定這個作業允許的日期或時間範圍以外的值。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastWriteTime(System.String,System.DateTime)">
      <summary>設定目錄上次被寫入的日期和時間。</summary>
      <param name="path">目錄的路徑。</param>
      <param name="lastWriteTime">目錄上次被寫入的日期和時間。這個值是以本地時間表示。</param>
      <exception cref="T:System.IO.FileNotFoundException">找不到指定的路徑。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串，其中只包含泛空白字元，或包含一個或多個無效的字元。您可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法來查詢無效的字元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.PlatformNotSupportedException">目前的作業系統不是 Windows NT (含) 以後版本。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTime" /> 指定這個作業允許的日期或時間範圍以外的值。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastWriteTimeUtc(System.String,System.DateTime)">
      <summary>設定上次寫入目錄的日期和時間，格式為 Coordinated Universal Time (UTC)。</summary>
      <param name="path">目錄的路徑。</param>
      <param name="lastWriteTimeUtc">目錄上次被寫入的日期和時間。這個值是以 UTC 時間表示的。</param>
      <exception cref="T:System.IO.FileNotFoundException">找不到指定的路徑。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串，其中只包含泛空白字元，或包含一個或多個無效的字元。您可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法來查詢無效的字元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的使用權限。</exception>
      <exception cref="T:System.PlatformNotSupportedException">目前的作業系統不是 Windows NT (含) 以後版本。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTimeUtc" /> 指定這個作業允許的日期或時間範圍以外的值。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.IO.DirectoryInfo">
      <summary>公開建立、移動和全面列舉目錄和子目錄的執行個體 (Instance) 方法。此類別無法被繼承。若要瀏覽此類型的.NET Framework 原始碼，請參閱
                                參考來源.
                            </summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.DirectoryInfo.#ctor(System.String)">
      <summary>初始化 
<see cref="T:System.IO.DirectoryInfo" />類別上指定的路徑。
                            </summary>
      <param name="path">字串，指定要在其中建立路徑
                                    DirectoryInfo.
                                </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />是
                                        null.
                                    </exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 含有無效的字元，例如 "、&amp;lt;、&amp;gt; 或 |。
                                    </exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。指定的路徑、檔名，或是兩者都太長。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.Create">
      <summary>建立目錄。</summary>
      <exception cref="T:System.IO.IOException">無法建立目錄</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.CreateSubdirectory(System.String)">
      <summary>在指定路徑上建立子目錄。指定的路徑可以是相對的這個執行個體
<see cref="T:System.IO.DirectoryInfo" />類別。
                        </summary>
      <returns>在指定的最後一個目錄
                                <paramref name="path" />.
                            </returns>
      <param name="path">指定的路徑。不能是不同的磁碟區或通用命名慣例 (UNC) 名稱。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />未指定有效的檔案路徑或包含不正確
DirectoryInfo字元。
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />是
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效，例如位在未對應的磁碟上。</exception>
      <exception cref="T:System.IO.IOException">無法建立子目錄-或-檔案或目錄已經具有所指定的名稱
                                        <paramref name="path" />.
                                    </exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。指定的路徑、檔名，或是兩者都太長。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端不具有建立目錄的程式碼存取權限。-或-呼叫端沒有程式碼存取權限讀取所傳回的目錄
<see cref="T:System.IO.DirectoryInfo" /> 物件
                                這可能會發生時
<paramref name="path" />參數描述現有的目錄。
                                </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 包含冒號字元 (:)，此字元不屬於磁碟機標籤 ("C:\") 的一部分。
                                    </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.Delete">
      <summary>這會刪除
<see cref="T:System.IO.DirectoryInfo" />如果它是空的。
                            </summary>
      <exception cref="T:System.UnauthorizedAccessException">目錄包含唯讀檔案。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">描述由這個目錄
<see cref="T:System.IO.DirectoryInfo" />物件不存在或找不到。
                                    </exception>
      <exception cref="T:System.IO.IOException">目錄不是空的。-或-此目錄是應用程式目前的工作目錄。-或-此目錄具有開啟控制代碼，而且作業系統是 Windows XP 或先前的版本。這個開啟控制代碼可能是列舉目錄所產生的結果。如需詳細資訊，請參閱
                                    如何：列舉目錄和檔案.
                                </exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.Delete(System.Boolean)">
      <summary>刪除這個執行個體
<see cref="T:System.IO.DirectoryInfo" />指定是否要刪除的子目錄和檔案。
                            </summary>
      <param name="recursive">true若要刪除此目錄，及其子目錄中，所有的檔案 ；否則，
                                    false.
                                </param>
      <exception cref="T:System.UnauthorizedAccessException">目錄包含唯讀檔案。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">描述由這個目錄
<see cref="T:System.IO.DirectoryInfo" />物件不存在或找不到。
                                    </exception>
      <exception cref="T:System.IO.IOException">目錄是唯讀的。-或-這個目錄包含一個或多個檔案或子目錄，
<paramref name="recursive" />是
                                        false.
                                    -或-此目錄是應用程式目前的工作目錄。-或-此目錄或其中一個檔案具有開啟控制代碼，而且作業系統是 Windows XP 或先前的版本。這個開啟控制代碼可能是列舉目錄和檔案所產生。如需詳細資訊，請參閱
                                    如何：列舉目錄和檔案.
                                </exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories">
      <summary>傳回目前目錄中目錄資訊的可列舉集合。</summary>
      <returns>目前目錄中目錄的可列舉集合。</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">在封裝路徑
<see cref="T:System.IO.DirectoryInfo" />無效的物件 (例如，它是未對應的磁碟機上)。
                                    </exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories(System.String)">
      <summary>傳回符合指定之搜尋模式的目錄資訊的可列舉集合。</summary>
      <returns>比對之目錄的可列舉集合
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">要比對目錄名稱的搜尋字串。這個參數可以包含有效常值路徑與萬用 (* 和 ?) 字元 (請參閱＜備註＞) 的組合，但是不支援規則運算式。預設模式為 "*"，這會傳回所有檔案。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />是
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">在封裝路徑
<see cref="T:System.IO.DirectoryInfo" />無效的物件 (例如，它是未對應的磁碟機上)。
                                    </exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories(System.String,System.IO.SearchOption)">
      <summary>傳回符合指定之搜尋模式和搜尋子目錄選項的目錄資訊的可列舉集合。</summary>
      <returns>比對之目錄的可列舉集合
<paramref name="searchPattern" />和
                                <paramref name="searchOption" />.
                            </returns>
      <param name="searchPattern">要比對目錄名稱的搜尋字串。這個參數可以包含有效常值路徑與萬用 (* 和 ?) 字元 (請參閱＜備註＞) 的組合，但是不支援規則運算式。預設模式為 "*"，這會傳回所有檔案。</param>
      <param name="searchOption">其中一個列舉值，指定搜尋作業應該只包含目前目錄還是包含所有子目錄。預設值是
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />是
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />不是有效
<see cref="T:System.IO.SearchOption" /> 值。
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">在封裝路徑
<see cref="T:System.IO.DirectoryInfo" />無效的物件 (例如，它是未對應的磁碟機上)。
                                    </exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles">
      <summary>傳回目前目錄中檔案資訊的可列舉集合。</summary>
      <returns>目前目錄中檔案的可列舉集合。</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">在封裝路徑
<see cref="T:System.IO.DirectoryInfo" />無效的物件 (例如，它是未對應的磁碟機上)。
                                    </exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles(System.String)">
      <summary>傳回符合指定之搜尋模式的檔案資訊的可列舉集合。</summary>
      <returns>符合檔案的可列舉集合
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">要比對檔案名稱的搜尋字串。這個參數可以包含有效常值路徑與萬用 (* 和 ?) 字元 (請參閱＜備註＞) 的組合，但是不支援規則運算式。預設模式為 "*"，這會傳回所有檔案。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />是
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">在封裝路徑
<see cref="T:System.IO.DirectoryInfo" />無效的物件，(比方說，它是未對應的磁碟機上)。
                                    </exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles(System.String,System.IO.SearchOption)">
      <summary>傳回符合指定之搜尋模式和搜尋子目錄選項的檔案資訊的可列舉集合。</summary>
      <returns>符合檔案的可列舉集合
<paramref name="searchPattern" />和
                                <paramref name="searchOption" />.
                            </returns>
      <param name="searchPattern">要比對檔案名稱的搜尋字串。這個參數可以包含有效常值路徑與萬用 (* 和 ?) 字元 (請參閱＜備註＞) 的組合，但是不支援規則運算式。預設模式為 "*"，這會傳回所有檔案。</param>
      <param name="searchOption">其中一個列舉值，指定搜尋作業應該只包含目前目錄還是包含所有子目錄。預設值是
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />是
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />不是有效
<see cref="T:System.IO.SearchOption" /> 值。
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">在封裝路徑
<see cref="T:System.IO.DirectoryInfo" />無效的物件 (例如，它是未對應的磁碟機上)。
                                    </exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos">
      <summary>傳回目前目錄中檔案系統資訊的可列舉集合。</summary>
      <returns>目前目錄中檔案系統資訊的可列舉集合。</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">在封裝路徑
<see cref="T:System.IO.DirectoryInfo" />無效的物件 (例如，它是未對應的磁碟機上)。
                                    </exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos(System.String)">
      <summary>傳回符合指定之搜尋模式的檔案系統資訊的可列舉集合。</summary>
      <returns>比對檔案系統資訊物件的可列舉集合
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">要比對目錄名稱的搜尋字串。這個參數可以包含有效常值路徑與萬用 (* 和 ?) 字元 (請參閱＜備註＞) 的組合，但是不支援規則運算式。預設模式為 "*"，這會傳回所有檔案。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />是
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">在封裝路徑
<see cref="T:System.IO.DirectoryInfo" />無效的物件 (例如，它是未對應的磁碟機上)。
                                    </exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos(System.String,System.IO.SearchOption)">
      <summary>傳回符合指定之搜尋模式和搜尋子目錄選項的檔案系統資訊的可列舉集合。</summary>
      <returns>比對檔案系統資訊物件的可列舉集合
<paramref name="searchPattern" />和
                                <paramref name="searchOption" />.
                            </returns>
      <param name="searchPattern">要比對目錄名稱的搜尋字串。這個參數可以包含有效常值路徑與萬用 (* 和 ?) 字元 (請參閱＜備註＞) 的組合，但是不支援規則運算式。預設模式為 "*"，這會傳回所有檔案。</param>
      <param name="searchOption">其中一個列舉值，指定搜尋作業應該只包含目前目錄還是包含所有子目錄。預設值是
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />是
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />不是有效
<see cref="T:System.IO.SearchOption" /> 值。
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">在封裝路徑
<see cref="T:System.IO.DirectoryInfo" />無效的物件 (例如，它是未對應的磁碟機上)。
                                    </exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
    </member>
    <member name="P:System.IO.DirectoryInfo.Exists">
      <summary>取得值，指出目錄是否存在。</summary>
      <returns>true如果目錄存在 ；否則，
                                false.
                            </returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories">
      <summary>傳回目前目錄的子目錄。</summary>
      <returns>陣列
<see cref="T:System.IO.DirectoryInfo" /> 物件。
                            </returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">在封裝路徑
<see cref="T:System.IO.DirectoryInfo" />物件無效，例如未對應的磁碟上。
                                    </exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的權限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories(System.String)">
      <summary>傳回陣列中目前的目錄。
<see cref="T:System.IO.DirectoryInfo" />比對給定的搜尋準則。
                            </summary>
      <returns>型別的陣列
DirectoryInfo比對
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">要比對目錄名稱的搜尋字串。這個參數可以包含有效常值路徑與萬用 (* 和 ?) 字元 (請參閱＜備註＞) 的組合，但是不支援規則運算式。預設模式為 "*"，這會傳回所有檔案。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />包含所定義的一個或多個無效的字元
<see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法。
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />是
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">在封裝路徑
DirectoryInfo無效的物件 (例如，它是未對應的磁碟機上)。
                                    </exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的權限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories(System.String,System.IO.SearchOption)">
      <summary>傳回陣列中目前的目錄。
<see cref="T:System.IO.DirectoryInfo" />比對給定的搜尋準則，並使用值來決定是否要在搜尋子目錄。
                            </summary>
      <returns>型別的陣列
DirectoryInfo比對
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">要比對目錄名稱的搜尋字串。這個參數可以包含有效常值路徑與萬用 (* 和 ?) 字元 (請參閱＜備註＞) 的組合，但是不支援規則運算式。預設模式為 "*"，這會傳回所有檔案。</param>
      <param name="searchOption">其中一個列舉值，指定搜尋作業應該只包含目前目錄還是包含所有子目錄。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />包含所定義的一個或多個無效的字元
<see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法。
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />是
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />不是有效
<see cref="T:System.IO.SearchOption" /> 值。
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">在封裝路徑
DirectoryInfo無效的物件 (例如，它是未對應的磁碟機上)。
                                    </exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的權限。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles">
      <summary>從目前的目錄傳回檔案清單。</summary>
      <returns>型別的陣列
                                <see cref="T:System.IO.FileInfo" />.
                            </returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">路徑無效，例如位在未對應的磁碟上。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles(System.String)">
      <summary>從目前目錄傳回符合指定之搜尋模式的檔案清單。</summary>
      <returns>型別的陣列
                                <see cref="T:System.IO.FileInfo" />.
                            </returns>
      <param name="searchPattern">要比對檔案名稱的搜尋字串。這個參數可以包含有效常值路徑與萬用 (* 和 ?) 字元 (請參閱＜備註＞) 的組合，但是不支援規則運算式。預設模式為 "*"，這會傳回所有檔案。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />包含所定義的一個或多個無效的字元
<see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法。
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />是
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles(System.String,System.IO.SearchOption)">
      <summary>從目前目錄傳回符合指定搜尋模式的檔案清單，並使用值來判斷是否搜尋子目錄。</summary>
      <returns>型別的陣列
                                <see cref="T:System.IO.FileInfo" />.
                            </returns>
      <param name="searchPattern">要比對檔案名稱的搜尋字串。這個參數可以包含有效常值路徑與萬用 (* 和 ?) 字元 (請參閱＜備註＞) 的組合，但是不支援規則運算式。預設模式為 "*"，這會傳回所有檔案。</param>
      <param name="searchOption">其中一個列舉值，指定搜尋作業應該只包含目前目錄還是包含所有子目錄。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />包含所定義的一個或多個無效的字元
<see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法。
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />是
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />不是有效
<see cref="T:System.IO.SearchOption" /> 值。
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos">
      <summary>傳回陣列的強型別
<see cref="T:System.IO.FileSystemInfo" />表示所有檔案和子目錄的目錄中的項目。
                            </summary>
      <returns>陣列的強型別
<see cref="T:System.IO.FileSystemInfo" />項目。
                            </returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos(System.String)">
      <summary>擷取陣列的強型別
<see cref="T:System.IO.FileSystemInfo" />物件代表的檔案與子目錄符合指定的搜尋準則。
                            </summary>
      <returns>陣列的強型別
FileSystemInfo符合搜尋準則的物件。
                            </returns>
      <param name="searchPattern">要比對目錄及檔案名稱的搜尋字串。這個參數可以包含有效常值路徑與萬用 (* 和 ?) 字元 (請參閱＜備註＞) 的組合，但是不支援規則運算式。預設模式為 "*"，這會傳回所有檔案。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />包含所定義的一個或多個無效的字元
<see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法。
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />是
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos(System.String,System.IO.SearchOption)">
      <summary>擷取陣列
<see cref="T:System.IO.FileSystemInfo" />代表檔案和子目錄比對指定的搜尋準則的物件。
                            </summary>
      <returns>陣列，包含符合搜尋準則的檔案系統項目。</returns>
      <param name="searchPattern">要比對目錄及檔案名稱的搜尋字串。這個參數可以包含有效常值路徑與萬用 (* 和 ?) 字元 (請參閱＜備註＞) 的組合，但是不支援規則運算式。預設模式為 "*"，這會傳回所有檔案。</param>
      <param name="searchOption">其中一個列舉值，指定搜尋作業應該只包含目前目錄還是包含所有子目錄。預設值是
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />包含所定義的一個或多個無效的字元
<see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法。
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />是
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />不是有效
<see cref="T:System.IO.SearchOption" /> 值。
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.MoveTo(System.String)">
      <summary>移動
<see cref="T:System.IO.DirectoryInfo" />執行個體，其內容，以新的路徑。
                            </summary>
      <param name="destDirName">要將這個目錄移至的目的名稱和路徑。目的端不可以是另一個磁碟區，或者是名稱完全相同的目錄。它可以是您要將這個目錄加入做為子目錄的現有目錄。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destDirName" />是
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="destDirName" /> 為空字串 ("")。
                                    </exception>
      <exception cref="T:System.IO.IOException">已嘗試將目錄移動到不同磁碟區。-或-<paramref name="destDirName" /> 已存在。
                                    -或-您未取得存取此路徑的授權。-或-正在移動的目錄與目的目錄具有相同名稱。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">找不到目的目錄。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.DirectoryInfo.Name">
      <summary>取得此名稱
<see cref="T:System.IO.DirectoryInfo" />執行個體。
                            </summary>
      <returns>目錄名稱。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.DirectoryInfo.Parent">
      <summary>取得指定子目錄的父代 (Parent) 目錄。</summary>
      <returns>上層目錄中，或
null如果路徑為 null，或如果檔案路徑表示根 (例如"\"、"C:"或 *"\\server\share")。
                            </returns>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.DirectoryInfo.Root">
      <summary>取得目錄的根目錄部分。</summary>
      <returns>表示根目錄的物件。</returns>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.ToString">
      <summary>傳回已由使用者傳遞的原始路徑。</summary>
      <returns>傳回已由使用者傳遞的原始路徑。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.IO.File">
      <summary>提供建立、複製、刪除、移動和開啟單一檔案的靜態方法，並協助 <see cref="T:System.IO.FileStream" /> 物件的建立。若要浏览此类型的.NET Framework 源代码，请参阅 Reference Source。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.File.AppendAllLines(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>在檔案中加入幾行內容，然後關閉檔案。如果指定的檔案不存在，則這個方法會建立檔案，將指定的程式行寫入檔案，然後關閉檔案。</summary>
      <param name="path">要在其中加入幾行內容的檔案。如果該檔案不存在，則會建立它。</param>
      <param name="contents">要加入檔案的幾行內容。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法所定義的一個或多個無效字元。</exception>
      <exception cref="T:System.ArgumentNullException">要么<paramref name=" path " />或 <paramref name="contents" /> 是 null。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 無效 (例如，目錄不存在或位於未對應的磁碟上)。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到 <paramref name="path" /> 所指定的檔案。</exception>
      <exception cref="T:System.IO.IOException">開啟檔案時發生 I/O 錯誤。</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> 超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有寫入檔案的權限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定唯讀的檔案。-或-這個作業在目前平台不受支援。-或-<paramref name="path" /> 是目錄。</exception>
    </member>
    <member name="M:System.IO.File.AppendAllLines(System.String,System.Collections.Generic.IEnumerable{System.String},System.Text.Encoding)">
      <summary>使用指定編碼將指定的幾行內容加入檔案，然後關閉檔案。如果指定的檔案不存在，則這個方法會建立檔案，將指定的程式行寫入檔案，然後關閉檔案。</summary>
      <param name="path">要在其中加入幾行內容的檔案。如果該檔案不存在，則會建立它。</param>
      <param name="contents">要加入檔案的幾行內容。</param>
      <param name="encoding">要使用的字元編碼。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法所定義的一個或多個無效字元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name=" path" />、 <paramref name="contents" /> 或 <paramref name="encoding" /> 為 null。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 無效 (例如，目錄不存在或位於未對應的磁碟上)。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到 <paramref name="path" /> 所指定的檔案。</exception>
      <exception cref="T:System.IO.IOException">開啟檔案時發生 I/O 錯誤。</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> 超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定唯讀的檔案。-或-這個作業在目前平台不受支援。-或-<paramref name="path" /> 是目錄。-或-呼叫端沒有必要的權限。</exception>
    </member>
    <member name="M:System.IO.File.AppendAllText(System.String,System.String)">
      <summary>開啟檔案，將指定的字串附加至檔案，然後關閉檔案。如果檔案不存在，則這個方法會建立檔案，將指定的字串寫入檔案，然後關閉檔案。</summary>
      <param name="path">要將指定字串附加至其中的檔案。</param>
      <param name="contents">要附加至檔案的字串。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效 (例如，目錄不存在或位於未對應的磁碟上)。</exception>
      <exception cref="T:System.IO.IOException">開啟檔案時發生 I/O 錯誤。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定了唯讀的檔案。-或- 這個作業在目前平台不受支援。-或- <paramref name="path" /> 指定了目錄。-或- 呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.AppendAllText(System.String,System.String,System.Text.Encoding)">
      <summary>將指定字串附加至檔案，如果檔案不存在，請建立一個新檔案。</summary>
      <param name="path">要將指定字串附加至其中的檔案。</param>
      <param name="contents">要附加至檔案的字串。</param>
      <param name="encoding">要使用的字元編碼。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效 (例如，目錄不存在或位於未對應的磁碟上)。</exception>
      <exception cref="T:System.IO.IOException">開啟檔案時發生 I/O 錯誤。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定了唯讀的檔案。-或- 這個作業在目前平台不受支援。-或- <paramref name="path" /> 指定了目錄。-或- 呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.AppendText(System.String)">
      <summary>建立會將 UTF-8 編碼的文字附加至現有檔案或新檔案 (如果指定的檔案不存在) 的 <see cref="T:System.IO.StreamWriter" />。</summary>
      <returns>資料流寫入器，會附加 UTF-8 編碼的文字至指定的檔案或新檔案。</returns>
      <param name="path">要附加至檔案的路徑。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效 (例如，目錄不存在或位於未對應的磁碟上)。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Copy(System.String,System.String)">
      <summary>複製現有的檔案到新的檔案。不允許覆寫相同名稱的檔案。</summary>
      <param name="sourceFileName">要複製的檔案。</param>
      <param name="destFileName">目的檔案的名稱。這不可以是目錄或現有檔案。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> 或 <paramref name="destFileName" /> 是長度為零的字串、只含有泛空白字元，或含有一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。-或- <paramref name="sourceFileName" /> 或 <paramref name="destFileName" /> 指定目錄。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> 或 <paramref name="destFileName" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceFileName" /> 或 <paramref name="destFileName" /> 中指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="sourceFileName" />。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destFileName" /> 存在。-或- 發生 I/O 錯誤。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceFileName" /> 或 <paramref name="destFileName" /> 的格式無效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Copy(System.String,System.String,System.Boolean)">
      <summary>複製現有的檔案到新的檔案。允許覆寫相同名稱的檔案。</summary>
      <param name="sourceFileName">要複製的檔案。</param>
      <param name="destFileName">目的檔案的名稱。這不可以是目錄。</param>
      <param name="overwrite">如果可以覆寫目的檔案，則為 true，否則為 false。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的權限。-或-<paramref name="destFileName" /> 是唯讀的。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> 或 <paramref name="destFileName" /> 是長度為零的字串、只含有泛空白字元，或含有一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。-或- <paramref name="sourceFileName" /> 或 <paramref name="destFileName" /> 指定目錄。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> 或 <paramref name="destFileName" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceFileName" /> 或 <paramref name="destFileName" /> 中指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="sourceFileName" />。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destFileName" /> 存在，而且 <paramref name="overwrite" /> 是 false。-或- 發生 I/O 錯誤。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceFileName" /> 或 <paramref name="destFileName" /> 的格式無效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Create(System.String)">
      <summary>在指定的路徑中建立或覆寫檔案。</summary>
      <returns>
        <see cref="T:System.IO.FileStream" />，提供在 <paramref name="path" /> 中指定之檔案的讀取/寫入存取。</returns>
      <param name="path">要建立的檔案路徑和名稱。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的權限。-或- <paramref name="path" /> 指定了唯讀的檔案。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <exception cref="T:System.IO.IOException">建立檔案時發生 I/O 錯誤。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Create(System.String,System.Int32)">
      <summary>建立或覆寫指定檔案。</summary>
      <returns>具有指定之緩衝區大小的 <see cref="T:System.IO.FileStream" />，提供在 <paramref name="path" /> 中指定之檔案的讀取/寫入存取。</returns>
      <param name="path">檔案的檔名。</param>
      <param name="bufferSize">用來緩衝檔案的讀取和寫入的位元組數。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的權限。-或- <paramref name="path" /> 指定了唯讀的檔案。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <exception cref="T:System.IO.IOException">建立檔案時發生 I/O 錯誤。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Create(System.String,System.Int32,System.IO.FileOptions)">
      <summary>建立或覆寫指定檔案，並指定緩衝區大小以及描述如何建立或覆寫檔案的 <see cref="T:System.IO.FileOptions" /> 值。</summary>
      <returns>具有指定緩衝區大小的新檔案。</returns>
      <param name="path">檔案的檔名。</param>
      <param name="bufferSize">用來緩衝檔案的讀取和寫入的位元組數。</param>
      <param name="options">其中一個 <see cref="T:System.IO.FileOptions" /> 值，描述如何建立或覆寫檔案。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的權限。-或- <paramref name="path" /> 指定了唯讀的檔案。-或-<see cref="F:System.IO.FileOptions.Encrypted" /> 是指定給 <paramref name="options" />，且目前平台上不支援檔案加密。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <exception cref="T:System.IO.IOException">建立檔案時發生 I/O 錯誤。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的權限。-或- <paramref name="path" /> 指定了唯讀的檔案。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的權限。-或- <paramref name="path" /> 指定了唯讀的檔案。</exception>
    </member>
    <member name="M:System.IO.File.CreateText(System.String)">
      <summary>建立或開啟用以寫入 UTF-8 編碼文字的檔案。</summary>
      <returns>
        <see cref="T:System.IO.StreamWriter" />，使用 UTF-8 編碼方式寫入指定檔案。</returns>
      <param name="path">要被開啟來寫入的檔案。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Delete(System.String)">
      <summary>刪除指定的檔案。</summary>
      <param name="path">要刪除的檔案的名稱。不支援萬用字元。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <exception cref="T:System.IO.IOException">指定檔案在使用中。-或-此檔案具有開啟控制代碼，而且作業系統是 Windows XP 或先前的版本。這個開啟控制代碼可能是列舉目錄和檔案所產生。如需詳細資訊，請參閱如何：列舉目錄和檔案。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的權限。-或- 该文件是正在使用的可执行文件。-或- <paramref name="path" /> 是目錄。-或- <paramref name="path" /> 指定了唯讀的檔案。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Exists(System.String)">
      <summary>判斷指定的檔案是否存在。</summary>
      <returns>如果呼叫端具有必要的權限，而且 true 包含現有檔案的名稱，則為 <paramref name="path" />，否則為 false。如果 false 是 <paramref name="path" />、無效路徑或長度為零的字串，這個方法也會傳回 null。如果呼叫端沒有充足權限讀取指定檔案，沒有例外狀況會擲回，並且不論 false 是否存在，方法都會傳回 <paramref name="path" />。</returns>
      <param name="path">要檢查的檔案。</param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetAttributes(System.String)">
      <summary>取得路徑上檔案的 <see cref="T:System.IO.FileAttributes" />。</summary>
      <returns>路徑上檔案的 <see cref="T:System.IO.FileAttributes" />。</returns>
      <param name="path">檔案的路徑。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是空的，只含有泛空白字元，或者含有無效的字元。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" /> 表示檔案而且無效，例如，位於未對應的磁碟上，或找不到檔案。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 表示目錄而且無效，例如，位於未對應的磁碟上，或找不到目錄。</exception>
      <exception cref="T:System.IO.IOException">這個檔案正由另一個處理序使用。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的權限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetCreationTime(System.String)">
      <summary>傳回指定檔案或目錄的建立日期和時間。</summary>
      <returns>
        <see cref="T:System.DateTime" /> 結構，設定為指定檔案或目錄的建立日期和時間。這個值以本地時間表示。</returns>
      <param name="path">要取得其建立日期和時間資訊的檔案或目錄。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetCreationTimeUtc(System.String)">
      <summary>傳回指定檔案或目錄的建立日期和時間，格式為國際標準時間 (UTC)。</summary>
      <returns>
        <see cref="T:System.DateTime" /> 結構，設定為指定檔案或目錄的建立日期和時間。這個值以 UTC 時間表示。</returns>
      <param name="path">要取得其建立日期和時間資訊的檔案或目錄。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastAccessTime(System.String)">
      <summary>傳回指定檔案或目錄上次被存取的日期和時間。</summary>
      <returns>
        <see cref="T:System.DateTime" /> 結構，設定為指定檔案或目錄的上次存取日期和時間。這個值以本地時間表示。</returns>
      <param name="path">要取得其存取日期和時間資訊的檔案或目錄。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastAccessTimeUtc(System.String)">
      <summary>傳回指定檔案或目錄上次被存取的日期和時間，格式為國際標準時間 (UTC)。</summary>
      <returns>
        <see cref="T:System.DateTime" /> 結構，設定為指定檔案或目錄的上次存取日期和時間。這個值以 UTC 時間表示。</returns>
      <param name="path">要取得其存取日期和時間資訊的檔案或目錄。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastWriteTime(System.String)">
      <summary>傳回指定檔案或目錄上次被寫入的日期和時間。</summary>
      <returns>
        <see cref="T:System.DateTime" /> 結構，設定為指定檔案或目錄的上次寫入日期和時間。這個值以本地時間表示。</returns>
      <param name="path">要取得其寫入日期和時間資訊的檔案或目錄。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastWriteTimeUtc(System.String)">
      <summary>傳回指定檔案或目錄上次被寫入的日期和時間，格式為國際標準時間 (UTC)。</summary>
      <returns>
        <see cref="T:System.DateTime" /> 結構，設定為指定檔案或目錄的上次寫入日期和時間。這個值以 UTC 時間表示。</returns>
      <param name="path">要取得其寫入日期和時間資訊的檔案或目錄。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Move(System.String,System.String)">
      <summary>移動指定的檔案至新的位置，提供指定新檔名的選項。</summary>
      <param name="sourceFileName">要移動的檔案名稱。可包含相對或絕對路徑。</param>
      <param name="destFileName">檔案的新路徑和名稱。</param>
      <exception cref="T:System.IO.IOException">目的檔案已經存在。-或-<paramref name="sourceFileName" />。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> 或 <paramref name="destFileName" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> 或 <paramref name="destFileName" /> 是長度為零的字串、只含有泛空白字元，或含有一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceFileName" /> 或 <paramref name="destFileName" /> 中指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceFileName" /> 或 <paramref name="destFileName" /> 的格式無效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode)">
      <summary>在指定路徑上以讀取/寫入存取來開啟 <see cref="T:System.IO.FileStream" />。</summary>
      <returns>在指定模式和路徑中開啟的 <see cref="T:System.IO.FileStream" />，具有讀取/寫入存取而且不共用。</returns>
      <param name="path">要開啟的檔案。</param>
      <param name="mode">
        <see cref="T:System.IO.FileMode" /> 值，指定是否要建立檔案 (如果不存在的話)，以及決定要保留或覆寫現有檔案的內容。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <exception cref="T:System.IO.IOException">開啟檔案時發生 I/O 錯誤。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定了唯讀的檔案。-或- 這個作業在目前平台不受支援。-或- <paramref name="path" /> 指定了目錄。-或- 呼叫端沒有必要的權限。-或-<paramref name="mode" /> 是 <see cref="F:System.IO.FileMode.Create" />，且指定的檔案是隱藏檔案。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> 指定了無效值。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到在 <paramref name="path" /> 中指定的檔案。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode,System.IO.FileAccess)">
      <summary>在指定路徑上以指定模式和存取來開啟 <see cref="T:System.IO.FileStream" />。</summary>
      <returns>不共用的 <see cref="T:System.IO.FileStream" />，提供對指定檔案 (具有指定模式和存取) 的存取。</returns>
      <param name="path">要開啟的檔案。</param>
      <param name="mode">
        <see cref="T:System.IO.FileMode" /> 值，指定是否要建立檔案 (如果不存在的話)，以及決定要保留或覆寫現有檔案的內容。</param>
      <param name="access">
        <see cref="T:System.IO.FileAccess" /> 值，指定可以在檔案上執行的作業。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。-或- <paramref name="access" /> 指定為 Read，並且 <paramref name="mode" /> 指定為 Create、CreateNew、Truncate 或 Append。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <exception cref="T:System.IO.IOException">開啟檔案時發生 I/O 錯誤。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定為唯讀的檔案，而且 <paramref name="access" /> 不是 Read。-或- <paramref name="path" /> 指定了目錄。-或- 呼叫端沒有必要的權限。-或-<paramref name="mode" /> 是 <see cref="F:System.IO.FileMode.Create" />，且指定的檔案是隱藏檔案。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> 或 <paramref name="access" /> 指定了無效值。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到在 <paramref name="path" /> 中指定的檔案。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>在指定路徑上開啟 <see cref="T:System.IO.FileStream" />，假定它具有讀取、寫入或讀取/寫入存取的指定模式和指定的共用選項。</summary>
      <returns>指定路徑上的 <see cref="T:System.IO.FileStream" />，假定它具有讀取、寫入或讀取/寫入存取的指定模式和指定的共用選項。</returns>
      <param name="path">要開啟的檔案。</param>
      <param name="mode">
        <see cref="T:System.IO.FileMode" /> 值，指定是否要建立檔案 (如果不存在的話)，以及決定要保留或覆寫現有檔案的內容。</param>
      <param name="access">
        <see cref="T:System.IO.FileAccess" /> 值，指定可以在檔案上執行的作業。</param>
      <param name="share">
        <see cref="T:System.IO.FileShare" /> 值，指定其他執行緒對檔案擁有的存取類型。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。-或- <paramref name="access" /> 指定為 Read，並且 <paramref name="mode" /> 指定為 Create、CreateNew、Truncate 或 Append。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <exception cref="T:System.IO.IOException">開啟檔案時發生 I/O 錯誤。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定為唯讀的檔案，而且 <paramref name="access" /> 不是 Read。-或- <paramref name="path" /> 指定了目錄。-或- 呼叫端沒有必要的權限。-或-<paramref name="mode" /> 是 <see cref="F:System.IO.FileMode.Create" />，且指定的檔案是隱藏檔案。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" />、<paramref name="access" /> 或 <paramref name="share" /> 指定了無效值。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到在 <paramref name="path" /> 中指定的檔案。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.OpenRead(System.String)">
      <summary>開啟現有檔案來讀取。</summary>
      <returns>指定路徑上的唯讀 <see cref="T:System.IO.FileStream" />。</returns>
      <param name="path">要被開啟來讀取的檔案。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定了目錄。-或- 呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到在 <paramref name="path" /> 中指定的檔案。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <exception cref="T:System.IO.IOException">開啟檔案時發生 I/O 錯誤。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.OpenText(System.String)">
      <summary>開啟現有 UTF-8 編碼的文字檔來讀取。</summary>
      <returns>指定路徑上的 <see cref="T:System.IO.StreamReader" />。</returns>
      <param name="path">要被開啟來讀取的檔案。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到在 <paramref name="path" /> 中指定的檔案。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.OpenWrite(System.String)">
      <summary>開啟現有檔案或建立新檔案，以進行寫入。</summary>
      <returns>在指定之路徑上具有 <see cref="T:System.IO.FileStream" /> 存取權的非共用 <see cref="F:System.IO.FileAccess.Write" /> 物件。</returns>
      <param name="path">要被開啟來寫入的檔案。</param>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的權限。-或- <paramref name="path" /> 指定為唯讀檔案或目錄。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllBytes(System.String)">
      <summary>開啟二進位檔案，將檔案內容讀入位元組陣列，然後關閉檔案。</summary>
      <returns>包含檔案內容的位元組陣列。</returns>
      <param name="path">要開啟用於讀取的檔案。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <exception cref="T:System.IO.IOException">開啟檔案時發生 I/O 錯誤。</exception>
      <exception cref="T:System.UnauthorizedAccessException">這個作業在目前平台不受支援。-或- <paramref name="path" /> 指定了目錄。-或- 呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到在 <paramref name="path" /> 中指定的檔案。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllLines(System.String)">
      <summary>開啟文字檔，讀取檔案的所有行，然後關閉檔案。</summary>
      <returns>包含檔案所有行的字串陣列。</returns>
      <param name="path">要開啟用於讀取的檔案。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <exception cref="T:System.IO.IOException">開啟檔案時發生 I/O 錯誤。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定了唯讀的檔案。-或- 這個作業在目前平台不受支援。-或- <paramref name="path" /> 指定了目錄。-或- 呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到在 <paramref name="path" /> 中指定的檔案。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllLines(System.String,System.Text.Encoding)">
      <summary>開啟檔案，以指定的編碼方式讀取檔案的所有行，然後關閉檔案。</summary>
      <returns>包含檔案所有行的字串陣列。</returns>
      <param name="path">要開啟用於讀取的檔案。</param>
      <param name="encoding">套用至檔案內容的編碼方式。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <exception cref="T:System.IO.IOException">開啟檔案時發生 I/O 錯誤。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定了唯讀的檔案。-或- 這個作業在目前平台不受支援。-或- <paramref name="path" /> 指定了目錄。-或- 呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到在 <paramref name="path" /> 中指定的檔案。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllText(System.String)">
      <summary>開啟文字檔，讀取檔案的所有行，然後關閉檔案。</summary>
      <returns>包含檔案所有行的字串。</returns>
      <param name="path">要開啟用於讀取的檔案。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <exception cref="T:System.IO.IOException">開啟檔案時發生 I/O 錯誤。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定了唯讀的檔案。-或- 這個作業在目前平台不受支援。-或- <paramref name="path" /> 指定了目錄。-或- 呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到在 <paramref name="path" /> 中指定的檔案。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllText(System.String,System.Text.Encoding)">
      <summary>開啟檔案，以指定的編碼方式讀取檔案的所有行，然後關閉檔案。</summary>
      <returns>包含檔案所有行的字串。</returns>
      <param name="path">要開啟用於讀取的檔案。</param>
      <param name="encoding">套用至檔案內容的編碼方式。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <exception cref="T:System.IO.IOException">開啟檔案時發生 I/O 錯誤。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定了唯讀的檔案。-或- 這個作業在目前平台不受支援。-或- <paramref name="path" /> 指定了目錄。-或- 呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到在 <paramref name="path" /> 中指定的檔案。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadLines(System.String)">
      <summary>讀取檔案的所有行。</summary>
      <returns>檔案的所有行，或查詢結果的各行。</returns>
      <param name="path">要讀取的檔案。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白，或者包含由定义的一个或多个无效字符 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 無效 (例如，位於未對應的磁碟機上)。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到 <paramref name="path" /> 所指定的檔案。</exception>
      <exception cref="T:System.IO.IOException">開啟檔案時發生 I/O 錯誤。</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> 超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定唯讀的檔案。-或-這個作業在目前平台不受支援。-或-<paramref name="path" /> 是目錄。-或-呼叫端沒有必要的權限。</exception>
    </member>
    <member name="M:System.IO.File.ReadLines(System.String,System.Text.Encoding)">
      <summary>讀取具有指定編碼方式的檔案所有行。</summary>
      <returns>檔案的所有行，或查詢結果的各行。</returns>
      <param name="path">要讀取的檔案。</param>
      <param name="encoding">套用至檔案內容的編碼方式。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法所定義的一個或多個無效字元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 無效 (例如，位於未對應的磁碟機上)。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到 <paramref name="path" /> 所指定的檔案。</exception>
      <exception cref="T:System.IO.IOException">開啟檔案時發生 I/O 錯誤。</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> 超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定唯讀的檔案。-或-這個作業在目前平台不受支援。-或-<paramref name="path" /> 是目錄。-或-呼叫端沒有必要的權限。</exception>
    </member>
    <member name="M:System.IO.File.SetAttributes(System.String,System.IO.FileAttributes)">
      <summary>在指定路徑上設定檔案的指定 <see cref="T:System.IO.FileAttributes" />。</summary>
      <param name="path">檔案的路徑。</param>
      <param name="fileAttributes">列舉值的位元組合。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是空白的、只包含泛空白字元、包含無效字元，或檔案屬性無效。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到檔案。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定了唯讀的檔案。-或- 這個作業在目前平台不受支援。-或- <paramref name="path" /> 指定了目錄。-或- 呼叫端沒有必要的權限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetCreationTime(System.String,System.DateTime)">
      <summary>設定檔案建立的日期和時間。</summary>
      <param name="path">要設定其建立日期和時間資訊的檔案。</param>
      <param name="creationTime">
        <see cref="T:System.DateTime" />，包含要為 <paramref name="path" /> 的建立日期和時間設定的值。這個值以本地時間表示。</param>
      <exception cref="T:System.IO.FileNotFoundException">找不到指定的路徑。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.IO.IOException">執行作業時發生 I/O 錯誤。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> 指定這個作業允許的日期、時間或兩者範圍之外的值。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetCreationTimeUtc(System.String,System.DateTime)">
      <summary>設定檔案建立的日期和時間，格式為國際標準時間 (UTC)。</summary>
      <param name="path">要設定其建立日期和時間資訊的檔案。</param>
      <param name="creationTimeUtc">
        <see cref="T:System.DateTime" />，包含要為 <paramref name="path" /> 的建立日期和時間設定的值。這個值以 UTC 時間表示。</param>
      <exception cref="T:System.IO.FileNotFoundException">找不到指定的路徑。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.IO.IOException">執行作業時發生 I/O 錯誤。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> 指定這個作業允許的日期、時間或兩者範圍之外的值。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastAccessTime(System.String,System.DateTime)">
      <summary>取得指定檔案上一次被存取的日期和時間。</summary>
      <param name="path">要設定其存取日期和時間資訊的檔案。</param>
      <param name="lastAccessTime">
        <see cref="T:System.DateTime" />，包含要為 <paramref name="path" /> 的上次存取日期和時間設定的值。這個值以本地時間表示。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到指定的路徑。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTime" /> 指定這個作業允許的日期或時間範圍以外的值。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastAccessTimeUtc(System.String,System.DateTime)">
      <summary>設定指定檔案上次被存取的日期和時間，格式為國際標準時間 (UTC)。</summary>
      <param name="path">要設定其存取日期和時間資訊的檔案。</param>
      <param name="lastAccessTimeUtc">
        <see cref="T:System.DateTime" />，包含要為 <paramref name="path" /> 的上次存取日期和時間設定的值。這個值以 UTC 時間表示。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到指定的路徑。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTimeUtc" /> 指定這個作業允許的日期或時間範圍以外的值。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastWriteTime(System.String,System.DateTime)">
      <summary>設定指定檔案上次被寫入的日期和時間。</summary>
      <param name="path">要設定其日期和時間資訊的檔案。</param>
      <param name="lastWriteTime">
        <see cref="T:System.DateTime" />，包含要為 <paramref name="path" /> 的上次寫入日期和時間設定的值。這個值以本地時間表示。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到指定的路徑。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTime" /> 指定這個作業允許的日期或時間範圍以外的值。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastWriteTimeUtc(System.String,System.DateTime)">
      <summary>設定指定檔案上次寫入的日期和時間，格式為國際標準時間 (UTC)。</summary>
      <param name="path">要設定其日期和時間資訊的檔案。</param>
      <param name="lastWriteTimeUtc">
        <see cref="T:System.DateTime" />，包含要為 <paramref name="path" /> 的上次寫入日期和時間設定的值。這個值以 UTC 時間表示。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到指定的路徑。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTimeUtc" /> 指定這個作業允許的日期或時間範圍以外的值。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.WriteAllBytes(System.String,System.Byte[])">
      <summary>建立新檔案，將指定的位元組陣列寫入檔案，然後關閉檔案。如果檔案已經存在，則會覆寫該檔案。</summary>
      <param name="path">要寫入其中的檔案。</param>
      <param name="bytes">要寫入檔案的位元組。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null，或位元組陣列為空。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <exception cref="T:System.IO.IOException">開啟檔案時發生 I/O 錯誤。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定了唯讀的檔案。-或- 這個作業在目前平台不受支援。-或- <paramref name="path" /> 指定了目錄。-或- 呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.WriteAllLines(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>建立新檔案，並於檔案中寫入字串的集合，然後關閉檔案。</summary>
      <param name="path">要寫入其中的檔案。</param>
      <param name="contents">要寫入檔案的幾行內容。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白，或者包含由定义的一个或多个无效字符 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法。</exception>
      <exception cref="T:System.ArgumentNullException">要么<paramref name=" path " />或 <paramref name="contents" /> 是 null。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 無效 (例如，位於未對應的磁碟機上)。</exception>
      <exception cref="T:System.IO.IOException">開啟檔案時發生 I/O 錯誤。</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> 超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定唯讀的檔案。-或-這個作業在目前平台不受支援。-或-<paramref name="path" /> 是目錄。-或-呼叫端沒有必要的權限。</exception>
    </member>
    <member name="M:System.IO.File.WriteAllLines(System.String,System.Collections.Generic.IEnumerable{System.String},System.Text.Encoding)">
      <summary>使用指定編碼方式建立新檔案，並將字串的集合寫入檔案，然後關閉檔案。</summary>
      <param name="path">要寫入其中的檔案。</param>
      <param name="contents">要寫入檔案的幾行內容。</param>
      <param name="encoding">要使用的字元編碼。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白，或者包含由定义的一个或多个无效字符 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法。</exception>
      <exception cref="T:System.ArgumentNullException">要么<paramref name=" path" />,，<paramref name=" contents" />, ，或 <paramref name="encoding" /> 是 null。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 無效 (例如，位於未對應的磁碟機上)。</exception>
      <exception cref="T:System.IO.IOException">開啟檔案時發生 I/O 錯誤。</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> 超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 個字元，而檔案名稱必須小於 260 個字元。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定唯讀的檔案。-或-這個作業在目前平台不受支援。-或-<paramref name="path" /> 是目錄。-或-呼叫端沒有必要的權限。</exception>
    </member>
    <member name="M:System.IO.File.WriteAllText(System.String,System.String)">
      <summary>建立新檔案，將指定的字串寫入檔案，然後關閉檔案。如果檔案已經存在，則會覆寫該檔案。</summary>
      <param name="path">要寫入其中的檔案。</param>
      <param name="contents">要寫入檔案的字串。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 是 null 或 <paramref name="contents" /> 是空的。 </exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <exception cref="T:System.IO.IOException">開啟檔案時發生 I/O 錯誤。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定了唯讀的檔案。-或- 這個作業在目前平台不受支援。-或- <paramref name="path" /> 指定了目錄。-或- 呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.WriteAllText(System.String,System.String,System.Text.Encoding)">
      <summary>建立新檔案，使用指定的編碼方式將指定的字串寫入檔案，然後關閉檔案。如果檔案已經存在，則會覆寫該檔案。</summary>
      <param name="path">要寫入其中的檔案。</param>
      <param name="contents">要寫入檔案的字串。</param>
      <param name="encoding">要套用至字串的編碼方式。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含空白字元，或包含一個或多個無效的字元 (如 <see cref="F:System.IO.Path.InvalidPathChars" /> 所定義)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 是 null 或 <paramref name="contents" /> 是空的。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效 (例如，位於未對應的磁碟上)。</exception>
      <exception cref="T:System.IO.IOException">開啟檔案時發生 I/O 錯誤。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定了唯讀的檔案。-或- 這個作業在目前平台不受支援。-或- <paramref name="path" /> 指定了目錄。-或- 呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式無效。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.IO.FileInfo">
      <summary>提供建立、複製、刪除、移動和開啟檔案的屬性和執行個體方法，並協助建立 <see cref="T:System.IO.FileStream" /> 物件。此類別無法被繼承。若要浏览此类型的.NET Framework 源代码，请参阅 Reference Source。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileInfo.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.IO.FileInfo" /> 類別的新執行個體，以做為檔案路徑的包裝函式。</summary>
      <param name="fileName">新檔案的完整名稱，或者相對的檔名。不要以目錄分隔符號字元做為路徑的結尾。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="fileName" /> 為 null。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.ArgumentException">檔案名稱是空的，只含有泛空白字元 (White Space)，或者含有無效的字元。</exception>
      <exception cref="T:System.UnauthorizedAccessException">存取 <paramref name="fileName" /> 遭拒。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="fileName" /> 在字串的中間包含一個冒號 (:)。</exception>
    </member>
    <member name="M:System.IO.FileInfo.AppendText">
      <summary>建立將文字附加在 <see cref="T:System.IO.StreamWriter" /> 的這個執行個體所表示之檔案的 <see cref="T:System.IO.FileInfo" />。</summary>
      <returns>新的 StreamWriter。</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.CopyTo(System.String)">
      <summary>複製現有的檔案到新的檔案，不允許覆寫現有的檔案。</summary>
      <returns>有完整路徑的新檔案。</returns>
      <param name="destFileName">要複製的目標新檔案名稱。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destFileName" /> 是空的，只含有泛空白字元，或者含有無效的字元。</exception>
      <exception cref="T:System.IO.IOException">發生錯誤，或者目的檔案已經存在。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destFileName" /> 為 null。</exception>
      <exception cref="T:System.UnauthorizedAccessException">傳入目錄路徑，或者檔案正要移至不同的磁碟。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="destFileName" /> 中指定的目錄不存在。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destFileName" /> 的字串包含冒號 (:)，但是未指定磁碟區。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.CopyTo(System.String,System.Boolean)">
      <summary>複製現有的檔案到新檔案，允許覆寫現有的檔案。</summary>
      <returns>新檔案，或現有檔案的覆寫 (如果 <paramref name="overwrite" /> 為 true)。如果檔案存在而且 <paramref name="overwrite" /> 為 false，則會擲回 <see cref="T:System.IO.IOException" />。</returns>
      <param name="destFileName">要複製的目標新檔案名稱。</param>
      <param name="overwrite">若允許覆寫現有檔案，則為 true，否則為 false。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destFileName" /> 是空的，只含有泛空白字元，或者含有無效的字元。</exception>
      <exception cref="T:System.IO.IOException">發生錯誤，或者目的檔案已經存在，而且 <paramref name="overwrite" /> 為 false。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destFileName" /> 為 null。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="destFileName" /> 中指定的目錄不存在。</exception>
      <exception cref="T:System.UnauthorizedAccessException">傳入目錄路徑，或者檔案正要移至不同的磁碟。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destFileName" /> 在字串的中間包含一個冒號 (:)。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Create">
      <summary>建立檔案。</summary>
      <returns>新檔案。</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.CreateText">
      <summary>建立寫入新文字檔的 <see cref="T:System.IO.StreamWriter" />。</summary>
      <returns>新的 StreamWriter。</returns>
      <exception cref="T:System.UnauthorizedAccessException">檔案名稱是個目錄。</exception>
      <exception cref="T:System.IO.IOException">磁碟是唯讀的。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Delete">
      <summary>永遠地刪除檔案。</summary>
      <exception cref="T:System.IO.IOException">目標檔案是開啟的，或者記憶體映射的電腦執行的是 Microsoft Windows NT。-或-此檔案具有開啟控制代碼，而且作業系統是 Windows XP 或先前的版本。這個開啟控制代碼可能是列舉目錄和檔案所產生。如需詳細資訊，請參閱如何：列舉目錄和檔案。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">路徑是個目錄。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.Directory">
      <summary>取得父代 (Parent) 目錄的執行個體。</summary>
      <returns>
        <see cref="T:System.IO.DirectoryInfo" />物件，代表這個檔案的父代目錄。</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效，例如位在未對應的磁碟上。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.DirectoryName">
      <summary>取得表示目錄完整路徑的字串。</summary>
      <returns>字串，表示目錄的完整路徑。</returns>
      <exception cref="T:System.ArgumentNullException">為目錄名稱傳入 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">完整路徑是 260 個以上的字元。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.Exists">
      <summary>取得值，這個值指出檔案是否存在。</summary>
      <returns>如果檔案存在，則為 true；如果檔案不存在或者檔案是目錄，則為 false。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileInfo.IsReadOnly">
      <summary>取得或設定值，判斷目前檔案是否為唯讀。</summary>
      <returns>如果目前檔案為唯讀，則為 true，否則為 false。</returns>
      <exception cref="T:System.IO.FileNotFoundException">找不到目前 <see cref="T:System.IO.FileInfo" /> 物件所描述的檔案。</exception>
      <exception cref="T:System.IO.IOException">開啟檔案時發生 I/O 錯誤。</exception>
      <exception cref="T:System.UnauthorizedAccessException">這個作業在目前平台不受支援。-或- 呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.ArgumentException">使用者沒有寫入權限，卻嘗試將這個屬性設定為false。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileInfo.Length">
      <summary>取得目前檔案的大小，以位元組為單位。</summary>
      <returns>目前檔案的大小，以位元組為單位。</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> 不能更新檔案或目錄的狀態。</exception>
      <exception cref="T:System.IO.FileNotFoundException">檔案不存在。-或- 呼叫目錄的 Length 屬性。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileInfo.MoveTo(System.String)">
      <summary>移動指定的檔案至新的位置，提供指定新檔名的選項。</summary>
      <param name="destFileName">要將檔案移至的路徑，可以指定不同的檔名。</param>
      <exception cref="T:System.IO.IOException">發生 I/O 錯誤，例如目的檔案已經存在，或者目的裝置未就緒。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destFileName" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="destFileName" /> 是空的，只含有泛空白字元，或者含有無效的字元。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="destFileName" /> 是唯讀的或者是目錄。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到檔案。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效，例如位在未對應的磁碟上。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destFileName" /> 在字串的中間包含一個冒號 (:)。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.Name">
      <summary>取得檔案的名稱。</summary>
      <returns>檔案的檔名。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode)">
      <summary>使用指定模式來開啟檔案。</summary>
      <returns>以指定模式開啟的檔案，其使用讀取/寫入存取且為不共用。</returns>
      <param name="mode">
        <see cref="T:System.IO.FileMode" /> 常數，指定用於開啟檔案的模式 (例如，Open 或 Append)。</param>
      <exception cref="T:System.IO.FileNotFoundException">找不到檔案。</exception>
      <exception cref="T:System.UnauthorizedAccessException">檔案是唯讀的或者是目錄。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效，例如位在未對應的磁碟上。</exception>
      <exception cref="T:System.IO.IOException">檔案已經開啟。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode,System.IO.FileAccess)">
      <summary>使用讀取、寫入或讀取/寫入存取，並以指定模式來開啟檔案。</summary>
      <returns>
        <see cref="T:System.IO.FileStream" /> 物件，開啟於指定模式和存取中，且為不共用。</returns>
      <param name="mode">
        <see cref="T:System.IO.FileMode" /> 常數，指定用於開啟檔案的模式 (例如，Open 或 Append)。</param>
      <param name="access">
        <see cref="T:System.IO.FileAccess" /> 常數，指定是否以 Read、Write 或 ReadWrite 檔案存取來開啟檔案。</param>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到檔案。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 是唯讀的或者是目錄。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效，例如位在未對應的磁碟上。</exception>
      <exception cref="T:System.IO.IOException">檔案已經開啟。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>使用讀取、寫入或讀取/寫入存取和指定的共用選項，將檔案開啟於指定模式中。</summary>
      <returns>
        <see cref="T:System.IO.FileStream" /> 物件，以指定的模式、存取和共用選項開啟。</returns>
      <param name="mode">
        <see cref="T:System.IO.FileMode" /> 常數，指定用於開啟檔案的模式 (例如，Open 或 Append)。</param>
      <param name="access">
        <see cref="T:System.IO.FileAccess" /> 常數，指定是否以 Read、Write 或 ReadWrite 檔案存取來開啟檔案。</param>
      <param name="share">
        <see cref="T:System.IO.FileShare" /> 常數，指定其他 FileStream 物件對這個檔案的存取類型。</param>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到檔案。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 是唯讀的或者是目錄。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效，例如位在未對應的磁碟上。</exception>
      <exception cref="T:System.IO.IOException">檔案已經開啟。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.OpenRead">
      <summary>建立唯讀的 <see cref="T:System.IO.FileStream" />。</summary>
      <returns>新的唯讀 <see cref="T:System.IO.FileStream" /> 物件。</returns>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 是唯讀的或者是目錄。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效，例如位在未對應的磁碟上。</exception>
      <exception cref="T:System.IO.IOException">檔案已經開啟。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.OpenText">
      <summary>建立從現有文字檔讀取的 <see cref="T:System.IO.StreamReader" /> (具有 UTF8 編碼方式)。</summary>
      <returns>具有 UTF8 編碼方式的新 StreamReader。</returns>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到檔案。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 是唯讀的或者是目錄。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效，例如位在未對應的磁碟上。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.OpenWrite">
      <summary>建立唯寫的 <see cref="T:System.IO.FileStream" />。</summary>
      <returns>新檔案或現有檔案的唯寫非共用 <see cref="T:System.IO.FileStream" /> 物件。</returns>
      <exception cref="T:System.UnauthorizedAccessException">在建立 <see cref="T:System.IO.FileInfo" /> 物件的執行個體時所指定的路徑唯讀的或是目錄。 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">在建立 <see cref="T:System.IO.FileInfo" /> 物件的執行個體時所指定的路徑無效，例如位在未對應的磁碟機上。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.ToString">
      <summary>將路徑當做字串傳回。</summary>
      <returns>表示路徑的字串。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.FileOptions">
      <summary>表示用於建立 <see cref="T:System.IO.FileStream" /> 物件的進階選項。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.FileOptions.Asynchronous">
      <summary>表示檔案可用於非同步讀取和寫入。</summary>
    </member>
    <member name="F:System.IO.FileOptions.DeleteOnClose">
      <summary>表示檔案不再使用時會自動刪除。</summary>
    </member>
    <member name="F:System.IO.FileOptions.Encrypted">
      <summary>表示檔案已加密，而且只能使用相同的加密使用者帳戶才能解密。</summary>
    </member>
    <member name="F:System.IO.FileOptions.None">
      <summary>表示建立 <see cref="T:System.IO.FileStream" /> 物件時，不應使用任何其他選項。</summary>
    </member>
    <member name="F:System.IO.FileOptions.RandomAccess">
      <summary>表示檔案是隨機存取的。系統可使用這個做為最佳化檔案快取的提示。</summary>
    </member>
    <member name="F:System.IO.FileOptions.SequentialScan">
      <summary>表示檔案是按順序從開頭至結尾依序存取。系統可使用這個做為最佳化檔案快取的提示。如果應用程式藉移動檔案指標來進行隨機存取，則可能不會發生最佳快取；然而，仍然保證正確的作業。</summary>
    </member>
    <member name="F:System.IO.FileOptions.WriteThrough">
      <summary>表示系統應透過中繼快取直接寫入磁碟。</summary>
    </member>
    <member name="T:System.IO.FileStream">
      <summary>為檔案提供 <see cref="T:System.IO.Stream" />，同時支援同步與非同步讀取和寫入作業。若要浏览此类型的.NET Framework 源代码，请参阅 Reference Source。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.#ctor(Microsoft.Win32.SafeHandles.SafeFileHandle,System.IO.FileAccess)">
      <summary>使用指定的讀取/寫入權限，初始化指定檔案控制代碼之 <see cref="T:System.IO.FileStream" /> 類別的新執行個體。</summary>
      <param name="handle">目前 FileStream 物件將會封裝之檔案的檔案控制代碼。</param>
      <param name="access">常數，設定 FileStream 物件的 <see cref="P:System.IO.FileStream.CanRead" /> 與 <see cref="P:System.IO.FileStream.CanWrite" /> 屬性。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="access" /> 不是 <see cref="T:System.IO.FileAccess" /> 的欄位。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.IO.IOException">發生 I/O 錯誤，例如磁碟錯誤。-或-已關閉資料流。</exception>
      <exception cref="T:System.UnauthorizedAccessException">指定之檔案控制代碼的作業系統不允許所要求的 <paramref name="access" />，例如，當 <paramref name="access" /> 為 Write 或 ReadWrite，而檔案控制代碼設定成唯讀存取時。</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(Microsoft.Win32.SafeHandles.SafeFileHandle,System.IO.FileAccess,System.Int32)">
      <summary>使用指定的讀取/寫入權限和緩衝區大小，初始化指定的檔案控制代碼之 <see cref="T:System.IO.FileStream" /> 類別的新執行個體。</summary>
      <param name="handle">目前 FileStream 物件將會封裝之檔案的檔案控制代碼。</param>
      <param name="access">
        <see cref="T:System.IO.FileAccess" /> 常數，它會設定 FileStream 物件的 <see cref="P:System.IO.FileStream.CanRead" /> 與 <see cref="P:System.IO.FileStream.CanWrite" /> 屬性。</param>
      <param name="bufferSize">大於 0 的 <see cref="T:System.Int32" /> 正值，指出緩衝區大小。預設緩衝區大小為 4096。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="handle" /> 參數是無效的控制代碼。-或-<paramref name="handle" /> 參數是同步控制代碼，且已非同步使用。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> 參數為負數。</exception>
      <exception cref="T:System.IO.IOException">發生 I/O 錯誤，例如磁碟錯誤。-或-已關閉資料流。 </exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">指定之檔案控制代碼的作業系統不允許所要求的 <paramref name="access" />，例如，當 <paramref name="access" /> 為 Write 或 ReadWrite，而檔案控制代碼設定成唯讀存取時。</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(Microsoft.Win32.SafeHandles.SafeFileHandle,System.IO.FileAccess,System.Int32,System.Boolean)">
      <summary>使用指定的讀取/寫入權限、緩衝區大小和同步或非同步狀態，初始化指定的檔案控制代碼之 <see cref="T:System.IO.FileStream" /> 類別的新執行個體。</summary>
      <param name="handle">這個 FileStream 物件將會封裝之檔案的檔案控制代碼。</param>
      <param name="access">常數，設定 FileStream 物件的 <see cref="P:System.IO.FileStream.CanRead" /> 與 <see cref="P:System.IO.FileStream.CanWrite" /> 屬性。</param>
      <param name="bufferSize">大於 0 的 <see cref="T:System.Int32" /> 正值，指出緩衝區大小。預設緩衝區大小為 4096。</param>
      <param name="isAsync">如果控制代碼為非同步開啟 (也就是，在重疊 I/O 模式)，則為 true；否則為 false。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="handle" /> 參數是無效的控制代碼。-或-<paramref name="handle" /> 參數是同步控制代碼，且已非同步使用。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> 參數為負數。</exception>
      <exception cref="T:System.IO.IOException">發生 I/O 錯誤，例如磁碟錯誤。-或-已關閉資料流。 </exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">指定之檔案控制代碼的作業系統不允許所要求的 <paramref name="access" />，例如，當 <paramref name="access" /> 為 Write 或 ReadWrite，而檔案控制代碼設定成唯讀存取時。</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode)">
      <summary>使用指定的路徑和建立模式初始化 <see cref="T:System.IO.FileStream" /> 類別的新執行個體。</summary>
      <param name="path">目前 FileStream 物件將會封裝之檔案的相對或絕對路徑。</param>
      <param name="mode">常數，決定如何開啟或建立檔案。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 為空字串 ("")、只包含泛空白字元，或包含一個或多個無效的字元。-或-<paramref name="path" /> 指非文件设备，如"con："、"com1："，"lpt1:"，等等。在 NTFS 环境中。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 指非文件设备，如"con："、"com1："，"lpt1:"，等等。在非 NTFS 环境中。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.IO.FileNotFoundException">無法找到檔案，例如，當 <paramref name="mode" /> 為 FileMode.Truncate 或 FileMode.Open，而且 <paramref name="path" /> 指定的檔案不存在的時候。檔案必須已在這些模式中存在。</exception>
      <exception cref="T:System.IO.IOException">發生 I/O 錯誤，例如在 FileMode.CreateNew 指定的檔案已經存在時指定 <paramref name="path" />。-或-已關閉資料流。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效，例如位在未對應的磁碟上。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> 包含無效的值。</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess)">
      <summary>使用指定路徑、建立模式和讀取/寫入權限，初始化 <see cref="T:System.IO.FileStream" /> 類別的新執行個體。</summary>
      <param name="path">目前 FileStream 物件將會封裝之檔案的相對或絕對路徑。</param>
      <param name="mode">常數，決定如何開啟或建立檔案。</param>
      <param name="access">常數，決定 FileStream 物件如何存取檔案。這也可以判斷 FileStream 物件之 <see cref="P:System.IO.FileStream.CanRead" /> 與 <see cref="P:System.IO.FileStream.CanWrite" /> 屬性傳回的值。如果 <paramref name="path" /> 指定了磁碟檔案，則 <see cref="P:System.IO.FileStream.CanSeek" /> 為 true。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 為空字串 ("")、只包含泛空白字元，或包含一個或多個無效的字元。-或-<paramref name="path" /> 指非文件设备，如"con："、"com1："，"lpt1:"，等等。在 NTFS 环境中。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 指非文件设备，如"con："、"com1："，"lpt1:"，等等。在非 NTFS 环境中。</exception>
      <exception cref="T:System.IO.FileNotFoundException">無法找到檔案，例如，當 <paramref name="mode" /> 為 FileMode.Truncate 或 FileMode.Open，而且 <paramref name="path" /> 指定的檔案不存在的時候。檔案必須已在這些模式中存在。</exception>
      <exception cref="T:System.IO.IOException">發生 I/O 錯誤，例如在 FileMode.CreateNew 指定的檔案已經存在時指定 <paramref name="path" />。-或-已關閉資料流。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效，例如位在未對應的磁碟上。</exception>
      <exception cref="T:System.UnauthorizedAccessException">指定之 <paramref name="access" /> 的作業系統不允許所要求的 <paramref name="path" />，例如，當 <paramref name="access" /> 為 Write 或 ReadWrite，且檔案或目錄設定為唯讀存取時。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> 包含無效的值。</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>使用指定路徑、建立模式、讀取/寫入權限和共用權限，初始化 <see cref="T:System.IO.FileStream" /> 類別的新執行個體。</summary>
      <param name="path">目前 FileStream 物件將會封裝之檔案的相對或絕對路徑。</param>
      <param name="mode">常數，決定如何開啟或建立檔案。</param>
      <param name="access">常數，決定 FileStream 物件如何存取檔案。這也可以判斷 FileStream 物件之 <see cref="P:System.IO.FileStream.CanRead" /> 與 <see cref="P:System.IO.FileStream.CanWrite" /> 屬性傳回的值。如果 <paramref name="path" /> 指定了磁碟檔案，則 <see cref="P:System.IO.FileStream.CanSeek" /> 為 true。</param>
      <param name="share">常數，決定處理程序如何共用檔案。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 為空字串 ("")、只包含泛空白字元，或包含一個或多個無效的字元。-或-<paramref name="path" /> 指非文件设备，如"con："、"com1："，"lpt1:"，等等。在 NTFS 环境中。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 指非文件设备，如"con："、"com1："，"lpt1:"，等等。在非 NTFS 环境中。</exception>
      <exception cref="T:System.IO.FileNotFoundException">無法找到檔案，例如，當 <paramref name="mode" /> 為 FileMode.Truncate 或 FileMode.Open，而且 <paramref name="path" /> 指定的檔案不存在的時候。檔案必須已在這些模式中存在。</exception>
      <exception cref="T:System.IO.IOException">發生 I/O 錯誤，例如在 FileMode.CreateNew 指定的檔案已經存在時指定 <paramref name="path" />。-或-系統在執行 Windows 98 或 Windows 98 SE，且 <paramref name="share" /> 設定為 FileShare.Delete。-或-已關閉資料流。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效，例如位在未對應的磁碟上。</exception>
      <exception cref="T:System.UnauthorizedAccessException">指定之 <paramref name="access" /> 的作業系統不允許所要求的 <paramref name="path" />，例如，當 <paramref name="access" /> 為 Write 或 ReadWrite，且檔案或目錄設定為唯讀存取時。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> 包含無效的值。</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Int32)">
      <summary>使用指定路徑、建立模式、讀取/寫入與共用權限與緩衝區大小，初始化 <see cref="T:System.IO.FileStream" /> 類別的新執行個體。</summary>
      <param name="path">目前 FileStream 物件將會封裝之檔案的相對或絕對路徑。</param>
      <param name="mode">常數，決定如何開啟或建立檔案。</param>
      <param name="access">常數，決定 FileStream 物件如何存取檔案。這也可以判斷 FileStream 物件之 <see cref="P:System.IO.FileStream.CanRead" /> 與 <see cref="P:System.IO.FileStream.CanWrite" /> 屬性傳回的值。如果 <paramref name="path" /> 指定了磁碟檔案，則 <see cref="P:System.IO.FileStream.CanSeek" /> 為 true。</param>
      <param name="share">常數，決定處理程序如何共用檔案。</param>
      <param name="bufferSize">大於 0 的 <see cref="T:System.Int32" /> 正值，指出緩衝區大小。預設緩衝區大小為 4096。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 為空字串 ("")、只包含泛空白字元，或包含一個或多個無效的字元。-或-<paramref name="path" /> 指非文件设备，如"con："、"com1："，"lpt1:"，等等。在 NTFS 环境中。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 指非文件设备，如"con："、"com1："，"lpt1:"，等等。在非 NTFS 环境中。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> 為負值或零。-或- <paramref name="mode" />、<paramref name="access" /> 或 <paramref name="share" /> 包含無效值。</exception>
      <exception cref="T:System.IO.FileNotFoundException">無法找到檔案，例如，當 <paramref name="mode" /> 為 FileMode.Truncate 或 FileMode.Open，而且 <paramref name="path" /> 指定的檔案不存在的時候。檔案必須已在這些模式中存在。</exception>
      <exception cref="T:System.IO.IOException">發生 I/O 錯誤，例如在 FileMode.CreateNew 指定的檔案已經存在時指定 <paramref name="path" />。-或-系統在執行 Windows 98 或 Windows 98 SE，且 <paramref name="share" /> 設定為 FileShare.Delete。-或-已關閉資料流。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效，例如位在未對應的磁碟上。</exception>
      <exception cref="T:System.UnauthorizedAccessException">指定之 <paramref name="access" /> 的作業系統不允許所要求的 <paramref name="path" />，例如，當 <paramref name="access" /> 為 Write 或 ReadWrite，且檔案或目錄設定為唯讀存取時。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Int32,System.Boolean)">
      <summary>使用指定路徑、建立模式、讀取/寫入與共用權限、緩衝大小與同步或非同步狀態，初始化 <see cref="T:System.IO.FileStream" /> 類別的新執行個體。</summary>
      <param name="path">目前 FileStream 物件將會封裝之檔案的相對或絕對路徑。</param>
      <param name="mode">常數，決定如何開啟或建立檔案。</param>
      <param name="access">常數，決定 FileStream 物件如何存取檔案。這也可以判斷 FileStream 物件之 <see cref="P:System.IO.FileStream.CanRead" /> 與 <see cref="P:System.IO.FileStream.CanWrite" /> 屬性傳回的值。如果 <paramref name="path" /> 指定了磁碟檔案，則 <see cref="P:System.IO.FileStream.CanSeek" /> 為 true。</param>
      <param name="share">常數，決定處理程序如何共用檔案。</param>
      <param name="bufferSize">大於 0 的 <see cref="T:System.Int32" /> 正值，指出緩衝區大小。預設緩衝區大小為 4096。</param>
      <param name="useAsync">指定要使用非同步 I/O 或同步 I/O。但是，請注意：基礎作業系統可能並不支援非同步 I/O，所以在指定 true 時，可能會視平台以同步方式開啟控制代碼。在以非同步方式開啟時，<see cref="M:System.IO.FileStream.BeginRead(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)" /> 與 <see cref="M:System.IO.FileStream.BeginWrite(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)" /> 方法對大量讀取或寫入的作業會有較佳的效能，但對於少量讀取和寫入的作業卻會比較慢。如果要將應用程式設計成使用非同步 I/O，請將 <paramref name="useAsync" /> 參數設定為 true。正確地使用非同步 I/O，可以讓應用程式的執行快上十倍，但如果沒有配合非同步 I/O 的需求重新設計應用程式，卻會降低十倍的效能。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 為空字串 ("")、只包含泛空白字元，或包含一個或多個無效的字元。-或-<paramref name="path" /> 指非文件设备，如"con："、"com1："，"lpt1:"，等等。在 NTFS 环境中。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 指非文件设备，如"con："、"com1："，"lpt1:"，等等。在非 NTFS 环境中。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> 為負值或零。-或- <paramref name="mode" />、<paramref name="access" /> 或 <paramref name="share" /> 包含無效值。</exception>
      <exception cref="T:System.IO.FileNotFoundException">無法找到檔案，例如，當 <paramref name="mode" /> 為 FileMode.Truncate 或 FileMode.Open，而且 <paramref name="path" /> 指定的檔案不存在的時候。檔案必須已在這些模式中存在。</exception>
      <exception cref="T:System.IO.IOException">發生 I/O 錯誤，例如在 FileMode.CreateNew 指定的檔案已經存在時指定 <paramref name="path" />。-或- 系統在執行 Windows 98 或 Windows 98 SE，且 <paramref name="share" /> 設定為 FileShare.Delete。-或-已關閉資料流。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效，例如位在未對應的磁碟上。</exception>
      <exception cref="T:System.UnauthorizedAccessException">指定之 <paramref name="access" /> 的作業系統不允許所要求的 <paramref name="path" />，例如，當 <paramref name="access" /> 為 Write 或 ReadWrite，且檔案或目錄設定為唯讀存取時。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Int32,System.IO.FileOptions)">
      <summary>使用指定的路徑、建立模式、讀取/寫入與共用權限、其他 FileStream 對同一檔案的存取權、緩衝區大小和其他檔案選項，初始化 <see cref="T:System.IO.FileStream" /> 類別的新執行個體。</summary>
      <param name="path">目前 FileStream 物件將會封裝之檔案的相對或絕對路徑。</param>
      <param name="mode">常數，決定如何開啟或建立檔案。</param>
      <param name="access">常數，決定 FileStream 物件如何存取檔案。這也可以判斷 FileStream 物件之 <see cref="P:System.IO.FileStream.CanRead" /> 與 <see cref="P:System.IO.FileStream.CanWrite" /> 屬性傳回的值。如果 <paramref name="path" /> 指定了磁碟檔案，則 <see cref="P:System.IO.FileStream.CanSeek" /> 為 true。</param>
      <param name="share">常數，決定處理程序如何共用檔案。</param>
      <param name="bufferSize">大於 0 的 <see cref="T:System.Int32" /> 正值，指出緩衝區大小。預設緩衝區大小為 4096。</param>
      <param name="options">值，指定其他檔案選項。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 為空字串 ("")、只包含泛空白字元，或包含一個或多個無效的字元。-或-<paramref name="path" /> 指非文件设备，如"con："、"com1："，"lpt1:"，等等。在 NTFS 环境中。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 指非文件设备，如"con："、"com1："，"lpt1:"，等等。在非 NTFS 环境中。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> 為負值或零。-或- <paramref name="mode" />、<paramref name="access" /> 或 <paramref name="share" /> 包含無效值。</exception>
      <exception cref="T:System.IO.FileNotFoundException">無法找到檔案，例如，當 <paramref name="mode" /> 為 FileMode.Truncate 或 FileMode.Open，而且 <paramref name="path" /> 指定的檔案不存在的時候。檔案必須已在這些模式中存在。</exception>
      <exception cref="T:System.IO.IOException">發生 I/O 錯誤，例如在 FileMode.CreateNew 指定的檔案已經存在時指定 <paramref name="path" />。-或-已關閉資料流。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效，例如位在未對應的磁碟上。</exception>
      <exception cref="T:System.UnauthorizedAccessException">指定之 <paramref name="access" /> 的作業系統不允許所要求的 <paramref name="path" />，例如，當 <paramref name="access" /> 為 Write 或 ReadWrite，且檔案或目錄設定為唯讀存取時。-或-<see cref="F:System.IO.FileOptions.Encrypted" /> 是指定給 <paramref name="options" />，但目前平台上不支援檔案加密。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
    </member>
    <member name="P:System.IO.FileStream.CanRead">
      <summary>取得表示目前資料流是否支援讀取的值。</summary>
      <returns>如果資料流支援讀取，則為 true；如果資料流已關閉或以唯寫存取開啟，則為 false。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.CanSeek">
      <summary>取得表示目前資料流是否支援搜尋的值。</summary>
      <returns>如果資料流支援搜尋，則為 true；如果資料流已關閉或如果 FileStream 是從作業系統控制代碼 (例如管道或主控台的輸出) 建構，則為 false。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.CanWrite">
      <summary>取得表示目前資料流是否支援寫入的值。</summary>
      <returns>如果資料流支援寫入，則為 true；如果資料流已關閉或以唯讀存取開啟，則為 false。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Dispose(System.Boolean)">
      <summary>釋放 <see cref="T:System.IO.FileStream" /> 所使用的 Unmanaged 資源，並選擇性釋放 Managed 資源。</summary>
      <param name="disposing">true 表示釋放 Managed 和 Unmanaged 資源，false 則表示只釋放 Unmanaged 資源。</param>
    </member>
    <member name="M:System.IO.FileStream.Finalize">
      <summary>確認釋出資源，並在記憶體回收行程再利用 FileStream 時執行其他清除作業。</summary>
    </member>
    <member name="M:System.IO.FileStream.Flush">
      <summary>清除這個資料流的緩衝區，讓所有緩衝資料全部寫入檔案。</summary>
      <exception cref="T:System.IO.IOException">發生 I/O 錯誤。</exception>
      <exception cref="T:System.ObjectDisposedException">已關閉資料流。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Flush(System.Boolean)">
      <summary>清除此資料流的緩衝區，讓所有緩衝資料全部寫入檔案，同時也清除所有的中繼檔案緩衝區。</summary>
      <param name="flushToDisk">true 表示排清所有中繼檔案緩衝區；否則為 false。</param>
    </member>
    <member name="M:System.IO.FileStream.FlushAsync(System.Threading.CancellationToken)">
      <summary>以非同步的方式清除這個資料流的所有緩衝區，造成任何緩衝資料都寫入基礎裝置，並且監視取消要求。</summary>
      <returns>表示非同步排清作業的工作。</returns>
      <param name="cancellationToken">用來監視是否有取消要求的語彙基元。</param>
      <exception cref="T:System.ObjectDisposedException">已處置資料流。</exception>
    </member>
    <member name="P:System.IO.FileStream.IsAsync">
      <summary>取得值，指出 FileStream 為非同步或同步開啟。</summary>
      <returns>如果 FileStream 是非同步開啟，則為 true，否則為 false。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.Length">
      <summary>取得資料流的位元組長度。</summary>
      <returns>代表資料流長度的長數值 (以位元組為單位)。</returns>
      <exception cref="T:System.NotSupportedException">這個資料流的 <see cref="P:System.IO.FileStream.CanSeek" /> 為 false。</exception>
      <exception cref="T:System.IO.IOException">發生 I/O 錯誤，例如檔案已經關閉。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.Name">
      <summary>取得傳遞給建構函式的 FileStream 名稱。</summary>
      <returns>FileStream 名稱的字串。</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileStream.Position">
      <summary>取得或設定這個資料流的目前位置。</summary>
      <returns>這個資料流的目前位置。</returns>
      <exception cref="T:System.NotSupportedException">資料流不支援搜尋。</exception>
      <exception cref="T:System.IO.IOException">發生 I/O 錯誤。-或-位置設定為非常大的值，超出 Windows 98 (含) 以前版本中資料流的末端。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">嘗試將位置設定為負值。</exception>
      <exception cref="T:System.IO.EndOfStreamException">嘗試搜尋超出資料流長度 (不支援此)。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>從資料流讀取位元組區塊，並將資料寫入指定緩衝區。</summary>
      <returns>緩衝區所讀取的總位元組數。如果目前無法提供那麼多的位元組數目，則這個數目可能小於所要求的位元組數；如果已經到達資料流末端，則為零。</returns>
      <param name="array">當這個方法傳回時，會包含指定的位元組陣列，這個陣列具有介於 <paramref name="offset" /> 到 (<paramref name="offset" /> + <paramref name="count" /> - 1<paramref name=")" /> 之間的值，已由讀取自目前來源的位元組所取代。</param>
      <param name="offset">要在其中放置讀取位元組之 <paramref name="array" /> 的位元組位移。</param>
      <param name="count">要讀取的最大位元組數。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 或 <paramref name="count" /> 為負值。</exception>
      <exception cref="T:System.NotSupportedException">資料流不支援讀取。</exception>
      <exception cref="T:System.IO.IOException">發生 I/O 錯誤。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> 和 <paramref name="count" /> 描述 <paramref name="array" /> 中的無效範圍。</exception>
      <exception cref="T:System.ObjectDisposedException">在資料流關閉後，會呼叫方法。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.ReadAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>以非同步的方式從目前資料流讀取一連串的位元組、依所讀取的位元組數目進階資料流中的位置，以及監視取消要求。</summary>
      <returns>表示非同步讀取作業的工作。<paramref name="TResult" /> 參數的值會包含讀取至緩衝區的位元組總數。如果目前可供使用的位元組數目少於所要求的數目，結果值可能會小於所要求的位元組數目，或者如果已經到達資料流末端，則可能為 0 (零)。</returns>
      <param name="buffer">寫入資料的緩衝區。</param>
      <param name="offset">開始於此處自資料流寫入資料的 <paramref name="buffer" /> 中的位元組位移。</param>
      <param name="count">要讀取的最大位元組數。</param>
      <param name="cancellationToken">用來監視是否有取消要求的語彙基元。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 或 <paramref name="count" /> 為負值。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> 和 <paramref name="count" /> 的總和大於緩衝區的長度。</exception>
      <exception cref="T:System.NotSupportedException">資料流不支援讀取。</exception>
      <exception cref="T:System.ObjectDisposedException">已處置資料流。</exception>
      <exception cref="T:System.InvalidOperationException">資料流目前由先前讀取作業所使用。</exception>
    </member>
    <member name="M:System.IO.FileStream.ReadByte">
      <summary>從檔案讀取一個位元組，並將讀取位置前移一個位元組。</summary>
      <returns>轉換為 <see cref="T:System.Int32" /> 的位元組，如果已經到達資料流的末端，則為 -1。</returns>
      <exception cref="T:System.NotSupportedException">目前資料流不支援讀取。</exception>
      <exception cref="T:System.ObjectDisposedException">目前的資料流已經關閉。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.SafeFileHandle">
      <summary>取得 <see cref="T:Microsoft.Win32.SafeHandles.SafeFileHandle" /> 物件，這個物件代表目前的 <see cref="T:System.IO.FileStream" /> 物件封裝之檔案的作業系統檔案控制代碼。</summary>
      <returns>代表目前 <see cref="T:System.IO.FileStream" /> 物件封裝之檔案的作業系統檔案控制代碼的物件。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Seek(System.Int64,System.IO.SeekOrigin)">
      <summary>設定這個資料流的目前位置為指定的數值。</summary>
      <returns>資料流中的新位置。</returns>
      <param name="offset">相對於 <paramref name="origin" /> 的搜尋起點。</param>
      <param name="origin">使用 <paramref name="offset" /> 類型的值，指定開頭、結尾或目前位置做為 <see cref="T:System.IO.SeekOrigin" /> 的參考點。</param>
      <exception cref="T:System.IO.IOException">發生 I/O 錯誤。</exception>
      <exception cref="T:System.NotSupportedException">資料流不支援搜尋，例如，如果 FileStream 為從管道或主控台輸出所建構。</exception>
      <exception cref="T:System.ArgumentException">在資料流開頭之前嘗試搜尋。</exception>
      <exception cref="T:System.ObjectDisposedException">在資料流關閉後，會呼叫方法。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.SetLength(System.Int64)">
      <summary>設定這個資料流長度為指定的數值。</summary>
      <param name="value">資料流的新長度。</param>
      <exception cref="T:System.IO.IOException">發生 I/O 錯誤。</exception>
      <exception cref="T:System.NotSupportedException">資料流不支援寫入和搜尋。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">嘗試將 <paramref name="value" /> 參數設定為小於 0。</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>將位元組區塊寫入檔案資料流中。</summary>
      <param name="array">包含要寫入資料流之資料的緩衝區。</param>
      <param name="offset">
        <paramref name="array" /> 中以零起始的位元組位移，要從其中開始將位元組複製至資料流。</param>
      <param name="count">寫入的最大位元組數。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> 和 <paramref name="count" /> 描述 <paramref name="array" /> 中的無效範圍。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 或 <paramref name="count" /> 為負值。</exception>
      <exception cref="T:System.IO.IOException">發生 I/O 錯誤。-或-其他執行緒可能導致作業系統之檔案控制代碼的位置發生非預期的變更。</exception>
      <exception cref="T:System.ObjectDisposedException">已關閉資料流。</exception>
      <exception cref="T:System.NotSupportedException">目前的資料流執行個體不支援寫入。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.WriteAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>以非同步的方式將一連串的位元組寫入目前的資料流，由這個資料流中目前的位置前移寫入的位元組數目，並且監視取消要求。</summary>
      <returns>表示非同步寫入作業的工作。</returns>
      <param name="buffer">寫入資料的來源緩衝區。</param>
      <param name="offset">
        <paramref name="buffer" /> 中以零起始的位元組位移，要從其中開始將位元組複製至資料流。</param>
      <param name="count">寫入的最大位元組數。</param>
      <param name="cancellationToken">用來監視是否有取消要求的語彙基元。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 或 <paramref name="count" /> 為負值。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> 和 <paramref name="count" /> 的總和大於緩衝區的長度。</exception>
      <exception cref="T:System.NotSupportedException">資料流不支援寫入。</exception>
      <exception cref="T:System.ObjectDisposedException">已處置資料流。</exception>
      <exception cref="T:System.InvalidOperationException">資料流目前由先前寫入作業所使用。</exception>
    </member>
    <member name="M:System.IO.FileStream.WriteByte(System.Byte)">
      <summary>寫入一個位元組到檔案資料流中的目前位置。</summary>
      <param name="value">要寫入資料流的位元組。</param>
      <exception cref="T:System.ObjectDisposedException">已關閉資料流。</exception>
      <exception cref="T:System.NotSupportedException">資料流不支援寫入。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.FileSystemInfo">
      <summary>為 <see cref="T:System.IO.FileInfo" /> 和 <see cref="T:System.IO.DirectoryInfo" /> 物件提供基底類別。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.FileSystemInfo.#ctor">
      <summary>初始化 <see cref="T:System.IO.FileSystemInfo" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.IO.FileSystemInfo.Attributes">
      <summary>取得或設定目前檔案或目錄的屬性。</summary>
      <returns>目前 <see cref="T:System.IO.FileSystemInfo" /> 的 <see cref="T:System.IO.FileAttributes" />。</returns>
      <exception cref="T:System.IO.FileNotFoundException">指定的檔案不存在。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效；例如，位於未對應的磁碟上。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.ArgumentException">呼叫端嘗試設定無效的檔案屬性 (Attribute)。-或-使用者嘗試設定屬性值，但並沒有寫入權限。</exception>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> 無法初始化資料。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.CreationTime">
      <summary>取得或設定目前檔案或目錄的建立時間。</summary>
      <returns>目前的 <see cref="T:System.IO.FileSystemInfo" /> 物件的建立日期和時間。</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> 無法初始化資料。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效；例如，位於未對應的磁碟上。</exception>
      <exception cref="T:System.PlatformNotSupportedException">目前的作業系統不是 Windows NT (含) 以後版本。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">呼叫端嘗試設定無效的建立時間。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.CreationTimeUtc">
      <summary>取得或設定目前檔案或目錄的建立時間，其格式為國際標準時間 (Coordinated Universal Time，UTC)。</summary>
      <returns>目前 <see cref="T:System.IO.FileSystemInfo" /> 物件以 UTC 格式表示的建立日期和時間。</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> 無法初始化資料。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效；例如，位於未對應的磁碟上。</exception>
      <exception cref="T:System.PlatformNotSupportedException">目前的作業系統不是 Windows NT (含) 以後版本。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">呼叫端嘗試設定無效的存取時間。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileSystemInfo.Delete">
      <summary>刪除檔案或目錄。</summary>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路徑無效；例如，位於未對應的磁碟上。</exception>
      <exception cref="T:System.IO.IOException">此檔案或目錄具有開啟控制代碼，而且作業系統是 Windows XP 或先前的版本。這個開啟控制代碼可能是列舉目錄和檔案所產生。如需詳細資訊，請參閱如何：列舉目錄和檔案。</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.FileSystemInfo.Exists">
      <summary>取得值，表示檔案或目錄是否存在。</summary>
      <returns>如果檔案或目錄存在，則為 true，否則為 false。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileSystemInfo.Extension">
      <summary>取得字串，代表檔案的副檔名部分。</summary>
      <returns>字串，含有 <see cref="T:System.IO.FileSystemInfo" /> 副檔名。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileSystemInfo.FullName">
      <summary>取得目錄或檔案的完整路徑。</summary>
      <returns>含有完整路徑的字串。</returns>
      <exception cref="T:System.IO.PathTooLongException">完整路徑和檔案名稱是 260 個以上的字元。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="F:System.IO.FileSystemInfo.FullPath">
      <summary>代表目錄或檔案的完整路徑。</summary>
      <exception cref="T:System.IO.PathTooLongException">完整路徑是 260 個以上的字元。</exception>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastAccessTime">
      <summary>取得或設定上次存取目前檔案或目錄的時間。</summary>
      <returns>上次存取目前檔案或目錄的時間。</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> 無法初始化資料。</exception>
      <exception cref="T:System.PlatformNotSupportedException">目前的作業系統不是 Windows NT (含) 以後版本。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">呼叫端嘗試設定無效的存取時間</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastAccessTimeUtc">
      <summary>取得或設定目前檔案或目錄上次被存取的時間，其格式為 Coordinated Universal Time (UTC)。</summary>
      <returns>目前檔案或目錄上次被存取的 UTC 時間。</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> 無法初始化資料。</exception>
      <exception cref="T:System.PlatformNotSupportedException">目前的作業系統不是 Windows NT (含) 以後版本。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">呼叫端嘗試設定無效的存取時間。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastWriteTime">
      <summary>取得或設定上次寫入目前檔案或目錄的時間。</summary>
      <returns>上次寫入目前檔案的時間。</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> 無法初始化資料。</exception>
      <exception cref="T:System.PlatformNotSupportedException">目前的作業系統不是 Windows NT (含) 以後版本。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">呼叫端嘗試設定無效的寫入時間。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastWriteTimeUtc">
      <summary>取得或設定目前檔案或目錄上次被寫入的時間，其格式為 Coordinated Universal Time (UTC)。</summary>
      <returns>目前檔案上次被寫入的 UTC 時間。</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> 無法初始化資料。</exception>
      <exception cref="T:System.PlatformNotSupportedException">目前的作業系統不是 Windows NT (含) 以後版本。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">呼叫端嘗試設定無效的寫入時間。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.Name">
      <summary>對於檔案，取得檔案的名稱。對於目錄，如果有階層架構，則是取得階層架構中最後一個目錄的名稱。否則，Name 屬性會取得目錄的名稱。</summary>
      <returns>字串，可以是父代 (Parent) 目錄的名稱、階層架構中最後一個目錄的名稱，或者包括副檔名在內的檔案名稱。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.FileSystemInfo.OriginalPath">
      <summary>使用者指定的相對或絕對的原始路徑。</summary>
    </member>
    <member name="M:System.IO.FileSystemInfo.Refresh">
      <summary>重新整理物件的狀態。</summary>
      <exception cref="T:System.IO.IOException">裝置 (例如，磁碟機) 未就緒。 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.SearchOption">
      <summary>指定要搜尋目前目錄，還是要搜尋目前目錄和所有子目錄。</summary>
    </member>
    <member name="F:System.IO.SearchOption.AllDirectories">
      <summary>在搜尋作業中包含目前目錄和所有其子目錄。這個選項會在搜尋中包含重新剖析點 (例如掛接磁碟和符號連結)。</summary>
    </member>
    <member name="F:System.IO.SearchOption.TopDirectoryOnly">
      <summary>只在搜尋作業中包含目前目錄。</summary>
    </member>
  </members>
</doc>