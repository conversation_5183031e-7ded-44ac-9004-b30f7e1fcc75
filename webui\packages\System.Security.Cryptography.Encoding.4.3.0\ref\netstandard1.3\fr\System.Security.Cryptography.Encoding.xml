﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Security.Cryptography.Encoding</name>
  </assembly>
  <members>
    <member name="T:System.Security.Cryptography.AsnEncodedData">
      <summary>Représente des données encodées ASN.1 (Abstract Syntax Notation One).</summary>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.Cryptography.AsnEncodedData" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.#ctor(System.Byte[])">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.Cryptography.AsnEncodedData" /> à l'aide d'un tableau d'octets.</summary>
      <param name="rawData">Tableau d'octets qui contient les données encodées ASN.1 (Abstract Syntax Notation One).</param>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.#ctor(System.Security.Cryptography.AsnEncodedData)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.Cryptography.AsnEncodedData" /> à l'aide d'une instance de la classe <see cref="T:System.Security.Cryptography.AsnEncodedData" />.</summary>
      <param name="asnEncodedData">Instance de la classe <see cref="T:System.Security.Cryptography.AsnEncodedData" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asnEncodedData" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.#ctor(System.Security.Cryptography.Oid,System.Byte[])">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.Cryptography.AsnEncodedData" /> à l'aide d'un objet <see cref="T:System.Security.Cryptography.Oid" /> et d'un tableau d'octets.</summary>
      <param name="oid">Objet <see cref="T:System.Security.Cryptography.Oid" />.</param>
      <param name="rawData">Tableau d'octets qui contient les données encodées ASN.1 (Abstract Syntax Notation One).</param>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.#ctor(System.String,System.Byte[])">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.Cryptography.AsnEncodedData" /> à l'aide d'un tableau d'octets.</summary>
      <param name="oid">Chaîne qui représente des informations <see cref="T:System.Security.Cryptography.Oid" />.</param>
      <param name="rawData">Tableau d'octets qui contient les données encodées ASN.1 (Abstract Syntax Notation One).</param>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>Copie les informations d'un objet <see cref="T:System.Security.Cryptography.AsnEncodedData" />.</summary>
      <param name="asnEncodedData">L'objet <see cref="T:System.Security.Cryptography.AsnEncodedData" /> sur lequel baser le nouvel objet.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asnEncodedData " />est null.</exception>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.Format(System.Boolean)">
      <summary>Retourne une version mise en forme des données encodées ASN.1 (Abstract Syntax Notation One) sous forme de chaîne.</summary>
      <returns>Chaîne mise en forme représentant les données encodées ASN.1 (Abstract Syntax Notation One).</returns>
      <param name="multiLine">true si la chaîne de retour doit contenir des retours chariot ; sinon, false.</param>
    </member>
    <member name="P:System.Security.Cryptography.AsnEncodedData.Oid">
      <summary>Obtient ou définit la valeur <see cref="T:System.Security.Cryptography.Oid" /> pour un objet <see cref="T:System.Security.Cryptography.AsnEncodedData" />.</summary>
      <returns>Objet <see cref="T:System.Security.Cryptography.Oid" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.AsnEncodedData.RawData">
      <summary>Obtient ou définit les données encodées ASN.1 (Abstract Syntax Notation One) représentées dans un tableau d'octets.</summary>
      <returns>Tableau d'octets qui représente les données encodées ASN.1 (Abstract Syntax Notation One).</returns>
      <exception cref="T:System.ArgumentNullException">La valeur est null.</exception>
    </member>
    <member name="T:System.Security.Cryptography.Oid">
      <summary>Représente un identificateur d'objet de chiffrement.Cette classe ne peut pas être héritée.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Oid.#ctor(System.Security.Cryptography.Oid)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.Cryptography.Oid" /> à l'aide de l'objet <see cref="T:System.Security.Cryptography.Oid" /> spécifié.</summary>
      <param name="oid">Les informations d'identificateur d'objet à utiliser pour créer le nouvel identificateur d'objet.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="oid " />est null.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Oid.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.Cryptography.Oid" /> à l'aide d'une valeur de chaîne d'un objet <see cref="T:System.Security.Cryptography.Oid" />.</summary>
      <param name="oid">Un identificateur d'objet.</param>
    </member>
    <member name="M:System.Security.Cryptography.Oid.#ctor(System.String,System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.Cryptography.Oid" /> avec la valeur et le nom convivial spécifiés.</summary>
      <param name="value">Le nombre séparé par des points de l'identificateur.</param>
      <param name="friendlyName">Le nom convivial de l'identificateur.</param>
    </member>
    <member name="P:System.Security.Cryptography.Oid.FriendlyName">
      <summary>Obtient ou définit le nom convivial de l'identificateur.</summary>
      <returns>Le nom convivial de l'identificateur.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Oid.FromFriendlyName(System.String,System.Security.Cryptography.OidGroup)">
      <summary>Crée un objet <see cref="T:System.Security.Cryptography.Oid" /> à partir d'un nom OID convivial en recherchant le groupe spécifié.</summary>
      <returns>Objet représentant l'OID spécifié.</returns>
      <param name="friendlyName">Le nom convivial de l'identificateur.</param>
      <param name="group">Groupe dans lequel effectuer la recherche.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="friendlyName " /> a la valeur null.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">OID introuvable.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Oid.FromOidValue(System.String,System.Security.Cryptography.OidGroup)">
      <summary>Crée un objet <see cref="T:System.Security.Cryptography.Oid" /> à l'aide du groupe et de la valeur OID spécifiés.</summary>
      <returns>Nouvelle instance d'un objet <see cref="T:System.Security.Cryptography.Oid" />.</returns>
      <param name="oidValue">Valeur de l'identificateur d'objet.</param>
      <param name="group">Groupe dans lequel effectuer la recherche.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="oidValue" /> a la valeur null.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Le nom convivial de la valeur OID est introuvable.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Oid.Value">
      <summary>Obtient ou définit le nombre séparé par des points de l'identificateur.</summary>
      <returns>Le nombre séparé par des points de l'identificateur.</returns>
    </member>
    <member name="T:System.Security.Cryptography.OidCollection">
      <summary>Représente une collection d'objets <see cref="T:System.Security.Cryptography.Oid" />.Cette classe ne peut pas être héritée.</summary>
    </member>
    <member name="M:System.Security.Cryptography.OidCollection.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.Cryptography.OidCollection" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.OidCollection.Add(System.Security.Cryptography.Oid)">
      <summary>Ajoute un objet <see cref="T:System.Security.Cryptography.Oid" /> à l'objet <see cref="T:System.Security.Cryptography.OidCollection" />.</summary>
      <returns>Index de l'objet ajouté <see cref="T:System.Security.Cryptography.Oid" />.</returns>
      <param name="oid">Objet <see cref="T:System.Security.Cryptography.Oid" /> à ajouter à la collection.</param>
    </member>
    <member name="M:System.Security.Cryptography.OidCollection.CopyTo(System.Security.Cryptography.Oid[],System.Int32)">
      <summary>Copie l'objet <see cref="T:System.Security.Cryptography.OidCollection" /> dans un tableau.</summary>
      <param name="array">Tableau dans lequel l'objet <see cref="T:System.Security.Cryptography.OidCollection" /> est copié.</param>
      <param name="index">Emplacement où commence l'opération de copie.</param>
    </member>
    <member name="P:System.Security.Cryptography.OidCollection.Count">
      <summary>Obtient le nombre d'objets <see cref="T:System.Security.Cryptography.Oid" /> figurant dans une collection. </summary>
      <returns>Nombre d'objets <see cref="T:System.Security.Cryptography.Oid" /> dans une collection.</returns>
    </member>
    <member name="M:System.Security.Cryptography.OidCollection.GetEnumerator">
      <summary>Retourne un objet <see cref="T:System.Security.Cryptography.OidEnumerator" /> qui peut être utilisé pour naviguer jusqu'à l'objet <see cref="T:System.Security.Cryptography.OidCollection" />.</summary>
      <returns>Objet <see cref="T:System.Security.Cryptography.OidEnumerator" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.OidCollection.Item(System.Int32)">
      <summary>Obtient un objet <see cref="T:System.Security.Cryptography.Oid" /> à partir de l'objet <see cref="T:System.Security.Cryptography.OidCollection" />.</summary>
      <returns>Objet <see cref="T:System.Security.Cryptography.Oid" />.</returns>
      <param name="index">Emplacement de l'objet <see cref="T:System.Security.Cryptography.Oid" /> dans la collection.</param>
    </member>
    <member name="P:System.Security.Cryptography.OidCollection.Item(System.String)">
      <summary>Obtient le premier objet <see cref="T:System.Security.Cryptography.Oid" /> qui contient une valeur de la propriété <see cref="P:System.Security.Cryptography.Oid.Value" /> ou une valeur de la propriété <see cref="P:System.Security.Cryptography.Oid.FriendlyName" /> qui correspond à la valeur de chaîne spécifiée dans l'objet <see cref="T:System.Security.Cryptography.OidCollection" />.</summary>
      <returns>Objet <see cref="T:System.Security.Cryptography.Oid" />.</returns>
      <param name="oid">Chaîne qui représente une propriété <see cref="P:System.Security.Cryptography.Oid.Value" /> ou une propriété <see cref="P:System.Security.Cryptography.Oid.FriendlyName" />.</param>
    </member>
    <member name="M:System.Security.Cryptography.OidCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copie l'objet <see cref="T:System.Security.Cryptography.OidCollection" /> dans un tableau.</summary>
      <param name="array">Tableau dans lequel copier l'objet <see cref="T:System.Security.Cryptography.OidCollection" />.</param>
      <param name="index">Emplacement où commence l'opération de copie.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> ne peut pas être un tableau multidimensionnel.ou<paramref name="array" /> contient une longueur d'offset non valide.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">La valeur de <paramref name="index" /> est hors limites.</exception>
    </member>
    <member name="P:System.Security.Cryptography.OidCollection.System#Collections#ICollection#IsSynchronized"></member>
    <member name="P:System.Security.Cryptography.OidCollection.System#Collections#ICollection#SyncRoot"></member>
    <member name="M:System.Security.Cryptography.OidCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Retourne un objet <see cref="T:System.Security.Cryptography.OidEnumerator" /> qui peut être utilisé pour naviguer jusqu'à l'objet <see cref="T:System.Security.Cryptography.OidCollection" />.</summary>
      <returns>Objet <see cref="T:System.Security.Cryptography.OidEnumerator" /> pouvant être utilisé pour naviguer dans la collection.</returns>
    </member>
    <member name="T:System.Security.Cryptography.OidEnumerator">
      <summary>Offre la capacité de naviguer dans un objet <see cref="T:System.Security.Cryptography.OidCollection" />.Cette classe ne peut pas être héritée.</summary>
    </member>
    <member name="P:System.Security.Cryptography.OidEnumerator.Current">
      <summary>Obtient l'objet <see cref="T:System.Security.Cryptography.Oid" /> en cours dans un objet <see cref="T:System.Security.Cryptography.OidCollection" />.</summary>
      <returns>Objet <see cref="T:System.Security.Cryptography.Oid" /> en cours dans la collection.</returns>
    </member>
    <member name="M:System.Security.Cryptography.OidEnumerator.MoveNext">
      <summary>Avance à l'objet <see cref="T:System.Security.Cryptography.Oid" /> suivant dans un objet <see cref="T:System.Security.Cryptography.OidCollection" />.</summary>
      <returns>true si l'énumérateur a pu avancer jusqu'à l'élément suivant ; false si l'énumérateur a dépassé la fin de la collection.</returns>
      <exception cref="T:System.InvalidOperationException">La collection a été modifiée après la création de l'énumérateur.</exception>
    </member>
    <member name="M:System.Security.Cryptography.OidEnumerator.Reset">
      <summary>Définit un énumérateur à sa position initiale.</summary>
      <exception cref="T:System.InvalidOperationException">La collection a été modifiée après la création de l'énumérateur.</exception>
    </member>
    <member name="P:System.Security.Cryptography.OidEnumerator.System#Collections#IEnumerator#Current">
      <summary>Obtient l'objet <see cref="T:System.Security.Cryptography.Oid" /> en cours dans un objet <see cref="T:System.Security.Cryptography.OidCollection" />.</summary>
      <returns>Objet <see cref="T:System.Security.Cryptography.Oid" /> en cours.</returns>
    </member>
    <member name="T:System.Security.Cryptography.OidGroup">
      <summary>Reconnaît les groupes d'identificateur d'objet de chiffrement (OID) Windows.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.All">
      <summary>Tous les groupes.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.Attribute">
      <summary>Groupe Windows qui est représenté par CRYPT_RDN_ATTR_OID_GROUP_ID.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.EncryptionAlgorithm">
      <summary>Groupe Windows qui est représenté par CRYPT_ENCRYPT_ALG_OID_GROUP_ID.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.EnhancedKeyUsage">
      <summary>Groupe Windows qui est représenté par CRYPT_ENHKEY_USAGE_OID_GROUP_ID.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.ExtensionOrAttribute">
      <summary>Groupe Windows qui est représenté par CRYPT_EXT_OR_ATTR_OID_GROUP_ID.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.HashAlgorithm">
      <summary>Groupe Windows qui est représenté par CRYPT_HASH_ALG_OID_GROUP_ID.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.KeyDerivationFunction">
      <summary>Groupe Windows qui est représenté par CRYPT_KDF_OID_GROUP_ID.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.Policy">
      <summary>Groupe Windows qui est représenté par CRYPT_POLICY_OID_GROUP_ID.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.PublicKeyAlgorithm">
      <summary>Groupe Windows qui est représenté par CRYPT_PUBKEY_ALG_OID_GROUP_ID.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.SignatureAlgorithm">
      <summary>Groupe Windows qui est représenté par CRYPT_SIGN_ALG_OID_GROUP_ID.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.Template">
      <summary>Groupe Windows qui est représenté par CRYPT_TEMPLATE_OID_GROUP_ID.</summary>
    </member>
  </members>
</doc>