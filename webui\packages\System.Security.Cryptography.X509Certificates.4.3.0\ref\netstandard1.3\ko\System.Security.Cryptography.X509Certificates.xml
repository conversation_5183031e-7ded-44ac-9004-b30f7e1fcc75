﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Security.Cryptography.X509Certificates</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Win32.SafeHandles.SafeX509ChainHandle">
      <summary>X.509 체인을 나타내는 SafeHandle을 제공합니다.자세한 내용은 <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" />을 참조하십시오.</summary>
    </member>
    <member name="P:Microsoft.Win32.SafeHandles.SafeX509ChainHandle.IsInvalid"></member>
    <member name="T:System.Security.Cryptography.X509Certificates.OpenFlags">
      <summary>X.509 인증서 저장소를 여는 방법을 지정합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.IncludeArchived">
      <summary>X.509 인증서 저장소를 열고 보관된 인증서를 포함시킵니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.MaxAllowed">
      <summary>허용되는 최고 액세스 권한에 대해 X.509 인증서 저장소를 엽니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.OpenExistingOnly">
      <summary>기존 저장소만 엽니다. 저장소가 없어도 <see cref="M:System.Security.Cryptography.X509Certificates.X509Store.Open(System.Security.Cryptography.X509Certificates.OpenFlags)" /> 메서드는 새 저장소를 만들지 않습니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.ReadOnly">
      <summary>읽기 전용으로 X.509 인증서 저장소를 엽니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.ReadWrite">
      <summary>읽기 및 쓰기용으로 X.509 인증서 저장소를 엽니다.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.PublicKey">
      <summary>인증서의 공개 키 정보를 나타냅니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.PublicKey.#ctor(System.Security.Cryptography.Oid,System.Security.Cryptography.AsnEncodedData,System.Security.Cryptography.AsnEncodedData)">
      <summary>공개 키의 OID(개체 식별자) 개체, ASN.1로 인코딩된 공개 키 매개 변수 및 ASN.1로 인코딩된 공개 키 값을 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.PublicKey" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
      <param name="oid">공개 키를 나타내는 OID(개체 식별자) 개체입니다.</param>
      <param name="parameters">ASN.1로 인코딩된 공개 키 매개 변수입니다.</param>
      <param name="keyValue">ASN.1로 인코딩된 공개 키 값입니다.</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.PublicKey.EncodedKeyValue">
      <summary>ASN.1로 인코딩된 공개 키 값을 가져옵니다.</summary>
      <returns>ASN.1로 인코딩된 공개 키 값입니다.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.PublicKey.EncodedParameters">
      <summary>ASN.1로 인코딩된 공개 키 매개 변수를 가져옵니다.</summary>
      <returns>ASN.1로 인코딩된 공개 키 매개 변수입니다.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.PublicKey.Key">
      <summary>공개 키를 나타내는 <see cref="T:System.Security.Cryptography.RSACryptoServiceProvider" /> 또는 <see cref="T:System.Security.Cryptography.DSACryptoServiceProvider" /> 개체를 가져옵니다.</summary>
      <returns>공개 키를 나타내는 <see cref="T:System.Security.Cryptography.AsymmetricAlgorithm" /> 개체입니다.</returns>
      <exception cref="T:System.NotSupportedException">키 알고리즘이 지원되지 않는 경우</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.PublicKey.Oid">
      <summary>공개 키의 OID(개체 식별자) 개체를 가져옵니다.</summary>
      <returns>공개 키의 OID(개체 식별자) 개체입니다.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.StoreLocation">
      <summary>X.509 인증서 저장소의 위치를 지정합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreLocation.CurrentUser">
      <summary>현재 사용자가 사용하는 X.509 인증서 저장소입니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreLocation.LocalMachine">
      <summary>로컬 컴퓨터에 지정된 X.509 인증서 저장소입니다.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.StoreName">
      <summary>열려는 X.509 인증서 저장소의 이름을 지정합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.AddressBook">
      <summary>다른 사용자용 X.509 인증서 저장소입니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.AuthRoot">
      <summary>제3의 CA(인증 기관)용 X.509 인증서 저장소입니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.CertificateAuthority">
      <summary>중개 CA(인증 기관)용 X.509 인증서 저장소입니다. </summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.Disallowed">
      <summary>해지된 인증서용 X.509 인증서 저장소입니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.My">
      <summary>개인 인증서용 X.509 인증서 저장소입니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.Root">
      <summary>신뢰할 수 있는 루트 CA(인증 기관)용 X.509 인증서 저장소입니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.TrustedPeople">
      <summary>직접 신뢰할 수 있는 사람 및 리소스용 X.509 인증서 저장소입니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.TrustedPublisher">
      <summary>직접 신뢰할 수 있는 게시자용 X.509 인증서 저장소입니다.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName">
      <summary>X509 인증서의 고유 이름을 나타냅니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.Byte[])">
      <summary>지정된 바이트 배열의 정보를 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="encodedDistinguishedName">고유 이름 정보가 들어 있는 바이트 배열입니다.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.Security.Cryptography.AsnEncodedData)">
      <summary>지정된 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 개체를 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="encodedDistinguishedName">고유 이름을 나타내는 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 개체입니다.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.Security.Cryptography.X509Certificates.X500DistinguishedName)">
      <summary>지정된 <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> 개체를 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="distinguishedName">
        <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> 개체</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.String)">
      <summary>지정된 문자열의 정보를 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="distinguishedName">고유 이름을 나타내는 문자열입니다.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.String,System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags)">
      <summary>지정된 문자열과 <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags" /> 플래그를 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="distinguishedName">고유 이름을 나타내는 문자열입니다.</param>
      <param name="flag">고유 이름의 특성을 지정하는 열거형 값의 비트 조합입니다.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.Decode(System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags)">
      <summary>
        <paramref name="flag" /> 매개 변수에 지정된 특징을 사용하여 고유 이름을 디코딩합니다.</summary>
      <returns>디코딩한 고유 이름입니다.</returns>
      <param name="flag">고유 이름의 특성을 지정하는 열거형 값의 비트 조합입니다.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">인증서의 이름이 잘못되었습니다.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.Format(System.Boolean)">
      <summary>인쇄하거나 텍스트 창 또는 콘솔에 출력할 수 있도록 형식이 지정된 버전의 X500 고유 이름을 반환합니다.</summary>
      <returns>X500 고유 이름을 나타내는 형식이 지정된 문자열입니다.</returns>
      <param name="multiLine">반환 문자열에 캐리지 리턴이 포함되어야 하는 경우 true이고, 그렇지 않으면 false입니다.</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X500DistinguishedName.Name">
      <summary>X500 인증서에서 쉼표로 구분된 고유 이름을 가져옵니다.</summary>
      <returns>X509 인증서의 쉼표로 구분된 고유 이름입니다.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags">
      <summary>X.500 고유 이름의 특성을 지정합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.DoNotUsePlusSign">
      <summary>고유 이름에 더하기 기호를 사용하지 않습니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.DoNotUseQuotes">
      <summary>고유 이름에 따옴표를 사용하지 않습니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.ForceUTF8Encoding">
      <summary>고유 이름이 특정 X.500 키를 인쇄 가능한 유니코드 문자열이 아닌 UTF-8 문자열로 인코딩하도록 합니다.자세한 내용과 영향을 받는 X.500 키의 목록은 X500NameFlags 열거형을 참조하세요.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.None">
      <summary>고유 이름에 특수 특성이 없습니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.Reversed">
      <summary>고유 이름이 역순입니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseCommas">
      <summary>고유 이름에 쉼표를 사용합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseNewLines">
      <summary>고유 이름에 줄 바꿈 문자를 사용합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseSemicolons">
      <summary>고유 이름에 세미콜론을 사용합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseT61Encoding">
      <summary>고유 이름에 T61 인코딩을 사용합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseUTF8Encoding">
      <summary>고유 이름에 유니코드 문자 인코딩이 아닌 UTF8 인코딩을 사용합니다.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension">
      <summary>인증서에 대해 설정되는 제약 조건을 정의합니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.#ctor">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.#ctor(System.Boolean,System.Boolean,System.Int32,System.Boolean)">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension" /> 클래스의 새 인스턴스를 초기화합니다.매개 변수는 인증서가 CA(인증 기관) 인증서인지 여부를 나타내는 값, 인증서에 허용되는 경로 수준의 수가 제한되어 있는지 여부를 나타내는 값, 인증서 경로에 허용되는 수준의 수 그리고 확장이 중요한지 여부를 나타내는 값을 지정합니다.</summary>
      <param name="certificateAuthority">인증서가 CA(인증 기관) 인증서이면 true이고, 그렇지 않으면 false입니다.</param>
      <param name="hasPathLengthConstraint">인증서에 허용되는 경로 수준의 수가 제한되어 있으면 true이고, 그렇지 않으면 false입니다.</param>
      <param name="pathLengthConstraint">인증서 경로에 허용되는 수준의 수입니다.</param>
      <param name="critical">중요한 확장이면 true이고, 그렇지 않으면 false입니다.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>
        <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 개체 및 확장이 중요한지 여부를 식별하는 값을 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
      <param name="encodedBasicConstraints">확장을 만드는 데 사용할 인코딩된 데이터입니다.</param>
      <param name="critical">중요한 확장이면 true이고, 그렇지 않으면 false입니다.</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.CertificateAuthority">
      <summary>인증서가 CA(인증 기관) 인증서인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>인증서가 CA(인증 기관) 인증서이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>
        <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 개체를 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="asnEncodedData">확장을 만드는 데 사용할 인코딩된 데이터입니다.</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.HasPathLengthConstraint">
      <summary>인증서에 허용되는 경로 수준의 수가 제한되어 있는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>인증서에 허용되는 경로 수준의 수가 제한되어 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">확장은 디코딩할 수 없는 경우 </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.PathLengthConstraint">
      <summary>인증서 경로에 허용되는 수준의 수를 가져옵니다.</summary>
      <returns>인증서 경로에 허용되는 수준의 수를 나타내는 정수입니다.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">확장은 디코딩할 수 없는 경우 </exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Certificate">
      <summary>X.509 v.3 인증서를 사용할 수 있도록 하는 메서드를 제공합니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.Byte[])">
      <summary>X.509v3 인증서를 나타내는 바이트 시퀀스에서 정의된 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="data">X.509 인증서의 데이터가 들어 있는 바이트 배열입니다.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">인증서 관련 오류가 발생하는 경우.예를 들면 다음과 같습니다.인증서 파일이 없는 경우인증서가 잘못된 경우인증서의 암호가 잘못된 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="rawData" /> 매개 변수가 null입니다.또는<paramref name="rawData" /> 매개 변수의 길이가 0인 경우</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.Byte[],System.String)">
      <summary>바이트 배열 및 암호를 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="rawData">X.509 인증서의 데이터가 들어 있는 바이트 배열입니다.</param>
      <param name="password">X.509 인증서 데이터에 액세스하는 데 필요한 암호입니다.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">인증서 관련 오류가 발생하는 경우.예를 들면 다음과 같습니다.인증서 파일이 없는 경우인증서가 잘못된 경우인증서의 암호가 잘못된 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="rawData" /> 매개 변수가 null입니다.또는<paramref name="rawData" /> 매개 변수의 길이가 0인 경우</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.Byte[],System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>바이트 배열, 암호 및 키 저장소 플래그를 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="rawData">X.509 인증서의 데이터가 들어 있는 바이트 배열입니다. </param>
      <param name="password">X.509 인증서 데이터에 액세스하는 데 필요한 암호입니다. </param>
      <param name="keyStorageFlags">인증서를 가져올 위치 및 방법을 제어하는 열거형 값의 비트 조합입니다. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">인증서 관련 오류가 발생하는 경우.예를 들면 다음과 같습니다.인증서 파일이 없는 경우인증서가 잘못된 경우인증서의 암호가 잘못된 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="rawData" /> 매개 변수가 null입니다.또는<paramref name="rawData" /> 매개 변수의 길이가 0인 경우</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.IntPtr)">
      <summary>[보안 중요] 비관리 PCCERT_CONTEXT 구조체에 대한 핸들을 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="handle">비관리 PCCERT_CONTEXT 구조체에 대한 핸들입니다.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.String)">
      <summary>PKCS7 서명 파일의 이름을 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
      <param name="fileName">PKCS7 서명 파일의 이름입니다.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">인증서 관련 오류가 발생하는 경우.예를 들면 다음과 같습니다.인증서 파일이 없는 경우인증서가 잘못된 경우인증서의 암호가 잘못된 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="fileName" /> 매개 변수가 null입니다.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.String,System.String)">
      <summary>PKCS7 서명 파일의 이름과 인증서에 액세스하기 위한 암호를 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="fileName">PKCS7 서명 파일의 이름입니다. </param>
      <param name="password">X.509 인증서 데이터에 액세스하는 데 필요한 암호입니다. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">인증서 관련 오류가 발생하는 경우.예를 들면 다음과 같습니다.인증서 파일이 없는 경우인증서가 잘못된 경우인증서의 암호가 잘못된 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="fileName" /> 매개 변수가 null입니다.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.String,System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>PKCS7 서명 파일의 이름, 인증서에 액세스하기 위한 암호 및 키 저장소 플래그를 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
      <param name="fileName">PKCS7 서명 파일의 이름입니다. </param>
      <param name="password">X.509 인증서 데이터에 액세스하는 데 필요한 암호입니다. </param>
      <param name="keyStorageFlags">인증서를 가져올 위치 및 방법을 제어하는 열거형 값의 비트 조합입니다. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">인증서 관련 오류가 발생하는 경우.예를 들면 다음과 같습니다.인증서 파일이 없는 경우인증서가 잘못된 경우인증서의 암호가 잘못된 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="fileName" /> 매개 변수가 null입니다.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Dispose">
      <summary>현재 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 개체에서 사용하는 모든 리소스를 해제합니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Dispose(System.Boolean)">
      <summary>이 사용 되는 관리 되지않는 리소스의 모든 해제 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 관리 되는 리소스를 선택적으로 해제 합니다. </summary>
      <param name="disposing">관리되는 리소스와 관리되지 않는 리소스를 모두 해제하려면 true로 설정하고, 관리되지 않는 리소스만 해제하려면 false로 설정합니다.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Equals(System.Object)">
      <summary>두 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 개체가 같은지 비교합니다.</summary>
      <returns>현재 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 개체가 <paramref name="other" /> 매개 변수에 의해 지정된 개체와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">현재 개체와 비교할 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 개체입니다. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Equals(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>두 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 개체가 같은지 비교합니다.</summary>
      <returns>현재 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 개체가 <paramref name="other" /> 매개 변수에 의해 지정된 개체와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="other">현재 개체와 비교할 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 개체입니다.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Export(System.Security.Cryptography.X509Certificates.X509ContentType)">
      <summary>현재 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 개체를 <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> 값 중 하나로 설명되는 형식으로 바이트 배열로 내보냅니다. </summary>
      <returns>현재 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 개체를 나타내는 바이트의 배열입니다.</returns>
      <param name="contentType">출력 데이터 서식 지정 방법을 설명하는 <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> 값 중 하나입니다. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.Cert" />, <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.SerializedCert" /> 또는 <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pkcs12" /> 이외의 값이 <paramref name="contentType" /> 매개 변수로 전달된 경우또는인증서를 내보낼 수 없는 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.KeyContainerPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="Open, Export" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Export(System.Security.Cryptography.X509Certificates.X509ContentType,System.String)">
      <summary>현재 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 개체를 지정된 암호를 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> 값 중 하나로 설명되는 형식으로 바이트 배열로 내보냅니다.</summary>
      <returns>현재 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 개체를 나타내는 바이트의 배열입니다.</returns>
      <param name="contentType">출력 데이터 서식 지정 방법을 설명하는 <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> 값 중 하나입니다.</param>
      <param name="password">X.509 인증서 데이터에 액세스하는 데 필요한 암호입니다.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.Cert" />, <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.SerializedCert" /> 또는 <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pkcs12" /> 이외의 값이 <paramref name="contentType" /> 매개 변수로 전달된 경우또는인증서를 내보낼 수 없는 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.KeyContainerPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="Open, Export" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetCertHash">
      <summary>X.509v3 인증서에 대한 해시 값을 바이트 배열로 반환합니다.</summary>
      <returns>Authenticode X.509 v.3 인증서에 대한 해시 값입니다.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetFormat">
      <summary>이 X.509v3 인증서의 형식 이름을 반환합니다.</summary>
      <returns>이 Authenticode X.509 v.3 인증서의 형식입니다.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetHashCode">
      <summary>X.509v3 인증서에 대한 해시 코드를 정수로 반환합니다.</summary>
      <returns>Authenticode X.509 v.3 인증서에 대한 해시 코드(정수)입니다.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetKeyAlgorithm">
      <summary>이 X.509v3 인증서에 대한 키 알고리즘 정보(문자열)를 반환합니다.</summary>
      <returns>이 Authenticode X.509 v.3 인증서에 대한 키 알고리즘 정보(문자열)입니다.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">인증서 컨텍스트가 잘못된 경우</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetKeyAlgorithmParameters">
      <summary>X.509v3 인증서에 대한 키 알고리즘 매개 변수(바이트 배열)를 반환합니다.</summary>
      <returns>Authenticode X.509 v.3 인증서에 대한 키 알고리즘 매개 변수(바이트 배열)입니다.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">인증서 컨텍스트가 잘못된 경우</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetKeyAlgorithmParametersString">
      <summary>X.509v3 인증서에 대한 키 알고리즘 매개 변수(16진 문자열)를 반환합니다.</summary>
      <returns>Authenticode X.509 v.3 인증서에 대한 키 알고리즘 매개 변수(16진 문자열)입니다.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">인증서 컨텍스트가 잘못된 경우</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetPublicKey">
      <summary>X.509v3 인증서에 대한 공개 키(바이트 배열)를 반환합니다.</summary>
      <returns>Authenticode X.509 v.3 인증서에 대한 공개 키(바이트 배열)입니다.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">인증서 컨텍스트가 잘못된 경우</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetSerialNumber">
      <summary>X.509v3 인증서의 일련 번호(바이트 배열)를 반환합니다.</summary>
      <returns>Authenticode X.509 v.3 인증서의 일련 번호(바이트 배열)입니다.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">인증서 컨텍스트가 잘못된 경우</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate.Handle">
      <summary>[보안 중요] 비관리 PCCERT_CONTEXT 구조체로 설명되는 Microsoft 암호화 API 인증서 컨텍스트에 대한 핸들을 가져옵니다. </summary>
      <returns>비관리 PCCERT_CONTEXT 구조체를 나타내는 <see cref="T:System.IntPtr" /> 구조체입니다.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate.Issuer">
      <summary>X.509v3 인증서를 발급한 인증 기관의 이름을 가져옵니다.</summary>
      <returns>X.509v3 인증서를 발급한 인증 기관의 이름입니다.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">인증서 핸들이 잘못된 경우</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate.Subject">
      <summary>인증서에서 구별된 주체 이름을 가져옵니다.</summary>
      <returns>인증서에서 구별된 주체 이름입니다.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">인증서 핸들이 잘못된 경우</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.ToString">
      <summary>현재 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 개체의 문자열 표현을 반환합니다.</summary>
      <returns>현재 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 개체의 문자열 표현입니다.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.ToString(System.Boolean)">
      <summary>지정되면 추가 정보와 함께 현재 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 개체의 문자열 표현을 반환합니다.</summary>
      <returns>현재 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 개체의 문자열 표현입니다.</returns>
      <param name="fVerbose">상세한 문자열 표현을 나타내려면 true이고, 그렇지 않으면 false입니다. </param>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Certificate2">
      <summary>X.509 인증서를 나타냅니다.  </summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.Byte[])">
      <summary>바이트 배열의 정보를 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="rawData">X.509 인증서의 데이터가 들어 있는 바이트 배열입니다. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">인증서 관련 오류가 발생하는 경우.예를 들면 다음과 같습니다.인증서 파일이 없는 경우인증서가 잘못된 경우인증서의 암호가 잘못된 경우</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.Byte[],System.String)">
      <summary>바이트 배열 및 암호를 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="rawData">X.509 인증서의 데이터가 들어 있는 바이트 배열입니다. </param>
      <param name="password">X.509 인증서 데이터에 액세스하는 데 필요한 암호입니다. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">인증서 관련 오류가 발생하는 경우.예를 들면 다음과 같습니다.인증서 파일이 없는 경우인증서가 잘못된 경우인증서의 암호가 잘못된 경우</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.Byte[],System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>바이트 배열, 암호 및 키 저장소 플래그를 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="rawData">X.509 인증서의 데이터가 들어 있는 바이트 배열입니다. </param>
      <param name="password">X.509 인증서 데이터에 액세스하는 데 필요한 암호입니다. </param>
      <param name="keyStorageFlags">인증서를 가져올 위치 및 방법을 제어하는 열거형 값의 비트 조합입니다. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">인증서 관련 오류가 발생하는 경우.예를 들면 다음과 같습니다.인증서 파일이 없는 경우인증서가 잘못된 경우인증서의 암호가 잘못된 경우</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.IntPtr)">
      <summary>관리되지 않는 핸들을 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="handle">비관리 코드의 인증서 컨텍스트에 대한 포인터입니다.이러한 C 구조를 PCCERT_CONTEXT라고 합니다.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">인증서 관련 오류가 발생하는 경우.예를 들면 다음과 같습니다.인증서 파일이 없는 경우인증서가 잘못된 경우인증서의 암호가 잘못된 경우</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.String)">
      <summary>인증서 파일 이름을 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="fileName">인증서 파일의 이름입니다. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">인증서 관련 오류가 발생하는 경우.예를 들면 다음과 같습니다.인증서 파일이 없는 경우인증서가 잘못된 경우인증서의 암호가 잘못된 경우</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.String,System.String)">
      <summary>인증서 파일 이름과 인증서 액세스에 사용되는 암호를 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="fileName">인증서 파일의 이름입니다. </param>
      <param name="password">X.509 인증서 데이터에 액세스하는 데 필요한 암호입니다. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">인증서 관련 오류가 발생하는 경우.예를 들면 다음과 같습니다.인증서 파일이 없는 경우인증서가 잘못된 경우인증서의 암호가 잘못된 경우</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.String,System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>인증서 파일 이름, 인증서 액세스에 사용되는 암호 및 키 저장소 플래그를 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="fileName">인증서 파일의 이름입니다. </param>
      <param name="password">X.509 인증서 데이터에 액세스하는 데 필요한 암호입니다. </param>
      <param name="keyStorageFlags">인증서를 가져올 위치 및 방법을 제어하는 열거형 값의 비트 조합입니다. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">인증서 관련 오류가 발생하는 경우.예를 들면 다음과 같습니다.인증서 파일이 없는 경우인증서가 잘못된 경우인증서의 암호가 잘못된 경우</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.Archived">
      <summary>X.509 인증서가 보관됨을 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>인증서가 보관되면 true이고, 인증서가 보관되지 않으면 false입니다.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">인증서를 읽을 수 없는 경우 </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.Extensions">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 개체의 컬렉션을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 개체</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">인증서를 읽을 수 없는 경우 </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.FriendlyName">
      <summary>인증서의 관련 별칭을 가져오거나 설정합니다.</summary>
      <returns>인증서의 이름입니다.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">인증서를 읽을 수 없는 경우 </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.GetCertContentType(System.Byte[])">
      <summary>바이트 배열에 포함된 인증서 형식을 나타냅니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> 개체</returns>
      <param name="rawData">X.509 인증서의 데이터가 들어 있는 바이트 배열입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="rawData" />의 길이가 0이거나 null인 경우 </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.GetCertContentType(System.String)">
      <summary>파일에 포함된 인증서 형식을 나타냅니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> 개체</returns>
      <param name="fileName">인증서 파일의 이름입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="fileName" />가 null입니다.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.GetNameInfo(System.Security.Cryptography.X509Certificates.X509NameType,System.Boolean)">
      <summary>인증서에서 주체와 발급자 이름을 가져옵니다.</summary>
      <returns>인증서 이름입니다.</returns>
      <param name="nameType">주체에 대한 <see cref="T:System.Security.Cryptography.X509Certificates.X509NameType" /> 값입니다. </param>
      <param name="forIssuer">발급자 이름을 포함하려면 true이고, 그렇지 않으면 false입니다. </param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.HasPrivateKey">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 개체에 개인 키가 들어 있는지 여부를 나타내는 값을 가져옵니다. </summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 개체에 개인 키가 들어 있으면 true이고, 그렇지 않으면 false입니다. </returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">인증서 컨텍스트가 잘못된 경우</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.IssuerName">
      <summary>인증서 발급자의 고유 이름을 가져옵니다.</summary>
      <returns>인증서 발급자의 이름이 들어 있는 <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> 개체입니다.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">인증서 컨텍스트가 잘못된 경우</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.NotAfter">
      <summary>인증서가 더 이상 유효하지 않은 현지 시간 날짜를 가져옵니다.</summary>
      <returns>인증서의 만료 날짜를 나타내는 <see cref="T:System.DateTime" /> 개체입니다.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">인증서를 읽을 수 없는 경우 </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.NotBefore">
      <summary>인증서를 사용할 수 있게 되는 현지 시간 날짜를 가져옵니다.</summary>
      <returns>인증서의 개시 날짜를 나타내는 <see cref="T:System.DateTime" /> 개체입니다.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">인증서를 읽을 수 없는 경우 </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.PrivateKey">
      <summary>인증서와 관련된 개인 키를 나타내는 <see cref="T:System.Security.Cryptography.AsymmetricAlgorithm" /> 개체를 가져오거나 설정합니다.</summary>
      <returns>RSA 또는 DSA 암호화 서비스 공급자인 <see cref="T:System.Security.Cryptography.AsymmetricAlgorithm" /> 개체입니다.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">키 값이 RSA 또는 DSA 키가 아니거나 키를 읽을 수 없는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">이 속성에 대해 설정되는 값이 null인 경우</exception>
      <exception cref="T:System.NotSupportedException">이 개인 키에 대한 키 알고리즘이 지원되지 않는 경우</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicUnexpectedOperationException">X.509 키가 일치하지 않는 경우</exception>
      <exception cref="T:System.ArgumentException">암호화 서비스 공급자 키가 null인 경우</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.PublicKey">
      <summary>인증서와 관련된 <see cref="P:System.Security.Cryptography.X509Certificates.X509Certificate2.PublicKey" /> 개체를 가져옵니다.</summary>
      <returns>
        <see cref="P:System.Security.Cryptography.X509Certificates.X509Certificate2.PublicKey" /> 개체</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">키 값이 RSA 또는 DSA 키가 아니거나 키를 읽을 수 없는 경우 </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.RawData">
      <summary>인증서의 원시 데이터를 가져옵니다.</summary>
      <returns>바이트 배열로 나타낸 인증서의 원시 데이터입니다.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.SerialNumber">
      <summary>인증서의 일련 번호를 가져옵니다.</summary>
      <returns>인증서의 일련 번호입니다.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.SignatureAlgorithm">
      <summary>인증서의 서명을 만드는 데 사용하는 알고리즘을 가져옵니다.</summary>
      <returns>서명 알고리즘의 개체 식별자(<see cref="T:System.Security.Cryptography.Oid" />)를 반환합니다.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">인증서를 읽을 수 없는 경우 </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.SubjectName">
      <summary>인증서에서 주체 고유 이름을 가져옵니다.</summary>
      <returns>인증서 주체의 이름을 나타내는 <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> 개체입니다.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">인증서 컨텍스트가 잘못된 경우</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.Thumbprint">
      <summary>인증서의 지문을 가져옵니다.</summary>
      <returns>인증서의 지문입니다.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.ToString">
      <summary>X.509 인증서를 텍스트 형식으로 표시합니다.</summary>
      <returns>인증서 정보입니다.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.ToString(System.Boolean)">
      <summary>X.509 인증서를 텍스트 형식으로 표시합니다.</summary>
      <returns>인증서 정보입니다.</returns>
      <param name="verbose">공개 키, 개인 키, 확장 등을 표시하려면 true이고, 지문, 일련 번호, 주체 및 발급자 이름 등을 포함하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 클래스와 유사한 정보를 표시하려면 false입니다. </param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.Version">
      <summary>X.509 형식의 인증서 버전을 가져옵니다.</summary>
      <returns>인증서 형식입니다.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">인증서를 읽을 수 없는 경우 </exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 개체의 컬렉션을 나타냅니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.#ctor">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 정보를 사용하지 않고 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 개체를 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="certificate">컬렉션을 시작할 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 개체입니다.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2[])">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 개체의 배열을 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="certificates">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 개체로 이루어진 배열입니다. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2Collection)">
      <summary>지정된 인증서 컬렉션을 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="certificates">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 개체 </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Add(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>개체를 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />의 끝 부분에 추가합니다.</summary>
      <returns>
        <paramref name="certificate" />가 추가된 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 인덱스입니다.</returns>
      <param name="certificate">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 개체로 표시되는 X.509 인증서입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" />가 null입니다. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.AddRange(System.Security.Cryptography.X509Certificates.X509Certificate2[])">
      <summary>배열의 여러 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 개체를 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 개체에 추가합니다.</summary>
      <param name="certificates">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 개체로 이루어진 배열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificates" />가 null입니다. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.AddRange(System.Security.Cryptography.X509Certificates.X509Certificate2Collection)">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 개체의 여러 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 개체를 다른 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 개체에 추가합니다.</summary>
      <param name="certificates">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 개체 </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificates" />가 null입니다. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Contains(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 개체에 특정 인증서가 포함되어 있는지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />에 지정된 <paramref name="certificate" />가 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="certificate">컬렉션에서 찾을 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 개체입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" />가 null입니다. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Export(System.Security.Cryptography.X509Certificates.X509ContentType)">
      <summary>X.509 인증서 정보를 바이트 배열로 내보냅니다.</summary>
      <returns>바이트 배열의 X.509 인증서 정보입니다.</returns>
      <param name="contentType">지원되는 <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> 개체입니다. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Export(System.Security.Cryptography.X509Certificates.X509ContentType,System.String)">
      <summary>암호를 사용하여 X.509 인증서 정보를 바이트 배열로 내보냅니다.</summary>
      <returns>바이트 배열의 X.509 인증서 정보입니다.</returns>
      <param name="contentType">지원되는 <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> 개체입니다. </param>
      <param name="password">바이트 배열 보호에 사용되는 문자열입니다. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">이 인증서를 읽을 수 없거나, 콘텐츠가 잘못되었거나, 인증서에 암호가 필요한데 제공된 암호가 잘못되어 개인 키를 내보낼 수 없는 경우 </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509FindType" /> 열거형 및 <paramref name="findValue" /> 개체로 지정된 검색 조건을 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 개체를 검색합니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 개체</returns>
      <param name="findType">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509FindType" /> 값 중 하나입니다. </param>
      <param name="findValue">개체로서의 검색 조건입니다. </param>
      <param name="validOnly">검색에서 유효한 인증서만 반환하려면 true이고, 그렇지 않으면 false입니다. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="findType" />이(가) 잘못되었습니다. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.GetEnumerator">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 개체 전체에서 반복할 수 있는 열거자를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 개체 전체에서 반복할 수 있는 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator" /> 개체입니다.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Import(System.Byte[])">
      <summary>바이트 배열 형식의 인증서를 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 개체로 가져옵니다.</summary>
      <param name="rawData">X.509 인증서의 데이터가 들어 있는 바이트 배열입니다. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Import(System.Byte[],System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>인증서에 액세스하기 위해 암호를 요구하는 바이트 배열 형식의 인증서를 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 개체로 가져옵니다.</summary>
      <param name="rawData">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 개체의 데이터가 들어 있는 바이트 배열입니다. </param>
      <param name="password">인증서 정보에 액세스하는 데 필요한 암호입니다. </param>
      <param name="keyStorageFlags">인증서를 가져올 위치 및 방법을 제어하는 열거형 값의 비트 조합 </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Import(System.String)">
      <summary>인증서 파일을 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 개체로 가져옵니다.</summary>
      <param name="fileName">인증서 정보를 포함하는 파일의 이름입니다. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Import(System.String,System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>암호가 필요한 인증서 파일을 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 개체로 가져옵니다.</summary>
      <param name="fileName">인증서 정보를 포함하는 파일의 이름입니다. </param>
      <param name="password">인증서 정보에 액세스하는 데 필요한 암호입니다. </param>
      <param name="keyStorageFlags">인증서를 가져올 위치 및 방법을 제어하는 열거형 값의 비트 조합 </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Insert(System.Int32,System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>개체를 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 개체 내의 지정된 인덱스에 삽입합니다.</summary>
      <param name="index">
        <paramref name="certificate" />를 삽입할 인덱스(0부터 시작)입니다. </param>
      <param name="certificate">삽입할 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 개체입니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작은 경우또는 <paramref name="index" />가 <see cref="P:System.Collections.CollectionBase.Count" /> 속성보다 큰 경우 </exception>
      <exception cref="T:System.NotSupportedException">컬렉션이 읽기 전용입니다.또는 컬렉션이 고정 크기를 갖는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" />가 null입니다. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Item(System.Int32)">
      <summary>지정된 인덱스에 있는 요소를 가져오거나 설정합니다.</summary>
      <returns>지정된 인덱스의 요소입니다.</returns>
      <param name="index">가져오거나 설정할 요소의 인덱스(0부터 시작)입니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작은 경우또는 <paramref name="index" />가 <see cref="P:System.Collections.CollectionBase.Count" /> 속성보다 크거나 같은 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="index" />가 null입니다. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Remove(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 개체에서 맨 처음 발견되는 인증서를 제거합니다.</summary>
      <param name="certificate">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 개체에서 제거할 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 개체입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" />가 null입니다. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.RemoveRange(System.Security.Cryptography.X509Certificates.X509Certificate2[])">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 개체에서 배열의 여러 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 개체를 제거합니다.</summary>
      <param name="certificates">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 개체로 이루어진 배열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificates" />가 null입니다. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.RemoveRange(System.Security.Cryptography.X509Certificates.X509Certificate2Collection)">
      <summary>다른 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 개체에서 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 개체의 여러 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 개체를 제거합니다.</summary>
      <param name="certificates">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 개체 </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificates" />가 null입니다. </exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 개체의 단순 반복을 지원합니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.Current">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 개체의 현재 요소를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 개체의 현재 요소입니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 컬렉션의 첫 번째 요소 앞 또는 마지막 요소 뒤에 배치되는 경우 </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.MoveNext">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 개체의 다음 요소로 열거자를 이동합니다.</summary>
      <returns>열거자가 다음 요소로 이동한 경우 true가 반환되고, 컬렉션의 끝을 지난 경우 false가 반환됩니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.Reset">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 개체의 첫 번째 요소 앞의 초기 위치로 열거자를 설정합니다.</summary>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.System#Collections#IEnumerator#Current">
      <summary>이 멤버에 대한 설명은 <see cref="P:System.Collections.IEnumerator.Current" />를 참조하십시오.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 개체의 현재 요소입니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 컬렉션의 첫 번째 요소 앞 또는 마지막 요소 뒤에 배치되는 경우 </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.System#Collections#IEnumerator#MoveNext">
      <summary>이 멤버에 대한 설명은 <see cref="M:System.Collections.IEnumerator.MoveNext" />를 참조하십시오.</summary>
      <returns>열거자가 다음 요소로 이동한 경우 true가 반환되고, 컬렉션의 끝을 지난 경우 false가 반환됩니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.System#Collections#IEnumerator#Reset">
      <summary>이 멤버에 대한 설명은 <see cref="M:System.Collections.IEnumerator.Reset" />를 참조하십시오.</summary>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 개체를 저장하는 컬렉션을 정의합니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.#ctor">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate[])">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 개체의 배열에서 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="value">새 개체를 초기화하는 데 사용할 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 개체의 배열입니다. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.#ctor(System.Security.Cryptography.X509Certificates.X509CertificateCollection)">
      <summary>다른 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />에서 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="value">새 개체를 초기화하는 데 사용할 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />입니다. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Add(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>지정된 값을 갖는 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />를 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />에 추가합니다.</summary>
      <returns>새 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />가 삽입된 현재 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />의 인덱스입니다.</returns>
      <param name="value">현재 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />에 추가할 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />입니다. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.AddRange(System.Security.Cryptography.X509Certificates.X509Certificate[])">
      <summary>현재 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />의 끝으로 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 형식의 배열 요소를 복사합니다.</summary>
      <param name="value">현재 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />에 추가할 개체를 포함하는 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 형식의 배열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 매개 변수가 null인 경우 </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.AddRange(System.Security.Cryptography.X509Certificates.X509CertificateCollection)">
      <summary>지정된 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />의 요소를 현재 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />의 끝에 복사합니다.</summary>
      <param name="value">컬렉션에 추가할 개체를 포함하는 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 매개 변수가 null인 경우 </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Clear"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Contains(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>현재 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />에 지정된 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />가 포함되는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />가 이 컬렉션에 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="value">찾을 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />입니다. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.CopyTo(System.Security.Cryptography.X509Certificates.X509Certificate[],System.Int32)">
      <summary>지정한 인덱스에서 현재 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />의 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 값을 일차원 <see cref="T:System.Array" /> 인스턴스에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />에서 복사한 값의 대상인 일차원 <see cref="T:System.Array" />입니다. </param>
      <param name="index">복사를 시작할 <paramref name="array" />의 인덱스입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> 매개 변수가 다차원 배열인 경우또는 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />의 요소 수가 <paramref name="arrayIndex" />와 <paramref name="array" /> 끝 사이의 사용 가능한 공간보다 큰 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 매개 변수가 null입니다. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> 매개 변수가 <paramref name="array" /> 매개 변수의 하한보다 작은 경우 </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Count"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.GetEnumerator">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />에서 반복할 수 있는 열거자를 반환합니다.</summary>
      <returns>컬렉션 전체에 걸쳐 반복하는 데 사용할 수 있는 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 하위 요소의 열거자입니다.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.GetHashCode">
      <summary>현재 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />에 포함된 모든 값을 기반으로 하는 해시 값을 작성합니다.</summary>
      <returns>현재 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />에 포함된 모든 값을 기반으로 하는 해시 값입니다.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.IndexOf(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>현재 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />에서 지정된 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />의 인덱스를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />에 <paramref name="value" /> 매개 변수에서 지정한 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />의 인덱스가 있으면 그 인덱스이고, 그렇지 않으면 -1입니다.</returns>
      <param name="value">찾을 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />입니다. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Insert(System.Int32,System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>지정된 인덱스에 있는 현재 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />에 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />를 삽입합니다.</summary>
      <param name="index">
        <paramref name="value" />를 삽입할 0부터 시작하는 인덱스입니다. </param>
      <param name="value">삽입할 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />입니다. </param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Item(System.Int32)">
      <summary>현재 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />의 지정된 인덱스에 있는 엔트리를 가져오거나 설정합니다.</summary>
      <returns>현재 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />의 지정된 인덱스에 있는 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />입니다.</returns>
      <param name="index">현재 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />이 있는 엔트리의 0부터 시작하는 인덱스입니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 매개 변수가 컬렉션의 유효한 인덱스 범위 밖에 있는 경우 </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Remove(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>현재 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />에서 특정 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />를 제거합니다.</summary>
      <param name="value">현재 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />에서 제거할 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> 매개 변수에서 지정한 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />가 현재 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />에 없는 경우 </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.RemoveAt(System.Int32)"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)"></member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#ICollection#IsSynchronized"></member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#ICollection#SyncRoot"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IEnumerable#GetEnumerator"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#Add(System.Object)"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#Contains(System.Object)"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#IndexOf(System.Object)"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#Insert(System.Int32,System.Object)"></member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#IsFixedSize"></member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#IsReadOnly"></member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#Item(System.Int32)"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#Remove(System.Object)"></member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />에 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 개체를 열거합니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.#ctor(System.Security.Cryptography.X509Certificates.X509CertificateCollection)">
      <summary>지정된 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />에 대한 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="mappings">열거할 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />입니다. </param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.Current">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />의 현재 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />의 현재 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />입니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 컬렉션의 첫 번째 요소 앞 또는 마지막 요소 뒤에 배치되는 경우 </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.MoveNext">
      <summary>열거자를 컬렉션의 다음 요소로 이동합니다.</summary>
      <returns>열거자가 다음 요소로 이동한 경우 true가 반환되고, 컬렉션의 끝을 지난 경우 false가 반환됩니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 인스턴스화된 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.Reset">
      <summary>컬렉션의 첫 번째 요소 앞의 초기 위치에 열거자를 설정합니다.</summary>
      <exception cref="T:System.InvalidOperationException">열거자가 인스턴스화된 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.System#Collections#IEnumerator#Current">
      <summary>이 멤버에 대한 설명은 <see cref="P:System.Collections.IEnumerator.Current" />를 참조하십시오.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 개체의 현재 X.509 인증서 개체입니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 컬렉션의 첫 번째 요소 앞 또는 마지막 요소 뒤에 배치되는 경우 </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.System#Collections#IEnumerator#MoveNext">
      <summary>이 멤버에 대한 설명은 <see cref="M:System.Collections.IEnumerator.MoveNext" />를 참조하십시오.</summary>
      <returns>열거자가 다음 요소로 이동한 경우 true가 반환되고, 컬렉션의 끝을 지난 경우 false가 반환됩니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 인스턴스화된 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.System#Collections#IEnumerator#Reset">
      <summary>이 멤버에 대한 설명은 <see cref="M:System.Collections.IEnumerator.Reset" />를 참조하십시오.</summary>
      <exception cref="T:System.InvalidOperationException">열거자가 인스턴스화된 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Chain">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 인증서에 대한 체인 빌딩 엔진을 나타냅니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Chain.#ctor">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Chain.Build(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" />에 지정된 정책을 사용하여 X.509 체인을 빌드합니다.</summary>
      <returns>X.509 인증서가 유효하면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="certificate">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 개체입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="certificate" />가 유효한 인증서가 아니거나 null인 경우 </exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="certificate" />를 읽을 수 없는 경우 </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Chain.ChainElements">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" /> 개체의 컬렉션입니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 개체입니다.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Chain.ChainPolicy">
      <summary>X.509 인증서 체인 빌드 시 사용할 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" />를 가져오거나 설정합니다.</summary>
      <returns>이 X.509 체인과 관련된 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" /> 개체입니다.</returns>
      <exception cref="T:System.ArgumentNullException">이 속성에 대해 설정되는 값이 null인 경우</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Chain.ChainStatus">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" /> 개체에 포함된 각 요소의 상태를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainStatus" /> 개체의 배열입니다.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Chain.Dispose">
      <summary>이 <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" />에서 사용하는 리소스를 모두 해제합니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Chain.Dispose(System.Boolean)">
      <summary>이 <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" />에서 사용하는 관리되지 않는 리소스를 해제하고, 관리되는 리소스를 선택적으로 해제할 수 있습니다.</summary>
      <param name="disposing">관리되는 리소스와 관리되지 않는 리소스를 모두 해제하려면 true로 설정하고, 관리되지 않는 리소스만 해제하려면 false로 설정합니다.</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Chain.SafeHandle">
      <summary>이 <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" /> 인스턴스의 SafeHandle을 가져옵니다. </summary>
      <returns>
        <see cref="T:Microsoft.Win32.SafeHandles.SafeX509ChainHandle" />를 반환합니다.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainElement">
      <summary>X.509 체인의 요소를 나타냅니다.</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElement.Certificate">
      <summary>특정 체인 요소의 X.509 인증서를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 개체</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElement.ChainElementStatus">
      <summary>체인에 있는 현재 X.509 인증서의 오류 상태를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainStatus" /> 개체로 이루어진 배열입니다.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElement.Information">
      <summary>비관리 인증서 체인 구조체에서 추가 오류 정보를 가져옵니다.</summary>
      <returns>암호화 API에서 비관리 CERT_CHAIN_ELEMENT 구조체의 pwszExtendedErrorInfo 멤버를 나타내는 문자열입니다.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" /> 개체의 컬렉션을 나타냅니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.CopyTo(System.Security.Cryptography.X509Certificates.X509ChainElement[],System.Int32)">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 개체를 지정된 인덱스에서 시작하여 배열에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" /> 개체로 이루어진 배열입니다. </param>
      <param name="index">인덱스 값을 나타내는 정수입니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">지정된 <paramref name="index" />가 0보다 작거나 배열의 길이보다 크거나 같은 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null입니다. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" />에 현재 개수를 더한 값이 배열의 길이보다 큰 경우 </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.Count">
      <summary>컬렉션에 있는 요소의 수를 가져옵니다.</summary>
      <returns>컬렉션의 요소 수를 나타내는 정수입니다.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.GetEnumerator">
      <summary>체인 요소 컬렉션을 탐색하는 데 사용할 수 있는 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator" /> 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator" /> 개체</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.IsSynchronized">
      <summary>체인 요소 컬렉션이 동기화되는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>항상 false를 반환합니다.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.Item(System.Int32)">
      <summary>지정된 인덱스의 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" /> 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" /> 개체</returns>
      <param name="index">정수 값입니다. </param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="index" />가 0보다 작은 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 컬렉션의 길이보다 크거나 같은 경우 </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.SyncRoot">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 개체에 대한 액세스를 동기화하는 데 사용할 수 있는 개체를 가져옵니다.</summary>
      <returns>현재 개체에 대한 포인터 참조입니다.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 개체를 지정된 인덱스에서 시작하여 배열에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 개체를 복사할 대상 배열입니다.</param>
      <param name="index">복사를 시작할 <paramref name="array" />의 인덱스입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">지정된 <paramref name="index" />가 0보다 작거나 배열의 길이보다 크거나 같은 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null입니다. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" />에 현재 개수를 더한 값이 배열의 길이보다 큰 경우 </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>체인 요소 컬렉션을 탐색하는 데 사용할 수 있는 <see cref="T:System.Collections.IEnumerator" /> 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> 개체</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />에서 단순하게 반복할 수 있도록 지원합니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator.Current">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />의 현재 요소를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />의 현재 요소입니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 컬렉션의 첫 번째 요소 앞 또는 마지막 요소 뒤에 배치되는 경우 </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator.MoveNext">
      <summary>열거자를 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />의 다음 요소로 이동합니다.</summary>
      <returns>열거자가 다음 요소로 이동한 경우 true가 반환되고, 컬렉션의 끝을 지난 경우 false가 반환됩니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator.Reset">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />의 첫 번째 요소 앞의 초기 위치에 열거자를 설정합니다.</summary>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator.System#Collections#IEnumerator#Current">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />의 현재 요소를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />의 현재 요소입니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 컬렉션의 첫 번째 요소 앞 또는 마지막 요소 뒤에 배치되는 경우 </exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy">
      <summary>X509 인증서 체인을 만들 때 적용할 체인 정책을 나타냅니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainPolicy.#ctor">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.ApplicationPolicy">
      <summary>인증서가 지원하는 응용 프로그램 정책이나 EKU(Enhanced Key Usage)를 지정하는 OID(개체 식별자) 컬렉션을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.OidCollection" /> 개체입니다.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.CertificatePolicy">
      <summary>인증서가 지원하는 인증서 정책을 지정하는 OID(개체 식별자) 컬렉션을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.OidCollection" /> 개체</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.ExtraStore">
      <summary>인증서 체인의 유효성을 검사할 때 체인 엔진에서 검색할 수 있는 추가 인증서 컬렉션을 나타냅니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 개체</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainPolicy.Reset">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" /> 멤버를 기본값으로 다시 설정합니다.</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.RevocationFlag">
      <summary>X509 해지 플래그에 대한 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509RevocationFlag" /> 개체</returns>
      <exception cref="T:System.ArgumentException">제공된 <see cref="T:System.Security.Cryptography.X509Certificates.X509RevocationFlag" /> 값이 유효한 플래그가 아닌 경우 </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.RevocationMode">
      <summary>X509 인증서 해지 모드에 대한 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509RevocationMode" /> 개체</returns>
      <exception cref="T:System.ArgumentException">제공된 <see cref="T:System.Security.Cryptography.X509Certificates.X509RevocationMode" /> 값이 유효한 플래그가 아닌 경우 </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.UrlRetrievalTimeout">
      <summary>온라인 해지 확인 또는 CRL(인증서 해지 목록) 다운로드 중에 경과된 기간을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.TimeSpan" /> 개체</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.VerificationFlags">
      <summary>인증서에 대한 확인 플래그를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509VerificationFlags" /> 열거형의 값입니다.</returns>
      <exception cref="T:System.ArgumentException">제공된 <see cref="T:System.Security.Cryptography.X509Certificates.X509VerificationFlags" /> 값이 유효한 플래그가 아닌 경우기본값은 <see cref="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.NoFlag" />입니다.</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.VerificationTime">
      <summary>인증서가 확인된 현지 시간입니다.</summary>
      <returns>
        <see cref="T:System.DateTime" /> 개체</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainStatus">
      <summary>X509 체인 상태와 오류 정보를 저장하기 위한 간단한 구조를 제공합니다.</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainStatus.Status">
      <summary>X509 체인의 상태를 지정합니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags" /> 값입니다.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainStatus.StatusInformation">
      <summary>
        <see cref="P:System.Security.Cryptography.X509Certificates.X509Chain.ChainStatus" /> 값에 대한 설명을 지정합니다.</summary>
      <returns>지역화할 수 있는 문자열입니다.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags">
      <summary>X509 체인의 상태를 정의합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.CtlNotSignatureValid">
      <summary>CTL(인증서 신뢰 목록)에 잘못된 서명이 포함되어 있음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.CtlNotTimeValid">
      <summary>CTL(인증서 신뢰 목록)이 만료된 경우 등과 같이 잘못된 시간 값으로 인해 CTL이 유효하지 않음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.CtlNotValidForUsage">
      <summary>이 용도에 CTL(인증서 신뢰 목록)이 유효하지 않음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.Cyclic">
      <summary>X509 체인을 빌드할 수 없음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.HasExcludedNameConstraint">
      <summary>인증서에서 이름 제약 조건이 제외되었으므로 X509 체인이 유효하지 않음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.HasNotDefinedNameConstraint">
      <summary>인증서에 정의되지 않은 이름 제약 조건이 있음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.HasNotPermittedNameConstraint">
      <summary>인증서에 허용되지 않는 이름 제약 조건이 있음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.HasNotSupportedNameConstraint">
      <summary>인증서에 지원되는 이름 제약 조건이 없거나 지원되지 않는 이름 제약 조건이 있음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.InvalidBasicConstraints">
      <summary>잘못된 기본 제약 조건으로 인해 X509 체인이 유효하지 않음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.InvalidExtension">
      <summary>잘못된 확장으로 인해 X509 체인이 유효하지 않음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.InvalidNameConstraints">
      <summary>잘못된 이름 제약 조건으로 인해 X509 체인이 유효하지 않음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.InvalidPolicyConstraints">
      <summary>잘못된 정책 제약 조건으로 인해 X509 체인이 유효하지 않음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NoError">
      <summary>X509 체인에 오류가 없음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NoIssuanceChainPolicy">
      <summary>인증서에 인증서 정책 확장이 없음을 나타냅니다.이 오류는 모든 인증서에 인증서 정책이 있도록 그룹 정책을 지정한 경우에 발생할 수 있습니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NotSignatureValid">
      <summary>잘못된 인증서 서명으로 인해 X509 체인이 유효하지 않음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NotTimeNested">
      <summary>사용되지 않습니다.CA(인증 기관) 인증서와 발급된 인증서의 기간이 서로 일치하지 않아도 유효하도록 지정합니다.예를 들어, CA 인증서는 1월 1일부터 12월 1일까지 유효하고 발급된 인증서는 1월 2일부터 12월 2일까지 유효한 경우 이는 유효 기간이 일치하지 않음을 의미합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NotTimeValid">
      <summary>인증서가 만료되었음을 의미하는 값 등과 같이 유효하지 않은 시간 값으로 인해 X509 체인이 유효하지 않음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NotValidForUsage">
      <summary>키 용도가 유효하지 않음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.OfflineRevocation">
      <summary>X509 체인에 사용되는 온라인 CRL(인증서 해지 목록)이 현재 오프라인 상태임을 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.PartialChain">
      <summary>루트 인증서까지 X509 체인을 빌드할 수 없음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.RevocationStatusUnknown">
      <summary>인증서가 해지되었는지 여부를 확인할 수 없음을 나타냅니다.CRL(인증서 해지 목록)이 오프라인 상태이거나 사용할 수 없기 때문일 수 있습니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.Revoked">
      <summary>인증서의 해지로 인해 X509 체인이 유효하지 않음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.UntrustedRoot">
      <summary>신뢰할 수 없는 루트 인증서로 인해 X509 체인이 유효하지 않음을 나타냅니다.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ContentType">
      <summary>X.509 인증서의 형식을 지정합니다. </summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Authenticode">
      <summary>Authenticode X.509 인증서입니다. </summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Cert">
      <summary>단일 X.509 인증서입니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pfx">
      <summary>PFX 형식의 인증서입니다.Pfx 값은 Pkcs12 값과 동일합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pkcs12">
      <summary>PKCS #12 형식의 인증서입니다.Pkcs12 값은 Pfx 값과 동일합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pkcs7">
      <summary>PKCS #7 형식의 인증서입니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.SerializedCert">
      <summary>serialize된 단일 X.509 인증서입니다. </summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.SerializedStore">
      <summary>serialize된 저장소입니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Unknown">
      <summary>알 수 없는 X.509 인증서입니다.  </summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension">
      <summary>키를 사용하는 응용 프로그램을 나타내는 OID(개체 식별자) 컬렉션을 정의합니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.#ctor">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>
        <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 개체와 확장의 중요 여부를 식별하는 값을 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="encodedEnhancedKeyUsages">확장을 만드는 데 사용할 인코딩된 데이터입니다.</param>
      <param name="critical">중요한 확장이면 true이고, 그렇지 않으면 false입니다.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.#ctor(System.Security.Cryptography.OidCollection,System.Boolean)">
      <summary>
        <see cref="T:System.Security.Cryptography.OidCollection" />과 확장의 중요 여부를 식별하는 값을 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
      <param name="enhancedKeyUsages">
        <see cref="T:System.Security.Cryptography.OidCollection" /> 컬렉션입니다. </param>
      <param name="critical">중요한 확장이면 true이고, 그렇지 않으면 false입니다.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">지정한 <see cref="T:System.Security.Cryptography.OidCollection" />에 손상된 값이 하나 이상 포함된 경우</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>
        <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 개체를 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="asnEncodedData">확장을 만드는 데 사용할 인코딩된 데이터입니다.</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.EnhancedKeyUsages">
      <summary>키를 사용하는 응용 프로그램을 나타내는 OID(개체 식별자) 컬렉션을 가져옵니다.</summary>
      <returns>키를 사용하는 응용 프로그램을 나타내는 <see cref="T:System.Security.Cryptography.OidCollection" /> 개체입니다.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Extension">
      <summary>X509 확장을 나타냅니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.#ctor">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="encodedExtension">확장을 만드는 데 사용할 인코딩된 데이터입니다.</param>
      <param name="critical">중요한 확장이면 true이고, 그렇지 않으면 false입니다.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.#ctor(System.Security.Cryptography.Oid,System.Byte[],System.Boolean)">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="oid">확장을 식별하는 데 사용되는 개체 식별자입니다.</param>
      <param name="rawData">확장을 만드는 데 사용할 인코딩된 데이터입니다.</param>
      <param name="critical">중요한 확장이면 true이고, 그렇지 않으면 false입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="oid" />가 null입니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="oid" />이 빈 문자열("")인 경우</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.#ctor(System.String,System.Byte[],System.Boolean)">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="oid">개체 식별자를 나타내는 문자열입니다.</param>
      <param name="rawData">확장을 만드는 데 사용할 인코딩된 데이터입니다.</param>
      <param name="critical">중요한 확장이면 true이고, 그렇지 않으면 false입니다.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>지정된 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 개체의 확장 속성을 복사합니다.</summary>
      <param name="asnEncodedData">복사할 <see cref="T:System.Security.Cryptography.AsnEncodedData" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asnEncodedData" />가 null입니다. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asnEncodedData" />에 유효한 X.509 확장이 없는 경우</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Extension.Critical">
      <summary>확장이 중요한지 여부를 나타내는 부울 값을 가져옵니다.</summary>
      <returns>중요한 확장이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 개체의 컬렉션을 나타냅니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.#ctor">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.Add(System.Security.Cryptography.X509Certificates.X509Extension)">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 개체에 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 개체를 추가합니다.</summary>
      <returns>
        <paramref name="extension" /> 매개 변수가 추가된 인덱스입니다.</returns>
      <param name="extension">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 개체에 추가할 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 개체입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="extension" /> 매개 변수의 값이 null인 경우</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.CopyTo(System.Security.Cryptography.X509Certificates.X509Extension[],System.Int32)">
      <summary>컬렉션을 지정된 인덱스에서 시작하여 배열에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 개체로 이루어진 배열입니다. </param>
      <param name="index">배열에서 복사를 시작할 위치입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" />가 길이가 0인 문자열이거나 잘못된 값을 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="index" />가 null입니다. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 배열의 범위를 벗어난 값을 지정하는 경우 </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.Count">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 개체의 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 개체 수를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 개체의 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 개체 수를 나타내는 정수입니다.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.GetEnumerator">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 개체에서 반복될 수 있는 열거자를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 개체를 반복하는 데 사용할 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator" /> 개체입니다.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.IsSynchronized">
      <summary>컬렉션이 스레드로부터 안전한지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>컬렉션이 스레드로부터 안전하면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.Item(System.Int32)">
      <summary>지정된 인덱스의 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 개체</returns>
      <param name="index">검색할 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 개체의 위치입니다. </param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="index" />가 0보다 작은 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 배열의 길이보다 크거나 같은 경우 </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.Item(System.String)">
      <summary>값 또는 이름이 OID(개체 식별자)로 지정된 첫 번째 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 개체</returns>
      <param name="oid">검색할 확장의 OID(개체 식별자)입니다. </param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.SyncRoot">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 개체에 대한 액세스를 동기화하는 데 사용할 수 있는 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 개체에 대한 액세스를 동기화하는 데 사용할 수 있는 개체입니다.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>컬렉션을 지정된 인덱스에서 시작하여 배열에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 개체로 이루어진 배열입니다. </param>
      <param name="index">배열에서 복사를 시작할 위치입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" />가 길이가 0인 문자열이거나 잘못된 값을 포함하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="index" />가 null입니다. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 배열의 범위를 벗어난 값을 지정하는 경우 </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 개체에서 반복될 수 있는 열거자를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 개체를 반복하는 데 사용할 <see cref="T:System.Collections.IEnumerator" /> 개체입니다.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />에서 단순하게 반복할 수 있도록 지원합니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator.Current">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />의 현재 요소를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />의 현재 요소입니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 컬렉션의 첫 번째 요소 앞 또는 마지막 요소 뒤에 배치되는 경우 </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator.MoveNext">
      <summary>열거자를 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />의 다음 요소로 이동합니다.</summary>
      <returns>열거자가 다음 요소로 이동한 경우 true가 반환되고, 컬렉션의 끝을 지난 경우 false가 반환됩니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator.Reset">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />의 첫 번째 요소 앞의 초기 위치에 열거자를 설정합니다.</summary>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator.System#Collections#IEnumerator#Current">
      <summary>컬렉션의 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />의 현재 요소입니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 컬렉션의 첫 번째 요소 앞 또는 마지막 요소 뒤에 배치되는 경우 </exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509FindType">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 메서드를 사용하여 검색되는 값의 형식을 지정합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByApplicationPolicy">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 메서드에 대한 <paramref name="findValue" /> 매개 변수는 인증서의 개체 식별자(OID 또는 <see cref="T:System.Security.Cryptography.Oid" />)나 응용 프로그램 정책 이름을 나타내는 문자열이어야 합니다.예를 들어, "Encrypting File System" 또는 "1.3.6.1.4.1.311.10.3.4"를 사용할 수 있습니다.지역화할 응용 프로그램의 경우 이름이 지역화되므로 OID 값을 사용해야 합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByCertificatePolicy">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 메서드에 대한 <paramref name="findValue" /> 매개 변수는 인증서 정책의 개체 식별자(OID, 또는 <see cref="T:System.Security.Cryptography.Oid" />)나 이름을 나타내는 문자열이어야 합니다.가장 좋은 방법은 "1.3.6.1.4.1.311.10.3.4"와 같이 OID를 사용하는 것입니다.지역화할 응용 프로그램의 경우 이름이 지역화되므로 OID를 사용해야 합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByExtension">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 메서드에 대한 <paramref name="findValue" /> 매개 변수는 찾으려는 확장명을 설명하는 문자열이어야 합니다.OID(개체 식별자)는 <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 메서드를 사용하여 해당 OID 값이 일치하는 확장이 있는 인증서를 모두 검색하려는 경우에 가장 일반적으로 사용됩니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerDistinguishedName">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 메서드에 대한 <paramref name="findValue" /> 매개 변수는 인증서의 발급자 고유 이름을 나타내는 문자열이어야 합니다.이 경우 <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerName" /> 열거형 값으로 제공된 것보다 검색이 더 구체적입니다.<see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerDistinguishedName" /> 값을 사용하면 <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 메서드에서 전체 고유 이름에 대해 대/소문자를 구분하지 않는 문자열 비교를 수행합니다.발급자 이름을 기준으로 검색하면 정확도가 떨어집니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerName">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 메서드에 대한 <paramref name="findValue" /> 매개 변수는 인증서의 발급자 이름을 나타내는 문자열이어야 합니다.이 경우 <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerDistinguishedName" /> 열거형 값으로 제공된 것보다 검색이 덜 구체적입니다.<see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerName" /> 값을 사용하면 <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 메서드에서 제공된 값으로 대/소문자를 구분하지 않는 문자열 비교를 수행합니다.예를 들어, <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 메서드에 "MyCA"를 전달하면 다른 발급자 값에 상관없이 해당 문자열이 포함된 발급자 이름의 모든 인증서를 찾습니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByKeyUsage">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 메서드에 대한 <paramref name="findValue" /> 매개 변수는 요청된 키 용도가 모두 포함된 비트 마스크를 나타내는 정수이거나 키 용도를 나타내는 문자열이어야 합니다.문자열 값의 경우 키 용도를 한 번에 하나만 지정할 수 있지만 캐스케이딩 시퀀스에 <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 메서드를 사용하면 요청된 용도의 교집합을 구할 수 있습니다.예를 들어, <paramref name="findValue" /> 매개 변수를 "KeyEncipherment"나 정수로 설정할 수 있습니다. 0x30은 "KeyEncipherment" 및 "DataEncipherment"를 나타냅니다.<see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags" /> 열거형의 값을 사용할 수도 있습니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySerialNumber">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 메서드의 <paramref name="findValue" /> 매개 변수는 인증서 대화 상자에 표시된 인증서 일련 번호를 나타내는 공백 없는 문자열 또는 <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetSerialNumberString" /> 메서드에서 반환한 문자열이어야 합니다. </summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectDistinguishedName">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 메서드에 대한 <paramref name="findValue" /> 매개 변수는 인증서의 구별된 주체 이름을 나타내는 문자열이어야 합니다.이 경우 <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectName" /> 열거형 값으로 제공된 것보다 검색이 더 구체적입니다.<see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectDistinguishedName" /> 값을 사용하면 <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 메서드에서 전체 고유 이름에 대해 대/소문자를 구분하지 않는 문자열 비교를 수행합니다.주체 이름을 기준으로 검색하면 정확도가 떨어집니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectKeyIdentifier">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 메서드에 대한 <paramref name="findValue" /> 매개 변수는 "F3E815D45E83B8477B9284113C64EF208E897112" 등과 같이 UI에 표시되는 16진수의 주체 키 식별자를 나타내는 문자열이어야 합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectName">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 메서드에 대한 <paramref name="findValue" /> 매개 변수는 인증서의 주체 이름을 나타내는 문자열이어야 합니다.이 경우 <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectDistinguishedName" /> 열거형 값으로 제공된 것보다 검색이 덜 구체적입니다.<see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectName" /> 값을 사용하면 <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 메서드에서 제공된 값으로 대/소문자를 구분하지 않는 문자열 비교를 수행합니다.예를 들어, <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 메서드에 "MyCert"를 전달하면 다른 주체 값에 상관없이 해당 문자열이 포함된 주체 이름의 모든 인증서를 찾습니다.고유 이름을 기준으로 검색하면 정확도가 향상됩니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTemplateName">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 메서드에 대한 <paramref name="findValue" /> 매개 변수는 "ClientAuth"와 같이 인증서의 템플릿 이름을 나타내는 문자열이어야 합니다.템플릿 이름은 인증서 용도를 지정하는 X509 버전 3 확장입니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByThumbprint">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 메서드에 대한 <paramref name="findValue" /> 매개 변수는 인증서의 지문을 나타내는 문자열이어야 합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeExpired">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 메서드에 대한 <paramref name="findValue" /> 매개 변수는 현지 시간의 <see cref="T:System.DateTime" /> 값이어야 합니다.예를 들어, <see cref="P:System.DateTime.Now" />에 대한 <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 연산의 결과에서 연도 마지막 날의 <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeExpired" />에 대한 <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 연산의 결과를 제거하여 연말까지 유효한 모든 인증서를 찾을 수 있습니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeNotYetValid">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 메서드에 대한 <paramref name="findValue" /> 매개 변수는 현지 시간의 <see cref="T:System.DateTime" /> 값이어야 합니다.값이 미래의 값일 필요는 없습니다.예를 들어, <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeNotYetValid" />를 사용하여 연도 마지막 날의 <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeNotYetValid" />에 대한 <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 연산의 결과와 <see cref="P:System.DateTime.Now" />의 <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeValid" />에 대한 <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 연산의 결과가 교차되는 부분을 가져와서 올해 유효하게 될 인증서를 찾을 수 있습니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeValid">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 메서드에 대한 <paramref name="findValue" /> 매개 변수는 현지 시간의 <see cref="T:System.DateTime" /> 값이어야 합니다.<see cref="P:System.DateTime.Now" />를 사용하여 현재 유효한 모든 인증서를 찾을 수 있습니다.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags">
      <summary>X.509 인증서의 개인 키를 가져올 위치 및 방법을 정의합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.DefaultKeySet">
      <summary>기본 키 설정이 사용됩니다.  일반적으로 사용자 기본 키가 기본 설정입니다. </summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.Exportable">
      <summary>가져온 키가 내보낼 수 있는 키로 표시됩니다.  </summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.MachineKeySet">
      <summary>개인 키가 현재 사용자 저장소가 아닌 로컬 컴퓨터 저장소에 저장됩니다. </summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.PersistKeySet">
      <summary>PFX 파일과 관련된 키가 인증서를 가져올 때 지속됩니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.UserKeySet">
      <summary>개인 키가 로컬 컴퓨터 저장소가 아닌 현재 사용자 저장소에 저장됩니다.인증서에 키가 로컬 컴퓨터 저장소로 이동되도록 지정된 경우에도 이 작업이 수행됩니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.UserProtected">
      <summary>키에 액세스되었음을 대화 상자 또는 다른 방법을 통해 사용자에게 알립니다.  사용 중인 CSP(암호화 서비스 공급자)가 정확한 동작을 정의합니다.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension">
      <summary>X.509 인증서 내에 포함된 키의 용도를 정의합니다.  이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.#ctor">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>
        <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 개체와 확장이 중요한지 여부를 식별하는 값을 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
      <param name="encodedKeyUsage">확장을 만드는 데 사용할 인코딩된 데이터입니다.</param>
      <param name="critical">중요한 확장이면 true이고, 그렇지 않으면 false입니다.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.#ctor(System.Security.Cryptography.X509Certificates.X509KeyUsageFlags,System.Boolean)">
      <summary>지정된 <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags" /> 값과 확장이 중요한지 여부를 식별하는 값을 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
      <param name="keyUsages">키 사용 방법을 설명하는 <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags" /> 값 중 하나입니다.</param>
      <param name="critical">중요한 확장이면 true이고, 그렇지 않으면 false입니다.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>
        <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 개체를 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
      <param name="asnEncodedData">확장을 만드는 데 사용할 인코딩된 데이터입니다.</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.KeyUsages">
      <summary>인증서와 관련된 키 용도 플래그를 가져옵니다.</summary>
      <returns>
        <see cref="P:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.KeyUsages" /> 값 중 하나입니다.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">확장은 디코딩할 수 없는 경우 </exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags">
      <summary>인증서 키의 사용 방법을 정의합니다.이 값을 정의하지 않으면 인증서 키를 어떠한 용도로도 사용할 수 있습니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.CrlSign">
      <summary>키를 CRL(인증서 해지 목록) 서명에 사용할 수 있습니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.DataEncipherment">
      <summary>키를 데이터 암호화에 사용할 수 있습니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.DecipherOnly">
      <summary>키를 암호 해독용으로만 사용할 수 있습니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.DigitalSignature">
      <summary>키를 디지털 서명으로 사용할 수 있습니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.EncipherOnly">
      <summary>키를 암호화용으로만 사용할 수 있습니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.KeyAgreement">
      <summary>Diffie-Hellman 키 계약 알고리즘을 사용하여 만든 키와 같이 키 계약을 확인하는 데 키를 사용할 수 있습니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.KeyCertSign">
      <summary>키를 인증서 서명에 사용할 수 있습니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.KeyEncipherment">
      <summary>키를 키 암호화에 사용할 수 있습니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.None">
      <summary>키 용도 매개 변수가 없습니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.NonRepudiation">
      <summary>키를 인증에 사용할 수 있습니다.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509NameType">
      <summary>X509 인증서에 포함된 이름의 형식을 지정합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.DnsFromAlternativeName">
      <summary>X.509 인증서의 발급자나 주체의 다른 이름과 관련된 DNS 이름입니다.  이 값은 <see cref="F:System.Security.Cryptography.X509Certificates.X509NameType.DnsName" /> 값과 동일합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.DnsName">
      <summary>X509 인증서의 발급자나 주체의 다른 이름과 관련된 DNS 이름입니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.EmailName">
      <summary>X509 인증서의 관련 발급자나 주체에 대한 전자 메일 주소입니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.SimpleName">
      <summary>X509 인증서의 발급자나 주체에 대한 단순한 이름입니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.UpnName">
      <summary>X509 인증서의 발급자나 주체에 대한 UPN 이름입니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.UrlName">
      <summary>X509 인증서의 발급자나 주체의 다른 이름과 관련된 URL 주소입니다.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509RevocationFlag">
      <summary>체인에서 해지 여부를 검사할 X509 인증서를 지정합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationFlag.EndCertificateOnly">
      <summary>최종 인증서의 해지 여부만 검사합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationFlag.EntireChain">
      <summary>전체 인증서 체인의 해지 여부를 검사합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationFlag.ExcludeRoot">
      <summary>루트 인증서를 제외한 전체 체인의 해지 여부를 검사합니다.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509RevocationMode">
      <summary>X509 인증서 해지를 검사하는 데 사용되는 모드를 지정합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationMode.NoCheck">
      <summary>인증서에 대한 해지 검사를 수행하지 않습니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationMode.Offline">
      <summary>캐시된 CRL(인증서 해지 목록)을 사용하여 해지 검사를 수행합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationMode.Online">
      <summary>온라인 CRL(인증서 해지 목록)을 사용하여 해지 검사를 수행합니다.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Store">
      <summary>인증서가 유지 및 관리되는 실제 저장소인 X.509 저장소를 나타냅니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.#ctor">
      <summary>현재 사용자 저장소의 개인 인증서를 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509Store" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.#ctor(System.Security.Cryptography.X509Certificates.StoreName,System.Security.Cryptography.X509Certificates.StoreLocation)">
      <summary>지정된 <see cref="T:System.Security.Cryptography.X509Certificates.StoreName" /> 및 <see cref="T:System.Security.Cryptography.X509Certificates.StoreLocation" /> 값을 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509Store" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="storeName">X.509 인증서 저장소의 이름을 지정하는 열거형 값 중 하나입니다. </param>
      <param name="storeLocation">X.509 인증서 저장소의 위치를 지정하는 열거형 값 중 하나입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="storeLocation" />이 올바른 위치가 아니거나 <paramref name="storeName" />이 올바른 이름이 아닌 경우 </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.#ctor(System.String,System.Security.Cryptography.X509Certificates.StoreLocation)">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.StoreName" /> 열거형의 값과 <see cref="T:System.Security.Cryptography.X509Certificates.StoreLocation" /> 열거형의 값을 나타내는 문자열을 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509Store" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="storeName">
        <see cref="T:System.Security.Cryptography.X509Certificates.StoreName" /> 열거형의 값을 나타내는 문자열입니다. </param>
      <param name="storeLocation">X.509 인증서 저장소의 위치를 지정하는 열거형 값 중 하나입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="storeLocation" />에 잘못된 값이 있는 경우 </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.Add(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>X.509 인증서 저장소에 인증서를 추가합니다.</summary>
      <param name="certificate">추가할 인증서입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" />가 null인 경우 </exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">인증서를 저장소에 추가할 수 없는 경우</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Store.Certificates">
      <summary>X.509 인증서 저장소에 있는 인증서 컬렉션을 반환합니다.</summary>
      <returns>인증서의 컬렉션입니다.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.Dispose">
      <summary>이 사용 되는 리소스를 해제 <see cref="T:System.Security.Cryptography.X509Certificates.X509Store" />.</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Store.Location">
      <summary>X.509 인증서 저장소의 위치를 가져옵니다.</summary>
      <returns>인증서 저장소의 위치입니다.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Store.Name">
      <summary>X.509 인증서 저장소 이름을 가져옵니다.</summary>
      <returns>인증서 저장소의 이름입니다.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.Open(System.Security.Cryptography.X509Certificates.OpenFlags)">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.OpenFlags" /> 플래그 설정에 따라 X.509 인증서 저장소를 열거나 새 저장소를 만듭니다.</summary>
      <param name="flags">X.509 인증서 저장소를 여는 방법을 지정하는 열거형 값의 비트 조합입니다. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">읽을 수 없는 저장소인 경우 </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">저장소에 잘못된 값이 있는 경우</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.Remove(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>X.509 인증서 저장소에서 인증서를 제거합니다.</summary>
      <param name="certificate">제거할 인증서입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" />가 null인 경우 </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension">
      <summary>인증서의 SKI(주체 키 식별자)를 식별하는 문자열을 정의합니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.Byte[],System.Boolean)">
      <summary>바이트 배열 및 확장의 중요 여부를 식별하는 값을 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="subjectKeyIdentifier">확장을 만드는 데 사용할 데이터를 나타내는 바이트 배열입니다.</param>
      <param name="critical">중요한 확장이면 true이고, 그렇지 않으면 false입니다.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>인코딩된 데이터 및 확장의 중요 여부를 식별하는 값을 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="encodedSubjectKeyIdentifier">확장을 만드는 데 사용할 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 개체입니다.</param>
      <param name="critical">중요한 확장이면 true이고, 그렇지 않으면 false입니다.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.Security.Cryptography.X509Certificates.PublicKey,System.Boolean)">
      <summary>공개 키 및 확장의 중요 여부를 식별하는 값을 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="key">SKI(주체 키 식별자)를 만들 <see cref="T:System.Security.Cryptography.X509Certificates.PublicKey" /> 개체입니다. </param>
      <param name="critical">중요한 확장이면 true이고, 그렇지 않으면 false입니다.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.Security.Cryptography.X509Certificates.PublicKey,System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm,System.Boolean)">
      <summary>공개 키, 해시 알고리즘 식별자 및 확장의 중요 여부를 식별하는 값을 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
      <param name="key">SKI(주체 키 식별자)를 만들 <see cref="T:System.Security.Cryptography.X509Certificates.PublicKey" /> 개체입니다.</param>
      <param name="algorithm">사용할 해시 알고리즘을 식별하는 <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm" /> 값 중 하나입니다.</param>
      <param name="critical">중요한 확장이면 true이고, 그렇지 않으면 false입니다.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.String,System.Boolean)">
      <summary>문자열 및 확장의 중요 여부를 식별하는 값을 사용하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="subjectKeyIdentifier">인증서의 SKI(주체 키 식별자)를 나타내며 16진수 형식으로 인코딩된 문자열입니다.</param>
      <param name="critical">중요한 확장이면 true이고, 그렇지 않으면 false입니다.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>인코딩된 데이터에서 정보를 복사하여 <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> 클래스의 새 인스턴스를 만듭니다.</summary>
      <param name="asnEncodedData">확장을 만드는 데 사용할 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 개체입니다.</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.SubjectKeyIdentifier">
      <summary>인증서의 SKI(주체 키 식별자)를 나타내는 문자열을 가져옵니다.</summary>
      <returns>SKI(주체 키 식별자)를 나타내며 16진수 형식으로 인코딩된 문자열입니다.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">확장은 디코딩할 수 없는 경우 </exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> 클래스와 함께 사용할 해시 알고리즘의 형식을 정의합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm.CapiSha1">
      <summary>SKI(주체 키 식별자)는 인코딩된 공개 키의 160비트 SHA-1 해시로 구성됩니다(태그, 길이 및 사용되지 않는 비트 수 포함).</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm.Sha1">
      <summary>SKI는 공개 키 값의 160비트 SHA-1 해시로 구성됩니다(태그, 길이 및 사용되지 않는 비트 수 제외).</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm.ShortSha1">
      <summary>SKI는 값이 0100인 4비트 형식 필드 뒤에 오는 공개 키 값의 최하위 60비트 SHA-1 해시로 구성됩니다(태그, 길이 및 사용되지 않는 비트 문자열 수 제외).</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509VerificationFlags">
      <summary>X509 체인에서 인증서의 안정성을 확인해야 하는 조건을 지정합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.AllFlags">
      <summary>안정성 확인과 관련된 모든 플래그가 포함됩니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.AllowUnknownCertificateAuthority">
      <summary>알 수 없는 CA(인증 기관)로 인해 체인의 안정성을 확인할 수 없는 경우 이를 무시합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreCertificateAuthorityRevocationUnknown">
      <summary>인증서 안정성 확인을 결정할 때 인증 기관 해지 상태를 알 수 없는 경우 이를 무시합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreCtlNotTimeValid">
      <summary>인증서 안정성 확인을 결정할 때 CTL(인증서 신뢰 목록) 만료 등의 이유로 CTL이 유효하지 않은 경우 이를 무시합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreCtlSignerRevocationUnknown">
      <summary>인증서 안정성 확인을 결정할 때 CTL(인증서 신뢰 목록) 서명자 해지 상태를 알 수 없는 경우 이를 무시합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreEndRevocationUnknown">
      <summary>인증서 안정성 확인을 결정할 때 최종 인증서(사용자 인증서) 해지 상태를 알 수 없는 경우 이를 무시합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreInvalidBasicConstraints">
      <summary>인증서 안정성 확인을 결정할 때 기본 제약 조건이 유효하지 않은 경우 이를 무시합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreInvalidName">
      <summary>인증서 안정성 확인을 결정할 때 인증서에 잘못된 이름이 있는 경우 이를 무시합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreInvalidPolicy">
      <summary>인증서 안정성 확인을 결정할 때 인증서에 잘못된 정책이 있는 경우 이를 무시합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreNotTimeNested">
      <summary>인증서의 안정성을 확인할 때 CA(인증 기관) 인증서와 발급된 인증서의 유효 기간이 서로 일치하지 않는 경우 이를 무시합니다.예를 들어, CA 인증서는 1월 1일부터 12월 1일까지 유효하고 발급된 인증서는 1월 2일부터 12월 2일까지 유효한 경우 이는 유효 기간이 일치하지 않음을 의미합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreNotTimeValid">
      <summary>인증서 유효성을 확인할 때 만료되었거나 더는 적용되지 않아서 유효하지 않는 인증서가 체인에 있는 경우 이를 무시합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreRootRevocationUnknown">
      <summary>인증서 안정성 확인을 결정할 때 루트 해지 상태를 알 수 없는 경우 이를 무시합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreWrongUsage">
      <summary>인증서 안정성 확인을 결정할 때 인증서가 현재 사용하도록 발급되지 않은 경우 이를 무시합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.NoFlag">
      <summary>안정성 확인과 관련된 플래그가 포함되지 않습니다.</summary>
    </member>
  </members>
</doc>