﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Security.Cryptography.X509Certificates</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Win32.SafeHandles.SafeX509ChainHandle">
      <summary>Предоставляет безопасный дескриптор, представляющий цепочки сертификата X.509.Для получения дополнительной информации см. <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" />.</summary>
    </member>
    <member name="P:Microsoft.Win32.SafeHandles.SafeX509ChainHandle.IsInvalid"></member>
    <member name="T:System.Security.Cryptography.X509Certificates.OpenFlags">
      <summary>Задает способ открытия хранилища сертификатов X.509.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.IncludeArchived">
      <summary>Откройте хранилище сертификатов X.509 и включите архив сертификатов.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.MaxAllowed">
      <summary>Откройте хранилище сертификатов X.509 для самого высокого уровня доступа.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.OpenExistingOnly">
      <summary>Открывает только существующие хранилища; если хранилища отсутствуют, метод <see cref="M:System.Security.Cryptography.X509Certificates.X509Store.Open(System.Security.Cryptography.X509Certificates.OpenFlags)" /> не создаст новое хранилище.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.ReadOnly">
      <summary>Откройте хранилище сертификатов X.509 только для чтения.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.ReadWrite">
      <summary>Откройте хранилище сертификатов X.509 для чтения и записи.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.PublicKey">
      <summary>Представляет сведения об открытом ключе сертификата.Этот класс не наследуется.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.PublicKey.#ctor(System.Security.Cryptography.Oid,System.Security.Cryptography.AsnEncodedData,System.Security.Cryptography.AsnEncodedData)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.PublicKey" /> с помощью объекта идентификатора объекта (OID) открытого ключа, представления параметров открытого ключа в кодировке ASN.1 и представления значения открытого ключа в кодировке ASN.1. </summary>
      <param name="oid">Объект идентификатора объекта (OID), представляющий открытый ключ.</param>
      <param name="parameters">Представление параметров открытого ключа в кодировке ASN.1.</param>
      <param name="keyValue">Представление значения отрытого ключа в кодировке ASN.1.</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.PublicKey.EncodedKeyValue">
      <summary>Получает представление значения открытого ключа в кодировке ASN.1.</summary>
      <returns>Представление значения открытого ключа в кодировке ASN.1.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.PublicKey.EncodedParameters">
      <summary>Получает представление параметров открытого ключа в кодировке ASN.1.</summary>
      <returns>Представление параметров открытого ключа в кодировке ASN.1.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.PublicKey.Key">
      <summary>Получает объект <see cref="T:System.Security.Cryptography.RSACryptoServiceProvider" /> или <see cref="T:System.Security.Cryptography.DSACryptoServiceProvider" />, представляющий открытый ключ.</summary>
      <returns>Объект <see cref="T:System.Security.Cryptography.AsymmetricAlgorithm" />, представляющий открытый ключ.</returns>
      <exception cref="T:System.NotSupportedException">Алгоритм ключа не поддерживается.</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.PublicKey.Oid">
      <summary>Получает идентификатор объекта (OID) открытого ключа.</summary>
      <returns>Идентификатор объекта (OID) открытого ключа.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.StoreLocation">
      <summary>Задает расположение хранилища сертификатов X.509.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreLocation.CurrentUser">
      <summary>Хранилище сертификатов X.509 используется текущим пользователем.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreLocation.LocalMachine">
      <summary>Хранилище сертификатов X.509, назначенное локальному компьютеру.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.StoreName">
      <summary>Задает имя открываемого хранилища сертификатов X.509.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.AddressBook">
      <summary>Хранилище сертификатов X.509 для других пользователей.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.AuthRoot">
      <summary>Хранилище сертификатов X.509 для сторонних центров сертификации (ЦС).</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.CertificateAuthority">
      <summary>Хранилище сертификатов X.509 для промежуточных центров сертификации. </summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.Disallowed">
      <summary>Хранилище сертификатов X.509 для отозванных сертификатов.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.My">
      <summary>Хранилище сертификатов X.509 для личных сертификатов.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.Root">
      <summary>Хранилище сертификатов X.509 для доверенного корневого центра сертификации.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.TrustedPeople">
      <summary>Хранилище сертификатов X.509 для непосредственно доверенных лиц и ресурсов.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.TrustedPublisher">
      <summary>Хранилище сертификатов X.509 для непосредственно доверенных издателей.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName">
      <summary>Представляет различающееся имя сертификата X.509.Этот класс не наследуется.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.Byte[])">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> с использованием информации из указанного массива байтов.</summary>
      <param name="encodedDistinguishedName">Массив байтов, который содержит информацию о различающемся имени.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.Security.Cryptography.AsnEncodedData)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" />, используя заданный объект <see cref="T:System.Security.Cryptography.AsnEncodedData" />.</summary>
      <param name="encodedDistinguishedName">Объект<see cref="T:System.Security.Cryptography.AsnEncodedData" />, представляющий различающееся имя.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.Security.Cryptography.X509Certificates.X500DistinguishedName)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" />, используя заданный объект <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" />.</summary>
      <param name="distinguishedName">Объект <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" />.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> с использованием информации из указанного строки.</summary>
      <param name="distinguishedName">Строка, которая представляет различающееся имя.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.String,System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> с использованием указанной строки и флага <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags" />.</summary>
      <param name="distinguishedName">Строка, которая представляет различающееся имя.</param>
      <param name="flag">Побитовая комбинация значений перечисления, определяющих характеристики различающегося имени.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.Decode(System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags)">
      <summary>Декодирует различающееся имя с помощью характеристик, заданных параметром <paramref name="flag" />.</summary>
      <returns>Декодированное различающееся имя.</returns>
      <param name="flag">Побитовая комбинация значений перечисления, определяющих характеристики различающегося имени.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Сертификат имеет недопустимое имя.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.Format(System.Boolean)">
      <summary>Возвращает форматированную версию различающегося имени сертификата X500 для печати или вывода в текстовое окно или на консоль.</summary>
      <returns>Форматированная строка, представляющая различающееся имя X500.</returns>
      <param name="multiLine">Значение true, если возвращаемая строка должна содержать возвраты каретки; в противном случае — false.</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X500DistinguishedName.Name">
      <summary>Получает различающееся имя с разделителями-запятыми из сертификата X500.</summary>
      <returns>Различающееся имя сертификата X.509 с разделителями-запятыми.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags">
      <summary>Задает характеристики различающегося имени, соответствующего стандарту X.500.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.DoNotUsePlusSign">
      <summary>В различающемся имени не используется знак «плюс».</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.DoNotUseQuotes">
      <summary>В различающемся имени не используются кавычки.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.ForceUTF8Encoding">
      <summary>Обеспечивает принудительную кодировку определенных ключей X.500 в различающемся имени как строк UTF-8, а не печатных строк Юникода.Дополнительную информацию и список затрагиваемых ключей X.500 см. в описании перечисления X500NameFlags.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.None">
      <summary>В различающемся имени отсутствуют особые параметры.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.Reversed">
      <summary>Различающееся имя записывается в обратном порядке.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseCommas">
      <summary>В различающемся имени используются запятые.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseNewLines">
      <summary>В различающемся имени используется символ новой строки.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseSemicolons">
      <summary>В различающемся имени используются точки с запятой.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseT61Encoding">
      <summary>В различающемся имени используется кодировка T61.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseUTF8Encoding">
      <summary>В различающемся имени используется кодировка UTF8 вместо кодировки символов Юникода.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension">
      <summary>Определяет ограничения, установленные для сертификата.Этот класс не наследуется.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.#ctor(System.Boolean,System.Boolean,System.Int32,System.Boolean)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension" />.Параметры задают значение, показывающее, является ли сертификат сертификатом центра сертификации (ЦС), значение, показывающее, имеется ли у сертификата ограничение количества допустимых уровней пути и количества уровней, допустимых в пути к сертификату, и значение, показывающее, является ли расширение критическим.</summary>
      <param name="certificateAuthority">Значение true, если сертификат является сертификатом центра сертификации; в противном случае — false.</param>
      <param name="hasPathLengthConstraint">Значение true, если сертификат имеет ограничение по количеству допустимых уровней пути, в противном случае — false.</param>
      <param name="pathLengthConstraint">Количество уровней, допустимых в пути к сертификату.</param>
      <param name="critical">Значение true, если расширение является критическим, в противном случае — false.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension" /> с использованием объекта <see cref="T:System.Security.Cryptography.AsnEncodedData" /> и значения, указывающего, является ли расширение критическим. </summary>
      <param name="encodedBasicConstraints">Закодированные данные, используемые для создания расширения.</param>
      <param name="critical">Значение true, если расширение является критическим, в противном случае — false.</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.CertificateAuthority">
      <summary>Получает значение, показывающее, является ли сертификат сертификатом центра сертификации (ЦС).</summary>
      <returns>Значение true, если сертификат является сертификатом центра сертификации; в противном случае — false.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension" /> с помощью объекта <see cref="T:System.Security.Cryptography.AsnEncodedData" />.</summary>
      <param name="asnEncodedData">Закодированные данные, используемые для создания расширения.</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.HasPathLengthConstraint">
      <summary>Получает значение, указывающее, имеются ли у сертификата ограничения количества допустимых уровней пути.</summary>
      <returns>Значение true, если сертификат имеет ограничение по количеству допустимых уровней пути, в противном случае — false.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Расширение не может быть декодировано. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.PathLengthConstraint">
      <summary>Получает количество уровней, допустимых в пути к сертификату.</summary>
      <returns>Целое число, указывающее допустимое количество уровней в пути к сертификату.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Расширение не может быть декодировано. </exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Certificate">
      <summary>Предоставляет методы, помогающие использовать сертификаты X.509 v.3.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />. </summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.Byte[])">
      <summary>Инициализирует новый класс <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />, определенный из последовательности байтов, представляющих сертификат X.509v3.</summary>
      <param name="data">Массив байтов, содержащий данные сертификата X.509.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Произошла ошибка сертификата.Примеры.Заданный файл сертификата не существует.Сертификат недействителен.Пароль сертификата неверен.</exception>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="rawData" /> — null.-или-Длина параметра <paramref name="rawData" /> равна 0.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.Byte[],System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> с использованием массива байтов и пароля.</summary>
      <param name="rawData">Массив байтов, содержащий данные сертификата X.509.</param>
      <param name="password">Пароль для доступа к данным сертификата X.509.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Произошла ошибка сертификата.Примеры.Заданный файл сертификата не существует.Сертификат недействителен.Пароль сертификата неверен.</exception>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="rawData" /> — null.-или-Длина параметра <paramref name="rawData" /> равна 0.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.Byte[],System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> с использованием массива байтов, пароля и флага хранилища ключей.</summary>
      <param name="rawData">Массив байтов, содержащий данные сертификата X.509. </param>
      <param name="password">Пароль для доступа к данным сертификата X.509. </param>
      <param name="keyStorageFlags">Поразрядное сочетание значений перечисления, определяющих, где и как следует импортировать сертификат. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Произошла ошибка сертификата.Примеры.Заданный файл сертификата не существует.Сертификат недействителен.Пароль сертификата неверен.</exception>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="rawData" /> — null.-или-Длина параметра <paramref name="rawData" /> равна 0.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.IntPtr)">
      <summary>[SECURITY CRITICAL] Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> с помощью дескриптора неуправляемой структуры PCCERT_CONTEXT.</summary>
      <param name="handle">Дескриптор неуправляемой структуры PCCERT_CONTEXT.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> именем подписанного файла PKCS7. </summary>
      <param name="fileName">Имя подписанного файла PKCS7.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Произошла ошибка сертификата.Примеры.Заданный файл сертификата не существует.Сертификат недействителен.Пароль сертификата неверен.</exception>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="fileName" /> — null.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.String,System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />, именем подписанного файла PKCS7 и паролем для доступа к сертификату.</summary>
      <param name="fileName">Имя подписанного файла PKCS7. </param>
      <param name="password">Пароль для доступа к данным сертификата X.509. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Произошла ошибка сертификата.Примеры.Заданный файл сертификата не существует.Сертификат недействителен.Пароль сертификата неверен.</exception>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="fileName" /> — null.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.String,System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />, именем подписанного файла PKCS7 и паролем для доступа к сертификату и флагом хранилища ключей. </summary>
      <param name="fileName">Имя подписанного файла PKCS7. </param>
      <param name="password">Пароль для доступа к данным сертификата X.509. </param>
      <param name="keyStorageFlags">Поразрядное сочетание значений перечисления, определяющих, где и как следует импортировать сертификат. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Произошла ошибка сертификата.Примеры.Заданный файл сертификата не существует.Сертификат недействителен.Пароль сертификата неверен.</exception>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="fileName" /> — null.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Dispose">
      <summary>Освобождает все ресурсы, используемые текущим объектом <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Dispose(System.Boolean)">
      <summary>Освобождает все неуправляемые ресурсы, используемые <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> и дополнительно освобождает управляемые ресурсы. </summary>
      <param name="disposing">trueЧтобы освободить управляемые и неуправляемые ресурсы; false чтобы освободить только неуправляемые ресурсы.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Equals(System.Object)">
      <summary>Определяет равенство двух объектов <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />.</summary>
      <returns>Значение true, если текущий объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> равен объекту, заданному параметром <paramref name="other" />; в противном случае — false.</returns>
      <param name="obj">Объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />, который требуется сравнить с текущим объектом. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Equals(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>Определяет равенство двух объектов <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />.</summary>
      <returns>Значение true, если текущий объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> равен объекту, заданному параметром <paramref name="other" />; в противном случае — false.</returns>
      <param name="other">Объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />, который требуется сравнить с текущим объектом.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Export(System.Security.Cryptography.X509Certificates.X509ContentType)">
      <summary>Экспортирует текущий объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> в массив байтов в формате, описанном одним из значений <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" />. </summary>
      <returns>Массив байтов, представляющий текущий объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />.</returns>
      <param name="contentType">Одно из значений <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" />, описывающих способы форматирования выходных данных. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Значение, отличное от <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.Cert" />, <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.SerializedCert" /> или <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pkcs12" />, было передано в параметр <paramref name="contentType" />.-или-Не удалось экспортировать сертификат.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.KeyContainerPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="Open, Export" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Export(System.Security.Cryptography.X509Certificates.X509ContentType,System.String)">
      <summary>Экспортирует текущий объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> в массив байтов в формате, описанном одним из значений <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" />, с использованием заданного пароля.</summary>
      <returns>Массив байтов, представляющий текущий объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />.</returns>
      <param name="contentType">Одно из значений <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" />, описывающих способы форматирования выходных данных.</param>
      <param name="password">Пароль для доступа к данным сертификата X.509.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Значение, отличное от <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.Cert" />, <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.SerializedCert" /> или <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pkcs12" />, было передано в параметр <paramref name="contentType" />.-или-Не удалось экспортировать сертификат.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.KeyContainerPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="Open, Export" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetCertHash">
      <summary>Возвращает хэш-значение для сертификата X.509v3 в виде массива байтов.</summary>
      <returns>Хэш-значение для сертификата X.509.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetFormat">
      <summary>Возвращает имя формата сертификата X.509v3.</summary>
      <returns>Формат сертификата X.509.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetHashCode">
      <summary>Возвращает хэш-код для сертификата X.509v3 в виде целого числа.</summary>
      <returns>Хэш-код для сертификата X.509 в виде целого числа.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetKeyAlgorithm">
      <summary>Возвращает сведения об алгоритме ключа для сертификата X.509v3 в виде строки.</summary>
      <returns>Сведения об алгоритме ключа для сертификата X.509 в виде строки.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Недопустимый контекст сертификата.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetKeyAlgorithmParameters">
      <summary>Возвращает параметры алгоритма ключа для сертификата X.509v3 в виде массива байтов.</summary>
      <returns>Параметры алгоритма ключа для сертификата X.509 в виде массива байтов.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Недопустимый контекст сертификата.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetKeyAlgorithmParametersString">
      <summary>Возвращает параметры алгоритма ключа для сертификата X.509v3 в виде шестнадцатеричной строки.</summary>
      <returns>Параметры алгоритма ключа для сертификата X.509 в виде шестнадцатеричной строки.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Недопустимый контекст сертификата.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetPublicKey">
      <summary>Возвращает открытый ключ для сертификата X.509v3 в виде массива байтов.</summary>
      <returns>Открытый ключ для сертификата X.509 в виде массива байтов.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Недопустимый контекст сертификата.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetSerialNumber">
      <summary>Возвращает серийный номер сертификата X.509v3 в виде массива байтов.</summary>
      <returns>Серийный номер сертификата X.509 в виде массива байтов.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Недопустимый контекст сертификата.</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate.Handle">
      <summary>[SECURITY CRITICAL] Получает дескриптор контекста сертификата Microsoft Cryptographic API, описанный неуправляемой структурой PCCERT_CONTEXT. </summary>
      <returns>Структура <see cref="T:System.IntPtr" />, представляющая неуправляемую структуру PCCERT_CONTEXT.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate.Issuer">
      <summary>Получает имя центра сертификации, выдавшего сертификат X.509v3.</summary>
      <returns>Имя центра сертификации, выдавшего сертификат X.509v3.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Недопустимый дескриптор сертификата.</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate.Subject">
      <summary>Возвращает различающееся имя субъекта из сертификата.</summary>
      <returns>Различающееся имя субъекта из сертификата.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Недопустимый дескриптор сертификата.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.ToString">
      <summary>Возвращает строковое представление текущего объекта <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />.</summary>
      <returns>Строковое представление текущего объекта <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.ToString(System.Boolean)">
      <summary>Возвращает строковое представление текущего объекта <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> с дополнительными сведениями, если заданы.</summary>
      <returns>Строковое представление текущего объекта <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />.</returns>
      <param name="fVerbose">trueдля создания подробной формы строкового представления; в противном случае — false. </param>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Certificate2">
      <summary>Представляет сертификат X.509.  </summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.Byte[])">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> с использованием информации из указанного массива байтов.</summary>
      <param name="rawData">Массив байтов, содержащий данные сертификата X.509. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Произошла ошибка сертификата.Например:Заданный файл сертификата не существует.Сертификат недействителен.Пароль сертификата неверен.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.Byte[],System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> с использованием массива байтов и пароля.</summary>
      <param name="rawData">Массив байтов, содержащий данные сертификата X.509. </param>
      <param name="password">Пароль для доступа к данным сертификата X.509. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Произошла ошибка сертификата.Например:Заданный файл сертификата не существует.Сертификат недействителен.Пароль сертификата неверен.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.Byte[],System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> с использованием массива байтов, пароля и флага хранилища ключей.</summary>
      <param name="rawData">Массив байтов, содержащий данные сертификата X.509. </param>
      <param name="password">Пароль для доступа к данным сертификата X.509. </param>
      <param name="keyStorageFlags">Поразрядное сочетание значений перечисления, определяющих, где и как следует импортировать сертификат. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Произошла ошибка сертификата.Например:Заданный файл сертификата не существует.Сертификат недействителен.Пароль сертификата неверен.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.IntPtr)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> с помощью неуправляемого дескриптора.</summary>
      <param name="handle">Указатель на контекст сертификата в неуправляемом коде.Структура на языке C вызывается PCCERT_CONTEXT.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Произошла ошибка сертификата.Например:Заданный файл сертификата не существует.Сертификат недействителен.Пароль сертификата неверен.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> с помощью имени файла сертификата.</summary>
      <param name="fileName">Имя файла сертификата. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Произошла ошибка сертификата.Например:Заданный файл сертификата не существует.Сертификат недействителен.Пароль сертификата неверен.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.String,System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />, используя имя файла сертификата и пароль для доступа к сертификату.</summary>
      <param name="fileName">Имя файла сертификата. </param>
      <param name="password">Пароль для доступа к данным сертификата X.509. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Произошла ошибка сертификата.Например:Заданный файл сертификата не существует.Сертификат недействителен.Пароль сертификата неверен.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.String,System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> с использованием имени файла сертификата и пароля для доступа к сертификату, а также флага хранилища ключа.</summary>
      <param name="fileName">Имя файла сертификата. </param>
      <param name="password">Пароль для доступа к данным сертификата X.509. </param>
      <param name="keyStorageFlags">Поразрядное сочетание значений перечисления, определяющих, где и как следует импортировать сертификат. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Произошла ошибка сертификата.Например:Заданный файл сертификата не существует.Сертификат недействителен.Пароль сертификата неверен.</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.Archived">
      <summary>Получает или задает значение, указывающее на архивирование сертификата X.509.</summary>
      <returns>Значение true, если сертификат архивирован, значениеfalse, если сертификат не архивирован.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Сертификат не читается. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.Extensions">
      <summary>Получает коллекцию объектов <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" />.</summary>
      <returns>Объект <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Сертификат не читается. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.FriendlyName">
      <summary>Получает или задает связанный псевдоним для сертификата.</summary>
      <returns>Понятное имя сертификата.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Сертификат не читается. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.GetCertContentType(System.Byte[])">
      <summary>Показывает тип сертификата, содержащегося в массиве байтов.</summary>
      <returns>Объект <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" />.</returns>
      <param name="rawData">Массив байтов, содержащий данные сертификата X.509. </param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="rawData" /> имеет нулевую длину, или его значение равно null. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.GetCertContentType(System.String)">
      <summary>Показывает тип сертификата, содержащегося в файле.</summary>
      <returns>Объект <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" />.</returns>
      <param name="fileName">Имя файла сертификата. </param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="fileName" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.GetNameInfo(System.Security.Cryptography.X509Certificates.X509NameType,System.Boolean)">
      <summary>Получает имена субъекта и поставщика сертификата.</summary>
      <returns>Имя сертификата.</returns>
      <param name="nameType">Значение <see cref="T:System.Security.Cryptography.X509Certificates.X509NameType" /> для субъекта. </param>
      <param name="forIssuer">Значение true для включения имени поставщика; в противном случае — false. </param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.HasPrivateKey">
      <summary>Возвращает значение, которое указывает, содержит ли объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> закрытый ключ. </summary>
      <returns>Значение true, если объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> содержит закрытый ключ; в противном случае — false. </returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Недопустимый контекст сертификата.</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.IssuerName">
      <summary>Получает различающееся имя поставщика сертификата.</summary>
      <returns>Объект <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" />, содержащий имя поставщика сертификата.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Недопустимый контекст сертификата.</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.NotAfter">
      <summary>Получает дату в формате местного времени, после которой сертификат недействителен.</summary>
      <returns>Объект <see cref="T:System.DateTime" />, представляющий дату окончания срока действия сертификата.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Сертификат не читается. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.NotBefore">
      <summary>Получает дату в формате местного времени, после которой сертификат становится действительным.</summary>
      <returns>Объект <see cref="T:System.DateTime" />, представляющий дату вступления в силу сертификата.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Сертификат не читается. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.PrivateKey">
      <summary>Получает или задает объект <see cref="T:System.Security.Cryptography.AsymmetricAlgorithm" />, который представляет закрытый ключ, связанный с сертификатом.</summary>
      <returns>Объект <see cref="T:System.Security.Cryptography.AsymmetricAlgorithm" />, являющийся поставщиком служб шифрования RSA или DSA.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Значение ключа не является значением RSA или DSA, или ключ не читается. </exception>
      <exception cref="T:System.ArgumentNullException">Для этого свойства устанавливается значение null.</exception>
      <exception cref="T:System.NotSupportedException">Алгоритм ключа для этого закрытого ключа не поддерживается.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicUnexpectedOperationException">Ключи X.509 не совпадают.</exception>
      <exception cref="T:System.ArgumentException">Значение ключа поставщика служб шифрования равно null.</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.PublicKey">
      <summary>Получает объект <see cref="P:System.Security.Cryptography.X509Certificates.X509Certificate2.PublicKey" />, связанный с сертификатом.</summary>
      <returns>Объект <see cref="P:System.Security.Cryptography.X509Certificates.X509Certificate2.PublicKey" />.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Значение ключа не является значением RSA или DSA, или ключ не читается. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.RawData">
      <summary>Получает необработанные данные сертификата.</summary>
      <returns>Необработанные данные сертификата в качестве массива байтов.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.SerialNumber">
      <summary>Получает серийный номер сертификата.</summary>
      <returns>Серийный номер сертификата.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.SignatureAlgorithm">
      <summary>Получает алгоритм, используемый для создания подписи сертификата.</summary>
      <returns>Возвращает идентификатор объекта (<see cref="T:System.Security.Cryptography.Oid" />) алгоритма подписи.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Сертификат не читается. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.SubjectName">
      <summary>Получает различающееся имя субъекта от сертификата.</summary>
      <returns>Объект <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" />, представляющий имя субъекта сертификата.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Недопустимый контекст сертификата.</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.Thumbprint">
      <summary>Получает отпечаток сертификата.</summary>
      <returns>Отпечаток сертификата.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.ToString">
      <summary>Отображает сертификат X.509 в текстовом формате.</summary>
      <returns>Сведения о сертификате.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.ToString(System.Boolean)">
      <summary>Отображает сертификат X.509 в текстовом формате.</summary>
      <returns>Сведения о сертификате.</returns>
      <param name="verbose">Значение true для отображения открытого ключа, закрытого ключа, расширений и т. д.; значение false для отображения сведений, аналогичных классу <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />, в том числе отпечаток, серийный номер, имена субъекта и поставщика и т. д. </param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.Version">
      <summary>Получает версию формата сертификата X.509.</summary>
      <returns>Формат сертификата.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Сертификат не читается. </exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection">
      <summary>Представляет коллекцию объектов <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />.Этот класс не наследуется.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> без дополнительной информации <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> с помощью объекта <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />.</summary>
      <param name="certificate">Объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />, с которого начинается коллекция.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2[])">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />, используя массив объектов <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />.</summary>
      <param name="certificates">Массив объектов <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2Collection)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />, используя заданную коллекцию сертификатов.</summary>
      <param name="certificates">Объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Add(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Добавляет объект в конец коллекции <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />.</summary>
      <returns>Индекс <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />, по которому был добавлен параметр <paramref name="certificate" />.</returns>
      <param name="certificate">Сертификат X.509 представлен в качестве объекта <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />. </param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="certificate" /> имеет значение null. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.AddRange(System.Security.Cryptography.X509Certificates.X509Certificate2[])">
      <summary>Добавляет несколько объектов <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> в массиве в объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />.</summary>
      <param name="certificates">Массив объектов <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />. </param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="certificates" /> имеет значение null. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.AddRange(System.Security.Cryptography.X509Certificates.X509Certificate2Collection)">
      <summary>Добавляет несколько объектов <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> в объекте <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> к другому объекту <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />.</summary>
      <param name="certificates">Объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />. </param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="certificates" /> имеет значение null. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Contains(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Определяет, содержит ли объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> указанный сертификат.</summary>
      <returns>Значение true, если объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> содержит указанный параметр <paramref name="certificate" />, в противном случае — значение false.</returns>
      <param name="certificate">Объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />, который требуется найти в коллекции. </param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="certificate" /> имеет значение null. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Export(System.Security.Cryptography.X509Certificates.X509ContentType)">
      <summary>Экспортирует сведения о сертификате X.509 в массив байтов.</summary>
      <returns>Сведения о сертификате X.509 в массиве байтов.</returns>
      <param name="contentType">Поддерживаемый объект <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" />. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Export(System.Security.Cryptography.X509Certificates.X509ContentType,System.String)">
      <summary>Экспортирует сведения о сертификате X.509 в массив байтов, используя пароль.</summary>
      <returns>Сведения о сертификате X.509 в массиве байтов.</returns>
      <param name="contentType">Поддерживаемый объект <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" />. </param>
      <param name="password">Строка, используемая для защиты массива байтов. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Нечитаемый сертификат, недопустимое содержимое или, если используется сертификат с паролем, ошибка экспорта закрытого ключа из-за неправильного пароля. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)">
      <summary>Выполняет поиск в объекте <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> с использованием критериев поиска, указанных в перечислении <see cref="T:System.Security.Cryptography.X509Certificates.X509FindType" /> и объекте <paramref name="findValue" />.</summary>
      <returns>Объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />.</returns>
      <param name="findType">Одно из значений <see cref="T:System.Security.Cryptography.X509Certificates.X509FindType" />. </param>
      <param name="findValue">Критерий поиска в качестве объекта. </param>
      <param name="validOnly">Значение true разрешает возврат из поиска только допустимых сертификатов; в противном случае — false. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="findType" /> не является допустимым. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.GetEnumerator">
      <summary>Возвращает перечислитель, который может выполнять итерацию объекта <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />.</summary>
      <returns>Объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator" />, который может выполнять итерацию в объекте <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Import(System.Byte[])">
      <summary>Импортирует сертификат в форме массива байтов в объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />.</summary>
      <param name="rawData">Массив байтов, содержащий данные сертификата X.509. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Import(System.Byte[],System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Импортирует сертификат в форме массива байтов, требующего пароля для доступа к сертификату, в объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />.</summary>
      <param name="rawData">Массив байтов, содержащий данные из объекта <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />. </param>
      <param name="password">Пароль, необходимый для доступа к сведениям о сертификате. </param>
      <param name="keyStorageFlags">Поразрядное сочетание значений перечисления, определяющих, где и как импортирован сертификат. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Import(System.String)">
      <summary>Импортирует файл сертификата в объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />.</summary>
      <param name="fileName">Имя файла, содержащего сведения о сертификате. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Import(System.String,System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Импортирует файл сертификата, требующий пароль, в объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />.</summary>
      <param name="fileName">Имя файла, содержащего сведения о сертификате. </param>
      <param name="password">Пароль, необходимый для доступа к сведениям о сертификате. </param>
      <param name="keyStorageFlags">Поразрядное сочетание значений перечисления, определяющих, где и как импортирован сертификат. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Insert(System.Int32,System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Вставляет объект в объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> по указанному индексу.</summary>
      <param name="index">Отсчитываемый с нуля индекс, по которому должен быть вставлен параметр <paramref name="certificate" />. </param>
      <param name="certificate">Вставляемый объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше нуля.– или – Значение параметра <paramref name="index" /> больше значения свойства <see cref="P:System.Collections.CollectionBase.Count" />. </exception>
      <exception cref="T:System.NotSupportedException">Семейство доступно только для чтения.– или – Коллекция имеет фиксированный размер. </exception>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="certificate" /> имеет значение null. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Item(System.Int32)">
      <summary>Получает или задает элемент с указанным индексом.</summary>
      <returns>Элемент с заданным индексом.</returns>
      <param name="index">Отсчитываемый с нуля индекс получаемого или задаваемого элемента. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше нуля.– или – Значение параметра <paramref name="index" /> больше или равно значению свойства <see cref="P:System.Collections.CollectionBase.Count" />. </exception>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="index" /> имеет значение null. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Remove(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Удаляет первое вхождение сертификата из объекта <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />.</summary>
      <param name="certificate">Объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />, удаляемый из объекта <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />. </param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="certificate" /> имеет значение null. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.RemoveRange(System.Security.Cryptography.X509Certificates.X509Certificate2[])">
      <summary>Удаляет несколько объектов <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> в массиве из объекта <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />.</summary>
      <param name="certificates">Массив объектов <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />. </param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="certificates" /> имеет значение null. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.RemoveRange(System.Security.Cryptography.X509Certificates.X509Certificate2Collection)">
      <summary>Удаляет несколько объектов <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> в объекте <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> из другого объекта <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />.</summary>
      <param name="certificates">Объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />. </param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="certificates" /> имеет значение null. </exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator">
      <summary>Поддерживает простую итерацию элементов объекта <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />.Этот класс не наследуется.</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.Current">
      <summary>Получает текущий элемент в объекте <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />.</summary>
      <returns>Текущий элемент в объекте <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />.</returns>
      <exception cref="T:System.InvalidOperationException">Перечислитель помещается перед первым элементом коллекции или после последнего элемента. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.MoveNext">
      <summary>Перемещает перечислитель к следующему элементу в объекте <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />.</summary>
      <returns>Значение true, если перечислитель был успешно перемещен к следующему элементу; значение false, если перечислитель достиг конца коллекции.</returns>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.Reset">
      <summary>Устанавливает перечислитель в исходное положение перед первым элементом в объекте <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />.</summary>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.System#Collections#IEnumerator#Current">
      <summary>Описание этого члена см. в разделе <see cref="P:System.Collections.IEnumerator.Current" />.</summary>
      <returns>Текущий элемент в объекте <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />.</returns>
      <exception cref="T:System.InvalidOperationException">Перечислитель помещается перед первым элементом коллекции или после последнего элемента. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.System#Collections#IEnumerator#MoveNext">
      <summary>Описание этого члена см. в разделе <see cref="M:System.Collections.IEnumerator.MoveNext" />.</summary>
      <returns>Значение true, если перечислитель был успешно перемещен к следующему элементу; значение false, если перечислитель достиг конца коллекции.</returns>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Описание этого члена см. в разделе <see cref="M:System.Collections.IEnumerator.Reset" />.</summary>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection">
      <summary>Определяет коллекцию, хранящую объекты <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate[])">
      <summary>Инициализирует экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> из массива объектов <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />.</summary>
      <param name="value">Массив объектов <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> для инициализации нового объекта. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.#ctor(System.Security.Cryptography.X509Certificates.X509CertificateCollection)">
      <summary>Инициализирует класс <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> из другого класса <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</summary>
      <param name="value">Класс <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> для инициализации нового объекта. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Add(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>Добавляет класс <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> с заданным значением в текущий класс <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</summary>
      <returns>Индекс в текущем объекте <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />, в котором вставлялся новый объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />.</returns>
      <param name="value">Объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />, добавляемый к текущему объекту <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.AddRange(System.Security.Cryptography.X509Certificates.X509Certificate[])">
      <summary>Копирует элементы массива типа <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> в конец текущего класса <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</summary>
      <param name="value">Массив типа <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />, содержащий объекты, добавляемые в текущий класс <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />. </param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="value" /> — null. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.AddRange(System.Security.Cryptography.X509Certificates.X509CertificateCollection)">
      <summary>Копирует элементы указанного объекта <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> в конец текущего объекта <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</summary>
      <param name="value">Объект <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />, содержащий объекты для добавления в коллекцию. </param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="value" /> — null. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Clear"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Contains(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>Получает значение, определяющее, содержит ли текущий класс <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> указанный объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />.</summary>
      <returns>Значение true, если объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> содержится в этой коллекции; в противном случае — значение false.</returns>
      <param name="value">Искомый объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.CopyTo(System.Security.Cryptography.X509Certificates.X509Certificate[],System.Int32)">
      <summary>Копирует значения <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> в текущем классе <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> в одномерный экземпляр <see cref="T:System.Array" /> по указанному индексу.</summary>
      <param name="array">Одномерный объект <see cref="T:System.Array" />, в который копируются значения из коллекции <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />. </param>
      <param name="index">Индекс в объекте <paramref name="array" />, с которого начинается копирование. </param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="array" /> является многомерным.– или – Число элементов в классе <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> превышает свободное место между параметром <paramref name="arrayIndex" /> концом массива <paramref name="array" />. </exception>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="array" /> — null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="arrayIndex" /> меньше нижней границы параметра <paramref name="array" />. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Count"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.GetEnumerator">
      <summary>Возвращает перечислитель, выполняющий итерацию коллекции <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</summary>
      <returns>Перечислитель дочерних элементов <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />, используемый для итерации коллекции.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.GetHashCode">
      <summary>Создает хэш-значение на основе всех значений, содержащихся в текущей коллекции <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</summary>
      <returns>Хэш-значение на основе всех значений, содержащихся в текущей коллекции <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.IndexOf(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>Возвращает индекс указанного объекта <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> в текущей коллекции <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</summary>
      <returns>Индекс объекта <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />, заданного параметром <paramref name="value" /> в коллекции <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />, если он найден; в противном случае — -1.</returns>
      <param name="value">Искомый объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Insert(System.Int32,System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>Вставляет объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> в текущую коллекцию <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> по указанному индексу.</summary>
      <param name="index">Начинающийся с нуля индекс места вставки параметра <paramref name="value" />. </param>
      <param name="value">Вставляемый объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />. </param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Item(System.Int32)">
      <summary>Получает или задает запись по указанному индексу текущей коллекции <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</summary>
      <returns>Объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> по указанному индексу текущей коллекции <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</returns>
      <param name="index">Начинающийся с нуля индекс записи, которую требуется найти в текущей коллекции <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="index" /> находится вне диапазона допустимых индексов коллекции. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Remove(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>Удаляет определенный объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> из текущей коллекции <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</summary>
      <param name="value">Объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />, который требуется удалить из текущей коллекции <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />. </param>
      <exception cref="T:System.ArgumentException">Объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />, заданный параметром <paramref name="value" />, не найден в текущей коллекции <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.RemoveAt(System.Int32)"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)"></member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#ICollection#IsSynchronized"></member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#ICollection#SyncRoot"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IEnumerable#GetEnumerator"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#Add(System.Object)"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#Contains(System.Object)"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#IndexOf(System.Object)"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#Insert(System.Int32,System.Object)"></member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#IsFixedSize"></member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#IsReadOnly"></member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#Item(System.Int32)"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#Remove(System.Object)"></member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator">
      <summary>Перечисляет объекты <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> в <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.#ctor(System.Security.Cryptography.X509Certificates.X509CertificateCollection)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator" /> для указанного объекта <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</summary>
      <param name="mappings">Объект <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> для перечисления. </param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.Current">
      <summary>Получает текущий объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> в объекте <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</summary>
      <returns>Текущий объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> в объекте <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</returns>
      <exception cref="T:System.InvalidOperationException">Перечислитель помещается перед первым элементом коллекции или после последнего элемента. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.MoveNext">
      <summary>Перемещает перечислитель к следующему элементу коллекции.</summary>
      <returns>Значение true, если перечислитель был успешно перемещен к следующему элементу; значение false, если перечислитель достиг конца коллекции.</returns>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.Reset">
      <summary>Устанавливает перечислитель в его начальное положение, т. е. перед первым элементом коллекции.</summary>
      <exception cref="T:System.InvalidOperationException">Коллекция изменяется после создания экземпляра перечисления. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.System#Collections#IEnumerator#Current">
      <summary>Описание этого члена см. в разделе <see cref="P:System.Collections.IEnumerator.Current" />.</summary>
      <returns>Текущий объект сертификата X.509 в объекте <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</returns>
      <exception cref="T:System.InvalidOperationException">Перечислитель помещается перед первым элементом коллекции или после последнего элемента. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.System#Collections#IEnumerator#MoveNext">
      <summary>Описание этого члена см. в разделе <see cref="M:System.Collections.IEnumerator.MoveNext" />.</summary>
      <returns>Значение true, если перечислитель был успешно перемещен к следующему элементу; значение false, если перечислитель достиг конца коллекции.</returns>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.System#Collections#IEnumerator#Reset">
      <summary>Описание этого члена см. в разделе <see cref="M:System.Collections.IEnumerator.Reset" />.</summary>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Chain">
      <summary>Представляет обработчик для создания цепочки для сертификатов <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Chain.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Chain.Build(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Создает цепочку X.509 с использованием политики, указанной в объекте <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" />.</summary>
      <returns>Значение true, если сертификат X.509 действителен; в противном случае — значение false.</returns>
      <param name="certificate">Объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />.</param>
      <exception cref="T:System.ArgumentException">Сертификат <paramref name="certificate" />недействителен, или значение равно null. </exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Нечитаемый сертификат <paramref name="certificate" />. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Chain.ChainElements">
      <summary>Возвращает коллекцию объектов <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" />.</summary>
      <returns>Объект <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Chain.ChainPolicy">
      <summary>Возвращает или задает объект <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" />, используемый для создания цепочки сертификатов X.509.</summary>
      <returns>Объект <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" />, связанный с данной цепочкой X.509.</returns>
      <exception cref="T:System.ArgumentNullException">Для этого свойства устанавливается значение null.</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Chain.ChainStatus">
      <summary>Возвращает состояние каждого элемента в объекте <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" />.</summary>
      <returns>Массив объектов <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainStatus" />.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Chain.Dispose">
      <summary>Освобождает все ресурсы, используемые объектом <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Chain.Dispose(System.Boolean)">
      <summary>Освобождает неуправляемые ресурсы, используемые объектом <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" />, а при необходимости освобождает также управляемые ресурсы.</summary>
      <param name="disposing">Значение true позволяет освободить как управляемые, так и неуправляемые ресурсы; значение false освобождает только неуправляемые ресурсы.</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Chain.SafeHandle">
      <summary>Возвращает безопасный дескриптор для данного экземпляра <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" />. </summary>
      <returns>Возвращает <see cref="T:Microsoft.Win32.SafeHandles.SafeX509ChainHandle" />.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainElement">
      <summary>Представляет элемент цепочки сертификата X.509.</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElement.Certificate">
      <summary>Получает сертификат X.509 в конкретном элементе цепочки.</summary>
      <returns>Объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElement.ChainElementStatus">
      <summary>Получает состояние ошибки текущего сертификата X.509 в цепочке.</summary>
      <returns>Массив объектов <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainStatus" />.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElement.Information">
      <summary>Получает дополнительные сведения об ошибке из структуры цепочки неуправляемого сертификата.</summary>
      <returns>Строка, представляющая элемент pwszExtendedErrorInfo неуправляемой структуры в CERT_CHAIN_ELEMENT в шифровании Crypto API.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection">
      <summary>Представляет коллекцию объектов <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" />.Этот класс не наследуется.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.CopyTo(System.Security.Cryptography.X509Certificates.X509ChainElement[],System.Int32)">
      <summary>Копирует объект <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> в массив, начиная с указанного индекса.</summary>
      <param name="array">Массив объектов <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" />. </param>
      <param name="index">Целое число, представляющее значение индекса. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">Указанное значение параметра <paramref name="index" /> меньше нуля или больше или равно длине массива. </exception>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="array" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="index" /> вместе с текущим количеством больше, чем длина массива. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.Count">
      <summary>Получает количество элементов коллекции.</summary>
      <returns>Целое число, представляющее количество элементов в коллекции.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.GetEnumerator">
      <summary>Получает объект <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator" />, который может использоваться для перехода в коллекции элементов цепочки.</summary>
      <returns>Объект <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.IsSynchronized">
      <summary>Получает значение, позволяющее определить, является ли коллекция элементов цепочки синхронизированной.</summary>
      <returns>Всегда возвращает значение false.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.Item(System.Int32)">
      <summary>Возвращает объект <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" /> по указанному индексу.</summary>
      <returns>Объект <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" />.</returns>
      <param name="index">Целочисленное значение. </param>
      <exception cref="T:System.InvalidOperationException">Значение параметра <paramref name="index" /> меньше нуля. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" />больше или равно длине коллекции. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.SyncRoot">
      <summary>Получает объект, который можно использовать для синхронизации доступа к объекту <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />.</summary>
      <returns>Ссылка на текущий объект.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Копирует объект <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> в массив, начиная с указанного индекса.</summary>
      <param name="array">Массив для копирования объекта <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />.</param>
      <param name="index">Индекс объекта <paramref name="array" />, с которого требуется начать копирование.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Указанное значение параметра <paramref name="index" /> меньше нуля или больше или равно длине массива. </exception>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="array" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="index" /> вместе с текущим количеством больше, чем длина массива. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Получает объект <see cref="T:System.Collections.IEnumerator" />, который может использоваться для перехода в коллекции элементов цепочки.</summary>
      <returns>Объект <see cref="T:System.Collections.IEnumerator" />.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator">
      <summary>Поддерживает простую итерацию коллекции <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />.Этот класс не наследуется.</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator.Current">
      <summary>Получает текущий элемент в объекте <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />.</summary>
      <returns>Текущий элемент в коллекции <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />.</returns>
      <exception cref="T:System.InvalidOperationException">Перечислитель помещается перед первым элементом коллекции или после последнего элемента. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator.MoveNext">
      <summary>Перемещает перечислитель к следующему элементу в коллекции <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />.</summary>
      <returns>Значение true, если перечислитель был успешно перемещен к следующему элементу; значение false, если перечислитель достиг конца коллекции.</returns>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator.Reset">
      <summary>Устанавливает перечислитель в исходное положение перед первым элементом коллекции <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />.</summary>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator.System#Collections#IEnumerator#Current">
      <summary>Получает текущий элемент в объекте <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />.</summary>
      <returns>Текущий элемент в коллекции <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />.</returns>
      <exception cref="T:System.InvalidOperationException">Перечислитель помещается перед первым элементом коллекции или после последнего элемента. </exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy">
      <summary>Представляет политику цепочки, применяемую при построении цепочки сертификата X509.Этот класс не наследуется.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainPolicy.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" />. </summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.ApplicationPolicy">
      <summary>Получает коллекцию идентификаторов объекта (OID), задающих политики применения и расширенные использования ключа, поддерживаемые сертификатом.</summary>
      <returns>Объект <see cref="T:System.Security.Cryptography.OidCollection" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.CertificatePolicy">
      <summary>Получает коллекцию идентификаторов объекта (OID), задающих политики сертификатов, которые поддерживаются сертификатом.</summary>
      <returns>Объект <see cref="T:System.Security.Cryptography.OidCollection" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.ExtraStore">
      <summary>Представляет дополнительную коллекцию сертификатов, в которой поиск может осуществляться модулем цепочки при проверке цепочки сертификатов.</summary>
      <returns>Объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainPolicy.Reset">
      <summary>Восстанавливает значения по умолчанию для членов <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" />.</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.RevocationFlag">
      <summary>Получает или задает значения для флагов отзыва X509.</summary>
      <returns>Объект <see cref="T:System.Security.Cryptography.X509Certificates.X509RevocationFlag" />.</returns>
      <exception cref="T:System.ArgumentException">Указанное значение <see cref="T:System.Security.Cryptography.X509Certificates.X509RevocationFlag" /> не является допустимым флагом. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.RevocationMode">
      <summary>Получает или задает значения для режима отзыва сертификата X509.</summary>
      <returns>Объект <see cref="T:System.Security.Cryptography.X509Certificates.X509RevocationMode" />.</returns>
      <exception cref="T:System.ArgumentException">Указанное значение <see cref="T:System.Security.Cryptography.X509Certificates.X509RevocationMode" /> не является допустимым флагом. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.UrlRetrievalTimeout">
      <summary>Получает интервал времени, прошедшего в процессе интерактивной проверки отзыва или загрузки списка отзыва сертификатов (CRL).</summary>
      <returns>Объект <see cref="T:System.TimeSpan" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.VerificationFlags">
      <summary>Получает флаги проверки для сертификата.</summary>
      <returns>Значение из перечисления <see cref="T:System.Security.Cryptography.X509Certificates.X509VerificationFlags" />.</returns>
      <exception cref="T:System.ArgumentException">Указанное значение <see cref="T:System.Security.Cryptography.X509Certificates.X509VerificationFlags" /> не является допустимым флагом.<see cref="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.NoFlag" /> является значением по умолчанию.</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.VerificationTime">
      <summary>Время проверки сертификата в формате местного времени.</summary>
      <returns>Объект <see cref="T:System.DateTime" />.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainStatus">
      <summary>Предоставляет простую структуру для хранения состояния цепочки X509 и сведений об ошибках.</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainStatus.Status">
      <summary>Задает состояние цепочки X509.</summary>
      <returns>Значение <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainStatus.StatusInformation">
      <summary>Задает описание значения <see cref="P:System.Security.Cryptography.X509Certificates.X509Chain.ChainStatus" />.</summary>
      <returns>Локализуемая строка.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags">
      <summary>Определяет состояние цепочки X509.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.CtlNotSignatureValid">
      <summary>Задает, что список доверия сертификатов (CTL) содержит недопустимую подпись.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.CtlNotTimeValid">
      <summary>Указывает, что список доверия сертификатов (CTL) является недопустимым, так как содержит недопустимое значение времени. Например, данное значение задает, что срок действия списка доверия сертификатов истек.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.CtlNotValidForUsage">
      <summary>Указывает, что список доверия сертификатов (CTL) недействителен для данного применения.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.Cyclic">
      <summary>Указывает, что цепочка X509 не может быть создана.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.HasExcludedNameConstraint">
      <summary>Указывает, что цепочка X509 является недопустимой, так как сертификат исключил ограничение имен.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.HasNotDefinedNameConstraint">
      <summary>Указывает, что сертификат имеет неопределенную ограничение имени.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.HasNotPermittedNameConstraint">
      <summary>Указывает, что сертификат имеет недопустимое ограничение имен.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.HasNotSupportedNameConstraint">
      <summary>Указывает, что у сертификата отсутствует поддерживаемая ограничение имени, или ограничение имени сертификата не поддерживается.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.InvalidBasicConstraints">
      <summary>Указывает, что цепочка X509 является недопустимой из-за недопустимых основных ограничений.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.InvalidExtension">
      <summary>Указывает, что цепочка X509 является недопустимой из-за недопустимого расширения.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.InvalidNameConstraints">
      <summary>Указывает, что цепочка X509 является недопустимой из-за недопустимых ограничений имени.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.InvalidPolicyConstraints">
      <summary>Указывает, что цепочка X509 является недопустимой из-за недопустимых ограничений политики.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NoError">
      <summary>Указывает, что в цепочке X509 отсутствуют ошибки.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NoIssuanceChainPolicy">
      <summary>Указывает, что в сертификате отсутствует расширение политики сертификатов.Если в групповой политике указано, что все сертификаты должны иметь политику сертификата, это приведет к возникновению ошибки.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NotSignatureValid">
      <summary>Указывает, что цепочка X509 является недопустимой из-за недопустимой подписи сертификата.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NotTimeNested">
      <summary>Не рекомендуется.Указывает, что сертификат центра сертификации (ЦС) и выданный сертификат имеют сроки действия, которые не являются вложенными.Например, сертификат центра сертификации (ЦС) может быть действителен с 1 января по 1 декабря, а выданный сертификат — со 2 января по 2 декабря. Это значит, что сроки действия не являются вложенными.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NotTimeValid">
      <summary>Указывает, что цепочка X509 является недопустимой из-за недопустимого значения времени. Например, это значение указывает, что срок действия сертификата истек.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NotValidForUsage">
      <summary>Указывает, что использование ключа недопустимо.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.OfflineRevocation">
      <summary>Указывает, что список отзыва сертификатов с подключением к сети, который использует цепочка X509, в настоящее время отключен.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.PartialChain">
      <summary>Указывает, что цепочка X509 не может быть построена для корневого сертификата.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.RevocationStatusUnknown">
      <summary>Указывает, что невозможно определить, был ли отозван сертификат.Это может быть вызвано тем, что список отзыва сертификатов отключен или недоступен.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.Revoked">
      <summary>Указывает, что цепочка X509 является недопустимой из-за отозванного сертификата.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.UntrustedRoot">
      <summary>Указывает, что цепочка X509 недопустима из-за ненадежного корневого сертификата.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ContentType">
      <summary>Задает формат сертификата X.509. </summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Authenticode">
      <summary>Сертификат Authenticode X.509. </summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Cert">
      <summary>Единый сертификат X.509.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pfx">
      <summary>Сертификат в формате PFX.Значение Pfx идентично значению Pkcs12.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pkcs12">
      <summary>Сертификат в формате PKCS 12.Значение Pkcs12 идентично значению Pfx.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pkcs7">
      <summary>Сертификат в формате PKCS 7.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.SerializedCert">
      <summary>Единый сериализованный сертификат X.509. </summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.SerializedStore">
      <summary>Сериализованное хранилище.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Unknown">
      <summary>Неизвестный сертификат X.509.  </summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension">
      <summary>Определяет коллекцию идентификаторов объекта (OID), которая указывает приложения, использующие ключ.Этот класс не наследуется.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension" /> с использованием объекта <see cref="T:System.Security.Cryptography.AsnEncodedData" /> и значения, указывающего, является ли расширение критическим.</summary>
      <param name="encodedEnhancedKeyUsages">Закодированные данные, используемые для создания расширения.</param>
      <param name="critical">Значение true, если расширение является критическим, в противном случае — false.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.#ctor(System.Security.Cryptography.OidCollection,System.Boolean)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension" /> с использованием объекта <see cref="T:System.Security.Cryptography.OidCollection" /> и значения, указывающего, является ли расширение критическим. </summary>
      <param name="enhancedKeyUsages">Коллекция <see cref="T:System.Security.Cryptography.OidCollection" />. </param>
      <param name="critical">Значение true, если расширение является критическим, в противном случае — false.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Указанный объект <see cref="T:System.Security.Cryptography.OidCollection" /> содержит одно или несколько поврежденных значений.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension" /> с помощью объекта <see cref="T:System.Security.Cryptography.AsnEncodedData" />.</summary>
      <param name="asnEncodedData">Закодированные данные, используемые для создания расширения.</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.EnhancedKeyUsages">
      <summary>Получает коллекцию идентификаторов объекта (OID), которые указывают приложения, использующие ключ.</summary>
      <returns>Объект <see cref="T:System.Security.Cryptography.OidCollection" />, который указывает приложения, использующие ключ.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Extension">
      <summary>Представляет расширение X509.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" />.</summary>
      <param name="encodedExtension">Закодированные данные, используемые для создания расширения.</param>
      <param name="critical">Значение true, если расширение является критическим, в противном случае — false.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.#ctor(System.Security.Cryptography.Oid,System.Byte[],System.Boolean)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" />.</summary>
      <param name="oid">Идентификатор объекта, используемый для определения расширения.</param>
      <param name="rawData">Закодированные данные, используемые для создания расширения.</param>
      <param name="critical">Значение true, если расширение является критическим, в противном случае — false.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="oid" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="oid" /> является пустой строкой ("").</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.#ctor(System.String,System.Byte[],System.Boolean)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" />.</summary>
      <param name="oid">Строка, представляющая идентификатор объекта.</param>
      <param name="rawData">Закодированные данные, используемые для создания расширения.</param>
      <param name="critical">Значение true, если расширение является критическим, в противном случае — false.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>Копирует свойства расширения указанного объекта <see cref="T:System.Security.Cryptography.AsnEncodedData" />.</summary>
      <param name="asnEncodedData">Копируемый объект <see cref="T:System.Security.Cryptography.AsnEncodedData" />.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="asnEncodedData" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentException">В классе <paramref name="asnEncodedData" /> отсутствует допустимое расширение X.509.</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Extension.Critical">
      <summary>Получает логическое значение, определяющее, является ли расширение критическим.</summary>
      <returns>Значение true, если расширение является критическим, в противном случае — false.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection">
      <summary>Представляет коллекцию объектов <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" />.Этот класс не наследуется.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />. </summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.Add(System.Security.Cryptography.X509Certificates.X509Extension)">
      <summary>Добавляет объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> в объект <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />.</summary>
      <returns>Индекс, по которому был добавлен параметр <paramref name="extension" />.</returns>
      <param name="extension">Объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> для добавления к объекту <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />. </param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="extension" /> — null.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.CopyTo(System.Security.Cryptography.X509Certificates.X509Extension[],System.Int32)">
      <summary>Копирует элемент коллекции в массив, начиная с заданного индекса.</summary>
      <param name="array">Массив объектов <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" />. </param>
      <param name="index">Расположение в массиве, с которого начинается копирование. </param>
      <exception cref="T:System.ArgumentException">В параметре <paramref name="index" /> содержится строка нулевой длины или недопустимое значение. </exception>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="index" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="index" /> задает значение, не входящее в массив. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.Count">
      <summary>Получает число объектов <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" />в объекте <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />.</summary>
      <returns>Целое число, представляющее число объектов <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> в объекте <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.GetEnumerator">
      <summary>Возвращает перечислитель, который может выполнять итерацию объекта <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />.</summary>
      <returns>Объект <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator" />, используемый для выполнения итерации в объекте <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.IsSynchronized">
      <summary>Получает значение, показывающее, гарантируется ли потокобезопасность коллекции.</summary>
      <returns>Значение true, если коллекция является потокобезопасной; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.Item(System.Int32)">
      <summary>Получает объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> по указанному индексу.</summary>
      <returns>Объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" />.</returns>
      <param name="index">Расположение объекта <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> для извлечения. </param>
      <exception cref="T:System.InvalidOperationException">Значение параметра <paramref name="index" /> меньше нуля. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> больше или равно длине массива. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.Item(System.String)">
      <summary>Получает первый объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" />, значение или понятное имя которого определяется идентификатором объекта.</summary>
      <returns>Объект <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" />.</returns>
      <param name="oid">Идентификатор объекта расширения для извлечения. </param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.SyncRoot">
      <summary>Получает объект, который можно использовать для синхронизации доступа к объекту <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />.</summary>
      <returns>Объект, который можно использовать для синхронизации доступа к объекту <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Копирует коллекцию в массив, начиная с заданного индекса.</summary>
      <param name="array">Массив объектов <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" />. </param>
      <param name="index">Расположение в массиве, с которого начинается копирование. </param>
      <exception cref="T:System.ArgumentException">В параметре <paramref name="index" /> содержится строка нулевой длины или недопустимое значение. </exception>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="index" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="index" /> задает значение, не входящее в массив. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Возвращает перечислитель, который может выполнять итерацию объекта <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />.</summary>
      <returns>Объект <see cref="T:System.Collections.IEnumerator" />, используемый для выполнения итерации в объекте <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator">
      <summary>Поддерживает простую итерацию элементов объекта <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />.Этот класс не наследуется.</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator.Current">
      <summary>Получает текущий элемент в объекте <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />.</summary>
      <returns>Текущий элемент в коллекции <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />.</returns>
      <exception cref="T:System.InvalidOperationException">Перечислитель помещается перед первым элементом коллекции или после последнего элемента. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator.MoveNext">
      <summary>Перемещает перечислитель к следующему элементу в коллекции <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />.</summary>
      <returns>Значение true, если перечислитель был успешно перемещен к следующему элементу; значение false, если перечислитель достиг конца коллекции.</returns>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator.Reset">
      <summary>Устанавливает перечислитель в исходное положение перед первым элементом в коллекции <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />.</summary>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator.System#Collections#IEnumerator#Current">
      <summary>Получает объект из коллекции.</summary>
      <returns>Текущий элемент в коллекции <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />.</returns>
      <exception cref="T:System.InvalidOperationException">Перечислитель помещается перед первым элементом коллекции или после последнего элемента. </exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509FindType">
      <summary>Задает тип значения, поиск которого выполняется с помощью метода <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" />.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByApplicationPolicy">
      <summary>Параметр <paramref name="findValue" /> для метода <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> должен быть строкой, представляющей либо понятное имя политики применения, либо идентификатор объекта (OID или <see cref="T:System.Security.Cryptography.Oid" />) сертификата.Например, могут использоваться "Шифрованная файловая система" или "1.3.6.1.4.1.311.10.3.4".Для приложения, которое будет локализовано, необходимо использовать значение идентификатора объекта, так как понятное имя локализуется.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByCertificatePolicy">
      <summary>Параметр <paramref name="findValue" /> для метода <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> должен быть строкой, представляющей либо понятное имя, либо идентификатор объекта (OID или <see cref="T:System.Security.Cryptography.Oid" />) политики сертификата.Рекомендуется использовать идентификатор объекта, например, "1.3.6.1.4.1.311.10.3.4".Для приложения, которое будет локализовано, необходимо использовать идентификатор объекта, так как понятное имя локализуется.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByExtension">
      <summary>Параметр <paramref name="findValue" /> для метода <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> должен быть строкой, описывающей искомое расширение.Идентификатор объекта, как правило, используется для направления метода <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> для поиска всех сертификатов, у которых расширение соответствует данному значению идентификатора объекта.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerDistinguishedName">
      <summary>Параметр <paramref name="findValue" /> для метода <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> должен быть строкой, представляющей различающееся имя поставщика сертификата.Это более определенный поиск по сравнению с использованием значения перечисления <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerName" />.Используя значение <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerDistinguishedName" />, метод <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> выполняет сравнение строк с учетом регистра для всего различающегося имени.Поиск по имени поставщика является менее точным.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerName">
      <summary>Параметр <paramref name="findValue" /> для метода <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> должен быть строкой, представляющей имя поставщика сертификата.Это менее определенный поиск по сравнению с использованием значения перечисления <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerDistinguishedName" />.Используя значение <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerName" />, метод <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> выполняет сравнение строк с учетом регистра с помощью предоставленного значения.Например, при передаче строки "MyCA" в метод <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" />, будут найдены все сертификаты, содержащие эту строку, вне зависимости от других значений поставщика.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByKeyUsage">
      <summary>Параметр <paramref name="findValue" /> для метода <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> должен быть либо строкой, представляющей использование ключа, либо целым числом, представляющим битовую маску с содержанием всех запрошенных использований ключа.Для значения строки может быть одновременно указано только одно использование ключа, но метод <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> может использоваться в каскадной последовательности для получения пересечения использований.Например, для параметра <paramref name="findValue" /> можно установить значение "KeyEncipherment" или целое число (0x30 обозначает "KeyEncipherment" и "DataEncipherment").Могут также использоваться значения перечисления <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags" />.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySerialNumber">
      <summary>Параметр <paramref name="findValue" /> метода <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> должен быть строкой, представляющей серийный номер сертификата, как показано диалоговым окном сертификата, но без пробелов, или как возвращается методом <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetSerialNumberString" />. </summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectDistinguishedName">
      <summary>Параметр <paramref name="findValue" /> для метода <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> должен быть строкой, представляющей различающееся имя субъекта сертификата.Это более определенный поиск по сравнению с использованием значения перечисления <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectName" />.Используя значение <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectDistinguishedName" />, метод <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> выполняет сравнение строк с учетом регистра для всего различающегося имени.Поиск по имени субъекта является менее точным.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectKeyIdentifier">
      <summary>Параметр <paramref name="findValue" /> для метода <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> должен быть строкой, представляющей идентификатор ключа субъекта, например "F3E815D45E83B8477B9284113C64EF208E897112", как отображено в интерфейсе пользователя.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectName">
      <summary>Параметр <paramref name="findValue" /> для метода <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> должен быть строкой, представляющей имя субъекта сертификата.Это менее определенный поиск по сравнению с использованием значения перечисления <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectDistinguishedName" />.Используя значение <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectName" />, метод <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> выполняет сравнение строк с учетом регистра с помощью предоставленного значения.Например, при передаче строки "MyCert" в метод <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" />, будут найдены все сертификаты, содержащие эту строку, вне зависимости от других значений субъекта.Поиск по различающемуся имени является более точным.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTemplateName">
      <summary>Параметр <paramref name="findValue" /> для метода <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> должен быть строкой, представляющей имя шаблона сертификата, например "ClientAuth".Имя шаблона представляет собой расширение X509 версии 3, указывающее использования сертификата.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByThumbprint">
      <summary>Параметр <paramref name="findValue" /> для метода <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> должен быть строкой, представляющей отпечаток сертификата.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeExpired">
      <summary>Параметр <paramref name="findValue" /> для метода <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> должен быть значением <see cref="T:System.DateTime" /> местного времени.К примеру, вы можете найти все сертификаты, которые будут действительны до конца года, отделив результаты операции <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> для <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeExpired" /> в последний день года от результатов операции <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> для <see cref="P:System.DateTime.Now" />.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeNotYetValid">
      <summary>Параметр <paramref name="findValue" /> для метода <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> должен быть значением <see cref="T:System.DateTime" /> местного времени.Значение не обязательно должно быть в будущем.К примеру, ожно ипольовать<see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeNotYetValid" />, чтобы найти сертификаты, которые стали действительны в течение текущего года, взяв пересечение результатов операции <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> для <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeNotYetValid" /> последнего дня последнего года с результатами операции <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> для <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeValid" /> для <see cref="P:System.DateTime.Now" />.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeValid">
      <summary>Параметр <paramref name="findValue" /> для метода <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> должен быть значением <see cref="T:System.DateTime" /> местного времени.Можно использовать свойство <see cref="P:System.DateTime.Now" /> для поиска всех действительный в данный момент сертификатов.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags">
      <summary>Определяет, где и как импортируется закрытый ключ сертификата X.509.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.DefaultKeySet">
      <summary>Используется набор ключей по умолчанию.  Пользовательский набор ключей обычно является набором по умолчанию. </summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.Exportable">
      <summary>Импортированные ключи помечаются как экспортируемые.  </summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.MachineKeySet">
      <summary>Закрытые ключи хранятся в хранилище локального компьютера, а не в хранилище текущего пользователя. </summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.PersistKeySet">
      <summary>Ключ, связанный с PFX-файлом, сохраняется при импорте сертификата.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.UserKeySet">
      <summary>Закрытые ключи хранятся в хранилище текущего пользователя, а не в хранилище локального компьютера.Это происходит, даже если сертификат указывает, что ключи должны храниться в хранилище локального компьютера.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.UserProtected">
      <summary>Уведомите пользователя о доступе к ключу с помощью диалогового окна или другого метода.  Используемый поставщик служб шифрования (CSP) определяет точный характер поведения.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension">
      <summary>Определяет использование ключа, содержащегося в сертификате X.509.  Этот класс не наследуется.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension" /> с использованием объекта <see cref="T:System.Security.Cryptography.AsnEncodedData" /> и значения, указывающего, является ли расширение критическим. </summary>
      <param name="encodedKeyUsage">Закодированные данные, используемые для создания расширения.</param>
      <param name="critical">Значение true, если расширение является критическим, в противном случае — false.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.#ctor(System.Security.Cryptography.X509Certificates.X509KeyUsageFlags,System.Boolean)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension" /> с использованием заданного значения <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags" /> и значения, указывающего, является ли расширение критическим. </summary>
      <param name="keyUsages">Одно из значений <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags" />, описывающее использование ключа.</param>
      <param name="critical">Значение true, если расширение является критическим, в противном случае — false.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension" /> с помощью объекта <see cref="T:System.Security.Cryptography.AsnEncodedData" />. </summary>
      <param name="asnEncodedData">Закодированные данные, используемые для создания расширения.</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.KeyUsages">
      <summary>Получает флаг использования ключа, связанный с сертификатом.</summary>
      <returns>Одно из значений <see cref="P:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.KeyUsages" />.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Расширение не может быть декодировано. </exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags">
      <summary>Определяет способы использования ключа сертификата.Если это значение не определено, ключ может использоваться для любой цели.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.CrlSign">
      <summary>Ключ может использоваться для подписи списка отзыва сертификатов (CRL).</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.DataEncipherment">
      <summary>Ключ может использоваться для шифрования данных.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.DecipherOnly">
      <summary>Ключ может использоваться только для расшифровки.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.DigitalSignature">
      <summary>Ключ может использоваться в качестве цифровой подписи.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.EncipherOnly">
      <summary>Ключ может использоваться только для шифрования.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.KeyAgreement">
      <summary>Ключ может использоваться для определения согласования ключа, например, ключ, созданный с использованием алгоритма согласования ключей Диффи-Хеллмана.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.KeyCertSign">
      <summary>Ключ может использоваться для подписи сертификатов.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.KeyEncipherment">
      <summary>Ключ может использоваться для шифрования ключа.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.None">
      <summary>Отсутствуют параметры использования ключа.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.NonRepudiation">
      <summary>Ключ может использоваться для проверки подлинности.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509NameType">
      <summary>Задает тип имени сертификата X509.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.DnsFromAlternativeName">
      <summary>DNS-имя, связанное с альтернативным именем субъекта или поставщика сертификата X509.  Это значение эквивалентно значению <see cref="F:System.Security.Cryptography.X509Certificates.X509NameType.DnsName" />.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.DnsName">
      <summary>DNS-имя, связанное с альтернативным именем субъекта или поставщика сертификата X509.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.EmailName">
      <summary>Адрес электронной почты субъекта или поставщика сертификата X509.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.SimpleName">
      <summary>Простое имя субъекта или поставщика сертификата X509.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.UpnName">
      <summary>Имя участника-пользователя (UPN) субъекта или поставщика сертификата X509.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.UrlName">
      <summary>URL-адрес, связанный с альтернативным именем субъекта или поставщика сертификата X509.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509RevocationFlag">
      <summary>Указывает, какие сертификаты X509 в цепочке должны быть проверены на отзыв.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationFlag.EndCertificateOnly">
      <summary>Проверяется, не был ли отозван конечный сертификат.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationFlag.EntireChain">
      <summary>Проверяется, не была ли отозвана вся цепочка сертификатов.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationFlag.ExcludeRoot">
      <summary>Вся цепочка, за исключением корневого сертификата, проверяется на отзыв сертификатов.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509RevocationMode">
      <summary>Задает режим, используемый для проверки отзыва сертификата X509.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationMode.NoCheck">
      <summary>Проверка отзыва сертификата не выполняется.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationMode.Offline">
      <summary>Проверка выполняется с помощью кэшированного списка отзыва сертификатов (CRL).</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationMode.Online">
      <summary>Проверка выполняется с помощью списка отзыва сертификатов с подключением к сети.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Store">
      <summary>Представляет хранилище X.509, которое является физическими хранилищем, используемым для хранения сертификатов X.509 и управления ими.Этот класс не наследуется.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.#ctor">
      <summary>Инициализирует новый класс <see cref="T:System.Security.Cryptography.X509Certificates.X509Store" /> с использованием личных сертификатов из хранилища текущего пользователя.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.#ctor(System.Security.Cryptography.X509Certificates.StoreName,System.Security.Cryptography.X509Certificates.StoreLocation)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509Store" />, используя указанные объекты <see cref="T:System.Security.Cryptography.X509Certificates.StoreName" /> и значения <see cref="T:System.Security.Cryptography.X509Certificates.StoreLocation" />.</summary>
      <param name="storeName">Одно из значений перечисления, указывающее имя хранилища сертификатов X.509. </param>
      <param name="storeLocation">Одно из значений перечисления, определяющее расположение хранилища сертификатов X.509. </param>
      <exception cref="T:System.ArgumentException">Расположения <paramref name="storeLocation" /> или имя <paramref name="storeName" /> недопустимы. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.#ctor(System.String,System.Security.Cryptography.X509Certificates.StoreLocation)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509Store" /> с помощью строки, представляющей значение из перечисления <see cref="T:System.Security.Cryptography.X509Certificates.StoreName" /> и <see cref="T:System.Security.Cryptography.X509Certificates.StoreLocation" />.</summary>
      <param name="storeName">Строка, представляющая значение из перечисления <see cref="T:System.Security.Cryptography.X509Certificates.StoreName" />. </param>
      <param name="storeLocation">Одно из значений перечисления, определяющее расположение хранилища сертификатов X.509. </param>
      <exception cref="T:System.ArgumentException">Объект <paramref name="storeLocation" /> содержит недопустимые значения. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.Add(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Добавляет сертификат в хранилище сертификатов X.509.</summary>
      <param name="certificate">Добавляемый сертификат. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" />is null. </exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Не удалось добавить сертификат в хранилище.</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Store.Certificates">
      <summary>Возвращает коллекцию сертификатов, расположенную в хранилище сертификатов X.509.</summary>
      <returns>Коллекция сертификатов.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.Dispose">
      <summary>Освобождает ресурсы, используемые <see cref="T:System.Security.Cryptography.X509Certificates.X509Store" />.</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Store.Location">
      <summary>Получает расположение хранилища сертификатов X.509.</summary>
      <returns>Расположение хранилища сертификатов.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Store.Name">
      <summary>Возвращает имя хранилища сертификатов X.509.</summary>
      <returns>Имя хранилища сертификатов.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.Open(System.Security.Cryptography.X509Certificates.OpenFlags)">
      <summary>Открывает хранилище сертификатов X.509 или создает новое хранилище, в зависимости от параметров флага <see cref="T:System.Security.Cryptography.X509Certificates.OpenFlags" />.</summary>
      <param name="flags">Побитовое сочетание значений перечисления, определяющее способ открывания хранилища сертификатов X.509. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Хранилище не читается. </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentException">Хранилище содержит недопустимые значения.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.Remove(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Удаляет сертификат из хранилища сертификатов X.509.</summary>
      <param name="certificate">Сертификат, подлежащий удалению.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" />is null. </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension">
      <summary>Определяет строку, указывающую идентификатор ключа субъекта (SKI) для сертификата.Этот класс не наследуется.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.Byte[],System.Boolean)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> с помощью массива байтов и значения, указывающего, является ли расширение критическим.</summary>
      <param name="subjectKeyIdentifier">Массив байтов, который представляет данные, используемые для создания расширения.</param>
      <param name="critical">Значение true, если расширение является критическим, в противном случае — false.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> с помощью закодированных данных и значения, указывающего, является ли расширение критическим.</summary>
      <param name="encodedSubjectKeyIdentifier">Объект <see cref="T:System.Security.Cryptography.AsnEncodedData" />, используемый для создания расширения.</param>
      <param name="critical">Значение true, если расширение является критическим, в противном случае — false.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.Security.Cryptography.X509Certificates.PublicKey,System.Boolean)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> с помощью открытого ключа и значения, указывающего, является ли расширение критическим.</summary>
      <param name="key">Объект <see cref="T:System.Security.Cryptography.X509Certificates.PublicKey" />, из которого создается идентификатор ключа субъекта (SKI). </param>
      <param name="critical">Значение true, если расширение является критическим, в противном случае — false.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.Security.Cryptography.X509Certificates.PublicKey,System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm,System.Boolean)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> с помощью открытого ключа, идентификатора алгоритма хэша и значения, указывающего, является ли расширение критическим. </summary>
      <param name="key">Объект <see cref="T:System.Security.Cryptography.X509Certificates.PublicKey" />, из которого создается идентификатор ключа субъекта (SKI).</param>
      <param name="algorithm">Одно из значений <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm" />, которое определяет используемый алгоритм хэша.</param>
      <param name="critical">Значение true, если расширение является критическим, в противном случае — false.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.String,System.Boolean)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> с помощью строки и значения, указывающего, является ли расширение критическим.</summary>
      <param name="subjectKeyIdentifier">Строка в шестнадцатеричной кодировке, представляющая идентификатор ключа субъекта (SKI) для сертификата.</param>
      <param name="critical">Значение true, если расширение является критическим, в противном случае — false.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>Создает новый экземпляр класса <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> с помощью копирования информации их закодированных данных.</summary>
      <param name="asnEncodedData">Объект <see cref="T:System.Security.Cryptography.AsnEncodedData" />, используемый для создания расширения.</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.SubjectKeyIdentifier">
      <summary>Получает строку, представляющую идентификатор ключа субъекта (SKI) для сертификата.</summary>
      <returns>Строка в шестнадцатеричной кодировке, представляющая идентификатор ключа субъекта (SKI).</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Расширение не может быть декодировано. </exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm">
      <summary>Определяет тип хэш-алгоритма для использования с классом <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" />.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm.CapiSha1">
      <summary>Идентификатор ключа субъекта (SKI) состоит из 160-разрядного хэш-значения SHA-1 закодированного открытого ключа (включая тег, длину и количество неиспользуемых разрядов).</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm.Sha1">
      <summary>Идентификатор ключа субъекта (SKI) состоит из 160-разрядного хэша SHA-1 значения открытого ключа (исключая тег, длину и количество неиспользуемых разрядов).</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm.ShortSha1">
      <summary>Идентификатор ключа субъекта (SKI) состоит из поля 4-разрядного типа со значением 0100, за которым следует не менее 60 значащих разрядов хэш-значения SHA-1 открытого ключа (исключая тег, длину и количество неиспользуемых разрядов двоичных строк).</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509VerificationFlags">
      <summary>Определяет условия, при которых должна проводиться проверка сертификатов в цепочке X509.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.AllFlags">
      <summary>Включены все флаги, относящиеся к проверке.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.AllowUnknownCertificateAuthority">
      <summary>Не учитывать, что цепочку нельзя проверить из-за неизвестного центра сертификации (ЦС).</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreCertificateAuthorityRevocationUnknown">
      <summary>При проверке сертификата не учитывать, что отзыв центра сертификации неизвестен.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreCtlNotTimeValid">
      <summary>При проверке сертификата не учитывать, что список доверия сертификатов (CTL) недействителен, например, из-за истечения срока действия списка доверия сертификатов.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreCtlSignerRevocationUnknown">
      <summary>При проверке сертификата не учитывать, что отзыв подписавшего список доверия сертификатов (CTL) неизвестен.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreEndRevocationUnknown">
      <summary>При проверке сертификата не учитывать, что отзыв конечного сертификата (сертификата пользователя) неизвестен.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreInvalidBasicConstraints">
      <summary>При проверке сертификата не учитывать, что основные ограничения недопустимы.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreInvalidName">
      <summary>При проверке сертификата не учитывать, что сертификат имеет недопустимое имя.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreInvalidPolicy">
      <summary>При проверке сертификата не учитывать, что сертификат имеет недопустимую политику.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreNotTimeNested">
      <summary>При проверке сертификата не учитывать, что сертификат центра сертификации (ЦС) и выданный сертификат имеют сроки действия, которые не являются вложенными.Например, сертификат центра сертификации (ЦС) может быть действителен с 1 января по 1 декабря, а выданный сертификат — со 2 января по 2 декабря. Это значит, что сроки действия не являются вложенными.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreNotTimeValid">
      <summary>При проверке сертификата не учитывать сертификаты в цепочке, которые недействительны, так как срок их действия истек или не наступил.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreRootRevocationUnknown">
      <summary>При проверке сертификата не учитывать, что корневой отзыв неизвестен.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreWrongUsage">
      <summary>При проверке сертификата не учитывать, что сертификат был выдан не текущему пользователю.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.NoFlag">
      <summary>Не включены флаги, относящиеся к проверке.</summary>
    </member>
  </members>
</doc>