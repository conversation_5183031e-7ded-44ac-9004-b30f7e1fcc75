﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Security.Cryptography.X509Certificates</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Win32.SafeHandles.SafeX509ChainHandle">
      <summary>提供表示 X.509 链的安全句柄。有关详细信息，请参阅<see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" />。</summary>
    </member>
    <member name="P:Microsoft.Win32.SafeHandles.SafeX509ChainHandle.IsInvalid"></member>
    <member name="T:System.Security.Cryptography.X509Certificates.OpenFlags">
      <summary>指定打开 X.509 证书存储区的方式。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.IncludeArchived">
      <summary>打开 X.509 证书存储区并添加存档证书。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.MaxAllowed">
      <summary>以允许最高级访问的方式打开 X.509 证书存储区。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.OpenExistingOnly">
      <summary>仅打开现有存储区。如果不存在任何存储区，<see cref="M:System.Security.Cryptography.X509Certificates.X509Store.Open(System.Security.Cryptography.X509Certificates.OpenFlags)" /> 方法不会创建新的存储区。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.ReadOnly">
      <summary>以只读方式打开 X.509 证书存储区。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.ReadWrite">
      <summary>以读写方式打开 X.509 证书存储区。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.PublicKey">
      <summary>表示证书的公钥信息。此类不能被继承。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.PublicKey.#ctor(System.Security.Cryptography.Oid,System.Security.Cryptography.AsnEncodedData,System.Security.Cryptography.AsnEncodedData)">
      <summary>使用公钥的对象标识符 (OID) 对象、公钥参数的 ASN.1 编码表示形式以及公钥值的 ASN.1 编码表示形式初始化 <see cref="T:System.Security.Cryptography.X509Certificates.PublicKey" /> 类的新实例。</summary>
      <param name="oid">表示公钥的对象标识符 (OID) 对象。</param>
      <param name="parameters">公钥参数的 ASN.1 编码表示形式。</param>
      <param name="keyValue">公钥值的 ASN.1 编码表示形式。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.PublicKey.EncodedKeyValue">
      <summary>获取公钥值的 ASN.1 编码表示形式。</summary>
      <returns>公钥值的 ASN.1 编码表示形式。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.PublicKey.EncodedParameters">
      <summary>获取公钥参数的 ASN.1 编码表示形式。</summary>
      <returns>公钥参数的 ASN.1 编码表示形式。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.PublicKey.Key">
      <summary>获取表示公钥的 <see cref="T:System.Security.Cryptography.RSACryptoServiceProvider" /> 或 <see cref="T:System.Security.Cryptography.DSACryptoServiceProvider" /> 对象。</summary>
      <returns>表示公钥的 <see cref="T:System.Security.Cryptography.AsymmetricAlgorithm" /> 对象。</returns>
      <exception cref="T:System.NotSupportedException">该密钥算法不受支持。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.PublicKey.Oid">
      <summary>获取公钥的对象标识符 (OID) 对象。</summary>
      <returns>公钥的对象标识符 (OID) 对象。</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.StoreLocation">
      <summary>指定 X.509 证书存储区的位置。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreLocation.CurrentUser">
      <summary>当前用户使用的 X.509 证书存储区。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreLocation.LocalMachine">
      <summary>分配给本地计算机的 X.509 证书存储区。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.StoreName">
      <summary>指定要打开的 X.509 证书存储区的名称。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.AddressBook">
      <summary>其他用户的 X.509 证书存储区。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.AuthRoot">
      <summary>第三方证书颁发机构 (CA) 的 X.509 证书存储区。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.CertificateAuthority">
      <summary>中间证书颁发机构 (CA) 的 X.509 证书存储区。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.Disallowed">
      <summary>吊销的证书的 X.509 证书存储区。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.My">
      <summary>个人证书的 X.509 证书存储区。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.Root">
      <summary>受信任的根证书颁发机构 (CA) 的 X.509 证书存储区。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.TrustedPeople">
      <summary>直接受信任的人和资源的 X.509 证书存储区。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.TrustedPublisher">
      <summary>直接受信任的发行者的 X.509 证书存储区。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName">
      <summary>表示 X509 证书的识别名。此类不能被继承。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.Byte[])">
      <summary>使用指定字节数组中的信息初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> 类的新实例。</summary>
      <param name="encodedDistinguishedName">一个包含识别名信息的字节数组。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.Security.Cryptography.AsnEncodedData)">
      <summary>使用指定的 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 对象初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> 类的新实例。</summary>
      <param name="encodedDistinguishedName">一个表示识别名的 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 对象。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.Security.Cryptography.X509Certificates.X500DistinguishedName)">
      <summary>使用指定的 <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> 对象初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> 类的新实例。</summary>
      <param name="distinguishedName">
        <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> 对象。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.String)">
      <summary>使用指定字符串中的信息初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> 类的新实例。</summary>
      <param name="distinguishedName">一个表示识别名的字符串。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.String,System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags)">
      <summary>使用指定的字符串和 <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags" /> 标志初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> 类的新实例。</summary>
      <param name="distinguishedName">一个表示识别名的字符串。</param>
      <param name="flag">指定区分名特征的枚举值的按位组合。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.Decode(System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags)">
      <summary>使用由 <paramref name="flag" /> 参数指定的特征对识别名进行解码。</summary>
      <returns>解码的识别名。</returns>
      <param name="flag">指定区分名特征的枚举值的按位组合。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">证书名称无效。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.Format(System.Boolean)">
      <summary>返回 X500 识别名的格式化版本，以便打印或输出到文本窗口或控制台。</summary>
      <returns>一个表示 X500 识别名的格式化字符串。</returns>
      <param name="multiLine">如果返回字符串应包含回车，则为 true；否则为 false。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X500DistinguishedName.Name">
      <summary>从 X500 证书获取逗号分隔的识别名。</summary>
      <returns>X509 证书的逗号分隔的识别名。</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags">
      <summary>指定 X.500 可分辨名称的特性。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.DoNotUsePlusSign">
      <summary>可分辨名称不使用加号。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.DoNotUseQuotes">
      <summary>可分辨名称不使用引号。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.ForceUTF8Encoding">
      <summary>强制可分辨名称将特定 X.500 密钥编码为 UTF-8 字符串而不是可打印的 Unicode 字符串。有关受影响的 X.500 密钥的详细信息和列表，请参阅 X500NameFlags 枚举。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.None">
      <summary>可分辨名称没有特殊特性。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.Reversed">
      <summary>反转可分辨名称。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseCommas">
      <summary>可分辨名称使用逗号。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseNewLines">
      <summary>可分辨名称使用新行字符。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseSemicolons">
      <summary>可分辨名称使用分号。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseT61Encoding">
      <summary>可分辨名称使用 T61 编码。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseUTF8Encoding">
      <summary>可分辨名称使用 UTF8 编码而不是 Unicode 字符编码。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension">
      <summary>定义对证书设置的约束。此类不能被继承。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.#ctor">
      <summary>初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.#ctor(System.Boolean,System.Boolean,System.Int32,System.Boolean)">
      <summary>初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension" /> 类的新实例。参数指定以下值：指示证书是否为证书颁发机构 (CA) 证书的值；指示证书对其允许的路径级别数是否有限制、证书路径允许的级别数的值；指示扩展是否重要的值。</summary>
      <param name="certificateAuthority">如果证书为证书颁发机构 (CA) 证书，则为 true；否则为 false。</param>
      <param name="hasPathLengthConstraint">如果证书对其允许的路径级别数有限制，则为 true；否则为 false。</param>
      <param name="pathLengthConstraint">证书路径允许的级别数。</param>
      <param name="critical">如果扩展是必不可少的，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>使用 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 对象和一个标识扩展是否重要的值初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension" /> 类的新实例。</summary>
      <param name="encodedBasicConstraints">用于创建扩展的编码数据。</param>
      <param name="critical">如果扩展是必不可少的，则为 true；否则为 false。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.CertificateAuthority">
      <summary>获取一个值，该值指示证书是否为证书颁发机构 (CA) 证书。</summary>
      <returns>如果证书为证书颁发机构 (CA) 证书，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>使用 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 对象初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension" /> 类的新实例。</summary>
      <param name="asnEncodedData">用于创建扩展的编码数据。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.HasPathLengthConstraint">
      <summary>获取一个值，该值指示证书对其允许的路径级别数是否有限制。</summary>
      <returns>如果证书对其允许的路径级别数有限制，则为 true；否则为 false。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">扩展无法解码。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.PathLengthConstraint">
      <summary>获取证书路径允许的级别数。</summary>
      <returns>一个整数，指示证书路径允许的级别数。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">扩展无法解码。</exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Certificate">
      <summary>提供帮助你使用 X.509 v.3 证书的方法。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor">
      <summary>初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.Byte[])">
      <summary>初始化从表示 X.509v3 证书的字节序列定义的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 类的新实例。</summary>
      <param name="data">一个包含 X.509 证书数据的字节数组。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">发生了与证书有关的错误。例如：证书文件不存在。证书无效。证书的密码不正确。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="rawData" /> 参数为 null。- 或 -<paramref name="rawData" /> 参数的长度为 0。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.Byte[],System.String)">
      <summary>使用一个字节数组和一个密码初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 类的新实例。</summary>
      <param name="rawData">一个包含 X.509 证书数据的字节数组。</param>
      <param name="password">访问 X.509 证书数据所需的密码。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">发生了与证书有关的错误。例如：证书文件不存在。证书无效。证书的密码不正确。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="rawData" /> 参数为 null。- 或 -<paramref name="rawData" /> 参数的长度为 0。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.Byte[],System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>使用一个字节数组、一个密码和一个密钥存储标志初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 类的新实例。</summary>
      <param name="rawData">一个包含 X.509 证书数据的字节数组。</param>
      <param name="password">访问 X.509 证书数据所需的密码。</param>
      <param name="keyStorageFlags">一个枚举值的按位组合，这些值控制在何处以及如何导入证书。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">发生了与证书有关的错误。例如：证书文件不存在。证书无效。证书的密码不正确。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="rawData" /> 参数为 null。- 或 -<paramref name="rawData" /> 参数的长度为 0。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.IntPtr)">
      <summary>[SECURITY CRITICAL] 使用非托管 PCCERT_CONTEXT 结构的句柄初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 类的一个新实例。</summary>
      <param name="handle">非托管 PCCERT_CONTEXT 结构的一个句柄。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.String)">
      <summary>使用 PKCS7 签名文件的名称初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 类的新实例。</summary>
      <param name="fileName">PKCS7 签名文件的名称。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">发生了与证书有关的错误。例如：证书文件不存在。证书无效。证书的密码不正确。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="fileName" /> 参数为 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.String,System.String)">
      <summary>使用 PKCS7 签名文件的名称和一个用于访问该证书的密码初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 类的新实例。</summary>
      <param name="fileName">PKCS7 签名文件的名称。</param>
      <param name="password">访问 X.509 证书数据所需的密码。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">发生了与证书有关的错误。例如：证书文件不存在。证书无效。证书的密码不正确。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="fileName" /> 参数为 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.String,System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>使用 PKCS7 签名文件的名称、一个用于访问该证书的密码和一个密钥存储标志初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 类的新实例。</summary>
      <param name="fileName">PKCS7 签名文件的名称。</param>
      <param name="password">访问 X.509 证书数据所需的密码。</param>
      <param name="keyStorageFlags">一个枚举值的按位组合，这些值控制在何处以及如何导入证书。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">发生了与证书有关的错误。例如：证书文件不存在。证书无效。证书的密码不正确。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="fileName" /> 参数为 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Dispose">
      <summary>释放由当前 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 对象使用的所有资源。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Dispose(System.Boolean)">
      <summary>释放由此使用的非托管资源的所有<see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />并选择性地释放托管的资源。 </summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Equals(System.Object)">
      <summary>比较两个 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 对象是否相等。</summary>
      <returns>如果当前 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 对象等于 <paramref name="other" /> 参数指定的对象，则为 true；否则为 false。</returns>
      <param name="obj">要与当前对象进行比较的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 对象。 </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Equals(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>比较两个 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 对象是否相等。</summary>
      <returns>如果当前 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 对象等于 <paramref name="other" /> 参数指定的对象，则为 true；否则为 false。</returns>
      <param name="other">要与当前对象进行比较的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 对象。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Export(System.Security.Cryptography.X509Certificates.X509ContentType)">
      <summary>以 <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> 值之一所描述的格式将当前 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 对象导出到字节数组。</summary>
      <returns>表示当前 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 对象的字节数组。</returns>
      <param name="contentType">描述如何设置输出数据格式的 <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> 值之一。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">将 <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.Cert" />、<see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.SerializedCert" /> 或 <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pkcs12" /> 之外的一个值传递给 <paramref name="contentType" /> 参数。- 或 -无法导出证书。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.KeyContainerPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="Open, Export" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Export(System.Security.Cryptography.X509Certificates.X509ContentType,System.String)">
      <summary>使用指定的密码，以 <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> 值之一所描述的格式将当前 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 对象导出到字节数组。</summary>
      <returns>表示当前 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 对象的字节数组。</returns>
      <param name="contentType">描述如何设置输出数据格式的 <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> 值之一。</param>
      <param name="password">访问 X.509 证书数据所需的密码。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">将 <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.Cert" />、<see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.SerializedCert" /> 或 <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pkcs12" /> 之外的一个值传递给 <paramref name="contentType" /> 参数。- 或 -无法导出证书。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.KeyContainerPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="Open, Export" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetCertHash">
      <summary>将 X.509v3 证书的哈希值作为字节数组返回。</summary>
      <returns>X.509 证书的哈希值。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetFormat">
      <summary>返回此 X.509v3 证书的格式的名称。</summary>
      <returns>此 X.509 证书的格式。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetHashCode">
      <summary>返回整数形式的 X.509v3 证书的哈希代码。</summary>
      <returns>整数形式的 X.509 证书的哈希代码。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetKeyAlgorithm">
      <summary>将此 X.509v3 证书的密钥算法信息作为字符串返回。</summary>
      <returns>字符串形式的 X.509 证书的密钥算法信息。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">证书上下文无效。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetKeyAlgorithmParameters">
      <summary>将 X.509v3 证书的密钥算法参数作为字节数组返回。</summary>
      <returns>字节数组形式的 X.509 证书的密钥算法参数。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">证书上下文无效。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetKeyAlgorithmParametersString">
      <summary>将 X.509v3 证书的密钥算法参数作为十六进制字符串返回。</summary>
      <returns>十六进制字符串形式的 X.509 证书的密钥算法参数。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">证书上下文无效。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetPublicKey">
      <summary>将 X.509v3 证书的公钥作为字节数组返回。</summary>
      <returns>字节数组形式的 X.509 证书的公钥。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">证书上下文无效。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetSerialNumber">
      <summary>将 X.509v3 证书的序列号作为字节数组返回。</summary>
      <returns>字节数组形式的 X.509 证书的序列号。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">证书上下文无效。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate.Handle">
      <summary>[SECURITY CRITICAL] 获取非托管 PCCERT_CONTEXT 结构所描述的 Microsoft Cryptographic API 证书上下文的句柄。</summary>
      <returns>表示非托管 PCCERT_CONTEXT 结构的 <see cref="T:System.IntPtr" /> 结构。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate.Issuer">
      <summary>获取颁发此 X.509v3 证书的证书颁发机构的名称。</summary>
      <returns>颁发此 X.509v3 证书的证书颁发机构的名称。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">证书句柄无效。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate.Subject">
      <summary>获取证书的主题可分辨名称。</summary>
      <returns>证书的主题可分辨名称。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">证书句柄无效。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.ToString">
      <summary>返回当前 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 对象的字符串表示形式。</summary>
      <returns>当前 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 对象的字符串表示形式。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.ToString(System.Boolean)">
      <summary>返回当前 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 对象的字符串表示形式，如果指定，带有其他信息。</summary>
      <returns>当前 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 对象的字符串表示形式。</returns>
      <param name="fVerbose">true to produce the verbose form of the string representation; otherwise, false.</param>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Certificate2">
      <summary>表示 X.509 证书。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor">
      <summary>初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.Byte[])">
      <summary>使用来自字节数组的信息初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 类的新实例。</summary>
      <param name="rawData">一个包含 X.509 证书数据的字节数组。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">发生了与证书有关的错误。例如：证书文件不存在。证书无效。证书的密码不正确。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.Byte[],System.String)">
      <summary>使用一个字节数组和一个密码初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 类的新实例。</summary>
      <param name="rawData">一个包含 X.509 证书数据的字节数组。</param>
      <param name="password">访问 X.509 证书数据所需的密码。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">发生了与证书有关的错误。例如：证书文件不存在。证书无效。证书的密码不正确。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.Byte[],System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>使用一个字节数组、一个密码和一个密钥存储标志初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 类的新实例。</summary>
      <param name="rawData">一个包含 X.509 证书数据的字节数组。</param>
      <param name="password">访问 X.509 证书数据所需的密码。</param>
      <param name="keyStorageFlags">控制在何处以及如何导入证书的枚举值的按位组合。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">发生了与证书有关的错误。例如：证书文件不存在。证书无效。证书的密码不正确。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.IntPtr)">
      <summary>使用非托管句柄初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 类的新实例。</summary>
      <param name="handle">一个指向非托管代码中的证书上下文的指针。C 结构称为 PCCERT_CONTEXT。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">发生了与证书有关的错误。例如：证书文件不存在。证书无效。证书的密码不正确。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.String)">
      <summary>使用证书文件名初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 类的新实例。</summary>
      <param name="fileName">一个证书文件的名称。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">发生了与证书有关的错误。例如：证书文件不存在。证书无效。证书的密码不正确。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.String,System.String)">
      <summary>使用一个证书文件名和一个用于访问该证书的密码初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 类的新实例。</summary>
      <param name="fileName">一个证书文件的名称。</param>
      <param name="password">访问 X.509 证书数据所需的密码。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">发生了与证书有关的错误。例如：证书文件不存在。证书无效。证书的密码不正确。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.String,System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>使用一个证书文件名、一个用于访问该证书的密码和一个密钥存储标志初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 类的新实例。</summary>
      <param name="fileName">一个证书文件的名称。</param>
      <param name="password">访问 X.509 证书数据所需的密码。</param>
      <param name="keyStorageFlags">控制在何处以及如何导入证书的枚举值的按位组合。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">发生了与证书有关的错误。例如：证书文件不存在。证书无效。证书的密码不正确。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.Archived">
      <summary>获取或设置一个指示是否存档 X.509 证书的值。</summary>
      <returns>如果存档该证书，则为 true；如果不存档该证书，则为 false。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">该证书不可读。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.Extensions">
      <summary>获取 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 对象的集合。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 对象。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">该证书不可读。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.FriendlyName">
      <summary>获取或设置证书的关联别名。</summary>
      <returns>证书的友好名称。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">该证书不可读。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.GetCertContentType(System.Byte[])">
      <summary>指示字节数组中所包含的证书类型。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> 对象。</returns>
      <param name="rawData">一个包含 X.509 证书数据的字节数组。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="rawData" /> 的长度为零或为 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.GetCertContentType(System.String)">
      <summary>指示文件中所包含的证书类型。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> 对象。</returns>
      <param name="fileName">一个证书文件的名称。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="fileName" /> 为 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.GetNameInfo(System.Security.Cryptography.X509Certificates.X509NameType,System.Boolean)">
      <summary>从证书中获取主题和颁发者名称。</summary>
      <returns>证书的名称。</returns>
      <param name="nameType">该主题的 <see cref="T:System.Security.Cryptography.X509Certificates.X509NameType" /> 值。</param>
      <param name="forIssuer">如果包含颁发者名称，则为 true；否则为 false。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.HasPrivateKey">
      <summary>获取一个值，该值指示 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 对象是否包含私钥。</summary>
      <returns>如果 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 对象包含私钥，则为 true；否则为 false。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">证书上下文无效。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.IssuerName">
      <summary>获取证书颁发者的可分辨名称。</summary>
      <returns>一个 <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> 对象，包含证书颁发者的名称。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">证书上下文无效。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.NotAfter">
      <summary>获取本地时间中的一个日期，在该日期后证书不再有效。</summary>
      <returns>表示证书的到期日期的 <see cref="T:System.DateTime" /> 对象。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">该证书不可读。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.NotBefore">
      <summary>获取证书生效的本地时间中的日期。</summary>
      <returns>一个表示证书生效日期的 <see cref="T:System.DateTime" /> 对象。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">该证书不可读。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.PrivateKey">
      <summary>获取或设置 <see cref="T:System.Security.Cryptography.AsymmetricAlgorithm" /> 对象，该对象表示与证书关联的私钥。</summary>
      <returns>一个 <see cref="T:System.Security.Cryptography.AsymmetricAlgorithm" /> 对象，表示 RSA 或 DSA 加密服务提供程序。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">密钥值不是 RSA 或 DSA 密钥，或者密钥不可读。</exception>
      <exception cref="T:System.ArgumentNullException">为此属性设置的值为 null。</exception>
      <exception cref="T:System.NotSupportedException">此私钥的密钥算法不受支持。</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicUnexpectedOperationException">X.509 密钥不匹配。</exception>
      <exception cref="T:System.ArgumentException">加密服务提供程序密钥为 null。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.PublicKey">
      <summary>获取一个与证书相关联的 <see cref="P:System.Security.Cryptography.X509Certificates.X509Certificate2.PublicKey" /> 对象。</summary>
      <returns>
        <see cref="P:System.Security.Cryptography.X509Certificates.X509Certificate2.PublicKey" /> 对象。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">密钥值不是 RSA 或 DSA 密钥，或者密钥不可读。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.RawData">
      <summary>获取证书的原始数据。</summary>
      <returns>字节数组形式的证书原始数据。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.SerialNumber">
      <summary>获取证书的序列号。</summary>
      <returns>证书的序列号。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.SignatureAlgorithm">
      <summary>获取用于创建证书签名的算法。</summary>
      <returns>返回签名算法的对象标识符 (<see cref="T:System.Security.Cryptography.Oid" />)。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">该证书不可读。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.SubjectName">
      <summary>获取证书的主题可分辨名称。</summary>
      <returns>一个 <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> 对象，用于表示证书主题的名称。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">证书上下文无效。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.Thumbprint">
      <summary>获取证书的指纹。</summary>
      <returns>证书的指纹。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.ToString">
      <summary>以文本格式显示 X.509 证书。</summary>
      <returns>证书信息。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.ToString(System.Boolean)">
      <summary>以文本格式显示 X.509 证书。</summary>
      <returns>证书信息。</returns>
      <param name="verbose">为 true 则显示公钥、私钥、扩展等；为 false 则显示类似于 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 类的信息，包括指纹、序列号、主题和颁发者名称等。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.Version">
      <summary>获取证书的 X.509 格式版本。</summary>
      <returns>证书格式。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">该证书不可读。</exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection">
      <summary>表示 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 对象的集合。此类不能被继承。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.#ctor">
      <summary>不使用任何 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 信息初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>使用 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 对象初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 类的新实例。</summary>
      <param name="certificate">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 对象，集合从该对象开始。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2[])">
      <summary>使用 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 对象的数组初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 类的新实例。</summary>
      <param name="certificates">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 对象的数组。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2Collection)">
      <summary>使用指定的证书集合初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 类的新实例。</summary>
      <param name="certificates">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 对象。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Add(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>将对象添加到 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 的结尾处。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 索引，已在此处添加了 <paramref name="certificate" />。</returns>
      <param name="certificate">表示为 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 对象的 X.509 证书。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> 为 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.AddRange(System.Security.Cryptography.X509Certificates.X509Certificate2[])">
      <summary>将数组中的多个 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 对象添加到 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 对象。</summary>
      <param name="certificates">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 对象的数组。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificates" /> 为 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.AddRange(System.Security.Cryptography.X509Certificates.X509Certificate2Collection)">
      <summary>将 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 对象中的多个 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 对象添加到另一个 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 对象。</summary>
      <param name="certificates">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificates" /> 为 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Contains(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>确定 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 对象是否包含特定证书。</summary>
      <returns>如果 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 包含指定的 <paramref name="certificate" />，则为 true；否则为 false。</returns>
      <param name="certificate">要在集合中定位的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> 为 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Export(System.Security.Cryptography.X509Certificates.X509ContentType)">
      <summary>将 X.509 证书信息导出到字节数组中。</summary>
      <returns>字节数组中的 X.509 证书信息。</returns>
      <param name="contentType">受支持的 <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> 对象。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Export(System.Security.Cryptography.X509Certificates.X509ContentType,System.String)">
      <summary>使用密码将 X.509 证书信息导出到字节数组中。</summary>
      <returns>字节数组中的 X.509 证书信息。</returns>
      <param name="contentType">受支持的 <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> 对象。</param>
      <param name="password">用于保护字节数组的字符串。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">证书不可读，内容无效，或证书要求密码，但提供的密码不正确因而未能导出私钥。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)">
      <summary>使用 <see cref="T:System.Security.Cryptography.X509Certificates.X509FindType" /> 枚举和 <paramref name="findValue" /> 对象所指定的搜索条件搜索 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 对象。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 对象。</returns>
      <param name="findType">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509FindType" /> 值之一。</param>
      <param name="findValue">作为对象的搜索条件。</param>
      <param name="validOnly">如果仅允许从搜索中返回有效的证书，则为 true；否则为 false。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="findType" /> 无效。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.GetEnumerator">
      <summary>返回可循环访问 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 对象的枚举数。</summary>
      <returns>可循环访问 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 对象的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator" /> 对象。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Import(System.Byte[])">
      <summary>将字节数组形式的证书导入到 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 对象。</summary>
      <param name="rawData">一个包含 X.509 证书数据的字节数组。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Import(System.Byte[],System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>将需要密码才能访问的、字节数组形式的证书导入到 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 对象。</summary>
      <param name="rawData">包含 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 对象的数据的字节数组。</param>
      <param name="password">访问证书信息所需的密码。</param>
      <param name="keyStorageFlags">控制如何导入证书以及导入地点的枚举值的按位组合。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Import(System.String)">
      <summary>将证书文件导入到 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 对象。</summary>
      <param name="fileName">包含证书信息的文件的名称。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Import(System.String,System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>将要求密码的证书文件导入到 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 对象。</summary>
      <param name="fileName">包含证书信息的文件的名称。</param>
      <param name="password">访问证书信息所需的密码。</param>
      <param name="keyStorageFlags">控制如何导入证书以及导入地点的枚举值的按位组合。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Insert(System.Int32,System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>将对象插入到 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 对象的指定索引位置。</summary>
      <param name="index">从零开始的索引，在该处插入 <paramref name="certificate" />。</param>
      <param name="certificate">要插入的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 对象。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于零。- 或 -<paramref name="index" /> 大于 <see cref="P:System.Collections.CollectionBase.Count" /> 属性。</exception>
      <exception cref="T:System.NotSupportedException">该集合为只读。- 或 -该集合的大小是固定的。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> 为 null。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Item(System.Int32)">
      <summary>获取或设置位于指定索引处的元素。</summary>
      <returns>位于指定索引处的元素。</returns>
      <param name="index">要获得或设置的元素从零开始的索引。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于零。- 或 -<paramref name="index" /> 等于或大于 <see cref="P:System.Collections.CollectionBase.Count" /> 属性。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="index" /> 为 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Remove(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>从 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 对象中移除证书的第一个匹配项。</summary>
      <param name="certificate">要从 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 对象中移除的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> 为 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.RemoveRange(System.Security.Cryptography.X509Certificates.X509Certificate2[])">
      <summary>从 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 对象中移除数组中的多个 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 对象。</summary>
      <param name="certificates">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 对象的数组。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificates" /> 为 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.RemoveRange(System.Security.Cryptography.X509Certificates.X509Certificate2Collection)">
      <summary>从 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 对象中移除另一个 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 对象中的多个 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 对象。</summary>
      <param name="certificates">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificates" /> 为 null。</exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator">
      <summary>支持在 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 对象上进行简单迭代。此类不能被继承。</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.Current">
      <summary>获取 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 对象中的当前元素。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 对象中的当前元素。</returns>
      <exception cref="T:System.InvalidOperationException">枚举数定位在该集合的第一个元素之前或最后一个元素之后。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.MoveNext">
      <summary>将枚举数前移到 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 对象中的下一个元素。</summary>
      <returns>如果枚举数成功地推进到下一个元素，则为 true；如果枚举数越过集合的结尾，则为 false。</returns>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.Reset">
      <summary>将枚举数设置为其初始位置，该位置位于 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 对象中第一个元素之前。</summary>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.System#Collections#IEnumerator#Current">
      <summary>有关此成员的说明，请参见 <see cref="P:System.Collections.IEnumerator.Current" />。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 对象中的当前元素。</returns>
      <exception cref="T:System.InvalidOperationException">枚举数定位在该集合的第一个元素之前或最后一个元素之后。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.System#Collections#IEnumerator#MoveNext">
      <summary>有关此成员的说明，请参见 <see cref="M:System.Collections.IEnumerator.MoveNext" />。</summary>
      <returns>如果枚举数成功地推进到下一个元素，则为 true；如果枚举数越过集合的结尾，则为 false。</returns>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.System#Collections#IEnumerator#Reset">
      <summary>有关此成员的说明，请参见 <see cref="M:System.Collections.IEnumerator.Reset" />。</summary>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection">
      <summary>定义存储 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 对象的集合。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.#ctor">
      <summary>初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate[])">
      <summary>从 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 对象数组初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 类的新实例。</summary>
      <param name="value">用来初始化新对象的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 对象数组。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.#ctor(System.Security.Cryptography.X509Certificates.X509CertificateCollection)">
      <summary>从其他 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 类的新实例。</summary>
      <param name="value">用于初始化新对象的 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Add(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>将具有指定值的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 添加到当前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />。</summary>
      <returns>当前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 中插入新的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 处的索引。</returns>
      <param name="value">要添加到当前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.AddRange(System.Security.Cryptography.X509Certificates.X509Certificate[])">
      <summary>将 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 类型数组的元素复制到当前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 的结尾。</summary>
      <param name="value">包含要添加到当前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 的对象的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 类型的数组。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 参数为 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.AddRange(System.Security.Cryptography.X509Certificates.X509CertificateCollection)">
      <summary>将指定的 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 的元素复制到当前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 的结尾。</summary>
      <param name="value">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />，包含要添加到集合的对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 参数为 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Clear"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Contains(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>获取一个值，该值指示当前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 是否包含指定的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />。</summary>
      <returns>如果 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 包含在此集合中，则为 true；否则为 false。</returns>
      <param name="value">要定位的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.CopyTo(System.Security.Cryptography.X509Certificates.X509Certificate[],System.Int32)">
      <summary>将当前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 中的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 值复制到一维 <see cref="T:System.Array" /> 实例的指定索引处。</summary>
      <param name="array">一维 <see cref="T:System.Array" />，是从 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 复制的值的目标。</param>
      <param name="index">
        <paramref name="array" /> 中要开始复制的索引。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> 参数是多维的。- 或 -<see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 中的元素数目大于 <paramref name="arrayIndex" /> 和 <paramref name="array" /> 结尾之间的可用空间。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> 参数小于 <paramref name="array" /> 参数的下限。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Count"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.GetEnumerator">
      <summary>返回可循环访问 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 的枚举数。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 的子元素的枚举数，可用于循环访问该集合。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.GetHashCode">
      <summary>生成基于包含在当前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 中的所有值的哈希值。</summary>
      <returns>基于包含在当前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 中的所有值的哈希值。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.IndexOf(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>返回当前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 中的指定 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 的索引。</summary>
      <returns>如果找到，则为由 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 中的 <paramref name="value" /> 参数指定的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 的索引；否则为 -1。</returns>
      <param name="value">要定位的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Insert(System.Int32,System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>将 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 插入当前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 中的指定索引位置。</summary>
      <param name="index">应插入 <paramref name="value" /> 处的从零开始的索引。</param>
      <param name="value">要插入的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Item(System.Int32)">
      <summary>获取或设置当前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 的指定索引处的项。</summary>
      <returns>当前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 的指定索引处的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />。</returns>
      <param name="index">要在当前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 中定位的项的从零开始的索引。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 参数不在集合的索引的有效范围内。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Remove(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>从当前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 中移除特定的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />。</summary>
      <param name="value">要从当前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 中移除的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />。</param>
      <exception cref="T:System.ArgumentException">由 <paramref name="value" /> 参数指定的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 未在当前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 中找到。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.RemoveAt(System.Int32)"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)"></member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#ICollection#IsSynchronized"></member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#ICollection#SyncRoot"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IEnumerable#GetEnumerator"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#Add(System.Object)"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#Contains(System.Object)"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#IndexOf(System.Object)"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#Insert(System.Int32,System.Object)"></member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#IsFixedSize"></member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#IsReadOnly"></member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#Item(System.Int32)"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#Remove(System.Object)"></member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator">
      <summary>枚举 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 中的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 对象。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.#ctor(System.Security.Cryptography.X509Certificates.X509CertificateCollection)">
      <summary>为指定的 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator" /> 类的新实例。</summary>
      <param name="mappings">要枚举的 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.Current">
      <summary>获取 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 中的当前 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 中的当前 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />。</returns>
      <exception cref="T:System.InvalidOperationException">枚举数定位在该集合的第一个元素之前或最后一个元素之后。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.MoveNext">
      <summary>将枚举数推进到集合的下一个元素。</summary>
      <returns>如果枚举数成功地推进到下一个元素，则为 true；如果枚举数越过集合的结尾，则为 false。</returns>
      <exception cref="T:System.InvalidOperationException">集合在实例化枚举数后发生了修改。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.Reset">
      <summary>将枚举数设置为其初始位置，该位置位于集合中第一个元素之前。</summary>
      <exception cref="T:System.InvalidOperationException">实例化枚举数之后修改了集合。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.System#Collections#IEnumerator#Current">
      <summary>有关此成员的说明，请参见 <see cref="P:System.Collections.IEnumerator.Current" />。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 对象中的当前 X.509 证书对象。</returns>
      <exception cref="T:System.InvalidOperationException">枚举数定位在该集合的第一个元素之前或最后一个元素之后。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.System#Collections#IEnumerator#MoveNext">
      <summary>有关此成员的说明，请参见 <see cref="M:System.Collections.IEnumerator.MoveNext" />。</summary>
      <returns>如果枚举数成功地推进到下一个元素，则为 true；如果枚举数越过集合的结尾，则为 false。</returns>
      <exception cref="T:System.InvalidOperationException">集合在实例化枚举数后发生了修改。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.System#Collections#IEnumerator#Reset">
      <summary>有关此成员的说明，请参见 <see cref="M:System.Collections.IEnumerator.Reset" />。</summary>
      <exception cref="T:System.InvalidOperationException">集合在实例化枚举数后发生了修改。</exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Chain">
      <summary>表示 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 证书的链生成引擎。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Chain.#ctor">
      <summary>初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Chain.Build(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>使用 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" /> 所指定的策略生成 X.509 链。</summary>
      <returns>如果 X.509 证书有效，则为 true；否则为 false。</returns>
      <param name="certificate">一个 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 对象。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="certificate" /> 不是有效的证书，或者为 null。</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="certificate" /> 不可读。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Chain.ChainElements">
      <summary>获取 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" /> 对象的集合。</summary>
      <returns>一个 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 对象。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Chain.ChainPolicy">
      <summary>获取或设置生成 X.509 证书链时要使用的 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" />。</summary>
      <returns>与此 X.509 链关联的 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" /> 对象。</returns>
      <exception cref="T:System.ArgumentNullException">为此属性设置的值为 null。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Chain.ChainStatus">
      <summary>获取 <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" /> 对象中每个元素的状态。</summary>
      <returns>一个 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainStatus" /> 对象数组。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Chain.Dispose">
      <summary>释放此 <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" /> 使用的所有资源。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Chain.Dispose(System.Boolean)">
      <summary>释放此 <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" /> 使用的非托管资源，并且可选择释放托管资源。</summary>
      <param name="disposing">若要释放托管资源和非托管资源，则为 true；若仅释放非托管资源，则为 false。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Chain.SafeHandle">
      <summary>获取此 <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" /> 实例的安全句柄。</summary>
      <returns>返回 <see cref="T:Microsoft.Win32.SafeHandles.SafeX509ChainHandle" />。</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainElement">
      <summary>表示 X.509 链的一个元素。</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElement.Certificate">
      <summary>在特定链元素处获取 X.509 证书。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 对象。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElement.ChainElementStatus">
      <summary>获取链中的当前 X.509 证书的错误状态。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainStatus" /> 对象的数组。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElement.Information">
      <summary>从非托管证书链结构中获取其他错误信息。</summary>
      <returns>一个字符串，表示 Crypto API 中非托管 CERT_CHAIN_ELEMENT 结构的 pwszExtendedErrorInfo 成员。</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection">
      <summary>表示 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" /> 对象的集合。此类不能被继承。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.CopyTo(System.Security.Cryptography.X509Certificates.X509ChainElement[],System.Int32)">
      <summary>从指定的索引开始，将 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 对象复制到数组中。</summary>
      <param name="array">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" /> 对象的数组。</param>
      <param name="index">一个整数，表示索引值。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">指定的 <paramref name="index" /> 小于零，或者大于等于数组的长度。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> 加上当前计数大于数组的长度。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.Count">
      <summary>获取集合中的元素数。</summary>
      <returns>一个整数，表示集合中的元素数。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.GetEnumerator">
      <summary>获取可用于在链元素集合中定位的 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator" /> 对象。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator" /> 对象。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.IsSynchronized">
      <summary>获取一个值，该值指示链元素的集合是否已同步。</summary>
      <returns>始终返回 false。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.Item(System.Int32)">
      <summary>获取位于指定索引位置的 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" /> 对象。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" /> 对象。</returns>
      <param name="index">一个整数值。</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="index" /> 小于零。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 大于或等于集合的长度。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.SyncRoot">
      <summary>获取一个对象，该对象可用于同步对 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 对象的访问。</summary>
      <returns>对当前对象的指针引用。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>从指定的索引开始，将 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 对象复制到数组中。</summary>
      <param name="array">要向其复制 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 对象的数组。</param>
      <param name="index">开始复制的 <paramref name="array" /> 索引位置。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">指定的 <paramref name="index" /> 小于零，或者大于等于数组的长度。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> 加上当前计数大于数组的长度。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>获取可用于在链元素集合中定位的 <see cref="T:System.Collections.IEnumerator" /> 对象。</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> 对象。</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator">
      <summary>支持在 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 上进行简单迭代。此类不能被继承。</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator.Current">
      <summary>获取 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 中的当前元素。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 中的当前元素。</returns>
      <exception cref="T:System.InvalidOperationException">枚举数定位在该集合的第一个元素之前或最后一个元素之后。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator.MoveNext">
      <summary>将枚举数推进到 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 中的下一个元素。</summary>
      <returns>如果枚举数成功地推进到下一个元素，则为 true；如果枚举数越过集合的结尾，则为 false。</returns>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator.Reset">
      <summary>将枚举数设置到其初始位置，该位置位于 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 中第一个元素之前。</summary>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator.System#Collections#IEnumerator#Current">
      <summary>获取 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 中的当前元素。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 中的当前元素。</returns>
      <exception cref="T:System.InvalidOperationException">枚举数定位在该集合的第一个元素之前或最后一个元素之后。</exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy">
      <summary>表示在生成 X509 证书链时要应用的链策略。此类不能被继承。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainPolicy.#ctor">
      <summary>初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.ApplicationPolicy">
      <summary>获取一个对象标识符 (OID) 集合，这些对象标识符指定证书支持的应用程序策略或增强型密钥用法 (EKU)。</summary>
      <returns>一个 <see cref="T:System.Security.Cryptography.OidCollection" /> 对象。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.CertificatePolicy">
      <summary>获取一个对象标识符 (OID) 集合，这些对象标识符指定证书支持的证书策略。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.OidCollection" /> 对象。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.ExtraStore">
      <summary>表示验证证书链时可由链引擎搜索的其他证书集合。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 对象。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainPolicy.Reset">
      <summary>将 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" /> 成员重置为其默认值。</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.RevocationFlag">
      <summary>获取或设置 X509 吊销标志的值。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509RevocationFlag" /> 对象。</returns>
      <exception cref="T:System.ArgumentException">提供的 <see cref="T:System.Security.Cryptography.X509Certificates.X509RevocationFlag" /> 值不是有效标志。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.RevocationMode">
      <summary>获取或设置 X509 证书吊销模式的值。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509RevocationMode" /> 对象。</returns>
      <exception cref="T:System.ArgumentException">提供的 <see cref="T:System.Security.Cryptography.X509Certificates.X509RevocationMode" /> 值不是有效标志。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.UrlRetrievalTimeout">
      <summary>获取联机吊销验证或下载证书吊销列表 (CRL) 期间所用的时间长度。</summary>
      <returns>
        <see cref="T:System.TimeSpan" /> 对象。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.VerificationFlags">
      <summary>获取证书的验证标志。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509VerificationFlags" /> 枚举中的一个值。</returns>
      <exception cref="T:System.ArgumentException">提供的 <see cref="T:System.Security.Cryptography.X509Certificates.X509VerificationFlags" /> 值不是有效标志。<see cref="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.NoFlag" /> 为默认值。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.VerificationTime">
      <summary>用本地时间表示的验证证书的时间。</summary>
      <returns>一个 <see cref="T:System.DateTime" /> 对象。</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainStatus">
      <summary>提供一个用于存储 X509 链状态和错误信息的简单结构。</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainStatus.Status">
      <summary>指定 X509 链的状态。</summary>
      <returns>一个 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags" /> 值。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainStatus.StatusInformation">
      <summary>指定对 <see cref="P:System.Security.Cryptography.X509Certificates.X509Chain.ChainStatus" /> 值的描述。</summary>
      <returns>可本地化的字符串。</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags">
      <summary>定义 X509 链的状态。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.CtlNotSignatureValid">
      <summary>指定证书信任列表 (CTL) 包含一个无效签名。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.CtlNotTimeValid">
      <summary>指定由于存在无效的时间值（如指示 CTL 已过期的时间值），因此证书信任列表 (CTL) 无效。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.CtlNotValidForUsage">
      <summary>指定证书信任列表 (CTL) 对此用途无效。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.Cyclic">
      <summary>指定未能生成 X509 链。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.HasExcludedNameConstraint">
      <summary>指定由于证书已排除名称约束，因此 X509 链无效。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.HasNotDefinedNameConstraint">
      <summary>指定证书具有未定义的名称约束。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.HasNotPermittedNameConstraint">
      <summary>指定证书具有不允许的名称约束。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.HasNotSupportedNameConstraint">
      <summary>指定证书没有受支持的名称约束或有一个不受支持的名称约束。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.InvalidBasicConstraints">
      <summary>指定由于基本约束无效，因此 X509 链无效。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.InvalidExtension">
      <summary>指定由于扩展无效，因此 X509 链无效。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.InvalidNameConstraints">
      <summary>指定由于名称约束无效，因此 X509 链无效。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.InvalidPolicyConstraints">
      <summary>指定由于策略约束无效，因此 X509 链无效。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NoError">
      <summary>指定 X509 链没有错误。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NoIssuanceChainPolicy">
      <summary>指定在证书中没有证书策略扩展。如果某个组策略已指定所有证书必须具有一个证书策略，则会发生此错误。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NotSignatureValid">
      <summary>指定由于证书签名无效，因此 X509 链无效。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NotTimeNested">
      <summary>不推荐使用。指定 CA（证书颁发机构）证书和所颁发证书的有效期不重叠的情况。例如，CA 证书的有效期限是从 1 月 1 日到 12 月 1 日，而已颁发的证书的有效期限是从 1 月 2 日到 12 月 2 日，这就表示有效期限是不配套的。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NotTimeValid">
      <summary>指定由于存在一个无效的时间值（如一个指示证书已过期的时间值），因此 X509 链无效。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NotValidForUsage">
      <summary>指定密钥用法无效。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.OfflineRevocation">
      <summary>指定 X509 链依赖的联机证书吊销列表 (CRL) 当前为脱机状态。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.PartialChain">
      <summary>指定未能生成一直到根证书的 X509 链。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.RevocationStatusUnknown">
      <summary>指定无法确定证书是否已被吊销。这可能是由于证书吊销列表 (CRL) 处于脱机状态或不可用造成的。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.Revoked">
      <summary>指定由于有一个证书已被吊销，因此 X509 链无效。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.UntrustedRoot">
      <summary>指定由于根证书不可信任，因此 X509 链无效。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ContentType">
      <summary>指定 X.509 证书的格式。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Authenticode">
      <summary>Authenticode X.509 证书。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Cert">
      <summary>单个 X.509 证书。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pfx">
      <summary>PFX 格式的证书。Pfx 值与 Pkcs12 值相同。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pkcs12">
      <summary>PKCS #12 格式的证书。Pkcs12 值与 Pfx 值相同。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pkcs7">
      <summary>PKCS #7 格式的证书。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.SerializedCert">
      <summary>单个序列化 X.509 证书。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.SerializedStore">
      <summary>序列化存储区。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Unknown">
      <summary>未知 X.509 证书。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension">
      <summary>定义对象标识符 (OID) 的集合，该集合指示使用密钥的应用程序。此类不能被继承。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.#ctor">
      <summary>初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>使用 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 对象和一个标识扩展是否重要的值初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension" /> 类的新实例。</summary>
      <param name="encodedEnhancedKeyUsages">用于创建扩展的编码数据。</param>
      <param name="critical">如果扩展是必不可少的，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.#ctor(System.Security.Cryptography.OidCollection,System.Boolean)">
      <summary>使用 <see cref="T:System.Security.Cryptography.OidCollection" /> 和一个标识扩展是否重要的值来初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension" /> 类的新实例。</summary>
      <param name="enhancedKeyUsages">
        <see cref="T:System.Security.Cryptography.OidCollection" /> 集合。</param>
      <param name="critical">如果扩展是必不可少的，则为 true；否则为 false。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">指定的 <see cref="T:System.Security.Cryptography.OidCollection" /> 包含一个或多个损坏的值。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>使用 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 对象初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension" /> 类的新实例。</summary>
      <param name="asnEncodedData">用于创建扩展的编码数据。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.EnhancedKeyUsages">
      <summary>获取对象标识符 (OID) 的集合，该集合指示使用密钥的应用程序。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.OidCollection" /> 对象，指示使用密钥的应用程序。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Extension">
      <summary>表示一个 X509 扩展。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.#ctor">
      <summary>初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 类的新实例。</summary>
      <param name="encodedExtension">用于创建扩展的已编码数据。</param>
      <param name="critical">如果扩展是必不可少的，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.#ctor(System.Security.Cryptography.Oid,System.Byte[],System.Boolean)">
      <summary>初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 类的新实例。</summary>
      <param name="oid">用于标识扩展的对象标识符。</param>
      <param name="rawData">用于创建扩展的编码数据。</param>
      <param name="critical">如果扩展是必不可少的，则为 true；否则为 false。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="oid" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="oid" /> 为空字符串 ("")。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.#ctor(System.String,System.Byte[],System.Boolean)">
      <summary>初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 类的新实例。</summary>
      <param name="oid">表示对象标识符的字符串。</param>
      <param name="rawData">用于创建扩展的编码数据。</param>
      <param name="critical">如果扩展是必不可少的，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>复制指定 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 对象的扩展属性。</summary>
      <param name="asnEncodedData">要复制的 <see cref="T:System.Security.Cryptography.AsnEncodedData" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asnEncodedData" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asnEncodedData" /> 没有有效的 X.509 扩展。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Extension.Critical">
      <summary>获取一个指示扩展是否必不可少的布尔值。</summary>
      <returns>如果扩展是必不可少的，则为 true；否则为 false。</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection">
      <summary>表示 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 对象的集合。此类不能被继承。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.#ctor">
      <summary>初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.Add(System.Security.Cryptography.X509Certificates.X509Extension)">
      <summary>将 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 对象添加到 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 对象中。</summary>
      <returns>添加 <paramref name="extension" /> 参数的位置处的索引。</returns>
      <param name="extension">要添加到 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 对象中的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="extension" /> 参数的值为 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.CopyTo(System.Security.Cryptography.X509Certificates.X509Extension[],System.Int32)">
      <summary>从指定的索引开始，将集合复制到数组。</summary>
      <param name="array">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 对象的数组。</param>
      <param name="index">数组中开始复制的位置。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> 为零长度字符串或包含无效值。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="index" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 指定一个不在数组范围中的值。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.Count">
      <summary>获取 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 对象中 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 对象的数目。</summary>
      <returns>一个整数，表示 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 对象中 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 对象的数目。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.GetEnumerator">
      <summary>返回可循环访问 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 对象的枚举数。</summary>
      <returns>一个 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator" /> 对象，用于循环访问 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 对象。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.IsSynchronized">
      <summary>获取一个值，该值指示是否保证该集合为线程安全的。</summary>
      <returns>如果集合是线程安全的，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.Item(System.Int32)">
      <summary>获取位于指定索引位置的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 对象。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 对象。</returns>
      <param name="index">要检索的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 对象的位置。</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="index" /> 小于零。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 等于或大于 array 的长度。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.Item(System.String)">
      <summary>获取值或友好名称由对象标识符 (OID) 指定的第一个 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 对象。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 对象。</returns>
      <param name="oid">要检索的扩展的对象标识符 (OID)。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.SyncRoot">
      <summary>获取一个对象，该对象可用于同步对 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 对象的访问。</summary>
      <returns>一个对象，它可用于同步对 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 对象的访问。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>从指定的索引开始，将集合复制到数组。</summary>
      <param name="array">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 对象的数组。</param>
      <param name="index">数组中开始复制的位置。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> 为零长度字符串或包含无效值。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="index" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 指定一个不在数组范围中的值。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>返回可循环访问 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 对象的枚举数。</summary>
      <returns>一个 <see cref="T:System.Collections.IEnumerator" /> 对象，用于循环访问 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 对象。</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator">
      <summary>支持在 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 上进行简单迭代。此类不能被继承。</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator.Current">
      <summary>获取 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 中的当前元素。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 中的当前元素。</returns>
      <exception cref="T:System.InvalidOperationException">枚举数定位在该集合的第一个元素之前或最后一个元素之后。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator.MoveNext">
      <summary>将枚举数推进到 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 中的下一个元素。</summary>
      <returns>如果枚举数成功地推进到下一个元素，则为 true；如果枚举数越过集合的结尾，则为 false。</returns>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator.Reset">
      <summary>将枚举数设置到其初始位置，该位置位于 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 中第一个元素之前。</summary>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator.System#Collections#IEnumerator#Current">
      <summary>从集合中获取一个对象。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 中的当前元素。</returns>
      <exception cref="T:System.InvalidOperationException">枚举数定位在该集合的第一个元素之前或最后一个元素之后。</exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509FindType">
      <summary>指定通过 <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法搜索的值的类型。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByApplicationPolicy">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法的 <paramref name="findValue" /> 参数必须是一个字符串，该字符串表示证书的应用程序策略友好名称或对象标识符（OID，或 <see cref="T:System.Security.Cryptography.Oid" />）。例如，可以使用“加密文件系统”或“1.3.6.1.4.1.311.10.3.4”。对于将要本地化的应用程序，由于友好名称经过本地化处理，因此必须使用 OID 值。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByCertificatePolicy">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法的 <paramref name="findValue" /> 参数必须是一个字符串，该字符串表示证书策略的友好名称或对象标识符（OID，或 <see cref="T:System.Security.Cryptography.Oid" />）。最佳做法是使用 OID，如“1.3.6.1.4.1.311.10.3.4”。对于将要本地化的应用程序，由于友好名称经过本地化处理，因此必须使用 OID。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByExtension">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法的 <paramref name="findValue" /> 参数必须是一个字符串，该字符串描述要查找的扩展名。对象标识符 (OID) 常用于指示 <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法搜索扩展名与 OID 值相匹配的所有证书。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerDistinguishedName">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法的 <paramref name="findValue" /> 参数必须是一个字符串，该字符串表示证书的颁发者可分辨名称。与 <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerName" /> 枚举值所提供的相比，这是一种更具体的搜索方式。通过使用 <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerDistinguishedName" /> 值，<see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法将对整个可分辨名称执行不区分大小写的字符串比较。按颁发者名称搜索的精确性较低。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerName">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法的 <paramref name="findValue" /> 参数必须是一个字符串，该字符串表示证书颁发者的名称。与 <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerDistinguishedName" /> 枚举值所提供的相比，这是一种不太具体的搜索方式。通过使用 <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerName" /> 值，<see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法将使用提供的值执行不区分大小写的字符串比较。例如，如果将“MyCA”传递给 <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法，该方法将查找颁发者名称中包含该字符串的所有证书，而不管其是否包含其他颁发者值。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByKeyUsage">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法的 <paramref name="findValue" /> 参数必须是一个表示密钥用法的字符串或一个表示位掩码（包含所有请求的密钥用法）的整数。对于字符串值，一次只能指定一种密钥用法，但是可以按级联序列使用 <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法以使请求用法交叉。例如，可以将 <paramref name="findValue" /> 参数设置为“KeyEncipherment”或整数（0x30 指示“KeyEncipherment”和“DataEncipherment”）。还可以使用 <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags" /> 枚举的值。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySerialNumber">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法的 <paramref name="findValue" /> 参数必须是一个字符串，它表示通过证书对话框显示的或 <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetSerialNumberString" /> 方法返回的证书的序列号，但不包含空格。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectDistinguishedName">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法的 <paramref name="findValue" /> 参数必须是一个字符串，该字符串表示证书的主题可分辨名称。与 <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectName" /> 枚举值所提供的相比，这是一种更具体的搜索方式。通过使用 <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectDistinguishedName" /> 值，<see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法将对整个可分辨名称执行不区分大小写的字符串比较。按主题名称搜索的精确性较低。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectKeyIdentifier">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法的 <paramref name="findValue" /> 参数必须是一个字符串，该字符串用十六进制值表示主题密钥标识符，如 UI 中显示的“F3E815D45E83B8477B9284113C64EF208E897112”。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectName">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法的 <paramref name="findValue" /> 参数必须是一个字符串，该字符串表示证书的主题名称。与 <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectDistinguishedName" /> 枚举值所提供的相比，这是一种不太具体的搜索方式。通过使用 <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectName" /> 值，<see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法将使用提供的值执行不区分大小写的字符串比较。例如，如果将“MyCert”传递给 <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法，该方法将查找主题名称中包含该字符串的所有证书，而不考虑其他主题值。按可分辨名称搜索的精确性较高。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTemplateName">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法的 <paramref name="findValue" /> 参数必须是一个字符串，该字符串表示证书的模板名，如“ClientAuth”。模板名为 X509 3 版扩展名，指定证书的使用。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByThumbprint">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法的 <paramref name="findValue" /> 参数必须是一个字符串，该字符串表示证书的指纹。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeExpired">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法的 <paramref name="findValue" /> 参数必须是一个以当地时间表示的 <see cref="T:System.DateTime" /> 值。例如，通过从 <see cref="P:System.DateTime.Now" /> 的 <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 操作的结果中消除今年的最后一天的 <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeExpired" /> 的 <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 操作的结果，可以查找有效期截止到今年年底的所有证书。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeNotYetValid">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法的 <paramref name="findValue" /> 参数必须是一个以当地时间表示的 <see cref="T:System.DateTime" /> 值。值不必处于将来时间。例如，通过从 <see cref="P:System.DateTime.Now" />的 <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeValid" /> 的 <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 操作的结果中采用去年最后一天的 <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeNotYetValid" /> 的 <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 操作的结果的交集，可以使用 <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeNotYetValid" /> 来查找已在当前年中有效的证书。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeValid">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法的 <paramref name="findValue" /> 参数必须是一个以当地时间表示的 <see cref="T:System.DateTime" /> 值。可使用 <see cref="P:System.DateTime.Now" /> 查找当前有效的所有证书。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags">
      <summary>定义将 X.509 证书的私钥导入到何处以及如何导出。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.DefaultKeySet">
      <summary>使用默认的密钥集。用户密钥集通常为默认值。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.Exportable">
      <summary>导入的密钥被标记为可导出。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.MachineKeySet">
      <summary>私钥存储在本地计算机存储区而不是当前用户存储区。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.PersistKeySet">
      <summary>导入证书时会保存与 PFX 文件关联的密钥。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.UserKeySet">
      <summary>私钥存储在当前用户存储区而不是本地计算机存储区。既使证书指定密钥应存储在本地计算机存储区，私钥也会存储到当前用户存储区。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.UserProtected">
      <summary>通过对话框或其他方法，通知用户密钥被访问。使用的加密服务提供程序 (CSP) 定义确切的行为。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension">
      <summary>定义 X.509 证书中所包含的密钥的用法。此类不能被继承。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.#ctor">
      <summary>初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>使用 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 对象和一个标识扩展是否重要的值初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension" /> 类的新实例。</summary>
      <param name="encodedKeyUsage">用于创建扩展的编码数据。</param>
      <param name="critical">如果扩展是必不可少的，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.#ctor(System.Security.Cryptography.X509Certificates.X509KeyUsageFlags,System.Boolean)">
      <summary>使用指定的 <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags" /> 值和一个标识扩展是否重要的值初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension" /> 类的新实例。</summary>
      <param name="keyUsages">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags" /> 值之一，说明如何使用密钥。</param>
      <param name="critical">如果扩展是必不可少的，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>使用 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 对象初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension" /> 类的新实例。</summary>
      <param name="asnEncodedData">用于创建扩展的编码数据。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.KeyUsages">
      <summary>获取与证书关联的密钥用法标志。</summary>
      <returns>
        <see cref="P:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.KeyUsages" /> 值之一。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">扩展无法解码。</exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags">
      <summary>定义证书密钥的使用方法。如果不定义此值，则密钥可用于任何目的。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.CrlSign">
      <summary>密钥可用于签署证书吊销列表 (CRL)。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.DataEncipherment">
      <summary>密钥可用于数据加密。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.DecipherOnly">
      <summary>密钥只能用于解密。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.DigitalSignature">
      <summary>密钥可用作数字签名。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.EncipherOnly">
      <summary>密钥只能用于加密。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.KeyAgreement">
      <summary>密钥可用于确定密钥协议，如使用 Diffie-Hellman 密钥协议算法创建的密钥。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.KeyCertSign">
      <summary>密钥可用于签署证书。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.KeyEncipherment">
      <summary>密钥可用于密钥加密。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.None">
      <summary>无密钥使用参数。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.NonRepudiation">
      <summary>密钥可用于身份验证。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509NameType">
      <summary>指定 X509 证书包含的名称类型。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.DnsFromAlternativeName">
      <summary>与 X.509 证书的使用者或颁发者的备用名称相关联的 DNS 名称。此值等效于 <see cref="F:System.Security.Cryptography.X509Certificates.X509NameType.DnsName" /> 值。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.DnsName">
      <summary>与 X509 证书的使用者或颁发者的备用名称相关联的 DNS 名称。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.EmailName">
      <summary>与 X509 证书关联的使用者或颁发者的电子邮件地址。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.SimpleName">
      <summary>X509 证书的使用者或颁发者的简单名称。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.UpnName">
      <summary>X509 证书的使用者或颁发者的 UPN 名称。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.UrlName">
      <summary>与 X509 证书的使用者或颁发者的备用名称相关联的 URL 地址。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509RevocationFlag">
      <summary>指定应对链中的哪些 X509 证书进行吊销检查。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationFlag.EndCertificateOnly">
      <summary>仅对最终证书进行吊销检查。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationFlag.EntireChain">
      <summary>对整个证书链进行吊销检查。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationFlag.ExcludeRoot">
      <summary>对除根证书之外的整个链进行吊销检查。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509RevocationMode">
      <summary>指定用于 X509 证书吊销检查的模式。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationMode.NoCheck">
      <summary>不对此证书执行吊销检查。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationMode.Offline">
      <summary>使用缓存的证书吊销列表 (CRL) 进行吊销检查。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationMode.Online">
      <summary>使用联机的证书吊销列表 (CRL) 进行吊销检查。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Store">
      <summary>表示 X.509 存储，该存储是保留和管理证书的物理存储。此类不能被继承。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.#ctor">
      <summary>使用当前用户存储区的个人证书初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Store" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.#ctor(System.Security.Cryptography.X509Certificates.StoreName,System.Security.Cryptography.X509Certificates.StoreLocation)">
      <summary>使用指定的 <see cref="T:System.Security.Cryptography.X509Certificates.StoreName" /> 和 <see cref="T:System.Security.Cryptography.X509Certificates.StoreLocation" /> 值初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Store" /> 类的新实例。</summary>
      <param name="storeName">指定 X.509 证书存储区名称的枚举值之一。</param>
      <param name="storeLocation">指定 X.509 证书存储位置的枚举值之一。 </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="storeLocation" /> 不是有效位置或者 <paramref name="storeName" /> 不是有效名称。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.#ctor(System.String,System.Security.Cryptography.X509Certificates.StoreLocation)">
      <summary>使用一个字符串初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Store" /> 类的新实例，该字符串表示 <see cref="T:System.Security.Cryptography.X509Certificates.StoreName" /> 枚举中的一个值和 <see cref="T:System.Security.Cryptography.X509Certificates.StoreLocation" /> 枚举中的一个值。</summary>
      <param name="storeName">一个字符串，表示 <see cref="T:System.Security.Cryptography.X509Certificates.StoreName" /> 枚举中的值。</param>
      <param name="storeLocation">指定 X.509 证书存储位置的枚举值之一。 </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="storeLocation" /> 包含无效值。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.Add(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>将证书添加到 X.509 证书存储。</summary>
      <param name="certificate">要添加的证书。 </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> 为 null。</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">未能将证书添加到存储区。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Store.Certificates">
      <summary>返回位于 X.509 证书存储中的证书集合。</summary>
      <returns>证书集合。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.Dispose">
      <summary>释放由此占用的资源<see cref="T:System.Security.Cryptography.X509Certificates.X509Store" />。</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Store.Location">
      <summary>获取 X.509 证书存储的位置。</summary>
      <returns>证书存储区的位置。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Store.Name">
      <summary>获取 X.509 证书存储的名称。</summary>
      <returns>证书存储的名称。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.Open(System.Security.Cryptography.X509Certificates.OpenFlags)">
      <summary>打开 X.509 证书存储区或创建新存储区，具体取决于 <see cref="T:System.Security.Cryptography.X509Certificates.OpenFlags" /> 标志设置。</summary>
      <param name="flags">枚举值的按位组合，指定打开 X.509 证书存储区的方式。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">该证书不可读。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.ArgumentException">存储区包含无效值。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.Remove(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>从 X.509 证书存储移除证书。</summary>
      <param name="certificate">要移除的证书。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> 为 null。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension">
      <summary>定义一个标识证书的主题密钥标识符 (SKI) 的字符串。此类不能被继承。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor">
      <summary>初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.Byte[],System.Boolean)">
      <summary>使用一个字节数组和一个标识扩展是否重要的值初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> 类的新实例。</summary>
      <param name="subjectKeyIdentifier">一个字节数组，表示要用来创建扩展的数据。</param>
      <param name="critical">如果扩展是必不可少的，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>使用编码数据和一个标识扩展是否重要的值初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> 类的新实例。</summary>
      <param name="encodedSubjectKeyIdentifier">要用来创建扩展的 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 对象。</param>
      <param name="critical">如果扩展是必不可少的，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.Security.Cryptography.X509Certificates.PublicKey,System.Boolean)">
      <summary>使用一个公钥和一个标识扩展是否重要的值初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> 类的新实例。</summary>
      <param name="key">要从其创建主题密钥标识符 (SKI) 的 <see cref="T:System.Security.Cryptography.X509Certificates.PublicKey" /> 对象。</param>
      <param name="critical">如果扩展是必不可少的，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.Security.Cryptography.X509Certificates.PublicKey,System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm,System.Boolean)">
      <summary>使用一个公钥、一个哈希算法标识符和一个指示扩展是否重要的值初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> 类的新实例。</summary>
      <param name="key">要从其创建主题密钥标识符 (SKI) 的 <see cref="T:System.Security.Cryptography.X509Certificates.PublicKey" /> 对象。</param>
      <param name="algorithm">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm" /> 值之一，标识要使用哪一个哈希算法。</param>
      <param name="critical">如果扩展是必不可少的，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.String,System.Boolean)">
      <summary>使用一个字符串和一个标识扩展是否重要的值初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> 类的新实例。</summary>
      <param name="subjectKeyIdentifier">一个以十六进制格式编码的字符串，表示证书的主题密钥标识符 (SKI)。</param>
      <param name="critical">如果扩展是必不可少的，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>通过从编码数据复制信息创建 <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> 类的新实例。</summary>
      <param name="asnEncodedData">要用来创建扩展的 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 对象。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.SubjectKeyIdentifier">
      <summary>获取一个表示证书的主题密钥标识符 (SKI) 的字符串。</summary>
      <returns>一个以十六进制格式编码的字符串，表示主题密钥标识符 (SKI)。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">扩展无法解码。</exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm">
      <summary>定义用于 <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> 类的哈希算法类型。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm.CapiSha1">
      <summary>主题密钥标识符 (SKI) 由编码公钥（包括标记、长度和未使用位数）的 160 位 SHA-1 哈希组成。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm.Sha1">
      <summary>SKI 由公钥值（不包括标记、长度和未使用位数）的 160 位 SHA-1 哈希组成。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm.ShortSha1">
      <summary>SKI 由具有值 0100 的四位类型字段组成，后跟公钥值（不包括标记、长度和未使用字符串位数）的 SHA-1 哈希的 60 位最低有效位</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509VerificationFlags">
      <summary>指定在哪些情况下应在 X509 链中进行证书验证。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.AllFlags">
      <summary>包含所有与验证相关的标志。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.AllowUnknownCertificateAuthority">
      <summary>忽略由于证书颁发机构 (CA) 未知而无法对链进行验证的情况。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreCertificateAuthorityRevocationUnknown">
      <summary>确定证书验证时，忽略证书颁发机构吊销未知的情况。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreCtlNotTimeValid">
      <summary>确定证书验证时，忽略由于 CTL 已过期等原因导致证书信任列表 (CTL) 无效的情况。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreCtlSignerRevocationUnknown">
      <summary>确定证书验证时，忽略证书信任列表 (CTL) 签名者吊销未知的情况。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreEndRevocationUnknown">
      <summary>确定证书验证时，忽略最终证书（用户证书）吊销未知的情况。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreInvalidBasicConstraints">
      <summary>确定证书验证时，忽略基本约束无效的情况。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreInvalidName">
      <summary>确定证书验证时，忽略证书具有无效名称的情况。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreInvalidPolicy">
      <summary>确定证书验证时，忽略证书具有无效策略的情况。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreNotTimeNested">
      <summary>证书验证时，忽略 CA（证书颁发机构）证书和所颁发证书的有效期不重叠的情况。例如，CA 证书的有效期限是从 1 月 1 日到 12 月 1 日，而已颁发的证书的有效期限是从 1 月 2 日到 12 月 2 日，这就表示有效期限是不配套的。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreNotTimeValid">
      <summary>确定证书验证时，忽略链中因已过期或尚未生效而无效的证书。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreRootRevocationUnknown">
      <summary>确定证书验证时，忽略根吊销未知的情况。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreWrongUsage">
      <summary>确定证书验证时，忽略证书不是为当前使用而颁发的情况。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.NoFlag">
      <summary>不包含任何与验证相关的标志。</summary>
    </member>
  </members>
</doc>