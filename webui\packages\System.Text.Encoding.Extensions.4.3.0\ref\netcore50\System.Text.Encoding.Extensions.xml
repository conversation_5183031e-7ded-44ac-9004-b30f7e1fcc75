﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Text.Encoding.Extensions</name>
  </assembly>
  <members>
    <member name="T:System.Text.ASCIIEncoding">
      <summary>Represents an ASCII character encoding of Unicode characters.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.ASCIIEncoding.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Text.ASCIIEncoding" /> class.</summary>
    </member>
    <member name="M:System.Text.ASCIIEncoding.GetByteCount(System.Char*,System.Int32)">
      <summary>Calculates the number of bytes produced by encoding a set of characters starting at the specified character pointer.</summary>
      <returns>The number of bytes produced by encoding the specified characters.</returns>
      <param name="chars">A pointer to the first character to encode.</param>
      <param name="count">The number of characters to encode.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" /> is less than zero.-or- The resulting number of bytes is greater than the maximum number that can be returned as an integer. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.EncoderFallback" /> is set to <see cref="T:System.Text.EncoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.ASCIIEncoding.GetByteCount(System.Char[],System.Int32,System.Int32)">
      <summary>Calculates the number of bytes produced by encoding a set of characters from the specified character array.</summary>
      <returns>The number of bytes produced by encoding the specified characters.</returns>
      <param name="chars">The character array containing the set of characters to encode.</param>
      <param name="index">The index of the first character to encode.</param>
      <param name="count">The number of characters to encode.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero.-or- <paramref name="index" /> and <paramref name="count" /> do not denote a valid range in <paramref name="chars" />.-or- The resulting number of bytes is greater than the maximum number that can be returned as an integer. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.EncoderFallback" /> is set to <see cref="T:System.Text.EncoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.ASCIIEncoding.GetByteCount(System.String)">
      <summary>Calculates the number of bytes produced by encoding the characters in the specified <see cref="T:System.String" />.</summary>
      <returns>The number of bytes produced by encoding the specified characters.</returns>
      <param name="chars">The <see cref="T:System.String" /> containing the set of characters to encode.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The resulting number of bytes is greater than the maximum number that can be returned as an integer. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.EncoderFallback" /> is set to <see cref="T:System.Text.EncoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.ASCIIEncoding.GetBytes(System.Char*,System.Int32,System.Byte*,System.Int32)">
      <summary>Encodes a set of characters starting at the specified character pointer into a sequence of bytes that are stored starting at the specified byte pointer.</summary>
      <returns>The actual number of bytes written at the location indicated by <paramref name="bytes" />.</returns>
      <param name="chars">A pointer to the first character to encode.</param>
      <param name="charCount">The number of characters to encode.</param>
      <param name="bytes">A pointer to the location at which to start writing the resulting sequence of bytes.</param>
      <param name="byteCount">The maximum number of bytes to write.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" /> is null.-or- <paramref name="bytes" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="charCount" /> or <paramref name="byteCount" /> is less than zero. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="byteCount" /> is less than the resulting number of bytes. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.EncoderFallback" /> is set to <see cref="T:System.Text.EncoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.ASCIIEncoding.GetBytes(System.Char[],System.Int32,System.Int32,System.Byte[],System.Int32)">
      <summary>Encodes a set of characters from the specified character array into the specified byte array.</summary>
      <returns>The actual number of bytes written into <paramref name="bytes" />.</returns>
      <param name="chars">The character array containing the set of characters to encode.</param>
      <param name="charIndex">The index of the first character to encode.</param>
      <param name="charCount">The number of characters to encode.</param>
      <param name="bytes">The byte array to contain the resulting sequence of bytes.</param>
      <param name="byteIndex">The index at which to start writing the resulting sequence of bytes.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" /> is null.-or- <paramref name="bytes" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="charIndex" /> or <paramref name="charCount" /> or <paramref name="byteIndex" /> is less than zero.-or- <paramref name="charIndex" /> and <paramref name="charCount" /> do not denote a valid range in <paramref name="chars" />.-or- <paramref name="byteIndex" /> is not a valid index in <paramref name="bytes" />. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="bytes" /> does not have enough capacity from <paramref name="byteIndex" /> to the end of the array to accommodate the resulting bytes. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.EncoderFallback" /> is set to <see cref="T:System.Text.EncoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.ASCIIEncoding.GetBytes(System.String,System.Int32,System.Int32,System.Byte[],System.Int32)">
      <summary>Encodes a set of characters from the specified <see cref="T:System.String" /> into the specified byte array.</summary>
      <returns>The actual number of bytes written into <paramref name="bytes" />.</returns>
      <param name="chars">The <see cref="T:System.String" /> containing the set of characters to encode.</param>
      <param name="charIndex">The index of the first character to encode.</param>
      <param name="charCount">The number of characters to encode.</param>
      <param name="bytes">The byte array to contain the resulting sequence of bytes.</param>
      <param name="byteIndex">The index at which to start writing the resulting sequence of bytes.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null.-or- <paramref name="bytes" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="charIndex" /> or <paramref name="charCount" /> or <paramref name="byteIndex" /> is less than zero.-or- <paramref name="charIndex" /> and <paramref name="charCount" /> do not denote a valid range in <paramref name="chars" />.-or- <paramref name="byteIndex" /> is not a valid index in <paramref name="bytes" />. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="bytes" /> does not have enough capacity from <paramref name="byteIndex" /> to the end of the array to accommodate the resulting bytes. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.EncoderFallback" /> is set to <see cref="T:System.Text.EncoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.ASCIIEncoding.GetCharCount(System.Byte*,System.Int32)">
      <summary>Calculates the number of characters produced by decoding a sequence of bytes starting at the specified byte pointer.</summary>
      <returns>The number of characters produced by decoding the specified sequence of bytes.</returns>
      <param name="bytes">A pointer to the first byte to decode.</param>
      <param name="count">The number of bytes to decode.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" /> is less than zero.-or- The resulting number of bytes is greater than the maximum number that can be returned as an integer. </exception>
      <exception cref="T:System.Text.DecoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.DecoderFallback" /> is set to <see cref="T:System.Text.DecoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.ASCIIEncoding.GetCharCount(System.Byte[],System.Int32,System.Int32)">
      <summary>Calculates the number of characters produced by decoding a sequence of bytes from the specified byte array.</summary>
      <returns>The number of characters produced by decoding the specified sequence of bytes.</returns>
      <param name="bytes">The byte array containing the sequence of bytes to decode.</param>
      <param name="index">The index of the first byte to decode.</param>
      <param name="count">The number of bytes to decode.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero.-or- <paramref name="index" /> and <paramref name="count" /> do not denote a valid range in <paramref name="bytes" />.-or- The resulting number of bytes is greater than the maximum number that can be returned as an integer. </exception>
      <exception cref="T:System.Text.DecoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.DecoderFallback" /> is set to <see cref="T:System.Text.DecoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.ASCIIEncoding.GetChars(System.Byte*,System.Int32,System.Char*,System.Int32)">
      <summary>Decodes a sequence of bytes starting at the specified byte pointer into a set of characters that are stored starting at the specified character pointer.</summary>
      <returns>The actual number of characters written at the location indicated by <paramref name="chars" />.</returns>
      <param name="bytes">A pointer to the first byte to decode.</param>
      <param name="byteCount">The number of bytes to decode.</param>
      <param name="chars">A pointer to the location at which to start writing the resulting set of characters.</param>
      <param name="charCount">The maximum number of characters to write.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null.-or- <paramref name="chars" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="byteCount" /> or <paramref name="charCount" /> is less than zero. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="charCount" /> is less than the resulting number of characters. </exception>
      <exception cref="T:System.Text.DecoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.DecoderFallback" /> is set to <see cref="T:System.Text.DecoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.ASCIIEncoding.GetChars(System.Byte[],System.Int32,System.Int32,System.Char[],System.Int32)">
      <summary>Decodes a sequence of bytes from the specified byte array into the specified character array.</summary>
      <returns>The actual number of characters written into <paramref name="chars" />.</returns>
      <param name="bytes">The byte array containing the sequence of bytes to decode.</param>
      <param name="byteIndex">The index of the first byte to decode.</param>
      <param name="byteCount">The number of bytes to decode.</param>
      <param name="chars">The character array to contain the resulting set of characters.</param>
      <param name="charIndex">The index at which to start writing the resulting set of characters.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null.-or- <paramref name="chars" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="byteIndex" /> or <paramref name="byteCount" /> or <paramref name="charIndex" /> is less than zero.-or- <paramref name="byteindex" /> and <paramref name="byteCount" /> do not denote a valid range in <paramref name="bytes" />.-or- <paramref name="charIndex" /> is not a valid index in <paramref name="chars" />. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="chars" /> does not have enough capacity from <paramref name="charIndex" /> to the end of the array to accommodate the resulting characters. </exception>
      <exception cref="T:System.Text.DecoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.DecoderFallback" /> is set to <see cref="T:System.Text.DecoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.ASCIIEncoding.GetDecoder">
      <summary>Obtains a decoder that converts an ASCII encoded sequence of bytes into a sequence of Unicode characters.</summary>
      <returns>A <see cref="T:System.Text.Decoder" /> that converts an ASCII encoded sequence of bytes into a sequence of Unicode characters.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.ASCIIEncoding.GetEncoder">
      <summary>Obtains an encoder that converts a sequence of Unicode characters into an ASCII encoded sequence of bytes.</summary>
      <returns>An <see cref="T:System.Text.Encoder" /> that converts a sequence of Unicode characters into an ASCII encoded sequence of bytes.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.ASCIIEncoding.GetMaxByteCount(System.Int32)">
      <summary>Calculates the maximum number of bytes produced by encoding the specified number of characters.</summary>
      <returns>The maximum number of bytes produced by encoding the specified number of characters.</returns>
      <param name="charCount">The number of characters to encode.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="charCount" /> is less than zero.-or- The resulting number of bytes is greater than the maximum number that can be returned as an integer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.ASCIIEncoding.GetMaxCharCount(System.Int32)">
      <summary>Calculates the maximum number of characters produced by decoding the specified number of bytes.</summary>
      <returns>The maximum number of characters produced by decoding the specified number of bytes.</returns>
      <param name="byteCount">The number of bytes to decode.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="byteCount" /> is less than zero.-or- The resulting number of bytes is greater than the maximum number that can be returned as an integer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.ASCIIEncoding.GetString(System.Byte[],System.Int32,System.Int32)">
      <summary>Decodes a range of bytes from a byte array into a string.</summary>
      <returns>A <see cref="T:System.String" /> containing the results of decoding the specified sequence of bytes.</returns>
      <param name="bytes">The byte array containing the sequence of bytes to decode.</param>
      <param name="byteIndex">The index of the first byte to decode.</param>
      <param name="byteCount">The number of bytes to decode.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero.-or- <paramref name="index" /> and <paramref name="count" /> do not denote a valid range in <paramref name="bytes" />. </exception>
      <exception cref="T:System.Text.DecoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.DecoderFallback" /> is set to <see cref="T:System.Text.DecoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Text.ASCIIEncoding.IsSingleByte">
      <summary>Gets a value indicating whether the current encoding uses single-byte code points.</summary>
      <returns>This property is always true.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Text.UnicodeEncoding">
      <summary>Represents a UTF-16 encoding of Unicode characters. </summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UnicodeEncoding.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Text.UnicodeEncoding" /> class.</summary>
    </member>
    <member name="M:System.Text.UnicodeEncoding.#ctor(System.Boolean,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Text.UnicodeEncoding" /> class. Parameters specify whether to use the big endian byte order and whether the <see cref="M:System.Text.UnicodeEncoding.GetPreamble" /> method returns a Unicode byte order mark. </summary>
      <param name="bigEndian">true to use the big endian byte order (most significant byte first), or false to use the little endian byte order (least significant byte first). </param>
      <param name="byteOrderMark">true to specify that the <see cref="M:System.Text.UnicodeEncoding.GetPreamble" /> method returns a Unicode byte order mark; otherwise, false. See the Remarks section for more information. </param>
    </member>
    <member name="M:System.Text.UnicodeEncoding.#ctor(System.Boolean,System.Boolean,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Text.UnicodeEncoding" /> class. Parameters specify whether to use the big endian byte order, whether to provide a Unicode byte order mark, and whether to throw an exception when an invalid encoding is detected.</summary>
      <param name="bigEndian">true to use the big endian byte order (most significant byte first); false to use the little endian byte order (least significant byte first). </param>
      <param name="byteOrderMark">true to specify that the <see cref="M:System.Text.UnicodeEncoding.GetPreamble" /> method returns a Unicode byte order mark; otherwise, false. See the Remarks section for more information. </param>
      <param name="throwOnInvalidBytes">true to specify that an exception should be thrown when an invalid encoding is detected; otherwise, false. </param>
    </member>
    <member name="M:System.Text.UnicodeEncoding.Equals(System.Object)">
      <summary>Determines whether the specified <see cref="T:System.Object" /> is equal to the current <see cref="T:System.Text.UnicodeEncoding" /> object.</summary>
      <returns>true if <paramref name="value" /> is an instance of <see cref="T:System.Text.UnicodeEncoding" /> and is equal to the current object; otherwise, false.</returns>
      <param name="value">The object to compare with the current object. </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.UnicodeEncoding.GetByteCount(System.Char[],System.Int32,System.Int32)">
      <summary>Calculates the number of bytes produced by encoding a set of characters from the specified character array.</summary>
      <returns>The number of bytes produced by encoding the specified characters.</returns>
      <param name="chars">The character array containing the set of characters to encode. </param>
      <param name="index">The index of the first character to encode. </param>
      <param name="count">The number of characters to encode. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" /> is null (Nothing). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero.-or- <paramref name="index" /> and <paramref name="count" /> do not denote a valid range in <paramref name="chars" />.-or- The resulting number of bytes is greater than the maximum number that can be returned as an integer. </exception>
      <exception cref="T:System.ArgumentException">Error detection is enabled, and <paramref name="chars" /> contains an invalid sequence of characters. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for fuller explanation)-and-<see cref="P:System.Text.Encoding.EncoderFallback" /> is set to <see cref="T:System.Text.EncoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UnicodeEncoding.GetByteCount(System.String)">
      <summary>Calculates the number of bytes produced by encoding the characters in the specified string.</summary>
      <returns>The number of bytes produced by encoding the specified characters. </returns>
      <param name="s">The string that contains the set of characters to encode. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null . </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The resulting number of bytes is greater than the maximum number that can be returned as an integer. </exception>
      <exception cref="T:System.ArgumentException">Error detection is enabled, and <paramref name="s" /> contains an invalid sequence of characters. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for fuller explanation)-and-<see cref="P:System.Text.Encoding.EncoderFallback" /> is set to <see cref="T:System.Text.EncoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UnicodeEncoding.GetBytes(System.Char[],System.Int32,System.Int32,System.Byte[],System.Int32)">
      <summary>Encodes a set of characters from the specified character array into the specified byte array.</summary>
      <returns>The actual number of bytes written into <paramref name="bytes" />.</returns>
      <param name="chars">The character array containing the set of characters to encode. </param>
      <param name="charIndex">The index of the first character to encode. </param>
      <param name="charCount">The number of characters to encode. </param>
      <param name="bytes">The byte array to contain the resulting sequence of bytes. </param>
      <param name="byteIndex">The index at which to start writing the resulting sequence of bytes. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" /> is null (Nothing).-or- <paramref name="bytes" /> is null (Nothing). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="charIndex" /> or <paramref name="charCount" /> or <paramref name="byteIndex" /> is less than zero.-or- <paramref name="charIndex" /> and <paramref name="charCount" /> do not denote a valid range in <paramref name="chars" />.-or- <paramref name="byteIndex" /> is not a valid index in <paramref name="bytes" />. </exception>
      <exception cref="T:System.ArgumentException">Error detection is enabled, and <paramref name="chars" /> contains an invalid sequence of characters.-or- <paramref name="bytes" /> does not have enough capacity from <paramref name="byteIndex" /> to the end of the array to accommodate the resulting bytes. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for fuller explanation)-and-<see cref="P:System.Text.Encoding.EncoderFallback" /> is set to <see cref="T:System.Text.EncoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UnicodeEncoding.GetBytes(System.String,System.Int32,System.Int32,System.Byte[],System.Int32)">
      <summary>Encodes a set of characters from the specified <see cref="T:System.String" /> into the specified byte array.</summary>
      <returns>The actual number of bytes written into <paramref name="bytes" />.</returns>
      <param name="s">The string containing the set of characters to encode. </param>
      <param name="charIndex">The index of the first character to encode. </param>
      <param name="charCount">The number of characters to encode. </param>
      <param name="bytes">The byte array to contain the resulting sequence of bytes. </param>
      <param name="byteIndex">The index at which to start writing the resulting sequence of bytes. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null .-or- <paramref name="bytes" /> is null (Nothing). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="charIndex" /> or <paramref name="charCount" /> or <paramref name="byteIndex" /> is less than zero.-or- <paramref name="charIndex" /> and <paramref name="charCount" /> do not denote a valid range in <paramref name="chars" />.-or- <paramref name="byteIndex" /> is not a valid index in <paramref name="bytes" />. </exception>
      <exception cref="T:System.ArgumentException">Error detection is enabled, and <paramref name="s" /> contains an invalid sequence of characters.-or- <paramref name="bytes" /> does not have enough capacity from <paramref name="byteIndex" /> to the end of the array to accommodate the resulting bytes. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for fuller explanation)-and-<see cref="P:System.Text.Encoding.EncoderFallback" /> is set to <see cref="T:System.Text.EncoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UnicodeEncoding.GetCharCount(System.Byte[],System.Int32,System.Int32)">
      <summary>Calculates the number of characters produced by decoding a sequence of bytes from the specified byte array.</summary>
      <returns>The number of characters produced by decoding the specified sequence of bytes.</returns>
      <param name="bytes">The byte array containing the sequence of bytes to decode. </param>
      <param name="index">The index of the first byte to decode. </param>
      <param name="count">The number of bytes to decode. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null (Nothing). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero.-or- <paramref name="index" /> and <paramref name="count" /> do not denote a valid range in <paramref name="bytes" />.-or- The resulting number of bytes is greater than the maximum number that can be returned as an integer. </exception>
      <exception cref="T:System.ArgumentException">Error detection is enabled, and <paramref name="bytes" /> contains an invalid sequence of bytes. </exception>
      <exception cref="T:System.Text.DecoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for fuller explanation)-and-<see cref="P:System.Text.Encoding.DecoderFallback" /> is set to <see cref="T:System.Text.DecoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UnicodeEncoding.GetChars(System.Byte[],System.Int32,System.Int32,System.Char[],System.Int32)">
      <summary>Decodes a sequence of bytes from the specified byte array into the specified character array.</summary>
      <returns>The actual number of characters written into <paramref name="chars" />.</returns>
      <param name="bytes">The byte array containing the sequence of bytes to decode. </param>
      <param name="byteIndex">The index of the first byte to decode. </param>
      <param name="byteCount">The number of bytes to decode. </param>
      <param name="chars">The character array to contain the resulting set of characters. </param>
      <param name="charIndex">The index at which to start writing the resulting set of characters. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null (Nothing).-or- <paramref name="chars" /> is null (Nothing). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="byteIndex" /> or <paramref name="byteCount" /> or <paramref name="charIndex" /> is less than zero.-or- <paramref name="byteindex" /> and <paramref name="byteCount" /> do not denote a valid range in <paramref name="bytes" />.-or- <paramref name="charIndex" /> is not a valid index in <paramref name="chars" />. </exception>
      <exception cref="T:System.ArgumentException">Error detection is enabled, and <paramref name="bytes" /> contains an invalid sequence of bytes.-or- <paramref name="chars" /> does not have enough capacity from <paramref name="charIndex" /> to the end of the array to accommodate the resulting characters. </exception>
      <exception cref="T:System.Text.DecoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for fuller explanation)-and-<see cref="P:System.Text.Encoding.DecoderFallback" /> is set to <see cref="T:System.Text.DecoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UnicodeEncoding.GetDecoder">
      <summary>Obtains a decoder that converts a UTF-16 encoded sequence of bytes into a sequence of Unicode characters.</summary>
      <returns>A <see cref="T:System.Text.Decoder" /> that converts a UTF-16 encoded sequence of bytes into a sequence of Unicode characters.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UnicodeEncoding.GetEncoder">
      <summary>Obtains an encoder that converts a sequence of Unicode characters into a UTF-16 encoded sequence of bytes.</summary>
      <returns>A <see cref="T:System.Text.Encoder" /> object that converts a sequence of Unicode characters into a UTF-16 encoded sequence of bytes.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UnicodeEncoding.GetHashCode">
      <summary>Returns the hash code for the current instance.</summary>
      <returns>The hash code for the current <see cref="T:System.Text.UnicodeEncoding" /> object.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UnicodeEncoding.GetMaxByteCount(System.Int32)">
      <summary>Calculates the maximum number of bytes produced by encoding the specified number of characters.</summary>
      <returns>The maximum number of bytes produced by encoding the specified number of characters.</returns>
      <param name="charCount">The number of characters to encode. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="charCount" /> is less than zero.-or- The resulting number of bytes is greater than the maximum number that can be returned as an integer. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for fuller explanation)-and-<see cref="P:System.Text.Encoding.EncoderFallback" /> is set to <see cref="T:System.Text.EncoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UnicodeEncoding.GetMaxCharCount(System.Int32)">
      <summary>Calculates the maximum number of characters produced by decoding the specified number of bytes.</summary>
      <returns>The maximum number of characters produced by decoding the specified number of bytes.</returns>
      <param name="byteCount">The number of bytes to decode. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="byteCount" /> is less than zero.-or- The resulting number of bytes is greater than the maximum number that can be returned as an integer. </exception>
      <exception cref="T:System.Text.DecoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for fuller explanation)-and-<see cref="P:System.Text.Encoding.DecoderFallback" /> is set to <see cref="T:System.Text.DecoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UnicodeEncoding.GetPreamble">
      <summary>Returns a Unicode byte order mark encoded in UTF-16 format, if the constructor for this instance requests a byte order mark.</summary>
      <returns>A byte array containing the Unicode byte order mark, if the <see cref="T:System.Text.UnicodeEncoding" /> object is configured to supply one. Otherwise, this method returns a zero-length byte array. </returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UnicodeEncoding.GetString(System.Byte[],System.Int32,System.Int32)">
      <summary>Decodes a range of bytes from a byte array into a string.</summary>
      <returns>A <see cref="T:System.String" /> object containing the results of decoding the specified sequence of bytes.</returns>
      <param name="bytes">The byte array containing the sequence of bytes to decode. </param>
      <param name="index">The index of the first byte to decode. </param>
      <param name="count">The number of bytes to decode. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null (Nothing). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero.-or- <paramref name="index" /> and <paramref name="count" /> do not denote a valid range in <paramref name="bytes" />. </exception>
      <exception cref="T:System.ArgumentException">Error detection is enabled, and <paramref name="bytes" /> contains an invalid sequence of bytes. </exception>
      <exception cref="T:System.Text.DecoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for fuller explanation)-and-<see cref="P:System.Text.Encoding.DecoderFallback" /> is set to <see cref="T:System.Text.DecoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Text.UTF32Encoding">
      <summary>Represents a UTF-32 encoding of Unicode characters.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF32Encoding.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Text.UTF32Encoding" /> class.</summary>
    </member>
    <member name="M:System.Text.UTF32Encoding.#ctor(System.Boolean,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Text.UTF32Encoding" /> class. Parameters specify whether to use the big endian byte order and whether the <see cref="M:System.Text.UTF32Encoding.GetPreamble" /> method returns a Unicode Unicode byte order mark.</summary>
      <param name="bigEndian">true to use the big endian byte order (most significant byte first), or false to use the little endian byte order (least significant byte first). </param>
      <param name="byteOrderMark">true to specify that a Unicode byte order mark is provided; otherwise, false. </param>
    </member>
    <member name="M:System.Text.UTF32Encoding.#ctor(System.Boolean,System.Boolean,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Text.UTF32Encoding" /> class. Parameters specify whether to use the big endian byte order, whether to provide a Unicode byte order mark, and whether to throw an exception when an invalid encoding is detected.</summary>
      <param name="bigEndian">true to use the big endian byte order (most significant byte first), or false to use the little endian byte order (least significant byte first). </param>
      <param name="byteOrderMark">true to specify that a Unicode byte order mark is provided; otherwise, false. </param>
      <param name="throwOnInvalidCharacters">true to specify that an exception should be thrown when an invalid encoding is detected; otherwise, false. </param>
    </member>
    <member name="M:System.Text.UTF32Encoding.Equals(System.Object)">
      <summary>Determines whether the specified <see cref="T:System.Object" /> is equal to the current <see cref="T:System.Text.UTF32Encoding" /> object.</summary>
      <returns>true if <paramref name="value" /> is an instance of <see cref="T:System.Text.UTF32Encoding" /> and is equal to the current object; otherwise, false.</returns>
      <param name="value">The <see cref="T:System.Object" /> to compare with the current object. </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.UTF32Encoding.GetByteCount(System.Char*,System.Int32)">
      <summary>Calculates the number of bytes produced by encoding a set of characters starting at the specified character pointer.</summary>
      <returns>The number of bytes produced by encoding the specified characters.</returns>
      <param name="chars">A pointer to the first character to encode. </param>
      <param name="count">The number of characters to encode. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" /> is less than zero.-or- The resulting number of bytes is greater than the maximum number that can be returned as an integer. </exception>
      <exception cref="T:System.ArgumentException">Error detection is enabled, and <paramref name="chars" /> contains an invalid sequence of characters. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.EncoderFallback" /> is set to <see cref="T:System.Text.EncoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF32Encoding.GetByteCount(System.Char[],System.Int32,System.Int32)">
      <summary>Calculates the number of bytes produced by encoding a set of characters from the specified character array.</summary>
      <returns>The number of bytes produced by encoding the specified characters.</returns>
      <param name="chars">The character array containing the set of characters to encode. </param>
      <param name="index">The index of the first character to encode. </param>
      <param name="count">The number of characters to encode. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero.-or- <paramref name="index" /> and <paramref name="count" /> do not denote a valid range in <paramref name="chars" />.-or- The resulting number of bytes is greater than the maximum number that can be returned as an integer. </exception>
      <exception cref="T:System.ArgumentException">Error detection is enabled, and <paramref name="chars" /> contains an invalid sequence of characters. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.EncoderFallback" /> is set to <see cref="T:System.Text.EncoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF32Encoding.GetByteCount(System.String)">
      <summary>Calculates the number of bytes produced by encoding the characters in the specified <see cref="T:System.String" />.</summary>
      <returns>The number of bytes produced by encoding the specified characters.</returns>
      <param name="s">The <see cref="T:System.String" /> containing the set of characters to encode. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The resulting number of bytes is greater than the maximum number that can be returned as an integer. </exception>
      <exception cref="T:System.ArgumentException">Error detection is enabled, and <paramref name="s" /> contains an invalid sequence of characters. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.EncoderFallback" /> is set to <see cref="T:System.Text.EncoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF32Encoding.GetBytes(System.Char*,System.Int32,System.Byte*,System.Int32)">
      <summary>Encodes a set of characters starting at the specified character pointer into a sequence of bytes that are stored starting at the specified byte pointer.</summary>
      <returns>The actual number of bytes written at the location indicated by the <paramref name="bytes" /> parameter.</returns>
      <param name="chars">A pointer to the first character to encode. </param>
      <param name="charCount">The number of characters to encode. </param>
      <param name="bytes">A pointer to the location at which to start writing the resulting sequence of bytes. </param>
      <param name="byteCount">The maximum number of bytes to write. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" /> is null.-or- <paramref name="bytes" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="charCount" /> or <paramref name="byteCount" /> is less than zero. </exception>
      <exception cref="T:System.ArgumentException">Error detection is enabled, and <paramref name="chars" /> contains an invalid sequence of characters.-or- <paramref name="byteCount" /> is less than the resulting number of bytes. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.EncoderFallback" /> is set to <see cref="T:System.Text.EncoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF32Encoding.GetBytes(System.Char[],System.Int32,System.Int32,System.Byte[],System.Int32)">
      <summary>Encodes a set of characters from the specified character array into the specified byte array.</summary>
      <returns>The actual number of bytes written into <paramref name="bytes" />.</returns>
      <param name="chars">The character array containing the set of characters to encode. </param>
      <param name="charIndex">The index of the first character to encode. </param>
      <param name="charCount">The number of characters to encode. </param>
      <param name="bytes">The byte array to contain the resulting sequence of bytes. </param>
      <param name="byteIndex">The index at which to start writing the resulting sequence of bytes. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" /> is null.-or- <paramref name="bytes" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="charIndex" /> or <paramref name="charCount" /> or <paramref name="byteIndex" /> is less than zero.-or- <paramref name="charIndex" /> and <paramref name="charCount" /> do not denote a valid range in <paramref name="chars" />.-or- <paramref name="byteIndex" /> is not a valid index in <paramref name="bytes" />. </exception>
      <exception cref="T:System.ArgumentException">Error detection is enabled, and <paramref name="chars" /> contains an invalid sequence of characters.-or- <paramref name="bytes" /> does not have enough capacity from <paramref name="byteIndex" /> to the end of the array to accommodate the resulting bytes. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.EncoderFallback" /> is set to <see cref="T:System.Text.EncoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF32Encoding.GetBytes(System.String,System.Int32,System.Int32,System.Byte[],System.Int32)">
      <summary>Encodes a set of characters from the specified <see cref="T:System.String" /> into the specified byte array.</summary>
      <returns>The actual number of bytes written into <paramref name="bytes" />.</returns>
      <param name="s">The <see cref="T:System.String" /> containing the set of characters to encode. </param>
      <param name="charIndex">The index of the first character to encode. </param>
      <param name="charCount">The number of characters to encode. </param>
      <param name="bytes">The byte array to contain the resulting sequence of bytes. </param>
      <param name="byteIndex">The index at which to start writing the resulting sequence of bytes. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null.-or- <paramref name="bytes" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="charIndex" /> or <paramref name="charCount" /> or <paramref name="byteIndex" /> is less than zero.-or- <paramref name="charIndex" /> and <paramref name="charCount" /> do not denote a valid range in <paramref name="chars" />.-or- <paramref name="byteIndex" /> is not a valid index in <paramref name="bytes" />. </exception>
      <exception cref="T:System.ArgumentException">Error detection is enabled, and <paramref name="s" /> contains an invalid sequence of characters.-or- <paramref name="bytes" /> does not have enough capacity from <paramref name="byteIndex" /> to the end of the array to accommodate the resulting bytes. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.EncoderFallback" /> is set to <see cref="T:System.Text.EncoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF32Encoding.GetCharCount(System.Byte*,System.Int32)">
      <summary>Calculates the number of characters produced by decoding a sequence of bytes starting at the specified byte pointer.</summary>
      <returns>The number of characters produced by decoding the specified sequence of bytes.</returns>
      <param name="bytes">A pointer to the first byte to decode. </param>
      <param name="count">The number of bytes to decode. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" /> is less than zero.-or- The resulting number of bytes is greater than the maximum number that can be returned as an integer. </exception>
      <exception cref="T:System.ArgumentException">Error detection is enabled, and <paramref name="bytes" /> contains an invalid sequence of bytes. </exception>
      <exception cref="T:System.Text.DecoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.DecoderFallback" /> is set to <see cref="T:System.Text.DecoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF32Encoding.GetCharCount(System.Byte[],System.Int32,System.Int32)">
      <summary>Calculates the number of characters produced by decoding a sequence of bytes from the specified byte array.</summary>
      <returns>The number of characters produced by decoding the specified sequence of bytes.</returns>
      <param name="bytes">The byte array containing the sequence of bytes to decode. </param>
      <param name="index">The index of the first byte to decode. </param>
      <param name="count">The number of bytes to decode. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero.-or- <paramref name="index" /> and <paramref name="count" /> do not denote a valid range in <paramref name="bytes" />.-or- The resulting number of bytes is greater than the maximum number that can be returned as an integer. </exception>
      <exception cref="T:System.ArgumentException">Error detection is enabled, and <paramref name="bytes" /> contains an invalid sequence of bytes. </exception>
      <exception cref="T:System.Text.DecoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.DecoderFallback" /> is set to <see cref="T:System.Text.DecoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF32Encoding.GetChars(System.Byte*,System.Int32,System.Char*,System.Int32)">
      <summary>Decodes a sequence of bytes starting at the specified byte pointer into a set of characters that are stored starting at the specified character pointer.</summary>
      <returns>The actual number of characters written at the location indicated by <paramref name="chars" />.</returns>
      <param name="bytes">A pointer to the first byte to decode. </param>
      <param name="byteCount">The number of bytes to decode. </param>
      <param name="chars">A pointer to the location at which to start writing the resulting set of characters. </param>
      <param name="charCount">The maximum number of characters to write. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null.-or- <paramref name="chars" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="byteCount" /> or <paramref name="charCount" /> is less than zero. </exception>
      <exception cref="T:System.ArgumentException">Error detection is enabled, and <paramref name="bytes" /> contains an invalid sequence of bytes.-or- <paramref name="charCount" /> is less than the resulting number of characters. </exception>
      <exception cref="T:System.Text.DecoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.DecoderFallback" /> is set to <see cref="T:System.Text.DecoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF32Encoding.GetChars(System.Byte[],System.Int32,System.Int32,System.Char[],System.Int32)">
      <summary>Decodes a sequence of bytes from the specified byte array into the specified character array.</summary>
      <returns>The actual number of characters written into <paramref name="chars" />.</returns>
      <param name="bytes">The byte array containing the sequence of bytes to decode. </param>
      <param name="byteIndex">The index of the first byte to decode. </param>
      <param name="byteCount">The number of bytes to decode. </param>
      <param name="chars">The character array to contain the resulting set of characters. </param>
      <param name="charIndex">The index at which to start writing the resulting set of characters. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null.-or- <paramref name="chars" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="byteIndex" /> or <paramref name="byteCount" /> or <paramref name="charIndex" /> is less than zero.-or- <paramref name="byteindex" /> and <paramref name="byteCount" /> do not denote a valid range in <paramref name="bytes" />.-or- <paramref name="charIndex" /> is not a valid index in <paramref name="chars" />. </exception>
      <exception cref="T:System.ArgumentException">Error detection is enabled, and <paramref name="bytes" /> contains an invalid sequence of bytes.-or- <paramref name="chars" /> does not have enough capacity from <paramref name="charIndex" /> to the end of the array to accommodate the resulting characters. </exception>
      <exception cref="T:System.Text.DecoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.DecoderFallback" /> is set to <see cref="T:System.Text.DecoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF32Encoding.GetDecoder">
      <summary>Obtains a decoder that converts a UTF-32 encoded sequence of bytes into a sequence of Unicode characters.</summary>
      <returns>A <see cref="T:System.Text.Decoder" /> that converts a UTF-32 encoded sequence of bytes into a sequence of Unicode characters.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF32Encoding.GetEncoder">
      <summary>Obtains an encoder that converts a sequence of Unicode characters into a UTF-32 encoded sequence of bytes.</summary>
      <returns>A <see cref="T:System.Text.Encoder" /> that converts a sequence of Unicode characters into a UTF-32 encoded sequence of bytes.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF32Encoding.GetHashCode">
      <summary>Returns the hash code for the current instance.</summary>
      <returns>The hash code for the current <see cref="T:System.Text.UTF32Encoding" /> object.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF32Encoding.GetMaxByteCount(System.Int32)">
      <summary>Calculates the maximum number of bytes produced by encoding the specified number of characters.</summary>
      <returns>The maximum number of bytes produced by encoding the specified number of characters.</returns>
      <param name="charCount">The number of characters to encode. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="charCount" /> is less than zero.-or- The resulting number of bytes is greater than the maximum number that can be returned as an integer. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.EncoderFallback" /> is set to <see cref="T:System.Text.EncoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF32Encoding.GetMaxCharCount(System.Int32)">
      <summary>Calculates the maximum number of characters produced by decoding the specified number of bytes.</summary>
      <returns>The maximum number of characters produced by decoding the specified number of bytes.</returns>
      <param name="byteCount">The number of bytes to decode. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="byteCount" /> is less than zero.-or- The resulting number of bytes is greater than the maximum number that can be returned as an integer. </exception>
      <exception cref="T:System.Text.DecoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.DecoderFallback" /> is set to <see cref="T:System.Text.DecoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF32Encoding.GetPreamble">
      <summary>Returns a Unicode byte order mark encoded in UTF-32 format, if the constructor for this instance requests a byte order mark.</summary>
      <returns>A byte array containing the Unicode byte order mark, if the constructor for this instance requests a byte order mark. Otherwise, this method returns a byte array of length zero.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF32Encoding.GetString(System.Byte[],System.Int32,System.Int32)">
      <summary>Decodes a range of bytes from a byte array into a string.</summary>
      <returns>A <see cref="T:System.String" /> containing the results of decoding the specified sequence of bytes.</returns>
      <param name="bytes">The byte array containing the sequence of bytes to decode. </param>
      <param name="index">The index of the first byte to decode. </param>
      <param name="count">The number of bytes to decode. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero.-or- <paramref name="index" /> and <paramref name="count" /> do not denote a valid range in <paramref name="bytes" />. </exception>
      <exception cref="T:System.ArgumentException">Error detection is enabled, and <paramref name="bytes" /> contains an invalid sequence of bytes. </exception>
      <exception cref="T:System.Text.DecoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.DecoderFallback" /> is set to <see cref="T:System.Text.DecoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Text.UTF7Encoding">
      <summary>Represents a UTF-7 encoding of Unicode characters.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF7Encoding.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Text.UTF7Encoding" /> class.</summary>
    </member>
    <member name="M:System.Text.UTF7Encoding.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Text.UTF7Encoding" /> class. A parameter specifies whether to allow optional characters.</summary>
      <param name="allowOptionals">true to specify that optional characters are allowed; otherwise, false. </param>
    </member>
    <member name="M:System.Text.UTF7Encoding.Equals(System.Object)">
      <summary>Gets a value indicating whether the specified object is equal to the current <see cref="T:System.Text.UTF7Encoding" /> object.</summary>
      <returns>true if <paramref name="value" /> is a <see cref="T:System.Text.UTF7Encoding" /> object and is equal to the current <see cref="T:System.Text.UTF7Encoding" /> object; otherwise, false.</returns>
      <param name="value">An object to compare to the current <see cref="T:System.Text.UTF7Encoding" /> object.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.UTF7Encoding.GetByteCount(System.Char*,System.Int32)">
      <summary>Calculates the number of bytes produced by encoding a set of characters starting at the specified character pointer.</summary>
      <returns>The number of bytes produced by encoding the specified characters.</returns>
      <param name="chars">A pointer to the first character to encode. </param>
      <param name="count">The number of characters to encode. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" /> is null (Nothing in Visual Basic .NET). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" /> is less than zero.-or- The resulting number of bytes is greater than the maximum number that can be returned as an int. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.EncoderFallback" /> is set to <see cref="T:System.Text.EncoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF7Encoding.GetByteCount(System.Char[],System.Int32,System.Int32)">
      <summary>Calculates the number of bytes produced by encoding a set of characters from the specified character array.</summary>
      <returns>The number of bytes produced by encoding the specified characters.</returns>
      <param name="chars">The character array containing the set of characters to encode. </param>
      <param name="index">The index of the first character to encode. </param>
      <param name="count">The number of characters to encode. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" /> is null (Nothing). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero.-or- <paramref name="index" /> and <paramref name="count" /> do not denote a valid range in <paramref name="chars" />.-or- The resulting number of bytes is greater than the maximum number that can be returned as an int. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.EncoderFallback" /> is set to <see cref="T:System.Text.EncoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF7Encoding.GetByteCount(System.String)">
      <summary>Calculates the number of bytes produced by encoding the characters in the specified <see cref="T:System.String" /> object.</summary>
      <returns>The number of bytes produced by encoding the specified characters.</returns>
      <param name="s">The <see cref="T:System.String" /> object containing the set of characters to encode. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null (Nothing). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The resulting number of bytes is greater than the maximum number that can be returned as an int. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for fuller explanation)-and-<see cref="P:System.Text.Encoding.EncoderFallback" /> is set to <see cref="T:System.Text.EncoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF7Encoding.GetBytes(System.Char*,System.Int32,System.Byte*,System.Int32)">
      <summary>Encodes a set of characters starting at the specified character pointer into a sequence of bytes that are stored starting at the specified byte pointer.</summary>
      <returns>The actual number of bytes written at the location indicated by <paramref name="bytes" />.</returns>
      <param name="chars">A pointer to the first character to encode. </param>
      <param name="charCount">The number of characters to encode. </param>
      <param name="bytes">A pointer to the location at which to start writing the resulting sequence of bytes. </param>
      <param name="byteCount">The maximum number of bytes to write. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" /> is null (Nothing).-or- <paramref name="bytes" /> is null (Nothing). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="charCount" /> or <paramref name="byteCount" /> is less than zero. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="byteCount" /> is less than the resulting number of bytes. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for fuller explanation)-and-<see cref="P:System.Text.Encoding.EncoderFallback" /> is set to <see cref="T:System.Text.EncoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF7Encoding.GetBytes(System.Char[],System.Int32,System.Int32,System.Byte[],System.Int32)">
      <summary>Encodes a set of characters from the specified character array into the specified byte array.</summary>
      <returns>The actual number of bytes written into <paramref name="bytes" />.</returns>
      <param name="chars">The character array containing the set of characters to encode. </param>
      <param name="charIndex">The index of the first character to encode. </param>
      <param name="charCount">The number of characters to encode. </param>
      <param name="bytes">The byte array to contain the resulting sequence of bytes. </param>
      <param name="byteIndex">The index at which to start writing the resulting sequence of bytes. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" /> is null (Nothing).-or- <paramref name="bytes" /> is null (Nothing). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="charIndex" /> or <paramref name="charCount" /> or <paramref name="byteIndex" /> is less than zero.-or- <paramref name="charIndex" /> and <paramref name="charCount" /> do not denote a valid range in <paramref name="chars" />.-or- <paramref name="byteIndex" /> is not a valid index in <paramref name="bytes" />. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="bytes" /> does not have enough capacity from <paramref name="byteIndex" /> to the end of the array to accommodate the resulting bytes. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for fuller explanation)-and-<see cref="P:System.Text.Encoding.EncoderFallback" /> is set to <see cref="T:System.Text.EncoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF7Encoding.GetBytes(System.String,System.Int32,System.Int32,System.Byte[],System.Int32)">
      <summary>Encodes a set of characters from the specified <see cref="T:System.String" /> into the specified byte array.</summary>
      <returns>The actual number of bytes written into <paramref name="bytes" />.</returns>
      <param name="s">The <see cref="T:System.String" /> containing the set of characters to encode. </param>
      <param name="charIndex">The index of the first character to encode. </param>
      <param name="charCount">The number of characters to encode. </param>
      <param name="bytes">The byte array to contain the resulting sequence of bytes. </param>
      <param name="byteIndex">The index at which to start writing the resulting sequence of bytes. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null (Nothing).-or- <paramref name="bytes" /> is null (Nothing). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="charIndex" /> or <paramref name="charCount" /> or <paramref name="byteIndex" /> is less than zero.-or- <paramref name="charIndex" /> and <paramref name="charCount" /> do not denote a valid range in <paramref name="chars" />.-or- <paramref name="byteIndex" /> is not a valid index in <paramref name="bytes" />. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="bytes" /> does not have enough capacity from <paramref name="byteIndex" /> to the end of the array to accommodate the resulting bytes. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for fuller explanation)-and-<see cref="P:System.Text.Encoding.EncoderFallback" /> is set to <see cref="T:System.Text.EncoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF7Encoding.GetCharCount(System.Byte*,System.Int32)">
      <summary>Calculates the number of characters produced by decoding a sequence of bytes starting at the specified byte pointer.</summary>
      <returns>The number of characters produced by decoding the specified sequence of bytes.</returns>
      <param name="bytes">A pointer to the first byte to decode. </param>
      <param name="count">The number of bytes to decode. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null (Nothing). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" /> is less than zero.-or- The resulting number of characters is greater than the maximum number that can be returned as an int. </exception>
      <exception cref="T:System.Text.DecoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for fuller explanation)-and-<see cref="P:System.Text.Encoding.DecoderFallback" /> is set to <see cref="T:System.Text.DecoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF7Encoding.GetCharCount(System.Byte[],System.Int32,System.Int32)">
      <summary>Calculates the number of characters produced by decoding a sequence of bytes from the specified byte array.</summary>
      <returns>The number of characters produced by decoding the specified sequence of bytes.</returns>
      <param name="bytes">The byte array containing the sequence of bytes to decode. </param>
      <param name="index">The index of the first byte to decode. </param>
      <param name="count">The number of bytes to decode. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null (Nothing). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero.-or- <paramref name="index" /> and <paramref name="count" /> do not denote a valid range in <paramref name="bytes" />.-or- The resulting number of characters is greater than the maximum number that can be returned as an int. </exception>
      <exception cref="T:System.Text.DecoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.DecoderFallback" /> is set to <see cref="T:System.Text.DecoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF7Encoding.GetChars(System.Byte*,System.Int32,System.Char*,System.Int32)">
      <summary>Decodes a sequence of bytes starting at the specified byte pointer into a set of characters that are stored starting at the specified character pointer.</summary>
      <returns>The actual number of characters written at the location indicated by <paramref name="chars" />.</returns>
      <param name="bytes">A pointer to the first byte to decode. </param>
      <param name="byteCount">The number of bytes to decode. </param>
      <param name="chars">A pointer to the location at which to start writing the resulting set of characters. </param>
      <param name="charCount">The maximum number of characters to write. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null (Nothing).-or- <paramref name="chars" /> is null (Nothing). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="byteCount" /> or <paramref name="charCount" /> is less than zero. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="charCount" /> is less than the resulting number of characters. </exception>
      <exception cref="T:System.Text.DecoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.DecoderFallback" /> is set to <see cref="T:System.Text.DecoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF7Encoding.GetChars(System.Byte[],System.Int32,System.Int32,System.Char[],System.Int32)">
      <summary>Decodes a sequence of bytes from the specified byte array into the specified character array.</summary>
      <returns>The actual number of characters written into <paramref name="chars" />.</returns>
      <param name="bytes">The byte array containing the sequence of bytes to decode. </param>
      <param name="byteIndex">The index of the first byte to decode. </param>
      <param name="byteCount">The number of bytes to decode. </param>
      <param name="chars">The character array to contain the resulting set of characters. </param>
      <param name="charIndex">The index at which to start writing the resulting set of characters. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null (Nothing).-or- <paramref name="chars" /> is null (Nothing). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="byteIndex" /> or <paramref name="byteCount" /> or <paramref name="charIndex" /> is less than zero.-or- <paramref name="byteindex" /> and <paramref name="byteCount" /> do not denote a valid range in <paramref name="bytes" />.-or- <paramref name="charIndex" /> is not a valid index in <paramref name="chars" />. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="chars" /> does not have enough capacity from <paramref name="charIndex" /> to the end of the array to accommodate the resulting characters. </exception>
      <exception cref="T:System.Text.DecoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.DecoderFallback" /> is set to <see cref="T:System.Text.DecoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF7Encoding.GetDecoder">
      <summary>Obtains a decoder that converts a UTF-7 encoded sequence of bytes into a sequence of Unicode characters.</summary>
      <returns>A <see cref="T:System.Text.Decoder" /> that converts a UTF-7 encoded sequence of bytes into a sequence of Unicode characters.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF7Encoding.GetEncoder">
      <summary>Obtains an encoder that converts a sequence of Unicode characters into a UTF-7 encoded sequence of bytes.</summary>
      <returns>A <see cref="T:System.Text.Encoder" /> that converts a sequence of Unicode characters into a UTF-7 encoded sequence of bytes.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF7Encoding.GetHashCode">
      <summary>Returns the hash code for the current <see cref="T:System.Text.UTF7Encoding" /> object.</summary>
      <returns>A 32-bit signed integer hash code.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF7Encoding.GetMaxByteCount(System.Int32)">
      <summary>Calculates the maximum number of bytes produced by encoding the specified number of characters.</summary>
      <returns>The maximum number of bytes produced by encoding the specified number of characters.</returns>
      <param name="charCount">The number of characters to encode. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="charCount" /> is less than zero.-or- The resulting number of bytes is greater than the maximum number that can be returned as an int. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.EncoderFallback" /> is set to <see cref="T:System.Text.EncoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF7Encoding.GetMaxCharCount(System.Int32)">
      <summary>Calculates the maximum number of characters produced by decoding the specified number of bytes.</summary>
      <returns>The maximum number of characters produced by decoding the specified number of bytes.</returns>
      <param name="byteCount">The number of bytes to decode. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="byteCount" /> is less than zero.-or- The resulting number of characters is greater than the maximum number that can be returned as an int. </exception>
      <exception cref="T:System.Text.DecoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.DecoderFallback" /> is set to <see cref="T:System.Text.DecoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF7Encoding.GetString(System.Byte[],System.Int32,System.Int32)">
      <summary>Decodes a range of bytes from a byte array into a string.</summary>
      <returns>A <see cref="T:System.String" /> containing the results of decoding the specified sequence of bytes.</returns>
      <param name="bytes">The byte array containing the sequence of bytes to decode. </param>
      <param name="index">The index of the first byte to decode. </param>
      <param name="count">The number of bytes to decode. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null (Nothing). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero.-or- <paramref name="index" /> and <paramref name="count" /> do not denote a valid range in <paramref name="bytes" />. </exception>
      <exception cref="T:System.Text.DecoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for fuller explanation)-and-<see cref="P:System.Text.Encoding.DecoderFallback" /> is set to <see cref="T:System.Text.DecoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Text.UTF8Encoding">
      <summary>Represents a UTF-8 encoding of Unicode characters.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF8Encoding.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Text.UTF8Encoding" /> class.</summary>
    </member>
    <member name="M:System.Text.UTF8Encoding.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Text.UTF8Encoding" /> class. A parameter specifies whether to provide a Unicode byte order mark. </summary>
      <param name="encoderShouldEmitUTF8Identifier">true to specify that the <see cref="M:System.Text.UTF8Encoding.GetPreamble" /> method returns a Unicode byte order mark; otherwise, false. See the Remarks section for more information. </param>
    </member>
    <member name="M:System.Text.UTF8Encoding.#ctor(System.Boolean,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Text.UTF8Encoding" /> class. Parameters specify whether to provide a Unicode byte order mark and whether to throw an exception when an invalid encoding is detected. </summary>
      <param name="encoderShouldEmitUTF8Identifier">true to specify that the <see cref="M:System.Text.UTF8Encoding.GetPreamble" /> method should return a Unicode byte order mark; otherwise, false. See the Remarks section for more information. </param>
      <param name="throwOnInvalidBytes">true to throw an exception when an invalid encoding is detected; otherwise, false. </param>
    </member>
    <member name="M:System.Text.UTF8Encoding.Equals(System.Object)">
      <summary>Determines whether the specified object is equal to the current <see cref="T:System.Text.UTF8Encoding" /> object.</summary>
      <returns>true if <paramref name="value" /> is an instance of <see cref="T:System.Text.UTF8Encoding" /> and is equal to the current object; otherwise, false.</returns>
      <param name="value">The object to compare with the current instance. </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.UTF8Encoding.GetByteCount(System.Char*,System.Int32)">
      <summary>Calculates the number of bytes produced by encoding a set of characters starting at the specified character pointer.</summary>
      <returns>The number of bytes produced by encoding the specified characters. </returns>
      <param name="chars">A pointer to the first character to encode. </param>
      <param name="count">The number of characters to encode. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" /> is less than zero.-or- The resulting number of bytes is greater than the maximum number that can be returned as an integer. </exception>
      <exception cref="T:System.ArgumentException">Error detection is enabled, and <paramref name="chars" /> contains an invalid sequence of characters. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for a complete explanation)-and-<see cref="P:System.Text.Encoding.EncoderFallback" /> is set to <see cref="T:System.Text.EncoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF8Encoding.GetByteCount(System.Char[],System.Int32,System.Int32)">
      <summary>Calculates the number of bytes produced by encoding a set of characters from the specified character array.</summary>
      <returns>The number of bytes produced by encoding the specified characters. </returns>
      <param name="chars">The character array containing the set of characters to encode. </param>
      <param name="index">The index of the first character to encode. </param>
      <param name="count">The number of characters to encode. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero.-or- <paramref name="index" /> and <paramref name="count" /> do not denote a valid range in <paramref name="chars" />.-or- The resulting number of bytes is greater than the maximum number that can be returned as an integer. </exception>
      <exception cref="T:System.ArgumentException">Error detection is enabled, and <paramref name="chars" /> contains an invalid sequence of characters. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-The <see cref="P:System.Text.Encoding.EncoderFallback" /> property is set to <see cref="T:System.Text.EncoderExceptionFallback" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF8Encoding.GetByteCount(System.String)">
      <summary>Calculates the number of bytes produced by encoding the characters in the specified <see cref="T:System.String" />.</summary>
      <returns>The number of bytes produced by encoding the specified characters.</returns>
      <param name="chars">The <see cref="T:System.String" /> containing the set of characters to encode. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The resulting number of bytes is greater than the maximum number that can be returned as an integer. </exception>
      <exception cref="T:System.ArgumentException">Error detection is enabled, and <paramref name="chars" /> contains an invalid sequence of characters. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.EncoderFallback" /> is set to <see cref="T:System.Text.EncoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF8Encoding.GetBytes(System.Char*,System.Int32,System.Byte*,System.Int32)">
      <summary>Encodes a set of characters starting at the specified character pointer into a sequence of bytes that are stored starting at the specified byte pointer.</summary>
      <returns>The actual number of bytes written at the location indicated by <paramref name="bytes" />.</returns>
      <param name="chars">A pointer to the first character to encode. </param>
      <param name="charCount">The number of characters to encode. </param>
      <param name="bytes">A pointer to the location at which to start writing the resulting sequence of bytes. </param>
      <param name="byteCount">The maximum number of bytes to write. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" /> is null.-or- <paramref name="bytes" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="charCount" /> or <paramref name="byteCount" /> is less than zero. </exception>
      <exception cref="T:System.ArgumentException">Error detection is enabled, and <paramref name="chars" /> contains an invalid sequence of characters.-or- <paramref name="byteCount" /> is less than the resulting number of bytes. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.EncoderFallback" /> is set to <see cref="T:System.Text.EncoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF8Encoding.GetBytes(System.Char[],System.Int32,System.Int32,System.Byte[],System.Int32)">
      <summary>Encodes a set of characters from the specified character array into the specified byte array.</summary>
      <returns>The actual number of bytes written into <paramref name="bytes" />.</returns>
      <param name="chars">The character array containing the set of characters to encode. </param>
      <param name="charIndex">The index of the first character to encode. </param>
      <param name="charCount">The number of characters to encode. </param>
      <param name="bytes">The byte array to contain the resulting sequence of bytes. </param>
      <param name="byteIndex">The index at which to start writing the resulting sequence of bytes. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" /> is null.-or- <paramref name="bytes" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="charIndex" /> or <paramref name="charCount" /> or <paramref name="byteIndex" /> is less than zero.-or- <paramref name="charIndex" /> and <paramref name="charCount" /> do not denote a valid range in <paramref name="chars" />.-or- <paramref name="byteIndex" /> is not a valid index in <paramref name="bytes" />. </exception>
      <exception cref="T:System.ArgumentException">Error detection is enabled, and <paramref name="chars" /> contains an invalid sequence of characters.-or- <paramref name="bytes" /> does not have enough capacity from <paramref name="byteIndex" /> to the end of the array to accommodate the resulting bytes. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.EncoderFallback" /> is set to <see cref="T:System.Text.EncoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF8Encoding.GetBytes(System.String,System.Int32,System.Int32,System.Byte[],System.Int32)">
      <summary>Encodes a set of characters from the specified <see cref="T:System.String" /> into the specified byte array.</summary>
      <returns>The actual number of bytes written into <paramref name="bytes" />.</returns>
      <param name="s">The <see cref="T:System.String" /> containing the set of characters to encode. </param>
      <param name="charIndex">The index of the first character to encode. </param>
      <param name="charCount">The number of characters to encode. </param>
      <param name="bytes">The byte array to contain the resulting sequence of bytes. </param>
      <param name="byteIndex">The index at which to start writing the resulting sequence of bytes. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null.-or- <paramref name="bytes" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="charIndex" /> or <paramref name="charCount" /> or <paramref name="byteIndex" /> is less than zero.-or- <paramref name="charIndex" /> and <paramref name="charCount" /> do not denote a valid range in <paramref name="chars" />.-or- <paramref name="byteIndex" /> is not a valid index in <paramref name="bytes" />. </exception>
      <exception cref="T:System.ArgumentException">Error detection is enabled, and <paramref name="s" /> contains an invalid sequence of characters.-or- <paramref name="bytes" /> does not have enough capacity from <paramref name="byteIndex" /> to the end of the array to accommodate the resulting bytes. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.EncoderFallback" /> is set to <see cref="T:System.Text.EncoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF8Encoding.GetCharCount(System.Byte*,System.Int32)">
      <summary>Calculates the number of characters produced by decoding a sequence of bytes starting at the specified byte pointer.</summary>
      <returns>The number of characters produced by decoding the specified sequence of bytes.</returns>
      <param name="bytes">A pointer to the first byte to decode. </param>
      <param name="count">The number of bytes to decode. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" /> is less than zero.-or- The resulting number of bytes is greater than the maximum number that can be returned as an integer. </exception>
      <exception cref="T:System.ArgumentException">Error detection is enabled, and <paramref name="bytes" /> contains an invalid sequence of bytes. </exception>
      <exception cref="T:System.Text.DecoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.DecoderFallback" /> is set to <see cref="T:System.Text.DecoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF8Encoding.GetCharCount(System.Byte[],System.Int32,System.Int32)">
      <summary>Calculates the number of characters produced by decoding a sequence of bytes from the specified byte array.</summary>
      <returns>The number of characters produced by decoding the specified sequence of bytes.</returns>
      <param name="bytes">The byte array containing the sequence of bytes to decode. </param>
      <param name="index">The index of the first byte to decode. </param>
      <param name="count">The number of bytes to decode. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero.-or- <paramref name="index" /> and <paramref name="count" /> do not denote a valid range in <paramref name="bytes" />.-or- The resulting number of bytes is greater than the maximum number that can be returned as an integer. </exception>
      <exception cref="T:System.ArgumentException">Error detection is enabled, and <paramref name="bytes" /> contains an invalid sequence of bytes. </exception>
      <exception cref="T:System.Text.DecoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.DecoderFallback" /> is set to <see cref="T:System.Text.DecoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF8Encoding.GetChars(System.Byte*,System.Int32,System.Char*,System.Int32)">
      <summary>Decodes a sequence of bytes starting at the specified byte pointer into a set of characters that are stored starting at the specified character pointer.</summary>
      <returns>The actual number of characters written at the location indicated by <paramref name="chars" />.</returns>
      <param name="bytes">A pointer to the first byte to decode. </param>
      <param name="byteCount">The number of bytes to decode. </param>
      <param name="chars">A pointer to the location at which to start writing the resulting set of characters. </param>
      <param name="charCount">The maximum number of characters to write. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null.-or- <paramref name="chars" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="byteCount" /> or <paramref name="charCount" /> is less than zero. </exception>
      <exception cref="T:System.ArgumentException">Error detection is enabled, and <paramref name="bytes" /> contains an invalid sequence of bytes.-or- <paramref name="charCount" /> is less than the resulting number of characters. </exception>
      <exception cref="T:System.Text.DecoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.DecoderFallback" /> is set to <see cref="T:System.Text.DecoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF8Encoding.GetChars(System.Byte[],System.Int32,System.Int32,System.Char[],System.Int32)">
      <summary>Decodes a sequence of bytes from the specified byte array into the specified character array.</summary>
      <returns>The actual number of characters written into <paramref name="chars" />.</returns>
      <param name="bytes">The byte array containing the sequence of bytes to decode. </param>
      <param name="byteIndex">The index of the first byte to decode. </param>
      <param name="byteCount">The number of bytes to decode. </param>
      <param name="chars">The character array to contain the resulting set of characters. </param>
      <param name="charIndex">The index at which to start writing the resulting set of characters. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null.-or- <paramref name="chars" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="byteIndex" /> or <paramref name="byteCount" /> or <paramref name="charIndex" /> is less than zero.-or- <paramref name="byteindex" /> and <paramref name="byteCount" /> do not denote a valid range in <paramref name="bytes" />.-or- <paramref name="charIndex" /> is not a valid index in <paramref name="chars" />. </exception>
      <exception cref="T:System.ArgumentException">Error detection is enabled, and <paramref name="bytes" /> contains an invalid sequence of bytes.-or- <paramref name="chars" /> does not have enough capacity from <paramref name="charIndex" /> to the end of the array to accommodate the resulting characters. </exception>
      <exception cref="T:System.Text.DecoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.DecoderFallback" /> is set to <see cref="T:System.Text.DecoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF8Encoding.GetDecoder">
      <summary>Obtains a decoder that converts a UTF-8 encoded sequence of bytes into a sequence of Unicode characters. </summary>
      <returns>A decoder that converts a UTF-8 encoded sequence of bytes into a sequence of Unicode characters.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF8Encoding.GetEncoder">
      <summary>Obtains an encoder that converts a sequence of Unicode characters into a UTF-8 encoded sequence of bytes.</summary>
      <returns>A <see cref="T:System.Text.Encoder" /> that converts a sequence of Unicode characters into a UTF-8 encoded sequence of bytes.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF8Encoding.GetHashCode">
      <summary>Returns the hash code for the current instance.</summary>
      <returns>The hash code for the current instance.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF8Encoding.GetMaxByteCount(System.Int32)">
      <summary>Calculates the maximum number of bytes produced by encoding the specified number of characters.</summary>
      <returns>The maximum number of bytes produced by encoding the specified number of characters.</returns>
      <param name="charCount">The number of characters to encode. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="charCount" /> is less than zero.-or- The resulting number of bytes is greater than the maximum number that can be returned as an integer. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.EncoderFallback" /> is set to <see cref="T:System.Text.EncoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF8Encoding.GetMaxCharCount(System.Int32)">
      <summary>Calculates the maximum number of characters produced by decoding the specified number of bytes.</summary>
      <returns>The maximum number of characters produced by decoding the specified number of bytes.</returns>
      <param name="byteCount">The number of bytes to decode. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="byteCount" /> is less than zero.-or- The resulting number of bytes is greater than the maximum number that can be returned as an integer. </exception>
      <exception cref="T:System.Text.DecoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.DecoderFallback" /> is set to <see cref="T:System.Text.DecoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF8Encoding.GetPreamble">
      <summary>Returns a Unicode byte order mark encoded in UTF-8 format, if the <see cref="T:System.Text.UTF8Encoding" /> encoding object is configured to supply one. </summary>
      <returns>A byte array containing the Unicode byte order mark, if the <see cref="T:System.Text.UTF8Encoding" /> encoding object is configured to supply one. Otherwise, this method returns a zero-length byte array. </returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.UTF8Encoding.GetString(System.Byte[],System.Int32,System.Int32)">
      <summary>Decodes a range of bytes from a byte array into a string.</summary>
      <returns>A <see cref="T:System.String" /> containing the results of decoding the specified sequence of bytes.</returns>
      <param name="bytes">The byte array containing the sequence of bytes to decode. </param>
      <param name="index">The index of the first byte to decode. </param>
      <param name="count">The number of bytes to decode. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero.-or- <paramref name="index" /> and <paramref name="count" /> do not denote a valid range in <paramref name="bytes" />. </exception>
      <exception cref="T:System.ArgumentException">Error detection is enabled, and <paramref name="bytes" /> contains an invalid sequence of bytes. </exception>
      <exception cref="T:System.Text.DecoderFallbackException">A fallback occurred (see Character Encoding in the .NET Framework for complete explanation)-and-<see cref="P:System.Text.Encoding.DecoderFallback" /> is set to <see cref="T:System.Text.DecoderExceptionFallback" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
  </members>
</doc>